"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function seedSubscriptionPlans() {
    console.log("Seeding subscription plans...");
    // Delete existing subscription plans
    await prisma.subscriptionPlan.deleteMany({});
    // Create subscription plans
    const plans = [
        {
            type: client_1.SubscriptionPlanType.FREE_TRIAL,
            name: "Free Trial",
            description: "Try all features for 14 days",
            monthlyPrice: 0,
            yearlyPrice: 0,
            trialDays: 14,
            maxUsers: 10,
            features: JSON.stringify([
                "All modules included",
                "Up to 10 users",
                "14-day trial period",
                "Email support",
            ]),
            modules: JSON.stringify([
                "Core",
                "Budget",
                "HR",
                "Companies",
                "Production",
            ]),
        },
        {
            type: client_1.SubscriptionPlanType.STARTER,
            name: "Starter",
            description: "Perfect for small businesses",
            monthlyPrice: 99,
            yearlyPrice: 990, // 15% discount for yearly
            trialDays: 0,
            maxUsers: 10,
            features: JSON.stringify([
                "Up to 10 users",
                "Additional users: €9/month each",
                "Email and chat support",
                "Regular updates",
            ]),
            modules: JSON.stringify([
                "Core",
                "Budget",
                "HR",
                "Companies",
                "Production",
            ]),
        },
        {
            type: client_1.SubscriptionPlanType.PREMIUM,
            name: "Premium",
            description: "For growing businesses with advanced needs",
            monthlyPrice: 199,
            yearlyPrice: 1990, // 15% discount for yearly
            trialDays: 0,
            maxUsers: 30,
            features: JSON.stringify([
                "Up to 30 users",
                "Additional users: €9/month each",
                "Priority support",
                "Regular updates",
                "Custom integrations",
            ]),
            modules: JSON.stringify([
                "Core",
                "Budget",
                "HR",
                "Companies",
                "Production",
                "Recruitment",
                "Communication",
                "Analytics",
            ]),
        },
    ];
    for (const plan of plans) {
        await prisma.subscriptionPlan.create({
            data: {
                ...plan,
                additionalUserFee: 9,
                features: JSON.parse(plan.features),
                modules: JSON.parse(plan.modules),
            },
        });
    }
    console.log("Subscription plans seeded successfully!");
}
// Run the seed function
seedSubscriptionPlans()
    .catch((e) => {
    console.error("Error seeding subscription plans:", e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=subscription-plans.js.map