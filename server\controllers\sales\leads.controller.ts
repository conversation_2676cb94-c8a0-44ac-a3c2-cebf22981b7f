import { Request, Response } from "express";
import { prisma } from "../../lib/prisma.js";
import { logDataOperation } from "../../utils/activityLogger.utils.js";
import { AuthenticatedRequest } from "../../types/auth.js";

// Using centralized prisma instance from lib/prisma.js

// Get all leads
export const getAllLeads = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      status,
      source,
      assignedTo,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
      page = 1,
      limit = 10,
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build filter conditions
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (source) {
      where.source = source;
    }

    if (assignedTo) {
      where.assignedToId = Number(assignedTo);
    }

    if (search) {
      where.OR = [
        { firstName: { contains: search as string, mode: "insensitive" } },
        { lastName: { contains: search as string, mode: "insensitive" } },
        { email: { contains: search as string, mode: "insensitive" } },
        { company: { contains: search as string, mode: "insensitive" } },
      ];
    }

    // Get total count for pagination
    const totalCount = await prisma.lead.count({ where });

    // Get leads with pagination, sorting and filtering
    const leads = await prisma.lead.findMany({
      where,
      include: {
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
        tags: true,
        _count: {
          select: {
            activities: true,
            deals: true,
          },
        },
      },
      orderBy: {
        [sortBy as string]: sortOrder,
      },
      skip,
      take: Number(limit),
    });

    res.status(200).json({
      leads,
      pagination: {
        total: totalCount,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(totalCount / Number(limit)),
      },
    });
  } catch (error) {
    console.error("Error fetching leads:", error);
    res.status(500).json({ error: "Failed to fetch leads" });
  }
};

// Get lead by ID
export const getLeadById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const lead = await prisma.lead.findUnique({
      where: { id: Number(id) },
      include: {
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
        tags: true,
        customFields: true,
        activities: {
          include: {
            assignedTo: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
          orderBy: {
            startDate: "desc",
          },
        },
        deals: {
          include: {
            assignedTo: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
        },
      },
    });

    if (!lead) {
      res.status(404).json({ error: "Lead not found" });
      return;
    }

    res.status(200).json(lead);
  } catch (error) {
    console.error("Error fetching lead:", error);
    res.status(500).json({ error: "Failed to fetch lead" });
  }
};

// Create a new lead
export const createLead = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      firstName,
      lastName,
      email,
      phone,
      company,
      jobTitle,
      status,
      source,
      notes,
      assignedToId,
      score,
      estimatedValue,
      tags,
      customFields,
    } = req.body;

    // Create lead with nested tags and custom fields
    const lead = await prisma.lead.create({
      data: {
        firstName,
        lastName,
        email,
        phone,
        company,
        jobTitle,
        status: status || "NEW",
        source,
        notes,
        assignedToId: assignedToId ? Number(assignedToId) : null,
        score,
        estimatedValue,
        tags: tags
          ? {
              create: tags.map((tag: string) => ({
                name: tag,
              })),
            }
          : undefined,
        customFields: customFields
          ? {
              create: Object.entries(customFields).map(([name, value]) => ({
                name,
                value: String(value),
              })),
            }
          : undefined,
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
        tags: true,
        customFields: true,
      },
    });

    // Log lead creation activity
    if (req.user?.id) {
      await logDataOperation(
        "CREATE",
        "lead",
        lead.id,
        "sales",
        req,
        req.user.id,
        undefined,
        `Lead Created: ${lead.firstName} ${lead.lastName}`,
        `New lead "${lead.firstName} ${lead.lastName}" was created`
      );
    }

    res.status(201).json(lead);
  } catch (error) {
    console.error("Error creating lead:", error);
    res.status(500).json({ error: "Failed to create lead" });
  }
};

// Update a lead
export const updateLead = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const {
      firstName,
      lastName,
      email,
      phone,
      company,
      jobTitle,
      status,
      source,
      notes,
      assignedToId,
      score,
      estimatedValue,
      tags,
      customFields,
    } = req.body;

    // Check if lead exists
    const existingLead = await prisma.lead.findUnique({
      where: { id: Number(id) },
      include: {
        tags: true,
        customFields: true,
      },
    });

    if (!existingLead) {
      res.status(404).json({ error: "Lead not found" });
      return;
    }

    // Update lead
    const updatedLead = await prisma.lead.update({
      where: { id: Number(id) },
      data: {
        firstName,
        lastName,
        email,
        phone,
        company,
        jobTitle,
        status,
        source,
        notes,
        assignedToId: assignedToId ? Number(assignedToId) : null,
        score,
        estimatedValue,
        updatedAt: new Date(),
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
        tags: true,
        customFields: true,
      },
    });

    // Update tags if provided
    if (tags) {
      // Delete existing tags
      await prisma.leadTag.deleteMany({
        where: { leadId: Number(id) },
      });

      // Create new tags
      if (tags.length > 0) {
        await prisma.leadTag.createMany({
          data: tags.map((tag: string) => ({
            leadId: Number(id),
            name: tag,
          })),
        });
      }
    }

    // Update custom fields if provided
    if (customFields) {
      // Handle each custom field
      for (const [name, value] of Object.entries(customFields)) {
        const existingField = existingLead.customFields.find(
          (field) => field.name === name
        );

        if (existingField) {
          // Update existing field
          await prisma.leadCustomField.update({
            where: { id: existingField.id },
            data: { value: String(value) },
          });
        } else {
          // Create new field
          await prisma.leadCustomField.create({
            data: {
              leadId: Number(id),
              name,
              value: String(value),
            },
          });
        }
      }
    }

    // Get the updated lead with all relations
    const finalLead = await prisma.lead.findUnique({
      where: { id: Number(id) },
      include: {
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
        tags: true,
        customFields: true,
      },
    });

    res.status(200).json(finalLead);
  } catch (error) {
    console.error("Error updating lead:", error);
    res.status(500).json({ error: "Failed to update lead" });
  }
};

// Delete a lead
export const deleteLead = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    // Check if lead exists
    const lead = await prisma.lead.findUnique({
      where: { id: Number(id) },
    });

    if (!lead) {
      res.status(404).json({ error: "Lead not found" });
      return;
    }

    // Delete lead (cascade will handle related records)
    await prisma.lead.delete({
      where: { id: Number(id) },
    });

    res.status(200).json({ message: "Lead deleted successfully" });
  } catch (error) {
    console.error("Error deleting lead:", error);
    res.status(500).json({ error: "Failed to delete lead" });
  }
};

// Add note to lead
export const addLeadNote = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    // Check if lead exists
    const lead = await prisma.lead.findUnique({
      where: { id: Number(id) },
    });

    if (!lead) {
      res.status(404).json({ error: "Lead not found" });
      return;
    }

    // Add note to lead
    // Since we don't have a proper note model, just update the lead with the note in the notes field
    const updatedLead = await prisma.lead.update({
      where: { id: Number(id) },
      data: {
        notes: lead.notes ? `${lead.notes}\n\n${content}` : content,
      },
    });

    // Create a simple note object to return
    const note = {
      id: Date.now(),
      content,
      createdAt: new Date(),
      createdBy: { id: userId },
    };

    res.status(201).json(note);
  } catch (error) {
    console.error("Error adding note to lead:", error);
    res.status(500).json({ error: "Failed to add note to lead" });
  }
};

// Add tag to lead
export const addLeadTag = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    // Check if lead exists
    const lead = await prisma.lead.findUnique({
      where: { id: Number(id) },
    });

    if (!lead) {
      res.status(404).json({ error: "Lead not found" });
      return;
    }

    // Check if tag already exists for this lead
    const existingTag = await prisma.leadTag.findFirst({
      where: {
        leadId: Number(id),
        name,
      },
    });

    if (existingTag) {
      res.status(400).json({ error: "Tag already exists for this lead" });
      return;
    }

    // Add tag to lead
    const tag = await prisma.leadTag.create({
      data: {
        leadId: Number(id),
        name,
      },
    });

    res.status(201).json(tag);
  } catch (error) {
    console.error("Error adding tag to lead:", error);
    res.status(500).json({ error: "Failed to add tag to lead" });
  }
};

// Remove tag from lead
export const removeLeadTag = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id, tagId } = req.params;

    // Check if lead exists
    const lead = await prisma.lead.findUnique({
      where: { id: Number(id) },
    });

    if (!lead) {
      res.status(404).json({ error: "Lead not found" });
      return;
    }

    // Check if tag exists
    const tag = await prisma.leadTag.findUnique({
      where: { id: Number(tagId) },
    });

    if (!tag || tag.leadId !== Number(id)) {
      res.status(404).json({ error: "Tag not found for this lead" });
      return;
    }

    // Remove tag from lead
    await prisma.leadTag.delete({
      where: { id: Number(tagId) },
    });

    res.status(200).json({ message: "Tag removed successfully" });
  } catch (error) {
    console.error("Error removing tag from lead:", error);
    res.status(500).json({ error: "Failed to remove tag from lead" });
  }
};

// Import leads from CSV/Excel
export const importLeads = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { leads } = req.body;

    if (!leads || !Array.isArray(leads) || leads.length === 0) {
      res.status(400).json({ error: "No leads provided for import" });
      return;
    }

    // Process and validate leads
    const validLeads = leads.filter(
      (lead) => lead.firstName && lead.lastName && lead.email && lead.source
    );

    if (validLeads.length === 0) {
      res.status(400).json({ error: "No valid leads found in import data" });
      return;
    }

    // Create leads in batch
    const createdLeads = await prisma.$transaction(
      validLeads.map((lead) =>
        prisma.lead.create({
          data: {
            firstName: lead.firstName,
            lastName: lead.lastName,
            email: lead.email,
            phone: lead.phone,
            company: lead.company,
            jobTitle: lead.jobTitle,
            status: lead.status || "NEW",
            source: lead.source,
            notes: lead.notes,
            assignedToId: lead.assignedToId ? Number(lead.assignedToId) : null,
            score: lead.score ? Number(lead.score) : null,
            estimatedValue: lead.estimatedValue
              ? Number(lead.estimatedValue)
              : null,
          },
        })
      )
    );

    res.status(201).json({
      message: `Successfully imported ${createdLeads.length} leads`,
      totalImported: createdLeads.length,
      totalAttempted: leads.length,
    });
  } catch (error) {
    console.error("Error importing leads:", error);
    res.status(500).json({ error: "Failed to import leads" });
  }
};

// Convert lead to deal
export const convertLead = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const {
      dealName,
      dealValue,
      dealStage,
      dealType,
      probability,
      expectedCloseDate,
      companyId,
      assignedToId,
    } = req.body;

    // Check if lead exists
    const lead = await prisma.lead.findUnique({
      where: { id: Number(id) },
    });

    if (!lead) {
      res.status(404).json({ error: "Lead not found" });
      return;
    }

    // Start a transaction
    const result = await prisma.$transaction(async (prisma) => {
      // Create a deal from the lead
      const deal = await prisma.deal.create({
        data: {
          name: dealName || `${lead.firstName} ${lead.lastName} - Deal`,
          value: dealValue || lead.estimatedValue || 0,
          stage: dealStage || "QUALIFICATION",
          type: dealType || "NEW_BUSINESS",
          probability: probability || 20,
          expectedCloseDate: expectedCloseDate
            ? new Date(expectedCloseDate)
            : null,
          leadId: Number(id),
          companyId: companyId || null,
          assignedToId: assignedToId ? Number(assignedToId) : lead.assignedToId,
          description: lead.notes,
        },
      });

      // Update lead status to CONVERTED
      const updatedLead = await prisma.lead.update({
        where: { id: Number(id) },
        data: {
          status: "CONVERTED",
          convertedAt: new Date(),
        },
      });

      return { deal, lead: updatedLead };
    });

    res.status(200).json({
      message: "Lead successfully converted to deal",
      deal: result.deal,
      lead: result.lead,
    });
  } catch (error) {
    console.error("Error converting lead:", error);
    res.status(500).json({ error: "Failed to convert lead" });
  }
};
