"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteCompany = exports.updateCompany = exports.createCompany = exports.getCompanyById = exports.getAllCompanies = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
const getAllCompanies = async (_req, res, next) => {
    try {
        const companies = await prisma_js_1.prisma.company.findMany({
            include: {
                contacts: true,
                workforceNeeds: true,
            },
        });
        res.json(companies);
    }
    catch (error) {
        next(createError(500, "Failed to fetch companies"));
    }
};
exports.getAllCompanies = getAllCompanies;
const getCompanyById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(id) },
            include: {
                contacts: true,
                workforceNeeds: true,
            },
        });
        if (!company) {
            return next(createError(404, "Company not found"));
        }
        res.json(company);
    }
    catch (error) {
        next(createError(500, "Failed to fetch company"));
    }
};
exports.getCompanyById = getCompanyById;
const createCompany = async (req, res, next) => {
    try {
        const payload = req.body;
        const company = await prisma_js_1.prisma.company.create({
            data: {
                ...payload,
                contractTerms: payload.contractTerms
                    ? payload.contractTerms
                    : undefined,
                // createdBy field might not exist in the schema
                // createdBy: req.user?.id,
            },
            include: {
                contacts: true,
                workforceNeeds: true,
            },
        });
        res.status(201).json(company);
    }
    catch (error) {
        next(createError(500, "Failed to create company"));
    }
};
exports.createCompany = createCompany;
const updateCompany = async (req, res, next) => {
    try {
        const { id } = req.params;
        const payload = req.body;
        // Get existing contractTerms or initialize empty object
        let contractTerms = payload.contractTerms || {};
        // Handle legal entity type as part of contractTerms
        // Check if type is an object with value property (from frontend)
        if (payload.type &&
            typeof payload.type === "object" &&
            payload.type.value) {
            contractTerms.legalEntityType = payload.type.value;
            // Remove type from payload to avoid conflict with CompanyType enum
            delete payload.type;
        }
        // Check if type is a string (direct API call)
        else if (payload.type &&
            typeof payload.type === "string" &&
            [
                "LLC",
                "CORPORATION",
                "PARTNERSHIP",
                "SOLE_PROPRIETORSHIP",
                "NON_PROFIT",
            ].includes(payload.type)) {
            contractTerms.legalEntityType = payload.type;
            // Remove type from payload to avoid conflict with CompanyType enum
            delete payload.type;
        }
        // Convert string numbers to integers for database
        // Note: employeeCount is calculated automatically, so we exclude it from updates
        const { employeeCount, ...payloadWithoutEmployeeCount } = payload;
        const processedPayload = {
            ...payloadWithoutEmployeeCount,
            foundedYear: payload.foundedYear
                ? Number(payload.foundedYear)
                : undefined,
        };
        const company = await prisma_js_1.prisma.company.update({
            where: { id: Number(id) },
            data: {
                ...processedPayload,
                contractTerms: Object.keys(contractTerms).length > 0 ? contractTerms : undefined,
                updatedAt: new Date(),
            },
            include: {
                contacts: true,
                workforceNeeds: true,
            },
        });
        res.json(company);
    }
    catch (error) {
        console.error("Error updating company:", error);
        if (error.code === "P2025") {
            return next(createError(404, "Company not found"));
        }
        next(createError(500, `Failed to update company: ${error.message || "Unknown error"}`));
    }
};
exports.updateCompany = updateCompany;
const deleteCompany = async (req, res, next) => {
    try {
        const { id } = req.params;
        await prisma_js_1.prisma.company.delete({
            where: { id: Number(id) },
        });
        res.status(204).send();
    }
    catch (error) {
        console.error("Error deleting company:", error);
        if (error.code === "P2025") {
            return next(createError(404, "Company not found"));
        }
        next(createError(500, `Failed to delete company: ${error.message || "Unknown error"}`));
    }
};
exports.deleteCompany = deleteCompany;
//# sourceMappingURL=company.controller.js.map