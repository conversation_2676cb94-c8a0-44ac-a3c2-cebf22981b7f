{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../*"], "@/*": ["../*"], "~~/*": ["../*"], "@@/*": ["../*"], "nitropack/types": ["../node_modules/.pnpm/nitropack@2.11.7_typescript@5.8.2_xml2js@0.6.2/node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/.pnpm/nitropack@2.11.7_typescript@5.8.2_xml2js@0.6.2/node_modules/nitropack/runtime"], "nitropack": ["../node_modules/.pnpm/nitropack@2.11.7_typescript@5.8.2_xml2js@0.6.2/node_modules/nitropack"], "defu": ["../node_modules/.pnpm/defu@6.1.4/node_modules/defu"], "h3": ["../node_modules/.pnpm/h3@1.15.1/node_modules/h3"], "consola": ["../node_modules/.pnpm/consola@3.4.2/node_modules/consola"], "ofetch": ["../node_modules/.pnpm/ofetch@1.4.1/node_modules/ofetch"], "@unhead/vue": ["../node_modules/.pnpm/@unhead+vue@2.0.0-rc.13_vue@3.5.13_typescript@5.8.2_/node_modules/@unhead/vue"], "@vue/runtime-core": ["../node_modules/.pnpm/@vue+runtime-core@3.4.21/node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/.pnpm/@vue+compiler-sfc@3.5.13/node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/.pnpm/unplugin-vue-router@0.12.0_vue-router@4.5.0_vue@3.5.13_typescript@5.8.2___vue@3.5.13_typescript@5.8.2_/node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/.pnpm/@nuxt+schema@3.16.1/node_modules/@nuxt/schema"], "nuxt": ["../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt"], "vite/client": ["../node_modules/.pnpm/vite@6.2.2_@types+node@22.14.0_jiti@2.4.2_lightningcss@1.29.2_terser@5.39.0_yaml@2.7.0/node_modules/vite/client"], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/core/runtime/nitro/utils/paths"], "#layers/tairo": ["../layers/tairo"], "#layers/tairo/*": ["../layers/tairo/*"], "#layers/accounting": ["../layers/accounting"], "#layers/accounting/*": ["../layers/accounting/*"], "#layers/budget": ["../layers/budget"], "#layers/budget/*": ["../layers/budget/*"], "#layers/companies": ["../layers/companies"], "#layers/companies/*": ["../layers/companies/*"], "#layers/core": ["../layers/core"], "#layers/core/*": ["../layers/core/*"], "#layers/hr": ["../layers/hr"], "#layers/hr/*": ["../layers/hr/*"], "#layers/production": ["../layers/production"], "#layers/production/*": ["../layers/production/*"], "#layers/recruitment": ["../layers/recruitment"], "#layers/recruitment/*": ["../layers/recruitment/*"], "#layers/@cssninja/tairo-layout-sidebar": ["../layers/tairo-layout-sidebar"], "#layers/@cssninja/tairo-layout-sidebar/*": ["../layers/tairo-layout-sidebar/*"], "#layers/landing": ["../layers/landing"], "#layers/landing/*": ["../layers/landing/*"], "#layers/@cssninja/tairo-layout-iconnav": ["../layers/tairo-layout-iconnav"], "#layers/@cssninja/tairo-layout-iconnav/*": ["../layers/tairo-layout-iconnav/*"], "vue-i18n": ["../node_modules/.pnpm/vue-i18n@10.0.6_vue@3.5.13_typescript@5.8.2_/node_modules/vue-i18n/dist/vue-i18n"], "@intlify/shared": ["../node_modules/.pnpm/@intlify+shared@10.0.6/node_modules/@intlify/shared/dist/shared"], "@intlify/message-compiler": ["../node_modules/.pnpm/@intlify+message-compiler@11.1.2/node_modules/@intlify/message-compiler/dist/message-compiler"], "@intlify/core-base": ["../node_modules/.pnpm/@intlify+core-base@10.0.6/node_modules/@intlify/core-base/dist/core-base"], "@intlify/core": ["../node_modules/.pnpm/@intlify+core@10.0.6/node_modules/@intlify/core/dist/core.node"], "@intlify/utils/h3": ["../node_modules/.pnpm/@intlify+utils@0.13.0/node_modules/@intlify/utils/dist/h3"], "ufo": ["../node_modules/.pnpm/ufo@1.5.4/node_modules/ufo/dist/index"], "is-https": ["../node_modules/.pnpm/is-https@4.0.0/node_modules/is-https/dist/index"], "#i18n": ["../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/dist/runtime/composables/index"], "#internal-i18n-types": ["../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/dist/types"], "#nuxt-i18n/logger": ["./nuxt-i18n-logger"], "#color-mode-options": ["./color-mode-options.mjs"], "#unhead/composables": ["../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/composables/v3"], "#nuxt-icon-server-bundle": ["./nuxt-icon-server-bundle"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./types/nitro-nuxt.d.ts", "../layers/tairo/modules/purge-comments.ts/runtime/server", "../node_modules/.pnpm/@pinia+nuxt@0.10.1_magicast@0.3.5_pinia@3.0.1_typescript@5.8.2_vue@3.5.13_typescript@5.8.2__/node_modules/@pinia/nuxt/runtime/server", "../../client/runtime/server", "../node_modules/.pnpm/@nuxtjs+ionic@0.16.0_@stencil+core@4.28.2_magicast@0.3.5_vue-router@4.5.0_vue@3.5.13_typescri_kvogt2gdziafhlhhzxvd6o57ta/node_modules/@nuxtjs/ionic/runtime/server", "../node_modules/.pnpm/@nuxt+eslint@1.2.0_@vue+compiler-sfc@3.5.13_eslint@8.57.1_magicast@0.3.5_typescript@5.8.2_vit_c2yug2rnfcmhnyyb4wqq6ulx4e/node_modules/@nuxt/eslint/runtime/server", "../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/runtime/server", "../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/runtime/server", "../../../AppData/Roaming/npm/node_modules/@nuxt/devtools/module.cjs/runtime/server", "../node_modules/.pnpm/@cssninja+nuxt-toaster@0.3.12_magicast@0.3.5_vue@3.5.13_typescript@5.8.2_/node_modules/@cssninja/nuxt-toaster/runtime/server", "../node_modules/.pnpm/@vueuse+nuxt@10.11.1_magicast@0.3.5_nuxt@3.11.2_@parcel+watcher@2.5.1_@types+node@22.14.0_db0_6bmhpeztrrzywfpk5w3srmppam/node_modules/@vueuse/nuxt/runtime/server", "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.13.2_magicast@0.3.5/node_modules/@nuxtjs/tailwindcss/runtime/server", "../node_modules/.pnpm/@nuxt+icon@1.11.0_magicast@0.3.5_vite@6.2.2_@types+node@22.14.0_jiti@2.4.2_lightningcss@1.29._4n4hvoiqlixjyr6n4h4n6fje7e/node_modules/@nuxt/icon/runtime/server", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime/server", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/node_modules", "../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/node_modules", "../node_modules/.pnpm/@pinia+nuxt@0.10.1_magicast@0.3.5_pinia@3.0.1_typescript@5.8.2_vue@3.5.13_typescript@5.8.2__/node_modules/@pinia/nuxt/node_modules", "../node_modules/.pnpm/pinia-plugin-persistedstate@4.2.0_@pinia+nuxt@0.10.1_magicast@0.3.5_pinia@3.0.1_typescript@5._kqpujbz6xdy7oo26diqtjfru6e/node_modules/pinia-plugin-persistedstate/node_modules", "../node_modules/.pnpm/@nuxtjs+ionic@0.16.0_@stencil+core@4.28.2_magicast@0.3.5_vue-router@4.5.0_vue@3.5.13_typescri_kvogt2gdziafhlhhzxvd6o57ta/node_modules/@nuxtjs/ionic/node_modules", "../node_modules/.pnpm/@nuxt+eslint@1.2.0_@vue+compiler-sfc@3.5.13_eslint@8.57.1_magicast@0.3.5_typescript@5.8.2_vit_c2yug2rnfcmhnyyb4wqq6ulx4e/node_modules/@nuxt/eslint/node_modules", "../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/node_modules", "../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/node_modules", "../../../AppData/Roaming/npm/node_modules/@nuxt/devtools/node_modules", "../node_modules/.pnpm/@cssninja+nuxt-toaster@0.3.12_magicast@0.3.5_vue@3.5.13_typescript@5.8.2_/node_modules/@cssninja/nuxt-toaster/node_modules", "../node_modules/.pnpm/@vueuse+nuxt@10.11.1_magicast@0.3.5_nuxt@3.11.2_@parcel+watcher@2.5.1_@types+node@22.14.0_db0_6bmhpeztrrzywfpk5w3srmppam/node_modules/@vueuse/nuxt/node_modules", "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.13.2_magicast@0.3.5/node_modules/@nuxtjs/tailwindcss/node_modules", "../node_modules/.pnpm/@nuxt+icon@1.11.0_magicast@0.3.5_vite@6.2.2_@types+node@22.14.0_jiti@2.4.2_lightningcss@1.29._4n4hvoiqlixjyr6n4h4n6fje7e/node_modules/@nuxt/icon/node_modules", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/node_modules", "../dist"]}