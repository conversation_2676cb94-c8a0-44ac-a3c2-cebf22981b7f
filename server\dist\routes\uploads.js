"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const upload_controller_js_1 = require("../controllers/uploads/upload.controller.js");
const router = express_1.default.Router();
// Upload avatar
router.post("/avatar", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(upload_controller_js_1.uploadAvatar));
// Upload user cover image
router.post("/cover", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(upload_controller_js_1.uploadCoverImage));
// Upload company logo
router.post("/company-logo/:companyId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(upload_controller_js_1.uploadCompanyLogo));
// Upload company cover
router.post("/company-cover/:companyId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(upload_controller_js_1.uploadCompanyCover));
// Upload document
router.post("/document{/:type}", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(upload_controller_js_1.uploadDocument));
exports.default = router;
//# sourceMappingURL=uploads.js.map