"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEndpointRequests = exports.deleteEndpoint = exports.updateEndpoint = exports.getEndpointById = exports.createEndpoint = exports.getEndpoints = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const crypto = __importStar(require("crypto"));
// Using centralized prisma instance from lib/prisma.js
// Get all endpoints for the authenticated user
const getEndpoints = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        const endpoints = await prisma_js_1.prisma.communicationEndpoint.findMany({
            where: {
                userId,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.status(200).json(endpoints);
    }
    catch (error) {
        console.error("Error fetching endpoints:", error);
        res.status(500).json({ error: "Failed to fetch endpoints" });
    }
};
exports.getEndpoints = getEndpoints;
// Create a new endpoint
const createEndpoint = async (req, res) => {
    try {
        const userId = req.user?.id;
        let { name, description, path, method, status, fields, companyId } = req.body;
        // Extract method value if it's an object
        if (method && typeof method === "object" && method.value) {
            method = method.value;
        }
        // Extract status value if it's an object
        if (status && typeof status === "object" && status.value) {
            status = status.value;
        }
        // Process fields to extract type values and ensure proper format
        if (fields && Array.isArray(fields)) {
            fields = fields.map((field) => {
                // Extract type value if it's an object
                const type = field.type && typeof field.type === "object"
                    ? field.type.value
                    : field.type;
                // Return a clean field object with only the necessary properties
                return {
                    name: field.name,
                    type,
                    required: field.required || false,
                    description: field.description || "",
                    validationRules: field.validationRules,
                };
            });
        }
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Validate required fields
        if (!name || !path || !method || !companyId) {
            res.status(400).json({ error: "Missing required fields" });
            return;
        }
        // Validate path format
        if (!path.startsWith("/")) {
            res.status(400).json({ error: "Path must start with /" });
            return;
        }
        // Check if path already exists
        const existingEndpoint = await prisma_js_1.prisma.communicationEndpoint.findFirst({
            where: {
                path,
                method,
            },
        });
        if (existingEndpoint) {
            res.status(400).json({
                error: "An endpoint with this path and method already exists",
            });
            return;
        }
        // Generate API key
        const apiKey = generateApiKey();
        // Check if the user exists
        const userExists = await prisma_js_1.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!userExists) {
            console.error(`User with ID ${userId} not found`);
            res.status(400).json({ error: `User with ID ${userId} not found` });
            return;
        }
        // Ensure companyId is a properly formatted string
        const formattedCompanyId = String(companyId).trim();
        console.log("Looking for company with ID:", formattedCompanyId);
        // Debug: List all companies to verify they exist
        const allCompanies = await prisma_js_1.prisma.company.findMany({ take: 10 });
        console.log("Sample of companies in DB:", allCompanies.map((c) => ({ id: c.id, name: c.name })));
        // Try a more direct query approach
        const companyQuery = `SELECT * FROM "Company" WHERE id = $1`;
        const companyResult = await prisma_js_1.prisma.$queryRawUnsafe(companyQuery, formattedCompanyId);
        console.log("Direct SQL query result:", companyResult);
        // Check if the company exists using the raw query result
        const companyExists = companyResult && Array.isArray(companyResult) && companyResult.length > 0
            ? companyResult[0]
            : null;
        console.log("Company exists:", companyExists);
        if (!companyExists) {
            console.error(`Company with ID ${formattedCompanyId} not found`);
            res
                .status(400)
                .json({ error: `Company with ID ${formattedCompanyId} not found` });
            return;
        }
        // Create the endpoint
        const endpoint = await prisma_js_1.prisma.communicationEndpoint.create({
            data: {
                userId,
                companyId,
                name,
                description,
                path,
                method,
                apiKey,
                status: status || "ACTIVE",
                fields: fields || [],
            },
        });
        res.status(201).json(endpoint);
    }
    catch (error) {
        console.error("Error creating endpoint:", error);
        // Provide more detailed error information
        if (error.code === "P2003") {
            // Foreign key constraint failed
            const field = error.meta?.field_name || "unknown field";
            res.status(400).json({
                error: `Foreign key constraint failed on ${field}. Make sure the referenced record exists.`,
                details: error.message,
            });
        }
        else if (error.code === "P2002") {
            // Unique constraint failed
            const target = error.meta?.target || "unknown field";
            res.status(400).json({
                error: `A record with this ${target} already exists.`,
                details: error.message,
            });
        }
        else {
            // Generic error
            res.status(500).json({
                error: "Failed to create endpoint",
                details: error.message || "Unknown error",
            });
        }
    }
};
exports.createEndpoint = createEndpoint;
// Get a specific endpoint by ID
const getEndpointById = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { endpointId } = req.params;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        const endpoint = await prisma_js_1.prisma.communicationEndpoint.findFirst({
            where: {
                id: Number(endpointId),
                userId,
            },
        });
        if (!endpoint) {
            res.status(404).json({ error: "Endpoint not found" });
            return;
        }
        res.status(200).json(endpoint);
    }
    catch (error) {
        console.error("Error fetching endpoint:", error);
        res.status(500).json({ error: "Failed to fetch endpoint" });
    }
};
exports.getEndpointById = getEndpointById;
// Update an endpoint
const updateEndpoint = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { endpointId } = req.params;
        let { name, description, status, fields } = req.body;
        // Extract status value if it's an object
        if (status && typeof status === "object" && status.value) {
            status = status.value;
        }
        // Process fields to extract type values
        if (fields && Array.isArray(fields)) {
            fields = fields.map((field) => ({
                ...field,
                type: field.type && typeof field.type === "object"
                    ? field.type.value
                    : field.type,
            }));
        }
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Find the endpoint
        const endpoint = await prisma_js_1.prisma.communicationEndpoint.findFirst({
            where: {
                id: Number(endpointId),
                userId,
            },
        });
        if (!endpoint) {
            res.status(404).json({ error: "Endpoint not found" });
            return;
        }
        // Update the endpoint
        const updatedEndpoint = await prisma_js_1.prisma.communicationEndpoint.update({
            where: {
                id: Number(endpointId),
            },
            data: {
                name,
                description,
                status,
                fields,
                // updatedAt is handled automatically by Prisma
            },
        });
        res.status(200).json(updatedEndpoint);
    }
    catch (error) {
        console.error("Error updating endpoint:", error);
        res.status(500).json({ error: "Failed to update endpoint" });
    }
};
exports.updateEndpoint = updateEndpoint;
// Delete an endpoint
const deleteEndpoint = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { endpointId } = req.params;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Find the endpoint
        const endpoint = await prisma_js_1.prisma.communicationEndpoint.findFirst({
            where: {
                id: Number(endpointId),
                userId,
            },
        });
        if (!endpoint) {
            res.status(404).json({ error: "Endpoint not found" });
            return;
        }
        // Delete all requests for this endpoint
        await prisma_js_1.prisma.endpointRequest.deleteMany({
            where: {
                endpointId: Number(endpointId),
            },
        });
        // Delete the endpoint
        await prisma_js_1.prisma.communicationEndpoint.delete({
            where: {
                id: Number(endpointId),
            },
        });
        res.status(204).send();
    }
    catch (error) {
        console.error("Error deleting endpoint:", error);
        res.status(500).json({ error: "Failed to delete endpoint" });
    }
};
exports.deleteEndpoint = deleteEndpoint;
// Get requests for a specific endpoint
const getEndpointRequests = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { endpointId } = req.params;
        const { limit = "50", offset = "0" } = req.query;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Find the endpoint
        const endpoint = await prisma_js_1.prisma.communicationEndpoint.findFirst({
            where: {
                id: Number(endpointId),
                userId,
            },
        });
        if (!endpoint) {
            res.status(404).json({ error: "Endpoint not found" });
            return;
        }
        // Get requests
        const requests = await prisma_js_1.prisma.endpointRequest.findMany({
            where: {
                endpointId: Number(endpointId),
            },
            orderBy: {
                createdAt: "desc",
            },
            take: parseInt(limit),
            skip: parseInt(offset),
        });
        // Get total count
        const totalCount = await prisma_js_1.prisma.endpointRequest.count({
            where: {
                endpointId: Number(endpointId),
            },
        });
        res.status(200).json({
            requests,
            totalCount,
        });
    }
    catch (error) {
        console.error("Error fetching endpoint requests:", error);
        res.status(500).json({ error: "Failed to fetch endpoint requests" });
    }
};
exports.getEndpointRequests = getEndpointRequests;
// Helper function to generate a random API key
function generateApiKey() {
    return crypto.randomUUID().replace(/-/g, "");
}
//# sourceMappingURL=endpoint.controller.js.map