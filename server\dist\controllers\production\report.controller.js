"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEfficiencyMetrics = exports.getQualityMetrics = exports.deleteMetric = exports.updateMetric = exports.addMetric = exports.deleteReport = exports.updateReport = exports.createReport = exports.getReportById = exports.getAllReports = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Get all production reports
const getAllReports = async (req, res, next) => {
    try {
        const { projectId, reportType, startDate, endDate } = req.query;
        const whereClause = {
            project: {
                companyId: req.user?.companyId || undefined,
            },
        };
        if (projectId) {
            whereClause.projectId = Number(projectId);
        }
        if (reportType) {
            whereClause.reportType = reportType;
        }
        if (startDate && endDate) {
            whereClause.reportDate = {
                gte: new Date(startDate),
                lte: new Date(endDate),
            };
        }
        const reports = await prisma_js_1.prisma.productionReport.findMany({
            where: whereClause,
            include: {
                project: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                metrics: {
                    select: {
                        id: true,
                        name: true,
                        value: true,
                        unit: true,
                        category: true,
                    },
                },
            },
            orderBy: {
                reportDate: "desc",
            },
        });
        res.json(reports);
    }
    catch (error) {
        console.error("Error fetching production reports:", error);
        next(error);
    }
};
exports.getAllReports = getAllReports;
// Get report by ID
const getReportById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const report = await prisma_js_1.prisma.productionReport.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                project: true,
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                metrics: {
                    orderBy: {
                        category: "asc",
                    },
                },
            },
        });
        if (!report) {
            return next(createHttpError(404, "Report not found"));
        }
        res.json(report);
    }
    catch (error) {
        console.error("Error fetching report:", error);
        next(error);
    }
};
exports.getReportById = getReportById;
// Create production report
const createReport = async (req, res, next) => {
    try {
        const { title, description, projectId, reportDate, reportType, metrics } = req.body;
        // Create report
        const report = await prisma_js_1.prisma.productionReport.create({
            data: {
                title,
                description,
                projectId: Number(projectId),
                reportDate: reportDate ? new Date(reportDate) : new Date(),
                reportType: reportType || "DAILY",
                createdById: req.user?.id || 1,
                status: "DRAFT",
                metrics: {
                    create: metrics?.map((metric) => ({
                        name: metric.name,
                        value: metric.value,
                        unit: metric.unit,
                        target: metric.target,
                        category: metric.category || "EFFICIENCY",
                        notes: metric.notes,
                    })) || [],
                },
            },
            include: {
                metrics: true,
            },
        });
        res.status(201).json(report);
    }
    catch (error) {
        console.error("Error creating production report:", error);
        next(error);
    }
};
exports.createReport = createReport;
// Update production report
const updateReport = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { title, description, reportDate, reportType, status } = req.body;
        const report = await prisma_js_1.prisma.productionReport.update({
            where: {
                id: Number(id),
            },
            data: {
                title,
                description,
                reportDate: reportDate ? new Date(reportDate) : undefined,
                reportType,
                status,
            },
        });
        res.json(report);
    }
    catch (error) {
        console.error("Error updating production report:", error);
        next(error);
    }
};
exports.updateReport = updateReport;
// Delete production report
const deleteReport = async (req, res, next) => {
    try {
        const { id } = req.params;
        await prisma_js_1.prisma.productionReport.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Production report deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting production report:", error);
        next(error);
    }
};
exports.deleteReport = deleteReport;
// Add metric to report
const addMetric = async (req, res, next) => {
    try {
        const { reportId } = req.params;
        const { name, value, unit, target, category, notes } = req.body;
        const metric = await prisma_js_1.prisma.productionMetric.create({
            data: {
                reportId: Number(reportId),
                name,
                value,
                unit,
                target,
                category: category || "EFFICIENCY",
                notes,
            },
        });
        res.status(201).json(metric);
    }
    catch (error) {
        console.error("Error adding metric to report:", error);
        next(error);
    }
};
exports.addMetric = addMetric;
// Update metric
const updateMetric = async (req, res, next) => {
    try {
        const { metricId } = req.params;
        const { name, value, unit, target, category, notes } = req.body;
        const metric = await prisma_js_1.prisma.productionMetric.update({
            where: {
                id: Number(metricId),
            },
            data: {
                name,
                value,
                unit,
                target,
                category,
                notes,
            },
        });
        res.json(metric);
    }
    catch (error) {
        console.error("Error updating metric:", error);
        next(error);
    }
};
exports.updateMetric = updateMetric;
// Delete metric
const deleteMetric = async (req, res, next) => {
    try {
        const { metricId } = req.params;
        await prisma_js_1.prisma.productionMetric.delete({
            where: {
                id: Number(metricId),
            },
        });
        res.json({ message: "Metric deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting metric:", error);
        next(error);
    }
};
exports.deleteMetric = deleteMetric;
// Get quality metrics
const getQualityMetrics = async (req, res, next) => {
    try {
        const { projectId, startDate, endDate } = req.query;
        const whereClause = {
            project: {
                companyId: req.user?.companyId || undefined,
            },
        };
        if (projectId) {
            whereClause.projectId = Number(projectId);
        }
        if (startDate && endDate) {
            whereClause.createdAt = {
                gte: new Date(startDate),
                lte: new Date(endDate),
            };
        }
        // Get quality checklists
        const checklists = await prisma_js_1.prisma.qualityChecklist.findMany({
            where: whereClause,
            include: {
                project: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                items: true,
            },
        });
        // Calculate quality metrics
        const projectMetrics = {};
        checklists.forEach((checklist) => {
            const projectId = checklist.projectId;
            if (!projectMetrics[projectId]) {
                projectMetrics[projectId] = {
                    projectId,
                    projectName: checklist.project.name,
                    totalChecklists: 0,
                    passedChecklists: 0,
                    failedChecklists: 0,
                    pendingChecklists: 0,
                    totalItems: 0,
                    passedItems: 0,
                    failedItems: 0,
                    pendingItems: 0,
                    passRate: 0,
                };
            }
            projectMetrics[projectId].totalChecklists++;
            // Determine checklist status based on items
            const passedItems = checklist.items.filter((item) => item.status === "PASSED").length;
            const failedItems = checklist.items.filter((item) => item.status === "FAILED").length;
            const checklistStatus = failedItems > 0
                ? "FAILED"
                : passedItems === checklist.items.length
                    ? "PASSED"
                    : "PENDING";
            if (checklistStatus === "PASSED") {
                projectMetrics[projectId].passedChecklists++;
            }
            else if (checklistStatus === "FAILED") {
                projectMetrics[projectId].failedChecklists++;
            }
            else {
                projectMetrics[projectId].pendingChecklists++;
            }
            checklist.items.forEach((item) => {
                projectMetrics[projectId].totalItems++;
                if (item.status === "PASSED") {
                    projectMetrics[projectId].passedItems++;
                }
                else if (item.status === "FAILED") {
                    projectMetrics[projectId].failedItems++;
                }
                else if (item.status === "PENDING") {
                    projectMetrics[projectId].pendingItems++;
                }
            });
        });
        // Calculate pass rates
        Object.values(projectMetrics).forEach((metric) => {
            const completedItems = metric.passedItems + metric.failedItems;
            metric.passRate =
                completedItems > 0
                    ? ((metric.passedItems / completedItems) * 100).toFixed(2)
                    : 0;
        });
        res.json(Object.values(projectMetrics));
    }
    catch (error) {
        console.error("Error fetching quality metrics:", error);
        next(error);
    }
};
exports.getQualityMetrics = getQualityMetrics;
// Get efficiency metrics
const getEfficiencyMetrics = async (req, res, next) => {
    try {
        const { projectId, startDate, endDate } = req.query;
        const whereClause = {
            project: {
                companyId: req.user?.companyId || undefined,
            },
        };
        if (projectId) {
            whereClause.projectId = Number(projectId);
        }
        // Get projects
        const projects = await prisma_js_1.prisma.project.findMany({
            where: {
                companyId: Number(req.user?.companyId) || undefined,
                ...(projectId ? { id: Number(projectId) } : {}),
            },
            include: {
                tasks: {
                    where: startDate && endDate
                        ? {
                            startDate: {
                                gte: new Date(startDate),
                            },
                            endDate: {
                                lte: new Date(endDate),
                            },
                        }
                        : undefined,
                },
                resourceAllocations: {
                    where: startDate && endDate
                        ? {
                            startDate: {
                                gte: new Date(startDate),
                            },
                            endDate: {
                                lte: new Date(endDate),
                            },
                        }
                        : undefined,
                },
                productionReports: {
                    where: {
                        metrics: {
                            some: {
                                category: "EFFICIENCY",
                            },
                        },
                        ...(startDate && endDate
                            ? {
                                reportDate: {
                                    gte: new Date(startDate),
                                    lte: new Date(endDate),
                                },
                            }
                            : {}),
                    },
                    include: {
                        metrics: {
                            where: {
                                category: "EFFICIENCY",
                            },
                        },
                    },
                },
            },
        });
        // Calculate efficiency metrics
        const efficiencyMetrics = projects.map((project) => {
            // Task completion metrics
            const totalTasks = project.tasks.length;
            const completedTasks = project.tasks.filter((task) => task.status === "COMPLETED").length;
            const taskCompletionRate = totalTasks > 0 ? ((completedTasks / totalTasks) * 100).toFixed(2) : 0;
            // Time efficiency
            const tasksWithTimeData = project.tasks.filter((task) => task.estimatedHours !== null && task.actualHours !== null);
            let timeEfficiency = 0;
            if (tasksWithTimeData.length > 0) {
                const totalEstimatedHours = tasksWithTimeData.reduce((sum, task) => sum + (task.estimatedHours || 0), 0);
                const totalActualHours = tasksWithTimeData.reduce((sum, task) => sum + (task.actualHours || 0), 0);
                timeEfficiency =
                    totalEstimatedHours > 0
                        ? Number(((totalEstimatedHours / totalActualHours) * 100).toFixed(2))
                        : 0;
            }
            // Resource utilization
            const resourceAllocations = project.resourceAllocations;
            const equipmentCount = resourceAllocations.filter((ra) => ra.equipmentId).length;
            const materialCount = resourceAllocations.filter((ra) => ra.materialId).length;
            const workerCount = resourceAllocations.filter((ra) => ra.userId).length;
            // Custom efficiency metrics from reports
            const reportMetrics = project.productionReports.flatMap((report) => report.metrics);
            const customMetrics = reportMetrics.map((metric) => ({
                name: metric.name,
                value: metric.value,
                unit: metric.unit,
                target: metric.target,
            }));
            return {
                projectId: project.id,
                projectName: project.name,
                taskMetrics: {
                    totalTasks,
                    completedTasks,
                    taskCompletionRate,
                },
                timeMetrics: {
                    timeEfficiency,
                    tasksWithTimeData: tasksWithTimeData.length,
                },
                resourceMetrics: {
                    equipmentCount,
                    materialCount,
                    workerCount,
                    totalAllocations: resourceAllocations.length,
                },
                customMetrics,
            };
        });
        res.json(efficiencyMetrics);
    }
    catch (error) {
        console.error("Error fetching efficiency metrics:", error);
        next(error);
    }
};
exports.getEfficiencyMetrics = getEfficiencyMetrics;
//# sourceMappingURL=report.controller.js.map