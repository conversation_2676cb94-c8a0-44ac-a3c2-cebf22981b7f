{"version": 3, "file": "subscriptionPlans.js", "sourceRoot": "", "sources": ["../../../prisma/seed/subscriptionPlans.ts"], "names": [], "mappings": ";;AAIA,sDAwDC;AA5DD,2CAAkF;AAElF,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAE3B,KAAK,UAAU,qBAAqB;IACzC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAE7C,+BAA+B;IAC/B,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;IAE/D,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,SAAS,aAAa,CAAC,MAAM,8CAA8C,CAAC,CAAC;QACzF,OAAO;IACT,CAAC;IAED,4BAA4B;IAC5B,MAAM,KAAK,GAAG;QACZ;YACE,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,uCAAuC;YACpD,IAAI,EAAE,6BAAoB,CAAC,UAAU;YACrC,YAAY,EAAE,CAAC;YACf,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,CAAC;YACX,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,CAAC,aAAa,EAAE,gBAAgB,EAAE,SAAS,CAAC;YACtD,OAAO,EAAE,CAAC,MAAM,CAAC;SAClB;QACD;YACE,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,2CAA2C;YACxD,IAAI,EAAE,6BAAoB,CAAC,OAAO;YAClC,YAAY,EAAE,KAAK;YACnB,WAAW,EAAE,MAAM;YACnB,QAAQ,EAAE,CAAC;YACX,iBAAiB,EAAE,IAAI;YACvB,QAAQ,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,CAAC;YACtE,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;SAC5B;QACD;YACE,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,yCAAyC;YACtD,IAAI,EAAE,6BAAoB,CAAC,OAAO;YAClC,YAAY,EAAE,KAAK;YACnB,WAAW,EAAE,MAAM;YACnB,QAAQ,EAAE,EAAE;YACZ,iBAAiB,EAAE,IAAI;YACvB,QAAQ,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,kBAAkB,EAAE,qBAAqB,CAAC;YAChF,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,CAAC;SAC5E;KACF,CAAC;IAEF,6BAA6B;IAC7B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACnC,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;IACL,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;AACzD,CAAC;AAED,0DAA0D;AAC1D,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,qBAAqB,EAAE;SACpB,IAAI,CAAC,KAAK,IAAI,EAAE;QACf,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC,CAAC;SACD,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;QACjB,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjB,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}