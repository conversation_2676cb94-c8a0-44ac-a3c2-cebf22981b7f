"use strict";
// server/controllers/calendar/calendar.controller.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCalendarEvents = getCalendarEvents;
exports.createCalendarEvent = createCalendarEvent;
exports.updateCalendarEvent = updateCalendarEvent;
exports.deleteCalendarEvent = deleteCalendarEvent;
exports.getEventCategories = getEventCategories;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// Get calendar events with role-based filtering
async function getCalendarEvents(req, res) {
    try {
        const { startDate, endDate, categories } = req.query;
        const userId = req.user.id;
        const userRoles = req.user.roles;
        const companyId = req.user.companyId;
        // Helper function to check if user has any of the specified roles
        const hasAnyRole = (roles) => {
            return roles.some((role) => userRoles.includes(role));
        };
        // Build where clause based on user role
        let whereClause = {
            companyId: companyId,
        };
        // Add date filtering if provided
        if (startDate && endDate) {
            whereClause.startDate = {
                gte: new Date(startDate),
                lte: new Date(endDate),
            };
        }
        // Add category filtering if provided
        if (categories && typeof categories === "string" && categories.length > 0) {
            whereClause.category = {
                in: categories.split(","),
            };
        }
        // Role-based filtering
        if (hasAnyRole(["SUPERADMIN", "ADMIN"])) {
            // Can see all events across all companies
            delete whereClause.companyId;
        }
        else if (hasAnyRole(["PROJECTLEADER"])) {
            // Can see project tasks, team activities, company events, client meetings
            whereClause.OR = [
                { category: { in: ["PROJECT", "MEETING", "COMPANY", "TASK"] } },
                { participants: { some: { userId: userId } } },
            ];
        }
        else if (hasAnyRole(["SALESMAN"])) {
            // Can see sales activities, client meetings, deal deadlines
            whereClause.OR = [
                { category: { in: ["SALES", "MEETING", "DEADLINE", "COMPANY"] } },
                { participants: { some: { userId: userId } } },
            ];
        }
        else if (hasAnyRole(["WORKER"])) {
            // Can see assigned tasks, personal time entries, team meetings
            whereClause.OR = [
                { category: { in: ["TASK", "MEETING", "PERSONAL", "COMPANY"] } },
                { participants: { some: { userId: userId } } },
            ];
        }
        else if (hasAnyRole(["CLIENT"])) {
            // Can only see events they're invited to
            whereClause.participants = {
                some: { userId: userId },
            };
        }
        else {
            // Default to user's own events only
            whereClause.participants = {
                some: { userId: userId },
            };
        }
        const events = await prisma.calendarEvent.findMany({
            where: whereClause,
            include: {
                participants: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                email: true,
                                avatar: true,
                            },
                        },
                    },
                },
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                reminders: true,
            },
            orderBy: {
                startDate: "asc",
            },
        });
        res.json(events);
    }
    catch (error) {
        console.error("Error fetching calendar events:", error);
        res.status(500).json({ error: "Failed to fetch calendar events" });
    }
}
// Create a new calendar event
async function createCalendarEvent(req, res) {
    try {
        const { title, description, startDate, endDate, category, participantIds = [], reminderMinutes = 15, } = req.body;
        const userId = req.user.id;
        const companyId = req.user.companyId;
        // Calculate duration in minutes
        const duration = Math.round((new Date(endDate).getTime() - new Date(startDate).getTime()) /
            (1000 * 60));
        const event = await prisma.calendarEvent.create({
            data: {
                title,
                description,
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                duration,
                category,
                companyId,
                createdById: userId,
                participants: {
                    create: [
                        // Add creator as participant
                        {
                            userId: userId,
                            status: client_1.ParticipantStatus.ACCEPTED,
                        },
                        // Add other participants (excluding creator to avoid duplicates)
                        ...participantIds
                            .filter((participantId) => participantId !== userId)
                            .map((participantId) => ({
                            userId: participantId,
                            status: client_1.ParticipantStatus.PENDING,
                        })),
                    ],
                },
                reminders: reminderMinutes > 0
                    ? {
                        create: [
                            {
                                user: {
                                    connect: { id: userId },
                                },
                                reminderType: "EMAIL",
                                minutesBefore: reminderMinutes,
                            },
                        ],
                    }
                    : undefined,
            },
            include: {
                participants: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                email: true,
                                avatar: true,
                            },
                        },
                    },
                },
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                reminders: true,
            },
        });
        res.status(201).json(event);
    }
    catch (error) {
        console.error("Error creating calendar event:", error);
        res.status(500).json({ error: "Failed to create calendar event" });
    }
}
// Update calendar event
async function updateCalendarEvent(req, res) {
    try {
        const { id } = req.params;
        const { title, description, startDate, endDate, category, participantIds, } = req.body;
        console.log(`🔄 Updating event ${id} with data:`, {
            title,
            description,
            startDate,
            endDate,
            category,
            participantIds,
        });
        const userId = req.user.id;
        const userRoles = req.user.roles;
        // Check if user can edit this event
        const existingEvent = await prisma.calendarEvent.findUnique({
            where: { id: parseInt(id) },
            include: { participants: true },
        });
        if (!existingEvent) {
            return res.status(404).json({ error: "Event not found" });
        }
        console.log(`📋 Existing event ${id} data:`, {
            title: existingEvent.title,
            startDate: existingEvent.startDate.toISOString(),
            endDate: existingEvent.endDate.toISOString(),
            category: existingEvent.category,
            updatedAt: existingEvent.updatedAt.toISOString(),
        });
        // Check permissions
        const canEdit = userRoles.includes("SUPERADMIN") ||
            userRoles.includes("ADMIN") ||
            existingEvent.createdById === userId ||
            existingEvent.participants.some((p) => p.userId === userId);
        if (!canEdit) {
            return res
                .status(403)
                .json({ error: "Not authorized to edit this event" });
        }
        // Calculate duration if dates are provided
        let updateData = { title, description, category };
        if (startDate && endDate) {
            updateData.startDate = new Date(startDate);
            updateData.endDate = new Date(endDate);
            updateData.duration = Math.round((new Date(endDate).getTime() - new Date(startDate).getTime()) /
                (1000 * 60));
        }
        // Update participants if provided
        if (participantIds !== undefined) {
            // First, delete existing participants
            await prisma.eventParticipant.deleteMany({
                where: { eventId: parseInt(id) },
            });
            // Then, add new participants
            if (participantIds.length > 0) {
                const participantsToCreate = [
                    // Add creator as participant if not already included
                    ...(participantIds.includes(userId)
                        ? []
                        : [
                            {
                                eventId: parseInt(id),
                                userId: userId,
                                status: client_1.ParticipantStatus.ACCEPTED,
                            },
                        ]),
                    // Add other participants
                    ...participantIds.map((participantId) => ({
                        eventId: parseInt(id),
                        userId: participantId,
                        status: participantId === userId
                            ? client_1.ParticipantStatus.ACCEPTED
                            : client_1.ParticipantStatus.PENDING,
                    })),
                ];
                await prisma.eventParticipant.createMany({
                    data: participantsToCreate,
                });
            }
            else {
                // If no participants provided, at least add the creator
                await prisma.eventParticipant.create({
                    data: {
                        eventId: parseInt(id),
                        userId: userId,
                        status: client_1.ParticipantStatus.ACCEPTED,
                    },
                });
            }
        }
        console.log(`💾 Updating event ${id} with updateData:`, updateData);
        const event = await prisma.calendarEvent.update({
            where: { id: parseInt(id) },
            data: updateData,
            include: {
                participants: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                email: true,
                                avatar: true,
                            },
                        },
                    },
                },
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                reminders: true,
            },
        });
        console.log(`✅ Event ${id} updated successfully:`, {
            title: event.title,
            startDate: event.startDate.toISOString(),
            endDate: event.endDate.toISOString(),
            category: event.category,
            updatedAt: event.updatedAt.toISOString(),
        });
        res.json(event);
    }
    catch (error) {
        console.error("Error updating calendar event:", error);
        res.status(500).json({ error: "Failed to update calendar event" });
    }
}
// Delete calendar event
async function deleteCalendarEvent(req, res) {
    try {
        const { id } = req.params;
        const userId = req.user.id;
        const userRoles = req.user.roles;
        // Check if user can delete this event
        const existingEvent = await prisma.calendarEvent.findUnique({
            where: { id: parseInt(id) },
        });
        if (!existingEvent) {
            return res.status(404).json({ error: "Event not found" });
        }
        // Check permissions
        const canDelete = userRoles.includes("SUPERADMIN") ||
            userRoles.includes("ADMIN") ||
            existingEvent.createdById === userId;
        if (!canDelete) {
            return res
                .status(403)
                .json({ error: "Not authorized to delete this event" });
        }
        await prisma.calendarEvent.delete({
            where: { id: parseInt(id) },
        });
        res.json({ message: "Event deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting calendar event:", error);
        res.status(500).json({ error: "Failed to delete calendar event" });
    }
}
// Get available event categories based on user role
async function getEventCategories(req, res) {
    try {
        const userRoles = req.user.roles;
        let categories = [];
        if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
            categories = [
                {
                    id: "ALL",
                    name: "Kõik sündmused",
                    icon: "lucide:calendar",
                    color: "#6366f1",
                },
                {
                    id: "COMPANY",
                    name: "Ettevõtte sündmused",
                    icon: "lucide:building",
                    color: "#8b5cf6",
                },
                {
                    id: "PROJECT",
                    name: "Projekti ülesanded",
                    icon: "lucide:folder",
                    color: "#06b6d4",
                },
                {
                    id: "MEETING",
                    name: "Kohtumised",
                    icon: "lucide:users",
                    color: "#10b981",
                },
                {
                    id: "SALES",
                    name: "Müügitegevused",
                    icon: "lucide:trending-up",
                    color: "#f59e0b",
                },
                {
                    id: "TASK",
                    name: "Ülesanded",
                    icon: "lucide:check-square",
                    color: "#ef4444",
                },
                {
                    id: "DEADLINE",
                    name: "Tähtajad",
                    icon: "lucide:clock",
                    color: "#dc2626",
                },
                {
                    id: "PERSONAL",
                    name: "Isiklik",
                    icon: "lucide:user",
                    color: "#6b7280",
                },
            ];
        }
        else if (userRoles.includes("PROJECTLEADER")) {
            categories = [
                {
                    id: "ALL",
                    name: "Kõik sündmused",
                    icon: "lucide:calendar",
                    color: "#6366f1",
                },
                {
                    id: "PROJECT",
                    name: "Projekti ülesanded",
                    icon: "lucide:folder",
                    color: "#06b6d4",
                },
                {
                    id: "MEETING",
                    name: "Kohtumised",
                    icon: "lucide:users",
                    color: "#10b981",
                },
                {
                    id: "TASK",
                    name: "Ülesanded",
                    icon: "lucide:check-square",
                    color: "#ef4444",
                },
                {
                    id: "COMPANY",
                    name: "Ettevõtte sündmused",
                    icon: "lucide:building",
                    color: "#8b5cf6",
                },
            ];
        }
        else if (userRoles.includes("SALESMAN")) {
            categories = [
                {
                    id: "ALL",
                    name: "Kõik sündmused",
                    icon: "lucide:calendar",
                    color: "#6366f1",
                },
                {
                    id: "SALES",
                    name: "Müügitegevused",
                    icon: "lucide:trending-up",
                    color: "#f59e0b",
                },
                {
                    id: "MEETING",
                    name: "Kliendi kohtumised",
                    icon: "lucide:users",
                    color: "#10b981",
                },
                {
                    id: "DEADLINE",
                    name: "Tehingu tähtajad",
                    icon: "lucide:clock",
                    color: "#dc2626",
                },
                {
                    id: "COMPANY",
                    name: "Ettevõtte sündmused",
                    icon: "lucide:building",
                    color: "#8b5cf6",
                },
            ];
        }
        else if (userRoles.includes("WORKER")) {
            categories = [
                {
                    id: "ALL",
                    name: "Kõik sündmused",
                    icon: "lucide:calendar",
                    color: "#6366f1",
                },
                {
                    id: "TASK",
                    name: "Minu ülesanded",
                    icon: "lucide:check-square",
                    color: "#ef4444",
                },
                {
                    id: "MEETING",
                    name: "Meeskonna kohtumised",
                    icon: "lucide:users",
                    color: "#10b981",
                },
                {
                    id: "PERSONAL",
                    name: "Isiklik",
                    icon: "lucide:user",
                    color: "#6b7280",
                },
                {
                    id: "COMPANY",
                    name: "Ettevõtte sündmused",
                    icon: "lucide:building",
                    color: "#8b5cf6",
                },
            ];
        }
        else if (userRoles.includes("CLIENT")) {
            categories = [
                {
                    id: "ALL",
                    name: "Kõik sündmused",
                    icon: "lucide:calendar",
                    color: "#6366f1",
                },
                {
                    id: "MEETING",
                    name: "Kohtumised",
                    icon: "lucide:users",
                    color: "#10b981",
                },
                {
                    id: "PROJECT",
                    name: "Projekti verstapostid",
                    icon: "lucide:folder",
                    color: "#06b6d4",
                },
            ];
        }
        else {
            categories = [
                {
                    id: "ALL",
                    name: "Kõik sündmused",
                    icon: "lucide:calendar",
                    color: "#6366f1",
                },
            ];
        }
        res.json(categories);
    }
    catch (error) {
        console.error("Error fetching event categories:", error);
        res.status(500).json({ error: "Failed to fetch event categories" });
    }
}
//# sourceMappingURL=calendar.controller.js.map