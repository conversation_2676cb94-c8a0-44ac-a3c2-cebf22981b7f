"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTimeToHireAnalysis = exports.getSourcesAnalysis = exports.getTopPerformingJobs = exports.getApplicationFunnel = exports.getMetricsByPeriod = exports.getAnalyticsOverview = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// Get recruitment analytics overview
const getAnalyticsOverview = async (req, res) => {
    try {
        const { companyId } = req.params;
        const { startDate, endDate } = req.query;
        const dateFilter = {};
        if (startDate)
            dateFilter.gte = new Date(startDate);
        if (endDate)
            dateFilter.lte = new Date(endDate);
        const whereClause = {
            companyId: parseInt(companyId),
            ...(Object.keys(dateFilter).length > 0 && { createdAt: dateFilter })
        };
        // Get basic metrics
        const [totalJobs, activeJobs, totalApplications, pendingApplications, shortlistedApplications, interviewsScheduled, hiredCandidates] = await Promise.all([
            prisma.jobPosting.count({ where: { companyId: parseInt(companyId) } }),
            prisma.jobPosting.count({ where: { companyId: parseInt(companyId), status: 'ACTIVE' } }),
            prisma.jobApplication.count({
                where: { job: whereClause }
            }),
            prisma.jobApplication.count({
                where: { job: whereClause, status: 'PENDING' }
            }),
            prisma.jobApplication.count({
                where: { job: whereClause, status: 'SHORTLISTED' }
            }),
            prisma.interview.count({
                where: {
                    application: { job: whereClause },
                    status: 'SCHEDULED'
                }
            }),
            prisma.jobApplication.count({
                where: { job: whereClause, status: 'HIRED' }
            })
        ]);
        // Calculate time-to-hire metrics
        const hiredApplications = await prisma.jobApplication.findMany({
            where: {
                job: whereClause,
                status: 'HIRED'
            },
            select: {
                createdAt: true,
                reviewedAt: true
            }
        });
        const avgTimeToHire = hiredApplications.length > 0
            ? hiredApplications.reduce((acc, app) => {
                if (app.reviewedAt) {
                    const days = Math.ceil((app.reviewedAt.getTime() - app.createdAt.getTime()) / (1000 * 60 * 60 * 24));
                    return acc + days;
                }
                return acc;
            }, 0) / hiredApplications.length
            : null;
        // Get application sources
        const applicationSources = await prisma.jobApplication.groupBy({
            by: ['applicantEmail'],
            where: { job: whereClause },
            _count: true
        });
        const sourceData = {
            website: applicationSources.length,
            referral: 0,
            linkedin: 0,
            other: 0
        };
        res.json({
            totalJobs,
            activeJobs,
            totalApplications,
            pendingApplications,
            shortlistedApplications,
            interviewsScheduled,
            hiredCandidates,
            avgTimeToHire,
            applicationSources: sourceData
        });
    }
    catch (error) {
        console.error('Error fetching analytics overview:', error);
        res.status(500).json({ error: 'Failed to fetch analytics' });
    }
};
exports.getAnalyticsOverview = getAnalyticsOverview;
// Get recruitment metrics by time period
const getMetricsByPeriod = async (req, res) => {
    try {
        const { companyId } = req.params;
        const { period = 'month' } = req.query;
        let groupBy;
        let dateFormat;
        switch (period) {
            case 'week':
                groupBy = 'week';
                dateFormat = '%Y-%u';
                break;
            case 'month':
                groupBy = 'month';
                dateFormat = '%Y-%m';
                break;
            case 'year':
                groupBy = 'year';
                dateFormat = '%Y';
                break;
            default:
                groupBy = 'month';
                dateFormat = '%Y-%m';
        }
        // Get applications by period
        const applicationsByPeriod = await prisma.$queryRaw `
      SELECT 
        DATE_TRUNC(${groupBy}, "createdAt") as period,
        COUNT(*) as applications,
        COUNT(CASE WHEN status = 'HIRED' THEN 1 END) as hired
      FROM "JobApplication" ja
      JOIN "JobPosting" jp ON ja."jobId" = jp.id
      WHERE jp."companyId" = ${parseInt(companyId)}
      GROUP BY DATE_TRUNC(${groupBy}, "createdAt")
      ORDER BY period DESC
      LIMIT 12
    `;
        // Get jobs by period
        const jobsByPeriod = await prisma.$queryRaw `
      SELECT 
        DATE_TRUNC(${groupBy}, "createdAt") as period,
        COUNT(*) as jobs,
        COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_jobs
      FROM "JobPosting"
      WHERE "companyId" = ${parseInt(companyId)}
      GROUP BY DATE_TRUNC(${groupBy}, "createdAt")
      ORDER BY period DESC
      LIMIT 12
    `;
        res.json({
            applications: applicationsByPeriod,
            jobs: jobsByPeriod
        });
    }
    catch (error) {
        console.error('Error fetching metrics by period:', error);
        res.status(500).json({ error: 'Failed to fetch metrics' });
    }
};
exports.getMetricsByPeriod = getMetricsByPeriod;
// Get application funnel data
const getApplicationFunnel = async (req, res) => {
    try {
        const { companyId } = req.params;
        const funnelData = await prisma.jobApplication.groupBy({
            by: ['stage'],
            where: {
                job: { companyId: parseInt(companyId) }
            },
            _count: true
        });
        const funnel = {
            APPLIED: 0,
            SCREENING: 0,
            INTERVIEW: 0,
            ASSESSMENT: 0,
            REFERENCE_CHECK: 0,
            OFFER: 0,
            HIRED: 0,
            REJECTED: 0
        };
        funnelData.forEach(item => {
            funnel[item.stage] = item._count;
        });
        res.json(funnel);
    }
    catch (error) {
        console.error('Error fetching application funnel:', error);
        res.status(500).json({ error: 'Failed to fetch funnel data' });
    }
};
exports.getApplicationFunnel = getApplicationFunnel;
// Get top performing jobs
const getTopPerformingJobs = async (req, res) => {
    try {
        const { companyId } = req.params;
        const { limit = 5 } = req.query;
        const topJobs = await prisma.jobPosting.findMany({
            where: { companyId: parseInt(companyId) },
            include: {
                _count: {
                    select: {
                        applications: true
                    }
                },
                applications: {
                    where: { status: 'HIRED' },
                    select: { id: true }
                }
            },
            orderBy: {
                applications: {
                    _count: 'desc'
                }
            },
            take: parseInt(limit)
        });
        const formattedJobs = topJobs.map(job => ({
            id: job.id,
            title: job.title,
            department: job.department,
            totalApplications: job._count.applications,
            hiredCount: job.applications.length,
            conversionRate: job._count.applications > 0
                ? (job.applications.length / job._count.applications * 100).toFixed(1)
                : '0.0'
        }));
        res.json(formattedJobs);
    }
    catch (error) {
        console.error('Error fetching top performing jobs:', error);
        res.status(500).json({ error: 'Failed to fetch top jobs' });
    }
};
exports.getTopPerformingJobs = getTopPerformingJobs;
// Get recruitment sources analysis
const getSourcesAnalysis = async (req, res) => {
    try {
        const { companyId } = req.params;
        // For now, we'll simulate source data since we don't have a source field
        // In a real implementation, you'd track application sources
        const totalApplications = await prisma.jobApplication.count({
            where: { job: { companyId: parseInt(companyId) } }
        });
        const sources = [
            { name: 'Company Website', applications: Math.floor(totalApplications * 0.4), percentage: 40 },
            { name: 'LinkedIn', applications: Math.floor(totalApplications * 0.3), percentage: 30 },
            { name: 'Job Boards', applications: Math.floor(totalApplications * 0.2), percentage: 20 },
            { name: 'Referrals', applications: Math.floor(totalApplications * 0.1), percentage: 10 }
        ];
        res.json(sources);
    }
    catch (error) {
        console.error('Error fetching sources analysis:', error);
        res.status(500).json({ error: 'Failed to fetch sources data' });
    }
};
exports.getSourcesAnalysis = getSourcesAnalysis;
// Get time-to-hire analysis
const getTimeToHireAnalysis = async (req, res) => {
    try {
        const { companyId } = req.params;
        const hiredApplications = await prisma.jobApplication.findMany({
            where: {
                job: { companyId: parseInt(companyId) },
                status: 'HIRED',
                reviewedAt: { not: null }
            },
            select: {
                createdAt: true,
                reviewedAt: true,
                job: {
                    select: { title: true, department: true }
                }
            }
        });
        const timeToHireData = hiredApplications.map(app => {
            const days = Math.ceil((app.reviewedAt.getTime() - app.createdAt.getTime()) / (1000 * 60 * 60 * 24));
            return {
                jobTitle: app.job.title,
                department: app.job.department,
                daysToHire: days
            };
        });
        const avgTimeToHire = timeToHireData.length > 0
            ? timeToHireData.reduce((acc, item) => acc + item.daysToHire, 0) / timeToHireData.length
            : 0;
        res.json({
            averageTimeToHire: Math.round(avgTimeToHire),
            timeToHireData: timeToHireData.slice(0, 10) // Last 10 hires
        });
    }
    catch (error) {
        console.error('Error fetching time-to-hire analysis:', error);
        res.status(500).json({ error: 'Failed to fetch time-to-hire data' });
    }
};
exports.getTimeToHireAnalysis = getTimeToHireAnalysis;
//# sourceMappingURL=analyticsController.js.map