"use strict";
// controllers/subscriptionPackageController.js
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.subscriptionPackageController = void 0;
const prisma_js_1 = require("../lib/prisma.js");
const http_errors_1 = __importDefault(require("http-errors"));
exports.subscriptionPackageController = {
    getAllPackages: async (req, res, next) => {
        try {
            const packages = await prisma_js_1.prisma.subscriptionPackage.findMany();
            res.json(packages);
        }
        catch (error) {
            console.error("Error fetching packages:", error);
            next((0, http_errors_1.default)(500, "Internal Server Error"));
        }
    },
    // Only admins can create packages
    createPackage: async (req, res, next) => {
        const { name, description, price, durationInDays } = req.body;
        try {
            const newPackage = await prisma_js_1.prisma.subscriptionPackage.create({
                data: {
                    name,
                    description,
                    price,
                    durationInDays,
                },
            });
            res.status(201).json(newPackage);
        }
        catch (error) {
            if (error.code === "P2002" && error.meta?.target?.includes("name")) {
                next((0, http_errors_1.default)(400, "Package name must be unique"));
                return;
            }
            console.error("Error creating package:", error);
            next((0, http_errors_1.default)(500, "Internal Server Error"));
        }
    },
};
//# sourceMappingURL=subscriptionPackageController.js.map