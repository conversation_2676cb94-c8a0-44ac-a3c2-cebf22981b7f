"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteContact = exports.updateContact = exports.createContact = exports.getCompanyContacts = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
const getCompanyContacts = async (req, res, next) => {
    try {
        const { id } = req.params;
        const contacts = await prisma_js_1.prisma.contact.findMany({
            where: { companyId: Number(id) },
        });
        res.json(contacts);
    }
    catch (error) {
        next(createError(500, "Failed to fetch contacts"));
    }
};
exports.getCompanyContacts = getCompanyContacts;
const createContact = async (req, res, next) => {
    try {
        const { id } = req.params;
        const payload = req.body;
        const contact = await prisma_js_1.prisma.contact.create({
            data: {
                ...payload,
                companyId: Number(id),
                // Remove createdBy as it's not in the schema
            },
        });
        res.status(201).json(contact);
    }
    catch (error) {
        next(createError(500, "Failed to create contact"));
    }
};
exports.createContact = createContact;
const updateContact = async (req, res, next) => {
    try {
        const { contactId } = req.params;
        const payload = req.body;
        const contact = await prisma_js_1.prisma.contact.update({
            where: { id: Number(contactId) },
            data: {
                ...payload,
                // updatedAt is handled automatically by Prisma
            },
        });
        res.json(contact);
    }
    catch (error) {
        // Type error as any to access code property
        if (error.code === "P2025") {
            return next(createError(404, "Contact not found"));
        }
        next(createError(500, "Failed to update contact"));
    }
};
exports.updateContact = updateContact;
const deleteContact = async (req, res, next) => {
    try {
        const { contactId } = req.params;
        await prisma_js_1.prisma.contact.delete({
            where: { id: Number(contactId) },
        });
        res.status(204).send();
    }
    catch (error) {
        // Type error as any to access code property
        if (error.code === "P2025") {
            return next(createError(404, "Contact not found"));
        }
        next(createError(500, "Failed to delete contact"));
    }
};
exports.deleteContact = deleteContact;
//# sourceMappingURL=contact.controller.js.map