"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteCareerPath = exports.updateCareerPath = exports.createCareerPath = exports.getCareerPathByEmployeeId = exports.getAllCareerPaths = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Using centralized prisma instance from lib/prisma.js
// Get all career paths
const getAllCareerPaths = async (req, res, next) => {
    try {
        const { companyId } = req.query;
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        const careerPaths = await prisma_js_1.prisma.careerPath.findMany({
            where: {
                employee: {
                    companyId: Number(companyId),
                },
            },
            include: {
                employee: {
                    include: {
                        user: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(careerPaths);
    }
    catch (error) {
        console.error("Error fetching career paths:", error);
        next(createHttpError(500, "Failed to fetch career paths"));
    }
};
exports.getAllCareerPaths = getAllCareerPaths;
// Get career path by employee ID
const getCareerPathByEmployeeId = async (req, res, next) => {
    try {
        const { employeeId } = req.params;
        const careerPath = await prisma_js_1.prisma.careerPath.findUnique({
            where: {
                employeeId: Number(employeeId),
            },
            include: {
                employee: {
                    include: {
                        user: true,
                    },
                },
            },
        });
        if (!careerPath) {
            return next(createHttpError(404, "Career path not found for this employee"));
        }
        res.json(careerPath);
    }
    catch (error) {
        console.error("Error fetching career path:", error);
        next(createHttpError(500, "Failed to fetch career path"));
    }
};
exports.getCareerPathByEmployeeId = getCareerPathByEmployeeId;
// Create a new career path
const createCareerPath = async (req, res, next) => {
    try {
        const { employeeId, currentPosition, targetPosition, targetDate, developmentPlan, requiredSkills, requiredTraining, mentorId, status, } = req.body;
        if (!employeeId || !currentPosition) {
            return next(createHttpError(400, "Employee ID and current position are required"));
        }
        // Check if employee exists
        const employee = await prisma_js_1.prisma.employee.findUnique({
            where: {
                id: Number(employeeId),
            },
        });
        if (!employee) {
            return next(createHttpError(404, "Employee not found"));
        }
        // Check if career path already exists for this employee
        const existingCareerPath = await prisma_js_1.prisma.careerPath.findUnique({
            where: {
                employeeId: Number(employeeId),
            },
        });
        if (existingCareerPath) {
            return next(createHttpError(409, "Career path already exists for this employee"));
        }
        const careerPath = await prisma_js_1.prisma.careerPath.create({
            data: {
                employeeId: Number(employeeId),
                currentPosition,
                targetPosition,
                developmentPlan,
                skills: requiredSkills,
                timeline: requiredTraining,
                mentorId: mentorId ? Number(mentorId) : undefined,
            },
        });
        res.status(201).json(careerPath);
    }
    catch (error) {
        console.error("Error creating career path:", error);
        next(createHttpError(500, "Failed to create career path"));
    }
};
exports.createCareerPath = createCareerPath;
// Update a career path
const updateCareerPath = async (req, res, next) => {
    try {
        const { employeeId } = req.params;
        const { currentPosition, targetPosition, targetDate, developmentPlan, requiredSkills, requiredTraining, mentorId, status, progressNotes, } = req.body;
        const careerPath = await prisma_js_1.prisma.careerPath.findUnique({
            where: {
                employeeId: Number(employeeId),
            },
        });
        if (!careerPath) {
            return next(createHttpError(404, "Career path not found for this employee"));
        }
        const updatedCareerPath = await prisma_js_1.prisma.careerPath.update({
            where: {
                employeeId: Number(employeeId),
            },
            data: {
                currentPosition: currentPosition !== undefined ? currentPosition : undefined,
                targetPosition: targetPosition !== undefined ? targetPosition : undefined,
                developmentPlan: developmentPlan !== undefined ? developmentPlan : undefined,
                skills: requiredSkills !== undefined ? requiredSkills : undefined,
                timeline: requiredTraining !== undefined ? requiredTraining : undefined,
                mentorId: mentorId !== undefined ? Number(mentorId) : undefined,
                progressNotes: progressNotes !== undefined ? progressNotes : undefined,
                // The lastUpdated field is not in the schema, so we'll use updatedAt which is handled automatically
            },
        });
        res.json(updatedCareerPath);
    }
    catch (error) {
        console.error("Error updating career path:", error);
        next(createHttpError(500, "Failed to update career path"));
    }
};
exports.updateCareerPath = updateCareerPath;
// Delete a career path
const deleteCareerPath = async (req, res, next) => {
    try {
        const { employeeId } = req.params;
        const careerPath = await prisma_js_1.prisma.careerPath.findUnique({
            where: {
                employeeId: Number(employeeId),
            },
        });
        if (!careerPath) {
            return next(createHttpError(404, "Career path not found for this employee"));
        }
        await prisma_js_1.prisma.careerPath.delete({
            where: {
                employeeId: Number(employeeId),
            },
        });
        res.json({ message: "Career path deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting career path:", error);
        next(createHttpError(500, "Failed to delete career path"));
    }
};
exports.deleteCareerPath = deleteCareerPath;
//# sourceMappingURL=career.controller.js.map