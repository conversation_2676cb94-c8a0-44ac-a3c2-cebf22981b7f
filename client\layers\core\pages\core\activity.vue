<!-- client/layers/core/pages/core/activity.vue -->

<template>
  <div class="space-y-4 px-4 md:px-6 lg:px-8 pb-20 pt-6">
    <!-- Page Header Actions -->
    <div class="flex justify-end">
      <div class="flex items-center gap-2">
        <BaseButton color="primary" variant="muted" @click="exportLogs">
          <Icon name="ph:download-duotone" class="h-4 w-4 mr-1" />
          {{ t("activity.actions.export") }}
        </BaseButton>

        <BaseButton
          color="primary"
          variant="primary"
          @click="refreshLogs"
          :loading="loading"
        >
          <Icon name="ph:arrows-clockwise-duotone" class="h-4 w-4 mr-1" />
          {{ t("activity.actions.refresh") }}
        </BaseButton>
      </div>
    </div>

    <!-- KPI Cards Grid - Banking-4 Style -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Total Activities -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-primary-500 relative">
              <BaseProgressCircle
                :max="1000"
                :model-value="metrics.totalActivities"
                :size="75"
                :thickness="1"
                variant="primary"
              />
              <Icon
                name="ph:activity-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                {{ t("activity.metrics.total_activities") }}
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.totalActivities }}
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph size="sm" weight="medium" class="text-primary-500">
                +{{ metrics.totalActivitiesTrend }}%
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- User Logins -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-success-500 relative">
              <BaseProgressCircle
                :max="100"
                :model-value="75"
                :size="75"
                :thickness="1"
                variant="none"
                class="text-success-500 *:first:text-muted-200 *:dark:first:text-muted-900"
              />
              <Icon
                name="ph:sign-in-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                {{ t("activity.metrics.user_logins") }}
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.userLogins }}
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph size="sm" weight="medium" class="text-success-500">
                +{{ metrics.userLoginsTrend }}%
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- Data Changes -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-info-500 relative">
              <BaseProgressCircle
                :max="200"
                :model-value="metrics.dataChanges"
                :size="75"
                :thickness="1"
                variant="none"
                class="text-info-500 *:first:text-muted-200 *:dark:first:text-muted-900"
              />
              <Icon
                name="ph:pencil-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                {{ t("activity.metrics.data_changes") }}
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.dataChanges }}
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph size="sm" weight="medium" class="text-info-500">
                +{{ metrics.dataChangesTrend }}%
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- System Errors -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-warning-500 relative">
              <BaseProgressCircle
                :max="50"
                :model-value="metrics.systemErrors"
                :size="75"
                :thickness="1"
                variant="none"
                class="text-warning-500 *:first:text-muted-200 *:dark:first:text-muted-900"
              />
              <Icon
                name="ph:warning-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                {{ t("activity.metrics.system_errors") }}
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.systemErrors }}
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph
                size="sm"
                weight="medium"
                :class="
                  metrics.systemErrorsTrend < 0
                    ? 'text-success-500'
                    : 'text-warning-500'
                "
              >
                {{ metrics.systemErrorsTrend > 0 ? "+" : ""
                }}{{ metrics.systemErrorsTrend }}%
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>

    <!-- Filters and Chart Row -->
    <div class="flex flex-col lg:flex-row gap-4">
      <!-- Activity Filters - 1/3 width -->
      <div style="flex: 1">
        <BaseCard class="h-full flex flex-col">
          <div class="p-4 border-b border-muted-200 dark:border-muted-700">
            <BaseHeading
              as="h3"
              size="sm"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              {{ t("activity.filters.title") }}
            </BaseHeading>
          </div>

          <div class="p-4 space-y-4 flex-1">
            <!-- Search Input -->
            <BaseField :label="t('activity.filters.search_placeholder')">
              <BaseInput
                v-model="filters.search"
                :placeholder="t('activity.filters.search_placeholder')"
                icon="solar:magnifer-line-duotone"
              />
            </BaseField>

            <!-- Activity Type Filter -->
            <BaseField :label="t('activity.filters.filter_by_type')">
              <TairoSelect
                v-model="filters.activityType"
                :placeholder="t('activity.filters.filter_by_type')"
                icon="solar:filter-line-duotone"
              >
                <TairoSelectItem value="all_types">
                  {{ t("activity.filters.all_types") }}
                </TairoSelectItem>
                <TairoSelectItem value="login">
                  {{ t("activity.filters.types.login") }}
                </TairoSelectItem>
                <TairoSelectItem value="data_change">
                  {{ t("activity.filters.types.data_change") }}
                </TairoSelectItem>
                <TairoSelectItem value="system_error">
                  {{ t("activity.filters.types.system_error") }}
                </TairoSelectItem>
              </TairoSelect>
            </BaseField>

            <!-- User Filter -->
            <BaseField :label="t('activity.filters.filter_by_user')">
              <TairoSelect
                v-model="filters.user"
                :placeholder="t('activity.filters.filter_by_user')"
                icon="solar:user-line-duotone"
              >
                <TairoSelectItem value="all_users">
                  {{ t("activity.filters.all_users") }}
                </TairoSelectItem>
                <TairoSelectItem value="user1"> John Doe </TairoSelectItem>
                <TairoSelectItem value="user2"> Jane Smith </TairoSelectItem>
                <TairoSelectItem value="user3"> Mike Johnson </TairoSelectItem>
              </TairoSelect>
            </BaseField>

            <!-- Date Filter -->
            <BaseField :label="t('activity.filters.filter_by_date')">
              <TairoSelect
                v-model="filters.date"
                :placeholder="t('activity.filters.filter_by_date')"
                icon="solar:calendar-line-duotone"
              >
                <TairoSelectItem value="all_dates">
                  {{ t("activity.filters.all_dates") }}
                </TairoSelectItem>
                <TairoSelectItem value="today">
                  {{ t("activity.filters.dates.today") }}
                </TairoSelectItem>
                <TairoSelectItem value="yesterday">
                  {{ t("activity.filters.dates.yesterday") }}
                </TairoSelectItem>
                <TairoSelectItem value="last7days">
                  {{ t("activity.filters.dates.last_7_days") }}
                </TairoSelectItem>
                <TairoSelectItem value="last30days">
                  {{ t("activity.filters.dates.last_30_days") }}
                </TairoSelectItem>
                <TairoSelectItem value="thisMonth">
                  {{ t("activity.filters.dates.this_month") }}
                </TairoSelectItem>
                <TairoSelectItem value="lastMonth">
                  {{ t("activity.filters.dates.last_month") }}
                </TairoSelectItem>
              </TairoSelect>
            </BaseField>
          </div>

          <div class="p-4 border-t border-muted-200 dark:border-muted-700">
            <div class="flex justify-between items-center">
              <BaseText size="xs" class="text-muted-400">
                {{ filteredActivities.length }}
                {{ t("activity.filters.results_found") }}
              </BaseText>
              <BaseButton variant="primary" size="sm" @click="clearFilters">
                <Icon name="solar:restart-line-duotone" class="h-4 w-4 mr-1" />
                {{ t("activity.filters.clear_all") }}
              </BaseButton>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- Activity Chart - 2/3 width -->
      <div style="flex: 2">
        <BaseCard class="h-full flex flex-col">
          <div
            class="p-4 border-b border-muted-200 dark:border-muted-700 flex justify-between items-center"
          >
            <BaseHeading
              as="h3"
              size="sm"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              {{ t("activity.chart.title") }}
            </BaseHeading>
          </div>

          <div class="p-4 flex-1">
            <div v-if="loading" class="flex justify-center py-4">
              <BaseButton variant="primary" color="primary">
                <Icon name="solar:refresh-line-duotone" class="-ms-1 h-4 w-4" />
                <span>Loading chart...</span>
              </BaseButton>
            </div>

            <div v-else>
              <LineChart :chart-data="chartData" height="320px" />
            </div>
          </div>

          <div class="p-4 border-t border-muted-200 dark:border-muted-700">
            <div class="flex justify-between items-center">
              <BaseText size="xs" class="text-muted-400">
                {{ t("activity.chart.last_updated") }}
              </BaseText>
              <BaseButton variant="primary" size="sm" @click="refreshLogs">
                <Icon name="solar:refresh-line-duotone" class="h-4 w-4 mr-1" />
                {{ t("activity.actions.refresh") }}
              </BaseButton>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>

    <!-- Activity Logs -->
    <div class="col-span-12">
      <BaseCard>
        <div class="p-4 border-b border-muted-200 dark:border-muted-700">
          <div class="flex justify-between items-center">
            <BaseHeading
              as="h3"
              size="sm"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              {{ t("activity.logs.title") }}
            </BaseHeading>

            <div class="flex items-center gap-4">
              <BaseSelect v-model="sortBy" size="sm" class="w-40">
                <BaseSelectItem value="timestamp">
                  {{ t("activity.logs.sort.newest_first") }}
                </BaseSelectItem>
                <BaseSelectItem value="timestamp_asc">
                  {{ t("activity.logs.sort.oldest_first") }}
                </BaseSelectItem>
                <BaseSelectItem value="type">
                  {{ t("activity.logs.sort.type") }}
                </BaseSelectItem>
                <BaseSelectItem value="user">
                  {{ t("activity.logs.sort.user") }}
                </BaseSelectItem>
                <BaseSelectItem value="module">
                  {{ t("activity.logs.sort.module") }}
                </BaseSelectItem>
              </BaseSelect>
            </div>
          </div>
        </div>

        <div class="w-full">
          <table
            class="w-full border-collapse bg-transparent"
            style="border: none; background: transparent"
          >
            <thead>
              <tr class="bg-transparent">
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  {{ t("activity.logs.columns.activity") }}
                </th>
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  {{ t("activity.logs.columns.user") }}
                </th>
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  {{ t("activity.logs.columns.module") }}
                </th>
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  {{ t("activity.logs.columns.time") }}
                </th>
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  {{ t("activity.logs.columns.status") }}
                </th>
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  <span class="sr-only">{{
                    t("activity.logs.columns.actions")
                  }}</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-transparent">
              <tr
                v-for="activity in filteredActivities"
                :key="activity.id"
                class="bg-transparent"
                :style="{
                  background: 'transparent',
                  borderBottom:
                    $colorMode.value === 'dark'
                      ? '1px solid rgb(75 85 99 / 0.3)'
                      : '1px solid rgb(229 231 235 / 0.7)',
                  borderTop: 'none',
                  borderLeft: 'none',
                  borderRight: 'none',
                }"
              >
                <td
                  class="p-4 bg-transparent border-none"
                  style="background: transparent; border: none"
                >
                  <div class="flex items-center">
                    <div
                      class="flex h-8 w-8 items-center justify-center rounded-lg"
                      :class="getActivityIconBg(activity.type)"
                    >
                      <Icon
                        :name="getActivityIcon(activity.type)"
                        class="h-4 w-4"
                        :class="getActivityIconColor(activity.type)"
                      />
                    </div>
                    <div class="ms-3 leading-none">
                      <h4 class="font-heading text-sm font-semibold">
                        {{ activity.title }}
                      </h4>
                      <p
                        class="text-muted-600 dark:text-muted-400 font-sans text-xs"
                      >
                        {{ activity.description }}
                      </p>
                    </div>
                  </div>
                </td>
                <td
                  class="p-4 bg-transparent border-none"
                  style="background: transparent; border: none"
                >
                  <div v-if="activity.user" class="flex items-center">
                    <BaseAvatar :src="activity.user.avatar" size="xs" />
                    <div class="ms-2 leading-none">
                      <p class="font-heading text-sm font-medium">
                        {{ activity.user.firstName }}
                        {{ activity.user.lastName }}
                      </p>
                    </div>
                  </div>
                  <span v-else class="text-muted-400">{{
                    t("activity.logs.system")
                  }}</span>
                </td>
                <td
                  class="p-4 bg-transparent border-none"
                  style="background: transparent; border: none"
                >
                  {{ activity.module || t("activity.logs.unknown") }}
                </td>
                <td
                  class="p-4 bg-transparent border-none"
                  style="background: transparent; border: none"
                >
                  <div class="leading-none">
                    <p class="font-sans text-sm">
                      {{ formatDate(activity.timestamp) }}
                    </p>
                    <p
                      class="text-muted-600 dark:text-muted-400 font-sans text-xs"
                    >
                      {{ formatTime(activity.timestamp) }}
                    </p>
                  </div>
                </td>
                <td
                  class="p-4 bg-transparent border-none"
                  style="background: transparent; border: none"
                >
                  <BaseTag
                    v-if="activity.type === 'login'"
                    rounded="full"
                    variant="none"
                    :style="{
                      backgroundColor: '#dcfce7',
                      color: '#166534',
                      border: '1px solid #86efac',
                    }"
                  >
                    {{ t("activity.logs.status.success") }}
                  </BaseTag>
                  <BaseTag
                    v-else-if="
                      activity.type === 'error' || activity.type === 'security'
                    "
                    rounded="full"
                    variant="none"
                    :style="{
                      backgroundColor: '#fee2e2',
                      color: '#dc2626',
                      border: '1px solid #fca5a5',
                    }"
                  >
                    {{ t("activity.logs.status.error") }}
                  </BaseTag>
                  <BaseTag
                    v-else
                    rounded="full"
                    variant="none"
                    :style="{
                      backgroundColor: '#dbeafe',
                      color: '#1e40af',
                      border: '1px solid #93c5fd',
                    }"
                  >
                    {{ t("activity.logs.status.info") }}
                  </BaseTag>
                </td>
                <td
                  class="p-4 bg-transparent border-none text-right"
                  style="background: transparent; border: none"
                >
                  <BaseButton
                    size="sm"
                    rounded="md"
                    @click="openActivityDetails(activity)"
                  >
                    {{ t("activity.logs.actions.view") }}
                  </BaseButton>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="pagination.totalItems > pagination.perPage" class="p-4">
          <div class="flex justify-between items-center">
            <BaseText size="xs" class="text-muted-400">
              {{ t("activity.logs.pagination.showing") }}
              {{ (pagination.page - 1) * pagination.perPage + 1 }}-{{
                Math.min(
                  pagination.page * pagination.perPage,
                  pagination.totalItems
                )
              }}
              {{ t("activity.logs.pagination.of") }} {{ pagination.totalItems }}
            </BaseText>
            <div class="flex items-center gap-2">
              <BaseButton
                size="sm"
                variant="muted"
                :disabled="pagination.page === 1"
                @click="updatePage(pagination.page - 1)"
              >
                {{ t("activity.logs.pagination.previous") }}
              </BaseButton>
              <BaseButton
                size="sm"
                variant="muted"
                :disabled="
                  pagination.page >=
                  Math.ceil(pagination.totalItems / pagination.perPage)
                "
                @click="updatePage(pagination.page + 1)"
              >
                {{ t("activity.logs.pagination.next") }}
              </BaseButton>
            </div>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- Activity Details Modal -->
    <ActivityLogDetails @resolve="resolveActivity" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";

// Import components
import ActivityLogDetails from "../../components/activity/ActivityLogDetails.vue";
import LineChart from "../../components/charts/LineChart.vue";

// Import composables
import { useActivityDialog } from "../../composables/useActivityDialog";

// Define page metadata for Toolbar component
definePageMeta({
  title: "activity.title",
});

const toaster = useNuiToasts();
const { t } = useI18n();

// Activity dialog composable
const activityDialog = useActivityDialog();

// Define activity type
interface Activity {
  id: string;
  title: string;
  description: string;
  type: string;
  timestamp: Date;
  module?: string;
  ipAddress?: string;
  user?: {
    id: string;
    firstName: string;
    lastName: string;
    avatar: string;
  };
  tags?: string[];
  changes?: any[];
  metadata?: any;
  status?: string;
}

// State
const loading = ref(false);
const activities = ref<Activity[]>([]);
const filters = ref({
  search: "",
  activityType: "all_types",
  user: "all_users",
  date: "all_dates",
});
const sortBy = ref("timestamp");
const pagination = ref({
  page: 1,
  perPage: 10,
  totalItems: 0,
});
// Metrics data
const metrics = ref({
  totalActivities: 256,
  totalActivitiesTrend: 12,
  userLogins: 78,
  userLoginsTrend: 5,
  dataChanges: 142,
  dataChangesTrend: 18,
  systemErrors: 8,
  systemErrorsTrend: -15,
});

// Removed unused activityTypes and users - now using direct TairoSelect dropdowns

// Chart data
const chartData = computed(() => {
  return {
    labels: [
      t("activity.chart.labels.7_days_ago"),
      t("activity.chart.labels.6_days_ago"),
      t("activity.chart.labels.5_days_ago"),
      t("activity.chart.labels.4_days_ago"),
      t("activity.chart.labels.3_days_ago"),
      t("activity.chart.labels.2_days_ago"),
      t("activity.chart.labels.yesterday"),
      t("activity.chart.labels.today"),
    ],
    datasets: [
      {
        label: t("activity.chart.datasets.user_logins"),
        data: [65, 72, 68, 75, 80, 85, 78, 82],
        borderColor: "#6366f1", // indigo-500 (primary)
        backgroundColor: "rgba(99, 102, 241, 0.1)",
        tension: 0.4,
        fill: true,
        pointRadius: 0, // No dots
        pointHoverRadius: 6,
        borderWidth: 2,
      },
      {
        label: t("activity.chart.datasets.data_changes"),
        data: [120, 135, 125, 140, 150, 145, 142, 155],
        borderColor: "#a855f7", // purple-500
        backgroundColor: "rgba(168, 85, 247, 0.1)",
        tension: 0.4,
        fill: true,
        pointRadius: 0, // No dots
        pointHoverRadius: 6,
        borderWidth: 2,
      },
      {
        label: t("activity.chart.datasets.system_errors"),
        data: [12, 10, 15, 8, 6, 9, 8, 5],
        borderColor: "#6366f1", // indigo-500 (matching health page)
        backgroundColor: "rgba(99, 102, 241, 0.05)",
        tension: 0.4,
        fill: true,
        pointRadius: 0, // No dots
        pointHoverRadius: 6,
        borderWidth: 2,
      },
    ],
  };
});

// Computed
const filteredActivities = computed(() => {
  let result = [...activities.value];

  // Apply search filter
  if (filters.value.search) {
    const searchTerm = filters.value.search.toLowerCase();
    result = result.filter(
      (activity) =>
        activity.title.toLowerCase().includes(searchTerm) ||
        activity.description.toLowerCase().includes(searchTerm) ||
        (activity.tags &&
          activity.tags.some((tag) => tag.toLowerCase().includes(searchTerm)))
    );
  }

  // Apply type filter
  if (
    filters.value.activityType &&
    filters.value.activityType !== "all_types"
  ) {
    result = result.filter(
      (activity) => activity.type === filters.value.activityType
    );
  }

  // Apply user filter
  if (filters.value.user && filters.value.user !== "all_users") {
    result = result.filter(
      (activity) => activity.user && activity.user.id === filters.value.user
    );
  }

  // Apply date filter
  if (filters.value.date && filters.value.date !== "all_dates") {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const last7Days = new Date(today);
    last7Days.setDate(last7Days.getDate() - 7);

    const last30Days = new Date(today);
    last30Days.setDate(last30Days.getDate() - 30);

    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);

    result = result.filter((activity) => {
      const activityDate = new Date(activity.timestamp);

      switch (filters.value.date) {
        case "today":
          return activityDate >= today;
        case "yesterday":
          return activityDate >= yesterday && activityDate < today;
        case "last7days":
          return activityDate >= last7Days;
        case "last30days":
          return activityDate >= last30Days;
        case "thisMonth":
          return activityDate >= thisMonth;
        case "lastMonth":
          return activityDate >= lastMonth && activityDate <= lastMonthEnd;
        default:
          return true;
      }
    });
  }

  // Apply sorting
  result.sort((a, b) => {
    switch (sortBy.value) {
      case "timestamp":
        return (
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
      case "timestamp_asc":
        return (
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );
      case "type":
        return a.type.localeCompare(b.type);
      case "user":
        if (!a.user && !b.user) return 0;
        if (!a.user) return 1;
        if (!b.user) return -1;
        return `${a.user.firstName} ${a.user.lastName}`.localeCompare(
          `${b.user.firstName} ${b.user.lastName}`
        );
      case "module":
        if (!a.module && !b.module) return 0;
        if (!a.module) return 1;
        if (!b.module) return -1;
        return a.module.localeCompare(b.module);
      default:
        return (
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
    }
  });

  // Update pagination total
  pagination.value.totalItems = result.length;

  // Apply pagination
  const start = (pagination.value.page - 1) * pagination.value.perPage;
  const end = start + pagination.value.perPage;

  return result.slice(start, end);
});

// Helper functions for activity icons and colors
const getActivityIcon = (type: string) => {
  const iconMap = {
    login: "solar:login-3-line-duotone",
    logout: "solar:logout-3-line-duotone",
    create: "solar:add-circle-line-duotone",
    update: "solar:pen-line-duotone",
    delete: "solar:trash-bin-trash-line-duotone",
    upload: "solar:upload-line-duotone",
    download: "solar:download-line-duotone",
    assign: "solar:user-plus-line-duotone",
    error: "solar:danger-triangle-line-duotone",
    security: "solar:shield-warning-line-duotone",
    complete: "solar:check-circle-line-duotone",
  };
  return iconMap[type] || "solar:chart-line-duotone";
};

const getActivityIconBg = (type: string) => {
  const bgMap = {
    login: "bg-green-500/10",
    logout: "bg-orange-500/10",
    create: "bg-blue-500/10",
    update: "bg-purple-500/10",
    delete: "bg-red-500/10",
    upload: "bg-cyan-500/10",
    download: "bg-indigo-500/10",
    assign: "bg-emerald-500/10",
    error: "bg-red-500/10",
    security: "bg-red-500/10",
    complete: "bg-green-500/10",
  };
  return bgMap[type] || "bg-muted-500/10";
};

const getActivityIconColor = (type: string) => {
  const colorMap = {
    login: "text-green-500",
    logout: "text-orange-500",
    create: "text-blue-500",
    update: "text-purple-500",
    delete: "text-red-500",
    upload: "text-cyan-500",
    download: "text-indigo-500",
    assign: "text-emerald-500",
    error: "text-red-500",
    security: "text-red-500",
    complete: "text-green-500",
  };
  return colorMap[type] || "text-muted-500";
};

// Date formatting functions
const formatDate = (timestamp: Date | string) => {
  if (!timestamp) return "N/A";
  try {
    const date =
      typeof timestamp === "string" ? new Date(timestamp) : timestamp;
    return date.toLocaleDateString();
  } catch (error) {
    return "N/A";
  }
};

const formatTime = (timestamp: Date | string) => {
  if (!timestamp) return "N/A";
  try {
    const date =
      typeof timestamp === "string" ? new Date(timestamp) : timestamp;
    return date.toLocaleTimeString();
  } catch (error) {
    return "N/A";
  }
};

// Methods
const updatePage = (page: number) => {
  pagination.value.page = page;
};

const clearFilters = () => {
  filters.value = {
    search: "",
    activityType: "all_types",
    user: "all_users",
    date: "all_dates",
  };
  pagination.value.page = 1;
};

const refreshLogs = () => {
  loading.value = true;

  // Simulate API call
  setTimeout(() => {
    fetchActivities();
    toaster.success("Activity logs refreshed");
  }, 1000);
};

const exportLogs = () => {
  // Simulate export
  toaster.add({
    title: "Success",
    description: "Activity logs exported",
    icon: "solar:check-circle-linear",
    progress: true,
  });
};

const openActivityDetails = (activity: Activity) => {
  console.log("Opening activity details for:", activity);
  activityDialog.openDialog(activity.id);
  console.log(
    "Dialog state:",
    activityDialog.isOpen.value,
    "Activity ID:",
    activityDialog.selectedActivityId.value
  );
};

const resolveActivity = (activity: Activity) => {
  // Simulate API call
  setTimeout(() => {
    // Update activity status
    activities.value = activities.value.map((a) => {
      if (a.id === activity.id) {
        return { ...a, status: "resolved" };
      }
      return a;
    });

    activityDialog.closeDialog();
    toaster.add({
      title: "Error",
      description: `Activity "${activity.title}" resolved`,
      icon: "solar:danger-triangle-linear",
      progress: true,
    });
  }, 500);
};

// Fetch activities
const fetchActivities = () => {
  loading.value = true;

  // Simulate API call
  setTimeout(() => {
    // Mock data
    activities.value = [
      {
        id: "activity_1",
        title: "User Login",
        description: "John Doe logged in from a new device",
        type: "login",
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        module: "Authentication",
        ipAddress: "*************",
        user: {
          id: "user_1",
          firstName: "John",
          lastName: "Doe",
          avatar: "/img/avatars/1.svg",
        },
        tags: ["Security", "User Activity"],
      },
      {
        id: "activity_2",
        title: "Document Updated",
        description: "Business Plan 2023 was updated by Sarah Johnson",
        type: "update",
        timestamp: new Date(Date.now() - 45 * 60 * 1000),
        module: "Documents",
        ipAddress: "*************",
        user: {
          id: "user_2",
          firstName: "Sarah",
          lastName: "Johnson",
          avatar: "/img/avatars/2.svg",
        },
        tags: ["Documents", "Business Plan"],
      },
      {
        id: "activity_3",
        title: "New User Added",
        description: "Michael Brown was added to the system with ADMIN role",
        type: "create",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        module: "User Management",
        ipAddress: "*************",
        user: {
          id: "user_1",
          firstName: "John",
          lastName: "Doe",
          avatar: "/img/avatars/1.svg",
        },
        tags: ["User Management", "Admin"],
      },
      {
        id: "activity_4",
        title: "API Error",
        description: "External API request failed with 500 status code",
        type: "error",
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
        module: "API Integration",
        ipAddress: "*************",
        tags: ["Error", "API", "Integration"],
      },
      {
        id: "activity_5",
        title: "Invoice Generated",
        description: "Invoice #INV-2023-0042 was generated for Client XYZ",
        type: "create",
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        module: "Accounting",
        ipAddress: "*************",
        user: {
          id: "user_3",
          firstName: "Michael",
          lastName: "Brown",
          avatar: "/img/avatars/3.svg",
        },
        tags: ["Accounting", "Invoice"],
      },
      {
        id: "activity_6",
        title: "User Logout",
        description: "Sarah Johnson logged out",
        type: "logout",
        timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000),
        module: "Authentication",
        ipAddress: "*************",
        user: {
          id: "user_2",
          firstName: "Sarah",
          lastName: "Johnson",
          avatar: "/img/avatars/2.svg",
        },
        tags: ["User Activity"],
      },
      {
        id: "activity_7",
        title: "Project Created",
        description: 'New project "Website Redesign" was created',
        type: "create",
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
        module: "Project Management",
        ipAddress: "*************",
        user: {
          id: "user_4",
          firstName: "Emily",
          lastName: "Davis",
          avatar: "/img/avatars/4.svg",
        },
        tags: ["Project", "Creation"],
      },
      {
        id: "activity_8",
        title: "Task Assigned",
        description: 'Task "Update Homepage" was assigned to Emily Davis',
        type: "assign",
        timestamp: new Date(Date.now() - 7 * 60 * 60 * 1000),
        module: "Project Management",
        ipAddress: "*************",
        user: {
          id: "user_3",
          firstName: "Michael",
          lastName: "Brown",
          avatar: "/img/avatars/3.svg",
        },
        tags: ["Task", "Assignment"],
      },
      {
        id: "activity_9",
        title: "File Uploaded",
        description: 'New file "Q2 Financial Report.xlsx" was uploaded',
        type: "upload",
        timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),
        module: "File Management",
        ipAddress: "*************",
        user: {
          id: "user_5",
          firstName: "David",
          lastName: "Wilson",
          avatar: "/img/avatars/5.svg",
        },
        tags: ["File", "Upload", "Financial"],
      },
      {
        id: "activity_10",
        title: "Settings Changed",
        description: "System notification settings were updated",
        type: "update",
        timestamp: new Date(Date.now() - 9 * 60 * 60 * 1000),
        module: "Settings",
        ipAddress: "*************",
        user: {
          id: "user_1",
          firstName: "John",
          lastName: "Doe",
          avatar: "/img/avatars/1.svg",
        },
        tags: ["Settings", "Notifications"],
      },
      {
        id: "activity_11",
        title: "Database Backup",
        description: "Automated database backup completed successfully",
        type: "complete",
        timestamp: new Date(Date.now() - 10 * 60 * 60 * 1000),
        module: "System",
        tags: ["Database", "Backup", "Automated"],
      },
      {
        id: "activity_12",
        title: "Security Alert",
        description: "Multiple failed login attempts detected for user account",
        type: "security",
        timestamp: new Date(Date.now() - 11 * 60 * 60 * 1000),
        module: "Security",
        ipAddress: "************",
        tags: ["Security", "Alert", "Login"],
      },
    ];

    // Update pagination total
    pagination.value.totalItems = activities.value.length;

    loading.value = false;
  }, 1000);
};

// Watch for sort changes
watch(sortBy, () => {
  // Reset to first page when sort changes
  pagination.value.page = 1;
});

// Watch for filter changes
watch(
  filters,
  () => {
    // Reset to first page when filters change
    pagination.value.page = 1;
  },
  { deep: true }
);

// Fetch data on mount
onMounted(() => {
  fetchActivities();
});
</script>
