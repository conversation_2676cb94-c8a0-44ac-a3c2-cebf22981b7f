<!-- client/layers/core/pages/core/activity.vue -->

<template>
  <div class="space-y-4 px-4 md:px-6 lg:px-8 pb-20 pt-6">
    <!-- Page Header Actions -->
    <div class="flex justify-end">
      <div class="flex items-center gap-2">
        <BaseButton color="primary" variant="muted" @click="exportLogs">
          <Icon name="ph:download-duotone" class="h-4 w-4 mr-1" />
          {{ t("activity.actions.export") }}
        </BaseButton>

        <BaseButton
          color="primary"
          variant="primary"
          @click="refreshLogs"
          :loading="loading"
        >
          <Icon name="ph:arrows-clockwise-duotone" class="h-4 w-4 mr-1" />
          {{ t("activity.actions.refresh") }}
        </BaseButton>
      </div>
    </div>

    <!-- KPI Cards Grid - Banking-4 Style -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Total Activities -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-primary-500 relative">
              <BaseProgressCircle
                :max="1000"
                :model-value="metrics.totalActivities"
                :size="75"
                :thickness="1"
                variant="primary"
              />
              <Icon
                name="ph:activity-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                {{ t("activity.metrics.total_activities") }}
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.totalActivities }}
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph size="sm" weight="medium" class="text-primary-500">
                +{{ metrics.totalActivitiesTrend }}%
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- User Logins -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-success-500 relative">
              <BaseProgressCircle
                :max="100"
                :model-value="75"
                :size="75"
                :thickness="1"
                variant="none"
                class="text-success-500 *:first:text-muted-200 *:dark:first:text-muted-900"
              />
              <Icon
                name="ph:sign-in-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                {{ t("activity.metrics.user_logins") }}
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.userLogins }}
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph size="sm" weight="medium" class="text-success-500">
                +{{ metrics.userLoginsTrend }}%
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- Data Changes -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-info-500 relative">
              <BaseProgressCircle
                :max="200"
                :model-value="metrics.dataChanges"
                :size="75"
                :thickness="1"
                variant="none"
                class="text-info-500 *:first:text-muted-200 *:dark:first:text-muted-900"
              />
              <Icon
                name="ph:pencil-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                {{ t("activity.metrics.data_changes") }}
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.dataChanges }}
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph size="sm" weight="medium" class="text-info-500">
                +{{ metrics.dataChangesTrend }}%
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- System Errors -->
      <div class="col-span-12 sm:col-span-6 lg:col-span-3">
        <BaseCard rounded="md" class="p-2">
          <div class="flex items-center">
            <div class="text-warning-500 relative">
              <BaseProgressCircle
                :max="50"
                :model-value="metrics.systemErrors"
                :size="75"
                :thickness="1"
                variant="none"
                class="text-warning-500 *:first:text-muted-200 *:dark:first:text-muted-900"
              />
              <Icon
                name="ph:warning-duotone"
                class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
              />
            </div>
            <div>
              <BaseParagraph
                size="xs"
                weight="medium"
                class="uppercase text-muted-600 dark:text-muted-400"
              >
                {{ t("activity.metrics.system_errors") }}
              </BaseParagraph>
              <BaseHeading
                size="xl"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ metrics.systemErrors }}
              </BaseHeading>
            </div>
            <div class="ms-auto me-2">
              <BaseParagraph
                size="sm"
                weight="medium"
                :class="
                  metrics.systemErrorsTrend < 0
                    ? 'text-success-500'
                    : 'text-warning-500'
                "
              >
                {{ metrics.systemErrorsTrend > 0 ? "+" : ""
                }}{{ metrics.systemErrorsTrend }}%
              </BaseParagraph>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>

    <!-- Filters and Chart Row -->
    <div class="flex flex-col lg:flex-row gap-4">
      <!-- Activity Filters - 1/3 width -->
      <div style="flex: 1">
        <BaseCard class="h-full flex flex-col">
          <div class="p-4 border-b border-muted-200 dark:border-muted-700">
            <BaseHeading
              as="h3"
              size="sm"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              {{ t("activity.filters.title") }}
            </BaseHeading>
          </div>

          <div class="p-4 space-y-4 flex-1">
            <!-- Search Input -->
            <BaseField :label="t('activity.filters.search_placeholder')">
              <BaseInput
                v-model="filters.search"
                :placeholder="t('activity.filters.search_placeholder')"
                icon="solar:magnifer-line-duotone"
              />
            </BaseField>

            <!-- Activity Type Filter -->
            <BaseField :label="t('activity.filters.filter_by_type')">
              <TairoSelect
                v-model="filters.activityType"
                :placeholder="t('activity.filters.filter_by_type')"
                icon="solar:filter-line-duotone"
              >
                <TairoSelectItem value="all_types">
                  {{ t("activity.filters.all_types") }}
                </TairoSelectItem>
                <TairoSelectItem value="login">
                  {{ t("activity.filters.types.login") }}
                </TairoSelectItem>
                <TairoSelectItem value="data_change">
                  {{ t("activity.filters.types.data_change") }}
                </TairoSelectItem>
                <TairoSelectItem value="system_error">
                  {{ t("activity.filters.types.system_error") }}
                </TairoSelectItem>
              </TairoSelect>
            </BaseField>

            <!-- User Filter -->
            <BaseField :label="t('activity.filters.filter_by_user')">
              <TairoSelect
                v-model="filters.user"
                :placeholder="t('activity.filters.filter_by_user')"
                icon="solar:user-line-duotone"
              >
                <TairoSelectItem value="all_users">
                  {{ t("activity.filters.all_users") }}
                </TairoSelectItem>
                <TairoSelectItem value="user1"> John Doe </TairoSelectItem>
                <TairoSelectItem value="user2"> Jane Smith </TairoSelectItem>
                <TairoSelectItem value="user3"> Mike Johnson </TairoSelectItem>
              </TairoSelect>
            </BaseField>

            <!-- Date Filter -->
            <BaseField :label="t('activity.filters.filter_by_date')">
              <TairoSelect
                v-model="filters.date"
                :placeholder="t('activity.filters.filter_by_date')"
                icon="solar:calendar-line-duotone"
              >
                <TairoSelectItem value="all_dates">
                  {{ t("activity.filters.all_dates") }}
                </TairoSelectItem>
                <TairoSelectItem value="today">
                  {{ t("activity.filters.dates.today") }}
                </TairoSelectItem>
                <TairoSelectItem value="yesterday">
                  {{ t("activity.filters.dates.yesterday") }}
                </TairoSelectItem>
                <TairoSelectItem value="last7days">
                  {{ t("activity.filters.dates.last_7_days") }}
                </TairoSelectItem>
                <TairoSelectItem value="last30days">
                  {{ t("activity.filters.dates.last_30_days") }}
                </TairoSelectItem>
                <TairoSelectItem value="thisMonth">
                  {{ t("activity.filters.dates.this_month") }}
                </TairoSelectItem>
                <TairoSelectItem value="lastMonth">
                  {{ t("activity.filters.dates.last_month") }}
                </TairoSelectItem>
              </TairoSelect>
            </BaseField>
          </div>

          <div class="p-4 border-t border-muted-200 dark:border-muted-700">
            <div class="flex justify-between items-center">
              <BaseText size="xs" class="text-muted-400">
                {{ filteredActivities.length }}
                {{ t("activity.filters.results_found") }}
              </BaseText>
              <BaseButton variant="primary" size="sm" @click="clearFilters">
                <Icon name="solar:restart-line-duotone" class="h-4 w-4 mr-1" />
                {{ t("activity.filters.clear_all") }}
              </BaseButton>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- Activity Chart - 2/3 width -->
      <div style="flex: 2">
        <BaseCard class="h-full flex flex-col">
          <div
            class="p-4 border-b border-muted-200 dark:border-muted-700 flex justify-between items-center"
          >
            <BaseHeading
              as="h3"
              size="sm"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              {{ t("activity.chart.title") }}
            </BaseHeading>
          </div>

          <div class="p-4 flex-1">
            <div v-if="loading" class="flex justify-center py-4">
              <BaseButton variant="primary" color="primary">
                <Icon name="solar:refresh-line-duotone" class="-ms-1 h-4 w-4" />
                <span>Loading chart...</span>
              </BaseButton>
            </div>

            <div v-else>
              <LineChart :chart-data="chartData" height="320px" />
            </div>
          </div>

          <div class="p-4 border-t border-muted-200 dark:border-muted-700">
            <div class="flex justify-between items-center">
              <BaseText size="xs" class="text-muted-400">
                {{ t("activity.chart.last_updated") }}
              </BaseText>
              <BaseButton variant="primary" size="sm" @click="refreshLogs">
                <Icon name="solar:refresh-line-duotone" class="h-4 w-4 mr-1" />
                {{ t("activity.actions.refresh") }}
              </BaseButton>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>

    <!-- Activity Logs -->
    <div class="col-span-12">
      <BaseCard>
        <div class="p-4 border-b border-muted-200 dark:border-muted-700">
          <div class="flex justify-between items-center">
            <BaseHeading
              as="h3"
              size="sm"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              {{ t("activity.logs.title") }}
            </BaseHeading>

            <div class="flex items-center gap-4">
              <BaseSelect v-model="sortBy" size="sm" class="w-40">
                <BaseSelectItem value="timestamp">
                  {{ t("activity.logs.sort.newest_first") }}
                </BaseSelectItem>
                <BaseSelectItem value="timestamp_asc">
                  {{ t("activity.logs.sort.oldest_first") }}
                </BaseSelectItem>
                <BaseSelectItem value="type">
                  {{ t("activity.logs.sort.type") }}
                </BaseSelectItem>
                <BaseSelectItem value="user">
                  {{ t("activity.logs.sort.user") }}
                </BaseSelectItem>
                <BaseSelectItem value="module">
                  {{ t("activity.logs.sort.module") }}
                </BaseSelectItem>
              </BaseSelect>
            </div>
          </div>
        </div>

        <div class="w-full">
          <table
            class="w-full border-collapse bg-transparent"
            style="border: none; background: transparent"
          >
            <thead>
              <tr class="bg-transparent">
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  {{ t("activity.logs.columns.activity") }}
                </th>
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  {{ t("activity.logs.columns.user") }}
                </th>
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  {{ t("activity.logs.columns.module") }}
                </th>
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  {{ t("activity.logs.columns.time") }}
                </th>
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  {{ t("activity.logs.columns.status") }}
                </th>
                <th
                  class="p-4 text-left text-xs font-semibold uppercase tracking-wider text-muted-500 bg-transparent border-none"
                >
                  <span class="sr-only">{{
                    t("activity.logs.columns.actions")
                  }}</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-transparent">
              <!-- Loading State -->
              <tr v-if="loading" class="bg-transparent">
                <td colspan="6" class="p-8 text-center">
                  <div
                    class="flex flex-col items-center justify-center space-y-4"
                  >
                    <div
                      class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"
                    ></div>
                    <p class="text-muted-500">
                      {{ t("activity.logs.loading") }}
                    </p>
                  </div>
                </td>
              </tr>

              <!-- Empty State -->
              <tr v-else-if="!hasActivities && !loading" class="bg-transparent">
                <td colspan="6" class="p-8 text-center">
                  <div
                    class="flex flex-col items-center justify-center space-y-4"
                  >
                    <div
                      class="flex h-16 w-16 items-center justify-center rounded-full bg-muted-100 dark:bg-muted-800"
                    >
                      <Icon
                        name="solar:chart-line-duotone"
                        class="h-8 w-8 text-muted-400"
                      />
                    </div>
                    <div class="space-y-2">
                      <h3 class="font-heading text-lg font-semibold">
                        {{ t("activity.logs.empty.title") }}
                      </h3>
                      <p class="text-muted-500 text-sm">
                        {{ t("activity.logs.empty.description") }}
                      </p>
                    </div>
                  </div>
                </td>
              </tr>

              <!-- Error State -->
              <tr v-else-if="error && !loading" class="bg-transparent">
                <td colspan="6" class="p-8 text-center">
                  <div
                    class="flex flex-col items-center justify-center space-y-4"
                  >
                    <div
                      class="flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20"
                    >
                      <Icon
                        name="solar:danger-triangle-line-duotone"
                        class="h-8 w-8 text-red-500"
                      />
                    </div>
                    <div class="space-y-2">
                      <h3
                        class="font-heading text-lg font-semibold text-red-600 dark:text-red-400"
                      >
                        {{ t("activity.logs.error.title") }}
                      </h3>
                      <p class="text-muted-500 text-sm">{{ error }}</p>
                      <BaseButton
                        size="sm"
                        variant="primary"
                        @click="fetchActivities"
                      >
                        {{ t("activity.logs.error.retry") }}
                      </BaseButton>
                    </div>
                  </div>
                </td>
              </tr>

              <!-- Activity Rows -->
              <tr
                v-else
                v-for="activity in filteredActivities"
                :key="activity.id"
                class="bg-transparent"
                :style="{
                  background: 'transparent',
                  borderBottom:
                    $colorMode.value === 'dark'
                      ? '1px solid rgb(75 85 99 / 0.3)'
                      : '1px solid rgb(229 231 235 / 0.7)',
                  borderTop: 'none',
                  borderLeft: 'none',
                  borderRight: 'none',
                }"
              >
                <td
                  class="p-4 bg-transparent border-none"
                  style="background: transparent; border: none"
                >
                  <div class="flex items-center">
                    <div
                      class="flex h-8 w-8 items-center justify-center rounded-lg"
                      :class="getActivityIconBg(activity.type)"
                    >
                      <Icon
                        :name="getActivityIcon(activity.type)"
                        class="h-4 w-4"
                        :class="getActivityIconColor(activity.type)"
                      />
                    </div>
                    <div class="ms-3 leading-none">
                      <h4 class="font-heading text-sm font-semibold">
                        {{ activity.title }}
                      </h4>
                      <p
                        class="text-muted-600 dark:text-muted-400 font-sans text-xs"
                      >
                        {{ activity.description }}
                      </p>
                    </div>
                  </div>
                </td>
                <td
                  class="p-4 bg-transparent border-none"
                  style="background: transparent; border: none"
                >
                  <div v-if="activity.user" class="flex items-center">
                    <BaseAvatar :src="activity.user.avatar" size="xs" />
                    <div class="ms-2 leading-none">
                      <p class="font-heading text-sm font-medium">
                        {{ activity.user.firstName }}
                        {{ activity.user.lastName }}
                      </p>
                    </div>
                  </div>
                  <span v-else class="text-muted-400">{{
                    t("activity.logs.system")
                  }}</span>
                </td>
                <td
                  class="p-4 bg-transparent border-none"
                  style="background: transparent; border: none"
                >
                  {{ activity.module || t("activity.logs.unknown") }}
                </td>
                <td
                  class="p-4 bg-transparent border-none"
                  style="background: transparent; border: none"
                >
                  <div class="leading-none">
                    <p class="font-sans text-sm">
                      {{ formatDate(activity.timestamp) }}
                    </p>
                    <p
                      class="text-muted-600 dark:text-muted-400 font-sans text-xs"
                    >
                      {{ formatTime(activity.timestamp) }}
                    </p>
                  </div>
                </td>
                <td
                  class="p-4 bg-transparent border-none"
                  style="background: transparent; border: none"
                >
                  <BaseTag
                    v-if="activity.type === 'login'"
                    rounded="full"
                    variant="none"
                    :style="{
                      backgroundColor: '#dcfce7',
                      color: '#166534',
                      border: '1px solid #86efac',
                    }"
                  >
                    {{ t("activity.logs.status.success") }}
                  </BaseTag>
                  <BaseTag
                    v-else-if="
                      activity.type === 'error' || activity.type === 'security'
                    "
                    rounded="full"
                    variant="none"
                    :style="{
                      backgroundColor: '#fee2e2',
                      color: '#dc2626',
                      border: '1px solid #fca5a5',
                    }"
                  >
                    {{ t("activity.logs.status.error") }}
                  </BaseTag>
                  <BaseTag
                    v-else
                    rounded="full"
                    variant="none"
                    :style="{
                      backgroundColor: '#dbeafe',
                      color: '#1e40af',
                      border: '1px solid #93c5fd',
                    }"
                  >
                    {{ t("activity.logs.status.info") }}
                  </BaseTag>
                </td>
                <td
                  class="p-4 bg-transparent border-none text-right"
                  style="background: transparent; border: none"
                >
                  <BaseButton
                    size="sm"
                    rounded="md"
                    @click="openActivityDetails(activity)"
                  >
                    {{ t("activity.logs.actions.view") }}
                  </BaseButton>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="pagination.totalItems > pagination.perPage" class="p-4">
          <div class="flex justify-between items-center">
            <BaseText size="xs" class="text-muted-400">
              {{ t("activity.logs.pagination.showing") }}
              {{ (pagination.page - 1) * pagination.perPage + 1 }}-{{
                Math.min(
                  pagination.page * pagination.perPage,
                  pagination.totalItems
                )
              }}
              {{ t("activity.logs.pagination.of") }} {{ pagination.totalItems }}
            </BaseText>
            <div class="flex items-center gap-2">
              <BaseButton
                size="sm"
                variant="muted"
                :disabled="pagination.page === 1"
                @click="updatePage(pagination.page - 1)"
              >
                {{ t("activity.logs.pagination.previous") }}
              </BaseButton>
              <BaseButton
                size="sm"
                variant="muted"
                :disabled="
                  pagination.page >=
                  Math.ceil(pagination.totalItems / pagination.perPage)
                "
                @click="updatePage(pagination.page + 1)"
              >
                {{ t("activity.logs.pagination.next") }}
              </BaseButton>
            </div>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- Activity Details Modal -->
    <ActivityLogDetails @resolve="resolveActivity" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { storeToRefs } from "pinia";

// Import components
import ActivityLogDetails from "../../components/activity/ActivityLogDetails.vue";
import LineChart from "../../components/charts/LineChart.vue";

// Import composables
import { useActivityDialog } from "../../composables/useActivityDialog";
import { useActivityStore } from "../../stores/useActivityStore";

// Define page metadata for Toolbar component
definePageMeta({
  title: "activity.title",
});

const toaster = useNuiToasts();
const { t } = useI18n();

// Activity dialog composable
const activityDialog = useActivityDialog();

// Activity store
const activityStore = useActivityStore();

// Use store state directly
const {
  activities,
  loading,
  error,
  pagination,
  filters,
  stats,
  hasActivities,
} = storeToRefs(activityStore);

// Local state for UI
const sortBy = ref("timestamp");
// Computed metrics from store stats
const metrics = computed(() => {
  if (!stats.value) {
    return {
      totalActivities: 0,
      totalActivitiesTrend: 0,
      userLogins: 0,
      userLoginsTrend: 0,
      dataChanges: 0,
      dataChangesTrend: 0,
      systemErrors: 0,
      systemErrorsTrend: 0,
    };
  }

  const loginLogs =
    stats.value.logsByType.find((item) => item.type === "LOGIN")?.count || 0;
  const updateLogs = stats.value.logsByType
    .filter((item) =>
      [
        "UPDATE",
        "CREATE",
        "DELETE",
        "USER_UPDATE",
        "USER_CREATE",
        "PASSWORD_CHANGE",
        "DEAL_CREATE",
        "DEAL_UPDATE",
        "PROJECT_CREATE",
        "PROJECT_UPDATE",
        "SETTINGS_UPDATE",
        "INTEGRATION_ENABLED",
        "INTEGRATION_DISABLED",
      ].includes(item.type)
    )
    .reduce((sum, item) => sum + item.count, 0);

  return {
    totalActivities: stats.value.totalLogs,
    totalActivitiesTrend: 0, // TODO: Calculate trend
    userLogins: loginLogs,
    userLoginsTrend: 0, // TODO: Calculate trend
    dataChanges: updateLogs,
    dataChangesTrend: 0, // TODO: Calculate trend
    systemErrors: stats.value.errorLogs,
    systemErrorsTrend: 0, // TODO: Calculate trend
  };
});

// Removed unused activityTypes and users - now using direct TairoSelect dropdowns

// Chart data - using real activity statistics from logsByType
const chartData = computed(() => {
  if (!stats.value?.logsByType || stats.value.logsByType.length === 0) {
    // Return empty chart if no data
    return {
      labels: [],
      datasets: [],
    };
  }

  // Create a simple bar chart showing activity types
  const labels = stats.value.logsByType.map((item) => {
    // Format the type name for display
    return item.type
      .replace(/_/g, " ")
      .replace(/\b\w/g, (l) => l.toUpperCase());
  });

  const data = stats.value.logsByType.map((item) => item.count);

  // Create color palette for different activity types
  const colors = [
    "#6366f1", // indigo-500 (primary)
    "#a855f7", // purple-500
    "#ef4444", // red-500
    "#10b981", // emerald-500
    "#f59e0b", // amber-500
    "#3b82f6", // blue-500
    "#8b5cf6", // violet-500
    "#06b6d4", // cyan-500
  ];

  return {
    labels,
    datasets: [
      {
        label: t("activity.chart.datasets.activity_count"),
        data,
        backgroundColor: data.map(
          (_, index) => colors[index % colors.length] + "20"
        ), // 20% opacity
        borderColor: data.map((_, index) => colors[index % colors.length]),
        borderWidth: 2,
        tension: 0.4,
        fill: true,
        pointRadius: 4,
        pointHoverRadius: 6,
      },
    ],
  };
});

// Computed - activities are already filtered and paginated by the store
const filteredActivities = computed(() => activities.value);

// Helper functions for activity icons and colors
const getActivityIcon = (type: string) => {
  const iconMap = {
    login: "solar:login-3-line-duotone",
    logout: "solar:logout-3-line-duotone",
    create: "solar:add-circle-line-duotone",
    update: "solar:pen-line-duotone",
    delete: "solar:trash-bin-trash-line-duotone",
    upload: "solar:upload-line-duotone",
    download: "solar:download-line-duotone",
    assign: "solar:user-plus-line-duotone",
    error: "solar:danger-triangle-line-duotone",
    security: "solar:shield-warning-line-duotone",
    complete: "solar:check-circle-line-duotone",
  };
  return iconMap[type] || "solar:chart-line-duotone";
};

const getActivityIconBg = (type: string) => {
  const bgMap = {
    login: "bg-green-500/10",
    logout: "bg-orange-500/10",
    create: "bg-blue-500/10",
    update: "bg-purple-500/10",
    delete: "bg-red-500/10",
    upload: "bg-cyan-500/10",
    download: "bg-indigo-500/10",
    assign: "bg-emerald-500/10",
    error: "bg-red-500/10",
    security: "bg-red-500/10",
    complete: "bg-green-500/10",
  };
  return bgMap[type] || "bg-muted-500/10";
};

const getActivityIconColor = (type: string) => {
  const colorMap = {
    login: "text-green-500",
    logout: "text-orange-500",
    create: "text-blue-500",
    update: "text-purple-500",
    delete: "text-red-500",
    upload: "text-cyan-500",
    download: "text-indigo-500",
    assign: "text-emerald-500",
    error: "text-red-500",
    security: "text-red-500",
    complete: "text-green-500",
  };
  return colorMap[type] || "text-muted-500";
};

// Date formatting functions
const formatDate = (timestamp: Date | string) => {
  if (!timestamp) return "N/A";
  try {
    const date =
      typeof timestamp === "string" ? new Date(timestamp) : timestamp;
    return date.toLocaleDateString();
  } catch (error) {
    return "N/A";
  }
};

const formatTime = (timestamp: Date | string) => {
  if (!timestamp) return "N/A";
  try {
    const date =
      typeof timestamp === "string" ? new Date(timestamp) : timestamp;
    return date.toLocaleTimeString();
  } catch (error) {
    return "N/A";
  }
};

// Methods
const updatePage = (page: number) => {
  activityStore.setPage(page);
  fetchActivities();
};

const clearFilters = () => {
  activityStore.clearFilters();
  fetchActivities();
};

const refreshLogs = async () => {
  try {
    await fetchActivities();
    toaster.add({
      title: t("common.success"),
      description: t("activity.logs.refreshed"),
      icon: "solar:check-circle-linear",
      progress: true,
    });
  } catch (error) {
    toaster.add({
      title: t("common.error"),
      description: t("activity.logs.refresh_failed"),
      icon: "solar:danger-triangle-linear",
      progress: true,
    });
  }
};

const exportLogs = () => {
  // TODO: Implement real export functionality
  toaster.add({
    title: t("common.success"),
    description: t("activity.logs.exported"),
    icon: "solar:check-circle-linear",
    progress: true,
  });
};

const openActivityDetails = (activity: any) => {
  console.log("Opening activity details for:", activity);
  activityDialog.openDialog(activity.id.toString());
  console.log(
    "Dialog state:",
    activityDialog.isOpen.value,
    "Activity ID:",
    activityDialog.selectedActivityId.value
  );
};

const resolveActivity = (activity: any) => {
  // TODO: Implement activity resolution if needed
  activityDialog.closeDialog();
  toaster.add({
    title: t("common.success"),
    description: t("activity.logs.resolved", { title: activity.title }),
    icon: "solar:check-circle-linear",
    progress: true,
  });
};

// Fetch activities
const fetchActivities = async () => {
  try {
    await activityStore.fetchActivities({
      sortBy: sortBy.value,
      sortOrder: "desc",
    });
  } catch (error) {
    console.error("Error fetching activities:", error);
    toaster.add({
      title: t("common.error"),
      description: t("activity.logs.fetch_failed"),
      icon: "solar:danger-triangle-linear",
      progress: true,
    });
  }
};

// Fetch activity statistics
const fetchActivityStats = async () => {
  try {
    await activityStore.fetchActivityStats("7d");
  } catch (error) {
    console.error("Error fetching activity stats:", error);
  }
};

// Watch for sort changes
watch(sortBy, () => {
  fetchActivities();
});

// Watch for filter changes
watch(
  filters,
  () => {
    activityStore.setPage(1);
    fetchActivities();
  },
  { deep: true }
);

// Fetch data on mount
onMounted(async () => {
  await fetchActivities();
  await fetchActivityStats();
});
</script>
