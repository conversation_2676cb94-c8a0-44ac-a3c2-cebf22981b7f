"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../../utils/route-helpers.js");
const formBuilderController = __importStar(require("../../controllers/recruitment/formBuilderController"));
const authorizeRoles_js_1 = require("../../middleware/authorizeRoles.js");
const router = express_1.default.Router();
// Public routes
router.get("/public/:slug", (0, route_helpers_js_1.wrapController)(formBuilderController.getFormBySlug));
router.post("/public/:id/submit", (0, route_helpers_js_1.wrapController)(formBuilderController.submitForm));
// Protected routes - Admin only
router.get("/", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.getAllForms));
router.get("/:id", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.getFormById));
router.post("/", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.createForm));
router.put("/:id", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.updateForm));
router.delete("/:id", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.deleteForm));
// Steps
router.post("/:id/steps", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.addStepToForm));
router.put("/:id/steps/:stepId", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.updateStep));
router.delete("/:id/steps/:stepId", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.deleteStep));
// Fields
router.post("/:id/fields", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.addField));
router.post("/:id/steps/:stepId/fields", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.addField));
router.put("/:id/fields/:fieldId", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.updateField));
router.delete("/:id/fields/:fieldId", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.deleteField));
// Options
router.post("/:id/fields/:fieldId/options", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.addOption));
router.put("/:id/fields/:fieldId/options/:optionId", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.updateOption));
router.delete("/:id/fields/:fieldId/options/:optionId", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.deleteOption));
// Submissions
router.get("/:id/submissions", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.getFormSubmissions));
router.get("/:id/submissions/:submissionId", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.getSubmissionById));
router.put("/:id/submissions/:submissionId/status", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.updateSubmissionStatus));
router.post("/:id/submissions/:submissionId/create-worker", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN", "ADMIN"), (0, route_helpers_js_1.wrapController)(formBuilderController.createWorkerFromSubmission));
exports.default = router;
//# sourceMappingURL=formBuilderRoutes.js.map