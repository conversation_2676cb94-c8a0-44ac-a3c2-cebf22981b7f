"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deletePhotoEvidence = exports.uploadPhotoEvidence = exports.getAllPhotoEvidence = exports.deleteQualityCheckItem = exports.updateQualityCheckItem = exports.createQualityCheckItem = exports.deleteQualityChecklist = exports.updateQualityChecklist = exports.createQualityChecklist = exports.getChecklistById = exports.getAllChecklists = exports.getQualityDashboard = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Get quality dashboard
const getQualityDashboard = async (req, res, next) => {
    try {
        const { companyId } = req.query;
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        // Get quality checklists statistics
        // Since the schema doesn't have a status field, we'll create a mock response
        const checklistStats = [
            { status: "PENDING", _count: { id: 0 } },
            { status: "PASSED", _count: { id: 0 } },
            { status: "FAILED", _count: { id: 0 } },
        ];
        // Get recent checklists
        const recentChecklists = await prisma_js_1.prisma.qualityChecklist.findMany({
            where: {
                project: {
                    companyId: Number(companyId),
                },
            },
            include: {
                project: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                // Task relation doesn't exist in the schema, so we need to remove it
                // task: {
                //   select: {
                //     id: true,
                //     name: true,
                //   },
                // },
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                items: {
                    select: {
                        id: true,
                        status: true,
                    },
                },
            },
            orderBy: [{ id: "desc" }],
            take: 5,
        });
        // Get photo evidence count
        const photoCount = await prisma_js_1.prisma.photoEvidence.count({
            where: {
                project: {
                    companyId: Number(companyId),
                },
            },
        });
        res.json({
            checklistStats,
            recentChecklists,
            photoCount,
        });
    }
    catch (error) {
        console.error("Error fetching quality dashboard:", error);
        next(error);
    }
};
exports.getQualityDashboard = getQualityDashboard;
// Get all quality checklists
const getAllChecklists = async (req, res, next) => {
    try {
        const { projectId, status } = req.query;
        const whereClause = {
            project: {
                companyId: Number(req.user?.companyId) || undefined,
            },
        };
        if (projectId) {
            whereClause.projectId = Number(projectId);
        }
        if (status) {
            whereClause.status = status;
        }
        const checklists = await prisma_js_1.prisma.qualityChecklist.findMany({
            where: whereClause,
            include: {
                project: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                // Task relation doesn't exist in the schema, so we need to remove it
                // task: {
                //   select: {
                //     id: true,
                //     name: true,
                //   },
                // },
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                checkedBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                items: {
                    orderBy: {
                        order: "asc",
                    },
                },
            },
            orderBy: [{ id: "desc" }],
        });
        res.json(checklists);
    }
    catch (error) {
        console.error("Error fetching quality checklists:", error);
        next(error);
    }
};
exports.getAllChecklists = getAllChecklists;
// Get checklist by ID
const getChecklistById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const checklist = await prisma_js_1.prisma.qualityChecklist.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                project: true,
                // Task relation doesn't exist in the schema, so we need to remove it
                // task: true,
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                checkedBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                items: {
                    orderBy: {
                        order: "asc",
                    },
                },
            },
        });
        if (!checklist) {
            return next(createHttpError(404, "Checklist not found"));
        }
        res.json(checklist);
    }
    catch (error) {
        console.error("Error fetching checklist:", error);
        next(error);
    }
};
exports.getChecklistById = getChecklistById;
// Create quality checklist
const createQualityChecklist = async (req, res, next) => {
    try {
        const { name, projectId, items } = req.body;
        // Create checklist
        const checklist = await prisma_js_1.prisma.qualityChecklist.create({
            data: {
                name,
                // description field doesn't exist in the schema
                projectId: Number(projectId),
                // Add companyId which is required according to the schema
                companyId: Number(req.user?.companyId) || 1,
                // taskId field doesn't exist in the schema
                // taskId: taskId ? Number(taskId) : undefined,
                // status field doesn't exist in the schema
                // status: "PENDING",
                createdById: Number(req.user?.id) || 1,
                items: {
                    create: items?.map((item, index) => ({
                        description: item.description,
                        status: "PENDING",
                        order: index,
                    })) || [],
                },
            },
            include: {
                items: true,
            },
        });
        res.status(201).json(checklist);
    }
    catch (error) {
        console.error("Error creating quality checklist:", error);
        next(error);
    }
};
exports.createQualityChecklist = createQualityChecklist;
// Update quality checklist
const updateQualityChecklist = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { name, status } = req.body;
        const checklist = await prisma_js_1.prisma.qualityChecklist.update({
            where: {
                id: Number(id),
            },
            data: {
                name,
                // description field doesn't exist in the schema
                // status field doesn't exist in the schema
                // status,
                ...(status === "PASSED" || status === "FAILED"
                    ? {
                        checkedById: Number(req.user?.id) || undefined,
                        // checkDate field doesn't exist in the schema
                        // checkDate: new Date(),
                    }
                    : {}),
            },
        });
        res.json(checklist);
    }
    catch (error) {
        console.error("Error updating quality checklist:", error);
        next(error);
    }
};
exports.updateQualityChecklist = updateQualityChecklist;
// Delete quality checklist
const deleteQualityChecklist = async (req, res, next) => {
    try {
        const { id } = req.params;
        await prisma_js_1.prisma.qualityChecklist.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Quality checklist deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting quality checklist:", error);
        next(error);
    }
};
exports.deleteQualityChecklist = deleteQualityChecklist;
// Create quality check item
const createQualityCheckItem = async (req, res, next) => {
    try {
        const { checklistId } = req.params;
        const { description, order } = req.body;
        // Get current max order if not provided
        let itemOrder = order;
        if (itemOrder === undefined) {
            const maxOrderItem = await prisma_js_1.prisma.qualityCheckItem.findFirst({
                where: {
                    checklistId: Number(checklistId),
                },
                orderBy: {
                    order: "desc",
                },
            });
            itemOrder = maxOrderItem ? maxOrderItem.order + 1 : 0;
        }
        const item = await prisma_js_1.prisma.qualityCheckItem.create({
            data: {
                checklistId: Number(checklistId),
                description,
                status: "PENDING",
                order: itemOrder,
            },
        });
        res.status(201).json(item);
    }
    catch (error) {
        console.error("Error creating quality check item:", error);
        next(error);
    }
};
exports.createQualityCheckItem = createQualityCheckItem;
// Update quality check item
const updateQualityCheckItem = async (req, res, next) => {
    try {
        const { itemId } = req.params;
        const { description, status, notes, order } = req.body;
        const item = await prisma_js_1.prisma.qualityCheckItem.update({
            where: {
                id: Number(itemId),
            },
            data: {
                description,
                status,
                notes,
                order,
            },
        });
        // Update checklist status if all items are completed
        const checklist = await prisma_js_1.prisma.qualityChecklist.findUnique({
            where: {
                id: item.checklistId,
            },
            include: {
                items: true,
            },
        });
        if (checklist) {
            const allItemsCompleted = checklist.items.every((item) => item.status === "PASSED" ||
                item.status === "FAILED" ||
                item.status === "N/A");
            if (allItemsCompleted) {
                // We're not using the status anymore since it doesn't exist in the schema
                const hasFailed = checklist.items.some((item) => item.status === "FAILED");
                await prisma_js_1.prisma.qualityChecklist.update({
                    where: {
                        id: checklist.id,
                    },
                    data: {
                        // Status field doesn't exist in the schema
                        // status: newStatus,
                        checkedById: Number(req.user?.id) || undefined,
                        // checkDate: new Date(),
                    },
                });
            }
        }
        res.json(item);
    }
    catch (error) {
        console.error("Error updating quality check item:", error);
        next(error);
    }
};
exports.updateQualityCheckItem = updateQualityCheckItem;
// Delete quality check item
const deleteQualityCheckItem = async (req, res, next) => {
    try {
        const { itemId } = req.params;
        await prisma_js_1.prisma.qualityCheckItem.delete({
            where: {
                id: Number(itemId),
            },
        });
        res.json({ message: "Quality check item deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting quality check item:", error);
        next(error);
    }
};
exports.deleteQualityCheckItem = deleteQualityCheckItem;
// Get all photo evidence
const getAllPhotoEvidence = async (req, res, next) => {
    try {
        const { projectId, taskId } = req.query;
        const whereClause = {
            project: {
                companyId: Number(req.user?.companyId) || undefined,
            },
        };
        if (projectId) {
            whereClause.projectId = Number(projectId);
        }
        if (taskId) {
            whereClause.taskId = Number(taskId);
        }
        const photos = await prisma_js_1.prisma.photoEvidence.findMany({
            where: whereClause,
            include: {
                project: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                task: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                takenBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
            },
            orderBy: {
                takenAt: "desc",
            },
        });
        res.json(photos);
    }
    catch (error) {
        console.error("Error fetching photo evidence:", error);
        next(error);
    }
};
exports.getAllPhotoEvidence = getAllPhotoEvidence;
// Upload photo evidence
const uploadPhotoEvidence = async (req, res, next) => {
    try {
        const { projectId, taskId, title, description, imageUrl, location, tags } = req.body;
        const photo = await prisma_js_1.prisma.photoEvidence.create({
            data: {
                projectId: Number(projectId),
                taskId: taskId ? Number(taskId) : undefined,
                title,
                description,
                imageUrl,
                takenById: Number(req.user?.id) || 1,
                location,
                tags,
            },
        });
        res.status(201).json(photo);
    }
    catch (error) {
        console.error("Error uploading photo evidence:", error);
        next(error);
    }
};
exports.uploadPhotoEvidence = uploadPhotoEvidence;
// Delete photo evidence
const deletePhotoEvidence = async (req, res, next) => {
    try {
        const { photoId } = req.params;
        await prisma_js_1.prisma.photoEvidence.delete({
            where: {
                id: Number(photoId),
            },
        });
        res.json({ message: "Photo evidence deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting photo evidence:", error);
        next(error);
    }
};
exports.deletePhotoEvidence = deletePhotoEvidence;
//# sourceMappingURL=quality.controller.js.map