"use strict";
// /server/middleware/authenticateToken.js
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const http_errors_1 = __importDefault(require("http-errors"));
const prisma_js_1 = require("../lib/prisma.js");
const JWT_SECRET = process.env.JWT_SECRET || "your_jwt_secret";
// User type is now defined in types/express.d.ts
const authenticateToken = async (req, res, next) => {
    const authHeader = req.headers["authorization"] || req.query.token;
    console.log("Auth header:", authHeader);
    const token = authHeader && authHeader.split(" ")[1];
    console.log("Extracted token:", token);
    if (!token) {
        console.log("No token found, returning 401");
        return next((0, http_errors_1.default)(401, "Unauthorized"));
    }
    try {
        console.log("Verifying token with JWT_SECRET:", JWT_SECRET.substring(0, 3) + "...");
        const decoded = jsonwebtoken_1.default.verify(token, JWT_SECRET);
        console.log("Decoded token:", decoded);
        const userId = parseInt(decoded.id, 10);
        console.log("Extracted userId:", userId);
        const dbUser = await prisma_js_1.prisma.user.findUnique({
            where: { id: userId },
            include: {
                userRoles: true,
                companies: true,
            },
        });
        if (!dbUser) {
            return next((0, http_errors_1.default)(404, "User not found"));
        }
        // Extract roles from userRoles relation
        const roles = dbUser.userRoles.map((userRole) => userRole.role);
        console.log("User roles from DB:", roles);
        // Check if the user is a SUPERADMIN
        // For testing purposes, let's make user with ID 4 a SUPERADMIN
        const isSuperAdmin = dbUser.id === 4 || roles.includes("SUPERADMIN");
        console.log("Is user a SUPERADMIN?", isSuperAdmin, "User ID:", dbUser.id);
        // Add SUPERADMIN role if needed
        if (isSuperAdmin && !roles.includes("SUPERADMIN")) {
            roles.push("SUPERADMIN");
            console.log("Added SUPERADMIN role. Updated roles:", roles);
        }
        // Get the first company ID if available
        const companyId = dbUser.companies.length > 0 ? dbUser.companies[0].companyId : 0;
        req.user = {
            id: dbUser.id,
            roles: roles,
            email: dbUser.email,
            companyId: companyId,
        };
        console.log("Set req.user:", req.user);
        next();
    }
    catch (error) {
        console.error("Error in authenticateToken:", error);
        next((0, http_errors_1.default)(403, "Forbidden"));
    }
};
exports.authenticateToken = authenticateToken;
//# sourceMappingURL=authenticateToken.js.map