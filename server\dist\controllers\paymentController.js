"use strict";
// server/controllers/paymentController.js
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSubscription = exports.getUserCards = exports.confirmPayment = exports.createPaymentIntent = void 0;
const stripe_1 = __importDefault(require("stripe"));
const dotenv_1 = __importDefault(require("dotenv"));
const prisma_js_1 = require("../lib/prisma.js");
// Initialize Prisma client
// Using centralized prisma instance from lib/prisma.js
// Load environment variables
dotenv_1.default.config();
// Initialize Stripe with the secret key
const stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY, {
    apiVersion: "2023-10-16", // Cast to any to avoid type errors with older versions
});
/**
 * Create a payment intent
 * @route POST /api/v1/payment/create-intent
 * @access Public
 */
const createPaymentIntent = async (req, res) => {
    try {
        const { amount, currency = "eur" } = req.body;
        if (!amount) {
            return res.status(400).json({ error: "Amount is required" });
        }
        // Create a PaymentIntent
        const paymentIntent = await stripe.paymentIntents.create({
            amount: Math.round(amount * 100), // Convert to cents
            currency,
            automatic_payment_methods: {
                enabled: true,
            },
        });
        console.log("Created payment intent with ID:", paymentIntent.id);
        console.log("Client secret:", paymentIntent.client_secret);
        return res.status(200).json({
            clientSecret: paymentIntent.client_secret,
        });
    }
    catch (error) {
        console.error("Error creating payment intent:", error);
        return res.status(500).json({ error: error.message });
    }
};
exports.createPaymentIntent = createPaymentIntent;
/**
 * Confirm a payment
 * @route POST /api/v1/payment/confirm
 * @access Public
 */
const confirmPayment = async (req, res) => {
    try {
        const { paymentMethodId, plan, billingCycle, userId } = req.body;
        if (!paymentMethodId) {
            return res.status(400).json({ error: "Payment method ID is required" });
        }
        console.log("Confirming payment with method ID:", paymentMethodId);
        console.log("Plan:", plan);
        console.log("Billing cycle:", billingCycle);
        console.log("User ID:", userId);
        // Create a Stripe customer for the user
        let stripeCustomerId;
        try {
            // Check if user already has a Stripe customer ID
            const user = await prisma_js_1.prisma.user.findUnique({
                where: { id: userId },
                select: { stripeCustomerId: true },
            });
            if (user?.stripeCustomerId) {
                stripeCustomerId = user.stripeCustomerId;
                console.log("Using existing Stripe customer ID:", stripeCustomerId);
            }
            else {
                // Get user details for creating a customer
                const userDetails = await prisma_js_1.prisma.user.findUnique({
                    where: { id: userId },
                    select: { email: true, firstName: true, lastName: true },
                });
                if (!userDetails) {
                    return res.status(404).json({ error: "User not found" });
                }
                // Create a new customer in Stripe
                const customer = await stripe.customers.create({
                    email: userDetails.email,
                    name: `${userDetails.firstName} ${userDetails.lastName}`,
                    payment_method: paymentMethodId,
                    invoice_settings: {
                        default_payment_method: paymentMethodId,
                    },
                });
                stripeCustomerId = customer.id;
                // Update user with Stripe customer ID
                await prisma_js_1.prisma.user.update({
                    where: { id: userId },
                    data: { stripeCustomerId },
                });
                console.log("Created new Stripe customer ID:", stripeCustomerId);
            }
            // Get the subscription plan details
            console.log("Looking for subscription plan with type:", plan);
            // Debug: List all subscription plans in the database
            const allPlans = await prisma_js_1.prisma.subscriptionPlan.findMany();
            console.log("All subscription plans in database count:", allPlans.length);
            let subscriptionPlan = await prisma_js_1.prisma.subscriptionPlan.findFirst({
                where: { type: plan },
            });
            console.log("Found subscription plan:", subscriptionPlan ? subscriptionPlan.id : "None");
            if (!subscriptionPlan) {
                // If no plan found, create a default one for testing
                console.log("No subscription plan found, creating a default one for testing");
                try {
                    const defaultPlan = await prisma_js_1.prisma.subscriptionPlan.create({
                        data: {
                            name: plan === "STARTER" ? "Starter Plan" : "Premium Plan",
                            description: `Default ${plan.toLowerCase()} plan`,
                            type: plan,
                            monthlyPrice: plan === "STARTER" ? 29.99 : 99.99,
                            yearlyPrice: plan === "STARTER" ? 299.99 : 999.99,
                            maxUsers: plan === "STARTER" ? 5 : 20,
                            additionalUserFee: plan === "STARTER" ? 9.99 : 4.99,
                            features: [
                                plan === "STARTER" ? "Core Module" : "All Modules",
                                plan === "STARTER" ? "Budget Module" : "Priority Support",
                                `${plan === "STARTER" ? "5" : "20"} Users`,
                                plan === "STARTER" ? "Email Support" : "Custom Integrations",
                            ],
                            modules: plan === "STARTER"
                                ? ["core", "budget"]
                                : [
                                    "core",
                                    "budget",
                                    "hr",
                                    "accounting",
                                    "product",
                                    "communication",
                                ],
                        },
                    });
                    console.log("Created default plan:", defaultPlan.id);
                    // Use the created plan
                    subscriptionPlan = defaultPlan;
                }
                catch (error) {
                    console.error("Error creating default plan:", error);
                    return res.status(404).json({
                        error: "Subscription plan not found and could not create default",
                    });
                }
            }
            // Calculate the price based on billing cycle
            const price = billingCycle === "MONTHLY"
                ? subscriptionPlan.monthlyPrice
                : subscriptionPlan.yearlyPrice;
            // Calculate the end date based on billing cycle
            const endDate = new Date();
            if (billingCycle === "MONTHLY") {
                endDate.setMonth(endDate.getMonth() + 1);
            }
            else {
                endDate.setFullYear(endDate.getFullYear() + 1);
            }
            // Create a product and price in Stripe (or use existing ones)
            const productName = `${plan} Plan - ${billingCycle}`;
            const product = await stripe.products.create({
                name: productName,
                description: `${plan} subscription plan with ${billingCycle.toLowerCase()} billing`,
            });
            const stripePrice = await stripe.prices.create({
                product: product.id,
                unit_amount: Math.round(price * 100), // Convert to cents
                currency: "eur",
                recurring: {
                    interval: billingCycle === "MONTHLY" ? "month" : "year",
                },
            });
            // Create a subscription in Stripe
            const stripeSubscription = await stripe.subscriptions.create({
                customer: stripeCustomerId,
                items: [{ price: stripePrice.id }],
                payment_behavior: "default_incomplete",
                payment_settings: {
                    payment_method_types: ["card"],
                    save_default_payment_method: "on_subscription",
                },
                expand: ["latest_invoice.payment_intent"],
            });
            // Create a subscription record in the database
            const subscription = await prisma_js_1.prisma.subscription.create({
                data: {
                    userId,
                    planId: subscriptionPlan.id,
                    status: "ACTIVE",
                    billingCycle: billingCycle,
                    startDate: new Date(),
                    endDate,
                    nextBillingDate: endDate,
                    stripeCustomerId,
                    stripeSubscriptionId: stripeSubscription.id,
                },
            });
            // Create a payment record
            await prisma_js_1.prisma.payment.create({
                data: {
                    subscriptionId: subscription.id,
                    amount: price,
                    currency: "EUR",
                    status: "succeeded",
                    stripePaymentId: paymentMethodId,
                    paymentMethod: "card",
                },
            });
            return res.status(200).json({
                success: true,
                subscriptionId: subscription.id,
                stripeSubscriptionId: stripeSubscription.id,
                plan,
                billingCycle,
                status: "active",
                currentPeriodEnd: endDate.toISOString(),
            });
        }
        catch (dbError) {
            console.error("Database error:", dbError);
            return res.status(500).json({ error: dbError.message });
        }
    }
    catch (error) {
        console.error("Error confirming payment:", error);
        return res.status(500).json({ error: error.message });
    }
};
exports.confirmPayment = confirmPayment;
/**
 * Get user cards
 * @route GET /api/v1/payments/cards
 * @access Private
 */
const getUserCards = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: "User not authenticated" });
        }
        // Get user's Stripe customer ID
        const user = await prisma_js_1.prisma.user.findUnique({
            where: { id: userId },
            select: { stripeCustomerId: true },
        });
        if (!user?.stripeCustomerId) {
            return res.status(200).json([]); // Return empty array if no Stripe customer
        }
        // Get payment methods from Stripe
        const paymentMethods = await stripe.paymentMethods.list({
            customer: user.stripeCustomerId,
            type: "card",
        });
        // Format the response
        const cards = paymentMethods.data.map((pm) => ({
            id: pm.id,
            brand: pm.card?.brand,
            last4: pm.card?.last4,
            expMonth: pm.card?.exp_month,
            expYear: pm.card?.exp_year,
            fingerprint: pm.card?.fingerprint,
        }));
        return res.status(200).json(cards);
    }
    catch (error) {
        console.error("Error fetching user cards:", error);
        return res.status(500).json({ error: error.message });
    }
};
exports.getUserCards = getUserCards;
/**
 * Create a subscription
 * @route POST /api/v1/payment/create-subscription
 * @access Private
 */
const createSubscription = async (req, res) => {
    try {
        const { customerId, priceId } = req.body;
        if (!customerId || !priceId) {
            return res
                .status(400)
                .json({ error: "Customer ID and Price ID are required" });
        }
        // Create a subscription
        const subscription = await stripe.subscriptions.create({
            customer: customerId,
            items: [{ price: priceId }],
            payment_behavior: "default_incomplete",
            expand: ["latest_invoice.payment_intent"],
        });
        return res.status(200).json({
            subscriptionId: subscription.id,
            clientSecret: subscription.latest_invoice?.payment_intent
                ?.client_secret,
        });
    }
    catch (error) {
        console.error("Error creating subscription:", error);
        return res.status(500).json({ error: error.message });
    }
};
exports.createSubscription = createSubscription;
//# sourceMappingURL=paymentController.js.map