{"version": 3, "file": "documents.controller.js", "sourceRoot": "", "sources": ["../../../controllers/hr/documents.controller.ts"], "names": [], "mappings": ";;;;;;AACA,mDAA6C;AAC7C,oDAA4B;AAC5B,gDAAwB;AACxB,4CAAoB;AAEpB,8DAA8D;AAC9D,SAAS,eAAe,CAAC,UAAkB,EAAE,OAAe;IAC1D,MAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IACtC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;IAC9B,OAAO,KAAK,CAAC;AACf,CAAC;AAcD,uDAAuD;AAEvD,oCAAoC;AACpC,MAAM,OAAO,GAAG,gBAAM,CAAC,WAAW,CAAC;IACjC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;QAC7B,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,yBAAyB,CAAC,CAAC;QAElE,uCAAuC;QACvC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,YAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACtB,CAAC;IACD,QAAQ,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;QAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QACxE,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5C,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,YAAY,GAAG,GAAG,CAAC,CAAC;IACtD,CAAC;CACF,CAAC,CAAC;AAEU,QAAA,MAAM,GAAG,IAAA,gBAAM,EAAC;IAC3B,OAAO;IACP,MAAM,EAAE,EAAE,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,EAAE,aAAa;IACrD,UAAU,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;QAC5B,+BAA+B;QAC/B,MAAM,YAAY,GAAG;YACnB,iBAAiB;YACjB,oBAAoB;YACpB,yEAAyE;YACzE,0BAA0B;YAC1B,mEAAmE;YACnE,+BAA+B;YAC/B,2EAA2E;YAC3E,YAAY;YACZ,YAAY;YACZ,WAAW;SACZ,CAAC;QAEF,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,EAAE,CACA,IAAI,KAAK,CACP,0FAA0F,CAC3F,CACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC,CAAC;AAEH,kCAAkC;AAC3B,MAAM,eAAe,GAAG,KAAK,EAClC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;QAEhC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,kBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACjD,KAAK,EAAE;gBACL,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;aAC7B;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;gBACd,iBAAiB,EAAE;oBACjB,OAAO,EAAE;wBACP,QAAQ,EAAE;4BACR,OAAO,EAAE;gCACP,IAAI,EAAE,IAAI;6BACX;yBACF;qBACF;iBACF;aACF;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACtB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAC;AAtCW,QAAA,eAAe,mBAsC1B;AAEF,gCAAgC;AACzB,MAAM,eAAe,GAAG,KAAK,EAClC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAE1B,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,UAAU,CAAC,UAAU,CAAC;YAClD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;gBACd,iBAAiB,EAAE;oBACjB,OAAO,EAAE;wBACP,QAAQ,EAAE;4BACR,OAAO,EAAE;gCACP,IAAI,EAAE,IAAI;6BACX;yBACF;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAC;AAnCW,QAAA,eAAe,mBAmC1B;AAEF,wBAAwB;AACjB,MAAM,cAAc,GAAG,KAAK,EACjC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,EACJ,KAAK,EACL,WAAW,EACX,QAAQ,EACR,cAAc,EACd,UAAU,EACV,SAAS,GACV,GAAG,GAAG,CAAC,IAAI,CAAC;QAEb,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;YACtC,OAAO,IAAI,CACT,eAAe,CAAC,GAAG,EAAE,8CAA8C,CAAC,CACrE,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,sBAAsB,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC1D,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClE,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QAE/B,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAC9C,IAAI,EAAE;gBACJ,KAAK;gBACL,WAAW;gBACX,QAAQ,EAAE,QAAe,EAAE,gCAAgC;gBAC3D,OAAO;gBACP,QAAQ;gBACR,QAAQ,EAAE,QAAQ;gBAClB,cAAc,EAAE,cAAc,KAAK,MAAM;gBACzC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI;gBACpD,UAAU,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC;gBAC7B,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;aAC7B;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAC;AAjDW,QAAA,cAAc,kBAiDzB;AAEF,oBAAoB;AACb,MAAM,cAAc,GAAG,KAAK,EACjC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,GAChE,GAAG,CAAC,IAAI,CAAC;QAEX,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,UAAU,CAAC,UAAU,CAAC;YAClD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,kBAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YACrD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;YACD,IAAI,EAAE;gBACJ,KAAK,EAAE,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;gBAC9C,WAAW,EAAE,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;gBAChE,QAAQ,EAAE,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAE,QAAgB,CAAC,CAAC,CAAC,SAAS;gBAChE,cAAc,EACZ,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,cAAc,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS;gBACtE,UAAU,EAAE,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;aACxE;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC5B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAC;AAvCW,QAAA,cAAc,kBAuCzB;AAEF,oBAAoB;AACb,MAAM,cAAc,GAAG,KAAK,EACjC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAE1B,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,UAAU,CAAC,UAAU,CAAC;YAClD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,sCAAsC;QACtC,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;QAED,4CAA4C;QAC5C,MAAM,kBAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;YACvC,KAAK,EAAE;gBACL,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC;aACvB;SACF,CAAC,CAAC;QAEH,6BAA6B;QAC7B,MAAM,kBAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAC7B,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC,CAAC;IACzD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAC;AA3CW,QAAA,cAAc,kBA2CzB;AAEF,mCAAmC;AAC5B,MAAM,wBAAwB,GAAG,KAAK,EAC3C,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEhE,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,EAAE,CAAC;YAC/B,OAAO,IAAI,CACT,eAAe,CAAC,GAAG,EAAE,0CAA0C,CAAC,CACjE,CAAC;QACJ,CAAC;QAED,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,UAAU,CAAC;aACvB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,UAAU,CAAC,UAAU,CAAC;YAClD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,UAAU,CAAC;aACvB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,qCAAqC;QACrC,MAAM,kBAAkB,GAAG,MAAM,kBAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;YACjE,KAAK,EAAE;gBACL,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;gBAC9B,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;aAC/B;SACF,CAAC,CAAC;QAEH,IAAI,kBAAkB,EAAE,CAAC;YACvB,OAAO,IAAI,CACT,eAAe,CAAC,GAAG,EAAE,+CAA+C,CAAC,CACtE,CAAC;QACJ,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,kBAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;YAC5D,IAAI,EAAE;gBACJ,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;gBAC9B,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;gBAC9B,WAAW,EAAE,WAAW,IAAI,MAAM;gBAClC,KAAK;aACN;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACzC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC9D,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAC;AAhEW,QAAA,wBAAwB,4BAgEnC;AAEF,oCAAoC;AAC7B,MAAM,oBAAoB,GAAG,KAAK,EACvC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAElC,MAAM,iBAAiB,GAAG,MAAM,kBAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;YAC/D,KAAK,EAAE;gBACL,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;aAC/B;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,QAAQ,EAAE,IAAI;qBACf;iBACF;aACF;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC9B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAC;AA7BW,QAAA,oBAAoB,wBA6B/B;AAEF,yCAAyC;AAClC,MAAM,sBAAsB,GAAG,KAAK,EACzC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAExD,MAAM,gBAAgB,GAAG,MAAM,kBAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;YAChE,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,OAAO,IAAI,CACT,eAAe,CAAC,GAAG,EAAE,wCAAwC,CAAC,CAC/D,CAAC;QACJ,CAAC;QAED,MAAM,uBAAuB,GAAG,MAAM,kBAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACnE,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;YACD,IAAI,EAAE;gBACJ,WAAW,EAAE,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;gBAChE,cAAc,EACZ,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS;gBAC3D,cAAc,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;gBACvD,KAAK,EAAE,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;aAC/C;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACpC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;QACrE,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAC;AAvCW,QAAA,sBAAsB,0BAuCjC;AAEF,qCAAqC;AAC9B,MAAM,sBAAsB,GAAG,KAAK,EACzC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAE1B,MAAM,gBAAgB,GAAG,MAAM,kBAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;YAChE,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,OAAO,IAAI,CACT,eAAe,CAAC,GAAG,EAAE,wCAAwC,CAAC,CAC/D,CAAC;QACJ,CAAC;QAED,MAAM,kBAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACnC,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,6CAA6C,EAAE,CAAC,CAAC;IACvE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAC;AA/BW,QAAA,sBAAsB,0BA+BjC"}