"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedUserData = seedUserData;
const client_1 = require("@prisma/client");
const dotenv_1 = require("dotenv");
const path_1 = require("path");
const bcrypt_1 = __importDefault(require("bcrypt"));
// Load environment variables from .env.dev
(0, dotenv_1.config)({ path: (0, path_1.resolve)(__dirname, "../../.env.dev") });
const prisma = new client_1.PrismaClient();
async function seedUserData() {
    console.log("🌱 Seeding user data...");
    try {
        // Check if users already exist
        const existingUsers = await prisma.user.count();
        if (existingUsers >= 10) {
            console.log(`✅ Already have ${existingUsers} users. Skipping user seeding.`);
            return;
        }
        console.log("👥 Creating 10 Estonian users...");
        // Estonian user data
        const estonianUsers = [
            {
                email: "<EMAIL>",
                firstName: "Mari",
                lastName: "Kask",
                role: client_1.Role.ADMIN,
                bio: "Ettevõtte juht ja strateegiline planeerija",
            },
            {
                email: "<EMAIL>",
                firstName: "Jaan",
                lastName: "Tamm",
                role: client_1.Role.PROJECTLEADER,
                bio: "Projektijuht ja meeskonnajuht",
            },
            {
                email: "<EMAIL>",
                firstName: "Liisa",
                lastName: "Mets",
                role: client_1.Role.WORKER,
                bio: "Arendaja ja tehnilise toe spetsialist",
            },
            {
                email: "<EMAIL>",
                firstName: "Peeter",
                lastName: "Org",
                role: client_1.Role.WORKER,
                bio: "Disainer ja kasutajakogemuse spetsialist",
            },
            {
                email: "<EMAIL>",
                firstName: "Karin",
                lastName: "Kuusk",
                role: client_1.Role.SALESMAN,
                bio: "Müügijuht ja kliendisuhete haldur",
            },
            {
                email: "<EMAIL>",
                firstName: "Martin",
                lastName: "Puu",
                role: client_1.Role.WORKER,
                bio: "Analüütik ja andmeteadlane",
            },
            {
                email: "<EMAIL>",
                firstName: "Anna",
                lastName: "Kivi",
                role: client_1.Role.WORKER,
                bio: "Turunduse spetsialist ja sisu looja",
            },
            {
                email: "<EMAIL>",
                firstName: "Toomas",
                lastName: "Lepp",
                role: client_1.Role.WORKER,
                bio: "Arendaja ja süsteemiadministraator",
            },
            {
                email: "<EMAIL>",
                firstName: "Kadri",
                lastName: "Saar",
                role: client_1.Role.PROJECTLEADER,
                bio: "Personalijuht ja organisatsiooni arendaja",
            },
            {
                email: "<EMAIL>",
                firstName: "Rein",
                lastName: "Tamme",
                role: client_1.Role.WORKER,
                bio: "Kvaliteedijuht ja testimise spetsialist",
            },
        ];
        // Default password for all test users
        const defaultPassword = "password123";
        const hashedPassword = await bcrypt_1.default.hash(defaultPassword, 10);
        // Get or create a default company
        let company = await prisma.company.findFirst();
        if (!company) {
            company = await prisma.company.create({
                data: {
                    name: "CoManager OÜ",
                    email: "<EMAIL>",
                    phone: "+************",
                    address: "Tallinn, Estonia",
                    website: "https://comanager.ee",
                    description: "Ärijuhtimise platvorm väikestele ja keskmise suurusega ettevõtetele",
                },
            });
            console.log(`🏢 Created company: ${company.name}`);
        }
        // Create users
        for (let i = 0; i < estonianUsers.length; i++) {
            const userData = estonianUsers[i];
            // Check if user already exists
            const existingUser = await prisma.user.findUnique({
                where: { email: userData.email },
            });
            if (existingUser) {
                console.log(`👤 User ${userData.firstName} ${userData.lastName} already exists, skipping...`);
                continue;
            }
            // Generate unique barcode and qrCode
            const barcode = `USER${Date.now()}${i}`;
            const qrCode = `QR${Date.now()}${i}`;
            const user = await prisma.user.create({
                data: {
                    email: userData.email,
                    firstName: userData.firstName,
                    lastName: userData.lastName,
                    password: hashedPassword,
                    barcode,
                    qrCode,
                    notes: userData.bio,
                    emailConfirmed: true,
                    avatar: `/img/avatars/${(i % 10) + 1}.svg`, // Cycle through available avatars
                },
            });
            // Create UserRole entry
            await prisma.userRole.create({
                data: {
                    userId: user.id,
                    role: userData.role,
                },
            });
            // Create UserCompany entry
            await prisma.userCompany.create({
                data: {
                    userId: user.id,
                    companyId: company.id,
                    role: userData.role,
                    status: "ACTIVE",
                },
            });
            console.log(`👤 Created user: ${user.firstName} ${user.lastName} (${user.email}) - ${userData.role}`);
        }
        console.log("✅ User data seeding completed successfully!");
        console.log(`📊 Total users in database: ${await prisma.user.count()}`);
        console.log(`🏢 Company: ${company.name}`);
        console.log(`🔑 Default password for all users: ${defaultPassword}`);
    }
    catch (error) {
        console.error("❌ Error seeding user data:", error);
        throw error;
    }
}
// Run if called directly
if (require.main === module) {
    seedUserData()
        .catch((e) => {
        console.error(e);
        process.exit(1);
    })
        .finally(async () => {
        await prisma.$disconnect();
    });
}
//# sourceMappingURL=user-data.js.map