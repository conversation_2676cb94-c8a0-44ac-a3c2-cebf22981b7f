"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCalendarActivities = exports.completeActivity = exports.deleteActivity = exports.updateActivity = exports.createActivity = exports.getActivityById = exports.getAllActivities = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Get all activities
const getAllActivities = async (req, res) => {
    try {
        const { type, status, assignedTo, search, startDate, endDate, sortBy = "startDate", sortOrder = "desc", page = 1, limit = 10, } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        // Build filter conditions
        const where = {};
        if (type) {
            where.type = type;
        }
        if (status) {
            where.status = status;
        }
        if (assignedTo) {
            where.assignedToId = Number(assignedTo);
        }
        if (startDate) {
            where.startDate = {
                ...where.startDate,
                gte: new Date(startDate),
            };
        }
        if (endDate) {
            where.startDate = {
                ...where.startDate,
                lte: new Date(endDate),
            };
        }
        if (search) {
            where.OR = [
                { subject: { contains: search, mode: "insensitive" } },
                { description: { contains: search, mode: "insensitive" } },
            ];
        }
        // Get total count for pagination
        const totalCount = await prisma_js_1.prisma.activity.count({ where });
        // Get activities with pagination, sorting and filtering
        const activities = await prisma_js_1.prisma.activity.findMany({
            where,
            include: {
                assignedTo: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
                lead: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                deal: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                company: {
                    select: {
                        id: true,
                        name: true,
                        logo: true,
                    },
                },
            },
            orderBy: {
                [sortBy]: sortOrder,
            },
            skip,
            take: Number(limit),
        });
        res.status(200).json({
            activities,
            pagination: {
                total: totalCount,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(totalCount / Number(limit)),
            },
        });
    }
    catch (error) {
        console.error("Error fetching activities:", error);
        res.status(500).json({ error: "Failed to fetch activities" });
    }
};
exports.getAllActivities = getAllActivities;
// Get activity by ID
const getActivityById = async (req, res) => {
    try {
        const { id } = req.params;
        const activity = await prisma_js_1.prisma.activity.findUnique({
            where: { id: Number(id) },
            include: {
                assignedTo: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
                lead: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        company: true,
                    },
                },
                deal: {
                    select: {
                        id: true,
                        name: true,
                        value: true,
                        stage: true,
                    },
                },
                company: {
                    select: {
                        id: true,
                        name: true,
                        logo: true,
                        email: true,
                        phone: true,
                    },
                },
            },
        });
        if (!activity) {
            res.status(404).json({ error: "Activity not found" });
            return;
        }
        res.status(200).json(activity);
    }
    catch (error) {
        console.error("Error fetching activity:", error);
        res.status(500).json({ error: "Failed to fetch activity" });
    }
};
exports.getActivityById = getActivityById;
// Create a new activity
const createActivity = async (req, res) => {
    try {
        const { type, subject, description, status, startDate, endDate, location, leadId, dealId, companyId, assignedToId, } = req.body;
        // Create activity
        const activity = await prisma_js_1.prisma.activity.create({
            data: {
                type,
                subject,
                description,
                status,
                startDate: new Date(startDate),
                endDate: endDate ? new Date(endDate) : null,
                location,
                leadId,
                dealId,
                companyId,
                assignedToId: Number(assignedToId),
            },
            include: {
                assignedTo: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
                lead: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                deal: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                company: {
                    select: {
                        id: true,
                        name: true,
                        logo: true,
                    },
                },
            },
        });
        res.status(201).json(activity);
    }
    catch (error) {
        console.error("Error creating activity:", error);
        res.status(500).json({ error: "Failed to create activity" });
    }
};
exports.createActivity = createActivity;
// Update an activity
const updateActivity = async (req, res) => {
    try {
        const { id } = req.params;
        const { type, subject, description, status, startDate, endDate, location, leadId, dealId, companyId, assignedToId, outcome, } = req.body;
        // Check if activity exists
        const existingActivity = await prisma_js_1.prisma.activity.findUnique({
            where: { id: Number(id) },
        });
        if (!existingActivity) {
            res.status(404).json({ error: "Activity not found" });
            return;
        }
        // Update activity
        const updatedActivity = await prisma_js_1.prisma.activity.update({
            where: { id: Number(id) },
            data: {
                type,
                subject,
                description,
                status,
                startDate: startDate ? new Date(startDate) : undefined,
                endDate: endDate ? new Date(endDate) : null,
                location,
                leadId,
                dealId,
                companyId,
                assignedToId: assignedToId ? Number(assignedToId) : undefined,
                outcome,
                updatedAt: new Date(),
            },
            include: {
                assignedTo: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
                lead: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                deal: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                company: {
                    select: {
                        id: true,
                        name: true,
                        logo: true,
                    },
                },
            },
        });
        res.status(200).json(updatedActivity);
    }
    catch (error) {
        console.error("Error updating activity:", error);
        res.status(500).json({ error: "Failed to update activity" });
    }
};
exports.updateActivity = updateActivity;
// Delete an activity
const deleteActivity = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if activity exists
        const activity = await prisma_js_1.prisma.activity.findUnique({
            where: { id: Number(id) },
        });
        if (!activity) {
            res.status(404).json({ error: "Activity not found" });
            return;
        }
        // Delete activity
        await prisma_js_1.prisma.activity.delete({
            where: { id: Number(id) },
        });
        res.status(200).json({ message: "Activity deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting activity:", error);
        res.status(500).json({ error: "Failed to delete activity" });
    }
};
exports.deleteActivity = deleteActivity;
// Complete an activity
const completeActivity = async (req, res) => {
    try {
        const { id } = req.params;
        const { outcome } = req.body;
        // Check if activity exists
        const activity = await prisma_js_1.prisma.activity.findUnique({
            where: { id: Number(id) },
        });
        if (!activity) {
            res.status(404).json({ error: "Activity not found" });
            return;
        }
        // Update activity to completed
        const completedActivity = await prisma_js_1.prisma.activity.update({
            where: { id: Number(id) },
            data: {
                status: "COMPLETED",
                completedAt: new Date(),
                outcome,
                updatedAt: new Date(),
            },
            include: {
                assignedTo: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
            },
        });
        res.status(200).json(completedActivity);
    }
    catch (error) {
        console.error("Error completing activity:", error);
        res.status(500).json({ error: "Failed to complete activity" });
    }
};
exports.completeActivity = completeActivity;
// Get activities for calendar view
const getCalendarActivities = async (req, res) => {
    try {
        const { startDate, endDate, assignedTo, types } = req.query;
        // Build filter conditions
        const where = {};
        if (startDate && endDate) {
            where.startDate = {
                gte: new Date(startDate),
                lte: new Date(endDate),
            };
        }
        if (assignedTo) {
            where.assignedToId = Number(assignedTo);
        }
        if (types) {
            where.type = {
                in: types.split(","),
            };
        }
        // Get activities for calendar
        const activities = await prisma_js_1.prisma.activity.findMany({
            where,
            include: {
                assignedTo: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                lead: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                    },
                },
                deal: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                company: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
            orderBy: {
                startDate: "asc",
            },
        });
        // Format activities for calendar
        const calendarActivities = activities.map((activity) => {
            let title = activity.subject;
            let relatedTo = null;
            if (activity.lead) {
                relatedTo = `Lead: ${activity.lead.firstName} ${activity.lead.lastName}`;
            }
            else if (activity.deal) {
                relatedTo = `Deal: ${activity.deal.name}`;
            }
            else if (activity.company) {
                relatedTo = `Company: ${activity.company.name}`;
            }
            return {
                id: activity.id,
                title,
                start: activity.startDate,
                end: activity.endDate || undefined,
                allDay: !activity.endDate,
                type: activity.type,
                status: activity.status,
                location: activity.location,
                description: activity.description,
                relatedTo,
                assignedTo: activity.assignedTo
                    ? `${activity.assignedTo.firstName} ${activity.assignedTo.lastName}`
                    : null,
                backgroundColor: getActivityTypeColor(activity.type),
                borderColor: getActivityTypeColor(activity.type),
                textColor: "#ffffff",
            };
        });
        res.status(200).json(calendarActivities);
    }
    catch (error) {
        console.error("Error fetching calendar activities:", error);
        res.status(500).json({ error: "Failed to fetch calendar activities" });
    }
};
exports.getCalendarActivities = getCalendarActivities;
// Helper function to get color for activity type
const getActivityTypeColor = (type) => {
    switch (type) {
        case "CALL":
            return "#4f46e5"; // indigo
        case "EMAIL":
            return "#0ea5e9"; // sky
        case "MEETING":
            return "#f59e0b"; // amber
        case "TASK":
            return "#10b981"; // emerald
        case "NOTE":
            return "#6b7280"; // gray
        case "FOLLOW_UP":
            return "#8b5cf6"; // violet
        case "DEMO":
            return "#ef4444"; // red
        case "PRESENTATION":
            return "#ec4899"; // pink
        default:
            return "#6b7280"; // gray
    }
};
//# sourceMappingURL=activities.controller.js.map