"use strict";
// server/routes/communication.js
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
// Import controllers
const email_controller_js_1 = require("../controllers/communication/email.controller.js");
const chat_controller_js_1 = require("../controllers/communication/chat.controller.js");
const endpoint_controller_js_1 = require("../controllers/communication/endpoint.controller.js");
const public_controller_js_1 = require("../controllers/communication/public.controller.js");
const message_controller_js_1 = require("../controllers/communication/message.controller.js");
const router = express_1.default.Router();
// Endpoint routes
router.get("/endpoints", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(endpoint_controller_js_1.getEndpoints));
router.post("/endpoints", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(endpoint_controller_js_1.createEndpoint));
// Email routes
router.get("/email/accounts", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(email_controller_js_1.getEmailAccounts));
router.post("/email/accounts", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(email_controller_js_1.createEmailAccount));
// Demo data route (for testing)
router.post("/email/demo-data", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(email_controller_js_1.createDemoEmailData));
router.get("/email/:accountId/messages", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(email_controller_js_1.getEmailMessages));
router.get("/email/:accountId/folders", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(email_controller_js_1.getEmailFolders));
router.post("/email/send", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(email_controller_js_1.sendEmail));
router.patch("/email/messages/:messageId/status", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(email_controller_js_1.updateEmailStatus));
// Chat routes
router.get("/chat/conversations", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(chat_controller_js_1.getChatConversations));
router.get("/chat/conversations/:conversationId/messages", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(chat_controller_js_1.getChatMessages));
router.post("/chat/conversations", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(chat_controller_js_1.createChatConversation));
router.post("/chat/conversations/:conversationId/messages", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(chat_controller_js_1.sendChatMessage));
// Get company members for chat
router.get("/chat/company-members", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(chat_controller_js_1.getCompanyMembers));
// Contact form message routes
router.get("/endpoints/messages", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(message_controller_js_1.getContactFormMessages));
router.patch("/endpoints/messages/:messageId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(message_controller_js_1.markContactFormMessageAsRead));
router.delete("/endpoints/messages/:messageId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(message_controller_js_1.deleteContactFormMessage));
router.get("/endpoints/:endpointId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(endpoint_controller_js_1.getEndpointById));
router.put("/endpoints/:endpointId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(endpoint_controller_js_1.updateEndpoint));
router.delete("/endpoints/:endpointId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(endpoint_controller_js_1.deleteEndpoint));
router.get("/endpoints/:endpointId/requests", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(endpoint_controller_js_1.getEndpointRequests));
// Public endpoint for receiving data from custom endpoints
router.all("/public/*endpointPath", (0, route_helpers_js_1.wrapController)(public_controller_js_1.handlePublicEndpoint));
exports.default = router;
//# sourceMappingURL=communication.js.map