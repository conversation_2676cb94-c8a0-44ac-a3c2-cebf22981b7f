{"id": "#", "properties": {"appConfig": {"id": "#appConfig", "properties": {"icon": {"title": "<PERSON><PERSON>t Icon", "description": "Configure Nuxt Icon module preferences.", "tags": ["@studioIcon material-symbols:star"], "id": "#appConfig/icon", "properties": {"size": {"title": "Icon Size", "description": "Set the default icon size.", "tags": ["@studioIcon material-symbols:format-size-rounded"], "tsType": "string | undefined", "id": "#appConfig/icon/size", "default": {}, "type": "any"}, "class": {"title": "CSS Class", "description": "Set the default CSS class.", "tags": ["@studioIcon material-symbols:css"], "id": "#appConfig/icon/class", "default": "", "type": "string"}, "attrs": {"title": "Default Attributes", "description": "Attributes applied to every icon component.\n\n@default { \"aria-hidden\": true }", "tags": ["@studioIcon material-symbols:settings"], "tsType": "Record<string, string | number | boolean>", "id": "#appConfig/icon/attrs", "default": {"aria-hidden": true}, "type": "object"}, "mode": {"title": "Default Rendering Mode", "description": "Set the default rendering mode for the icon component", "enum": ["css", "svg"], "tags": ["@studioIcon material-symbols:move-down-rounded"], "id": "#appConfig/icon/mode", "default": "css", "type": "string"}, "aliases": {"title": "Icon aliases", "description": "Define Icon aliases to update them easily without code changes.", "tags": ["@studioIcon material-symbols:star-rounded"], "tsType": "{ [alias: string]: string }", "id": "#appConfig/icon/aliases", "default": {}, "type": "object"}, "cssSelectorPrefix": {"title": "CSS Selector Prefix", "description": "Set the default CSS selector prefix.", "tags": ["@studioIcon material-symbols:format-textdirection-l-to-r"], "id": "#appConfig/icon/cssSelectorPrefix", "default": "i-", "type": "string"}, "cssLayer": {"title": "CSS Layer Name", "description": "Set the default CSS `@layer` name.", "tags": ["@studioIcon material-symbols:layers"], "tsType": "string | undefined", "id": "#appConfig/icon/cssLayer", "default": {}, "type": "any"}, "cssWherePseudo": {"title": "Use CSS `:where()` P<PERSON>udo Selector", "description": "Use CSS `:where()` pseudo selector to reduce specificity.", "tags": ["@studioIcon material-symbols:low-priority"], "id": "#appConfig/icon/cssWherePseudo", "default": true, "type": "boolean"}, "collections": {"title": "Icon Collections", "description": "List of known icon collections name. Used to resolve collection name ambiguity.\ne.g. `simple-icons-github` -> `simple-icons:github` instead of `simple:icons-github`\n\nWhen not provided, will use the full Iconify collection list.", "tags": ["@studioIcon material-symbols:format-list-bulleted"], "tsType": "string[] | null", "id": "#appConfig/icon/collections", "default": null, "type": "any"}, "customCollections": {"title": "Custom Icon Collections", "tags": ["@studioIcon material-symbols:format-list-bulleted"], "tsType": "string[] | null", "id": "#appConfig/icon/customCollections", "default": null, "type": "any"}, "provider": {"title": "Icon Provider", "description": "Provider to use for fetching icons\n\n- `server` - Fetch icons with a server handler\n- `iconify` - Fetch icons with Iconify API, purely client-side\n\n`server` by default; `iconify` when `ssr: false`", "enum": ["server", "iconify"], "tags": ["@studioIcon material-symbols:cloud"], "type": "\"server\" | \"iconify\" | undefined", "id": "#appConfig/icon/provider"}, "iconifyApiEndpoint": {"title": "Iconify API Endpoint URL", "description": "Define a custom Iconify API endpoint URL. Useful if you want to use a self-hosted Iconify API. Learn more: https://iconify.design/docs/api.", "tags": ["@studioIcon material-symbols:api"], "id": "#appConfig/icon/iconifyApiEndpoint", "default": "https://api.iconify.design", "type": "string"}, "fallbackToApi": {"title": "Fallback to Iconify API", "description": "Fallback to Iconify API if server provider fails to found the collection.", "tags": ["@studioIcon material-symbols:public"], "enum": [true, false, "server-only", "client-only"], "type": "boolean | \"server-only\" | \"client-only\"", "id": "#appConfig/icon/fallbackToApi", "default": true}, "localApiEndpoint": {"title": "Local API Endpoint Path", "description": "Define a custom path for the local API endpoint.", "tags": ["@studioIcon material-symbols:api"], "id": "#appConfig/icon/localApiEndpoint", "default": "/api/_nuxt_icon", "type": "string"}, "fetchTimeout": {"title": "Fetch Timeout", "description": "Set the timeout for fetching icons.", "tags": ["@studioIcon material-symbols:timer"], "id": "#appConfig/icon/fetchTimeout", "default": 1500, "type": "number"}, "customize": {"title": "Customize callback", "description": "Customize icon content (replace stroke-width, colors, etc...).", "tags": ["@studioIcon material-symbols:edit"], "type": "IconifyIconCustomizeCallback", "id": "#appConfig/icon/customize"}}, "type": "object", "default": {"size": {}, "class": "", "attrs": {"aria-hidden": true}, "mode": "css", "aliases": {}, "cssSelectorPrefix": "i-", "cssLayer": {}, "cssWherePseudo": true, "collections": null, "customCollections": null, "iconifyApiEndpoint": "https://api.iconify.design", "fallbackToApi": true, "localApiEndpoint": "/api/_nuxt_icon", "fetchTimeout": 1500}}, "tairo": {"id": "#appConfig/tairo", "properties": {"iconnav": {"id": "#appConfig/tairo/iconnav", "properties": {"circularMenu": {"id": "#appConfig/tairo/iconnav/circularMenu", "properties": {"enabled": {"type": "boolean", "id": "#appConfig/tairo/iconnav/circularMenu/enabled", "default": true}, "tools": {"type": "array", "items": {"type": "object", "required": ["name"], "properties": {"component": {"type": "string"}, "props": {"type": "object"}}}, "id": "#appConfig/tairo/iconnav/circularMenu/tools"}}, "type": "object", "default": {"enabled": true}}, "toolbar": {"id": "#appConfig/tairo/iconnav/toolbar", "properties": {"enabled": {"type": "boolean", "id": "#appConfig/tairo/iconnav/toolbar/enabled", "default": true}, "showTitle": {"type": "boolean", "id": "#appConfig/tairo/iconnav/toolbar/showTitle", "default": true}, "tools": {"type": "array", "items": {"type": "object", "required": ["name"], "properties": {"component": {"type": "string"}, "props": {"type": "object"}}}, "id": "#appConfig/tairo/iconnav/toolbar/tools"}}, "type": "object", "default": {"enabled": true, "showTitle": true}}, "navigation": {"id": "#appConfig/tairo/iconnav/navigation", "properties": {"enabled": {"type": "boolean", "id": "#appConfig/tairo/iconnav/navigation/enabled", "default": true}, "logo": {"id": "#appConfig/tairo/iconnav/navigation/logo", "properties": {"component": {"type": "string", "id": "#appConfig/tairo/iconnav/navigation/logo/component", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "props": {"id": "#appConfig/tairo/iconnav/navigation/logo/props", "type": "any", "default": {}}}, "type": "object", "default": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}, "items": {"type": "array", "items": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "to": {"type": "string"}, "activePath": {"type": "string"}, "position": {"type": "string", "tsType": "'start' | 'end'"}, "divider": {"type": "boolean"}, "icon": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "class": {"type": "string"}}}, "children": {"type": "array", "items": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "to": {"type": "string"}, "activePath": {"type": "string"}, "position": {"type": "string", "tsType": "'start' | 'end'"}, "icon": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "class": {"type": "string"}}}}}}, "component": {"type": "object", "required": ["name"], "properties": {"component": {"type": "string"}, "props": {"type": "object"}}}, "click": {"type": "function"}}}, "id": "#appConfig/tairo/iconnav/navigation/items"}}, "type": "object", "default": {"enabled": true, "logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}}, "footer": {"id": "#appConfig/tairo/iconnav/footer", "properties": {"enabled": {"type": "boolean", "id": "#appConfig/tairo/iconnav/footer/enabled", "default": true}, "logoSeparator": {"id": "#appConfig/tairo/iconnav/footer/logoSeparator", "properties": {"component": {"type": "string", "id": "#appConfig/tairo/iconnav/footer/logoSeparator/component", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "props": {"id": "#appConfig/tairo/iconnav/footer/logoSeparator/props", "type": "any", "default": {}}}, "type": "object", "default": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}, "logo": {"id": "#appConfig/tairo/iconnav/footer/logo", "properties": {"component": {"type": "string", "id": "#appConfig/tairo/iconnav/footer/logo/component", "default": "TairoLogoText"}, "props": {"id": "#appConfig/tairo/iconnav/footer/logo/props", "type": "any", "default": {}}}, "type": "object", "default": {"component": "TairoLogoText", "props": {}}}, "copyright": {"id": "#appConfig/tairo/iconnav/footer/copyright", "properties": {"name": {"type": "string", "id": "#appConfig/tairo/iconnav/footer/copyright/name", "default": ""}, "to": {"type": "string", "id": "#appConfig/tairo/iconnav/footer/copyright/to", "default": ""}, "since": {"type": "string", "id": "#appConfig/tairo/iconnav/footer/copyright/since", "default": ""}}, "type": "object", "default": {"name": "", "to": "", "since": ""}}, "links": {"type": "array", "items": {"type": "object", "required": ["name", "to"], "properties": {"name": {"type": "string"}, "to": {"type": "string"}, "rel": {"type": "string"}, "target": {"type": "string"}}}, "id": "#appConfig/tairo/iconnav/footer/links"}}, "type": "object", "default": {"enabled": true, "logoSeparator": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}, "logo": {"component": "TairoLogoText", "props": {}}, "copyright": {"name": "", "to": "", "since": ""}}}}, "type": "object", "default": {"circularMenu": {"enabled": true}, "toolbar": {"enabled": true, "showTitle": true}, "navigation": {"enabled": true, "logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}, "footer": {"enabled": true, "logoSeparator": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}, "logo": {"component": "TairoLogoText", "props": {}}, "copyright": {"name": "", "to": "", "since": ""}}}}, "sidebar": {"id": "#appConfig/tairo/sidebar", "properties": {"circularMenu": {"id": "#appConfig/tairo/sidebar/circularMenu", "properties": {"enabled": {"type": "boolean", "id": "#appConfig/tairo/sidebar/circularMenu/enabled", "default": true}, "tools": {"type": "array", "items": {"type": "object", "required": ["name"], "properties": {"component": {"type": "string"}, "props": {"type": "object"}}}, "id": "#appConfig/tairo/sidebar/circularMenu/tools"}}, "type": "object", "default": {"enabled": true}}, "toolbar": {"id": "#appConfig/tairo/sidebar/toolbar", "properties": {"enabled": {"type": "boolean", "id": "#appConfig/tairo/sidebar/toolbar/enabled", "default": true}, "showTitle": {"type": "boolean", "id": "#appConfig/tairo/sidebar/toolbar/showTitle", "default": true}, "showNavBurger": {"type": "boolean", "id": "#appConfig/tairo/sidebar/toolbar/showNavBurger", "default": false}, "tools": {"type": "array", "items": {"type": "object", "required": ["name"], "properties": {"component": {"type": "string"}, "props": {"type": "object"}}}, "id": "#appConfig/tairo/sidebar/toolbar/tools"}}, "type": "object", "default": {"enabled": true, "showTitle": true, "showNavBurger": false}}, "navigation": {"id": "#appConfig/tairo/sidebar/navigation", "properties": {"enabled": {"type": "boolean", "id": "#appConfig/tairo/sidebar/navigation/enabled", "default": true}, "startOpen": {"type": "boolean", "id": "#appConfig/tairo/sidebar/navigation/startOpen", "default": true}, "logo": {"id": "#appConfig/tairo/sidebar/navigation/logo", "properties": {"component": {"type": "string", "id": "#appConfig/tairo/sidebar/navigation/logo/component", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "props": {"id": "#appConfig/tairo/sidebar/navigation/logo/props", "type": "any", "default": {}}}, "type": "object", "default": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}, "items": {"type": "array", "items": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string"}, "order": {"type": "number", "$default": 10}, "activePath": {"type": "string"}, "position": {"type": "string", "tsType": "'start' | 'end'", "$default": "start"}, "component": {"type": "string"}, "props": {"type": "object"}, "icon": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "class": {"type": "string"}}}, "to": {"type": "string"}, "click": {"type": "function"}, "subsidebar": {"type": "object", "required": ["name"], "properties": {"component": {"type": "string"}, "props": {"type": "object"}}}}}, "id": "#appConfig/tairo/sidebar/navigation/items"}}, "type": "object", "default": {"enabled": true, "startOpen": true, "logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}}}, "type": "object", "default": {"circularMenu": {"enabled": true}, "toolbar": {"enabled": true, "showTitle": true, "showNavBurger": false}, "navigation": {"enabled": true, "startOpen": true, "logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}}}, "title": {"type": "string", "id": "#appConfig/tairo/title", "default": "<PERSON><PERSON>"}, "error": {"id": "#appConfig/tairo/error", "properties": {"logo": {"id": "#appConfig/tairo/error/logo", "properties": {"component": {"type": "string", "id": "#appConfig/tairo/error/logo/component", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "props": {"id": "#appConfig/tairo/error/logo/props", "type": "any", "default": {}}}, "type": "object", "default": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}}, "type": "object", "default": {"logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}}, "panels": {"type": "array", "items": {"type": "object", "required": ["name", "component"], "properties": {"name": {"type": "string"}, "position": {"type": "string", "tsType": "'left' | 'right'", "$default": "left"}, "size": {"type": "string", "tsType": "'sm' | 'md'", "$default": "sm"}, "component": {"type": "string"}, "props": {"type": "object"}, "overlay": {"type": "boolean", "$default": true}}}, "id": "#appConfig/tairo/panels"}}, "type": "object", "default": {"iconnav": {"circularMenu": {"enabled": true}, "toolbar": {"enabled": true, "showTitle": true}, "navigation": {"enabled": true, "logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}, "footer": {"enabled": true, "logoSeparator": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}, "logo": {"component": "TairoLogoText", "props": {}}, "copyright": {"name": "", "to": "", "since": ""}}}, "sidebar": {"circularMenu": {"enabled": true}, "toolbar": {"enabled": true, "showTitle": true, "showNavBurger": false}, "navigation": {"enabled": true, "startOpen": true, "logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}}, "title": "<PERSON><PERSON>", "error": {"logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}}}, "nui": {"id": "#appConfig/nui", "properties": {"BaseAccordion": {"title": "", "description": "", "tags": [], "id": "#appConfig/nui/BaseAccordion", "properties": {"action": {"title": "The action icon of the accordion.", "description": "", "tags": [], "tsType": "'dot' | 'chevron' | 'plus'", "id": "#appConfig/nui/BaseAccordion/action", "default": "dot", "type": "string"}, "dotColor": {"title": "Default color for the accordion dot", "description": "", "tags": [], "tsType": "'default' | 'primary' | 'success' | 'info' | 'warning' | 'danger' | 'dark' | 'black'", "id": "#appConfig/nui/BaseAccordion/dotColor", "default": "primary", "type": "string"}, "color": {"title": "The color of the accordion.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast'", "id": "#appConfig/nui/BaseAccordion/color", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the accordion.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg'", "id": "#appConfig/nui/BaseAccordion/rounded", "default": "sm", "type": "string"}}, "type": "object", "default": {"action": "dot", "dotColor": "primary", "color": "default", "rounded": "sm"}}, "BaseAvatar": {"id": "#appConfig/nui/BaseAvatar", "properties": {"color": {"title": "Default color for the avatar", "description": "", "tags": [], "tsType": "'white' | 'muted' | 'primary' | 'success' | 'info' | 'warning' | 'danger' | 'pink' | 'yellow' | 'indigo' | 'violet'", "id": "#appConfig/nui/BaseAvatar/color", "default": "muted", "type": "string"}, "rounded": {"title": "The radius of the avatar.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseAvatar/rounded", "default": "full", "type": "string"}, "size": {"title": "The size of the avatar.", "description": "", "tags": [], "tsType": "'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl'", "id": "#appConfig/nui/BaseAvatar/size", "default": "sm", "type": "string"}}, "type": "object", "default": {"color": "muted", "rounded": "full", "size": "sm"}}, "BaseAvatarGroup": {"id": "#appConfig/nui/BaseAvatarGroup", "properties": {"limit": {"title": "The limit of avatars to display.", "description": "", "tags": [], "type": "number", "tsType": "number", "id": "#appConfig/nui/BaseAvatarGroup/limit", "default": 4}, "size": {"title": "The size of the avatar group.", "description": "", "tags": [], "tsType": "'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl'", "id": "#appConfig/nui/BaseAvatarGroup/size", "default": "sm", "type": "string"}}, "type": "object", "default": {"limit": 4, "size": "sm"}}, "BaseBreadcrumb": {"id": "#appConfig/nui/BaseBreadcrumb", "properties": {"color": {"title": "Defines the hover color of the breadcrumb links", "description": "", "tags": [], "tsType": "'primary' | 'dark' | 'black'", "id": "#appConfig/nui/BaseBreadcrumb/color", "default": "primary", "type": "string"}}, "type": "object", "default": {"color": "primary"}}, "BaseButton": {"id": "#appConfig/nui/BaseButton", "properties": {"color": {"title": "Default color for the BaseButton component", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'light' | 'dark' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'none'", "id": "#appConfig/nui/BaseButton/color", "default": "default", "type": "string"}, "rounded": {"title": "Default rounded for the BaseButton component", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseButton/rounded", "default": "md", "type": "string"}, "size": {"title": "Default size for the BaseButton component", "description": "", "tags": [], "tsType": "'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BaseButton/size", "default": "md", "type": "string"}, "variant": {"title": "Default variant for the BaseButton component", "description": "", "tags": [], "tsType": "'solid' | 'pastel' | 'outline'", "id": "#appConfig/nui/BaseButton/variant", "default": "solid", "type": "string"}}, "type": "object", "default": {"color": "default", "rounded": "md", "size": "md", "variant": "solid"}}, "BaseButtonAction": {"id": "#appConfig/nui/BaseButtonAction", "properties": {"color": {"title": "Default color for the BaseButtonAction component", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'light' | 'dark' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'none'", "id": "#appConfig/nui/BaseButtonAction/color", "default": "default", "type": "string"}, "rounded": {"title": "Default rounded for the BaseButtonAction component", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseButtonAction/rounded", "default": "md", "type": "string"}}, "type": "object", "default": {"color": "default", "rounded": "md"}}, "BaseButtonClose": {"id": "#appConfig/nui/BaseButtonClose", "properties": {"color": {"title": "Default color for the BaseButtonClose component", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'none'", "id": "#appConfig/nui/BaseButtonClose/color", "default": "default", "type": "string"}, "rounded": {"title": "Default rounded for the BaseButtonClose component", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseButtonClose/rounded", "default": "full", "type": "string"}, "size": {"title": "Default size for the BaseButtonClose component", "description": "", "tags": [], "tsType": "'xs' | 'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BaseButtonClose/size", "default": "sm", "type": "string"}}, "type": "object", "default": {"color": "default", "rounded": "full", "size": "sm"}}, "BaseButtonGroup": {"id": "#appConfig/nui/BaseButtonGroup", "type": "any", "default": {}}, "BaseButtonIcon": {"id": "#appConfig/nui/BaseButtonIcon", "properties": {"color": {"title": "Default color for the BaseButtonIcon component", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'light' | 'dark' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'none'", "id": "#appConfig/nui/BaseButtonIcon/color", "default": "default", "type": "string"}, "rounded": {"title": "Default rounded for the BaseButtonIcon component", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseButtonIcon/rounded", "default": "md", "type": "string"}, "size": {"title": "Default size for the BaseButton component", "description": "", "tags": [], "tsType": "'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BaseButtonIcon/size", "default": "md", "type": "string"}}, "type": "object", "default": {"color": "default", "rounded": "md", "size": "md"}}, "BaseCard": {"id": "#appConfig/nui/BaseCard", "properties": {"color": {"title": "Default color for the BaseCard component", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'dark' | 'black' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'none'", "id": "#appConfig/nui/BaseCard/color", "default": "default", "type": "string"}, "rounded": {"title": "Default rounded for the BaseCard component", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg'", "id": "#appConfig/nui/BaseCard/rounded", "default": "sm", "type": "string"}}, "type": "object", "default": {"color": "default", "rounded": "sm"}}, "BaseDropdown": {"id": "#appConfig/nui/BaseDropdown", "properties": {"buttonColor": {"title": "The color of the dropdown button.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'light' | 'dark' | 'black' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'none'", "id": "#appConfig/nui/BaseDropdown/buttonColor", "default": "default", "type": "string"}, "buttonSize": {"title": "The size of the dropdown button.", "description": "", "tags": [], "tsType": "'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BaseDropdown/buttonSize", "default": "md", "type": "string"}, "color": {"title": "The color of the dropdown.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'none'", "id": "#appConfig/nui/BaseDropdown/color", "default": "default", "type": "string"}, "placement": {"title": "The placement of the dropdown via floating-ui", "description": "", "tags": [], "tsType": "'top' | 'top-start' | 'top-end' | 'right' | 'right-start' | 'right-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end'", "id": "#appConfig/nui/BaseDropdown/placement", "default": "bottom-start", "type": "string"}, "rounded": {"title": "Default rounded for the BaseDropdown component", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseDropdown/rounded", "default": "sm", "type": "string"}, "size": {"title": "Default size for the BaseDropdown component menu", "description": "", "tags": [], "tsType": "'md' | 'lg'", "id": "#appConfig/nui/BaseDropdown/size", "default": "md", "type": "string"}, "variant": {"title": "The variant of the dropdown.", "description": "", "tags": [], "tsType": "'button' | 'context' | 'text'", "id": "#appConfig/nui/BaseDropdown/variant", "default": "button", "type": "string"}}, "type": "object", "default": {"buttonColor": "default", "buttonSize": "md", "color": "default", "placement": "bottom-start", "rounded": "sm", "size": "md", "variant": "button"}}, "BaseDropdownDivider": {"id": "#appConfig/nui/BaseDropdownDivider", "type": "any", "default": {}}, "BaseDropdownItem": {"id": "#appConfig/nui/BaseDropdownItem", "properties": {"color": {"title": "The hover color of the dropdown item inner elements.", "description": "", "tags": [], "tsType": "'primary' | 'info' | 'success' | 'warning' | 'danger' | 'dark' | 'black'", "id": "#appConfig/nui/BaseDropdownItem/color", "default": "primary", "type": "string"}, "contrast": {"title": "The contrast of the dropdown item.", "description": "", "tags": [], "tsType": "'default' | 'contrast'", "id": "#appConfig/nui/BaseDropdownItem/contrast", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the dropdown item.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg'", "id": "#appConfig/nui/BaseDropdownItem/rounded", "default": "sm", "type": "string"}}, "type": "object", "default": {"color": "primary", "contrast": "default", "rounded": "sm"}}, "BaseHeading": {"id": "#appConfig/nui/BaseHeading", "properties": {"as": {"title": "The tag of the heading.", "description": "", "tags": [], "type": "string", "tsType": "string", "id": "#appConfig/nui/BaseHeading/as", "default": "p"}, "lead": {"title": "The lead of the heading.", "description": "", "tags": [], "tsType": "'none' | 'tight' | 'snug' | 'normal' | 'relaxed' | 'loose'", "id": "#appConfig/nui/BaseHeading/lead", "default": "normal", "type": "string"}, "size": {"title": "The size of the heading.", "description": "", "tags": [], "tsType": "'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | '8xl' | '9xl'", "id": "#appConfig/nui/BaseHeading/size", "default": "xl", "type": "string"}, "weight": {"title": "The weight of the heading.", "description": "", "tags": [], "tsType": "'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold'", "id": "#appConfig/nui/BaseHeading/weight", "default": "semibold", "type": "string"}}, "type": "object", "default": {"as": "p", "lead": "normal", "size": "xl", "weight": "semibold"}}, "BaseIconBox": {"id": "#appConfig/nui/BaseIconBox", "properties": {"color": {"title": "The color of the icon box.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'dark' | 'light' | 'black' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'none'", "id": "#appConfig/nui/BaseIconBox/color", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the icon box.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseIconBox/rounded", "default": "sm", "type": "string"}, "size": {"title": "The size of the icon box.", "description": "", "tags": [], "tsType": "'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'", "id": "#appConfig/nui/BaseIconBox/size", "default": "xs", "type": "string"}, "variant": {"title": "The variant of the icon box.", "description": "", "tags": [], "tsType": "'solid' | 'outline' | 'pastel'", "id": "#appConfig/nui/BaseIconBox/variant", "default": "solid", "type": "string"}}, "type": "object", "default": {"color": "default", "rounded": "sm", "size": "xs", "variant": "solid"}}, "BaseKbd": {"id": "#appConfig/nui/BaseKbd", "properties": {"color": {"title": "The color of the kbd.", "description": "", "tags": [], "tsType": "'default' | 'muted' | 'none'", "id": "#appConfig/nui/BaseKbd/color", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the kbd.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseKbd/rounded", "default": "sm", "type": "string"}, "size": {"title": "The size of the kbd.", "description": "", "tags": [], "tsType": "'xs' | 'sm' | 'md' | 'lg'", "id": "#appConfig/nui/BaseKbd/size", "default": "sm", "type": "string"}}, "type": "object", "default": {"color": "default", "rounded": "sm", "size": "sm"}}, "BaseLink": {"id": "#appConfig/nui/BaseLink", "type": "any", "default": {}}, "BaseList": {"id": "#appConfig/nui/BaseList", "type": "any", "default": {}}, "BaseListItem": {"id": "#appConfig/nui/BaseListItem", "type": "any", "default": {}}, "BaseMessage": {"id": "#appConfig/nui/BaseMessage", "properties": {"color": {"title": "The color of the message.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-muted' | 'primary' | 'info' | 'success' | 'warning' | 'danger'", "id": "#appConfig/nui/BaseMessage/color", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the message.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseMessage/rounded", "default": "sm", "type": "string"}, "defaultIcons": {"title": "The default icons of the message.", "description": "", "tags": [], "tsType": "{\n            default?: string\n            'default-contrast'?: string\n            muted?: string\n            'muted-contrast'?: string\n            info?: string\n            success?: string\n            warning?: string\n            danger?: string\n            primary?: string\n          }", "id": "#appConfig/nui/BaseMessage/defaultIcons", "properties": {"muted": {"type": "string", "id": "#appConfig/nui/BaseMessage/defaultIcons/muted", "default": "akar-icons:info-fill"}, "muted-contrast": {"type": "string", "id": "#appConfig/nui/BaseMessage/defaultIcons/muted-contrast", "default": "akar-icons:info-fill"}, "default": {"type": "string", "id": "#appConfig/nui/BaseMessage/defaultIcons/default", "default": "akar-icons:info-fill"}, "default-contrast": {"type": "string", "id": "#appConfig/nui/BaseMessage/defaultIcons/default-contrast", "default": "akar-icons:info-fill"}, "info": {"type": "string", "id": "#appConfig/nui/BaseMessage/defaultIcons/info", "default": "akar-icons:info-fill"}, "success": {"type": "string", "id": "#appConfig/nui/BaseMessage/defaultIcons/success", "default": "carbon:checkmark-filled"}, "warning": {"type": "string", "id": "#appConfig/nui/BaseMessage/defaultIcons/warning", "default": "ci:warning"}, "danger": {"type": "string", "id": "#appConfig/nui/BaseMessage/defaultIcons/danger", "default": "ph:warning-octagon-fill"}, "primary": {"type": "string", "id": "#appConfig/nui/BaseMessage/defaultIcons/primary", "default": "akar-icons:info-fill"}}, "type": "object", "default": {"muted": "akar-icons:info-fill", "muted-contrast": "akar-icons:info-fill", "default": "akar-icons:info-fill", "default-contrast": "akar-icons:info-fill", "info": "akar-icons:info-fill", "success": "carbon:checkmark-filled", "warning": "ci:warning", "danger": "ph:warning-octagon-fill", "primary": "akar-icons:info-fill"}}}, "type": "object", "default": {"color": "default", "rounded": "sm", "defaultIcons": {"muted": "akar-icons:info-fill", "muted-contrast": "akar-icons:info-fill", "default": "akar-icons:info-fill", "default-contrast": "akar-icons:info-fill", "info": "akar-icons:info-fill", "success": "carbon:checkmark-filled", "warning": "ci:warning", "danger": "ph:warning-octagon-fill", "primary": "akar-icons:info-fill"}}}, "BasePagination": {"id": "#appConfig/nui/BasePagination", "properties": {"color": {"title": "The color of the pagination.", "description": "", "tags": [], "tsType": "'primary' | 'dark' | 'black'", "id": "#appConfig/nui/BasePagination/color", "default": "primary", "type": "string"}, "rounded": {"title": "The radius of the pagination.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BasePagination/rounded", "default": "sm", "type": "string"}}, "type": "object", "default": {"color": "primary", "rounded": "sm"}}, "BaseParagraph": {"id": "#appConfig/nui/BaseParagraph", "properties": {"as": {"title": "The tag of the paragraph.", "description": "", "tags": [], "type": "string", "tsType": "string", "id": "#appConfig/nui/BaseParagraph/as", "default": "p"}, "lead": {"title": "The lead of the paragraph.", "description": "", "tags": [], "tsType": "'none' | 'tight' | 'snug' | 'normal' | 'relaxed' | 'loose'", "id": "#appConfig/nui/BaseParagraph/lead", "default": "normal", "type": "string"}, "size": {"title": "The size of the paragraph.", "description": "", "tags": [], "tsType": "'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | '8xl' | '9xl'", "id": "#appConfig/nui/BaseParagraph/size", "default": "md", "type": "string"}, "weight": {"title": "The weight of the paragraph.", "description": "", "tags": [], "tsType": "'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold'", "id": "#appConfig/nui/BaseParagraph/weight", "default": "normal", "type": "string"}}, "type": "object", "default": {"as": "p", "lead": "normal", "size": "md", "weight": "normal"}}, "BasePlaceholderPage": {"id": "#appConfig/nui/BasePlaceholderPage", "properties": {"imageSize": {"title": "The size of the placeholder image.", "description": "", "tags": [], "tsType": "'xs' | 'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BasePlaceholderPage/imageSize", "default": "xs", "type": "string"}}, "type": "object", "default": {"imageSize": "xs"}}, "BasePlaceload": {"id": "#appConfig/nui/BasePlaceload", "type": "any", "default": {}}, "BaseProgress": {"id": "#appConfig/nui/BaseProgress", "properties": {"color": {"title": "The color of the progress.", "description": "", "tags": [], "tsType": "'primary' | 'info' | 'success' | 'warning' | 'danger' | 'light' | 'dark' | 'black'", "id": "#appConfig/nui/BaseProgress/color", "default": "primary", "type": "string"}, "contrast": {"title": "The grey shade of the progress.", "description": "", "tags": [], "tsType": "'default' | 'contrast'", "id": "#appConfig/nui/BaseProgress/contrast", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the progress.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseProgress/rounded", "default": "full", "type": "string"}, "size": {"title": "The size of the progress.", "description": "", "tags": [], "tsType": "'xs' | 'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BaseProgress/size", "default": "sm", "type": "string"}}, "type": "object", "default": {"color": "primary", "contrast": "default", "rounded": "full", "size": "sm"}}, "BaseProgressCircle": {"id": "#appConfig/nui/BaseProgressCircle", "properties": {"color": {"title": "The color of the progress circle.", "description": "", "tags": [], "tsType": "'primary' | 'info' | 'success' | 'warning' | 'danger' | 'light' | 'dark' | 'black'", "id": "#appConfig/nui/BaseProgressCircle/color", "default": "primary", "type": "string"}}, "type": "object", "default": {"color": "primary"}}, "BaseProse": {"id": "#appConfig/nui/BaseProse", "properties": {"rounded": {"title": "The radius of the prose.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg'", "id": "#appConfig/nui/BaseProse/rounded", "default": "none", "type": "string"}}, "type": "object", "default": {"rounded": "none"}}, "BaseSnack": {"id": "#appConfig/nui/BaseSnack", "properties": {"color": {"title": "The color of the snack.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast'", "id": "#appConfig/nui/BaseSnack/color", "default": "default", "type": "string"}, "size": {"title": "The size of the snack.", "description": "", "tags": [], "tsType": "'xs' | 'sm' | 'md'", "id": "#appConfig/nui/BaseSnack/size", "default": "md", "type": "string"}}, "type": "object", "default": {"color": "default", "size": "md"}}, "BaseTabs": {"id": "#appConfig/nui/BaseTabs", "properties": {"color": {"title": "The color of the active tab.", "description": "", "tags": [], "tsType": "'default' | 'primary' | 'light' | 'dark' | 'black'", "id": "#appConfig/nui/BaseTabs/color", "default": "primary", "type": "string"}, "justify": {"title": "The alignment of the tabs.", "description": "", "tags": [], "tsType": "'start' | 'center' | 'end'", "id": "#appConfig/nui/BaseTabs/justify", "default": "start", "type": "string"}, "type": {"title": "The type of the tabs.", "description": "", "tags": [], "tsType": "'tabs' | 'box'", "id": "#appConfig/nui/BaseTabs/type", "default": "tabs", "type": "string"}}, "type": "object", "default": {"color": "primary", "justify": "start", "type": "tabs"}}, "BaseTabSlider": {"id": "#appConfig/nui/BaseTabSlider", "properties": {"color": {"title": "The color of the active tab.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'primary' | 'light' | 'dark' | 'black'", "id": "#appConfig/nui/BaseTabSlider/color", "default": "default", "type": "string"}, "justify": {"title": "The alignment of the tabs.", "description": "", "tags": [], "tsType": "'start' | 'center' | 'end'", "id": "#appConfig/nui/BaseTabSlider/justify", "default": "start", "type": "string"}, "rounded": {"title": "The radius of the tabs.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseTabSlider/rounded", "default": "lg", "type": "string"}, "size": {"title": "The size of the tabs.", "description": "", "tags": [], "tsType": "'sm' | 'md'", "id": "#appConfig/nui/BaseTabSlider/size", "default": "md", "type": "string"}}, "type": "object", "default": {"color": "default", "justify": "start", "rounded": "lg", "size": "md"}}, "BaseTag": {"id": "#appConfig/nui/BaseTag", "properties": {"color": {"title": "The color of the tag.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'light' | 'dark' | 'black' | 'primary' | 'info' | 'success' | 'warning' | 'danger'", "id": "#appConfig/nui/BaseTag/color", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the tag.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseTag/rounded", "default": "lg", "type": "string"}, "size": {"title": "The size of the tag.", "description": "", "tags": [], "tsType": "'sm' | 'md'", "id": "#appConfig/nui/BaseTag/size", "default": "md", "type": "string"}, "variant": {"title": "The variant of the tag.", "description": "", "tags": [], "tsType": "'solid' | 'pastel' | 'outline'", "id": "#appConfig/nui/BaseTag/variant", "default": "solid", "type": "string"}}, "type": "object", "default": {"color": "default", "rounded": "lg", "size": "md", "variant": "solid"}}, "BaseText": {"id": "#appConfig/nui/BaseText", "properties": {"lead": {"title": "The lead of the text span.", "description": "", "tags": [], "tsType": "'none' | 'tight' | 'snug' | 'normal' | 'relaxed' | 'loose'", "id": "#appConfig/nui/BaseText/lead", "default": "normal", "type": "string"}, "size": {"title": "The size of the text span.", "description": "", "tags": [], "tsType": "'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | '8xl' | '9xl'", "id": "#appConfig/nui/BaseText/size", "default": "md", "type": "string"}, "weight": {"title": "The weight of the text span.", "description": "", "tags": [], "tsType": "'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold'", "id": "#appConfig/nui/BaseText/weight", "default": "normal", "type": "string"}}, "type": "object", "default": {"lead": "normal", "size": "md", "weight": "normal"}}, "BaseThemeSwitch": {"id": "#appConfig/nui/BaseThemeSwitch", "properties": {"disableTransitions": {"title": "Disables transitions when toggling between light and dark mode.", "description": "", "tags": [], "type": "boolean", "tsType": "boolean", "id": "#appConfig/nui/BaseThemeSwitch/disableTransitions", "default": false}}, "type": "object", "default": {"disableTransitions": false}}, "BaseThemeToggle": {"id": "#appConfig/nui/BaseThemeToggle", "properties": {"disableTransitions": {"title": "Disables transitions when toggling between light and dark mode.", "description": "", "tags": [], "type": "boolean", "tsType": "boolean", "id": "#appConfig/nui/BaseThemeToggle/disableTransitions", "default": false}}, "type": "object", "default": {"disableTransitions": false}}, "BaseAutocomplete": {"title": "", "description": "", "tags": [], "id": "#appConfig/nui/BaseAutocomplete", "properties": {"contrast": {"title": "The contrast of the autocomplete.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast'", "id": "#appConfig/nui/BaseAutocomplete/contrast", "default": "default", "type": "string"}, "i18n": {"title": "Translation strings.", "description": "", "tags": [], "tsType": "Record<'empty' | 'pending', string>", "id": "#appConfig/nui/BaseAutocomplete/i18n", "properties": {"empty": {"type": "string", "id": "#appConfig/nui/BaseAutocomplete/i18n/empty", "default": "Nothing found."}, "pending": {"type": "string", "id": "#appConfig/nui/BaseAutocomplete/i18n/pending", "default": "Loading ..."}}, "type": "object", "default": {"empty": "Nothing found.", "pending": "Loading ..."}}, "rounded": {"title": "The radius of the autocomplete.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseAutocomplete/rounded", "default": "sm", "type": "string"}, "size": {"title": "The size of the autocomplete.", "description": "", "tags": [], "tsType": "'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BaseAutocomplete/size", "default": "md", "type": "string"}}, "type": "object", "default": {"contrast": "default", "i18n": {"empty": "Nothing found.", "pending": "Loading ..."}, "rounded": "sm", "size": "md"}}, "BaseAutocompleteItem": {"id": "#appConfig/nui/BaseAutocompleteItem", "properties": {"rounded": {"title": "The radius of the autocomplete item.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseAutocompleteItem/rounded", "default": "sm", "type": "string"}}, "type": "object", "default": {"rounded": "sm"}}, "BaseCheckbox": {"id": "#appConfig/nui/BaseCheckbox", "properties": {"color": {"title": "Default color for the BaseCheckbox component", "description": "", "tags": [], "tsType": "'default' | 'muted' | 'light' | 'dark' | 'black' | 'primary' | 'info' | 'success' | 'warning' | 'danger'", "id": "#appConfig/nui/BaseCheckbox/color", "default": "default", "type": "string"}, "rounded": {"title": "Default rounded for the BaseCheckbox component", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseCheckbox/rounded", "default": "sm", "type": "string"}}, "type": "object", "default": {"color": "default", "rounded": "sm"}}, "BaseCheckboxAnimated": {"id": "#appConfig/nui/BaseCheckboxAnimated", "properties": {"color": {"title": "Default color for the BaseCheckbox component", "description": "", "tags": [], "tsType": "'primary' | 'info' | 'success' | 'warning' | 'danger' | 'light' | 'muted' | 'dark' | 'black'", "id": "#appConfig/nui/BaseCheckboxAnimated/color", "default": "primary", "type": "string"}}, "type": "object", "default": {"color": "primary"}}, "BaseCheckboxHeadless": {"id": "#appConfig/nui/BaseCheckboxHeadless", "type": "any", "default": {}}, "BaseFullscreenDropfile": {"id": "#appConfig/nui/BaseFullscreenDropfile", "properties": {"color": {"title": "The color of the icon.", "description": "", "tags": [], "tsType": "'primary' | 'dark' | 'black'", "id": "#appConfig/nui/BaseFullscreenDropfile/color", "default": "primary", "type": "string"}}, "type": "object", "default": {"color": "primary"}}, "BaseInput": {"id": "#appConfig/nui/BaseInput", "properties": {"contrast": {"title": "The contrast of the input.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast'", "id": "#appConfig/nui/BaseInput/contrast", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the input.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseInput/rounded", "default": "sm", "type": "string"}, "size": {"title": "The size of the input.", "description": "", "tags": [], "tsType": "'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BaseInput/size", "default": "md", "type": "string"}}, "type": "object", "default": {"contrast": "default", "rounded": "sm", "size": "md"}}, "BaseInputFile": {"id": "#appConfig/nui/BaseInputFile", "properties": {"contrast": {"title": "The contrast of the input.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast'", "id": "#appConfig/nui/BaseInputFile/contrast", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the input.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseInputFile/rounded", "default": "sm", "type": "string"}, "size": {"title": "The size of the input.", "description": "", "tags": [], "tsType": "'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BaseInputFile/size", "default": "md", "type": "string"}, "i18n": {"title": "The translation strings for the input file.", "description": "", "tags": [], "tsType": "{ empty: string; invalid: string; multiple: string }", "id": "#appConfig/nui/BaseInputFile/i18n", "properties": {"empty": {"type": "string", "id": "#appConfig/nui/BaseInputFile/i18n/empty", "default": "No file chosen"}, "invalid": {"type": "string", "id": "#appConfig/nui/BaseInputFile/i18n/invalid", "default": "Invalid file selected"}, "multiple": {"type": "string", "id": "#appConfig/nui/BaseInputFile/i18n/multiple", "default": "{count} files selected"}}, "type": "object", "default": {"empty": "No file chosen", "invalid": "Invalid file selected", "multiple": "{count} files selected"}}}, "type": "object", "default": {"contrast": "default", "rounded": "sm", "size": "md", "i18n": {"empty": "No file chosen", "invalid": "Invalid file selected", "multiple": "{count} files selected"}}}, "BaseInputFileHeadless": {"id": "#appConfig/nui/BaseInputFileHeadless", "type": "any", "default": {}}, "BaseInputNumber": {"id": "#appConfig/nui/BaseInputNumber", "properties": {"contrast": {"title": "The contrast of the input.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast'", "id": "#appConfig/nui/BaseInputNumber/contrast", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the input.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseInputNumber/rounded", "default": "sm", "type": "string"}, "size": {"title": "The size of the input.", "description": "", "tags": [], "tsType": "'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BaseInputNumber/size", "default": "md", "type": "string"}}, "type": "object", "default": {"contrast": "default", "rounded": "sm", "size": "md"}}, "BaseInputHelpText": {"id": "#appConfig/nui/BaseInputHelpText", "properties": {"color": {"title": "The color of the text.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'light' | 'dark' | 'black' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'none'", "id": "#appConfig/nui/BaseInputHelpText/color", "default": "default", "type": "string"}}, "type": "object", "default": {"color": "default"}}, "BaseListbox": {"id": "#appConfig/nui/BaseListbox", "properties": {"contrast": {"title": "The contrast of the input.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast'", "id": "#appConfig/nui/BaseListbox/contrast", "default": "default", "type": "string"}, "placement": {"title": "The placement of the dropdown via floating-ui", "description": "", "tags": [], "tsType": "'top' | 'top-start' | 'top-end' | 'right' | 'right-start' | 'right-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end'", "id": "#appConfig/nui/BaseListbox/placement", "default": "bottom-start", "type": "string"}, "rounded": {"title": "The radius of the input.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseListbox/rounded", "default": "sm", "type": "string"}, "size": {"title": "The size of the input.", "description": "", "tags": [], "tsType": "'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BaseListbox/size", "default": "md", "type": "string"}}, "type": "object", "default": {"contrast": "default", "placement": "bottom-start", "rounded": "sm", "size": "md"}}, "BaseListboxItem": {"id": "#appConfig/nui/BaseListboxItem", "type": "any", "default": {}}, "BaseRadio": {"id": "#appConfig/nui/BaseRadio", "properties": {"color": {"title": "Default color for the BaseRadio component", "description": "", "tags": [], "tsType": "'default' | 'light' | 'muted' | 'dark' | 'black' | 'primary' | 'info' | 'success' | 'warning' | 'danger'", "id": "#appConfig/nui/BaseRadio/color", "default": "default", "type": "string"}}, "type": "object", "default": {"color": "default"}}, "BaseRadioHeadless": {"id": "#appConfig/nui/BaseRadioHeadless", "type": "any", "default": {}}, "BaseSelect": {"id": "#appConfig/nui/BaseSelect", "properties": {"contrast": {"title": "The contrast of the select.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast'", "id": "#appConfig/nui/BaseSelect/contrast", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the select.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseSelect/rounded", "default": "sm", "type": "string"}, "size": {"title": "The size of the select.", "description": "", "tags": [], "tsType": "'sm' | 'md' | 'lg' | 'xl'", "id": "#appConfig/nui/BaseSelect/size", "default": "md", "type": "string"}}, "type": "object", "default": {"contrast": "default", "rounded": "sm", "size": "md"}}, "BaseSwitchBall": {"id": "#appConfig/nui/BaseSwitchBall", "properties": {"color": {"title": "The color of the switch.", "description": "", "tags": [], "tsType": "'primary' | 'info' | 'success' | 'warning' | 'danger' | 'dark' | 'black'", "id": "#appConfig/nui/BaseSwitchBall/color", "default": "primary", "type": "string"}}, "type": "object", "default": {"color": "primary"}}, "BaseSwitchThin": {"id": "#appConfig/nui/BaseSwitchThin", "properties": {"color": {"title": "The color of the switch.", "description": "", "tags": [], "tsType": "'primary' | 'info' | 'success' | 'warning' | 'danger' | 'dark' | 'black'", "id": "#appConfig/nui/BaseSwitchThin/color", "default": "primary", "type": "string"}}, "type": "object", "default": {"color": "primary"}}, "BaseTextarea": {"id": "#appConfig/nui/BaseTextarea", "properties": {"contrast": {"title": "The contrast of the textarea.", "description": "", "tags": [], "tsType": "'default' | 'default-contrast' | 'muted' | 'muted-contrast'", "id": "#appConfig/nui/BaseTextarea/contrast", "default": "default", "type": "string"}, "rounded": {"title": "The radius of the textarea.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseTextarea/rounded", "default": "sm", "type": "string"}, "size": {"title": "The size of the textarea.", "description": "", "tags": [], "tsType": "'sm' | 'md' | 'lg'", "id": "#appConfig/nui/BaseTextarea/size", "default": "md", "type": "string"}}, "type": "object", "default": {"contrast": "default", "rounded": "sm", "size": "md"}}, "BaseTreeSelectItem": {"id": "#appConfig/nui/BaseTreeSelectItem", "properties": {"rounded": {"title": "The radius of the tree select item.", "description": "", "tags": [], "tsType": "'none' | 'sm' | 'md' | 'lg' | 'full'", "id": "#appConfig/nui/BaseTreeSelectItem/rounded", "default": "sm", "type": "string"}}, "type": "object", "default": {"rounded": "sm"}}}, "type": "object", "default": {"BaseAccordion": {"action": "dot", "dotColor": "primary", "color": "default", "rounded": "sm"}, "BaseAvatar": {"color": "muted", "rounded": "full", "size": "sm"}, "BaseAvatarGroup": {"limit": 4, "size": "sm"}, "BaseBreadcrumb": {"color": "primary"}, "BaseButton": {"color": "default", "rounded": "md", "size": "md", "variant": "solid"}, "BaseButtonAction": {"color": "default", "rounded": "md"}, "BaseButtonClose": {"color": "default", "rounded": "full", "size": "sm"}, "BaseButtonGroup": {}, "BaseButtonIcon": {"color": "default", "rounded": "md", "size": "md"}, "BaseCard": {"color": "default", "rounded": "sm"}, "BaseDropdown": {"buttonColor": "default", "buttonSize": "md", "color": "default", "placement": "bottom-start", "rounded": "sm", "size": "md", "variant": "button"}, "BaseDropdownDivider": {}, "BaseDropdownItem": {"color": "primary", "contrast": "default", "rounded": "sm"}, "BaseHeading": {"as": "p", "lead": "normal", "size": "xl", "weight": "semibold"}, "BaseIconBox": {"color": "default", "rounded": "sm", "size": "xs", "variant": "solid"}, "BaseKbd": {"color": "default", "rounded": "sm", "size": "sm"}, "BaseLink": {}, "BaseList": {}, "BaseListItem": {}, "BaseMessage": {"color": "default", "rounded": "sm", "defaultIcons": {"muted": "akar-icons:info-fill", "muted-contrast": "akar-icons:info-fill", "default": "akar-icons:info-fill", "default-contrast": "akar-icons:info-fill", "info": "akar-icons:info-fill", "success": "carbon:checkmark-filled", "warning": "ci:warning", "danger": "ph:warning-octagon-fill", "primary": "akar-icons:info-fill"}}, "BasePagination": {"color": "primary", "rounded": "sm"}, "BaseParagraph": {"as": "p", "lead": "normal", "size": "md", "weight": "normal"}, "BasePlaceholderPage": {"imageSize": "xs"}, "BasePlaceload": {}, "BaseProgress": {"color": "primary", "contrast": "default", "rounded": "full", "size": "sm"}, "BaseProgressCircle": {"color": "primary"}, "BaseProse": {"rounded": "none"}, "BaseSnack": {"color": "default", "size": "md"}, "BaseTabs": {"color": "primary", "justify": "start", "type": "tabs"}, "BaseTabSlider": {"color": "default", "justify": "start", "rounded": "lg", "size": "md"}, "BaseTag": {"color": "default", "rounded": "lg", "size": "md", "variant": "solid"}, "BaseText": {"lead": "normal", "size": "md", "weight": "normal"}, "BaseThemeSwitch": {"disableTransitions": false}, "BaseThemeToggle": {"disableTransitions": false}, "BaseAutocomplete": {"contrast": "default", "i18n": {"empty": "Nothing found.", "pending": "Loading ..."}, "rounded": "sm", "size": "md"}, "BaseAutocompleteItem": {"rounded": "sm"}, "BaseCheckbox": {"color": "default", "rounded": "sm"}, "BaseCheckboxAnimated": {"color": "primary"}, "BaseCheckboxHeadless": {}, "BaseFullscreenDropfile": {"color": "primary"}, "BaseInput": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseInputFile": {"contrast": "default", "rounded": "sm", "size": "md", "i18n": {"empty": "No file chosen", "invalid": "Invalid file selected", "multiple": "{count} files selected"}}, "BaseInputFileHeadless": {}, "BaseInputNumber": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseInputHelpText": {"color": "default"}, "BaseListbox": {"contrast": "default", "placement": "bottom-start", "rounded": "sm", "size": "md"}, "BaseListboxItem": {}, "BaseRadio": {"color": "default"}, "BaseRadioHeadless": {}, "BaseSelect": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseSwitchBall": {"color": "primary"}, "BaseSwitchThin": {"color": "primary"}, "BaseTextarea": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseTreeSelectItem": {"rounded": "sm"}}}}, "type": "object", "default": {"icon": {"size": {}, "class": "", "attrs": {"aria-hidden": true}, "mode": "css", "aliases": {}, "cssSelectorPrefix": "i-", "cssLayer": {}, "cssWherePseudo": true, "collections": null, "customCollections": null, "iconifyApiEndpoint": "https://api.iconify.design", "fallbackToApi": true, "localApiEndpoint": "/api/_nuxt_icon", "fetchTimeout": 1500}, "tairo": {"iconnav": {"circularMenu": {"enabled": true}, "toolbar": {"enabled": true, "showTitle": true}, "navigation": {"enabled": true, "logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}, "footer": {"enabled": true, "logoSeparator": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}, "logo": {"component": "TairoLogoText", "props": {}}, "copyright": {"name": "", "to": "", "since": ""}}}, "sidebar": {"circularMenu": {"enabled": true}, "toolbar": {"enabled": true, "showTitle": true, "showNavBurger": false}, "navigation": {"enabled": true, "startOpen": true, "logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}}, "title": "<PERSON><PERSON>", "error": {"logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}}, "nui": {"BaseAccordion": {"action": "dot", "dotColor": "primary", "color": "default", "rounded": "sm"}, "BaseAvatar": {"color": "muted", "rounded": "full", "size": "sm"}, "BaseAvatarGroup": {"limit": 4, "size": "sm"}, "BaseBreadcrumb": {"color": "primary"}, "BaseButton": {"color": "default", "rounded": "md", "size": "md", "variant": "solid"}, "BaseButtonAction": {"color": "default", "rounded": "md"}, "BaseButtonClose": {"color": "default", "rounded": "full", "size": "sm"}, "BaseButtonGroup": {}, "BaseButtonIcon": {"color": "default", "rounded": "md", "size": "md"}, "BaseCard": {"color": "default", "rounded": "sm"}, "BaseDropdown": {"buttonColor": "default", "buttonSize": "md", "color": "default", "placement": "bottom-start", "rounded": "sm", "size": "md", "variant": "button"}, "BaseDropdownDivider": {}, "BaseDropdownItem": {"color": "primary", "contrast": "default", "rounded": "sm"}, "BaseHeading": {"as": "p", "lead": "normal", "size": "xl", "weight": "semibold"}, "BaseIconBox": {"color": "default", "rounded": "sm", "size": "xs", "variant": "solid"}, "BaseKbd": {"color": "default", "rounded": "sm", "size": "sm"}, "BaseLink": {}, "BaseList": {}, "BaseListItem": {}, "BaseMessage": {"color": "default", "rounded": "sm", "defaultIcons": {"muted": "akar-icons:info-fill", "muted-contrast": "akar-icons:info-fill", "default": "akar-icons:info-fill", "default-contrast": "akar-icons:info-fill", "info": "akar-icons:info-fill", "success": "carbon:checkmark-filled", "warning": "ci:warning", "danger": "ph:warning-octagon-fill", "primary": "akar-icons:info-fill"}}, "BasePagination": {"color": "primary", "rounded": "sm"}, "BaseParagraph": {"as": "p", "lead": "normal", "size": "md", "weight": "normal"}, "BasePlaceholderPage": {"imageSize": "xs"}, "BasePlaceload": {}, "BaseProgress": {"color": "primary", "contrast": "default", "rounded": "full", "size": "sm"}, "BaseProgressCircle": {"color": "primary"}, "BaseProse": {"rounded": "none"}, "BaseSnack": {"color": "default", "size": "md"}, "BaseTabs": {"color": "primary", "justify": "start", "type": "tabs"}, "BaseTabSlider": {"color": "default", "justify": "start", "rounded": "lg", "size": "md"}, "BaseTag": {"color": "default", "rounded": "lg", "size": "md", "variant": "solid"}, "BaseText": {"lead": "normal", "size": "md", "weight": "normal"}, "BaseThemeSwitch": {"disableTransitions": false}, "BaseThemeToggle": {"disableTransitions": false}, "BaseAutocomplete": {"contrast": "default", "i18n": {"empty": "Nothing found.", "pending": "Loading ..."}, "rounded": "sm", "size": "md"}, "BaseAutocompleteItem": {"rounded": "sm"}, "BaseCheckbox": {"color": "default", "rounded": "sm"}, "BaseCheckboxAnimated": {"color": "primary"}, "BaseCheckboxHeadless": {}, "BaseFullscreenDropfile": {"color": "primary"}, "BaseInput": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseInputFile": {"contrast": "default", "rounded": "sm", "size": "md", "i18n": {"empty": "No file chosen", "invalid": "Invalid file selected", "multiple": "{count} files selected"}}, "BaseInputFileHeadless": {}, "BaseInputNumber": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseInputHelpText": {"color": "default"}, "BaseListbox": {"contrast": "default", "placement": "bottom-start", "rounded": "sm", "size": "md"}, "BaseListboxItem": {}, "BaseRadio": {"color": "default"}, "BaseRadioHeadless": {}, "BaseSelect": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseSwitchBall": {"color": "primary"}, "BaseSwitchThin": {"color": "primary"}, "BaseTextarea": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseTreeSelectItem": {"rounded": "sm"}}}}}, "type": "object", "default": {"appConfig": {"icon": {"size": {}, "class": "", "attrs": {"aria-hidden": true}, "mode": "css", "aliases": {}, "cssSelectorPrefix": "i-", "cssLayer": {}, "cssWherePseudo": true, "collections": null, "customCollections": null, "iconifyApiEndpoint": "https://api.iconify.design", "fallbackToApi": true, "localApiEndpoint": "/api/_nuxt_icon", "fetchTimeout": 1500}, "tairo": {"iconnav": {"circularMenu": {"enabled": true}, "toolbar": {"enabled": true, "showTitle": true}, "navigation": {"enabled": true, "logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}, "footer": {"enabled": true, "logoSeparator": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}, "logo": {"component": "TairoLogoText", "props": {}}, "copyright": {"name": "", "to": "", "since": ""}}}, "sidebar": {"circularMenu": {"enabled": true}, "toolbar": {"enabled": true, "showTitle": true, "showNavBurger": false}, "navigation": {"enabled": true, "startOpen": true, "logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}}, "title": "<PERSON><PERSON>", "error": {"logo": {"component": "<PERSON><PERSON><PERSON><PERSON>", "props": {}}}}, "nui": {"BaseAccordion": {"action": "dot", "dotColor": "primary", "color": "default", "rounded": "sm"}, "BaseAvatar": {"color": "muted", "rounded": "full", "size": "sm"}, "BaseAvatarGroup": {"limit": 4, "size": "sm"}, "BaseBreadcrumb": {"color": "primary"}, "BaseButton": {"color": "default", "rounded": "md", "size": "md", "variant": "solid"}, "BaseButtonAction": {"color": "default", "rounded": "md"}, "BaseButtonClose": {"color": "default", "rounded": "full", "size": "sm"}, "BaseButtonGroup": {}, "BaseButtonIcon": {"color": "default", "rounded": "md", "size": "md"}, "BaseCard": {"color": "default", "rounded": "sm"}, "BaseDropdown": {"buttonColor": "default", "buttonSize": "md", "color": "default", "placement": "bottom-start", "rounded": "sm", "size": "md", "variant": "button"}, "BaseDropdownDivider": {}, "BaseDropdownItem": {"color": "primary", "contrast": "default", "rounded": "sm"}, "BaseHeading": {"as": "p", "lead": "normal", "size": "xl", "weight": "semibold"}, "BaseIconBox": {"color": "default", "rounded": "sm", "size": "xs", "variant": "solid"}, "BaseKbd": {"color": "default", "rounded": "sm", "size": "sm"}, "BaseLink": {}, "BaseList": {}, "BaseListItem": {}, "BaseMessage": {"color": "default", "rounded": "sm", "defaultIcons": {"muted": "akar-icons:info-fill", "muted-contrast": "akar-icons:info-fill", "default": "akar-icons:info-fill", "default-contrast": "akar-icons:info-fill", "info": "akar-icons:info-fill", "success": "carbon:checkmark-filled", "warning": "ci:warning", "danger": "ph:warning-octagon-fill", "primary": "akar-icons:info-fill"}}, "BasePagination": {"color": "primary", "rounded": "sm"}, "BaseParagraph": {"as": "p", "lead": "normal", "size": "md", "weight": "normal"}, "BasePlaceholderPage": {"imageSize": "xs"}, "BasePlaceload": {}, "BaseProgress": {"color": "primary", "contrast": "default", "rounded": "full", "size": "sm"}, "BaseProgressCircle": {"color": "primary"}, "BaseProse": {"rounded": "none"}, "BaseSnack": {"color": "default", "size": "md"}, "BaseTabs": {"color": "primary", "justify": "start", "type": "tabs"}, "BaseTabSlider": {"color": "default", "justify": "start", "rounded": "lg", "size": "md"}, "BaseTag": {"color": "default", "rounded": "lg", "size": "md", "variant": "solid"}, "BaseText": {"lead": "normal", "size": "md", "weight": "normal"}, "BaseThemeSwitch": {"disableTransitions": false}, "BaseThemeToggle": {"disableTransitions": false}, "BaseAutocomplete": {"contrast": "default", "i18n": {"empty": "Nothing found.", "pending": "Loading ..."}, "rounded": "sm", "size": "md"}, "BaseAutocompleteItem": {"rounded": "sm"}, "BaseCheckbox": {"color": "default", "rounded": "sm"}, "BaseCheckboxAnimated": {"color": "primary"}, "BaseCheckboxHeadless": {}, "BaseFullscreenDropfile": {"color": "primary"}, "BaseInput": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseInputFile": {"contrast": "default", "rounded": "sm", "size": "md", "i18n": {"empty": "No file chosen", "invalid": "Invalid file selected", "multiple": "{count} files selected"}}, "BaseInputFileHeadless": {}, "BaseInputNumber": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseInputHelpText": {"color": "default"}, "BaseListbox": {"contrast": "default", "placement": "bottom-start", "rounded": "sm", "size": "md"}, "BaseListboxItem": {}, "BaseRadio": {"color": "default"}, "BaseRadioHeadless": {}, "BaseSelect": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseSwitchBall": {"color": "primary"}, "BaseSwitchThin": {"color": "primary"}, "BaseTextarea": {"contrast": "default", "rounded": "sm", "size": "md"}, "BaseTreeSelectItem": {"rounded": "sm"}}}}}