"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const income_controller_js_1 = require("../controllers/budget/income.controller.js");
const expense_controller_js_1 = require("../controllers/budget/expense.controller.js");
const asset_controller_js_1 = require("../controllers/budget/asset.controller.js");
const budget_controller_js_1 = require("../controllers/budget/budget.controller.js");
const transaction_controller_js_1 = require("../controllers/budget/transaction.controller.js");
const goal_controller_js_1 = require("../controllers/budget/goal.controller.js");
const router = express_1.default.Router();
// Apply authentication middleware to all routes
router.use(route_helpers_js_1.auth);
// Income routes
router.post("/incomes", (0, route_helpers_js_1.wrapController)(income_controller_js_1.createIncome));
router.get("/incomes", (0, route_helpers_js_1.wrapController)(income_controller_js_1.getIncomes));
router.put("/incomes/:id", (0, route_helpers_js_1.wrapController)(income_controller_js_1.updateIncome));
router.delete("/incomes/:id", (0, route_helpers_js_1.wrapController)(income_controller_js_1.deleteIncome));
router.patch("/incomes/:id/paid", (0, route_helpers_js_1.wrapController)(income_controller_js_1.markIncomePaid));
router.patch("/incomes/:id/unpaid", (0, route_helpers_js_1.wrapController)(income_controller_js_1.markIncomeUnpaid));
// Expense routes
router.post("/expenses", (0, route_helpers_js_1.wrapController)(expense_controller_js_1.createExpense));
router.get("/expenses", (0, route_helpers_js_1.wrapController)(expense_controller_js_1.getExpenses));
router.put("/expenses/:id", (0, route_helpers_js_1.wrapController)(expense_controller_js_1.updateExpense));
router.delete("/expenses/:id", (0, route_helpers_js_1.wrapController)(expense_controller_js_1.deleteExpense));
router.patch("/expenses/:id/paid", (0, route_helpers_js_1.wrapController)(expense_controller_js_1.markExpensePaid));
router.patch("/expenses/:id/unpaid", (0, route_helpers_js_1.wrapController)(expense_controller_js_1.markExpenseUnpaid));
// Asset routes
router.post("/assets", (0, route_helpers_js_1.wrapController)(asset_controller_js_1.createAsset));
router.get("/assets", (0, route_helpers_js_1.wrapController)(asset_controller_js_1.getAssets));
router.put("/assets/:id", (0, route_helpers_js_1.wrapController)(asset_controller_js_1.updateAsset));
router.delete("/assets/:id", (0, route_helpers_js_1.wrapController)(asset_controller_js_1.deleteAsset));
// Budget routes
router.post("/budgets", (0, route_helpers_js_1.wrapController)(budget_controller_js_1.createBudget));
router.get("/budgets", (0, route_helpers_js_1.wrapController)(budget_controller_js_1.getBudgets));
router.get("/budgets/:id", (0, route_helpers_js_1.wrapController)(budget_controller_js_1.getBudgetById));
router.put("/budgets/:id", (0, route_helpers_js_1.wrapController)(budget_controller_js_1.updateBudget));
router.delete("/budgets/:id", (0, route_helpers_js_1.wrapController)(budget_controller_js_1.deleteBudget));
// Transaction routes
router.post("/transactions", (0, route_helpers_js_1.wrapController)(transaction_controller_js_1.createTransaction));
router.get("/transactions", (0, route_helpers_js_1.wrapController)(transaction_controller_js_1.getTransactions));
router.get("/transactions/:id", (0, route_helpers_js_1.wrapController)(transaction_controller_js_1.getTransactionById));
router.put("/transactions/:id", (0, route_helpers_js_1.wrapController)(transaction_controller_js_1.updateTransaction));
router.delete("/transactions/:id", (0, route_helpers_js_1.wrapController)(transaction_controller_js_1.deleteTransaction));
// Budget Goal routes
router.post("/goals", (0, route_helpers_js_1.wrapController)(goal_controller_js_1.createBudgetGoal));
router.get("/goals", (0, route_helpers_js_1.wrapController)(goal_controller_js_1.getBudgetGoals));
router.get("/goals/:id", (0, route_helpers_js_1.wrapController)(goal_controller_js_1.getBudgetGoalById));
router.put("/goals/:id", (0, route_helpers_js_1.wrapController)(goal_controller_js_1.updateBudgetGoal));
router.delete("/goals/:id", (0, route_helpers_js_1.wrapController)(goal_controller_js_1.deleteBudgetGoal));
router.patch("/goals/:id/progress", (0, route_helpers_js_1.wrapController)(goal_controller_js_1.updateBudgetGoalProgress));
exports.default = router;
//# sourceMappingURL=budget.js.map