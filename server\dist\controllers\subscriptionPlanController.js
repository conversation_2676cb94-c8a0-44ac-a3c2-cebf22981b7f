"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.subscriptionPlanController = void 0;
const client_1 = require("@prisma/client");
const http_errors_1 = __importDefault(require("http-errors"));
const prisma = new client_1.PrismaClient();
exports.subscriptionPlanController = {
    // Get all subscription plans
    getAllPlans: async (req, res, next) => {
        try {
            const plans = await prisma.subscriptionPlan.findMany({
                orderBy: {
                    monthlyPrice: "asc",
                },
            });
            res.status(200).json(plans);
        }
        catch (error) {
            console.error("Error fetching subscription plans:", error);
            next((0, http_errors_1.default)(500, "Failed to fetch subscription plans"));
        }
    },
    // Get a specific subscription plan by ID
    getPlanById: async (req, res, next) => {
        const { id } = req.params;
        try {
            const plan = await prisma.subscriptionPlan.findUnique({
                where: { id: Number(id) },
            });
            if (!plan) {
                next((0, http_errors_1.default)(404, "Subscription plan not found"));
                return;
            }
            res.status(200).json(plan);
        }
        catch (error) {
            console.error("Error fetching subscription plan:", error);
            next((0, http_errors_1.default)(500, "Failed to fetch subscription plan"));
        }
    },
    // Create a new subscription plan
    createPlan: async (req, res, next) => {
        console.log("createPlan called with req.user:", req.user);
        // Check if user is SUPERADMIN
        if (!req.user?.roles.includes("SUPERADMIN")) {
            console.log("User is not SUPERADMIN, access denied");
            next((0, http_errors_1.default)(403, "Only SUPERADMIN can create subscription plans"));
            return;
        }
        console.log("User is SUPERADMIN, proceeding with plan creation");
        const { name, description, type, monthlyPrice, yearlyPrice, maxUsers, additionalUserFee, features, modules, } = req.body;
        // Validate required fields
        if (!name ||
            !description ||
            !type ||
            monthlyPrice === undefined ||
            yearlyPrice === undefined ||
            !maxUsers ||
            additionalUserFee === undefined ||
            !features ||
            !modules) {
            next((0, http_errors_1.default)(400, "Missing required fields"));
            return;
        }
        try {
            // Check if a plan with the same type already exists
            const existingPlan = await prisma.subscriptionPlan.findFirst({
                where: { type: type },
            });
            if (existingPlan) {
                next((0, http_errors_1.default)(409, `A plan with type ${type} already exists. Please use a different type.`));
                return;
            }
            // Create the plan
            const plan = await prisma.subscriptionPlan.create({
                data: {
                    name,
                    description,
                    type: type,
                    monthlyPrice: Number(monthlyPrice),
                    yearlyPrice: Number(yearlyPrice),
                    maxUsers: Number(maxUsers),
                    additionalUserFee: Number(additionalUserFee),
                    features,
                    modules,
                },
            });
            res.status(201).json(plan);
        }
        catch (error) {
            console.error("Error creating subscription plan:", error);
            next((0, http_errors_1.default)(500, "Failed to create subscription plan"));
        }
    },
    // Update a subscription plan
    updatePlan: async (req, res, next) => {
        // Check if user is SUPERADMIN
        if (!req.user?.roles.includes("SUPERADMIN")) {
            next((0, http_errors_1.default)(403, "Only SUPERADMIN can update subscription plans"));
            return;
        }
        const { id } = req.params;
        const { name, description, type, monthlyPrice, yearlyPrice, maxUsers, additionalUserFee, features, modules, } = req.body;
        // Validate required fields
        if (!name ||
            !description ||
            !type ||
            monthlyPrice === undefined ||
            yearlyPrice === undefined ||
            !maxUsers ||
            additionalUserFee === undefined ||
            !features ||
            !modules) {
            next((0, http_errors_1.default)(400, "Missing required fields"));
            return;
        }
        try {
            // Check if the plan exists
            const existingPlan = await prisma.subscriptionPlan.findUnique({
                where: { id: Number(id) },
            });
            if (!existingPlan) {
                next((0, http_errors_1.default)(404, "Subscription plan not found"));
                return;
            }
            // Check if another plan with the same type exists (if type is being changed)
            if (type !== existingPlan.type) {
                const duplicateType = await prisma.subscriptionPlan.findFirst({
                    where: {
                        type: type,
                        id: { not: Number(id) },
                    },
                });
                if (duplicateType) {
                    next((0, http_errors_1.default)(409, `Another plan with type ${type} already exists. Please use a different type.`));
                    return;
                }
            }
            // Update the plan
            const updatedPlan = await prisma.subscriptionPlan.update({
                where: { id: Number(id) },
                data: {
                    name,
                    description,
                    type: type,
                    monthlyPrice: Number(monthlyPrice),
                    yearlyPrice: Number(yearlyPrice),
                    maxUsers: Number(maxUsers),
                    additionalUserFee: Number(additionalUserFee),
                    features,
                    modules,
                },
            });
            res.status(200).json(updatedPlan);
        }
        catch (error) {
            console.error("Error updating subscription plan:", error);
            next((0, http_errors_1.default)(500, "Failed to update subscription plan"));
        }
    },
    // Delete a subscription plan
    deletePlan: async (req, res, next) => {
        // Check if user is SUPERADMIN
        if (!req.user?.roles.includes("SUPERADMIN")) {
            next((0, http_errors_1.default)(403, "Only SUPERADMIN can delete subscription plans"));
            return;
        }
        const { id } = req.params;
        try {
            // Check if the plan exists
            const existingPlan = await prisma.subscriptionPlan.findUnique({
                where: { id: Number(id) },
            });
            if (!existingPlan) {
                next((0, http_errors_1.default)(404, "Subscription plan not found"));
                return;
            }
            // Check if the plan is in use by any subscriptions
            const subscriptionsUsingPlan = await prisma.subscription.count({
                where: { planId: Number(id) },
            });
            if (subscriptionsUsingPlan > 0) {
                next((0, http_errors_1.default)(409, "Cannot delete plan because it is currently in use by active subscriptions"));
                return;
            }
            // Delete the plan
            await prisma.subscriptionPlan.delete({
                where: { id: Number(id) },
            });
            res
                .status(200)
                .json({ message: "Subscription plan deleted successfully" });
        }
        catch (error) {
            console.error("Error deleting subscription plan:", error);
            next((0, http_errors_1.default)(500, "Failed to delete subscription plan"));
        }
    },
};
exports.default = exports.subscriptionPlanController;
//# sourceMappingURL=subscriptionPlanController.js.map