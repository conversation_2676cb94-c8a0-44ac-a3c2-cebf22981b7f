"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWaitlistEmails = exports.confirmWaitlistEmail = exports.registerWaitlistEmail = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const uuid_1 = require("uuid");
const sendEmail_js_1 = require("../../utils/sendEmail.js");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
// Using centralized prisma instance from lib/prisma.js
const CLIENT_URL = process.env.FRONTEND_URL || "http://localhost:3000";
// Token expiration time (24 hours in milliseconds)
const TOKEN_EXPIRATION = 24 * 60 * 60 * 1000;
/**
 * Register a new email for the waitlist
 */
const registerWaitlistEmail = async (req, res) => {
    console.log("=== WAITLIST REGISTRATION DEBUG ===");
    console.log("Request received at /v1/waitlist/register");
    console.log("Request method:", req.method);
    console.log("Request headers:", req.headers);
    console.log("Request body:", req.body);
    try {
        const { email } = req.body;
        console.log("Extracted email from request body:", email);
        if (!email) {
            console.log("Email is missing in request body");
            return res.status(400).json({ message: "Email is required" });
        }
        // Check if email is valid
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            console.log("Invalid email format:", email);
            return res.status(400).json({ message: "Invalid email format" });
        }
        console.log("Email format is valid");
        // Check if email already exists and is confirmed
        console.log("Checking if email already exists in database");
        const existingEmail = await prisma_js_1.prisma.waitlistEmail.findUnique({
            where: { email },
        });
        console.log("Database query result:", existingEmail);
        if (existingEmail && existingEmail.isConfirmed) {
            console.log("Email already exists and is confirmed");
            return res.status(400).json({
                message: "This email is already registered in our waitlist",
            });
        }
        // Generate a unique confirmation token
        console.log("Generating confirmation token");
        const confirmationToken = (0, uuid_1.v4)();
        const tokenExpiresAt = new Date(Date.now() + TOKEN_EXPIRATION);
        console.log("Token expiration set to:", tokenExpiresAt);
        // If email exists but is not confirmed, update the token
        if (existingEmail) {
            console.log("Email exists but is not confirmed, updating token");
            await prisma_js_1.prisma.waitlistEmail.update({
                where: { id: existingEmail.id },
                data: {
                    confirmationToken,
                    tokenExpiresAt,
                    updatedAt: new Date(),
                },
            });
            console.log("Token updated successfully");
        }
        else {
            // Create a new waitlist entry
            console.log("Creating new waitlist entry");
            try {
                const newEntry = await prisma_js_1.prisma.waitlistEmail.create({
                    data: {
                        email,
                        confirmationToken,
                        tokenExpiresAt,
                    },
                });
                console.log("New waitlist entry created:", newEntry.id);
            }
            catch (dbError) {
                console.error("Database error creating waitlist entry:", dbError);
                throw dbError;
            }
        }
        // Generate confirmation link
        const confirmationLink = `${CLIENT_URL}/waitlist/confirm/${confirmationToken}`;
        console.log("Confirmation link generated:", confirmationLink);
        // Send confirmation email
        const emailSubject = "Confirm your CoManager Beta Waitlist Registration";
        console.log("Preparing to send confirmation email to:", email);
        const emailBody = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="color-scheme" content="light">
        <meta name="supported-color-schemes" content="light">
        <title>Confirm Your CoManager Beta Waitlist Registration</title>
        <!--[if mso]>
        <noscript>
          <xml>
            <o:OfficeDocumentSettings>
              <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
          </xml>
        </noscript>
        <![endif]-->
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            -moz-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
          }
          .logo-container {
            display: inline-block;
            margin-bottom: 20px;
            white-space: nowrap;
            line-height: 1;
          }
          .logo-text {
            font-size: 22px;
            font-weight: bold !important;
            font-family: Arial, sans-serif;
            color: #333 !important;
            letter-spacing: 0.05em;
            display: inline-block;
            vertical-align: middle;
            line-height: 1;
          }
          .logo-text.bold {
            font-weight: 900 !important;
          }
          .logo-text-firstletter {
            font-size: 24px;
            font-weight: bold !important;
            font-family: Arial, sans-serif;
            color: #333 !important;
            display: inline-block;
            vertical-align: middle;
            line-height: 1;
          }
          .logo-image {
            width: 20px;
            height: 22px;
            vertical-align: middle;
            margin-bottom: 4px;
            display: inline-block;
            position: relative;
          }
          .content {
            padding: 30px 20px;
            text-align: center;
          }
          .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #00a0c0 !important;
            color: #ffffff !important;
            text-decoration: none !important;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
            transition: background-color 0.3s;
            border: 1px solid #00a0c0;
            font-family: Arial, sans-serif;
            font-size: 16px;
            text-align: center;
            mso-line-height-rule: exactly;
            line-height: 1.5;
            -webkit-text-size-adjust: none;
          }
          .button:hover {
            background-color: #00b8db !important;
          }
          .footer {
            text-align: center;
            padding: 20px;
            color: #888;
            font-size: 0.9em;
            border-top: 1px solid #eee;
          }
          .expiry {
            font-size: 0.85em;
            color: #888;
            margin-top: 15px;
          }
          h1 {
            color: #333;
            margin-bottom: 20px;
          }
          p {
            margin-bottom: 15px;
          }
        </style>
      </head>
      <body style="margin:0;padding:0;background-color:#f9f9f9;font-family:Arial,sans-serif;">
        <!-- Preheader text (hidden) -->
        <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;">
          Please confirm your registration for the CoManager Beta Waitlist.
        </div>
        <!-- Main table structure -->
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center" style="padding:40px 0;">
              <div class="container">
          <div class="header">
            <div class="logo-container">
              <span class="logo-text-firstletter bold">C</span>
              <img src="cid:<EMAIL>" alt="O" class="logo-image">
              <span class="logo-text bold">MANAGER</span>
            </div>
            <h1>Confirm Your Beta Waitlist Registration</h1>
          </div>
          <div class="content">
            <p>Thank you for your interest in CoManager's beta program!</p>
            <p>We're excited to have you join our waitlist. To confirm your registration, please click the button below:</p>
            <!-- MSO conditional code for Outlook -->
            <!--[if mso]>
            <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${confirmationLink}" style="height:40px;v-text-anchor:middle;width:200px;" arcsize="10%" stroke="f" fillcolor="#00b8db">
              <w:anchorlock/>
              <center>
                <![endif]-->
                <a href="${confirmationLink}" class="button" style="background-color:#00b8db;border-radius:6px;color:#ffffff;display:inline-block;font-family:Arial, sans-serif;font-size:16px;font-weight:bold;line-height:40px;text-align:center;text-decoration:none;width:200px;-webkit-text-size-adjust:none;">Confirm Registration</a>
            <!--[if mso]>
              </center>
            </v:roundrect>
            <![endif]-->
            <p class="expiry">This confirmation link will expire in 24 hours.</p>
            <p>If you didn't request to join our beta waitlist, you can safely ignore this email.</p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} CoManager. All rights reserved.</p>
          </div>
        </div>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `;
        let emailSent = false;
        try {
            // Use the logo from the server/public directory
            const logoPath = path.join(process.cwd(), "public/logo.png");
            console.log("Using logo path:", logoPath);
            // Check if the file exists
            if (fs.existsSync(logoPath)) {
                console.log("Logo file exists at path:", logoPath);
                // Send email with logo attachment
                await (0, sendEmail_js_1.sendEmail)(email, emailSubject, null, emailBody, [
                    {
                        filename: "logo.png",
                        path: logoPath,
                        cid: "<EMAIL>", // CID referenced in the HTML
                    },
                ]);
                console.log("Confirmation email sent successfully with logo attachment");
                emailSent = true;
            }
            else {
                console.error("No logo file found in any of the checked paths");
                // Send email without logo attachment as fallback
                await (0, sendEmail_js_1.sendEmail)(email, emailSubject, null, emailBody);
                console.log("Confirmation email sent without logo attachment");
                emailSent = true;
            }
        }
        catch (emailError) {
            console.error("Error sending confirmation email:", emailError);
            console.error("Error details:", emailError instanceof Error ? emailError.message : String(emailError));
            // Don't set emailSent to true since it failed
        }
        if (emailSent) {
            // If email was sent successfully, return success response
            console.log("Sending success response");
            const response = {
                message: "Registration successful. Please check your inbox (and spam folder) for a confirmation email.",
                status: "success",
                registered: true,
            };
            console.log("Response data:", response);
            return res.status(200).json(response);
        }
        else {
            // If email sending failed, return error response
            console.log("Sending error response due to email failure");
            const errorResponse = {
                message: "Registration was processed, but we couldn't send a confirmation email. Please contact support.",
                status: "error",
                registered: true,
                emailSent: false,
            };
            console.log("Error response data:", errorResponse);
            return res.status(500).json(errorResponse);
        }
    }
    catch (error) {
        console.error("Error registering waitlist email:", error);
        console.error("Error stack:", error instanceof Error ? error.stack : "No stack trace");
        return res.status(500).json({
            message: "Server error",
            error: error instanceof Error ? error.message : String(error),
        });
    }
    finally {
        console.log("=== END WAITLIST REGISTRATION DEBUG ===");
    }
};
exports.registerWaitlistEmail = registerWaitlistEmail;
/**
 * Confirm a waitlist email registration
 */
const confirmWaitlistEmail = async (req, res) => {
    try {
        const { token } = req.params;
        if (!token) {
            return res.status(400).json({ message: "Token is required" });
        }
        // Find the waitlist entry by token
        const waitlistEntry = await prisma_js_1.prisma.waitlistEmail.findUnique({
            where: { confirmationToken: token },
        });
        if (!waitlistEntry) {
            return res.status(404).json({ message: "Invalid confirmation token" });
        }
        // Check if token is expired
        if (waitlistEntry.tokenExpiresAt < new Date()) {
            return res
                .status(400)
                .json({ message: "Confirmation token has expired" });
        }
        // Check if already confirmed
        if (waitlistEntry.isConfirmed) {
            return res.status(400).json({ message: "Email is already confirmed" });
        }
        // Update the waitlist entry to confirmed
        await prisma_js_1.prisma.waitlistEmail.update({
            where: { id: waitlistEntry.id },
            data: {
                isConfirmed: true,
                confirmedAt: new Date(),
            },
        });
        // Redirect to success page (handled by frontend)
        return res.status(200).json({
            message: "Email confirmed successfully",
            email: waitlistEntry.email,
        });
    }
    catch (error) {
        console.error("Error confirming waitlist email:", error);
        return res.status(500).json({ message: "Server error", error });
    }
};
exports.confirmWaitlistEmail = confirmWaitlistEmail;
/**
 * Get all confirmed waitlist emails (admin only)
 */
const getWaitlistEmails = async (_req, res) => {
    try {
        // Check if user is admin (implement your own auth check)
        // if (!req.user?.isAdmin) {
        //   return res.status(403).json({ message: "Unauthorized" });
        // }
        const waitlistEmails = await prisma_js_1.prisma.waitlistEmail.findMany({
            where: { isConfirmed: true },
            orderBy: { confirmedAt: "desc" },
        });
        return res.status(200).json(waitlistEmails);
    }
    catch (error) {
        console.error("Error getting waitlist emails:", error);
        return res.status(500).json({ message: "Server error", error });
    }
};
exports.getWaitlistEmails = getWaitlistEmails;
//# sourceMappingURL=waitlist.controller.js.map