"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkLocationCompliance = exports.getProjectLocationById = exports.getProjectLocations = void 0;
// Using centralized prisma instance from lib/prisma.js
// Get all project locations
const getProjectLocations = async (req, res) => {
    try {
        // In a real implementation, this would fetch from the database
        // For now, we'll return dummy data for projects in Tallinn, Estonia
        const projectLocations = [
            {
                id: '1',
                projectId: '1',
                name: 'Office Building Construction',
                latitude: 59.436962,
                longitude: 24.753574,
                radius: 100, // meters
                address: 'Narva mnt 7, 10117 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '2',
                projectId: '2',
                name: 'Residential Complex',
                latitude: 59.432962,
                longitude: 24.758574,
                radius: 150, // meters
                address: 'Liivalaia 33, 10118 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '3',
                projectId: '3',
                name: 'Highway Bridge',
                latitude: 59.438962,
                longitude: 24.763574,
                radius: 200, // meters
                address: 'Pärnu mnt 139, 11317 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '4',
                projectId: '4',
                name: 'Shopping Mall',
                latitude: 59.430962,
                longitude: 24.748574,
                radius: 120, // meters
                address: 'Estonia pst 9, 10143 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '5',
                projectId: '5',
                name: 'Hospital Renovation',
                latitude: 59.434962,
                longitude: 24.751574,
                radius: 80, // meters
                address: 'Ravi 18, 10138 Tallinn, Estonia',
                isActive: true
            }
        ];
        return res.status(200).json(projectLocations);
    }
    catch (error) {
        console.error('Error fetching project locations:', error);
        return res.status(500).json({ error: 'Failed to fetch project locations' });
    }
};
exports.getProjectLocations = getProjectLocations;
// Get project location by ID
const getProjectLocationById = async (req, res) => {
    try {
        const { id } = req.params;
        // In a real implementation, this would fetch from the database
        // For now, we'll return dummy data for projects in Tallinn, Estonia
        const projectLocations = [
            {
                id: '1',
                projectId: '1',
                name: 'Office Building Construction',
                latitude: 59.436962,
                longitude: 24.753574,
                radius: 100, // meters
                address: 'Narva mnt 7, 10117 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '2',
                projectId: '2',
                name: 'Residential Complex',
                latitude: 59.432962,
                longitude: 24.758574,
                radius: 150, // meters
                address: 'Liivalaia 33, 10118 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '3',
                projectId: '3',
                name: 'Highway Bridge',
                latitude: 59.438962,
                longitude: 24.763574,
                radius: 200, // meters
                address: 'Pärnu mnt 139, 11317 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '4',
                projectId: '4',
                name: 'Shopping Mall',
                latitude: 59.430962,
                longitude: 24.748574,
                radius: 120, // meters
                address: 'Estonia pst 9, 10143 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '5',
                projectId: '5',
                name: 'Hospital Renovation',
                latitude: 59.434962,
                longitude: 24.751574,
                radius: 80, // meters
                address: 'Ravi 18, 10138 Tallinn, Estonia',
                isActive: true
            }
        ];
        const projectLocation = projectLocations.find(location => location.id === id || location.projectId === id);
        if (!projectLocation) {
            return res.status(404).json({ error: 'Project location not found' });
        }
        return res.status(200).json(projectLocation);
    }
    catch (error) {
        console.error('Error fetching project location:', error);
        return res.status(500).json({ error: 'Failed to fetch project location' });
    }
};
exports.getProjectLocationById = getProjectLocationById;
// Check if a user's location is within a project's radius
const checkLocationCompliance = async (req, res) => {
    try {
        const { projectId, latitude, longitude } = req.body;
        if (!projectId || !latitude || !longitude) {
            return res.status(400).json({ error: 'Missing required parameters' });
        }
        // In a real implementation, this would fetch from the database
        // For now, we'll use dummy data
        const projectLocations = [
            {
                id: '1',
                projectId: '1',
                name: 'Office Building Construction',
                latitude: 59.436962,
                longitude: 24.753574,
                radius: 100, // meters
                address: 'Narva mnt 7, 10117 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '2',
                projectId: '2',
                name: 'Residential Complex',
                latitude: 59.432962,
                longitude: 24.758574,
                radius: 150, // meters
                address: 'Liivalaia 33, 10118 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '3',
                projectId: '3',
                name: 'Highway Bridge',
                latitude: 59.438962,
                longitude: 24.763574,
                radius: 200, // meters
                address: 'Pärnu mnt 139, 11317 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '4',
                projectId: '4',
                name: 'Shopping Mall',
                latitude: 59.430962,
                longitude: 24.748574,
                radius: 120, // meters
                address: 'Estonia pst 9, 10143 Tallinn, Estonia',
                isActive: true
            },
            {
                id: '5',
                projectId: '5',
                name: 'Hospital Renovation',
                latitude: 59.434962,
                longitude: 24.751574,
                radius: 80, // meters
                address: 'Ravi 18, 10138 Tallinn, Estonia',
                isActive: true
            }
        ];
        const projectLocation = projectLocations.find(location => location.projectId === projectId);
        if (!projectLocation) {
            return res.status(404).json({ error: 'Project location not found' });
        }
        // Calculate distance between user and project location
        const distance = calculateDistance(latitude, longitude, projectLocation.latitude, projectLocation.longitude);
        // Check if user is within the project's radius
        const isCompliant = distance <= projectLocation.radius;
        return res.status(200).json({
            isCompliant,
            distance,
            projectLocation
        });
    }
    catch (error) {
        console.error('Error checking location compliance:', error);
        return res.status(500).json({ error: 'Failed to check location compliance' });
    }
};
exports.checkLocationCompliance = checkLocationCompliance;
// Helper function to calculate distance between two points using the Haversine formula
function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
        Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in meters
}
//# sourceMappingURL=project-locations.controller.js.map