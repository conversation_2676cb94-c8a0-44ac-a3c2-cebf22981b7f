"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getActivityLogStats = exports.createActivityLog = exports.getActivityLogById = exports.getActivityLogs = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// Get all activity logs with filtering and pagination
const getActivityLogs = async (req, res) => {
    try {
        const { page = 1, limit = 10, search, type, module, userId, status, severity, startDate, endDate, sortBy = "timestamp", sortOrder = "desc", } = req.query;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;
        // Build where clause
        const where = {};
        if (search) {
            where.OR = [
                { title: { contains: search, mode: "insensitive" } },
                { description: { contains: search, mode: "insensitive" } },
                { action: { contains: search, mode: "insensitive" } },
            ];
        }
        if (type && type !== "all_types") {
            where.type = type;
        }
        if (module && module !== "all_modules") {
            where.module = module;
        }
        if (userId && userId !== "all_users") {
            where.userId = parseInt(userId);
        }
        if (status && status !== "all_statuses") {
            where.status = status;
        }
        if (severity && severity !== "all_severities") {
            where.severity = severity;
        }
        if (startDate || endDate) {
            where.timestamp = {};
            if (startDate) {
                where.timestamp.gte = new Date(startDate);
            }
            if (endDate) {
                where.timestamp.lte = new Date(endDate);
            }
        }
        // Get total count
        const total = await prisma.activityLog.count({ where });
        // Get activity logs
        const activityLogs = await prisma.activityLog.findMany({
            where,
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
            },
            orderBy: {
                [sortBy]: sortOrder,
            },
            skip,
            take: limitNum,
        });
        res.status(200).json({
            data: activityLogs,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total,
                pages: Math.ceil(total / limitNum),
            },
        });
    }
    catch (error) {
        console.error("Error fetching activity logs:", error);
        res.status(500).json({ error: "Failed to fetch activity logs" });
    }
};
exports.getActivityLogs = getActivityLogs;
// Get activity log by ID
const getActivityLogById = async (req, res) => {
    try {
        const { id } = req.params;
        const activityLog = await prisma.activityLog.findUnique({
            where: { id: parseInt(id) },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
            },
        });
        if (!activityLog) {
            res.status(404).json({ error: "Activity log not found" });
            return;
        }
        res.status(200).json(activityLog);
    }
    catch (error) {
        console.error("Error fetching activity log:", error);
        res.status(500).json({ error: "Failed to fetch activity log" });
    }
};
exports.getActivityLogById = getActivityLogById;
// Create activity log (for system use)
const createActivityLog = async (req, res) => {
    try {
        const { type, action, module, entity, entityId, title, description, metadata, userId, ipAddress, userAgent, status = "SUCCESS", severity = "INFO", changes, } = req.body;
        const activityLog = await prisma.activityLog.create({
            data: {
                type,
                action,
                module,
                entity,
                entityId,
                title,
                description,
                metadata,
                userId: userId ? parseInt(userId) : null,
                ipAddress,
                userAgent,
                status,
                severity,
                changes,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
            },
        });
        res.status(201).json(activityLog);
    }
    catch (error) {
        console.error("Error creating activity log:", error);
        res.status(500).json({ error: "Failed to create activity log" });
    }
};
exports.createActivityLog = createActivityLog;
// Get activity log statistics
const getActivityLogStats = async (req, res) => {
    try {
        const { period = "7d" } = req.query;
        // Calculate date range based on period
        const now = new Date();
        let startDate;
        switch (period) {
            case "24h":
                startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case "7d":
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case "30d":
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        }
        // Get total logs
        const totalLogs = await prisma.activityLog.count({
            where: {
                timestamp: {
                    gte: startDate,
                },
            },
        });
        // Get logs by type
        const logsByType = await prisma.activityLog.groupBy({
            by: ["type"],
            where: {
                timestamp: {
                    gte: startDate,
                },
            },
            _count: {
                id: true,
            },
        });
        // Get logs by module
        const logsByModule = await prisma.activityLog.groupBy({
            by: ["module"],
            where: {
                timestamp: {
                    gte: startDate,
                },
            },
            _count: {
                id: true,
            },
        });
        // Get error logs
        const errorLogs = await prisma.activityLog.count({
            where: {
                timestamp: {
                    gte: startDate,
                },
                severity: {
                    in: ["ERROR", "CRITICAL"],
                },
            },
        });
        // Get unique users
        const uniqueUsers = await prisma.activityLog.findMany({
            where: {
                timestamp: {
                    gte: startDate,
                },
                userId: {
                    not: null,
                },
            },
            select: {
                userId: true,
            },
            distinct: ["userId"],
        });
        res.status(200).json({
            totalLogs,
            errorLogs,
            uniqueUsers: uniqueUsers.length,
            logsByType: logsByType.map((item) => ({
                type: item.type,
                count: item._count.id,
            })),
            logsByModule: logsByModule.map((item) => ({
                module: item.module,
                count: item._count.id,
            })),
            period,
            startDate,
            endDate: now,
        });
    }
    catch (error) {
        console.error("Error fetching activity log stats:", error);
        res.status(500).json({ error: "Failed to fetch activity log statistics" });
    }
};
exports.getActivityLogStats = getActivityLogStats;
//# sourceMappingURL=activityLogs.controller.js.map