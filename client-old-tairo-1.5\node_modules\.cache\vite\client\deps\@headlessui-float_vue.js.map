{"version": 3, "sources": ["../../../../.pnpm/@headlessui-float+vue@0.15.0_@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2___vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui-float/vue/dist/headlessui-float.mjs", "../../../../.pnpm/@tanstack+virtual-core@3.13.4/node_modules/@tanstack/virtual-core/src/utils.ts", "../../../../.pnpm/@tanstack+virtual-core@3.13.4/node_modules/@tanstack/virtual-core/src/index.ts", "../../../../.pnpm/@tanstack+vue-virtual@3.13.4_vue@3.5.13_typescript@5.8.2_/node_modules/@tanstack/vue-virtual/src/index.ts", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/combobox/combobox.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-controllable.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-disposables.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/micro-task.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/disposables.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-frame-debounce.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-id.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-outside-click.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/dom.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/focus-management.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/match.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/env.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/owner.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/platform.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-document-event.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-window-event.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-resolve-button-type.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-tracked-pointer.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-tree-walker.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/internal/hidden.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/render.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/internal/open-closed.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/keyboard.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/mouse.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/document-ready.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/active-element-history.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/calculate-active-index.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/form.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/dialog/dialog.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/focus-trap/focus-trap.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-event-listener.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-tab-direction.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/document-overflow/use-document-overflow.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-store.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/store.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/document-overflow/adjust-scrollbar-padding.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/document-overflow/handle-ios-locking.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/document-overflow/prevent-scroll.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/document-overflow/overflow-store.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-inert.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-root-containers.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/internal/portal-force-root.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/internal/stack-context.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/description/description.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/portal/portal.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/disclosure/disclosure.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/listbox/listbox.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-text-value.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/get-text-value.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/menu/menu.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/popover/popover.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/radio-group/radio-group.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/label/label.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/switch/switch.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/tabs/tabs.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/internal/focus-sentinel.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/transitions/transition.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/once.js", "../../../../.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/transitions/utils/transition.js", "../../../../.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "../../../../.pnpm/@floating-ui+core@1.6.9/node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "../../../../.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../../../.pnpm/@floating-ui+dom@1.6.13/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "../../../../.pnpm/@floating-ui+vue@1.1.6_vue@3.5.13_typescript@5.8.2_/node_modules/@floating-ui/vue/dist/floating-ui.vue.mjs"], "sourcesContent": ["var he = Object.defineProperty;\nvar pe = (e, r, o) => r in e ? he(e, r, { enumerable: !0, configurable: !0, writable: !0, value: o }) : e[r] = o;\nvar G = (e, r, o) => pe(e, typeof r != \"symbol\" ? r + \"\" : r, o);\nimport { ref as h, Fragment as ye, unref as we, watch as P, onMounted as U, onBeforeUnmount as be, computed as O, watchEffect as te, mergeProps as N, cloneVNode as k, h as b, toRef as J, shallowRef as Fe, nextTick as Ce, provide as $, defineComponent as R, inject as W, createCommentVNode as M, Transition as Te } from \"vue\";\nimport { Portal as xe, TransitionChild as Ee, TransitionRoot as Ae } from \"@headlessui/vue\";\nimport { arrow as Pe, useFloating as Oe } from \"@floating-ui/vue\";\nimport { offset as Re, flip as Se, shift as Ne, autoPlacement as je, hide as Be, autoUpdate as Ve } from \"@floating-ui/dom\";\nfunction B(e) {\n  return e == null || e.value == null ? null : e.value instanceof Node ? e.value : \"$el\" in e.value && e.value.$el ? B(h(e.value.$el)) : \"getBoundingClientRect\" in e.value ? e.value : null;\n}\nfunction He(e) {\n  return typeof window > \"u\" ? 1 : (e.ownerDocument.defaultView || window).devicePixelRatio || 1;\n}\nfunction K(e, r) {\n  const o = He(e);\n  return Math.round(r * o) / o;\n}\nfunction L(e) {\n  return e.reduce((r, o) => o.type === ye ? r.concat(L(o.children)) : r.concat(o), []);\n}\nfunction I(e) {\n  return e == null ? !1 : typeof e.type == \"string\" || typeof e.type == \"object\" || typeof e.type == \"function\";\n}\nfunction Q(e) {\n  return e = we(e), e && (e == null ? void 0 : e.nodeType) !== Node.COMMENT_NODE;\n}\nclass Ie {\n  constructor() {\n    G(this, \"current\", this.detect());\n  }\n  set(r) {\n    this.current !== r && (this.current = r);\n  }\n  reset() {\n    this.set(this.detect());\n  }\n  get isServer() {\n    return this.current === \"server\";\n  }\n  get isClient() {\n    return this.current === \"client\";\n  }\n  detect() {\n    return typeof window > \"u\" || typeof document > \"u\" ? \"server\" : \"client\";\n  }\n}\nconst X = new Ie();\nfunction ne(e) {\n  if (X.isServer) return null;\n  if (e instanceof Node) return e.ownerDocument;\n  if (e && Object.prototype.hasOwnProperty.call(e, \"value\")) {\n    const r = B(e);\n    if (r) return r.ownerDocument;\n  }\n  return document;\n}\nfunction Y(e, r) {\n  !r.vueTransition && (r.transitionName || r.transitionType) && console.warn(`[headlessui-float]: <${e} /> pass \"transition-name\" or \"transition-type\" prop, must be set \"vue-transition\" prop.`);\n}\nfunction Ue(e, r, o, a, t) {\n  P([\n    () => t.offset,\n    () => t.flip,\n    () => t.shift,\n    () => t.autoPlacement,\n    () => t.arrow,\n    () => t.hide,\n    () => t.middleware\n  ], () => {\n    const i = [];\n    (typeof t.offset == \"number\" || typeof t.offset == \"object\" || typeof t.offset == \"function\") && i.push(Re(t.offset)), (t.flip === !0 || typeof t.flip == \"number\" || typeof t.flip == \"object\") && i.push(Se({\n      padding: typeof t.flip == \"number\" ? t.flip : void 0,\n      ...typeof t.flip == \"object\" ? t.flip : {}\n    })), (t.shift === !0 || typeof t.shift == \"number\" || typeof t.shift == \"object\") && i.push(Ne({\n      padding: typeof t.shift == \"number\" ? t.shift : void 0,\n      ...typeof t.shift == \"object\" ? t.shift : {}\n    })), (t.autoPlacement === !0 || typeof t.autoPlacement == \"object\") && i.push(je(\n      typeof t.autoPlacement == \"object\" ? t.autoPlacement : void 0\n    )), i.push(...typeof t.middleware == \"function\" ? t.middleware({\n      referenceEl: r,\n      floatingEl: o\n    }) : t.middleware || []), (t.arrow === !0 || typeof t.arrow == \"number\") && i.push(Pe({\n      element: a,\n      padding: t.arrow === !0 ? 0 : t.arrow\n    })), (t.hide === !0 || typeof t.hide == \"object\" || Array.isArray(t.hide)) && (Array.isArray(t.hide) ? t.hide : [t.hide]).forEach((u) => {\n      i.push(Be(\n        typeof u == \"object\" ? u : void 0\n      ));\n    }), e.value = i;\n  }, { immediate: !0 });\n}\nfunction ze(e, r, o) {\n  let a = () => {\n  };\n  U(() => {\n    if (e && X.isClient && typeof ResizeObserver < \"u\" && r.value && r.value instanceof Element) {\n      const t = new ResizeObserver(([i]) => {\n        o.value = i.borderBoxSize.reduce((u, { inlineSize: s }) => u + s, 0);\n      });\n      t.observe(r.value), a = () => {\n        t.disconnect(), o.value = null;\n      };\n    }\n  }), be(() => {\n    a();\n  });\n}\nconst st = [\n  \"origin-bottom\",\n  \"origin-top\",\n  \"origin-right\",\n  \"origin-left\",\n  \"origin-bottom-left\",\n  \"origin-bottom-right\",\n  \"origin-top-left\",\n  \"origin-top-right\"\n], De = (e) => {\n  switch (e) {\n    case \"top\":\n      return \"origin-bottom\";\n    case \"bottom\":\n      return \"origin-top\";\n    case \"left\":\n      return \"origin-right\";\n    case \"right\":\n      return \"origin-left\";\n    case \"top-start\":\n    case \"right-end\":\n      return \"origin-bottom-left\";\n    case \"top-end\":\n    case \"left-end\":\n      return \"origin-bottom-right\";\n    case \"right-start\":\n    case \"bottom-start\":\n      return \"origin-top-left\";\n    case \"left-start\":\n    case \"bottom-end\":\n      return \"origin-top-right\";\n    default:\n      return \"origin-center\";\n  }\n}, ut = [\n  \"origin-bottom\",\n  \"origin-top\",\n  \"ltr:origin-right rtl:origin-left\",\n  \"ltr:origin-left rtl:origin-right\",\n  \"ltr:origin-bottom-left rtl:origin-bottom-right\",\n  \"ltr:origin-bottom-right rtl:origin-bottom-left\",\n  \"ltr:origin-top-left rtl:origin-top-right\",\n  \"ltr:origin-top-right rtl:origin-top-left\"\n], ft = (e) => {\n  switch (e) {\n    case \"top\":\n      return \"origin-bottom\";\n    case \"bottom\":\n      return \"origin-top\";\n    case \"left\":\n      return \"ltr:origin-right rtl:origin-left\";\n    case \"right\":\n      return \"ltr:origin-left rtl:origin-right\";\n    case \"top-start\":\n    case \"right-end\":\n      return \"ltr:origin-bottom-left rtl:origin-bottom-right\";\n    case \"top-end\":\n    case \"left-end\":\n      return \"ltr:origin-bottom-right rtl:origin-bottom-left\";\n    case \"right-start\":\n    case \"bottom-start\":\n      return \"ltr:origin-top-left rtl:origin-top-right\";\n    case \"left-start\":\n    case \"bottom-end\":\n      return \"ltr:origin-top-right rtl:origin-top-left\";\n    default:\n      return \"origin-center\";\n  }\n};\nfunction Me(e, r) {\n  const o = O(() => {\n    if (typeof e.originClass == \"function\")\n      return e.originClass(r.value);\n    if (typeof e.originClass == \"string\")\n      return e.originClass;\n    if (e.tailwindcssOriginClass)\n      return De(r.value);\n  }), a = O(\n    () => e.enter || o.value ? `${e.enter || \"\"} ${o.value || \"\"}` : void 0\n  ), t = O(\n    () => e.leave || o.value ? `${e.leave || \"\"} ${o.value || \"\"}` : void 0\n  );\n  return { originClassRef: o, enterActiveClassRef: a, leaveActiveClassRef: t };\n}\nfunction re(e, r, ...o) {\n  if (e in r) {\n    const t = r[e];\n    return typeof t == \"function\" ? t(...o) : t;\n  }\n  const a = new Error(\n    `Tried to handle \"${e}\" but there is no handler defined. Only defined handlers are: ${Object.keys(\n      r\n    ).map((t) => `\"${t}\"`).join(\", \")}.`\n  );\n  throw Error.captureStackTrace && Error.captureStackTrace(a, re), a;\n}\nconst Z = [\n  \"[contentEditable=true]\",\n  \"[tabindex]\",\n  \"a[href]\",\n  \"area[href]\",\n  \"button:not([disabled])\",\n  \"iframe\",\n  \"input:not([disabled])\",\n  \"select:not([disabled])\",\n  \"textarea:not([disabled])\"\n].map(\n  // TODO: Remove this once JSDOM fixes the issue where an element that is\n  // \"hidden\" can be the document.activeElement, because this is not possible\n  // in real browsers.\n  process.env.NODE_ENV === \"test\" ? (e) => `${e}:not([tabindex='-1']):not([style*='display: none'])` : (e) => `${e}:not([tabindex='-1'])`\n).join(\",\");\nvar oe = /* @__PURE__ */ ((e) => (e[e.Strict = 0] = \"Strict\", e[e.Loose = 1] = \"Loose\", e))(oe || {});\nfunction $e(e, r = 0) {\n  var o;\n  return e === ((o = ne(e)) == null ? void 0 : o.body) ? !1 : re(r, {\n    0() {\n      return e.matches(Z);\n    },\n    1() {\n      let a = e;\n      for (; a !== null; ) {\n        if (a.matches(Z)) return !0;\n        a = a.parentElement;\n      }\n      return !1;\n    }\n  });\n}\nfunction C(e, r, o) {\n  X.isServer || te((a) => {\n    document.addEventListener(e, r, o), a(() => document.removeEventListener(e, r, o));\n  });\n}\nfunction ke(e, r, o = O(() => !0)) {\n  function a(i, u) {\n    if (!o.value || i.defaultPrevented) return;\n    const s = u(i);\n    if (s === null || !s.getRootNode().contains(s)) return;\n    const d = function f(c) {\n      return typeof c == \"function\" ? f(c()) : Array.isArray(c) || c instanceof Set ? c : [c];\n    }(e);\n    for (const f of d) {\n      if (f === null) continue;\n      const c = f instanceof HTMLElement ? f : B(f);\n      if (c != null && c.contains(s) || i.composed && i.composedPath().includes(c))\n        return;\n    }\n    return (\n      // This check alllows us to know whether or not we clicked on a \"focusable\" element like a\n      // button or an input. This is a backwards compatibility check so that you can open a <Menu\n      // /> and click on another <Menu /> which should close Menu A and open Menu B. We might\n      // revisit that so that you will require 2 clicks instead.\n      !$e(s, oe.Loose) && // This could be improved, but the `Combobox.Button` adds tabIndex={-1} to make it\n      // unfocusable via the keyboard so that tabbing to the next item from the input doesn't\n      // first go to the button.\n      s.tabIndex !== -1 && i.preventDefault(), r(i, s)\n    );\n  }\n  const t = h(null);\n  C(\"mousedown\", (i) => {\n    var u, s;\n    o.value && (t.value = ((s = (u = i.composedPath) == null ? void 0 : u.call(i)) == null ? void 0 : s[0]) || i.target);\n  }, !0), C(\n    \"click\",\n    (i) => {\n      t.value && (a(i, () => t.value), t.value = null);\n    },\n    // We will use the `capture` phase so that layers in between with `event.stopPropagation()`\n    // don't \"cancel\" this outside click check. E.g.: A `Menu` inside a `DialogPanel` if the `Menu`\n    // is open, and you click outside of it in the `DialogPanel` the `Menu` should close. However,\n    // the `DialogPanel` has a `onClick(e) { e.stopPropagation() }` which would cancel this.\n    !0\n  ), C(\"blur\", (i) => a(\n    i,\n    () => window.document.activeElement instanceof HTMLIFrameElement ? window.document.activeElement : null\n  ), !0);\n}\nconst ae = Symbol(\"ReferenceContext\"), ie = Symbol(\"FloatingContext\"), le = Symbol(\"ArrowContext\");\nfunction se(e) {\n  const r = W(ae, null);\n  if (r === null) {\n    const o = new Error(`<${e} /> must be in the <Float /> component.`);\n    throw Error.captureStackTrace && Error.captureStackTrace(o, se), o;\n  }\n  return r;\n}\nfunction ue(e) {\n  const r = W(ie, null);\n  if (r === null) {\n    const o = new Error(`<${e} /> must be in the <Float /> component.`);\n    throw Error.captureStackTrace && Error.captureStackTrace(o, ue), o;\n  }\n  return r;\n}\nfunction fe(e) {\n  const r = W(le, null);\n  if (r === null) {\n    const o = new Error(`<${e} /> must be in the <Float /> component.`);\n    throw Error.captureStackTrace && Error.captureStackTrace(o, fe), o;\n  }\n  return r;\n}\nconst n = {\n  as: {\n    type: [String, Function],\n    default: \"template\"\n  },\n  floatingAs: {\n    type: [String, Function],\n    default: \"div\"\n  },\n  show: {\n    type: Boolean,\n    default: null\n  },\n  placement: {\n    type: String,\n    default: \"bottom-start\"\n  },\n  strategy: {\n    type: String,\n    default: \"absolute\"\n  },\n  offset: [Number, Function, Object],\n  shift: {\n    type: [Boolean, Number, Object],\n    default: !1\n  },\n  flip: {\n    type: [Boolean, Number, Object],\n    default: !1\n  },\n  arrow: {\n    type: [Boolean, Number],\n    default: !1\n  },\n  autoPlacement: {\n    type: [Boolean, Object],\n    default: !1\n  },\n  hide: {\n    type: [Boolean, Object, Array],\n    default: !1\n  },\n  referenceHiddenClass: String,\n  escapedClass: String,\n  autoUpdate: {\n    type: [Boolean, Object],\n    default: !0\n  },\n  zIndex: {\n    type: [Number, String],\n    default: 9999\n  },\n  vueTransition: {\n    type: Boolean,\n    default: !1\n  },\n  transitionName: String,\n  transitionType: String,\n  enter: String,\n  enterFrom: String,\n  enterTo: String,\n  leave: String,\n  leaveFrom: String,\n  leaveTo: String,\n  originClass: [String, Function],\n  tailwindcssOriginClass: {\n    type: Boolean,\n    default: !1\n  },\n  portal: {\n    type: Boolean,\n    default: !1\n  },\n  transform: {\n    type: Boolean,\n    default: !1\n  },\n  adaptiveWidth: {\n    type: [Boolean, Object],\n    default: !1\n  },\n  composable: {\n    type: Boolean,\n    default: !1\n  },\n  dialog: {\n    type: Boolean,\n    default: !1\n  },\n  middleware: {\n    type: [Array, Function],\n    default: () => []\n  }\n};\nfunction ce(e, r, o, a) {\n  const { referenceRef: t } = a, i = r, u = N(o, {\n    ref: t\n  }), s = k(\n    e,\n    i.as === \"template\" ? u : {}\n  );\n  return i.as === \"template\" ? s : typeof i.as == \"string\" ? b(i.as, u, [s]) : b(i.as, u, () => [s]);\n}\nfunction _(e, r, o, a) {\n  const { floatingRef: t, props: i, mounted: u, show: s, referenceHidden: d, escaped: f, placement: c, floatingStyles: p, referenceElWidth: y, updateFloating: T } = a, l = N(\n    { ...i, as: i.floatingAs },\n    r\n  ), { enterActiveClassRef: g, leaveActiveClassRef: m } = Me(l, c), F = {\n    show: u.value ? l.show : !1,\n    enter: g.value,\n    enterFrom: l.enterFrom,\n    enterTo: l.enterTo,\n    leave: m.value,\n    leaveFrom: l.leaveFrom,\n    leaveTo: l.leaveTo,\n    onBeforeEnter() {\n      s.value = !0;\n    },\n    onAfterLeave() {\n      s.value = !1;\n    }\n  }, x = {\n    name: l.transitionName,\n    type: l.transitionType,\n    appear: !0,\n    ...l.transitionName ? {} : {\n      enterActiveClass: g.value,\n      enterFromClass: l.enterFrom,\n      enterToClass: l.enterTo,\n      leaveActiveClass: m.value,\n      leaveFromClass: l.leaveFrom,\n      leaveToClass: l.leaveTo\n    },\n    onBeforeEnter() {\n      s.value = !0;\n    },\n    onAfterLeave() {\n      s.value = !1;\n    }\n  }, E = {\n    class: [\n      d.value ? l.referenceHiddenClass : void 0,\n      f.value ? l.escapedClass : void 0\n    ].filter((v) => !!v).join(\" \"),\n    style: {\n      ...p.value,\n      zIndex: l.zIndex\n    }\n  };\n  if (l.adaptiveWidth && typeof y.value == \"number\") {\n    const v = {\n      attribute: \"width\",\n      ...typeof l.adaptiveWidth == \"object\" ? l.adaptiveWidth : {}\n    };\n    E.style[v.attribute] = `${y.value}px`;\n  }\n  function z(v) {\n    return l.portal ? u.value ? b(xe, () => v) : M() : v;\n  }\n  function D(v) {\n    const A = N(\n      E,\n      o,\n      l.dialog ? {} : { ref: t }\n    );\n    return l.as === \"template\" ? v : typeof l.as == \"string\" ? b(l.as, A, v) : b(l.as, A, () => v);\n  }\n  function V() {\n    function v() {\n      var H;\n      const A = l.as === \"template\" ? N(\n        E,\n        o,\n        l.dialog ? {} : { ref: t }\n      ) : null, j = k(e, A);\n      return ((H = e.props) == null ? void 0 : H.unmount) === !1 ? (T(), j) : l.vueTransition && l.show === !1 ? M() : j;\n    }\n    return u.value ? l.vueTransition ? b(Te, {\n      ...l.dialog ? { ref: t } : {},\n      ...x\n    }, v) : b(l.transitionChild ? Ee : Ae, {\n      key: `placement-${c.value}`,\n      ...l.dialog ? { ref: t } : {},\n      as: \"template\",\n      ...F\n    }, v) : M();\n  }\n  return z(\n    D(\n      V()\n    )\n  );\n}\nfunction de(e, r, o, a, t) {\n  const i = h(!1), u = J(a, \"placement\"), s = J(a, \"strategy\"), d = Fe({}), f = h(void 0), c = h(void 0), p = h(null), y = h(void 0), T = h(void 0), l = O(() => B(r)), g = O(() => B(o)), m = O(\n    () => Q(l) && Q(g)\n  ), { placement: F, middlewareData: x, isPositioned: E, floatingStyles: z, update: D } = Oe(l, g, {\n    placement: u,\n    strategy: s,\n    middleware: d,\n    // If enable dialog mode, then set `transform` to false.\n    transform: a.dialog ? !1 : a.transform,\n    // Fix transition not smooth bug when dialog mode enabled.\n    whileElementsMounted: a.dialog ? () => () => {\n    } : void 0\n  }), V = h(null);\n  U(() => {\n    i.value = !0;\n  }), P(e, (w, S) => {\n    w && !S ? t(\"show\") : !w && S && t(\"hide\");\n  }, { immediate: !0 });\n  function v() {\n    m.value && (D(), t(\"update\"));\n  }\n  P([u, s, d], v, { flush: \"sync\" }), Ue(\n    d,\n    l,\n    g,\n    p,\n    a\n  ), P([x, () => a.hide, E], () => {\n    var w, S;\n    (a.hide === !0 || typeof a.hide == \"object\" || Array.isArray(a.hide)) && (f.value = ((w = x.value.hide) == null ? void 0 : w.referenceHidden) || !E.value, c.value = (S = x.value.hide) == null ? void 0 : S.escaped);\n  }), P(x, () => {\n    const w = x.value.arrow;\n    y.value = w == null ? void 0 : w.x, T.value = w == null ? void 0 : w.y;\n  }), ze(!!a.adaptiveWidth, l, V), P([e, m], async (w, S, ve) => {\n    if (await Ce(), e.value && m.value && a.autoUpdate) {\n      const ge = Ve(\n        l.value,\n        g.value,\n        v,\n        typeof a.autoUpdate == \"object\" ? a.autoUpdate : void 0\n      );\n      ve(ge);\n    }\n  }, { flush: \"post\", immediate: !0 });\n  const A = h(!0);\n  P(l, () => {\n    !(l.value instanceof Element) && m.value && A.value && (A.value = !1, window.requestAnimationFrame(() => {\n      A.value = !0, v();\n    }));\n  }, { flush: \"sync\" });\n  const j = {\n    referenceRef: r,\n    placement: F\n  }, H = {\n    floatingRef: o,\n    props: a,\n    mounted: i,\n    show: e,\n    referenceHidden: f,\n    escaped: c,\n    placement: F,\n    floatingStyles: z,\n    referenceElWidth: V,\n    updateFloating: v\n  }, q = {\n    ref: p,\n    placement: F,\n    x: y,\n    y: T\n  };\n  return $(le, q), { referenceApi: j, floatingApi: H, arrowApi: q, placement: F, referenceEl: l, floatingEl: g, middlewareData: x, update: v };\n}\nconst We = R({\n  name: \"Float\",\n  inheritAttrs: !1,\n  props: n,\n  emits: [\"show\", \"hide\", \"update\"],\n  setup(e, { emit: r, slots: o, attrs: a }) {\n    Y(\"Float\", e);\n    const t = h(e.show ?? !1), i = h(null), u = h(null), {\n      referenceApi: s,\n      floatingApi: d,\n      placement: f\n    } = de(t, i, u, e, r);\n    function c(y) {\n      return e.as === \"template\" ? y : typeof e.as == \"string\" ? b(e.as, a, y) : b(e.as, a, () => y);\n    }\n    const p = {\n      placement: f.value\n    };\n    return e.composable || e.dialog ? ($(ae, s), $(ie, d), () => {\n      if (o.default)\n        return c(o.default(p));\n    }) : () => {\n      if (!o.default) return;\n      const [y, T] = L(o.default(p)).filter(I);\n      if (!I(y))\n        return;\n      const l = ce(\n        y,\n        { as: \"template\" },\n        {},\n        s\n      ), g = _(\n        T,\n        { as: e.floatingAs },\n        {},\n        d\n      );\n      return c([\n        l,\n        g\n      ]);\n    };\n  }\n}), Le = We, Xe = {\n  as: n.as\n}, Ye = R({\n  name: \"FloatReference\",\n  inheritAttrs: !1,\n  props: Xe,\n  setup(e, { slots: r, attrs: o }) {\n    const a = se(\"FloatReference\"), { placement: t } = a;\n    return () => {\n      if (!r.default) return;\n      const i = {\n        placement: t.value\n      };\n      return ce(\n        r.default(i)[0],\n        e,\n        o,\n        a\n      );\n    };\n  }\n}), ct = Ye, ee = {\n  as: n.floatingAs,\n  vueTransition: n.vueTransition,\n  transitionName: n.transitionName,\n  transitionType: n.transitionType,\n  enter: n.enter,\n  enterFrom: n.enterFrom,\n  enterTo: n.enterTo,\n  leave: n.leave,\n  leaveFrom: n.leaveFrom,\n  leaveTo: n.leaveTo,\n  originClass: n.originClass,\n  tailwindcssOriginClass: n.tailwindcssOriginClass,\n  transitionChild: {\n    type: Boolean,\n    default: !1\n  }\n}, _e = R({\n  name: \"FloatContent\",\n  inheritAttrs: !1,\n  props: ee,\n  setup(e, { slots: r, attrs: o }) {\n    Y(\"FloatContent\", e);\n    const a = ue(\"FloatContent\"), { placement: t } = a;\n    return () => {\n      if (!r.default) return;\n      const i = {\n        placement: t.value\n      }, u = Object.entries(e).reduce((s, [d, f]) => {\n        const c = ee;\n        return (typeof c[d] == \"object\" && f === c[d].default || f === void 0) && delete s[d], s;\n      }, { ...e });\n      return _(\n        r.default(i)[0],\n        u,\n        o,\n        a\n      );\n    };\n  }\n}), dt = _e, qe = {\n  as: {\n    ...n.as,\n    default: \"div\"\n  },\n  offset: {\n    type: Number,\n    default: 4\n  }\n}, Ge = R({\n  name: \"FloatArrow\",\n  props: qe,\n  setup(e, { slots: r, attrs: o }) {\n    const { ref: a, placement: t, x: i, y: u } = fe(\"FloatArrow\");\n    return () => {\n      var f;\n      const s = {\n        top: \"bottom\",\n        right: \"left\",\n        bottom: \"top\",\n        left: \"right\"\n      }[t.value.split(\"-\")[0]], d = {\n        left: a.value && typeof i.value == \"number\" ? `${K(a.value, i.value)}px` : void 0,\n        top: a.value && typeof u.value == \"number\" ? `${K(a.value, u.value)}px` : void 0,\n        right: void 0,\n        bottom: void 0,\n        [s]: `${e.offset * -1}px`\n      };\n      if (e.as === \"template\") {\n        const c = {\n          placement: t.value\n        }, p = (f = r.default) == null ? void 0 : f.call(r, c)[0];\n        return !p || !I(p) ? void 0 : k(p, { ref: a, style: d });\n      }\n      return b(e.as, N(o, { ref: a, style: d }));\n    };\n  }\n}), mt = Ge, Je = {\n  as: n.as,\n  show: n.show,\n  placement: n.placement,\n  strategy: n.strategy,\n  offset: n.offset,\n  shift: n.shift,\n  flip: n.flip,\n  arrow: n.arrow,\n  autoPlacement: n.autoPlacement,\n  autoUpdate: n.autoUpdate,\n  zIndex: n.zIndex,\n  vueTransition: n.vueTransition,\n  transitionName: n.transitionName,\n  transitionType: n.transitionType,\n  enter: n.enter,\n  enterFrom: n.enterFrom,\n  enterTo: n.enterTo,\n  leave: n.leave,\n  leaveFrom: n.leaveFrom,\n  leaveTo: n.leaveTo,\n  originClass: n.originClass,\n  tailwindcssOriginClass: n.tailwindcssOriginClass,\n  portal: n.portal,\n  transform: n.transform,\n  middleware: n.middleware\n}, Ke = R({\n  name: \"FloatVirtual\",\n  inheritAttrs: !1,\n  props: Je,\n  emits: [\"initial\", \"show\", \"hide\", \"update\"],\n  setup(e, { emit: r, slots: o, attrs: a }) {\n    Y(\"FloatVirtual\", e);\n    const t = h(e.show ?? !1), i = h({\n      getBoundingClientRect() {\n        return {\n          x: 0,\n          y: 0,\n          top: 0,\n          left: 0,\n          bottom: 0,\n          right: 0,\n          width: 0,\n          height: 0\n        };\n      }\n    }), u = h(null), {\n      floatingApi: s,\n      placement: d\n    } = de(t, i, u, e, r);\n    P(() => e.show, () => {\n      t.value = e.show ?? !1;\n    });\n    function f() {\n      t.value = !1;\n    }\n    return r(\"initial\", {\n      show: t,\n      placement: d,\n      reference: i,\n      floating: u\n    }), () => {\n      if (!o.default) return;\n      const c = {\n        placement: d.value,\n        close: f\n      }, [p] = L(o.default(c)).filter(I);\n      return _(\n        p,\n        {\n          as: e.as,\n          show: t.value\n        },\n        a,\n        s\n      );\n    };\n  }\n}), me = Ke, Qe = {\n  as: n.as,\n  placement: n.placement,\n  strategy: n.strategy,\n  offset: n.offset,\n  shift: n.shift,\n  flip: {\n    ...n.flip,\n    default: !0\n  },\n  arrow: n.arrow,\n  autoPlacement: n.autoPlacement,\n  autoUpdate: n.autoUpdate,\n  zIndex: n.zIndex,\n  vueTransition: n.vueTransition,\n  transitionName: n.transitionName,\n  transitionType: n.transitionType,\n  enter: n.enter,\n  enterFrom: n.enterFrom,\n  enterTo: n.enterTo,\n  leave: n.leave,\n  leaveFrom: n.leaveFrom,\n  leaveTo: n.leaveTo,\n  originClass: n.originClass,\n  tailwindcssOriginClass: n.tailwindcssOriginClass,\n  transform: n.transform,\n  middleware: n.middleware\n}, Ze = R({\n  name: \"FloatContextMenu\",\n  inheritAttrs: !1,\n  props: Qe,\n  emits: [\"show\", \"hide\", \"update\"],\n  setup(e, { emit: r, slots: o, attrs: a }) {\n    const t = h(!1);\n    function i({ show: u, reference: s, floating: d }) {\n      C(\"contextmenu\", (f) => {\n        f.preventDefault(), s.value = {\n          getBoundingClientRect() {\n            return {\n              width: 0,\n              height: 0,\n              x: f.clientX,\n              y: f.clientY,\n              top: f.clientY,\n              left: f.clientX,\n              right: f.clientX,\n              bottom: f.clientY\n            };\n          }\n        }, u.value = !0;\n      }), ke(d, () => {\n        u.value = !1;\n      }, O(() => u.value));\n    }\n    return U(() => {\n      t.value = !0;\n    }), () => {\n      if (o.default && t.value)\n        return b(me, {\n          ...e,\n          ...a,\n          portal: !0,\n          onInitial: i,\n          onShow: () => r(\"show\"),\n          onHide: () => r(\"hide\"),\n          onUpdate: () => r(\"update\")\n        }, o.default);\n    };\n  }\n}), vt = Ze, et = {\n  as: n.as,\n  placement: n.placement,\n  strategy: n.strategy,\n  offset: n.offset,\n  shift: n.shift,\n  flip: n.flip,\n  arrow: n.arrow,\n  autoPlacement: n.autoPlacement,\n  autoUpdate: n.autoUpdate,\n  zIndex: n.zIndex,\n  vueTransition: n.vueTransition,\n  transitionName: n.transitionName,\n  transitionType: n.transitionType,\n  enter: n.enter,\n  enterFrom: n.enterFrom,\n  enterTo: n.enterTo,\n  leave: n.leave,\n  leaveFrom: n.leaveFrom,\n  leaveTo: n.leaveTo,\n  originClass: n.originClass,\n  tailwindcssOriginClass: n.tailwindcssOriginClass,\n  transform: n.transform,\n  middleware: n.middleware,\n  globalHideCursor: {\n    type: Boolean,\n    default: !0\n  }\n}, tt = R({\n  name: \"FloatCursor\",\n  inheritAttrs: !1,\n  props: et,\n  emits: [\"show\", \"hide\", \"update\"],\n  setup({ globalHideCursor: e, ...r }, { emit: o, slots: a, attrs: t }) {\n    const i = h(!1);\n    function u({ show: s, reference: d, floating: f }) {\n      function c() {\n        s.value = !0;\n      }\n      function p() {\n        s.value = !1;\n      }\n      function y(m) {\n        d.value = {\n          getBoundingClientRect() {\n            return {\n              width: 0,\n              height: 0,\n              x: m.clientX,\n              y: m.clientY,\n              top: m.clientY,\n              left: m.clientX,\n              right: m.clientX,\n              bottom: m.clientY\n            };\n          }\n        };\n      }\n      function T(m) {\n        c(), y(m);\n      }\n      function l(m) {\n        c(), y(m.touches[0]);\n      }\n      const g = ne(f);\n      g && (te((m) => {\n        if (e && !g.getElementById(\"headlesui-float-cursor-style\")) {\n          const F = g.createElement(\"style\");\n          (g.head || g.getElementsByTagName(\"head\")[0]).appendChild(F), F.id = \"headlesui-float-cursor-style\", F.appendChild(g.createTextNode([\n            \"*, *::before, *::after {\",\n            \"  cursor: none !important;\",\n            \"}\",\n            \".headlesui-float-cursor-root {\",\n            \"  pointer-events: none !important;\",\n            \"}\"\n          ].join(`\n`))), m(() => {\n            var E;\n            return (E = g.getElementById(\"headlesui-float-cursor-style\")) == null ? void 0 : E.remove();\n          });\n        }\n      }, { flush: \"post\" }), \"ontouchstart\" in window || navigator.maxTouchPoints > 0 ? (C(\"touchstart\", l), C(\"touchend\", p), C(\"touchmove\", l)) : (C(\"mouseenter\", T), C(\"mouseleave\", p), C(\"mousemove\", T)));\n    }\n    return U(() => {\n      i.value = !0;\n    }), () => {\n      if (a.default && i.value)\n        return b(me, {\n          ...r,\n          ...t,\n          portal: !0,\n          class: \"headlesui-float-cursor-root\",\n          onInitial: u,\n          onShow: () => o(\"show\"),\n          onHide: () => o(\"hide\"),\n          onUpdate: () => o(\"update\")\n        }, a.default);\n    };\n  }\n}), gt = tt;\nfunction ht(e) {\n  return R({\n    name: \"HighOrderFloat\",\n    setup(o, { slots: a }) {\n      return () => b(Le, N(\n        e,\n        o\n      ), a);\n    }\n  });\n}\nconst nt = [\n  \"Float\",\n  \"FloatArrow\",\n  \"FloatContent\",\n  \"FloatReference\"\n];\nfunction pt(e = {}) {\n  const { prefix: r = \"\" } = e;\n  return {\n    type: \"component\",\n    resolve: (o) => {\n      if (o.startsWith(r)) {\n        const a = o.substring(r.length);\n        if (nt.includes(a))\n          return {\n            name: a,\n            from: \"@headlessui-float/vue\"\n          };\n      }\n    }\n  };\n}\nexport {\n  Le as Float,\n  mt as FloatArrow,\n  qe as FloatArrowPropsValidators,\n  dt as FloatContent,\n  ee as FloatContentPropsValidators,\n  vt as FloatContextMenu,\n  Qe as FloatContextMenuPropsValidators,\n  gt as FloatCursor,\n  et as FloatCursorPropsValidators,\n  n as FloatPropsValidators,\n  ct as FloatReference,\n  Xe as FloatReferencePropsValidators,\n  me as FloatVirtual,\n  Je as FloatVirtualPropsValidators,\n  pt as HeadlessUiFloatResolver,\n  ht as createHighOrderFloatComponent,\n  _ as renderFloatingElement,\n  ce as renderReferenceElement,\n  De as tailwindcssOriginClassResolver,\n  st as tailwindcssOriginSafelist,\n  ft as tailwindcssRtlOriginClassResolver,\n  ut as tailwindcssRtlOriginSafelist,\n  de as useFloat,\n  ke as useOutsideClick\n};\n", "export type NoInfer<A extends any> = [A][A extends any ? 0 : never]\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\n\nexport function memo<TDeps extends ReadonlyArray<any>, TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: false | string\n    debug?: () => boolean\n    onChange?: (result: TResult) => void\n    initialDeps?: TDeps\n  },\n) {\n  let deps = opts.initialDeps ?? []\n  let result: TResult | undefined\n\n  function memoizedFunction(): TResult {\n    let depTime: number\n    if (opts.key && opts.debug?.()) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug?.()) resultTime = Date.now()\n\n    result = fn(...newDeps)\n\n    if (opts.key && opts.debug?.()) {\n      const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n      const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n      const resultFpsPercentage = resultEndTime / 16\n\n      const pad = (str: number | string, num: number) => {\n        str = String(str)\n        while (str.length < num) {\n          str = ' ' + str\n        }\n        return str\n      }\n\n      console.info(\n        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n        `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120),\n            )}deg 100% 31%);`,\n        opts?.key,\n      )\n    }\n\n    opts?.onChange?.(result)\n\n    return result\n  }\n\n  // Attach updateDeps to the function itself\n  memoizedFunction.updateDeps = (newDeps: [...TDeps]) => {\n    deps = newDeps\n  }\n\n  return memoizedFunction\n}\n\nexport function notUndefined<T>(value: T | undefined, msg?: string): T {\n  if (value === undefined) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : ''}`)\n  } else {\n    return value\n  }\n}\n\nexport const approxEqual = (a: number, b: number) => Math.abs(a - b) < 1\n\nexport const debounce = (\n  targetWindow: Window & typeof globalThis,\n  fn: Function,\n  ms: number,\n) => {\n  let timeoutId: number\n  return function (this: any, ...args: Array<any>) {\n    targetWindow.clearTimeout(timeoutId)\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms)\n  }\n}\n", "import { approxEqual, debounce, memo, notUndefined } from './utils'\n\nexport * from './utils'\n\n//\n\ntype ScrollDirection = 'forward' | 'backward'\n\ntype ScrollAlignment = 'start' | 'center' | 'end' | 'auto'\n\ntype ScrollBehavior = 'auto' | 'smooth'\n\nexport interface ScrollToOptions {\n  align?: ScrollAlignment\n  behavior?: ScrollBehavior\n}\n\ntype ScrollToOffsetOptions = ScrollToOptions\n\ntype ScrollToIndexOptions = ScrollToOptions\n\nexport interface Range {\n  startIndex: number\n  endIndex: number\n  overscan: number\n  count: number\n}\n\ntype Key = number | string | bigint\n\nexport interface VirtualItem {\n  key: Key\n  index: number\n  start: number\n  end: number\n  size: number\n  lane: number\n}\n\nexport interface Rect {\n  width: number\n  height: number\n}\n\n//\n\nexport const defaultKeyExtractor = (index: number) => index\n\nexport const defaultRangeExtractor = (range: Range) => {\n  const start = Math.max(range.startIndex - range.overscan, 0)\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1)\n\n  const arr = []\n\n  for (let i = start; i <= end; i++) {\n    arr.push(i)\n  }\n\n  return arr\n}\n\nexport const observeElementRect = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  const handler = (rect: Rect) => {\n    const { width, height } = rect\n    cb({ width: Math.round(width), height: Math.round(height) })\n  }\n\n  handler(element.getBoundingClientRect())\n\n  if (!targetWindow.ResizeObserver) {\n    return () => {}\n  }\n\n  const observer = new targetWindow.ResizeObserver((entries) => {\n    const run = () => {\n      const entry = entries[0]\n      if (entry?.borderBoxSize) {\n        const box = entry.borderBoxSize[0]\n        if (box) {\n          handler({ width: box.inlineSize, height: box.blockSize })\n          return\n        }\n      }\n      handler(element.getBoundingClientRect())\n    }\n\n    instance.options.useAnimationFrameWithResizeObserver\n      ? requestAnimationFrame(run)\n      : run()\n  })\n\n  observer.observe(element, { box: 'border-box' })\n\n  return () => {\n    observer.unobserve(element)\n  }\n}\n\nconst addEventListenerOptions = {\n  passive: true,\n}\n\nexport const observeWindowRect = (\n  instance: Virtualizer<Window, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n\n  const handler = () => {\n    cb({ width: element.innerWidth, height: element.innerHeight })\n  }\n  handler()\n\n  element.addEventListener('resize', handler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('resize', handler)\n  }\n}\n\nconst supportsScrollend =\n  typeof window == 'undefined' ? true : 'onscrollend' in window\n\ntype ObserveOffsetCallBack = (offset: number, isScrolling: boolean) => void\n\nexport const observeElementOffset = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    const { horizontal, isRtl } = instance.options\n    offset = horizontal\n      ? element['scrollLeft'] * ((isRtl && -1) || 1)\n      : element['scrollTop']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const observeWindowOffset = (\n  instance: Virtualizer<Window, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    offset = element[instance.options.horizontal ? 'scrollX' : 'scrollY']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const measureElement = <TItemElement extends Element>(\n  element: TItemElement,\n  entry: ResizeObserverEntry | undefined,\n  instance: Virtualizer<any, TItemElement>,\n) => {\n  if (entry?.borderBoxSize) {\n    const box = entry.borderBoxSize[0]\n    if (box) {\n      const size = Math.round(\n        box[instance.options.horizontal ? 'inlineSize' : 'blockSize'],\n      )\n      return size\n    }\n  }\n  return Math.round(\n    element.getBoundingClientRect()[\n      instance.options.horizontal ? 'width' : 'height'\n    ],\n  )\n}\n\nexport const windowScroll = <T extends Window>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport const elementScroll = <T extends Element>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport interface VirtualizerOptions<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  // Required from the user\n  count: number\n  getScrollElement: () => TScrollElement | null\n  estimateSize: (index: number) => number\n\n  // Required from the framework adapter (but can be overridden)\n  scrollToFn: (\n    offset: number,\n    options: { adjustments?: number; behavior?: ScrollBehavior },\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => void\n  observeElementRect: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: (rect: Rect) => void,\n  ) => void | (() => void)\n  observeElementOffset: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: ObserveOffsetCallBack,\n  ) => void | (() => void)\n  // Optional\n  debug?: boolean\n  initialRect?: Rect\n  onChange?: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    sync: boolean,\n  ) => void\n  measureElement?: (\n    element: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => number\n  overscan?: number\n  horizontal?: boolean\n  paddingStart?: number\n  paddingEnd?: number\n  scrollPaddingStart?: number\n  scrollPaddingEnd?: number\n  initialOffset?: number | (() => number)\n  getItemKey?: (index: number) => Key\n  rangeExtractor?: (range: Range) => Array<number>\n  scrollMargin?: number\n  gap?: number\n  indexAttribute?: string\n  initialMeasurementsCache?: Array<VirtualItem>\n  lanes?: number\n  isScrollingResetDelay?: number\n  useScrollendEvent?: boolean\n  enabled?: boolean\n  isRtl?: boolean\n  useAnimationFrameWithResizeObserver?: boolean\n}\n\nexport class Virtualizer<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  private unsubs: Array<void | (() => void)> = []\n  options!: Required<VirtualizerOptions<TScrollElement, TItemElement>>\n  scrollElement: TScrollElement | null = null\n  targetWindow: (Window & typeof globalThis) | null = null\n  isScrolling = false\n  private scrollToIndexTimeoutId: number | null = null\n  measurementsCache: Array<VirtualItem> = []\n  private itemSizeCache = new Map<Key, number>()\n  private pendingMeasuredCacheIndexes: Array<number> = []\n  scrollRect: Rect | null = null\n  scrollOffset: number | null = null\n  scrollDirection: ScrollDirection | null = null\n  private scrollAdjustments = 0\n  shouldAdjustScrollPositionOnItemSizeChange:\n    | undefined\n    | ((\n        item: VirtualItem,\n        delta: number,\n        instance: Virtualizer<TScrollElement, TItemElement>,\n      ) => boolean)\n  elementsCache = new Map<Key, TItemElement>()\n  private observer = (() => {\n    let _ro: ResizeObserver | null = null\n\n    const get = () => {\n      if (_ro) {\n        return _ro\n      }\n\n      if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n        return null\n      }\n\n      return (_ro = new this.targetWindow.ResizeObserver((entries) => {\n        entries.forEach((entry) => {\n          const run = () => {\n            this._measureElement(entry.target as TItemElement, entry)\n          }\n          this.options.useAnimationFrameWithResizeObserver\n            ? requestAnimationFrame(run)\n            : run()\n        })\n      }))\n    }\n\n    return {\n      disconnect: () => {\n        get()?.disconnect()\n        _ro = null\n      },\n      observe: (target: Element) =>\n        get()?.observe(target, { box: 'border-box' }),\n      unobserve: (target: Element) => get()?.unobserve(target),\n    }\n  })()\n  range: { startIndex: number; endIndex: number } | null = null\n\n  constructor(opts: VirtualizerOptions<TScrollElement, TItemElement>) {\n    this.setOptions(opts)\n  }\n\n  setOptions = (opts: VirtualizerOptions<TScrollElement, TItemElement>) => {\n    Object.entries(opts).forEach(([key, value]) => {\n      if (typeof value === 'undefined') delete (opts as any)[key]\n    })\n\n    this.options = {\n      debug: false,\n      initialOffset: 0,\n      overscan: 1,\n      paddingStart: 0,\n      paddingEnd: 0,\n      scrollPaddingStart: 0,\n      scrollPaddingEnd: 0,\n      horizontal: false,\n      getItemKey: defaultKeyExtractor,\n      rangeExtractor: defaultRangeExtractor,\n      onChange: () => {},\n      measureElement,\n      initialRect: { width: 0, height: 0 },\n      scrollMargin: 0,\n      gap: 0,\n      indexAttribute: 'data-index',\n      initialMeasurementsCache: [],\n      lanes: 1,\n      isScrollingResetDelay: 150,\n      enabled: true,\n      isRtl: false,\n      useScrollendEvent: false,\n      useAnimationFrameWithResizeObserver: false,\n      ...opts,\n    }\n  }\n\n  private notify = (sync: boolean) => {\n    this.options.onChange?.(this, sync)\n  }\n\n  private maybeNotify = memo(\n    () => {\n      this.calculateRange()\n\n      return [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ]\n    },\n    (isScrolling) => {\n      this.notify(isScrolling)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'maybeNotify',\n      debug: () => this.options.debug,\n      initialDeps: [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ] as [boolean, number | null, number | null],\n    },\n  )\n\n  private cleanup = () => {\n    this.unsubs.filter(Boolean).forEach((d) => d!())\n    this.unsubs = []\n    this.observer.disconnect()\n    this.scrollElement = null\n    this.targetWindow = null\n  }\n\n  _didMount = () => {\n    return () => {\n      this.cleanup()\n    }\n  }\n\n  _willUpdate = () => {\n    const scrollElement = this.options.enabled\n      ? this.options.getScrollElement()\n      : null\n\n    if (this.scrollElement !== scrollElement) {\n      this.cleanup()\n\n      if (!scrollElement) {\n        this.maybeNotify()\n        return\n      }\n\n      this.scrollElement = scrollElement\n\n      if (this.scrollElement && 'ownerDocument' in this.scrollElement) {\n        this.targetWindow = this.scrollElement.ownerDocument.defaultView\n      } else {\n        this.targetWindow = this.scrollElement?.window ?? null\n      }\n\n      this.elementsCache.forEach((cached) => {\n        this.observer.observe(cached)\n      })\n\n      this._scrollToOffset(this.getScrollOffset(), {\n        adjustments: undefined,\n        behavior: undefined,\n      })\n\n      this.unsubs.push(\n        this.options.observeElementRect(this, (rect) => {\n          this.scrollRect = rect\n          this.maybeNotify()\n        }),\n      )\n\n      this.unsubs.push(\n        this.options.observeElementOffset(this, (offset, isScrolling) => {\n          this.scrollAdjustments = 0\n          this.scrollDirection = isScrolling\n            ? this.getScrollOffset() < offset\n              ? 'forward'\n              : 'backward'\n            : null\n          this.scrollOffset = offset\n          this.isScrolling = isScrolling\n\n          this.maybeNotify()\n        }),\n      )\n    }\n  }\n\n  private getSize = () => {\n    if (!this.options.enabled) {\n      this.scrollRect = null\n      return 0\n    }\n\n    this.scrollRect = this.scrollRect ?? this.options.initialRect\n\n    return this.scrollRect[this.options.horizontal ? 'width' : 'height']\n  }\n\n  private getScrollOffset = () => {\n    if (!this.options.enabled) {\n      this.scrollOffset = null\n      return 0\n    }\n\n    this.scrollOffset =\n      this.scrollOffset ??\n      (typeof this.options.initialOffset === 'function'\n        ? this.options.initialOffset()\n        : this.options.initialOffset)\n\n    return this.scrollOffset\n  }\n\n  private getFurthestMeasurement = (\n    measurements: Array<VirtualItem>,\n    index: number,\n  ) => {\n    const furthestMeasurementsFound = new Map<number, true>()\n    const furthestMeasurements = new Map<number, VirtualItem>()\n    for (let m = index - 1; m >= 0; m--) {\n      const measurement = measurements[m]!\n\n      if (furthestMeasurementsFound.has(measurement.lane)) {\n        continue\n      }\n\n      const previousFurthestMeasurement = furthestMeasurements.get(\n        measurement.lane,\n      )\n      if (\n        previousFurthestMeasurement == null ||\n        measurement.end > previousFurthestMeasurement.end\n      ) {\n        furthestMeasurements.set(measurement.lane, measurement)\n      } else if (measurement.end < previousFurthestMeasurement.end) {\n        furthestMeasurementsFound.set(measurement.lane, true)\n      }\n\n      if (furthestMeasurementsFound.size === this.options.lanes) {\n        break\n      }\n    }\n\n    return furthestMeasurements.size === this.options.lanes\n      ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n          if (a.end === b.end) {\n            return a.index - b.index\n          }\n\n          return a.end - b.end\n        })[0]\n      : undefined\n  }\n\n  private getMeasurementOptions = memo(\n    () => [\n      this.options.count,\n      this.options.paddingStart,\n      this.options.scrollMargin,\n      this.options.getItemKey,\n      this.options.enabled,\n    ],\n    (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n      this.pendingMeasuredCacheIndexes = []\n      return {\n        count,\n        paddingStart,\n        scrollMargin,\n        getItemKey,\n        enabled,\n      }\n    },\n    {\n      key: false,\n    },\n  )\n\n  private getMeasurements = memo(\n    () => [this.getMeasurementOptions(), this.itemSizeCache],\n    (\n      { count, paddingStart, scrollMargin, getItemKey, enabled },\n      itemSizeCache,\n    ) => {\n      if (!enabled) {\n        this.measurementsCache = []\n        this.itemSizeCache.clear()\n        return []\n      }\n\n      if (this.measurementsCache.length === 0) {\n        this.measurementsCache = this.options.initialMeasurementsCache\n        this.measurementsCache.forEach((item) => {\n          this.itemSizeCache.set(item.key, item.size)\n        })\n      }\n\n      const min =\n        this.pendingMeasuredCacheIndexes.length > 0\n          ? Math.min(...this.pendingMeasuredCacheIndexes)\n          : 0\n      this.pendingMeasuredCacheIndexes = []\n\n      const measurements = this.measurementsCache.slice(0, min)\n\n      for (let i = min; i < count; i++) {\n        const key = getItemKey(i)\n\n        const furthestMeasurement =\n          this.options.lanes === 1\n            ? measurements[i - 1]\n            : this.getFurthestMeasurement(measurements, i)\n\n        const start = furthestMeasurement\n          ? furthestMeasurement.end + this.options.gap\n          : paddingStart + scrollMargin\n\n        const measuredSize = itemSizeCache.get(key)\n        const size =\n          typeof measuredSize === 'number'\n            ? measuredSize\n            : this.options.estimateSize(i)\n\n        const end = start + size\n\n        const lane = furthestMeasurement\n          ? furthestMeasurement.lane\n          : i % this.options.lanes\n\n        measurements[i] = {\n          index: i,\n          start,\n          size,\n          end,\n          key,\n          lane,\n        }\n      }\n\n      this.measurementsCache = measurements\n\n      return measurements\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getMeasurements',\n      debug: () => this.options.debug,\n    },\n  )\n\n  calculateRange = memo(\n    () => [\n      this.getMeasurements(),\n      this.getSize(),\n      this.getScrollOffset(),\n      this.options.lanes,\n    ],\n    (measurements, outerSize, scrollOffset, lanes) => {\n      return (this.range =\n        measurements.length > 0 && outerSize > 0\n          ? calculateRange({\n              measurements,\n              outerSize,\n              scrollOffset,\n              lanes,\n            })\n          : null)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'calculateRange',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualIndexes = memo(\n    () => {\n      let startIndex: number | null = null\n      let endIndex: number | null = null\n      const range = this.calculateRange()\n      if (range) {\n        startIndex = range.startIndex\n        endIndex = range.endIndex\n      }\n      this.maybeNotify.updateDeps([this.isScrolling, startIndex, endIndex])\n      return [\n        this.options.rangeExtractor,\n        this.options.overscan,\n        this.options.count,\n        startIndex,\n        endIndex,\n      ]\n    },\n    (rangeExtractor, overscan, count, startIndex, endIndex) => {\n      return startIndex === null || endIndex === null\n        ? []\n        : rangeExtractor({\n            startIndex,\n            endIndex,\n            overscan,\n            count,\n          })\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualIndexes',\n      debug: () => this.options.debug,\n    },\n  )\n\n  indexFromElement = (node: TItemElement) => {\n    const attributeName = this.options.indexAttribute\n    const indexStr = node.getAttribute(attributeName)\n\n    if (!indexStr) {\n      console.warn(\n        `Missing attribute name '${attributeName}={index}' on measured element.`,\n      )\n      return -1\n    }\n\n    return parseInt(indexStr, 10)\n  }\n\n  private _measureElement = (\n    node: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n  ) => {\n    const index = this.indexFromElement(node)\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const key = item.key\n    const prevNode = this.elementsCache.get(key)\n\n    if (prevNode !== node) {\n      if (prevNode) {\n        this.observer.unobserve(prevNode)\n      }\n      this.observer.observe(node)\n      this.elementsCache.set(key, node)\n    }\n\n    if (node.isConnected) {\n      this.resizeItem(index, this.options.measureElement(node, entry, this))\n    }\n  }\n\n  resizeItem = (index: number, size: number) => {\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const itemSize = this.itemSizeCache.get(item.key) ?? item.size\n    const delta = size - itemSize\n\n    if (delta !== 0) {\n      if (\n        this.shouldAdjustScrollPositionOnItemSizeChange !== undefined\n          ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this)\n          : item.start < this.getScrollOffset() + this.scrollAdjustments\n      ) {\n        if (process.env.NODE_ENV !== 'production' && this.options.debug) {\n          console.info('correction', delta)\n        }\n\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: (this.scrollAdjustments += delta),\n          behavior: undefined,\n        })\n      }\n\n      this.pendingMeasuredCacheIndexes.push(item.index)\n      this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size))\n\n      this.notify(false)\n    }\n  }\n\n  measureElement = (node: TItemElement | null | undefined) => {\n    if (!node) {\n      this.elementsCache.forEach((cached, key) => {\n        if (!cached.isConnected) {\n          this.observer.unobserve(cached)\n          this.elementsCache.delete(key)\n        }\n      })\n      return\n    }\n\n    this._measureElement(node, undefined)\n  }\n\n  getVirtualItems = memo(\n    () => [this.getVirtualIndexes(), this.getMeasurements()],\n    (indexes, measurements) => {\n      const virtualItems: Array<VirtualItem> = []\n\n      for (let k = 0, len = indexes.length; k < len; k++) {\n        const i = indexes[k]!\n        const measurement = measurements[i]!\n\n        virtualItems.push(measurement)\n      }\n\n      return virtualItems\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualItems',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualItemForOffset = (offset: number) => {\n    const measurements = this.getMeasurements()\n    if (measurements.length === 0) {\n      return undefined\n    }\n    return notUndefined(\n      measurements[\n        findNearestBinarySearch(\n          0,\n          measurements.length - 1,\n          (index: number) => notUndefined(measurements[index]).start,\n          offset,\n        )\n      ],\n    )\n  }\n\n  getOffsetForAlignment = (\n    toOffset: number,\n    align: ScrollAlignment,\n    itemSize = 0,\n  ) => {\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      align = toOffset >= scrollOffset + size ? 'end' : 'start'\n    }\n\n    if (align === 'center') {\n      // When aligning to a particular item (e.g. with scrollToIndex),\n      // adjust offset by the size of the item to center on the item\n      toOffset += (itemSize - size) / 2\n    } else if (align === 'end') {\n      toOffset -= size\n    }\n\n    const scrollSizeProp = this.options.horizontal\n      ? 'scrollWidth'\n      : 'scrollHeight'\n    const scrollSize = this.scrollElement\n      ? 'document' in this.scrollElement\n        ? this.scrollElement.document.documentElement[scrollSizeProp]\n        : this.scrollElement[scrollSizeProp]\n      : 0\n\n    const maxOffset = scrollSize - size\n\n    return Math.max(Math.min(maxOffset, toOffset), 0)\n  }\n\n  getOffsetForIndex = (index: number, align: ScrollAlignment = 'auto') => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return undefined\n    }\n\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {\n        align = 'end'\n      } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {\n        align = 'start'\n      } else {\n        return [scrollOffset, align] as const\n      }\n    }\n\n    const toOffset =\n      align === 'end'\n        ? item.end + this.options.scrollPaddingEnd\n        : item.start - this.options.scrollPaddingStart\n\n    return [\n      this.getOffsetForAlignment(toOffset, align, item.size),\n      align,\n    ] as const\n  }\n\n  private isDynamicMode = () => this.elementsCache.size > 0\n\n  private cancelScrollToIndex = () => {\n    if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {\n      this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId)\n      this.scrollToIndexTimeoutId = null\n    }\n  }\n\n  scrollToOffset = (\n    toOffset: number,\n    { align = 'start', behavior }: ScrollToOffsetOptions = {},\n  ) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  scrollToIndex = (\n    index: number,\n    { align: initialAlign = 'auto', behavior }: ScrollToIndexOptions = {},\n  ) => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    const offsetAndAlign = this.getOffsetForIndex(index, initialAlign)\n    if (!offsetAndAlign) return\n\n    const [offset, align] = offsetAndAlign\n\n    this._scrollToOffset(offset, { adjustments: undefined, behavior })\n\n    if (behavior !== 'smooth' && this.isDynamicMode() && this.targetWindow) {\n      this.scrollToIndexTimeoutId = this.targetWindow.setTimeout(() => {\n        this.scrollToIndexTimeoutId = null\n\n        const elementInDOM = this.elementsCache.has(\n          this.options.getItemKey(index),\n        )\n\n        if (elementInDOM) {\n          const [latestOffset] = notUndefined(\n            this.getOffsetForIndex(index, align),\n          )\n\n          if (!approxEqual(latestOffset, this.getScrollOffset())) {\n            this.scrollToIndex(index, { align, behavior })\n          }\n        } else {\n          this.scrollToIndex(index, { align, behavior })\n        }\n      })\n    }\n  }\n\n  scrollBy = (delta: number, { behavior }: ScrollToOffsetOptions = {}) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getScrollOffset() + delta, {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  getTotalSize = () => {\n    const measurements = this.getMeasurements()\n\n    let end: number\n    // If there are no measurements, set the end to paddingStart\n    // If there is only one lane, use the last measurement's end\n    // Otherwise find the maximum end value among all measurements\n    if (measurements.length === 0) {\n      end = this.options.paddingStart\n    } else if (this.options.lanes === 1) {\n      end = measurements[measurements.length - 1]?.end ?? 0\n    } else {\n      const endByLane = Array<number | null>(this.options.lanes).fill(null)\n      let endIndex = measurements.length - 1\n      while (endIndex > 0 && endByLane.some((val) => val === null)) {\n        const item = measurements[endIndex]!\n        if (endByLane[item.lane] === null) {\n          endByLane[item.lane] = item.end\n        }\n\n        endIndex--\n      }\n\n      end = Math.max(...endByLane.filter((val): val is number => val !== null))\n    }\n\n    return Math.max(\n      end - this.options.scrollMargin + this.options.paddingEnd,\n      0,\n    )\n  }\n\n  private _scrollToOffset = (\n    offset: number,\n    {\n      adjustments,\n      behavior,\n    }: {\n      adjustments: number | undefined\n      behavior: ScrollBehavior | undefined\n    },\n  ) => {\n    this.options.scrollToFn(offset, { behavior, adjustments }, this)\n  }\n\n  measure = () => {\n    this.itemSizeCache = new Map()\n    this.notify(false)\n  }\n}\n\nconst findNearestBinarySearch = (\n  low: number,\n  high: number,\n  getCurrentValue: (i: number) => number,\n  value: number,\n) => {\n  while (low <= high) {\n    const middle = ((low + high) / 2) | 0\n    const currentValue = getCurrentValue(middle)\n\n    if (currentValue < value) {\n      low = middle + 1\n    } else if (currentValue > value) {\n      high = middle - 1\n    } else {\n      return middle\n    }\n  }\n\n  if (low > 0) {\n    return low - 1\n  } else {\n    return 0\n  }\n}\n\nfunction calculateRange({\n  measurements,\n  outerSize,\n  scrollOffset,\n  lanes,\n}: {\n  measurements: Array<VirtualItem>\n  outerSize: number\n  scrollOffset: number\n  lanes: number\n}) {\n  const lastIndex = measurements.length - 1\n  const getOffset = (index: number) => measurements[index]!.start\n\n  let startIndex = findNearestBinarySearch(\n    0,\n    lastIndex,\n    getOffset,\n    scrollOffset,\n  )\n  let endIndex = startIndex\n\n  if (lanes === 1) {\n    while (\n      endIndex < lastIndex &&\n      measurements[endIndex]!.end < scrollOffset + outerSize\n    ) {\n      endIndex++\n    }\n  } else if (lanes > 1) {\n    // Expand forward until we include the visible items from all lanes\n    // which are closer to the end of the virtualizer window\n    const endPerLane = Array(lanes).fill(0)\n    while (\n      endIndex < lastIndex &&\n      endPerLane.some((pos) => pos < scrollOffset + outerSize)\n    ) {\n      const item = measurements[endIndex]!\n      endPerLane[item.lane] = item.end\n      endIndex++\n    }\n\n    // Expand backward until we include all lanes' visible items\n    // closer to the top\n    const startPerLane = Array(lanes).fill(scrollOffset + outerSize)\n    while (startIndex > 0 && startPerLane.some((pos) => pos >= scrollOffset)) {\n      const item = measurements[startIndex]!\n      startPerLane[item.lane] = item.start\n      startIndex--\n    }\n\n    // Align startIndex to the beginning of its lane\n    startIndex = Math.max(0, startIndex - (startIndex % lanes))\n    // Align endIndex to the end of its lane\n    endIndex = Math.min(lastIndex, endIndex + (lanes - 1 - (endIndex % lanes)))\n  }\n\n  return { startIndex, endIndex }\n}\n", "import {\n  Virtualizer,\n  elementScroll,\n  observeElementOffset,\n  observeElementRect,\n  observeWindowOffset,\n  observeWindowRect,\n  windowScroll,\n} from '@tanstack/virtual-core'\nimport {\n  computed,\n  onScopeDispose,\n  shallowRef,\n  triggerRef,\n  unref,\n  watch,\n} from 'vue'\nimport type { PartialKeys, VirtualizerOptions } from '@tanstack/virtual-core'\nimport type { Ref } from 'vue'\n\nexport * from '@tanstack/virtual-core'\n\ntype MaybeRef<T> = T | Ref<T>\n\nfunction useVirtualizerBase<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n>(\n  options: MaybeRef<VirtualizerOptions<TScrollElement, TItemElement>>,\n): Ref<Virtualizer<TScrollElement, TItemElement>> {\n  const virtualizer = new Virtualizer(unref(options))\n  const state = shallowRef(virtualizer)\n\n  const cleanup = virtualizer._didMount()\n\n  watch(\n    () => unref(options).getScrollElement(),\n    (el) => {\n      if (el) {\n        virtualizer._willUpdate()\n      }\n    },\n    {\n      immediate: true,\n    },\n  )\n\n  watch(\n    () => unref(options),\n    (options) => {\n      virtualizer.setOptions({\n        ...options,\n        onChange: (instance, sync) => {\n          triggerRef(state)\n          options.onChange?.(instance, sync)\n        },\n      })\n\n      virtualizer._willUpdate()\n      triggerRef(state)\n    },\n    {\n      immediate: true,\n    },\n  )\n\n  onScopeDispose(cleanup)\n\n  return state\n}\n\nexport function useVirtualizer<\n  TScrollElement extends Element,\n  TItemElement extends Element,\n>(\n  options: MaybeRef<\n    PartialKeys<\n      VirtualizerOptions<TScrollElement, TItemElement>,\n      'observeElementRect' | 'observeElementOffset' | 'scrollToFn'\n    >\n  >,\n): Ref<Virtualizer<TScrollElement, TItemElement>> {\n  return useVirtualizerBase<TScrollElement, TItemElement>(\n    computed(() => ({\n      observeElementRect: observeElementRect,\n      observeElementOffset: observeElementOffset,\n      scrollToFn: elementScroll,\n      ...unref(options),\n    })),\n  )\n}\n\nexport function useWindowVirtualizer<TItemElement extends Element>(\n  options: MaybeRef<\n    PartialKeys<\n      VirtualizerOptions<Window, TItemElement>,\n      | 'observeElementRect'\n      | 'observeElementOffset'\n      | 'scrollToFn'\n      | 'getScrollElement'\n    >\n  >,\n): Ref<Virtualizer<Window, TItemElement>> {\n  return useVirtualizerBase<Window, TItemElement>(\n    computed(() => ({\n      getScrollElement: () => (typeof document !== 'undefined' ? window : null),\n      observeElementRect: observeWindowRect,\n      observeElementOffset: observeWindowOffset,\n      scrollToFn: windowScroll,\n      initialOffset: () =>\n        typeof document !== 'undefined' ? window.scrollY : 0,\n      ...unref(options),\n    })),\n  )\n}\n", "import{useVirtualizer as re}from\"@tanstack/vue-virtual\";import{cloneVNode as de,computed as m,defineComponent as H,Fragment as se,h as z,inject as ee,nextTick as N,onMounted as X,onUnmounted as fe,provide as te,reactive as ve,ref as k,toRaw as L,watch as J,watchEffect as Y}from\"vue\";import{useControllable as pe}from'../../hooks/use-controllable.js';import{useFrameDebounce as be}from'../../hooks/use-frame-debounce.js';import{useId as W}from'../../hooks/use-id.js';import{useOutsideClick as ce}from'../../hooks/use-outside-click.js';import{useResolveButtonType as me}from'../../hooks/use-resolve-button-type.js';import{useTrackedPointer as xe}from'../../hooks/use-tracked-pointer.js';import{useTreeWalker as ge}from'../../hooks/use-tree-walker.js';import{Features as Se,Hidden as Oe}from'../../internal/hidden.js';import{State as G,useOpenClosed as Ce,useOpenClosedProvider as Re}from'../../internal/open-closed.js';import{Keys as F}from'../../keyboard.js';import{MouseButton as ye}from'../../mouse.js';import{history as oe}from'../../utils/active-element-history.js';import{calculateActiveIndex as le,Focus as P}from'../../utils/calculate-active-index.js';import{disposables as ae}from'../../utils/disposables.js';import{dom as x}from'../../utils/dom.js';import{sortByDomNode as Te}from'../../utils/focus-management.js';import{objectToFormEntries as Ie}from'../../utils/form.js';import{match as _}from'../../utils/match.js';import{getOwnerDocument as he}from'../../utils/owner.js';import{isMobile as we}from'../../utils/platform.js';import{compact as Pe,Features as Q,omit as Z,render as U}from'../../utils/render.js';function De(a,h){return a===h}var Ee=(r=>(r[r.Open=0]=\"Open\",r[r.Closed=1]=\"Closed\",r))(Ee||{}),Ve=(r=>(r[r.Single=0]=\"Single\",r[r.Multi=1]=\"Multi\",r))(Ve||{}),ke=(y=>(y[y.Pointer=0]=\"Pointer\",y[y.Focus=1]=\"Focus\",y[y.Other=2]=\"Other\",y))(ke||{});let ne=Symbol(\"ComboboxContext\");function K(a){let h=ee(ne,null);if(h===null){let r=new Error(`<${a} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,K),r}return h}let ie=Symbol(\"VirtualContext\"),Ae=H({name:\"VirtualProvider\",setup(a,{slots:h}){let r=K(\"VirtualProvider\"),y=m(()=>{let c=x(r.optionsRef);if(!c)return{start:0,end:0};let f=window.getComputedStyle(c);return{start:parseFloat(f.paddingBlockStart||f.paddingTop),end:parseFloat(f.paddingBlockEnd||f.paddingBottom)}}),o=re(m(()=>({scrollPaddingStart:y.value.start,scrollPaddingEnd:y.value.end,count:r.virtual.value.options.length,estimateSize(){return 40},getScrollElement(){return x(r.optionsRef)},overscan:12}))),u=m(()=>{var c;return(c=r.virtual.value)==null?void 0:c.options}),e=k(0);return J([u],()=>{e.value+=1}),te(ie,r.virtual.value?o:null),()=>[z(\"div\",{style:{position:\"relative\",width:\"100%\",height:`${o.value.getTotalSize()}px`},ref:c=>{if(c){if(typeof process!=\"undefined\"&&process.env.JEST_WORKER_ID!==void 0||r.activationTrigger.value===0)return;r.activeOptionIndex.value!==null&&r.virtual.value.options.length>r.activeOptionIndex.value&&o.value.scrollToIndex(r.activeOptionIndex.value)}}},o.value.getVirtualItems().map(c=>de(h.default({option:r.virtual.value.options[c.index],open:r.comboboxState.value===0})[0],{key:`${e.value}-${c.index}`,\"data-index\":c.index,\"aria-setsize\":r.virtual.value.options.length,\"aria-posinset\":c.index+1,style:{position:\"absolute\",top:0,left:0,transform:`translateY(${c.start}px)`,overflowAnchor:\"none\"}})))]}}),lt=H({name:\"Combobox\",emits:{\"update:modelValue\":a=>!0},props:{as:{type:[Object,String],default:\"template\"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],nullable:!0,default:null},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},nullable:{type:Boolean,default:!1},multiple:{type:[Boolean],default:!1},immediate:{type:[Boolean],default:!1},virtual:{type:Object,default:null}},inheritAttrs:!1,setup(a,{slots:h,attrs:r,emit:y}){let o=k(1),u=k(null),e=k(null),c=k(null),f=k(null),S=k({static:!1,hold:!1}),v=k([]),d=k(null),D=k(2),E=k(!1);function w(t=n=>n){let n=d.value!==null?v.value[d.value]:null,s=t(v.value.slice()),b=s.length>0&&s[0].dataRef.order.value!==null?s.sort((C,A)=>C.dataRef.order.value-A.dataRef.order.value):Te(s,C=>x(C.dataRef.domRef)),O=n?b.indexOf(n):null;return O===-1&&(O=null),{options:b,activeOptionIndex:O}}let M=m(()=>a.multiple?1:0),$=m(()=>a.nullable),[B,p]=pe(m(()=>a.modelValue),t=>y(\"update:modelValue\",t),m(()=>a.defaultValue)),R=m(()=>B.value===void 0?_(M.value,{[1]:[],[0]:void 0}):B.value),V=null,i=null;function I(t){return _(M.value,{[0](){return p==null?void 0:p(t)},[1]:()=>{let n=L(l.value.value).slice(),s=L(t),b=n.findIndex(O=>l.compare(s,L(O)));return b===-1?n.push(s):n.splice(b,1),p==null?void 0:p(n)}})}let T=m(()=>{});J([T],([t],[n])=>{if(l.virtual.value&&t&&n&&d.value!==null){let s=t.indexOf(n[d.value]);s!==-1?d.value=s:d.value=null}});let l={comboboxState:o,value:R,mode:M,compare(t,n){if(typeof a.by==\"string\"){let s=a.by;return(t==null?void 0:t[s])===(n==null?void 0:n[s])}return a.by===null?De(t,n):a.by(t,n)},calculateIndex(t){return l.virtual.value?a.by===null?l.virtual.value.options.indexOf(t):l.virtual.value.options.findIndex(n=>l.compare(n,t)):v.value.findIndex(n=>l.compare(n.dataRef.value,t))},defaultValue:m(()=>a.defaultValue),nullable:$,immediate:m(()=>!1),virtual:m(()=>null),inputRef:e,labelRef:u,buttonRef:c,optionsRef:f,disabled:m(()=>a.disabled),options:v,change(t){p(t)},activeOptionIndex:m(()=>{if(E.value&&d.value===null&&(l.virtual.value?l.virtual.value.options.length>0:v.value.length>0)){if(l.virtual.value){let n=l.virtual.value.options.findIndex(s=>{var b;return!((b=l.virtual.value)!=null&&b.disabled(s))});if(n!==-1)return n}let t=v.value.findIndex(n=>!n.dataRef.disabled);if(t!==-1)return t}return d.value}),activationTrigger:D,optionsPropsRef:S,closeCombobox(){E.value=!1,!a.disabled&&o.value!==1&&(o.value=1,d.value=null)},openCombobox(){if(E.value=!0,!a.disabled&&o.value!==0){if(l.value.value){let t=l.calculateIndex(l.value.value);t!==-1&&(d.value=t)}o.value=0}},setActivationTrigger(t){D.value=t},goToOption(t,n,s){E.value=!1,V!==null&&cancelAnimationFrame(V),V=requestAnimationFrame(()=>{if(a.disabled||f.value&&!S.value.static&&o.value===1)return;if(l.virtual.value){d.value=t===P.Specific?n:le({focus:t},{resolveItems:()=>l.virtual.value.options,resolveActiveIndex:()=>{var C,A;return(A=(C=l.activeOptionIndex.value)!=null?C:l.virtual.value.options.findIndex(j=>{var q;return!((q=l.virtual.value)!=null&&q.disabled(j))}))!=null?A:null},resolveDisabled:C=>l.virtual.value.disabled(C),resolveId(){throw new Error(\"Function not implemented.\")}}),D.value=s!=null?s:2;return}let b=w();if(b.activeOptionIndex===null){let C=b.options.findIndex(A=>!A.dataRef.disabled);C!==-1&&(b.activeOptionIndex=C)}let O=t===P.Specific?n:le({focus:t},{resolveItems:()=>b.options,resolveActiveIndex:()=>b.activeOptionIndex,resolveId:C=>C.id,resolveDisabled:C=>C.dataRef.disabled});d.value=O,D.value=s!=null?s:2,v.value=b.options})},selectOption(t){let n=v.value.find(b=>b.id===t);if(!n)return;let{dataRef:s}=n;I(s.value)},selectActiveOption(){if(l.activeOptionIndex.value!==null){if(l.virtual.value)I(l.virtual.value.options[l.activeOptionIndex.value]);else{let{dataRef:t}=v.value[l.activeOptionIndex.value];I(t.value)}l.goToOption(P.Specific,l.activeOptionIndex.value)}},registerOption(t,n){let s=ve({id:t,dataRef:n});if(l.virtual.value){v.value.push(s);return}i&&cancelAnimationFrame(i);let b=w(O=>(O.push(s),O));d.value===null&&l.isSelected(n.value.value)&&(b.activeOptionIndex=b.options.indexOf(s)),v.value=b.options,d.value=b.activeOptionIndex,D.value=2,b.options.some(O=>!x(O.dataRef.domRef))&&(i=requestAnimationFrame(()=>{let O=w();v.value=O.options,d.value=O.activeOptionIndex}))},unregisterOption(t,n){if(V!==null&&cancelAnimationFrame(V),n&&(E.value=!0),l.virtual.value){v.value=v.value.filter(b=>b.id!==t);return}let s=w(b=>{let O=b.findIndex(C=>C.id===t);return O!==-1&&b.splice(O,1),b});v.value=s.options,d.value=s.activeOptionIndex,D.value=2},isSelected(t){return _(M.value,{[0]:()=>l.compare(L(l.value.value),L(t)),[1]:()=>L(l.value.value).some(n=>l.compare(L(n),L(t)))})},isActive(t){return d.value===l.calculateIndex(t)}};ce([e,c,f],()=>l.closeCombobox(),m(()=>o.value===0)),te(ne,l),Re(m(()=>_(o.value,{[0]:G.Open,[1]:G.Closed})));let g=m(()=>{var t;return(t=x(e))==null?void 0:t.closest(\"form\")});return X(()=>{J([g],()=>{if(!g.value||a.defaultValue===void 0)return;function t(){l.change(a.defaultValue)}return g.value.addEventListener(\"reset\",t),()=>{var n;(n=g.value)==null||n.removeEventListener(\"reset\",t)}},{immediate:!0})}),()=>{var C,A,j;let{name:t,disabled:n,form:s,...b}=a,O={open:o.value===0,disabled:n,activeIndex:l.activeOptionIndex.value,activeOption:l.activeOptionIndex.value===null?null:l.virtual.value?l.virtual.value.options[(C=l.activeOptionIndex.value)!=null?C:0]:(j=(A=l.options.value[l.activeOptionIndex.value])==null?void 0:A.dataRef.value)!=null?j:null,value:R.value};return z(se,[...t!=null&&R.value!=null?Ie({[t]:R.value}).map(([q,ue])=>z(Oe,Pe({features:Se.Hidden,key:q,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:s,disabled:n,name:q,value:ue}))):[],U({theirProps:{...r,...Z(b,[\"by\",\"defaultValue\",\"immediate\",\"modelValue\",\"multiple\",\"nullable\",\"onUpdate:modelValue\",\"virtual\"])},ourProps:{},slot:O,slots:h,attrs:r,name:\"Combobox\"})])}}}),at=H({name:\"ComboboxLabel\",props:{as:{type:[Object,String],default:\"label\"},id:{type:String,default:null}},setup(a,{attrs:h,slots:r}){var e;let y=(e=a.id)!=null?e:`headlessui-combobox-label-${W()}`,o=K(\"ComboboxLabel\");function u(){var c;(c=x(o.inputRef))==null||c.focus({preventScroll:!0})}return()=>{let c={open:o.comboboxState.value===0,disabled:o.disabled.value},{...f}=a,S={id:y,ref:o.labelRef,onClick:u};return U({ourProps:S,theirProps:f,slot:c,attrs:h,slots:r,name:\"ComboboxLabel\"})}}}),nt=H({name:\"ComboboxButton\",props:{as:{type:[Object,String],default:\"button\"},id:{type:String,default:null}},setup(a,{attrs:h,slots:r,expose:y}){var S;let o=(S=a.id)!=null?S:`headlessui-combobox-button-${W()}`,u=K(\"ComboboxButton\");y({el:u.buttonRef,$el:u.buttonRef});function e(v){u.disabled.value||(u.comboboxState.value===0?u.closeCombobox():(v.preventDefault(),u.openCombobox()),N(()=>{var d;return(d=x(u.inputRef))==null?void 0:d.focus({preventScroll:!0})}))}function c(v){switch(v.key){case F.ArrowDown:v.preventDefault(),v.stopPropagation(),u.comboboxState.value===1&&u.openCombobox(),N(()=>{var d;return(d=u.inputRef.value)==null?void 0:d.focus({preventScroll:!0})});return;case F.ArrowUp:v.preventDefault(),v.stopPropagation(),u.comboboxState.value===1&&(u.openCombobox(),N(()=>{u.value.value||u.goToOption(P.Last)})),N(()=>{var d;return(d=u.inputRef.value)==null?void 0:d.focus({preventScroll:!0})});return;case F.Escape:if(u.comboboxState.value!==0)return;v.preventDefault(),u.optionsRef.value&&!u.optionsPropsRef.value.static&&v.stopPropagation(),u.closeCombobox(),N(()=>{var d;return(d=u.inputRef.value)==null?void 0:d.focus({preventScroll:!0})});return}}let f=me(m(()=>({as:a.as,type:h.type})),u.buttonRef);return()=>{var E,w;let v={open:u.comboboxState.value===0,disabled:u.disabled.value,value:u.value.value},{...d}=a,D={ref:u.buttonRef,id:o,type:f.value,tabindex:\"-1\",\"aria-haspopup\":\"listbox\",\"aria-controls\":(E=x(u.optionsRef))==null?void 0:E.id,\"aria-expanded\":u.comboboxState.value===0,\"aria-labelledby\":u.labelRef.value?[(w=x(u.labelRef))==null?void 0:w.id,o].join(\" \"):void 0,disabled:u.disabled.value===!0?!0:void 0,onKeydown:c,onClick:e};return U({ourProps:D,theirProps:d,slot:v,attrs:h,slots:r,name:\"ComboboxButton\"})}}}),it=H({name:\"ComboboxInput\",props:{as:{type:[Object,String],default:\"input\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},displayValue:{type:Function},defaultValue:{type:String,default:void 0},id:{type:String,default:null}},emits:{change:a=>!0},setup(a,{emit:h,attrs:r,slots:y,expose:o}){var V;let u=(V=a.id)!=null?V:`headlessui-combobox-input-${W()}`,e=K(\"ComboboxInput\"),c=m(()=>he(x(e.inputRef))),f={value:!1};o({el:e.inputRef,$el:e.inputRef});function S(){e.change(null);let i=x(e.optionsRef);i&&(i.scrollTop=0),e.goToOption(P.Nothing)}let v=m(()=>{var I;let i=e.value.value;return x(e.inputRef)?typeof a.displayValue!=\"undefined\"&&i!==void 0?(I=a.displayValue(i))!=null?I:\"\":typeof i==\"string\"?i:\"\":\"\"});X(()=>{J([v,e.comboboxState,c],([i,I],[T,l])=>{if(f.value)return;let g=x(e.inputRef);g&&((l===0&&I===1||i!==T)&&(g.value=i),requestAnimationFrame(()=>{var s;if(f.value||!g||((s=c.value)==null?void 0:s.activeElement)!==g)return;let{selectionStart:t,selectionEnd:n}=g;Math.abs((n!=null?n:0)-(t!=null?t:0))===0&&t===0&&g.setSelectionRange(g.value.length,g.value.length)}))},{immediate:!0}),J([e.comboboxState],([i],[I])=>{if(i===0&&I===1){if(f.value)return;let T=x(e.inputRef);if(!T)return;let l=T.value,{selectionStart:g,selectionEnd:t,selectionDirection:n}=T;T.value=\"\",T.value=l,n!==null?T.setSelectionRange(g,t,n):T.setSelectionRange(g,t)}})});let d=k(!1);function D(){d.value=!0}function E(){ae().nextFrame(()=>{d.value=!1})}let w=be();function M(i){switch(f.value=!0,w(()=>{f.value=!1}),i.key){case F.Enter:if(f.value=!1,e.comboboxState.value!==0||d.value)return;if(i.preventDefault(),i.stopPropagation(),e.activeOptionIndex.value===null){e.closeCombobox();return}e.selectActiveOption(),e.mode.value===0&&e.closeCombobox();break;case F.ArrowDown:return f.value=!1,i.preventDefault(),i.stopPropagation(),_(e.comboboxState.value,{[0]:()=>e.goToOption(P.Next),[1]:()=>e.openCombobox()});case F.ArrowUp:return f.value=!1,i.preventDefault(),i.stopPropagation(),_(e.comboboxState.value,{[0]:()=>e.goToOption(P.Previous),[1]:()=>{e.openCombobox(),N(()=>{e.value.value||e.goToOption(P.Last)})}});case F.Home:if(i.shiftKey)break;return f.value=!1,i.preventDefault(),i.stopPropagation(),e.goToOption(P.First);case F.PageUp:return f.value=!1,i.preventDefault(),i.stopPropagation(),e.goToOption(P.First);case F.End:if(i.shiftKey)break;return f.value=!1,i.preventDefault(),i.stopPropagation(),e.goToOption(P.Last);case F.PageDown:return f.value=!1,i.preventDefault(),i.stopPropagation(),e.goToOption(P.Last);case F.Escape:if(f.value=!1,e.comboboxState.value!==0)return;i.preventDefault(),e.optionsRef.value&&!e.optionsPropsRef.value.static&&i.stopPropagation(),e.nullable.value&&e.mode.value===0&&e.value.value===null&&S(),e.closeCombobox();break;case F.Tab:if(f.value=!1,e.comboboxState.value!==0)return;e.mode.value===0&&e.activationTrigger.value!==1&&e.selectActiveOption(),e.closeCombobox();break}}function $(i){h(\"change\",i),e.nullable.value&&e.mode.value===0&&i.target.value===\"\"&&S(),e.openCombobox()}function B(i){var T,l,g;let I=(T=i.relatedTarget)!=null?T:oe.find(t=>t!==i.currentTarget);if(f.value=!1,!((l=x(e.optionsRef))!=null&&l.contains(I))&&!((g=x(e.buttonRef))!=null&&g.contains(I))&&e.comboboxState.value===0)return i.preventDefault(),e.mode.value===0&&(e.nullable.value&&e.value.value===null?S():e.activationTrigger.value!==1&&e.selectActiveOption()),e.closeCombobox()}function p(i){var T,l,g;let I=(T=i.relatedTarget)!=null?T:oe.find(t=>t!==i.currentTarget);(l=x(e.buttonRef))!=null&&l.contains(I)||(g=x(e.optionsRef))!=null&&g.contains(I)||e.disabled.value||e.immediate.value&&e.comboboxState.value!==0&&(e.openCombobox(),ae().nextFrame(()=>{e.setActivationTrigger(1)}))}let R=m(()=>{var i,I,T,l;return(l=(T=(I=a.defaultValue)!=null?I:e.defaultValue.value!==void 0?(i=a.displayValue)==null?void 0:i.call(a,e.defaultValue.value):null)!=null?T:e.defaultValue.value)!=null?l:\"\"});return()=>{var t,n,s,b,O,C,A;let i={open:e.comboboxState.value===0},{displayValue:I,onChange:T,...l}=a,g={\"aria-controls\":(t=e.optionsRef.value)==null?void 0:t.id,\"aria-expanded\":e.comboboxState.value===0,\"aria-activedescendant\":e.activeOptionIndex.value===null?void 0:e.virtual.value?(n=e.options.value.find(j=>!e.virtual.value.disabled(j.dataRef.value)&&e.compare(j.dataRef.value,e.virtual.value.options[e.activeOptionIndex.value])))==null?void 0:n.id:(s=e.options.value[e.activeOptionIndex.value])==null?void 0:s.id,\"aria-labelledby\":(C=(b=x(e.labelRef))==null?void 0:b.id)!=null?C:(O=x(e.buttonRef))==null?void 0:O.id,\"aria-autocomplete\":\"list\",id:u,onCompositionstart:D,onCompositionend:E,onKeydown:M,onInput:$,onFocus:p,onBlur:B,role:\"combobox\",type:(A=r.type)!=null?A:\"text\",tabIndex:0,ref:e.inputRef,defaultValue:R.value,disabled:e.disabled.value===!0?!0:void 0};return U({ourProps:g,theirProps:l,slot:i,attrs:r,slots:y,features:Q.RenderStrategy|Q.Static,name:\"ComboboxInput\"})}}}),ut=H({name:\"ComboboxOptions\",props:{as:{type:[Object,String],default:\"ul\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},hold:{type:[Boolean],default:!1}},setup(a,{attrs:h,slots:r,expose:y}){let o=K(\"ComboboxOptions\"),u=`headlessui-combobox-options-${W()}`;y({el:o.optionsRef,$el:o.optionsRef}),Y(()=>{o.optionsPropsRef.value.static=a.static}),Y(()=>{o.optionsPropsRef.value.hold=a.hold});let e=Ce(),c=m(()=>e!==null?(e.value&G.Open)===G.Open:o.comboboxState.value===0);ge({container:m(()=>x(o.optionsRef)),enabled:m(()=>o.comboboxState.value===0),accept(S){return S.getAttribute(\"role\")===\"option\"?NodeFilter.FILTER_REJECT:S.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(S){S.setAttribute(\"role\",\"none\")}});function f(S){S.preventDefault()}return()=>{var D,E,w;let S={open:o.comboboxState.value===0},v={\"aria-labelledby\":(w=(D=x(o.labelRef))==null?void 0:D.id)!=null?w:(E=x(o.buttonRef))==null?void 0:E.id,id:u,ref:o.optionsRef,role:\"listbox\",\"aria-multiselectable\":o.mode.value===1?!0:void 0,onMousedown:f},d=Z(a,[\"hold\"]);return U({ourProps:v,theirProps:d,slot:S,attrs:h,slots:o.virtual.value&&o.comboboxState.value===0?{...r,default:()=>[z(Ae,{},r.default)]}:r,features:Q.RenderStrategy|Q.Static,visible:c.value,name:\"ComboboxOptions\"})}}}),rt=H({name:\"ComboboxOption\",props:{as:{type:[Object,String],default:\"li\"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},order:{type:[Number],default:null}},setup(a,{slots:h,attrs:r,expose:y}){let o=K(\"ComboboxOption\"),u=`headlessui-combobox-option-${W()}`,e=k(null),c=m(()=>a.disabled);y({el:e,$el:e});let f=m(()=>{var p;return o.virtual.value?o.activeOptionIndex.value===o.calculateIndex(a.value):o.activeOptionIndex.value===null?!1:((p=o.options.value[o.activeOptionIndex.value])==null?void 0:p.id)===u}),S=m(()=>o.isSelected(a.value)),v=ee(ie,null),d=m(()=>({disabled:a.disabled,value:a.value,domRef:e,order:m(()=>a.order)}));X(()=>o.registerOption(u,d)),fe(()=>o.unregisterOption(u,f.value)),Y(()=>{let p=x(e);p&&(v==null||v.value.measureElement(p))}),Y(()=>{o.comboboxState.value===0&&f.value&&(o.virtual.value||o.activationTrigger.value!==0&&N(()=>{var p,R;return(R=(p=x(e))==null?void 0:p.scrollIntoView)==null?void 0:R.call(p,{block:\"nearest\"})}))});function D(p){p.preventDefault(),p.button===ye.Left&&(c.value||(o.selectOption(u),we()||requestAnimationFrame(()=>{var R;return(R=x(o.inputRef))==null?void 0:R.focus({preventScroll:!0})}),o.mode.value===0&&o.closeCombobox()))}function E(){var R;if(a.disabled||(R=o.virtual.value)!=null&&R.disabled(a.value))return o.goToOption(P.Nothing);let p=o.calculateIndex(a.value);o.goToOption(P.Specific,p)}let w=xe();function M(p){w.update(p)}function $(p){var V;if(!w.wasMoved(p)||a.disabled||(V=o.virtual.value)!=null&&V.disabled(a.value)||f.value)return;let R=o.calculateIndex(a.value);o.goToOption(P.Specific,R,0)}function B(p){var R;w.wasMoved(p)&&(a.disabled||(R=o.virtual.value)!=null&&R.disabled(a.value)||f.value&&(o.optionsPropsRef.value.hold||o.goToOption(P.Nothing)))}return()=>{let{disabled:p}=a,R={active:f.value,selected:S.value,disabled:p},V={id:u,ref:e,role:\"option\",tabIndex:p===!0?void 0:-1,\"aria-disabled\":p===!0?!0:void 0,\"aria-selected\":S.value,disabled:void 0,onMousedown:D,onFocus:E,onPointerenter:M,onMouseenter:M,onPointermove:$,onMousemove:$,onPointerleave:B,onMouseleave:B},i=Z(a,[\"order\",\"value\"]);return U({ourProps:V,theirProps:i,slot:R,attrs:r,slots:h,name:\"ComboboxOption\"})}}});export{lt as Combobox,nt as ComboboxButton,it as ComboboxInput,at as ComboboxLabel,rt as ComboboxOption,ut as ComboboxOptions};\n", "import{computed as p,ref as s}from\"vue\";function d(u,e,r){let i=s(r==null?void 0:r.value),f=p(()=>u.value!==void 0);return[p(()=>f.value?u.value:i.value),function(t){return f.value||(i.value=t),e==null?void 0:e(t)}]}export{d as useControllable};\n", "import{onUnmounted as s}from\"vue\";import{disposables as e}from'../utils/disposables.js';function i(){let o=e();return s(()=>o.dispose()),o}export{i as useDisposables};\n", "function t(e){typeof queueMicrotask==\"function\"?queueMicrotask(e):Promise.resolve().then(e).catch(o=>setTimeout(()=>{throw o}))}export{t as microTask};\n", "import{microTask as n}from'./micro-task.js';function o(){let a=[],s={addEventListener(e,t,r,i){return e.addEventListener(t,r,i),s.add(()=>e.removeEventListener(t,r,i))},requestAnimationFrame(...e){let t=requestAnimationFrame(...e);s.add(()=>cancelAnimationFrame(t))},nextFrame(...e){s.requestAnimationFrame(()=>{s.requestAnimationFrame(...e)})},setTimeout(...e){let t=setTimeout(...e);s.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return n(()=>{t.current&&e[0]()}),s.add(()=>{t.current=!1})},style(e,t,r){let i=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:r}),this.add(()=>{Object.assign(e.style,{[t]:i})})},group(e){let t=o();return e(t),this.add(()=>t.dispose())},add(e){return a.push(e),()=>{let t=a.indexOf(e);if(t>=0)for(let r of a.splice(t,1))r()}},dispose(){for(let e of a.splice(0))e()}};return s}export{o as disposables};\n", "import{useDisposables as r}from'./use-disposables.js';function t(){let e=r();return o=>{e.dispose(),e.nextFrame(o)}}export{t as useFrameDebounce};\n", "var r;import*as e from\"vue\";let n=Symbol(\"headlessui.useid\"),o=0;const i=(r=e.useId)!=null?r:function(){return e.inject(n,()=>`${++o}`)()};function s(t){e.provide(n,t)}export{s as provideUseId,i as useId};\n", "import{computed as s,ref as E}from\"vue\";import{dom as p}from'../utils/dom.js';import{FocusableMode as d,isFocusableElement as C}from'../utils/focus-management.js';import{isMobile as T}from'../utils/platform.js';import{useDocumentEvent as i}from'./use-document-event.js';import{useWindowEvent as M}from'./use-window-event.js';function w(f,m,l=s(()=>!0)){function a(e,r){if(!l.value||e.defaultPrevented)return;let t=r(e);if(t===null||!t.getRootNode().contains(t))return;let c=function o(n){return typeof n==\"function\"?o(n()):Array.isArray(n)||n instanceof Set?n:[n]}(f);for(let o of c){if(o===null)continue;let n=o instanceof HTMLElement?o:p(o);if(n!=null&&n.contains(t)||e.composed&&e.composedPath().includes(n))return}return!C(t,d.Loose)&&t.tabIndex!==-1&&e.preventDefault(),m(e,t)}let u=E(null);i(\"pointerdown\",e=>{var r,t;l.value&&(u.value=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),i(\"mousedown\",e=>{var r,t;l.value&&(u.value=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),i(\"click\",e=>{T()||u.value&&(a(e,()=>u.value),u.value=null)},!0),i(\"touchend\",e=>a(e,()=>e.target instanceof HTMLElement?e.target:null),!0),M(\"blur\",e=>a(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}export{w as useOutsideClick};\n", "function o(e){var l;if(e==null||e.value==null)return null;let n=(l=e.value.$el)!=null?l:e.value;return n instanceof Node?n:null}export{o as dom};\n", "import{nextTick as b}from\"vue\";import{match as M}from'./match.js';import{getOwnerDocument as f}from'./owner.js';let c=[\"[contentEditable=true]\",\"[tabindex]\",\"a[href]\",\"area[href]\",\"button:not([disabled])\",\"iframe\",\"input:not([disabled])\",\"select:not([disabled])\",\"textarea:not([disabled])\"].map(e=>`${e}:not([tabindex='-1'])`).join(\",\");var N=(n=>(n[n.First=1]=\"First\",n[n.Previous=2]=\"Previous\",n[n.Next=4]=\"Next\",n[n.Last=8]=\"Last\",n[n.WrapAround=16]=\"WrapAround\",n[n.NoScroll=32]=\"NoScroll\",n))(N||{}),T=(o=>(o[o.Error=0]=\"Error\",o[o.Overflow=1]=\"Overflow\",o[o.Success=2]=\"Success\",o[o.Underflow=3]=\"Underflow\",o))(T||{}),F=(t=>(t[t.Previous=-1]=\"Previous\",t[t.Next=1]=\"Next\",t))(F||{});function E(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(c)).sort((r,t)=>Math.sign((r.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var h=(t=>(t[t.Strict=0]=\"Strict\",t[t.Loose=1]=\"Loose\",t))(h||{});function w(e,r=0){var t;return e===((t=f(e))==null?void 0:t.body)?!1:M(r,{[0](){return e.matches(c)},[1](){let l=e;for(;l!==null;){if(l.matches(c))return!0;l=l.parentElement}return!1}})}function _(e){let r=f(e);b(()=>{r&&!w(r.activeElement,0)&&S(e)})}var y=(t=>(t[t.Keyboard=0]=\"Keyboard\",t[t.Mouse=1]=\"Mouse\",t))(y||{});typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"keydown\",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0),document.addEventListener(\"click\",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0));function S(e){e==null||e.focus({preventScroll:!0})}let H=[\"textarea\",\"input\"].join(\",\");function I(e){var r,t;return(t=(r=e==null?void 0:e.matches)==null?void 0:r.call(e,H))!=null?t:!1}function O(e,r=t=>t){return e.slice().sort((t,l)=>{let o=r(t),i=r(l);if(o===null||i===null)return 0;let n=o.compareDocumentPosition(i);return n&Node.DOCUMENT_POSITION_FOLLOWING?-1:n&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function v(e,r){return P(E(),r,{relativeTo:e})}function P(e,r,{sorted:t=!0,relativeTo:l=null,skipElements:o=[]}={}){var m;let i=(m=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e==null?void 0:e.ownerDocument)!=null?m:document,n=Array.isArray(e)?t?O(e):e:E(e);o.length>0&&n.length>1&&(n=n.filter(s=>!o.includes(s))),l=l!=null?l:i.activeElement;let x=(()=>{if(r&5)return 1;if(r&10)return-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),p=(()=>{if(r&1)return 0;if(r&2)return Math.max(0,n.indexOf(l))-1;if(r&4)return Math.max(0,n.indexOf(l))+1;if(r&8)return n.length-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),L=r&32?{preventScroll:!0}:{},a=0,d=n.length,u;do{if(a>=d||a+d<=0)return 0;let s=p+a;if(r&16)s=(s+d)%d;else{if(s<0)return 3;if(s>=d)return 1}u=n[s],u==null||u.focus(L),a+=x}while(u!==i.activeElement);return r&6&&I(u)&&u.select(),2}export{N as Focus,T as FocusResult,h as FocusableMode,S as focusElement,v as focusFrom,P as focusIn,E as getFocusableElements,w as isFocusableElement,_ as restoreFocusIfNecessary,O as sortByDomNode};\n", "function u(r,n,...a){if(r in n){let e=n[r];return typeof e==\"function\"?e(...a):e}let t=new Error(`Tried to handle \"${r}\" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(e=>`\"${e}\"`).join(\", \")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}export{u as match};\n", "var i=Object.defineProperty;var d=(t,e,r)=>e in t?i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var n=(t,e,r)=>(d(t,typeof e!=\"symbol\"?e+\"\":e,r),r);class s{constructor(){n(this,\"current\",this.detect());n(this,\"currentId\",0)}set(e){this.current!==e&&(this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current===\"server\"}get isClient(){return this.current===\"client\"}detect(){return typeof window==\"undefined\"||typeof document==\"undefined\"?\"server\":\"client\"}}let c=new s;export{c as env};\n", "import{dom as o}from'./dom.js';import{env as t}from'./env.js';function i(r){if(t.isServer)return null;if(r instanceof Node)return r.ownerDocument;if(r!=null&&r.hasOwnProperty(\"value\")){let n=o(r);if(n)return n.ownerDocument}return document}export{i as getOwnerDocument};\n", "function t(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function i(){return/Android/gi.test(window.navigator.userAgent)}function n(){return t()||i()}export{i as isAndroid,t as isIOS,n as isMobile};\n", "import{watchEffect as r}from\"vue\";import{env as m}from'../utils/env.js';function u(e,t,n){m.isServer||r(o=>{document.addEventListener(e,t,n),o(()=>document.removeEventListener(e,t,n))})}export{u as useDocumentEvent};\n", "import{watchEffect as i}from\"vue\";import{env as r}from'../utils/env.js';function w(e,n,t){r.isServer||i(o=>{window.addEventListener(e,n,t),o(()=>window.removeEventListener(e,n,t))})}export{w as useWindowEvent};\n", "import{onMounted as i,ref as f,watchEffect as l}from\"vue\";import{dom as o}from'../utils/dom.js';function r(t,e){if(t)return t;let n=e!=null?e:\"button\";if(typeof n==\"string\"&&n.toLowerCase()===\"button\")return\"button\"}function s(t,e){let n=f(r(t.value.type,t.value.as));return i(()=>{n.value=r(t.value.type,t.value.as)}),l(()=>{var u;n.value||o(e)&&o(e)instanceof HTMLButtonElement&&!((u=o(e))!=null&&u.hasAttribute(\"type\"))&&(n.value=\"button\")}),n}export{s as useResolveButtonType};\n", "import{ref as o}from\"vue\";function r(e){return[e.screenX,e.screenY]}function u(){let e=o([-1,-1]);return{wasMoved(n){let t=r(n);return e.value[0]===t[0]&&e.value[1]===t[1]?!1:(e.value=t,!0)},update(n){e.value=r(n)}}}export{u as useTrackedPointer};\n", "import{watchEffect as p}from\"vue\";import{getOwnerDocument as u}from'../utils/owner.js';function i({container:e,accept:t,walk:d,enabled:o}){p(()=>{let r=e.value;if(!r||o!==void 0&&!o.value)return;let l=u(e);if(!l)return;let c=Object.assign(f=>t(f),{acceptNode:t}),n=l.createTreeWalker(r,NodeFilter.SHOW_ELEMENT,c,!1);for(;n.nextNode();)d(n.currentNode)})}export{i as useTreeWalker};\n", "import{defineComponent as a}from\"vue\";import{render as p}from'../utils/render.js';var u=(e=>(e[e.None=1]=\"None\",e[e.Focusable=2]=\"Focusable\",e[e.Hidden=4]=\"Hidden\",e))(u||{});let f=a({name:\"Hidden\",props:{as:{type:[Object,String],default:\"div\"},features:{type:Number,default:1}},setup(t,{slots:n,attrs:i}){return()=>{var r;let{features:e,...d}=t,o={\"aria-hidden\":(e&2)===2?!0:(r=d[\"aria-hidden\"])!=null?r:void 0,hidden:(e&4)===4?!0:void 0,style:{position:\"fixed\",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:\"hidden\",clip:\"rect(0, 0, 0, 0)\",whiteSpace:\"nowrap\",borderWidth:\"0\",...(e&4)===4&&(e&2)!==2&&{display:\"none\"}}};return p({ourProps:o,theirProps:d,slot:{},attrs:i,slots:n,name:\"Hidden\"})}}});export{u as Features,f as Hidden};\n", "import{cloneVNode as O,Fragment as x,h as k}from\"vue\";import{match as w}from'./match.js';var N=(o=>(o[o.None=0]=\"None\",o[o.RenderStrategy=1]=\"RenderStrategy\",o[o.Static=2]=\"Static\",o))(N||{}),S=(e=>(e[e.Unmount=0]=\"Unmount\",e[e.Hidden=1]=\"Hidden\",e))(S||{});function A({visible:r=!0,features:t=0,ourProps:e,theirProps:o,...i}){var a;let n=j(o,e),l=Object.assign(i,{props:n});if(r||t&2&&n.static)return y(l);if(t&1){let d=(a=n.unmount)==null||a?0:1;return w(d,{[0](){return null},[1](){return y({...i,props:{...n,hidden:!0,style:{display:\"none\"}}})}})}return y(l)}function y({props:r,attrs:t,slots:e,slot:o,name:i}){var m,h;let{as:n,...l}=T(r,[\"unmount\",\"static\"]),a=(m=e.default)==null?void 0:m.call(e,o),d={};if(o){let u=!1,c=[];for(let[p,f]of Object.entries(o))typeof f==\"boolean\"&&(u=!0),f===!0&&c.push(p);u&&(d[\"data-headlessui-state\"]=c.join(\" \"))}if(n===\"template\"){if(a=b(a!=null?a:[]),Object.keys(l).length>0||Object.keys(t).length>0){let[u,...c]=a!=null?a:[];if(!v(u)||c.length>0)throw new Error(['Passing props on \"template\"!',\"\",`The current component <${i} /> is rendering a \"template\".`,\"However we need to passthrough the following props:\",Object.keys(l).concat(Object.keys(t)).map(s=>s.trim()).filter((s,g,R)=>R.indexOf(s)===g).sort((s,g)=>s.localeCompare(g)).map(s=>`  - ${s}`).join(`\n`),\"\",\"You can apply a few solutions:\",['Add an `as=\"...\"` prop, to ensure that we render an actual element instead of a \"template\".',\"Render a single element as the child so that we can forward the props onto that element.\"].map(s=>`  - ${s}`).join(`\n`)].join(`\n`));let p=j((h=u.props)!=null?h:{},l,d),f=O(u,p,!0);for(let s in p)s.startsWith(\"on\")&&(f.props||(f.props={}),f.props[s]=p[s]);return f}return Array.isArray(a)&&a.length===1?a[0]:a}return k(n,Object.assign({},l,d),{default:()=>a})}function b(r){return r.flatMap(t=>t.type===x?b(t.children):[t])}function j(...r){var o;if(r.length===0)return{};if(r.length===1)return r[0];let t={},e={};for(let i of r)for(let n in i)n.startsWith(\"on\")&&typeof i[n]==\"function\"?((o=e[n])!=null||(e[n]=[]),e[n].push(i[n])):t[n]=i[n];if(t.disabled||t[\"aria-disabled\"])return Object.assign(t,Object.fromEntries(Object.keys(e).map(i=>[i,void 0])));for(let i in e)Object.assign(t,{[i](n,...l){let a=e[i];for(let d of a){if(n instanceof Event&&n.defaultPrevented)return;d(n,...l)}}});return t}function E(r){let t=Object.assign({},r);for(let e in t)t[e]===void 0&&delete t[e];return t}function T(r,t=[]){let e=Object.assign({},r);for(let o of t)o in e&&delete e[o];return e}function v(r){return r==null?!1:typeof r.type==\"string\"||typeof r.type==\"object\"||typeof r.type==\"function\"}export{N as Features,S as RenderStrategy,E as compact,T as omit,A as render};\n", "import{inject as p,provide as r}from\"vue\";let n=Symbol(\"Context\");var i=(e=>(e[e.Open=1]=\"Open\",e[e.Closed=2]=\"Closed\",e[e.Closing=4]=\"Closing\",e[e.Opening=8]=\"Opening\",e))(i||{});function s(){return l()!==null}function l(){return p(n,null)}function t(o){r(n,o)}export{i as State,s as hasOpenClosed,l as useOpenClosed,t as useOpenClosedProvider};\n", "var o=(r=>(r.<PERSON>=\" \",r.<PERSON><PERSON>=\"Enter\",r.<PERSON>=\"Escape\",r.<PERSON>space=\"Backspace\",r.Delete=\"Delete\",r.<PERSON>=\"ArrowLeft\",r.<PERSON>p=\"ArrowUp\",r.<PERSON>=\"ArrowRight\",r.<PERSON>=\"ArrowDown\",r.Home=\"Home\",r.End=\"End\",r.PageUp=\"PageUp\",r.PageDown=\"PageDown\",r.Tab=\"Tab\",r))(o||{});export{o as Keys};\n", "var g=(f=>(f[f.Left=0]=\"Left\",f[f.Right=2]=\"Right\",f))(g||{});export{g as MouseButton};\n", "function t(n){function e(){document.readyState!==\"loading\"&&(n(),document.removeEventListener(\"DOMContentLoaded\",e))}typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"DOMContentLoaded\",e),e())}export{t as onDocumentReady};\n", "import{onDocumentReady as d}from'./document-ready.js';let t=[];d(()=>{function e(n){n.target instanceof HTMLElement&&n.target!==document.body&&t[0]!==n.target&&(t.unshift(n.target),t=t.filter(r=>r!=null&&r.isConnected),t.splice(10))}window.addEventListener(\"click\",e,{capture:!0}),window.addEventListener(\"mousedown\",e,{capture:!0}),window.addEventListener(\"focus\",e,{capture:!0}),document.body.addEventListener(\"click\",e,{capture:!0}),document.body.addEventListener(\"mousedown\",e,{capture:!0}),document.body.addEventListener(\"focus\",e,{capture:!0})});export{t as history};\n", "function u(l){throw new Error(\"Unexpected object: \"+l)}var c=(i=>(i[i.First=0]=\"First\",i[i.Previous=1]=\"Previous\",i[i.Next=2]=\"Next\",i[i.Last=3]=\"Last\",i[i.Specific=4]=\"Specific\",i[i.Nothing=5]=\"Nothing\",i))(c||{});function f(l,n){let t=n.resolveItems();if(t.length<=0)return null;let r=n.resolveActiveIndex(),s=r!=null?r:-1;switch(l.focus){case 0:{for(let e=0;e<t.length;++e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 1:{s===-1&&(s=t.length);for(let e=s-1;e>=0;--e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 2:{for(let e=s+1;e<t.length;++e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 3:{for(let e=t.length-1;e>=0;--e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 4:{for(let e=0;e<t.length;++e)if(n.resolveId(t[e],e,t)===l.id)return e;return r}case 5:return null;default:u(l)}}export{c as Focus,f as calculateActiveIndex};\n", "function e(i={},s=null,t=[]){for(let[r,n]of Object.entries(i))o(t,f(s,r),n);return t}function f(i,s){return i?i+\"[\"+s+\"]\":s}function o(i,s,t){if(Array.isArray(t))for(let[r,n]of t.entries())o(i,f(s,r.toString()),n);else t instanceof Date?i.push([s,t.toISOString()]):typeof t==\"boolean\"?i.push([s,t?\"1\":\"0\"]):typeof t==\"string\"?i.push([s,t]):typeof t==\"number\"?i.push([s,`${t}`]):t==null?i.push([s,\"\"]):e(t,s,i)}function p(i){var t,r;let s=(t=i==null?void 0:i.form)!=null?t:i.closest(\"form\");if(s){for(let n of s.elements)if(n!==i&&(n.tagName===\"INPUT\"&&n.type===\"submit\"||n.tagName===\"BUTTON\"&&n.type===\"submit\"||n.nodeName===\"INPUT\"&&n.type===\"image\")){n.click();return}(r=s.requestSubmit)==null||r.call(s)}}export{p as attemptSubmit,e as objectToFormEntries};\n", "import{computed as o,defineComponent as O,h as v,inject as Y,nextTick as se,onMounted as $,onUnmounted as pe,provide as de,ref as y,watchEffect as fe}from\"vue\";import{FocusTrap as P}from'../../components/focus-trap/focus-trap.js';import{useDocumentOverflowLockedEffect as ge}from'../../hooks/document-overflow/use-document-overflow.js';import{useEventListener as ce}from'../../hooks/use-event-listener.js';import{useId as b}from'../../hooks/use-id.js';import{useInert as _}from'../../hooks/use-inert.js';import{useOutsideClick as ve}from'../../hooks/use-outside-click.js';import{useRootContainers as me}from'../../hooks/use-root-containers.js';import{State as I,useOpenClosed as De}from'../../internal/open-closed.js';import{ForcePortalRoot as F}from'../../internal/portal-force-root.js';import{StackMessage as z,useStackProvider as ye}from'../../internal/stack-context.js';import{Keys as Se}from'../../keyboard.js';import{dom as j}from'../../utils/dom.js';import{match as G}from'../../utils/match.js';import{getOwnerDocument as he}from'../../utils/owner.js';import{Features as V,render as C}from'../../utils/render.js';import{Description as Oe,useDescriptions as Pe}from'../description/description.js';import{Portal as J,PortalGroup as be,useNestedPortals as Ce}from'../portal/portal.js';var Te=(l=>(l[l.Open=0]=\"Open\",l[l.Closed=1]=\"Closed\",l))(Te||{});let H=Symbol(\"DialogContext\");function T(t){let i=Y(H,null);if(i===null){let l=new Error(`<${t} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,T),l}return i}let A=\"DC8F892D-2EBD-447C-A4C8-A03058436FF4\",Ye=O({name:\"Dialog\",inheritAttrs:!1,props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:A},initialFocus:{type:Object,default:null},id:{type:String,default:null},role:{type:String,default:\"dialog\"}},emits:{close:t=>!0},setup(t,{emit:i,attrs:l,slots:p,expose:s}){var q,W;let n=(q=t.id)!=null?q:`headlessui-dialog-${b()}`,u=y(!1);$(()=>{u.value=!0});let r=!1,g=o(()=>t.role===\"dialog\"||t.role===\"alertdialog\"?t.role:(r||(r=!0,console.warn(`Invalid role [${g}] passed to <Dialog />. Only \\`dialog\\` and and \\`alertdialog\\` are supported. Using \\`dialog\\` instead.`)),\"dialog\")),D=y(0),S=De(),R=o(()=>t.open===A&&S!==null?(S.value&I.Open)===I.Open:t.open),m=y(null),E=o(()=>he(m));if(s({el:m,$el:m}),!(t.open!==A||S!==null))throw new Error(\"You forgot to provide an `open` prop to the `Dialog`.\");if(typeof R.value!=\"boolean\")throw new Error(`You provided an \\`open\\` prop to the \\`Dialog\\`, but the value is not a boolean. Received: ${R.value===A?void 0:t.open}`);let c=o(()=>u.value&&R.value?0:1),k=o(()=>c.value===0),w=o(()=>D.value>1),N=Y(H,null)!==null,[Q,X]=Ce(),{resolveContainers:B,mainTreeNodeRef:K,MainTreeNode:Z}=me({portals:Q,defaultContainers:[o(()=>{var e;return(e=h.panelRef.value)!=null?e:m.value})]}),ee=o(()=>w.value?\"parent\":\"leaf\"),U=o(()=>S!==null?(S.value&I.Closing)===I.Closing:!1),te=o(()=>N||U.value?!1:k.value),le=o(()=>{var e,a,d;return(d=Array.from((a=(e=E.value)==null?void 0:e.querySelectorAll(\"body > *\"))!=null?a:[]).find(f=>f.id===\"headlessui-portal-root\"?!1:f.contains(j(K))&&f instanceof HTMLElement))!=null?d:null});_(le,te);let ae=o(()=>w.value?!0:k.value),oe=o(()=>{var e,a,d;return(d=Array.from((a=(e=E.value)==null?void 0:e.querySelectorAll(\"[data-headlessui-portal]\"))!=null?a:[]).find(f=>f.contains(j(K))&&f instanceof HTMLElement))!=null?d:null});_(oe,ae),ye({type:\"Dialog\",enabled:o(()=>c.value===0),element:m,onUpdate:(e,a)=>{if(a===\"Dialog\")return G(e,{[z.Add]:()=>D.value+=1,[z.Remove]:()=>D.value-=1})}});let re=Pe({name:\"DialogDescription\",slot:o(()=>({open:R.value}))}),M=y(null),h={titleId:M,panelRef:y(null),dialogState:c,setTitleId(e){M.value!==e&&(M.value=e)},close(){i(\"close\",!1)}};de(H,h);let ne=o(()=>!(!k.value||w.value));ve(B,(e,a)=>{e.preventDefault(),h.close(),se(()=>a==null?void 0:a.focus())},ne);let ie=o(()=>!(w.value||c.value!==0));ce((W=E.value)==null?void 0:W.defaultView,\"keydown\",e=>{ie.value&&(e.defaultPrevented||e.key===Se.Escape&&(e.preventDefault(),e.stopPropagation(),h.close()))});let ue=o(()=>!(U.value||c.value!==0||N));return ge(E,ue,e=>{var a;return{containers:[...(a=e.containers)!=null?a:[],B]}}),fe(e=>{if(c.value!==0)return;let a=j(m);if(!a)return;let d=new ResizeObserver(f=>{for(let L of f){let x=L.target.getBoundingClientRect();x.x===0&&x.y===0&&x.width===0&&x.height===0&&h.close()}});d.observe(a),e(()=>d.disconnect())}),()=>{let{open:e,initialFocus:a,...d}=t,f={...l,ref:m,id:n,role:g.value,\"aria-modal\":c.value===0?!0:void 0,\"aria-labelledby\":M.value,\"aria-describedby\":re.value},L={open:c.value===0};return v(F,{force:!0},()=>[v(J,()=>v(be,{target:m.value},()=>v(F,{force:!1},()=>v(P,{initialFocus:a,containers:B,features:k.value?G(ee.value,{parent:P.features.RestoreFocus,leaf:P.features.All&~P.features.FocusLock}):P.features.None},()=>v(X,{},()=>C({ourProps:f,theirProps:{...d,...l},slot:L,attrs:l,slots:p,visible:c.value===0,features:V.RenderStrategy|V.Static,name:\"Dialog\"})))))),v(Z)])}}}),_e=O({name:\"DialogOverlay\",props:{as:{type:[Object,String],default:\"div\"},id:{type:String,default:null}},setup(t,{attrs:i,slots:l}){var u;let p=(u=t.id)!=null?u:`headlessui-dialog-overlay-${b()}`,s=T(\"DialogOverlay\");function n(r){r.target===r.currentTarget&&(r.preventDefault(),r.stopPropagation(),s.close())}return()=>{let{...r}=t;return C({ourProps:{id:p,\"aria-hidden\":!0,onClick:n},theirProps:r,slot:{open:s.dialogState.value===0},attrs:i,slots:l,name:\"DialogOverlay\"})}}}),ze=O({name:\"DialogBackdrop\",props:{as:{type:[Object,String],default:\"div\"},id:{type:String,default:null}},inheritAttrs:!1,setup(t,{attrs:i,slots:l,expose:p}){var r;let s=(r=t.id)!=null?r:`headlessui-dialog-backdrop-${b()}`,n=T(\"DialogBackdrop\"),u=y(null);return p({el:u,$el:u}),$(()=>{if(n.panelRef.value===null)throw new Error(\"A <DialogBackdrop /> component is being used, but a <DialogPanel /> component is missing.\")}),()=>{let{...g}=t,D={id:s,ref:u,\"aria-hidden\":!0};return v(F,{force:!0},()=>v(J,()=>C({ourProps:D,theirProps:{...i,...g},slot:{open:n.dialogState.value===0},attrs:i,slots:l,name:\"DialogBackdrop\"})))}}}),Ge=O({name:\"DialogPanel\",props:{as:{type:[Object,String],default:\"div\"},id:{type:String,default:null}},setup(t,{attrs:i,slots:l,expose:p}){var r;let s=(r=t.id)!=null?r:`headlessui-dialog-panel-${b()}`,n=T(\"DialogPanel\");p({el:n.panelRef,$el:n.panelRef});function u(g){g.stopPropagation()}return()=>{let{...g}=t,D={id:s,ref:n.panelRef,onClick:u};return C({ourProps:D,theirProps:g,slot:{open:n.dialogState.value===0},attrs:i,slots:l,name:\"DialogPanel\"})}}}),Ve=O({name:\"DialogTitle\",props:{as:{type:[Object,String],default:\"h2\"},id:{type:String,default:null}},setup(t,{attrs:i,slots:l}){var n;let p=(n=t.id)!=null?n:`headlessui-dialog-title-${b()}`,s=T(\"DialogTitle\");return $(()=>{s.setTitleId(p),pe(()=>s.setTitleId(null))}),()=>{let{...u}=t;return C({ourProps:{id:p},theirProps:u,slot:{open:s.dialogState.value===0},attrs:i,slots:l,name:\"DialogTitle\"})}}}),Je=Oe;export{Ye as Dialog,ze as DialogBackdrop,Je as DialogDescription,_e as DialogOverlay,Ge as DialogPanel,Ve as DialogTitle};\n", "import{computed as L,defineComponent as I,Fragment as j,h as R,onMounted as M,onUnmounted as h,ref as E,watch as g,watchEffect as K}from\"vue\";import{useEventListener as U}from'../../hooks/use-event-listener.js';import{Direction as y,useTabDirection as _}from'../../hooks/use-tab-direction.js';import{Features as k,Hidden as D}from'../../internal/hidden.js';import{history as C}from'../../utils/active-element-history.js';import{dom as c}from'../../utils/dom.js';import{Focus as v,focusElement as p,focusIn as b,FocusResult as q}from'../../utils/focus-management.js';import{match as P}from'../../utils/match.js';import{microTask as S}from'../../utils/micro-task.js';import{getOwnerDocument as x}from'../../utils/owner.js';import{render as G}from'../../utils/render.js';function B(t){if(!t)return new Set;if(typeof t==\"function\")return new Set(t());let n=new Set;for(let r of t.value){let l=c(r);l instanceof HTMLElement&&n.add(l)}return n}var A=(e=>(e[e.None=1]=\"None\",e[e.InitialFocus=2]=\"InitialFocus\",e[e.TabLock=4]=\"TabLock\",e[e.FocusLock=8]=\"FocusLock\",e[e.RestoreFocus=16]=\"RestoreFocus\",e[e.All=30]=\"All\",e))(A||{});let ue=Object.assign(I({name:\"FocusTrap\",props:{as:{type:[Object,String],default:\"div\"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:E(new Set)}},inheritAttrs:!1,setup(t,{attrs:n,slots:r,expose:l}){let o=E(null);l({el:o,$el:o});let i=L(()=>x(o)),e=E(!1);M(()=>e.value=!0),h(()=>e.value=!1),$({ownerDocument:i},L(()=>e.value&&Boolean(t.features&16)));let m=z({ownerDocument:i,container:o,initialFocus:L(()=>t.initialFocus)},L(()=>e.value&&Boolean(t.features&2)));J({ownerDocument:i,container:o,containers:t.containers,previousActiveElement:m},L(()=>e.value&&Boolean(t.features&8)));let f=_();function a(u){let T=c(o);if(!T)return;(w=>w())(()=>{P(f.value,{[y.Forwards]:()=>{b(T,v.First,{skipElements:[u.relatedTarget]})},[y.Backwards]:()=>{b(T,v.Last,{skipElements:[u.relatedTarget]})}})})}let s=E(!1);function F(u){u.key===\"Tab\"&&(s.value=!0,requestAnimationFrame(()=>{s.value=!1}))}function H(u){if(!e.value)return;let T=B(t.containers);c(o)instanceof HTMLElement&&T.add(c(o));let d=u.relatedTarget;d instanceof HTMLElement&&d.dataset.headlessuiFocusGuard!==\"true\"&&(N(T,d)||(s.value?b(c(o),P(f.value,{[y.Forwards]:()=>v.Next,[y.Backwards]:()=>v.Previous})|v.WrapAround,{relativeTo:u.target}):u.target instanceof HTMLElement&&p(u.target)))}return()=>{let u={},T={ref:o,onKeydown:F,onFocusout:H},{features:d,initialFocus:w,containers:Q,...O}=t;return R(j,[Boolean(d&4)&&R(D,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:a,features:k.Focusable}),G({ourProps:T,theirProps:{...n,...O},slot:u,attrs:n,slots:r,name:\"FocusTrap\"}),Boolean(d&4)&&R(D,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:a,features:k.Focusable})])}}}),{features:A});function W(t){let n=E(C.slice());return g([t],([r],[l])=>{l===!0&&r===!1?S(()=>{n.value.splice(0)}):l===!1&&r===!0&&(n.value=C.slice())},{flush:\"post\"}),()=>{var r;return(r=n.value.find(l=>l!=null&&l.isConnected))!=null?r:null}}function $({ownerDocument:t},n){let r=W(n);M(()=>{K(()=>{var l,o;n.value||((l=t.value)==null?void 0:l.activeElement)===((o=t.value)==null?void 0:o.body)&&p(r())},{flush:\"post\"})}),h(()=>{n.value&&p(r())})}function z({ownerDocument:t,container:n,initialFocus:r},l){let o=E(null),i=E(!1);return M(()=>i.value=!0),h(()=>i.value=!1),M(()=>{g([n,r,l],(e,m)=>{if(e.every((a,s)=>(m==null?void 0:m[s])===a)||!l.value)return;let f=c(n);f&&S(()=>{var F,H;if(!i.value)return;let a=c(r),s=(F=t.value)==null?void 0:F.activeElement;if(a){if(a===s){o.value=s;return}}else if(f.contains(s)){o.value=s;return}a?p(a):b(f,v.First|v.NoScroll)===q.Error&&console.warn(\"There are no focusable elements inside the <FocusTrap />\"),o.value=(H=t.value)==null?void 0:H.activeElement})},{immediate:!0,flush:\"post\"})}),o}function J({ownerDocument:t,container:n,containers:r,previousActiveElement:l},o){var i;U((i=t.value)==null?void 0:i.defaultView,\"focus\",e=>{if(!o.value)return;let m=B(r);c(n)instanceof HTMLElement&&m.add(c(n));let f=l.value;if(!f)return;let a=e.target;a&&a instanceof HTMLElement?N(m,a)?(l.value=a,p(a)):(e.preventDefault(),e.stopPropagation(),p(f)):p(l.value)},!0)}function N(t,n){for(let r of t)if(r.contains(n))return!0;return!1}export{ue as FocusTrap};\n", "import{watchEffect as i}from\"vue\";import{env as a}from'../utils/env.js';function E(n,e,o,r){a.isServer||i(t=>{n=n!=null?n:window,n.addEventListener(e,o,r),t(()=>n.removeEventListener(e,o,r))})}export{E as useEventListener};\n", "import{ref as a}from\"vue\";import{useWindowEvent as t}from'./use-window-event.js';var d=(r=>(r[r.Forwards=0]=\"Forwards\",r[r.Backwards=1]=\"Backwards\",r))(d||{});function n(){let o=a(0);return t(\"keydown\",e=>{e.key===\"Tab\"&&(o.value=e.shiftKey?1:0)}),o}export{d as Direction,n as useTabDirection};\n", "import{computed as p,watch as s}from\"vue\";import{useStore as v}from'../../hooks/use-store.js';import{overflows as u}from'./overflow-store.js';function d(t,a,n){let i=v(u),l=p(()=>{let e=t.value?i.value.get(t.value):void 0;return e?e.count>0:!1});return s([t,a],([e,m],[r],o)=>{if(!e||!m)return;u.dispatch(\"PUSH\",e,n);let f=!1;o(()=>{f||(u.dispatch(\"POP\",r!=null?r:e,n),f=!0)})},{immediate:!0}),l}export{d as useDocumentOverflowLockedEffect};\n", "import{onUnmounted as o,shallowRef as n}from\"vue\";function m(t){let e=n(t.getSnapshot());return o(t.subscribe(()=>{e.value=t.getSnapshot()})),e}export{m as useStore};\n", "function a(o,r){let t=o(),n=new Set;return{getSnapshot(){return t},subscribe(e){return n.add(e),()=>n.delete(e)},dispatch(e,...s){let i=r[e].call(t,...s);i&&(t=i,n.forEach(c=>c()))}}}export{a as createStore};\n", "function c(){let o;return{before({doc:e}){var l;let n=e.documentElement;o=((l=e.defaultView)!=null?l:window).innerWidth-n.clientWidth},after({doc:e,d:n}){let t=e.documentElement,l=t.clientWidth-t.offsetWidth,r=o-l;n.style(t,\"paddingRight\",`${r}px`)}}}export{c as adjustScrollbarPadding};\n", "import{disposables as m}from'../../utils/disposables.js';import{isIOS as u}from'../../utils/platform.js';function w(){return u()?{before({doc:r,d:n,meta:c}){function a(o){return c.containers.flatMap(l=>l()).some(l=>l.contains(o))}n.microTask(()=>{var s;if(window.getComputedStyle(r.documentElement).scrollBehavior!==\"auto\"){let t=m();t.style(r.documentElement,\"scrollBehavior\",\"auto\"),n.add(()=>n.microTask(()=>t.dispose()))}let o=(s=window.scrollY)!=null?s:window.pageYOffset,l=null;n.addEventListener(r,\"click\",t=>{if(t.target instanceof HTMLElement)try{let e=t.target.closest(\"a\");if(!e)return;let{hash:f}=new URL(e.href),i=r.querySelector(f);i&&!a(i)&&(l=i)}catch{}},!0),n.addEventListener(r,\"touchstart\",t=>{if(t.target instanceof HTMLElement)if(a(t.target)){let e=t.target;for(;e.parentElement&&a(e.parentElement);)e=e.parentElement;n.style(e,\"overscrollBehavior\",\"contain\")}else n.style(t.target,\"touchAction\",\"none\")}),n.addEventListener(r,\"touchmove\",t=>{if(t.target instanceof HTMLElement){if(t.target.tagName===\"INPUT\")return;if(a(t.target)){let e=t.target;for(;e.parentElement&&e.dataset.headlessuiPortal!==\"\"&&!(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth);)e=e.parentElement;e.dataset.headlessuiPortal===\"\"&&t.preventDefault()}else t.preventDefault()}},{passive:!1}),n.add(()=>{var e;let t=(e=window.scrollY)!=null?e:window.pageYOffset;o!==t&&window.scrollTo(0,o),l&&l.isConnected&&(l.scrollIntoView({block:\"nearest\"}),l=null)})})}}:{}}export{w as handleIOSLocking};\n", "function l(){return{before({doc:e,d:o}){o.style(e.documentElement,\"overflow\",\"hidden\")}}}export{l as preventScroll};\n", "import{disposables as s}from'../../utils/disposables.js';import{createStore as i}from'../../utils/store.js';import{adjustScrollbarPadding as l}from'./adjust-scrollbar-padding.js';import{handleIOSLocking as d}from'./handle-ios-locking.js';import{preventScroll as p}from'./prevent-scroll.js';function m(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let a=i(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:s(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:m(t)},c=[d(),l(),p()];c.forEach(({before:r})=>r==null?void 0:r(o)),c.forEach(({after:r})=>r==null?void 0:r(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});a.subscribe(()=>{let e=a.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)===\"hidden\",c=t.count!==0;(c&&!o||!c&&o)&&a.dispatch(t.count>0?\"SCROLL_PREVENT\":\"SCROLL_ALLOW\",t),t.count===0&&a.dispatch(\"TEARDOWN\",t)}});export{a as overflows};\n", "import{ref as m,watchEffect as s}from\"vue\";import{dom as h}from'../utils/dom.js';let i=new Map,t=new Map;function E(d,f=m(!0)){s(o=>{var a;if(!f.value)return;let e=h(d);if(!e)return;o(function(){var u;if(!e)return;let r=(u=t.get(e))!=null?u:1;if(r===1?t.delete(e):t.set(e,r-1),r!==1)return;let n=i.get(e);n&&(n[\"aria-hidden\"]===null?e.removeAttribute(\"aria-hidden\"):e.setAttribute(\"aria-hidden\",n[\"aria-hidden\"]),e.inert=n.inert,i.delete(e))});let l=(a=t.get(e))!=null?a:0;t.set(e,l+1),l===0&&(i.set(e,{\"aria-hidden\":e.getAttribute(\"aria-hidden\"),inert:e.inert}),e.setAttribute(\"aria-hidden\",\"true\"),e.inert=!0)})}export{E as useInert};\n", "import{h as m,ref as s}from\"vue\";import{Features as d,Hidden as c}from'../internal/hidden.js';import{dom as T}from'../utils/dom.js';import{getOwnerDocument as E}from'../utils/owner.js';function N({defaultContainers:o=[],portals:i,mainTreeNodeRef:H}={}){let t=s(null),r=E(t);function u(){var l,f,a;let n=[];for(let e of o)e!==null&&(e instanceof HTMLElement?n.push(e):\"value\"in e&&e.value instanceof HTMLElement&&n.push(e.value));if(i!=null&&i.value)for(let e of i.value)n.push(e);for(let e of(l=r==null?void 0:r.querySelectorAll(\"html > *, body > *\"))!=null?l:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e.id!==\"headlessui-portal-root\"&&(e.contains(T(t))||e.contains((a=(f=T(t))==null?void 0:f.getRootNode())==null?void 0:a.host)||n.some(M=>e.contains(M))||n.push(e));return n}return{resolveContainers:u,contains(n){return u().some(l=>l.contains(n))},mainTreeNodeRef:t,MainTreeNode(){return H!=null?null:m(c,{features:d.Hidden,ref:t})}}}function v(){let o=s(null);return{mainTreeNodeRef:o,MainTreeNode(){return m(c,{features:d.Hidden,ref:o})}}}export{v as useMainTreeNode,N as useRootContainers};\n", "import{defineComponent as l,inject as a,provide as c}from\"vue\";import{render as p}from'../utils/render.js';let e=Symbol(\"ForcePortalRootContext\");function s(){return a(e,!1)}let u=l({name:\"ForcePortalRoot\",props:{as:{type:[Object,String],default:\"template\"},force:{type:Boolean,default:!1}},setup(o,{slots:t,attrs:r}){return c(e,o.force),()=>{let{force:f,...n}=o;return p({theirProps:n,ourProps:{},slot:{},slots:t,attrs:r,name:\"ForcePortalRoot\"})}}});export{u as ForcePortalRoot,s as usePortalRoot};\n", "import{inject as f,onMounted as m,onUnmounted as l,provide as c,watch as p}from\"vue\";let u=Symbol(\"StackContext\");var s=(e=>(e[e.Add=0]=\"Add\",e[e.Remove=1]=\"Remove\",e))(s||{});function y(){return f(u,()=>{})}function R({type:o,enabled:r,element:e,onUpdate:i}){let a=y();function t(...n){i==null||i(...n),a(...n)}m(()=>{p(r,(n,d)=>{n?t(0,o,e):d===!0&&t(1,o,e)},{immediate:!0,flush:\"sync\"})}),l(()=>{r.value&&t(1,o,e)}),c(u,t)}export{s as StackMessage,y as useStackContext,R as useStackProvider};\n", "import{computed as x,defineComponent as y,inject as R,onMounted as v,onUnmounted as D,provide as j,ref as p,unref as C}from\"vue\";import{useId as h}from'../../hooks/use-id.js';import{render as b}from'../../utils/render.js';let u=Symbol(\"DescriptionContext\");function w(){let t=R(u,null);if(t===null)throw new Error(\"Missing parent\");return t}function k({slot:t=p({}),name:o=\"Description\",props:s={}}={}){let e=p([]);function r(n){return e.value.push(n),()=>{let i=e.value.indexOf(n);i!==-1&&e.value.splice(i,1)}}return j(u,{register:r,slot:t,name:o,props:s}),x(()=>e.value.length>0?e.value.join(\" \"):void 0)}let K=y({name:\"Description\",props:{as:{type:[Object,String],default:\"p\"},id:{type:String,default:null}},setup(t,{attrs:o,slots:s}){var n;let e=(n=t.id)!=null?n:`headlessui-description-${h()}`,r=w();return v(()=>D(r.register(e))),()=>{let{name:i=\"Description\",slot:l=p({}),props:d={}}=r,{...c}=t,f={...Object.entries(d).reduce((a,[g,m])=>Object.assign(a,{[g]:C(m)}),{}),id:e};return b({ourProps:f,theirProps:c,slot:l.value,attrs:o,slots:s,name:i})}}});export{K as Description,k as useDescriptions};\n", "import{computed as w,defineComponent as m,getCurrentInstance as j,h as I,inject as s,onMounted as R,onUnmounted as y,provide as E,reactive as G,ref as p,Teleport as O,watch as D,watchEffect as K}from\"vue\";import{usePortalRoot as S}from'../../internal/portal-force-root.js';import{dom as B}from'../../utils/dom.js';import{getOwnerDocument as C}from'../../utils/owner.js';import{render as h}from'../../utils/render.js';function x(e){let t=C(e);if(!t){if(e===null)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let l=t.getElementById(\"headlessui-portal-root\");if(l)return l;let r=t.createElement(\"div\");return r.setAttribute(\"id\",\"headlessui-portal-root\"),t.body.appendChild(r)}const f=new WeakMap;function U(e){var t;return(t=f.get(e))!=null?t:0}function M(e,t){let l=t(U(e));return l<=0?f.delete(e):f.set(e,l),l}let $=m({name:\"Portal\",props:{as:{type:[Object,String],default:\"div\"}},setup(e,{slots:t,attrs:l}){let r=p(null),i=w(()=>C(r)),o=S(),u=s(H,null),n=p(o===!0||u==null?x(r.value):u.resolveTarget());n.value&&M(n.value,a=>a+1);let c=p(!1);R(()=>{c.value=!0}),K(()=>{o||u!=null&&(n.value=u.resolveTarget())});let v=s(d,null),g=!1,b=j();return D(r,()=>{if(g||!v)return;let a=B(r);a&&(y(v.register(a),b),g=!0)}),y(()=>{var P,T;let a=(P=i.value)==null?void 0:P.getElementById(\"headlessui-portal-root\");!a||n.value!==a||M(n.value,L=>L-1)||n.value.children.length>0||(T=n.value.parentElement)==null||T.removeChild(n.value)}),()=>{if(!c.value||n.value===null)return null;let a={ref:r,\"data-headlessui-portal\":\"\"};return I(O,{to:n.value},h({ourProps:a,theirProps:e,slot:{},attrs:l,slots:t,name:\"Portal\"}))}}}),d=Symbol(\"PortalParentContext\");function q(){let e=s(d,null),t=p([]);function l(o){return t.value.push(o),e&&e.register(o),()=>r(o)}function r(o){let u=t.value.indexOf(o);u!==-1&&t.value.splice(u,1),e&&e.unregister(o)}let i={register:l,unregister:r,portals:t};return[t,m({name:\"PortalWrapper\",setup(o,{slots:u}){return E(d,i),()=>{var n;return(n=u.default)==null?void 0:n.call(u)}}})]}let H=Symbol(\"PortalGroupContext\"),z=m({name:\"PortalGroup\",props:{as:{type:[Object,String],default:\"template\"},target:{type:Object,default:null}},setup(e,{attrs:t,slots:l}){let r=G({resolveTarget(){return e.target}});return E(H,r),()=>{let{target:i,...o}=e;return h({theirProps:o,ourProps:{},slot:{},attrs:t,slots:l,name:\"PortalGroup\"})}}});export{$ as Portal,z as PortalGroup,q as useNestedPortals};\n", "import{computed as m,defineComponent as b,inject as I,onMounted as P,onUnmounted as h,provide as R,ref as d,watchEffect as w}from\"vue\";import{useId as E}from'../../hooks/use-id.js';import{useResolveButtonType as H}from'../../hooks/use-resolve-button-type.js';import{State as y,useOpenClosed as L,useOpenClosedProvider as j}from'../../internal/open-closed.js';import{Keys as f}from'../../keyboard.js';import{dom as p}from'../../utils/dom.js';import{match as x}from'../../utils/match.js';import{Features as B,render as g}from'../../utils/render.js';var $=(o=>(o[o.Open=0]=\"Open\",o[o.Closed=1]=\"Closed\",o))($||{});let T=Symbol(\"DisclosureContext\");function O(t){let r=I(T,null);if(r===null){let o=new Error(`<${t} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,O),o}return r}let k=Symbol(\"DisclosurePanelContext\");function U(){return I(k,null)}let N=b({name:\"Disclosure\",props:{as:{type:[Object,String],default:\"template\"},defaultOpen:{type:[Boolean],default:!1}},setup(t,{slots:r,attrs:o}){let s=d(t.defaultOpen?0:1),e=d(null),i=d(null),n={buttonId:d(`headlessui-disclosure-button-${E()}`),panelId:d(`headlessui-disclosure-panel-${E()}`),disclosureState:s,panel:e,button:i,toggleDisclosure(){s.value=x(s.value,{[0]:1,[1]:0})},closeDisclosure(){s.value!==1&&(s.value=1)},close(l){n.closeDisclosure();let a=(()=>l?l instanceof HTMLElement?l:l.value instanceof HTMLElement?p(l):p(n.button):p(n.button))();a==null||a.focus()}};return R(T,n),j(m(()=>x(s.value,{[0]:y.Open,[1]:y.Closed}))),()=>{let{defaultOpen:l,...a}=t,c={open:s.value===0,close:n.close};return g({theirProps:a,ourProps:{},slot:c,slots:r,attrs:o,name:\"Disclosure\"})}}}),Q=b({name:\"DisclosureButton\",props:{as:{type:[Object,String],default:\"button\"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(t,{attrs:r,slots:o,expose:s}){let e=O(\"DisclosureButton\"),i=U(),n=m(()=>i===null?!1:i.value===e.panelId.value);P(()=>{n.value||t.id!==null&&(e.buttonId.value=t.id)}),h(()=>{n.value||(e.buttonId.value=null)});let l=d(null);s({el:l,$el:l}),n.value||w(()=>{e.button.value=l.value});let a=H(m(()=>({as:t.as,type:r.type})),l);function c(){var u;t.disabled||(n.value?(e.toggleDisclosure(),(u=p(e.button))==null||u.focus()):e.toggleDisclosure())}function D(u){var S;if(!t.disabled)if(n.value)switch(u.key){case f.Space:case f.Enter:u.preventDefault(),u.stopPropagation(),e.toggleDisclosure(),(S=p(e.button))==null||S.focus();break}else switch(u.key){case f.Space:case f.Enter:u.preventDefault(),u.stopPropagation(),e.toggleDisclosure();break}}function v(u){switch(u.key){case f.Space:u.preventDefault();break}}return()=>{var C;let u={open:e.disclosureState.value===0},{id:S,...K}=t,M=n.value?{ref:l,type:a.value,onClick:c,onKeydown:D}:{id:(C=e.buttonId.value)!=null?C:S,ref:l,type:a.value,\"aria-expanded\":e.disclosureState.value===0,\"aria-controls\":e.disclosureState.value===0||p(e.panel)?e.panelId.value:void 0,disabled:t.disabled?!0:void 0,onClick:c,onKeydown:D,onKeyup:v};return g({ourProps:M,theirProps:K,slot:u,attrs:r,slots:o,name:\"DisclosureButton\"})}}}),V=b({name:\"DisclosurePanel\",props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(t,{attrs:r,slots:o,expose:s}){let e=O(\"DisclosurePanel\");P(()=>{t.id!==null&&(e.panelId.value=t.id)}),h(()=>{e.panelId.value=null}),s({el:e.panel,$el:e.panel}),R(k,e.panelId);let i=L(),n=m(()=>i!==null?(i.value&y.Open)===y.Open:e.disclosureState.value===0);return()=>{var v;let l={open:e.disclosureState.value===0,close:e.close},{id:a,...c}=t,D={id:(v=e.panelId.value)!=null?v:a,ref:e.panel};return g({ourProps:D,theirProps:c,slot:l,attrs:r,slots:o,features:B.RenderStrategy|B.Static,visible:n.value,name:\"DisclosurePanel\"})}}});export{N as Disclosure,Q as DisclosureButton,V as DisclosurePanel};\n", "import{computed as x,defineComponent as E,Fragment as z,h as N,inject as _,nextTick as V,onMounted as K,onUnmounted as q,provide as W,ref as T,toRaw as R,watch as H,watchEffect as G}from\"vue\";import{useControllable as J}from'../../hooks/use-controllable.js';import{useId as F}from'../../hooks/use-id.js';import{useOutsideClick as X}from'../../hooks/use-outside-click.js';import{useResolveButtonType as Y}from'../../hooks/use-resolve-button-type.js';import{useTextValue as Z}from'../../hooks/use-text-value.js';import{useTrackedPointer as ee}from'../../hooks/use-tracked-pointer.js';import{Features as te,Hidden as oe}from'../../internal/hidden.js';import{State as B,useOpenClosed as ae,useOpenClosedProvider as ie}from'../../internal/open-closed.js';import{Keys as c}from'../../keyboard.js';import{calculateActiveIndex as ne,Focus as g}from'../../utils/calculate-active-index.js';import{dom as S}from'../../utils/dom.js';import{FocusableMode as le,isFocusableElement as ue,sortByDomNode as re}from'../../utils/focus-management.js';import{objectToFormEntries as se}from'../../utils/form.js';import{match as P}from'../../utils/match.js';import{compact as de,Features as U,omit as fe,render as j}from'../../utils/render.js';function pe(o,b){return o===b}var ce=(r=>(r[r.Open=0]=\"Open\",r[r.Closed=1]=\"Closed\",r))(ce||{}),ve=(r=>(r[r.Single=0]=\"Single\",r[r.Multi=1]=\"Multi\",r))(ve||{}),be=(r=>(r[r.Pointer=0]=\"Pointer\",r[r.Other=1]=\"Other\",r))(be||{});function me(o){requestAnimationFrame(()=>requestAnimationFrame(o))}let $=Symbol(\"ListboxContext\");function A(o){let b=_($,null);if(b===null){let r=new Error(`<${o} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,A),r}return b}let Ie=E({name:\"Listbox\",emits:{\"update:modelValue\":o=>!0},props:{as:{type:[Object,String],default:\"template\"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>pe},horizontal:{type:[Boolean],default:!1},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},multiple:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(o,{slots:b,attrs:r,emit:w}){let n=T(1),e=T(null),f=T(null),v=T(null),s=T([]),m=T(\"\"),p=T(null),a=T(1);function u(t=i=>i){let i=p.value!==null?s.value[p.value]:null,l=re(t(s.value.slice()),O=>S(O.dataRef.domRef)),d=i?l.indexOf(i):null;return d===-1&&(d=null),{options:l,activeOptionIndex:d}}let D=x(()=>o.multiple?1:0),[y,L]=J(x(()=>o.modelValue),t=>w(\"update:modelValue\",t),x(()=>o.defaultValue)),M=x(()=>y.value===void 0?P(D.value,{[1]:[],[0]:void 0}):y.value),k={listboxState:n,value:M,mode:D,compare(t,i){if(typeof o.by==\"string\"){let l=o.by;return(t==null?void 0:t[l])===(i==null?void 0:i[l])}return o.by(t,i)},orientation:x(()=>o.horizontal?\"horizontal\":\"vertical\"),labelRef:e,buttonRef:f,optionsRef:v,disabled:x(()=>o.disabled),options:s,searchQuery:m,activeOptionIndex:p,activationTrigger:a,closeListbox(){o.disabled||n.value!==1&&(n.value=1,p.value=null)},openListbox(){o.disabled||n.value!==0&&(n.value=0)},goToOption(t,i,l){if(o.disabled||n.value===1)return;let d=u(),O=ne(t===g.Specific?{focus:g.Specific,id:i}:{focus:t},{resolveItems:()=>d.options,resolveActiveIndex:()=>d.activeOptionIndex,resolveId:h=>h.id,resolveDisabled:h=>h.dataRef.disabled});m.value=\"\",p.value=O,a.value=l!=null?l:1,s.value=d.options},search(t){if(o.disabled||n.value===1)return;let l=m.value!==\"\"?0:1;m.value+=t.toLowerCase();let O=(p.value!==null?s.value.slice(p.value+l).concat(s.value.slice(0,p.value+l)):s.value).find(I=>I.dataRef.textValue.startsWith(m.value)&&!I.dataRef.disabled),h=O?s.value.indexOf(O):-1;h===-1||h===p.value||(p.value=h,a.value=1)},clearSearch(){o.disabled||n.value!==1&&m.value!==\"\"&&(m.value=\"\")},registerOption(t,i){let l=u(d=>[...d,{id:t,dataRef:i}]);s.value=l.options,p.value=l.activeOptionIndex},unregisterOption(t){let i=u(l=>{let d=l.findIndex(O=>O.id===t);return d!==-1&&l.splice(d,1),l});s.value=i.options,p.value=i.activeOptionIndex,a.value=1},theirOnChange(t){o.disabled||L(t)},select(t){o.disabled||L(P(D.value,{[0]:()=>t,[1]:()=>{let i=R(k.value.value).slice(),l=R(t),d=i.findIndex(O=>k.compare(l,R(O)));return d===-1?i.push(l):i.splice(d,1),i}}))}};X([f,v],(t,i)=>{var l;k.closeListbox(),ue(i,le.Loose)||(t.preventDefault(),(l=S(f))==null||l.focus())},x(()=>n.value===0)),W($,k),ie(x(()=>P(n.value,{[0]:B.Open,[1]:B.Closed})));let C=x(()=>{var t;return(t=S(f))==null?void 0:t.closest(\"form\")});return K(()=>{H([C],()=>{if(!C.value||o.defaultValue===void 0)return;function t(){k.theirOnChange(o.defaultValue)}return C.value.addEventListener(\"reset\",t),()=>{var i;(i=C.value)==null||i.removeEventListener(\"reset\",t)}},{immediate:!0})}),()=>{let{name:t,modelValue:i,disabled:l,form:d,...O}=o,h={open:n.value===0,disabled:l,value:M.value};return N(z,[...t!=null&&M.value!=null?se({[t]:M.value}).map(([I,Q])=>N(oe,de({features:te.Hidden,key:I,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:d,disabled:l,name:I,value:Q}))):[],j({ourProps:{},theirProps:{...r,...fe(O,[\"defaultValue\",\"onUpdate:modelValue\",\"horizontal\",\"multiple\",\"by\"])},slot:h,slots:b,attrs:r,name:\"Listbox\"})])}}}),Ee=E({name:\"ListboxLabel\",props:{as:{type:[Object,String],default:\"label\"},id:{type:String,default:null}},setup(o,{attrs:b,slots:r}){var f;let w=(f=o.id)!=null?f:`headlessui-listbox-label-${F()}`,n=A(\"ListboxLabel\");function e(){var v;(v=S(n.buttonRef))==null||v.focus({preventScroll:!0})}return()=>{let v={open:n.listboxState.value===0,disabled:n.disabled.value},{...s}=o,m={id:w,ref:n.labelRef,onClick:e};return j({ourProps:m,theirProps:s,slot:v,attrs:b,slots:r,name:\"ListboxLabel\"})}}}),je=E({name:\"ListboxButton\",props:{as:{type:[Object,String],default:\"button\"},id:{type:String,default:null}},setup(o,{attrs:b,slots:r,expose:w}){var p;let n=(p=o.id)!=null?p:`headlessui-listbox-button-${F()}`,e=A(\"ListboxButton\");w({el:e.buttonRef,$el:e.buttonRef});function f(a){switch(a.key){case c.Space:case c.Enter:case c.ArrowDown:a.preventDefault(),e.openListbox(),V(()=>{var u;(u=S(e.optionsRef))==null||u.focus({preventScroll:!0}),e.value.value||e.goToOption(g.First)});break;case c.ArrowUp:a.preventDefault(),e.openListbox(),V(()=>{var u;(u=S(e.optionsRef))==null||u.focus({preventScroll:!0}),e.value.value||e.goToOption(g.Last)});break}}function v(a){switch(a.key){case c.Space:a.preventDefault();break}}function s(a){e.disabled.value||(e.listboxState.value===0?(e.closeListbox(),V(()=>{var u;return(u=S(e.buttonRef))==null?void 0:u.focus({preventScroll:!0})})):(a.preventDefault(),e.openListbox(),me(()=>{var u;return(u=S(e.optionsRef))==null?void 0:u.focus({preventScroll:!0})})))}let m=Y(x(()=>({as:o.as,type:b.type})),e.buttonRef);return()=>{var y,L;let a={open:e.listboxState.value===0,disabled:e.disabled.value,value:e.value.value},{...u}=o,D={ref:e.buttonRef,id:n,type:m.value,\"aria-haspopup\":\"listbox\",\"aria-controls\":(y=S(e.optionsRef))==null?void 0:y.id,\"aria-expanded\":e.listboxState.value===0,\"aria-labelledby\":e.labelRef.value?[(L=S(e.labelRef))==null?void 0:L.id,n].join(\" \"):void 0,disabled:e.disabled.value===!0?!0:void 0,onKeydown:f,onKeyup:v,onClick:s};return j({ourProps:D,theirProps:u,slot:a,attrs:b,slots:r,name:\"ListboxButton\"})}}}),Ae=E({name:\"ListboxOptions\",props:{as:{type:[Object,String],default:\"ul\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(o,{attrs:b,slots:r,expose:w}){var p;let n=(p=o.id)!=null?p:`headlessui-listbox-options-${F()}`,e=A(\"ListboxOptions\"),f=T(null);w({el:e.optionsRef,$el:e.optionsRef});function v(a){switch(f.value&&clearTimeout(f.value),a.key){case c.Space:if(e.searchQuery.value!==\"\")return a.preventDefault(),a.stopPropagation(),e.search(a.key);case c.Enter:if(a.preventDefault(),a.stopPropagation(),e.activeOptionIndex.value!==null){let u=e.options.value[e.activeOptionIndex.value];e.select(u.dataRef.value)}e.mode.value===0&&(e.closeListbox(),V(()=>{var u;return(u=S(e.buttonRef))==null?void 0:u.focus({preventScroll:!0})}));break;case P(e.orientation.value,{vertical:c.ArrowDown,horizontal:c.ArrowRight}):return a.preventDefault(),a.stopPropagation(),e.goToOption(g.Next);case P(e.orientation.value,{vertical:c.ArrowUp,horizontal:c.ArrowLeft}):return a.preventDefault(),a.stopPropagation(),e.goToOption(g.Previous);case c.Home:case c.PageUp:return a.preventDefault(),a.stopPropagation(),e.goToOption(g.First);case c.End:case c.PageDown:return a.preventDefault(),a.stopPropagation(),e.goToOption(g.Last);case c.Escape:a.preventDefault(),a.stopPropagation(),e.closeListbox(),V(()=>{var u;return(u=S(e.buttonRef))==null?void 0:u.focus({preventScroll:!0})});break;case c.Tab:a.preventDefault(),a.stopPropagation();break;default:a.key.length===1&&(e.search(a.key),f.value=setTimeout(()=>e.clearSearch(),350));break}}let s=ae(),m=x(()=>s!==null?(s.value&B.Open)===B.Open:e.listboxState.value===0);return()=>{var y,L;let a={open:e.listboxState.value===0},{...u}=o,D={\"aria-activedescendant\":e.activeOptionIndex.value===null||(y=e.options.value[e.activeOptionIndex.value])==null?void 0:y.id,\"aria-multiselectable\":e.mode.value===1?!0:void 0,\"aria-labelledby\":(L=S(e.buttonRef))==null?void 0:L.id,\"aria-orientation\":e.orientation.value,id:n,onKeydown:v,role:\"listbox\",tabIndex:0,ref:e.optionsRef};return j({ourProps:D,theirProps:u,slot:a,attrs:b,slots:r,features:U.RenderStrategy|U.Static,visible:m.value,name:\"ListboxOptions\"})}}}),Fe=E({name:\"ListboxOption\",props:{as:{type:[Object,String],default:\"li\"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(o,{slots:b,attrs:r,expose:w}){var C;let n=(C=o.id)!=null?C:`headlessui-listbox-option-${F()}`,e=A(\"ListboxOption\"),f=T(null);w({el:f,$el:f});let v=x(()=>e.activeOptionIndex.value!==null?e.options.value[e.activeOptionIndex.value].id===n:!1),s=x(()=>P(e.mode.value,{[0]:()=>e.compare(R(e.value.value),R(o.value)),[1]:()=>R(e.value.value).some(t=>e.compare(R(t),R(o.value)))})),m=x(()=>P(e.mode.value,{[1]:()=>{var i;let t=R(e.value.value);return((i=e.options.value.find(l=>t.some(d=>e.compare(R(d),R(l.dataRef.value)))))==null?void 0:i.id)===n},[0]:()=>s.value})),p=Z(f),a=x(()=>({disabled:o.disabled,value:o.value,get textValue(){return p()},domRef:f}));K(()=>e.registerOption(n,a)),q(()=>e.unregisterOption(n)),K(()=>{H([e.listboxState,s],()=>{e.listboxState.value===0&&s.value&&P(e.mode.value,{[1]:()=>{m.value&&e.goToOption(g.Specific,n)},[0]:()=>{e.goToOption(g.Specific,n)}})},{immediate:!0})}),G(()=>{e.listboxState.value===0&&v.value&&e.activationTrigger.value!==0&&V(()=>{var t,i;return(i=(t=S(f))==null?void 0:t.scrollIntoView)==null?void 0:i.call(t,{block:\"nearest\"})})});function u(t){if(o.disabled)return t.preventDefault();e.select(o.value),e.mode.value===0&&(e.closeListbox(),V(()=>{var i;return(i=S(e.buttonRef))==null?void 0:i.focus({preventScroll:!0})}))}function D(){if(o.disabled)return e.goToOption(g.Nothing);e.goToOption(g.Specific,n)}let y=ee();function L(t){y.update(t)}function M(t){y.wasMoved(t)&&(o.disabled||v.value||e.goToOption(g.Specific,n,0))}function k(t){y.wasMoved(t)&&(o.disabled||v.value&&e.goToOption(g.Nothing))}return()=>{let{disabled:t}=o,i={active:v.value,selected:s.value,disabled:t},{value:l,disabled:d,...O}=o,h={id:n,ref:f,role:\"option\",tabIndex:t===!0?void 0:-1,\"aria-disabled\":t===!0?!0:void 0,\"aria-selected\":s.value,disabled:void 0,onClick:u,onFocus:D,onPointerenter:L,onMouseenter:L,onPointermove:M,onMousemove:M,onPointerleave:k,onMouseleave:k};return j({ourProps:h,theirProps:O,slot:i,attrs:r,slots:b,name:\"ListboxOption\"})}}});export{Ie as Listbox,je as ListboxButton,Ee as ListboxLabel,Fe as ListboxOption,Ae as ListboxOptions};\n", "import{ref as n}from\"vue\";import{dom as o}from'../utils/dom.js';import{getTextValue as i}from'../utils/get-text-value.js';function p(a){let t=n(\"\"),r=n(\"\");return()=>{let e=o(a);if(!e)return\"\";let l=e.innerText;if(t.value===l)return r.value;let u=i(e).trim().toLowerCase();return t.value=l,r.value=u,u}}export{p as useTextValue};\n", "let a=/([\\u2700-\\u27BF]|[\\uE000-\\uF8FF]|\\uD83C[\\uDC00-\\uDFFF]|\\uD83D[\\uDC00-\\uDFFF]|[\\u2011-\\u26FF]|\\uD83E[\\uDD10-\\uDDFF])/g;function o(e){var r,i;let n=(r=e.innerText)!=null?r:\"\",t=e.cloneNode(!0);if(!(t instanceof HTMLElement))return n;let u=!1;for(let f of t.querySelectorAll('[hidden],[aria-hidden],[role=\"img\"]'))f.remove(),u=!0;let l=u?(i=t.innerText)!=null?i:\"\":n;return a.test(l)&&(l=l.replace(a,\"\")),l}function g(e){let n=e.getAttribute(\"aria-label\");if(typeof n==\"string\")return n.trim();let t=e.getAttribute(\"aria-labelledby\");if(t){let u=t.split(\" \").map(l=>{let r=document.getElementById(l);if(r){let i=r.getAttribute(\"aria-label\");return typeof i==\"string\"?i.trim():o(r).trim()}return null}).filter(Boolean);if(u.length>0)return u.join(\", \")}return o(e).trim()}export{g as getTextValue};\n", "import{computed as y,defineComponent as T,inject as K,nextTick as x,onMounted as N,onUnmounted as j,provide as L,ref as R,watchEffect as B}from\"vue\";import{useId as w}from'../../hooks/use-id.js';import{useOutsideClick as U}from'../../hooks/use-outside-click.js';import{useResolveButtonType as $}from'../../hooks/use-resolve-button-type.js';import{useTextValue as V}from'../../hooks/use-text-value.js';import{useTrackedPointer as H}from'../../hooks/use-tracked-pointer.js';import{useTreeWalker as Q}from'../../hooks/use-tree-walker.js';import{State as D,useOpenClosed as _,useOpenClosedProvider as q}from'../../internal/open-closed.js';import{Keys as c}from'../../keyboard.js';import{calculateActiveIndex as W,Focus as S}from'../../utils/calculate-active-index.js';import{dom as m}from'../../utils/dom.js';import{Focus as E,FocusableMode as J,focusFrom as z,isFocusableElement as G,restoreFocusIfNecessary as k,sortByDomNode as X}from'../../utils/focus-management.js';import{match as Y}from'../../utils/match.js';import{Features as F,render as P}from'../../utils/render.js';var Z=(i=>(i[i.Open=0]=\"Open\",i[i.Closed=1]=\"Closed\",i))(Z||{}),ee=(i=>(i[i.Pointer=0]=\"Pointer\",i[i.Other=1]=\"Other\",i))(ee||{});function te(o){requestAnimationFrame(()=>requestAnimationFrame(o))}let A=Symbol(\"MenuContext\");function O(o){let M=K(A,null);if(M===null){let i=new Error(`<${o} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(i,O),i}return M}let ge=T({name:\"Menu\",props:{as:{type:[Object,String],default:\"template\"}},setup(o,{slots:M,attrs:i}){let I=R(1),p=R(null),e=R(null),r=R([]),f=R(\"\"),d=R(null),g=R(1);function b(t=a=>a){let a=d.value!==null?r.value[d.value]:null,n=X(t(r.value.slice()),v=>m(v.dataRef.domRef)),s=a?n.indexOf(a):null;return s===-1&&(s=null),{items:n,activeItemIndex:s}}let l={menuState:I,buttonRef:p,itemsRef:e,items:r,searchQuery:f,activeItemIndex:d,activationTrigger:g,closeMenu:()=>{I.value=1,d.value=null},openMenu:()=>I.value=0,goToItem(t,a,n){let s=b(),v=W(t===S.Specific?{focus:S.Specific,id:a}:{focus:t},{resolveItems:()=>s.items,resolveActiveIndex:()=>s.activeItemIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.disabled});f.value=\"\",d.value=v,g.value=n!=null?n:1,r.value=s.items},search(t){let n=f.value!==\"\"?0:1;f.value+=t.toLowerCase();let v=(d.value!==null?r.value.slice(d.value+n).concat(r.value.slice(0,d.value+n)):r.value).find(h=>h.dataRef.textValue.startsWith(f.value)&&!h.dataRef.disabled),u=v?r.value.indexOf(v):-1;u===-1||u===d.value||(d.value=u,g.value=1)},clearSearch(){f.value=\"\"},registerItem(t,a){let n=b(s=>[...s,{id:t,dataRef:a}]);r.value=n.items,d.value=n.activeItemIndex,g.value=1},unregisterItem(t){let a=b(n=>{let s=n.findIndex(v=>v.id===t);return s!==-1&&n.splice(s,1),n});r.value=a.items,d.value=a.activeItemIndex,g.value=1}};return U([p,e],(t,a)=>{var n;l.closeMenu(),G(a,J.Loose)||(t.preventDefault(),(n=m(p))==null||n.focus())},y(()=>I.value===0)),L(A,l),q(y(()=>Y(I.value,{[0]:D.Open,[1]:D.Closed}))),()=>{let t={open:I.value===0,close:l.closeMenu};return P({ourProps:{},theirProps:o,slot:t,slots:M,attrs:i,name:\"Menu\"})}}}),Se=T({name:\"MenuButton\",props:{disabled:{type:Boolean,default:!1},as:{type:[Object,String],default:\"button\"},id:{type:String,default:null}},setup(o,{attrs:M,slots:i,expose:I}){var b;let p=(b=o.id)!=null?b:`headlessui-menu-button-${w()}`,e=O(\"MenuButton\");I({el:e.buttonRef,$el:e.buttonRef});function r(l){switch(l.key){case c.Space:case c.Enter:case c.ArrowDown:l.preventDefault(),l.stopPropagation(),e.openMenu(),x(()=>{var t;(t=m(e.itemsRef))==null||t.focus({preventScroll:!0}),e.goToItem(S.First)});break;case c.ArrowUp:l.preventDefault(),l.stopPropagation(),e.openMenu(),x(()=>{var t;(t=m(e.itemsRef))==null||t.focus({preventScroll:!0}),e.goToItem(S.Last)});break}}function f(l){switch(l.key){case c.Space:l.preventDefault();break}}function d(l){o.disabled||(e.menuState.value===0?(e.closeMenu(),x(()=>{var t;return(t=m(e.buttonRef))==null?void 0:t.focus({preventScroll:!0})})):(l.preventDefault(),e.openMenu(),te(()=>{var t;return(t=m(e.itemsRef))==null?void 0:t.focus({preventScroll:!0})})))}let g=$(y(()=>({as:o.as,type:M.type})),e.buttonRef);return()=>{var n;let l={open:e.menuState.value===0},{...t}=o,a={ref:e.buttonRef,id:p,type:g.value,\"aria-haspopup\":\"menu\",\"aria-controls\":(n=m(e.itemsRef))==null?void 0:n.id,\"aria-expanded\":e.menuState.value===0,onKeydown:r,onKeyup:f,onClick:d};return P({ourProps:a,theirProps:t,slot:l,attrs:M,slots:i,name:\"MenuButton\"})}}}),Me=T({name:\"MenuItems\",props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(o,{attrs:M,slots:i,expose:I}){var l;let p=(l=o.id)!=null?l:`headlessui-menu-items-${w()}`,e=O(\"MenuItems\"),r=R(null);I({el:e.itemsRef,$el:e.itemsRef}),Q({container:y(()=>m(e.itemsRef)),enabled:y(()=>e.menuState.value===0),accept(t){return t.getAttribute(\"role\")===\"menuitem\"?NodeFilter.FILTER_REJECT:t.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(t){t.setAttribute(\"role\",\"none\")}});function f(t){var a;switch(r.value&&clearTimeout(r.value),t.key){case c.Space:if(e.searchQuery.value!==\"\")return t.preventDefault(),t.stopPropagation(),e.search(t.key);case c.Enter:if(t.preventDefault(),t.stopPropagation(),e.activeItemIndex.value!==null){let s=e.items.value[e.activeItemIndex.value];(a=m(s.dataRef.domRef))==null||a.click()}e.closeMenu(),k(m(e.buttonRef));break;case c.ArrowDown:return t.preventDefault(),t.stopPropagation(),e.goToItem(S.Next);case c.ArrowUp:return t.preventDefault(),t.stopPropagation(),e.goToItem(S.Previous);case c.Home:case c.PageUp:return t.preventDefault(),t.stopPropagation(),e.goToItem(S.First);case c.End:case c.PageDown:return t.preventDefault(),t.stopPropagation(),e.goToItem(S.Last);case c.Escape:t.preventDefault(),t.stopPropagation(),e.closeMenu(),x(()=>{var n;return(n=m(e.buttonRef))==null?void 0:n.focus({preventScroll:!0})});break;case c.Tab:t.preventDefault(),t.stopPropagation(),e.closeMenu(),x(()=>z(m(e.buttonRef),t.shiftKey?E.Previous:E.Next));break;default:t.key.length===1&&(e.search(t.key),r.value=setTimeout(()=>e.clearSearch(),350));break}}function d(t){switch(t.key){case c.Space:t.preventDefault();break}}let g=_(),b=y(()=>g!==null?(g.value&D.Open)===D.Open:e.menuState.value===0);return()=>{var s,v;let t={open:e.menuState.value===0},{...a}=o,n={\"aria-activedescendant\":e.activeItemIndex.value===null||(s=e.items.value[e.activeItemIndex.value])==null?void 0:s.id,\"aria-labelledby\":(v=m(e.buttonRef))==null?void 0:v.id,id:p,onKeydown:f,onKeyup:d,role:\"menu\",tabIndex:0,ref:e.itemsRef};return P({ourProps:n,theirProps:a,slot:t,attrs:M,slots:i,features:F.RenderStrategy|F.Static,visible:b.value,name:\"MenuItems\"})}}}),be=T({name:\"MenuItem\",inheritAttrs:!1,props:{as:{type:[Object,String],default:\"template\"},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(o,{slots:M,attrs:i,expose:I}){var v;let p=(v=o.id)!=null?v:`headlessui-menu-item-${w()}`,e=O(\"MenuItem\"),r=R(null);I({el:r,$el:r});let f=y(()=>e.activeItemIndex.value!==null?e.items.value[e.activeItemIndex.value].id===p:!1),d=V(r),g=y(()=>({disabled:o.disabled,get textValue(){return d()},domRef:r}));N(()=>e.registerItem(p,g)),j(()=>e.unregisterItem(p)),B(()=>{e.menuState.value===0&&f.value&&e.activationTrigger.value!==0&&x(()=>{var u,h;return(h=(u=m(r))==null?void 0:u.scrollIntoView)==null?void 0:h.call(u,{block:\"nearest\"})})});function b(u){if(o.disabled)return u.preventDefault();e.closeMenu(),k(m(e.buttonRef))}function l(){if(o.disabled)return e.goToItem(S.Nothing);e.goToItem(S.Specific,p)}let t=H();function a(u){t.update(u)}function n(u){t.wasMoved(u)&&(o.disabled||f.value||e.goToItem(S.Specific,p,0))}function s(u){t.wasMoved(u)&&(o.disabled||f.value&&e.goToItem(S.Nothing))}return()=>{let{disabled:u,...h}=o,C={active:f.value,disabled:u,close:e.closeMenu};return P({ourProps:{id:p,ref:r,role:\"menuitem\",tabIndex:u===!0?void 0:-1,\"aria-disabled\":u===!0?!0:void 0,onClick:b,onFocus:l,onPointerenter:a,onMouseenter:a,onPointermove:n,onMousemove:n,onPointerleave:s,onMouseleave:s},theirProps:{...i,...h},slot:C,attrs:i,slots:M,name:\"MenuItem\"})}}});export{ge as Menu,Se as MenuButton,be as MenuItem,Me as MenuItems};\n", "import{computed as O,defineComponent as j,Fragment as W,h as T,inject as q,onMounted as ee,onUnmounted as te,provide as z,ref as R,shallowRef as ie,watchEffect as J}from\"vue\";import{useNestedPortals as se}from'../../components/portal/portal.js';import{useEventListener as pe}from'../../hooks/use-event-listener.js';import{useId as H}from'../../hooks/use-id.js';import{useOutsideClick as fe}from'../../hooks/use-outside-click.js';import{useResolveButtonType as ve}from'../../hooks/use-resolve-button-type.js';import{useMainTreeNode as ce,useRootContainers as de}from'../../hooks/use-root-containers.js';import{Direction as M,useTabDirection as oe}from'../../hooks/use-tab-direction.js';import{Features as Q,Hidden as X}from'../../internal/hidden.js';import{State as N,useOpenClosed as ne,useOpenClosedProvider as Pe}from'../../internal/open-closed.js';import{Keys as k}from'../../keyboard.js';import{dom as n}from'../../utils/dom.js';import{Focus as D,FocusableMode as me,focusIn as B,FocusResult as Y,getFocusableElements as Z,isFocusableElement as be}from'../../utils/focus-management.js';import{match as K}from'../../utils/match.js';import'../../utils/micro-task.js';import{getOwnerDocument as V}from'../../utils/owner.js';import{Features as _,render as A}from'../../utils/render.js';var Se=(s=>(s[s.Open=0]=\"Open\",s[s.Closed=1]=\"Closed\",s))(Se||{});let re=Symbol(\"PopoverContext\");function U(d){let P=q(re,null);if(P===null){let s=new Error(`<${d} /> is missing a parent <${ye.name} /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(s,U),s}return P}let le=Symbol(\"PopoverGroupContext\");function ae(){return q(le,null)}let ue=Symbol(\"PopoverPanelContext\");function ge(){return q(ue,null)}let ye=j({name:\"Popover\",inheritAttrs:!1,props:{as:{type:[Object,String],default:\"div\"}},setup(d,{slots:P,attrs:s,expose:h}){var u;let f=R(null);h({el:f,$el:f});let t=R(1),o=R(null),y=R(null),v=R(null),m=R(null),b=O(()=>V(f)),E=O(()=>{var L,$;if(!n(o)||!n(m))return!1;for(let x of document.querySelectorAll(\"body > *\"))if(Number(x==null?void 0:x.contains(n(o)))^Number(x==null?void 0:x.contains(n(m))))return!0;let e=Z(),r=e.indexOf(n(o)),l=(r+e.length-1)%e.length,g=(r+1)%e.length,G=e[l],C=e[g];return!((L=n(m))!=null&&L.contains(G))&&!(($=n(m))!=null&&$.contains(C))}),a={popoverState:t,buttonId:R(null),panelId:R(null),panel:m,button:o,isPortalled:E,beforePanelSentinel:y,afterPanelSentinel:v,togglePopover(){t.value=K(t.value,{[0]:1,[1]:0})},closePopover(){t.value!==1&&(t.value=1)},close(e){a.closePopover();let r=(()=>e?e instanceof HTMLElement?e:e.value instanceof HTMLElement?n(e):n(a.button):n(a.button))();r==null||r.focus()}};z(re,a),Pe(O(()=>K(t.value,{[0]:N.Open,[1]:N.Closed})));let S={buttonId:a.buttonId,panelId:a.panelId,close(){a.closePopover()}},c=ae(),I=c==null?void 0:c.registerPopover,[F,w]=se(),i=de({mainTreeNodeRef:c==null?void 0:c.mainTreeNodeRef,portals:F,defaultContainers:[o,m]});function p(){var e,r,l,g;return(g=c==null?void 0:c.isFocusWithinPopoverGroup())!=null?g:((e=b.value)==null?void 0:e.activeElement)&&(((r=n(o))==null?void 0:r.contains(b.value.activeElement))||((l=n(m))==null?void 0:l.contains(b.value.activeElement)))}return J(()=>I==null?void 0:I(S)),pe((u=b.value)==null?void 0:u.defaultView,\"focus\",e=>{var r,l;e.target!==window&&e.target instanceof HTMLElement&&t.value===0&&(p()||o&&m&&(i.contains(e.target)||(r=n(a.beforePanelSentinel))!=null&&r.contains(e.target)||(l=n(a.afterPanelSentinel))!=null&&l.contains(e.target)||a.closePopover()))},!0),fe(i.resolveContainers,(e,r)=>{var l;a.closePopover(),be(r,me.Loose)||(e.preventDefault(),(l=n(o))==null||l.focus())},O(()=>t.value===0)),()=>{let e={open:t.value===0,close:a.close};return T(W,[T(w,{},()=>A({theirProps:{...d,...s},ourProps:{ref:f},slot:e,slots:P,attrs:s,name:\"Popover\"})),T(i.MainTreeNode)])}}}),Ge=j({name:\"PopoverButton\",props:{as:{type:[Object,String],default:\"button\"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(d,{attrs:P,slots:s,expose:h}){var u;let f=(u=d.id)!=null?u:`headlessui-popover-button-${H()}`,t=U(\"PopoverButton\"),o=O(()=>V(t.button));h({el:t.button,$el:t.button}),ee(()=>{t.buttonId.value=f}),te(()=>{t.buttonId.value=null});let y=ae(),v=y==null?void 0:y.closeOthers,m=ge(),b=O(()=>m===null?!1:m.value===t.panelId.value),E=R(null),a=`headlessui-focus-sentinel-${H()}`;b.value||J(()=>{t.button.value=n(E)});let S=ve(O(()=>({as:d.as,type:P.type})),E);function c(e){var r,l,g,G,C;if(b.value){if(t.popoverState.value===1)return;switch(e.key){case k.Space:case k.Enter:e.preventDefault(),(l=(r=e.target).click)==null||l.call(r),t.closePopover(),(g=n(t.button))==null||g.focus();break}}else switch(e.key){case k.Space:case k.Enter:e.preventDefault(),e.stopPropagation(),t.popoverState.value===1&&(v==null||v(t.buttonId.value)),t.togglePopover();break;case k.Escape:if(t.popoverState.value!==0)return v==null?void 0:v(t.buttonId.value);if(!n(t.button)||(G=o.value)!=null&&G.activeElement&&!((C=n(t.button))!=null&&C.contains(o.value.activeElement)))return;e.preventDefault(),e.stopPropagation(),t.closePopover();break}}function I(e){b.value||e.key===k.Space&&e.preventDefault()}function F(e){var r,l;d.disabled||(b.value?(t.closePopover(),(r=n(t.button))==null||r.focus()):(e.preventDefault(),e.stopPropagation(),t.popoverState.value===1&&(v==null||v(t.buttonId.value)),t.togglePopover(),(l=n(t.button))==null||l.focus()))}function w(e){e.preventDefault(),e.stopPropagation()}let i=oe();function p(){let e=n(t.panel);if(!e)return;function r(){K(i.value,{[M.Forwards]:()=>B(e,D.First),[M.Backwards]:()=>B(e,D.Last)})===Y.Error&&B(Z().filter(g=>g.dataset.headlessuiFocusGuard!==\"true\"),K(i.value,{[M.Forwards]:D.Next,[M.Backwards]:D.Previous}),{relativeTo:n(t.button)})}r()}return()=>{let e=t.popoverState.value===0,r={open:e},{...l}=d,g=b.value?{ref:E,type:S.value,onKeydown:c,onClick:F}:{ref:E,id:f,type:S.value,\"aria-expanded\":t.popoverState.value===0,\"aria-controls\":n(t.panel)?t.panelId.value:void 0,disabled:d.disabled?!0:void 0,onKeydown:c,onKeyup:I,onClick:F,onMousedown:w};return T(W,[A({ourProps:g,theirProps:{...P,...l},slot:r,attrs:P,slots:s,name:\"PopoverButton\"}),e&&!b.value&&t.isPortalled.value&&T(X,{id:a,features:Q.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:p})])}}}),$e=j({name:\"PopoverOverlay\",props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0}},setup(d,{attrs:P,slots:s}){let h=U(\"PopoverOverlay\"),f=`headlessui-popover-overlay-${H()}`,t=ne(),o=O(()=>t!==null?(t.value&N.Open)===N.Open:h.popoverState.value===0);function y(){h.closePopover()}return()=>{let v={open:h.popoverState.value===0};return A({ourProps:{id:f,\"aria-hidden\":!0,onClick:y},theirProps:d,slot:v,attrs:P,slots:s,features:_.RenderStrategy|_.Static,visible:o.value,name:\"PopoverOverlay\"})}}}),je=j({name:\"PopoverPanel\",props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(d,{attrs:P,slots:s,expose:h}){var w;let f=(w=d.id)!=null?w:`headlessui-popover-panel-${H()}`,{focus:t}=d,o=U(\"PopoverPanel\"),y=O(()=>V(o.panel)),v=`headlessui-focus-sentinel-before-${H()}`,m=`headlessui-focus-sentinel-after-${H()}`;h({el:o.panel,$el:o.panel}),ee(()=>{o.panelId.value=f}),te(()=>{o.panelId.value=null}),z(ue,o.panelId),J(()=>{var p,u;if(!t||o.popoverState.value!==0||!o.panel)return;let i=(p=y.value)==null?void 0:p.activeElement;(u=n(o.panel))!=null&&u.contains(i)||B(n(o.panel),D.First)});let b=ne(),E=O(()=>b!==null?(b.value&N.Open)===N.Open:o.popoverState.value===0);function a(i){var p,u;switch(i.key){case k.Escape:if(o.popoverState.value!==0||!n(o.panel)||y.value&&!((p=n(o.panel))!=null&&p.contains(y.value.activeElement)))return;i.preventDefault(),i.stopPropagation(),o.closePopover(),(u=n(o.button))==null||u.focus();break}}function S(i){var u,e,r,l,g;let p=i.relatedTarget;p&&n(o.panel)&&((u=n(o.panel))!=null&&u.contains(p)||(o.closePopover(),((r=(e=n(o.beforePanelSentinel))==null?void 0:e.contains)!=null&&r.call(e,p)||(g=(l=n(o.afterPanelSentinel))==null?void 0:l.contains)!=null&&g.call(l,p))&&p.focus({preventScroll:!0})))}let c=oe();function I(){let i=n(o.panel);if(!i)return;function p(){K(c.value,{[M.Forwards]:()=>{var e;B(i,D.First)===Y.Error&&((e=n(o.afterPanelSentinel))==null||e.focus())},[M.Backwards]:()=>{var u;(u=n(o.button))==null||u.focus({preventScroll:!0})}})}p()}function F(){let i=n(o.panel);if(!i)return;function p(){K(c.value,{[M.Forwards]:()=>{let u=n(o.button),e=n(o.panel);if(!u)return;let r=Z(),l=r.indexOf(u),g=r.slice(0,l+1),C=[...r.slice(l+1),...g];for(let L of C.slice())if(L.dataset.headlessuiFocusGuard===\"true\"||e!=null&&e.contains(L)){let $=C.indexOf(L);$!==-1&&C.splice($,1)}B(C,D.First,{sorted:!1})},[M.Backwards]:()=>{var e;B(i,D.Previous)===Y.Error&&((e=n(o.button))==null||e.focus())}})}p()}return()=>{let i={open:o.popoverState.value===0,close:o.close},{focus:p,...u}=d,e={ref:o.panel,id:f,onKeydown:a,onFocusout:t&&o.popoverState.value===0?S:void 0,tabIndex:-1};return A({ourProps:e,theirProps:{...P,...u},attrs:P,slot:i,slots:{...s,default:(...r)=>{var l;return[T(W,[E.value&&o.isPortalled.value&&T(X,{id:v,ref:o.beforePanelSentinel,features:Q.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:I}),(l=s.default)==null?void 0:l.call(s,...r),E.value&&o.isPortalled.value&&T(X,{id:m,ref:o.afterPanelSentinel,features:Q.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:F})])]}},features:_.RenderStrategy|_.Static,visible:E.value,name:\"PopoverPanel\"})}}}),Ae=j({name:\"PopoverGroup\",inheritAttrs:!1,props:{as:{type:[Object,String],default:\"div\"}},setup(d,{attrs:P,slots:s,expose:h}){let f=R(null),t=ie([]),o=O(()=>V(f)),y=ce();h({el:f,$el:f});function v(a){let S=t.value.indexOf(a);S!==-1&&t.value.splice(S,1)}function m(a){return t.value.push(a),()=>{v(a)}}function b(){var c;let a=o.value;if(!a)return!1;let S=a.activeElement;return(c=n(f))!=null&&c.contains(S)?!0:t.value.some(I=>{var F,w;return((F=a.getElementById(I.buttonId.value))==null?void 0:F.contains(S))||((w=a.getElementById(I.panelId.value))==null?void 0:w.contains(S))})}function E(a){for(let S of t.value)S.buttonId.value!==a&&S.close()}return z(le,{registerPopover:m,unregisterPopover:v,isFocusWithinPopoverGroup:b,closeOthers:E,mainTreeNodeRef:y.mainTreeNodeRef}),()=>T(W,[A({ourProps:{ref:f},theirProps:{...d,...P},slot:{},attrs:P,slots:s,name:\"PopoverGroup\"}),T(y.MainTreeNode)])}});export{ye as Popover,Ge as PopoverButton,Ae as PopoverGroup,$e as PopoverOverlay,je as PopoverPanel};\n", "import{computed as o,defineComponent as F,Fragment as _,h as C,inject as $,onMounted as D,onUnmounted as U,provide as W,ref as k,toRaw as y,watch as J}from\"vue\";import{useControllable as q}from'../../hooks/use-controllable.js';import{useId as x}from'../../hooks/use-id.js';import{useTreeWalker as Q}from'../../hooks/use-tree-walker.js';import{Features as X,Hidden as Y}from'../../internal/hidden.js';import{Keys as h}from'../../keyboard.js';import{dom as E}from'../../utils/dom.js';import{Focus as w,focusIn as I,FocusResult as P,sortByDomNode as Z}from'../../utils/focus-management.js';import{attemptSubmit as z,objectToFormEntries as ee}from'../../utils/form.js';import{getOwnerDocument as A}from'../../utils/owner.js';import{compact as te,omit as ae,render as B}from'../../utils/render.js';import{Description as ne,useDescriptions as V}from'../description/description.js';import{Label as re,useLabels as j}from'../label/label.js';function le(t,m){return t===m}let H=Symbol(\"RadioGroupContext\");function N(t){let m=$(H,null);if(m===null){let u=new Error(`<${t} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(u,N),u}return m}let he=F({name:\"RadioGroup\",emits:{\"update:modelValue\":t=>!0},props:{as:{type:[Object,String],default:\"div\"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>le},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},id:{type:String,default:null}},inheritAttrs:!1,setup(t,{emit:m,attrs:u,slots:S,expose:g}){var O;let d=(O=t.id)!=null?O:`headlessui-radiogroup-${x()}`,p=k(null),l=k([]),R=j({name:\"RadioGroupLabel\"}),T=V({name:\"RadioGroupDescription\"});g({el:p,$el:p});let[f,G]=q(o(()=>t.modelValue),e=>m(\"update:modelValue\",e),o(()=>t.defaultValue)),s={options:l,value:f,disabled:o(()=>t.disabled),firstOption:o(()=>l.value.find(e=>!e.propsRef.disabled)),containsCheckedOption:o(()=>l.value.some(e=>s.compare(y(e.propsRef.value),y(t.modelValue)))),compare(e,a){if(typeof t.by==\"string\"){let n=t.by;return(e==null?void 0:e[n])===(a==null?void 0:a[n])}return t.by(e,a)},change(e){var n;if(t.disabled||s.compare(y(f.value),y(e)))return!1;let a=(n=l.value.find(i=>s.compare(y(i.propsRef.value),y(e))))==null?void 0:n.propsRef;return a!=null&&a.disabled?!1:(G(e),!0)},registerOption(e){l.value.push(e),l.value=Z(l.value,a=>a.element)},unregisterOption(e){let a=l.value.findIndex(n=>n.id===e);a!==-1&&l.value.splice(a,1)}};W(H,s),Q({container:o(()=>E(p)),accept(e){return e.getAttribute(\"role\")===\"radio\"?NodeFilter.FILTER_REJECT:e.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(e){e.setAttribute(\"role\",\"none\")}});function v(e){if(!p.value||!p.value.contains(e.target))return;let a=l.value.filter(n=>n.propsRef.disabled===!1).map(n=>n.element);switch(e.key){case h.Enter:z(e.currentTarget);break;case h.ArrowLeft:case h.ArrowUp:if(e.preventDefault(),e.stopPropagation(),I(a,w.Previous|w.WrapAround)===P.Success){let i=l.value.find(r=>{var c;return r.element===((c=A(p))==null?void 0:c.activeElement)});i&&s.change(i.propsRef.value)}break;case h.ArrowRight:case h.ArrowDown:if(e.preventDefault(),e.stopPropagation(),I(a,w.Next|w.WrapAround)===P.Success){let i=l.value.find(r=>{var c;return r.element===((c=A(r.element))==null?void 0:c.activeElement)});i&&s.change(i.propsRef.value)}break;case h.Space:{e.preventDefault(),e.stopPropagation();let n=l.value.find(i=>{var r;return i.element===((r=A(i.element))==null?void 0:r.activeElement)});n&&s.change(n.propsRef.value)}break}}let b=o(()=>{var e;return(e=E(p))==null?void 0:e.closest(\"form\")});return D(()=>{J([b],()=>{if(!b.value||t.defaultValue===void 0)return;function e(){s.change(t.defaultValue)}return b.value.addEventListener(\"reset\",e),()=>{var a;(a=b.value)==null||a.removeEventListener(\"reset\",e)}},{immediate:!0})}),()=>{let{disabled:e,name:a,form:n,...i}=t,r={ref:p,id:d,role:\"radiogroup\",\"aria-labelledby\":R.value,\"aria-describedby\":T.value,onKeydown:v};return C(_,[...a!=null&&f.value!=null?ee({[a]:f.value}).map(([c,L])=>C(Y,te({features:X.Hidden,key:c,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:n,disabled:e,name:c,value:L}))):[],B({ourProps:r,theirProps:{...u,...ae(i,[\"modelValue\",\"defaultValue\",\"by\"])},slot:{},attrs:u,slots:S,name:\"RadioGroup\"})])}}});var ie=(u=>(u[u.Empty=1]=\"Empty\",u[u.Active=2]=\"Active\",u))(ie||{});let Oe=F({name:\"RadioGroupOption\",props:{as:{type:[Object,String],default:\"div\"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(t,{attrs:m,slots:u,expose:S}){var i;let g=(i=t.id)!=null?i:`headlessui-radiogroup-option-${x()}`,d=N(\"RadioGroupOption\"),p=j({name:\"RadioGroupLabel\"}),l=V({name:\"RadioGroupDescription\"}),R=k(null),T=o(()=>({value:t.value,disabled:t.disabled})),f=k(1);S({el:R,$el:R});let G=o(()=>E(R));D(()=>d.registerOption({id:g,element:G,propsRef:T})),U(()=>d.unregisterOption(g));let s=o(()=>{var r;return((r=d.firstOption.value)==null?void 0:r.id)===g}),v=o(()=>d.disabled.value||t.disabled),b=o(()=>d.compare(y(d.value.value),y(t.value))),O=o(()=>v.value?-1:b.value||!d.containsCheckedOption.value&&s.value?0:-1);function e(){var r;d.change(t.value)&&(f.value|=2,(r=E(R))==null||r.focus())}function a(){f.value|=2}function n(){f.value&=-3}return()=>{let{value:r,disabled:c,...L}=t,K={checked:b.value,disabled:v.value,active:Boolean(f.value&2)},M={id:g,ref:R,role:\"radio\",\"aria-checked\":b.value?\"true\":\"false\",\"aria-labelledby\":p.value,\"aria-describedby\":l.value,\"aria-disabled\":v.value?!0:void 0,tabIndex:O.value,onClick:v.value?void 0:e,onFocus:v.value?void 0:a,onBlur:v.value?void 0:n};return B({ourProps:M,theirProps:L,slot:K,attrs:m,slots:u,name:\"RadioGroupOption\"})}}}),ke=re,Ee=ne;export{he as RadioGroup,Ee as RadioGroupDescription,ke as RadioGroupLabel,Oe as RadioGroupOption};\n", "import{computed as v,defineComponent as x,inject as L,onMounted as k,onUnmounted as C,provide as j,ref as y,unref as h}from\"vue\";import{useId as w}from'../../hooks/use-id.js';import{render as R}from'../../utils/render.js';let a=Symbol(\"LabelContext\");function d(){let t=L(a,null);if(t===null){let n=new Error(\"You used a <Label /> component, but it is not inside a parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(n,d),n}return t}function E({slot:t={},name:n=\"Label\",props:i={}}={}){let e=y([]);function o(r){return e.value.push(r),()=>{let l=e.value.indexOf(r);l!==-1&&e.value.splice(l,1)}}return j(a,{register:o,slot:t,name:n,props:i}),v(()=>e.value.length>0?e.value.join(\" \"):void 0)}let K=x({name:\"Label\",props:{as:{type:[Object,String],default:\"label\"},passive:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(t,{slots:n,attrs:i}){var r;let e=(r=t.id)!=null?r:`headlessui-label-${w()}`,o=d();return k(()=>C(o.register(e))),()=>{let{name:l=\"Label\",slot:p={},props:c={}}=o,{passive:f,...s}=t,u={...Object.entries(c).reduce((b,[g,m])=>Object.assign(b,{[g]:h(m)}),{}),id:e};return f&&(delete u.onClick,delete u.htmlFor,delete s.onClick),R({ourProps:u,theirProps:s,slot:p,attrs:i,slots:n,name:l})}}});export{K as Label,E as useLabels};\n", "import{computed as u,defineComponent as v,Fragment as H,h as S,inject as M,onMounted as I,provide as P,ref as w,watch as j}from\"vue\";import{useControllable as G}from'../../hooks/use-controllable.js';import{useId as V}from'../../hooks/use-id.js';import{useResolveButtonType as F}from'../../hooks/use-resolve-button-type.js';import{Features as O,Hidden as A}from'../../internal/hidden.js';import{Keys as g}from'../../keyboard.js';import{dom as N}from'../../utils/dom.js';import{attemptSubmit as $}from'../../utils/form.js';import{compact as U,omit as _,render as k}from'../../utils/render.js';import{Description as q,useDescriptions as z}from'../description/description.js';import{Label as J,useLabels as Q}from'../label/label.js';let C=Symbol(\"GroupContext\"),oe=v({name:\"SwitchGroup\",props:{as:{type:[Object,String],default:\"template\"}},setup(l,{slots:c,attrs:i}){let r=w(null),f=Q({name:\"SwitchLabel\",props:{htmlFor:u(()=>{var t;return(t=r.value)==null?void 0:t.id}),onClick(t){r.value&&(t.currentTarget.tagName===\"LABEL\"&&t.preventDefault(),r.value.click(),r.value.focus({preventScroll:!0}))}}}),p=z({name:\"SwitchDescription\"});return P(C,{switchRef:r,labelledby:f,describedby:p}),()=>k({theirProps:l,ourProps:{},slot:{},slots:c,attrs:i,name:\"SwitchGroup\"})}}),ue=v({name:\"Switch\",emits:{\"update:modelValue\":l=>!0},props:{as:{type:[Object,String],default:\"button\"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(l,{emit:c,attrs:i,slots:r,expose:f}){var h;let p=(h=l.id)!=null?h:`headlessui-switch-${V()}`,n=M(C,null),[t,s]=G(u(()=>l.modelValue),e=>c(\"update:modelValue\",e),u(()=>l.defaultChecked));function m(){s(!t.value)}let E=w(null),o=n===null?E:n.switchRef,L=F(u(()=>({as:l.as,type:i.type})),o);f({el:o,$el:o});function D(e){e.preventDefault(),m()}function R(e){e.key===g.Space?(e.preventDefault(),m()):e.key===g.Enter&&$(e.currentTarget)}function x(e){e.preventDefault()}let d=u(()=>{var e,a;return(a=(e=N(o))==null?void 0:e.closest)==null?void 0:a.call(e,\"form\")});return I(()=>{j([d],()=>{if(!d.value||l.defaultChecked===void 0)return;function e(){s(l.defaultChecked)}return d.value.addEventListener(\"reset\",e),()=>{var a;(a=d.value)==null||a.removeEventListener(\"reset\",e)}},{immediate:!0})}),()=>{let{name:e,value:a,form:K,tabIndex:y,...b}=l,T={checked:t.value},B={id:p,ref:o,role:\"switch\",type:L.value,tabIndex:y===-1?0:y,\"aria-checked\":t.value,\"aria-labelledby\":n==null?void 0:n.labelledby.value,\"aria-describedby\":n==null?void 0:n.describedby.value,onClick:D,onKeyup:R,onKeypress:x};return S(H,[e!=null&&t.value!=null?S(A,U({features:O.Hidden,as:\"input\",type:\"checkbox\",hidden:!0,readOnly:!0,checked:t.value,form:K,disabled:b.disabled,name:e,value:a})):null,k({ourProps:B,theirProps:{...i,..._(b,[\"modelValue\",\"defaultChecked\"])},slot:T,attrs:i,slots:r,name:\"Switch\"})])}}}),de=J,ce=q;export{ue as Switch,ce as SwitchDescription,oe as SwitchGroup,de as SwitchLabel};\n", "import{computed as v,defineComponent as L,Fragment as z,h as A,inject as j,onMounted as F,onUnmounted as K,provide as N,ref as P,watch as _,watchEffect as J}from\"vue\";import{useId as $}from'../../hooks/use-id.js';import{useResolveButtonType as Q}from'../../hooks/use-resolve-button-type.js';import{FocusSentinel as V}from'../../internal/focus-sentinel.js';import{Hidden as X}from'../../internal/hidden.js';import{Keys as S}from'../../keyboard.js';import{dom as f}from'../../utils/dom.js';import{Focus as g,focusIn as D,FocusResult as B,sortByDomNode as k}from'../../utils/focus-management.js';import{match as H}from'../../utils/match.js';import{microTask as Y}from'../../utils/micro-task.js';import{getOwnerDocument as Z}from'../../utils/owner.js';import{Features as q,omit as ee,render as M}from'../../utils/render.js';var te=(s=>(s[s.Forwards=0]=\"Forwards\",s[s.Backwards=1]=\"Backwards\",s))(te||{}),le=(d=>(d[d.Less=-1]=\"Less\",d[d.Equal=0]=\"Equal\",d[d.Greater=1]=\"Greater\",d))(le||{});let U=Symbol(\"TabsContext\");function C(a){let b=j(U,null);if(b===null){let s=new Error(`<${a} /> is missing a parent <TabGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(s,C),s}return b}let G=Symbol(\"TabsSSRContext\"),me=L({name:\"TabGroup\",emits:{change:a=>!0},props:{as:{type:[Object,String],default:\"template\"},selectedIndex:{type:[Number],default:null},defaultIndex:{type:[Number],default:0},vertical:{type:[Boolean],default:!1},manual:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(a,{slots:b,attrs:s,emit:d}){var E;let i=P((E=a.selectedIndex)!=null?E:a.defaultIndex),l=P([]),r=P([]),p=v(()=>a.selectedIndex!==null),R=v(()=>p.value?a.selectedIndex:i.value);function y(t){var c;let n=k(u.tabs.value,f),o=k(u.panels.value,f),e=n.filter(I=>{var m;return!((m=f(I))!=null&&m.hasAttribute(\"disabled\"))});if(t<0||t>n.length-1){let I=H(i.value===null?0:Math.sign(t-i.value),{[-1]:()=>1,[0]:()=>H(Math.sign(t),{[-1]:()=>0,[0]:()=>0,[1]:()=>1}),[1]:()=>0}),m=H(I,{[0]:()=>n.indexOf(e[0]),[1]:()=>n.indexOf(e[e.length-1])});m!==-1&&(i.value=m),u.tabs.value=n,u.panels.value=o}else{let I=n.slice(0,t),h=[...n.slice(t),...I].find(W=>e.includes(W));if(!h)return;let O=(c=n.indexOf(h))!=null?c:u.selectedIndex.value;O===-1&&(O=u.selectedIndex.value),i.value=O,u.tabs.value=n,u.panels.value=o}}let u={selectedIndex:v(()=>{var t,n;return(n=(t=i.value)!=null?t:a.defaultIndex)!=null?n:null}),orientation:v(()=>a.vertical?\"vertical\":\"horizontal\"),activation:v(()=>a.manual?\"manual\":\"auto\"),tabs:l,panels:r,setSelectedIndex(t){R.value!==t&&d(\"change\",t),p.value||y(t)},registerTab(t){var o;if(l.value.includes(t))return;let n=l.value[i.value];if(l.value.push(t),l.value=k(l.value,f),!p.value){let e=(o=l.value.indexOf(n))!=null?o:i.value;e!==-1&&(i.value=e)}},unregisterTab(t){let n=l.value.indexOf(t);n!==-1&&l.value.splice(n,1)},registerPanel(t){r.value.includes(t)||(r.value.push(t),r.value=k(r.value,f))},unregisterPanel(t){let n=r.value.indexOf(t);n!==-1&&r.value.splice(n,1)}};N(U,u);let T=P({tabs:[],panels:[]}),x=P(!1);F(()=>{x.value=!0}),N(G,v(()=>x.value?null:T.value));let w=v(()=>a.selectedIndex);return F(()=>{_([w],()=>{var t;return y((t=a.selectedIndex)!=null?t:a.defaultIndex)},{immediate:!0})}),J(()=>{if(!p.value||R.value==null||u.tabs.value.length<=0)return;let t=k(u.tabs.value,f);t.some((o,e)=>f(u.tabs.value[e])!==f(o))&&u.setSelectedIndex(t.findIndex(o=>f(o)===f(u.tabs.value[R.value])))}),()=>{let t={selectedIndex:i.value};return A(z,[l.value.length<=0&&A(V,{onFocus:()=>{for(let n of l.value){let o=f(n);if((o==null?void 0:o.tabIndex)===0)return o.focus(),!0}return!1}}),M({theirProps:{...s,...ee(a,[\"selectedIndex\",\"defaultIndex\",\"manual\",\"vertical\",\"onChange\"])},ourProps:{},slot:t,slots:b,attrs:s,name:\"TabGroup\"})])}}}),pe=L({name:\"TabList\",props:{as:{type:[Object,String],default:\"div\"}},setup(a,{attrs:b,slots:s}){let d=C(\"TabList\");return()=>{let i={selectedIndex:d.selectedIndex.value},l={role:\"tablist\",\"aria-orientation\":d.orientation.value};return M({ourProps:l,theirProps:a,slot:i,attrs:b,slots:s,name:\"TabList\"})}}}),xe=L({name:\"Tab\",props:{as:{type:[Object,String],default:\"button\"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(a,{attrs:b,slots:s,expose:d}){var o;let i=(o=a.id)!=null?o:`headlessui-tabs-tab-${$()}`,l=C(\"Tab\"),r=P(null);d({el:r,$el:r}),F(()=>l.registerTab(r)),K(()=>l.unregisterTab(r));let p=j(G),R=v(()=>{if(p.value){let e=p.value.tabs.indexOf(i);return e===-1?p.value.tabs.push(i)-1:e}return-1}),y=v(()=>{let e=l.tabs.value.indexOf(r);return e===-1?R.value:e}),u=v(()=>y.value===l.selectedIndex.value);function T(e){var I;let c=e();if(c===B.Success&&l.activation.value===\"auto\"){let m=(I=Z(r))==null?void 0:I.activeElement,h=l.tabs.value.findIndex(O=>f(O)===m);h!==-1&&l.setSelectedIndex(h)}return c}function x(e){let c=l.tabs.value.map(m=>f(m)).filter(Boolean);if(e.key===S.Space||e.key===S.Enter){e.preventDefault(),e.stopPropagation(),l.setSelectedIndex(y.value);return}switch(e.key){case S.Home:case S.PageUp:return e.preventDefault(),e.stopPropagation(),T(()=>D(c,g.First));case S.End:case S.PageDown:return e.preventDefault(),e.stopPropagation(),T(()=>D(c,g.Last))}if(T(()=>H(l.orientation.value,{vertical(){return e.key===S.ArrowUp?D(c,g.Previous|g.WrapAround):e.key===S.ArrowDown?D(c,g.Next|g.WrapAround):B.Error},horizontal(){return e.key===S.ArrowLeft?D(c,g.Previous|g.WrapAround):e.key===S.ArrowRight?D(c,g.Next|g.WrapAround):B.Error}}))===B.Success)return e.preventDefault()}let w=P(!1);function E(){var e;w.value||(w.value=!0,!a.disabled&&((e=f(r))==null||e.focus({preventScroll:!0}),l.setSelectedIndex(y.value),Y(()=>{w.value=!1})))}function t(e){e.preventDefault()}let n=Q(v(()=>({as:a.as,type:b.type})),r);return()=>{var m,h;let e={selected:u.value,disabled:(m=a.disabled)!=null?m:!1},{...c}=a,I={ref:r,onKeydown:x,onMousedown:t,onClick:E,id:i,role:\"tab\",type:n.value,\"aria-controls\":(h=f(l.panels.value[y.value]))==null?void 0:h.id,\"aria-selected\":u.value,tabIndex:u.value?0:-1,disabled:a.disabled?!0:void 0};return M({ourProps:I,theirProps:c,slot:e,attrs:b,slots:s,name:\"Tab\"})}}}),Ie=L({name:\"TabPanels\",props:{as:{type:[Object,String],default:\"div\"}},setup(a,{slots:b,attrs:s}){let d=C(\"TabPanels\");return()=>{let i={selectedIndex:d.selectedIndex.value};return M({theirProps:a,ourProps:{},slot:i,attrs:s,slots:b,name:\"TabPanels\"})}}}),ye=L({name:\"TabPanel\",props:{as:{type:[Object,String],default:\"div\"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null},tabIndex:{type:Number,default:0}},setup(a,{attrs:b,slots:s,expose:d}){var T;let i=(T=a.id)!=null?T:`headlessui-tabs-panel-${$()}`,l=C(\"TabPanel\"),r=P(null);d({el:r,$el:r}),F(()=>l.registerPanel(r)),K(()=>l.unregisterPanel(r));let p=j(G),R=v(()=>{if(p.value){let x=p.value.panels.indexOf(i);return x===-1?p.value.panels.push(i)-1:x}return-1}),y=v(()=>{let x=l.panels.value.indexOf(r);return x===-1?R.value:x}),u=v(()=>y.value===l.selectedIndex.value);return()=>{var n;let x={selected:u.value},{tabIndex:w,...E}=a,t={ref:r,id:i,role:\"tabpanel\",\"aria-labelledby\":(n=f(l.tabs.value[y.value]))==null?void 0:n.id,tabIndex:u.value?w:-1};return!u.value&&a.unmount&&!a.static?A(X,{as:\"span\",\"aria-hidden\":!0,...t}):M({ourProps:t,theirProps:E,slot:x,attrs:b,slots:s,features:q.Static|q.RenderStrategy,visible:u.value,name:\"TabPanel\"})}}});export{xe as Tab,me as TabGroup,pe as TabList,ye as TabPanel,Ie as TabPanels};\n", "import{defineComponent as i,h as m,ref as f}from\"vue\";import{Features as l,Hidden as F}from'./hidden.js';let d=i({props:{onFocus:{type:Function,required:!0}},setup(t){let n=f(!0);return()=>n.value?m(F,{as:\"button\",type:\"button\",features:l.Focusable,onFocus(o){o.preventDefault();let e,a=50;function r(){var u;if(a--<=0){e&&cancelAnimationFrame(e);return}if((u=t.onFocus)!=null&&u.call(t)){n.value=!1,cancelAnimationFrame(e);return}e=requestAnimationFrame(r)}e=requestAnimationFrame(r)}}):null}});export{d as FocusSentinel};\n", "import{computed as w,defineComponent as K,h as k,inject as F,normalizeClass as ae,onMounted as C,onUnmounted as z,provide as B,ref as m,watch as le,watchEffect as x}from\"vue\";import{useId as ie}from'../../hooks/use-id.js';import{hasOpenClosed as se,State as u,useOpenClosed as oe,useOpenClosedProvider as ue}from'../../internal/open-closed.js';import{dom as $}from'../../utils/dom.js';import{env as fe}from'../../utils/env.js';import{match as O}from'../../utils/match.js';import{Features as de,omit as ve,render as q,RenderStrategy as T}from'../../utils/render.js';import{Reason as G,transition as J}from'./utils/transition.js';function g(e=\"\"){return e.split(/\\s+/).filter(t=>t.length>1)}let R=Symbol(\"TransitionContext\");var pe=(a=>(a.Visible=\"visible\",a.Hidden=\"hidden\",a))(pe||{});function me(){return F(R,null)!==null}function Te(){let e=F(R,null);if(e===null)throw new Error(\"A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.\");return e}function ge(){let e=F(N,null);if(e===null)throw new Error(\"A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.\");return e}let N=Symbol(\"NestingContext\");function L(e){return\"children\"in e?L(e.children):e.value.filter(({state:t})=>t===\"visible\").length>0}function Q(e){let t=m([]),a=m(!1);C(()=>a.value=!0),z(()=>a.value=!1);function s(n,r=T.Hidden){let l=t.value.findIndex(({id:f})=>f===n);l!==-1&&(O(r,{[T.Unmount](){t.value.splice(l,1)},[T.Hidden](){t.value[l].state=\"hidden\"}}),!L(t)&&a.value&&(e==null||e()))}function h(n){let r=t.value.find(({id:l})=>l===n);return r?r.state!==\"visible\"&&(r.state=\"visible\"):t.value.push({id:n,state:\"visible\"}),()=>s(n,T.Unmount)}return{children:t,register:h,unregister:s}}let W=de.RenderStrategy,he=K({props:{as:{type:[Object,String],default:\"div\"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:\"\"},enterFrom:{type:[String],default:\"\"},enterTo:{type:[String],default:\"\"},entered:{type:[String],default:\"\"},leave:{type:[String],default:\"\"},leaveFrom:{type:[String],default:\"\"},leaveTo:{type:[String],default:\"\"}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:a,slots:s,expose:h}){let n=m(0);function r(){n.value|=u.Opening,t(\"beforeEnter\")}function l(){n.value&=~u.Opening,t(\"afterEnter\")}function f(){n.value|=u.Closing,t(\"beforeLeave\")}function S(){n.value&=~u.Closing,t(\"afterLeave\")}if(!me()&&se())return()=>k(Se,{...e,onBeforeEnter:r,onAfterEnter:l,onBeforeLeave:f,onAfterLeave:S},s);let d=m(null),y=w(()=>e.unmount?T.Unmount:T.Hidden);h({el:d,$el:d});let{show:v,appear:A}=Te(),{register:D,unregister:H}=ge(),i=m(v.value?\"visible\":\"hidden\"),I={value:!0},c=ie(),b={value:!1},P=Q(()=>{!b.value&&i.value!==\"hidden\"&&(i.value=\"hidden\",H(c),S())});C(()=>{let o=D(c);z(o)}),x(()=>{if(y.value===T.Hidden&&c){if(v.value&&i.value!==\"visible\"){i.value=\"visible\";return}O(i.value,{[\"hidden\"]:()=>H(c),[\"visible\"]:()=>D(c)})}});let j=g(e.enter),M=g(e.enterFrom),X=g(e.enterTo),_=g(e.entered),Y=g(e.leave),Z=g(e.leaveFrom),ee=g(e.leaveTo);C(()=>{x(()=>{if(i.value===\"visible\"){let o=$(d);if(o instanceof Comment&&o.data===\"\")throw new Error(\"Did you forget to passthrough the `ref` to the actual DOM node?\")}})});function te(o){let E=I.value&&!A.value,p=$(d);!p||!(p instanceof HTMLElement)||E||(b.value=!0,v.value&&r(),v.value||f(),o(v.value?J(p,j,M,X,_,V=>{b.value=!1,V===G.Finished&&l()}):J(p,Y,Z,ee,_,V=>{b.value=!1,V===G.Finished&&(L(P)||(i.value=\"hidden\",H(c),S()))})))}return C(()=>{le([v],(o,E,p)=>{te(p),I.value=!1},{immediate:!0})}),B(N,P),ue(w(()=>O(i.value,{[\"visible\"]:u.Open,[\"hidden\"]:u.Closed})|n.value)),()=>{let{appear:o,show:E,enter:p,enterFrom:V,enterTo:Ce,entered:ye,leave:be,leaveFrom:Ee,leaveTo:Ve,...U}=e,ne={ref:d},re={...U,...A.value&&v.value&&fe.isServer?{class:ae([a.class,U.class,...j,...M])}:{}};return q({theirProps:re,ourProps:ne,slot:{},slots:s,attrs:a,features:W,visible:i.value===\"visible\",name:\"TransitionChild\"})}}}),ce=he,Se=K({inheritAttrs:!1,props:{as:{type:[Object,String],default:\"div\"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:\"\"},enterFrom:{type:[String],default:\"\"},enterTo:{type:[String],default:\"\"},entered:{type:[String],default:\"\"},leave:{type:[String],default:\"\"},leaveFrom:{type:[String],default:\"\"},leaveTo:{type:[String],default:\"\"}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:a,slots:s}){let h=oe(),n=w(()=>e.show===null&&h!==null?(h.value&u.Open)===u.Open:e.show);x(()=>{if(![!0,!1].includes(n.value))throw new Error('A <Transition /> is used but it is missing a `:show=\"true | false\"` prop.')});let r=m(n.value?\"visible\":\"hidden\"),l=Q(()=>{r.value=\"hidden\"}),f=m(!0),S={show:n,appear:w(()=>e.appear||!f.value)};return C(()=>{x(()=>{f.value=!1,n.value?r.value=\"visible\":L(l)||(r.value=\"hidden\")})}),B(N,l),B(R,S),()=>{let d=ve(e,[\"show\",\"appear\",\"unmount\",\"onBeforeEnter\",\"onBeforeLeave\",\"onAfterEnter\",\"onAfterLeave\"]),y={unmount:e.unmount};return q({ourProps:{...y,as:\"template\"},theirProps:{},slot:{},slots:{...s,default:()=>[k(ce,{onBeforeEnter:()=>t(\"beforeEnter\"),onAfterEnter:()=>t(\"afterEnter\"),onBeforeLeave:()=>t(\"beforeLeave\"),onAfterLeave:()=>t(\"afterLeave\"),...a,...y,...d},s.default)]},attrs:{},features:W,visible:r.value===\"visible\",name:\"Transition\"})}}});export{he as TransitionChild,Se as TransitionRoot};\n", "function l(r){let e={called:!1};return(...t)=>{if(!e.called)return e.called=!0,r(...t)}}export{l as once};\n", "import{disposables as p}from'../../../utils/disposables.js';import{once as f}from'../../../utils/once.js';function m(e,...t){e&&t.length>0&&e.classList.add(...t)}function d(e,...t){e&&t.length>0&&e.classList.remove(...t)}var g=(i=>(i.Finished=\"finished\",i.Cancelled=\"cancelled\",i))(g||{});function F(e,t){let i=p();if(!e)return i.dispose;let{transitionDuration:n,transitionDelay:a}=getComputedStyle(e),[l,s]=[n,a].map(o=>{let[u=0]=o.split(\",\").filter(Boolean).map(r=>r.includes(\"ms\")?parseFloat(r):parseFloat(r)*1e3).sort((r,c)=>c-r);return u});return l!==0?i.setTimeout(()=>t(\"finished\"),l+s):t(\"finished\"),i.add(()=>t(\"cancelled\")),i.dispose}function L(e,t,i,n,a,l){let s=p(),o=l!==void 0?f(l):()=>{};return d(e,...a),m(e,...t,...i),s.nextFrame(()=>{d(e,...i),m(e,...n),s.add(F(e,u=>(d(e,...n,...t),m(e,...a),o(u))))}),s.add(()=>d(e,...t,...i,...n,...a)),s.add(()=>o(\"cancelled\")),s.dispose}export{g as Reason,L as transition};\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          // Try next placement and re-run the lifecycle.\n          return {\n            data: {\n              index: nextIndex,\n              overflows: overflowsData\n            },\n            reset: {\n              placement: nextPlacement\n            }\n          };\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { rectToClientRect, detectOverflow as detectOverflow$1, offset as offset$1, autoPlacement as autoPlacement$1, shift as shift$1, flip as flip$1, size as size$1, hide as hide$1, arrow as arrow$1, inline as inline$1, limitShift as limitShift$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n      // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n", "import { arrow as arrow$1, computePosition } from '@floating-ui/dom';\nexport { autoPlacement, autoUpdate, computePosition, detectOverflow, flip, getOverflowAncestors, hide, inline, limitShift, offset, platform, shift, size } from '@floating-ui/dom';\nimport { isNode, getNodeName } from '@floating-ui/utils/dom';\nimport { unref, computed, ref, shallowRef, watch, getCurrentScope, onScopeDispose, shallowReadonly } from 'vue-demi';\n\nfunction isComponentPublicInstance(target) {\n  return target != null && typeof target === 'object' && '$el' in target;\n}\nfunction unwrapElement(target) {\n  if (isComponentPublicInstance(target)) {\n    const element = target.$el;\n    return isNode(element) && getNodeName(element) === '#comment' ? null : element;\n  }\n  return target;\n}\n\nfunction toValue(source) {\n  return typeof source === 'function' ? source() : unref(source);\n}\n\n/**\n * Positions an inner element of the floating element such that it is centered to the reference element.\n * @param options The arrow options.\n * @see https://floating-ui.com/docs/arrow\n */\nfunction arrow(options) {\n  return {\n    name: 'arrow',\n    options,\n    fn(args) {\n      const element = unwrapElement(toValue(options.element));\n      if (element == null) {\n        return {};\n      }\n      return arrow$1({\n        element,\n        padding: options.padding\n      }).fn(args);\n    }\n  };\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element next to a reference element when it is given a certain CSS positioning strategy.\n * @param reference The reference template ref.\n * @param floating The floating template ref.\n * @param options The floating options.\n * @see https://floating-ui.com/docs/vue\n */\nfunction useFloating(reference, floating, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const whileElementsMountedOption = options.whileElementsMounted;\n  const openOption = computed(() => {\n    var _toValue;\n    return (_toValue = toValue(options.open)) != null ? _toValue : true;\n  });\n  const middlewareOption = computed(() => toValue(options.middleware));\n  const placementOption = computed(() => {\n    var _toValue2;\n    return (_toValue2 = toValue(options.placement)) != null ? _toValue2 : 'bottom';\n  });\n  const strategyOption = computed(() => {\n    var _toValue3;\n    return (_toValue3 = toValue(options.strategy)) != null ? _toValue3 : 'absolute';\n  });\n  const transformOption = computed(() => {\n    var _toValue4;\n    return (_toValue4 = toValue(options.transform)) != null ? _toValue4 : true;\n  });\n  const referenceElement = computed(() => unwrapElement(reference.value));\n  const floatingElement = computed(() => unwrapElement(floating.value));\n  const x = ref(0);\n  const y = ref(0);\n  const strategy = ref(strategyOption.value);\n  const placement = ref(placementOption.value);\n  const middlewareData = shallowRef({});\n  const isPositioned = ref(false);\n  const floatingStyles = computed(() => {\n    const initialStyles = {\n      position: strategy.value,\n      left: '0',\n      top: '0'\n    };\n    if (!floatingElement.value) {\n      return initialStyles;\n    }\n    const xVal = roundByDPR(floatingElement.value, x.value);\n    const yVal = roundByDPR(floatingElement.value, y.value);\n    if (transformOption.value) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + xVal + \"px, \" + yVal + \"px)\",\n        ...(getDPR(floatingElement.value) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy.value,\n      left: xVal + \"px\",\n      top: yVal + \"px\"\n    };\n  });\n  let whileElementsMountedCleanup;\n  function update() {\n    if (referenceElement.value == null || floatingElement.value == null) {\n      return;\n    }\n    const open = openOption.value;\n    computePosition(referenceElement.value, floatingElement.value, {\n      middleware: middlewareOption.value,\n      placement: placementOption.value,\n      strategy: strategyOption.value\n    }).then(position => {\n      x.value = position.x;\n      y.value = position.y;\n      strategy.value = position.strategy;\n      placement.value = position.placement;\n      middlewareData.value = position.middlewareData;\n      /**\n       * The floating element's position may be recomputed while it's closed\n       * but still mounted (such as when transitioning out). To ensure\n       * `isPositioned` will be `false` initially on the next open, avoid\n       * setting it to `true` when `open === false` (must be specified).\n       */\n      isPositioned.value = open !== false;\n    });\n  }\n  function cleanup() {\n    if (typeof whileElementsMountedCleanup === 'function') {\n      whileElementsMountedCleanup();\n      whileElementsMountedCleanup = undefined;\n    }\n  }\n  function attach() {\n    cleanup();\n    if (whileElementsMountedOption === undefined) {\n      update();\n      return;\n    }\n    if (referenceElement.value != null && floatingElement.value != null) {\n      whileElementsMountedCleanup = whileElementsMountedOption(referenceElement.value, floatingElement.value, update);\n      return;\n    }\n  }\n  function reset() {\n    if (!openOption.value) {\n      isPositioned.value = false;\n    }\n  }\n  watch([middlewareOption, placementOption, strategyOption, openOption], update, {\n    flush: 'sync'\n  });\n  watch([referenceElement, floatingElement], attach, {\n    flush: 'sync'\n  });\n  watch(openOption, reset, {\n    flush: 'sync'\n  });\n  if (getCurrentScope()) {\n    onScopeDispose(cleanup);\n  }\n  return {\n    x: shallowReadonly(x),\n    y: shallowReadonly(y),\n    strategy: shallowReadonly(strategy),\n    placement: shallowReadonly(placement),\n    middlewareData: shallowReadonly(middlewareData),\n    isPositioned: shallowReadonly(isPositioned),\n    floatingStyles,\n    update\n  };\n}\n\nexport { arrow, useFloating };\n"], "mappings": ";;;AAGA,SAAS,OAAOA,IAAG,YAAYC,KAAI,SAAS,IAAI,SAASC,IAAG,aAAaC,IAAG,mBAAmBC,KAAI,YAAYC,IAAG,eAAeC,KAAI,cAAcC,KAAG,cAAcC,IAAG,KAAKC,IAAG,SAASC,IAAG,cAAcC,KAAI,YAAY,IAAI,WAAWC,IAAG,mBAAmBC,KAAG,UAAUC,IAAG,sBAAsBC,IAAG,cAAcC,WAAU;;;ACC/S,SAAA,KACd,SACA,IACA,MAMA;AACI,MAAA,OAAO,KAAK,eAAe,CAAC;AAC5B,MAAA;AAEJ,WAAS,mBAA4B;AAbvB,QAAA,IAAA,IAAA,IAAA;AAcR,QAAA;AACJ,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,GAAgB,WAAU,KAAK,IAAI;AAEnD,UAAM,UAAU,QAAQ;AAExB,UAAM,cACJ,QAAQ,WAAW,KAAK,UACxB,QAAQ,KAAK,CAAC,KAAU,UAAkB,KAAK,KAAK,MAAM,GAAG;AAE/D,QAAI,CAAC,aAAa;AACT,aAAA;IAAA;AAGF,WAAA;AAEH,QAAA;AACJ,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,GAAgB,cAAa,KAAK,IAAI;AAE7C,aAAA,GAAG,GAAG,OAAO;AAEtB,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,IAAgB;AACxB,YAAA,aAAa,KAAK,OAAO,KAAK,IAAA,IAAQ,WAAY,GAAG,IAAI;AACzD,YAAA,gBAAgB,KAAK,OAAO,KAAK,IAAA,IAAQ,cAAe,GAAG,IAAI;AACrE,YAAM,sBAAsB,gBAAgB;AAEtC,YAAA,MAAM,CAAC,KAAsB,QAAgB;AACjD,cAAM,OAAO,GAAG;AACT,eAAA,IAAI,SAAS,KAAK;AACvB,gBAAM,MAAM;QAAA;AAEP,eAAA;MACT;AAEQ,cAAA;QACN,OAAO,IAAI,eAAe,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC;QACnD;;;yBAGiB,KAAK;UAChB;UACA,KAAK,IAAI,MAAM,MAAM,qBAAqB,GAAG;QAC9C,CAAA;QACL,QAAA,OAAA,SAAA,KAAM;MACR;IAAA;AAGF,KAAA,KAAA,QAAA,OAAA,SAAA,KAAM,aAAN,OAAA,SAAA,GAAA,KAAA,MAAiB,MAAA;AAEV,WAAA;EAAA;AAIQ,mBAAA,aAAa,CAAC,YAAwB;AAC9C,WAAA;EACT;AAEO,SAAA;AACT;AAEgB,SAAA,aAAgB,OAAsB,KAAiB;AACrE,MAAI,UAAU,QAAW;AACjB,UAAA,IAAI,MAAM,uBAAuB,MAAM,KAAK,GAAG,KAAK,EAAE,EAAE;EAAA,OACzD;AACE,WAAA;EAAA;AAEX;AAEa,IAAA,cAAc,CAACC,IAAWC,OAAc,KAAK,IAAID,KAAIC,EAAC,IAAI;AAEhE,IAAM,WAAW,CACtB,cACA,IACA,OACG;AACC,MAAA;AACJ,SAAO,YAAwB,MAAkB;AAC/C,iBAAa,aAAa,SAAS;AACvB,gBAAA,aAAa,WAAW,MAAM,GAAG,MAAM,MAAM,IAAI,GAAG,EAAE;EACpE;AACF;;;ACnDa,IAAA,sBAAsB,CAAC,UAAkB;AAEzC,IAAA,wBAAwB,CAAC,UAAiB;AACrD,QAAM,QAAQ,KAAK,IAAI,MAAM,aAAa,MAAM,UAAU,CAAC;AACrD,QAAA,MAAM,KAAK,IAAI,MAAM,WAAW,MAAM,UAAU,MAAM,QAAQ,CAAC;AAErE,QAAM,MAAM,CAAC;AAEb,WAASC,MAAI,OAAOA,OAAK,KAAKA,OAAK;AACjC,QAAI,KAAKA,GAAC;EAAA;AAGL,SAAA;AACT;AAEa,IAAA,qBAAqB,CAChC,UACA,OACG;AACH,QAAM,UAAU,SAAS;AACzB,MAAI,CAAC,SAAS;AACZ;EAAA;AAEF,QAAM,eAAe,SAAS;AAC9B,MAAI,CAAC,cAAc;AACjB;EAAA;AAGI,QAAA,UAAU,CAAC,SAAe;AACxB,UAAA,EAAE,OAAO,OAAA,IAAW;AACvB,OAAA,EAAE,OAAO,KAAK,MAAM,KAAK,GAAG,QAAQ,KAAK,MAAM,MAAM,EAAA,CAAG;EAC7D;AAEQ,UAAA,QAAQ,sBAAA,CAAuB;AAEnC,MAAA,CAAC,aAAa,gBAAgB;AAChC,WAAO,MAAM;IAAC;EAAA;AAGhB,QAAM,WAAW,IAAI,aAAa,eAAe,CAAC,YAAY;AAC5D,UAAM,MAAM,MAAM;AACV,YAAA,QAAQ,QAAQ,CAAC;AACvB,UAAI,SAAA,OAAA,SAAA,MAAO,eAAe;AAClB,cAAA,MAAM,MAAM,cAAc,CAAC;AACjC,YAAI,KAAK;AACP,kBAAQ,EAAE,OAAO,IAAI,YAAY,QAAQ,IAAI,UAAA,CAAW;AACxD;QAAA;MACF;AAEM,cAAA,QAAQ,sBAAA,CAAuB;IACzC;AAEA,aAAS,QAAQ,sCACb,sBAAsB,GAAG,IACzB,IAAI;EAAA,CACT;AAED,WAAS,QAAQ,SAAS,EAAE,KAAK,aAAA,CAAc;AAE/C,SAAO,MAAM;AACX,aAAS,UAAU,OAAO;EAC5B;AACF;AAEA,IAAM,0BAA0B;EAC9B,SAAS;AACX;AAuBA,IAAM,oBACJ,OAAO,UAAU,cAAc,OAAO,iBAAiB;AAI5C,IAAA,uBAAuB,CAClC,UACA,OACG;AACH,QAAM,UAAU,SAAS;AACzB,MAAI,CAAC,SAAS;AACZ;EAAA;AAEF,QAAM,eAAe,SAAS;AAC9B,MAAI,CAAC,cAAc;AACjB;EAAA;AAGF,MAAIC,UAAS;AACb,QAAM,WACJ,SAAS,QAAQ,qBAAqB,oBAClC,MAAM,SACN;IACE;IACA,MAAM;AACJ,SAAGA,SAAQ,KAAK;IAClB;IACA,SAAS,QAAQ;EACnB;AAEA,QAAA,gBAAgB,CAAC,gBAAyB,MAAM;AACpD,UAAM,EAAE,YAAY,MAAM,IAAI,SAAS;AAC9B,IAAAA,UAAA,aACL,QAAQ,YAAY,KAAM,SAAS,MAAO,KAC1C,QAAQ,WAAW;AACd,aAAA;AACT,OAAGA,SAAQ,WAAW;EACxB;AACM,QAAA,UAAU,cAAc,IAAI;AAC5B,QAAA,aAAa,cAAc,KAAK;AAC3B,aAAA;AAEH,UAAA,iBAAiB,UAAU,SAAS,uBAAuB;AAC7D,QAAA,yBACJ,SAAS,QAAQ,qBAAqB;AACxC,MAAI,wBAAwB;AAClB,YAAA,iBAAiB,aAAa,YAAY,uBAAuB;EAAA;AAE3E,SAAO,MAAM;AACH,YAAA,oBAAoB,UAAU,OAAO;AAC7C,QAAI,wBAAwB;AAClB,cAAA,oBAAoB,aAAa,UAAU;IAAA;EAEvD;AACF;AAkDO,IAAM,iBAAiB,CAC5B,SACA,OACA,aACG;AACH,MAAI,SAAA,OAAA,SAAA,MAAO,eAAe;AAClB,UAAA,MAAM,MAAM,cAAc,CAAC;AACjC,QAAI,KAAK;AACP,YAAMC,QAAO,KAAK;QAChB,IAAI,SAAS,QAAQ,aAAa,eAAe,WAAW;MAC9D;AACO,aAAAA;IAAA;EACT;AAEF,SAAO,KAAK;IACV,QAAQ,sBAAsB,EAC5B,SAAS,QAAQ,aAAa,UAAU,QAC1C;EACF;AACF;AAkBa,IAAA,gBAAgB,CAC3BC,SACA;EACE,cAAc;EACd;AACF,GACA,aACG;;AACH,QAAM,WAAWA,UAAS;AAE1B,GAAA,MAAA,KAAA,SAAS,kBAAT,OAAA,SAAA,GAAwB,aAAxB,OAAA,SAAA,GAAA,KAAA,IAAmC;IACjC,CAAC,SAAS,QAAQ,aAAa,SAAS,KAAK,GAAG;IAChD;EAAA,CAAA;AAEJ;AA0DO,IAAM,cAAN,MAGL;EA0DA,YAAY,MAAwD;AAzDpE,SAAQ,SAAqC,CAAC;AAEP,SAAA,gBAAA;AACa,SAAA,eAAA;AACtC,SAAA,cAAA;AACd,SAAQ,yBAAwC;AAChD,SAAA,oBAAwC,CAAC;AACjC,SAAA,gBAAA,oBAAoB,IAAiB;AAC7C,SAAQ,8BAA6C,CAAC;AAC5B,SAAA,aAAA;AACI,SAAA,eAAA;AACY,SAAA,kBAAA;AAC1C,SAAQ,oBAAoB;AAQ5B,SAAA,gBAAA,oBAAoB,IAAuB;AAC3C,SAAQ,WAAkB,uBAAA;AACxB,UAAI,MAA6B;AAEjC,YAAM,MAAM,MAAM;AAChB,YAAI,KAAK;AACA,iBAAA;QAAA;AAGT,YAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,aAAa,gBAAgB;AACpD,iBAAA;QAAA;AAGT,eAAQ,MAAM,IAAI,KAAK,aAAa,eAAe,CAAC,YAAY;AACtD,kBAAA,QAAQ,CAAC,UAAU;AACzB,kBAAM,MAAM,MAAM;AACX,mBAAA,gBAAgB,MAAM,QAAwB,KAAK;YAC1D;AACA,iBAAK,QAAQ,sCACT,sBAAsB,GAAG,IACzB,IAAI;UAAA,CACT;QAAA,CACF;MACH;AAEO,aAAA;QACL,YAAY,MAAM;;AAChB,WAAA,KAAA,IAAA,MAAA,OAAA,SAAA,GAAO,WAAA;AACD,gBAAA;QACR;QACA,SAAS,CAAC,WAAA;;AACR,kBAAA,KAAA,IAAI,MAAJ,OAAA,SAAA,GAAO,QAAQ,QAAQ,EAAE,KAAK,aAAA,CAAA;;QAChC,WAAW,CAAC,WAAA;;AAAoB,kBAAA,KAAA,IAAI,MAAJ,OAAA,SAAA,GAAO,UAAU,MAAA;QAAA;MACnD;IAAA,GACC;AACsD,SAAA,QAAA;AAMzD,SAAA,aAAa,CAACC,UAA2D;AAChE,aAAA,QAAQA,KAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,YAAI,OAAO,UAAU,YAAa,QAAQA,MAAa,GAAG;MAAA,CAC3D;AAED,WAAK,UAAU;QACb,OAAO;QACP,eAAe;QACf,UAAU;QACV,cAAc;QACd,YAAY;QACZ,oBAAoB;QACpB,kBAAkB;QAClB,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,UAAU,MAAM;QAAC;QACjB;QACA,aAAa,EAAE,OAAO,GAAG,QAAQ,EAAE;QACnC,cAAc;QACd,KAAK;QACL,gBAAgB;QAChB,0BAA0B,CAAC;QAC3B,OAAO;QACP,uBAAuB;QACvB,SAAS;QACT,OAAO;QACP,mBAAmB;QACnB,qCAAqC;QACrC,GAAGA;MACL;IACF;AAEQ,SAAA,SAAS,CAAC,SAAkB;;AAC7B,OAAA,MAAA,KAAA,KAAA,SAAQ,aAAR,OAAA,SAAA,GAAA,KAAA,IAAmB,MAAM,IAAA;IAChC;AAEA,SAAQ,cAAc;MACpB,MAAM;AACJ,aAAK,eAAe;AAEb,eAAA;UACL,KAAK;UACL,KAAK,QAAQ,KAAK,MAAM,aAAa;UACrC,KAAK,QAAQ,KAAK,MAAM,WAAW;QACrC;MACF;MACA,CAAC,gBAAgB;AACf,aAAK,OAAO,WAAW;MACzB;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;QAC1B,aAAa;UACX,KAAK;UACL,KAAK,QAAQ,KAAK,MAAM,aAAa;UACrC,KAAK,QAAQ,KAAK,MAAM,WAAW;QAAA;MACrC;IAEJ;AAEA,SAAQ,UAAU,MAAM;AACjB,WAAA,OAAO,OAAO,OAAO,EAAE,QAAQ,CAACC,QAAMA,IAAA,CAAI;AAC/C,WAAK,SAAS,CAAC;AACf,WAAK,SAAS,WAAW;AACzB,WAAK,gBAAgB;AACrB,WAAK,eAAe;IACtB;AAEA,SAAA,YAAY,MAAM;AAChB,aAAO,MAAM;AACX,aAAK,QAAQ;MACf;IACF;AAEA,SAAA,cAAc,MAAM;;AAClB,YAAM,gBAAgB,KAAK,QAAQ,UAC/B,KAAK,QAAQ,iBAAA,IACb;AAEA,UAAA,KAAK,kBAAkB,eAAe;AACxC,aAAK,QAAQ;AAEb,YAAI,CAAC,eAAe;AAClB,eAAK,YAAY;AACjB;QAAA;AAGF,aAAK,gBAAgB;AAErB,YAAI,KAAK,iBAAiB,mBAAmB,KAAK,eAAe;AAC1D,eAAA,eAAe,KAAK,cAAc,cAAc;QAAA,OAChD;AACA,eAAA,iBAAe,KAAA,KAAK,kBAAL,OAAA,SAAA,GAAoB,WAAU;QAAA;AAG/C,aAAA,cAAc,QAAQ,CAAC,WAAW;AAChC,eAAA,SAAS,QAAQ,MAAM;QAAA,CAC7B;AAEI,aAAA,gBAAgB,KAAK,gBAAA,GAAmB;UAC3C,aAAa;UACb,UAAU;QAAA,CACX;AAED,aAAK,OAAO;UACV,KAAK,QAAQ,mBAAmB,MAAM,CAAC,SAAS;AAC9C,iBAAK,aAAa;AAClB,iBAAK,YAAY;UAClB,CAAA;QACH;AAEA,aAAK,OAAO;UACV,KAAK,QAAQ,qBAAqB,MAAM,CAACF,SAAQ,gBAAgB;AAC/D,iBAAK,oBAAoB;AACzB,iBAAK,kBAAkB,cACnB,KAAK,gBAAA,IAAoBA,UACvB,YACA,aACF;AACJ,iBAAK,eAAeA;AACpB,iBAAK,cAAc;AAEnB,iBAAK,YAAY;UAClB,CAAA;QACH;MAAA;IAEJ;AAEA,SAAQ,UAAU,MAAM;AAClB,UAAA,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAK,aAAa;AACX,eAAA;MAAA;AAGT,WAAK,aAAa,KAAK,cAAc,KAAK,QAAQ;AAElD,aAAO,KAAK,WAAW,KAAK,QAAQ,aAAa,UAAU,QAAQ;IACrE;AAEA,SAAQ,kBAAkB,MAAM;AAC1B,UAAA,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAK,eAAe;AACb,eAAA;MAAA;AAGT,WAAK,eACH,KAAK,iBACJ,OAAO,KAAK,QAAQ,kBAAkB,aACnC,KAAK,QAAQ,cAAc,IAC3B,KAAK,QAAQ;AAEnB,aAAO,KAAK;IACd;AAEQ,SAAA,yBAAyB,CAC/B,cACA,UACG;AACG,YAAA,4BAAA,oBAAgC,IAAkB;AAClD,YAAA,uBAAA,oBAA2B,IAAyB;AAC1D,eAASG,MAAI,QAAQ,GAAGA,OAAK,GAAGA,OAAK;AAC7B,cAAA,cAAc,aAAaA,GAAC;AAElC,YAAI,0BAA0B,IAAI,YAAY,IAAI,GAAG;AACnD;QAAA;AAGF,cAAM,8BAA8B,qBAAqB;UACvD,YAAY;QACd;AACA,YACE,+BAA+B,QAC/B,YAAY,MAAM,4BAA4B,KAC9C;AACqB,+BAAA,IAAI,YAAY,MAAM,WAAW;QAC7C,WAAA,YAAY,MAAM,4BAA4B,KAAK;AAClC,oCAAA,IAAI,YAAY,MAAM,IAAI;QAAA;AAGtD,YAAI,0BAA0B,SAAS,KAAK,QAAQ,OAAO;AACzD;QAAA;MACF;AAGF,aAAO,qBAAqB,SAAS,KAAK,QAAQ,QAC9C,MAAM,KAAK,qBAAqB,OAAA,CAAQ,EAAE,KAAK,CAACC,IAAGC,OAAM;AACnD,YAAAD,GAAE,QAAQC,GAAE,KAAK;AACZ,iBAAAD,GAAE,QAAQC,GAAE;QAAA;AAGd,eAAAD,GAAE,MAAMC,GAAE;MAAA,CAClB,EAAE,CAAC,IACJ;IACN;AAEA,SAAQ,wBAAwB;MAC9B,MAAM;QACJ,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;MACf;MACA,CAAC,OAAO,cAAc,cAAc,YAAY,YAAY;AAC1D,aAAK,8BAA8B,CAAC;AAC7B,eAAA;UACL;UACA;UACA;UACA;UACA;QACF;MACF;MACA;QACE,KAAK;MAAA;IAET;AAEA,SAAQ,kBAAkB;MACxB,MAAM,CAAC,KAAK,sBAAA,GAAyB,KAAK,aAAa;MACvD,CACE,EAAE,OAAO,cAAc,cAAc,YAAY,QAAA,GACjD,kBACG;AACH,YAAI,CAAC,SAAS;AACZ,eAAK,oBAAoB,CAAC;AAC1B,eAAK,cAAc,MAAM;AACzB,iBAAO,CAAC;QAAA;AAGN,YAAA,KAAK,kBAAkB,WAAW,GAAG;AAClC,eAAA,oBAAoB,KAAK,QAAQ;AACjC,eAAA,kBAAkB,QAAQ,CAAC,SAAS;AACvC,iBAAK,cAAc,IAAI,KAAK,KAAK,KAAK,IAAI;UAAA,CAC3C;QAAA;AAGG,cAAAC,OACJ,KAAK,4BAA4B,SAAS,IACtC,KAAK,IAAI,GAAG,KAAK,2BAA2B,IAC5C;AACN,aAAK,8BAA8B,CAAC;AAEpC,cAAM,eAAe,KAAK,kBAAkB,MAAM,GAAGA,IAAG;AAExD,iBAASC,MAAID,MAAKC,MAAI,OAAOA,OAAK;AAC1B,gBAAA,MAAM,WAAWA,GAAC;AAExB,gBAAM,sBACJ,KAAK,QAAQ,UAAU,IACnB,aAAaA,MAAI,CAAC,IAClB,KAAK,uBAAuB,cAAcA,GAAC;AAEjD,gBAAM,QAAQ,sBACV,oBAAoB,MAAM,KAAK,QAAQ,MACvC,eAAe;AAEb,gBAAA,eAAe,cAAc,IAAI,GAAG;AACpC,gBAAAC,QACJ,OAAO,iBAAiB,WACpB,eACA,KAAK,QAAQ,aAAaD,GAAC;AAEjC,gBAAM,MAAM,QAAQC;AAEpB,gBAAM,OAAO,sBACT,oBAAoB,OACpBD,MAAI,KAAK,QAAQ;AAErB,uBAAaA,GAAC,IAAI;YAChB,OAAOA;YACP;YACA,MAAAC;YACA;YACA;YACA;UACF;QAAA;AAGF,aAAK,oBAAoB;AAElB,eAAA;MACT;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEiB,SAAA,iBAAA;MACf,MAAM;QACJ,KAAK,gBAAgB;QACrB,KAAK,QAAQ;QACb,KAAK,gBAAgB;QACrB,KAAK,QAAQ;MACf;MACA,CAAC,cAAc,WAAW,cAAc,UAAU;AAChD,eAAQ,KAAK,QACX,aAAa,SAAS,KAAK,YAAY,IACnC,eAAe;UACb;UACA;UACA;UACA;QACD,CAAA,IACD;MACR;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEoB,SAAA,oBAAA;MAClB,MAAM;AACJ,YAAI,aAA4B;AAChC,YAAI,WAA0B;AACxB,cAAA,QAAQ,KAAK,eAAe;AAClC,YAAI,OAAO;AACT,uBAAa,MAAM;AACnB,qBAAW,MAAM;QAAA;AAEnB,aAAK,YAAY,WAAW,CAAC,KAAK,aAAa,YAAY,QAAQ,CAAC;AAC7D,eAAA;UACL,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb;UACA;QACF;MACF;MACA,CAAC,gBAAgB,UAAU,OAAO,YAAY,aAAa;AACzD,eAAO,eAAe,QAAQ,aAAa,OACvC,CAAA,IACA,eAAe;UACb;UACA;UACA;UACA;QAAA,CACD;MACP;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEA,SAAA,mBAAmB,CAAC,SAAuB;AACnC,YAAA,gBAAgB,KAAK,QAAQ;AAC7B,YAAA,WAAW,KAAK,aAAa,aAAa;AAEhD,UAAI,CAAC,UAAU;AACL,gBAAA;UACN,2BAA2B,aAAa;QAC1C;AACO,eAAA;MAAA;AAGF,aAAA,SAAS,UAAU,EAAE;IAC9B;AAEQ,SAAA,kBAAkB,CACxB,MACA,UACG;AACG,YAAA,QAAQ,KAAK,iBAAiB,IAAI;AAClC,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACT;MAAA;AAEF,YAAM,MAAM,KAAK;AACjB,YAAM,WAAW,KAAK,cAAc,IAAI,GAAG;AAE3C,UAAI,aAAa,MAAM;AACrB,YAAI,UAAU;AACP,eAAA,SAAS,UAAU,QAAQ;QAAA;AAE7B,aAAA,SAAS,QAAQ,IAAI;AACrB,aAAA,cAAc,IAAI,KAAK,IAAI;MAAA;AAGlC,UAAI,KAAK,aAAa;AACf,aAAA,WAAW,OAAO,KAAK,QAAQ,eAAe,MAAM,OAAO,IAAI,CAAC;MAAA;IAEzE;AAEa,SAAA,aAAA,CAAC,OAAeA,UAAiB;AACtC,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACT;MAAA;AAEF,YAAM,WAAW,KAAK,cAAc,IAAI,KAAK,GAAG,KAAK,KAAK;AAC1D,YAAM,QAAQA,QAAO;AAErB,UAAI,UAAU,GAAG;AACf,YACE,KAAK,+CAA+C,SAChD,KAAK,2CAA2C,MAAM,OAAO,IAAI,IACjE,KAAK,QAAQ,KAAK,gBAAgB,IAAI,KAAK,mBAC/C;AACA,cAA6C,KAAK,QAAQ,OAAO;AACvD,oBAAA,KAAK,cAAc,KAAK;UAAA;AAG7B,eAAA,gBAAgB,KAAK,gBAAA,GAAmB;YAC3C,aAAc,KAAK,qBAAqB;YACxC,UAAU;UAAA,CACX;QAAA;AAGE,aAAA,4BAA4B,KAAK,KAAK,KAAK;AAC3C,aAAA,gBAAgB,IAAI,IAAI,KAAK,cAAc,IAAI,KAAK,KAAKA,KAAI,CAAC;AAEnE,aAAK,OAAO,KAAK;MAAA;IAErB;AAEA,SAAA,iBAAiB,CAAC,SAA0C;AAC1D,UAAI,CAAC,MAAM;AACT,aAAK,cAAc,QAAQ,CAAC,QAAQ,QAAQ;AACtC,cAAA,CAAC,OAAO,aAAa;AAClB,iBAAA,SAAS,UAAU,MAAM;AACzB,iBAAA,cAAc,OAAO,GAAG;UAAA;QAC/B,CACD;AACD;MAAA;AAGG,WAAA,gBAAgB,MAAM,MAAS;IACtC;AAEkB,SAAA,kBAAA;MAChB,MAAM,CAAC,KAAK,kBAAqB,GAAA,KAAK,gBAAA,CAAiB;MACvD,CAAC,SAAS,iBAAiB;AACzB,cAAM,eAAmC,CAAC;AAE1C,iBAASC,KAAI,GAAG,MAAM,QAAQ,QAAQA,KAAI,KAAKA,MAAK;AAC5C,gBAAAF,MAAI,QAAQE,EAAC;AACb,gBAAA,cAAc,aAAaF,GAAC;AAElC,uBAAa,KAAK,WAAW;QAAA;AAGxB,eAAA;MACT;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEA,SAAA,0BAA0B,CAACP,YAAmB;AACtC,YAAA,eAAe,KAAK,gBAAgB;AACtC,UAAA,aAAa,WAAW,GAAG;AACtB,eAAA;MAAA;AAEF,aAAA;QACL,aACE;UACE;UACA,aAAa,SAAS;UACtB,CAAC,UAAkB,aAAa,aAAa,KAAK,CAAC,EAAE;UACrDA;QAEJ,CAAA;MACF;IACF;AAEA,SAAA,wBAAwB,CACtB,UACA,OACA,WAAW,MACR;AACG,YAAAQ,QAAO,KAAK,QAAQ;AACpB,YAAA,eAAe,KAAK,gBAAgB;AAE1C,UAAI,UAAU,QAAQ;AACZ,gBAAA,YAAY,eAAeA,QAAO,QAAQ;MAAA;AAGpD,UAAI,UAAU,UAAU;AAGtB,qBAAa,WAAWA,SAAQ;MAAA,WACvB,UAAU,OAAO;AACd,oBAAAA;MAAA;AAGd,YAAM,iBAAiB,KAAK,QAAQ,aAChC,gBACA;AACJ,YAAM,aAAa,KAAK,gBACpB,cAAc,KAAK,gBACjB,KAAK,cAAc,SAAS,gBAAgB,cAAc,IAC1D,KAAK,cAAc,cAAc,IACnC;AAEJ,YAAM,YAAY,aAAaA;AAE/B,aAAO,KAAK,IAAI,KAAK,IAAI,WAAW,QAAQ,GAAG,CAAC;IAClD;AAEoB,SAAA,oBAAA,CAAC,OAAe,QAAyB,WAAW;AAC9D,cAAA,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAErD,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACF,eAAA;MAAA;AAGH,YAAAA,QAAO,KAAK,QAAQ;AACpB,YAAA,eAAe,KAAK,gBAAgB;AAE1C,UAAI,UAAU,QAAQ;AACpB,YAAI,KAAK,OAAO,eAAeA,QAAO,KAAK,QAAQ,kBAAkB;AAC3D,kBAAA;QAAA,WACC,KAAK,SAAS,eAAe,KAAK,QAAQ,oBAAoB;AAC/D,kBAAA;QAAA,OACH;AACE,iBAAA,CAAC,cAAc,KAAK;QAAA;MAC7B;AAGI,YAAA,WACJ,UAAU,QACN,KAAK,MAAM,KAAK,QAAQ,mBACxB,KAAK,QAAQ,KAAK,QAAQ;AAEzB,aAAA;QACL,KAAK,sBAAsB,UAAU,OAAO,KAAK,IAAI;QACrD;MACF;IACF;AAEA,SAAQ,gBAAgB,MAAM,KAAK,cAAc,OAAO;AAExD,SAAQ,sBAAsB,MAAM;AAClC,UAAI,KAAK,2BAA2B,QAAQ,KAAK,cAAc;AACxD,aAAA,aAAa,aAAa,KAAK,sBAAsB;AAC1D,aAAK,yBAAyB;MAAA;IAElC;AAEiB,SAAA,iBAAA,CACf,UACA,EAAE,QAAQ,SAAS,SAAS,IAA2B,CAAA,MACpD;AACH,WAAK,oBAAoB;AAEzB,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGF,WAAK,gBAAgB,KAAK,sBAAsB,UAAU,KAAK,GAAG;QAChE,aAAa;QACb;MAAA,CACD;IACH;AAEgB,SAAA,gBAAA,CACd,OACA,EAAE,OAAO,eAAe,QAAQ,SAAmC,IAAA,CAAA,MAChE;AACK,cAAA,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAE3D,WAAK,oBAAoB;AAEzB,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGF,YAAM,iBAAiB,KAAK,kBAAkB,OAAO,YAAY;AACjE,UAAI,CAAC,eAAgB;AAEf,YAAA,CAACR,SAAQ,KAAK,IAAI;AAExB,WAAK,gBAAgBA,SAAQ,EAAE,aAAa,QAAW,SAAA,CAAU;AAEjE,UAAI,aAAa,YAAY,KAAK,cAAc,KAAK,KAAK,cAAc;AACtE,aAAK,yBAAyB,KAAK,aAAa,WAAW,MAAM;AAC/D,eAAK,yBAAyB;AAExB,gBAAA,eAAe,KAAK,cAAc;YACtC,KAAK,QAAQ,WAAW,KAAK;UAC/B;AAEA,cAAI,cAAc;AACV,kBAAA,CAAC,YAAY,IAAI;cACrB,KAAK,kBAAkB,OAAO,KAAK;YACrC;AAEA,gBAAI,CAAC,YAAY,cAAc,KAAK,gBAAiB,CAAA,GAAG;AACtD,mBAAK,cAAc,OAAO,EAAE,OAAO,SAAA,CAAU;YAAA;UAC/C,OACK;AACL,iBAAK,cAAc,OAAO,EAAE,OAAO,SAAA,CAAU;UAAA;QAC/C,CACD;MAAA;IAEL;AAEA,SAAA,WAAW,CAAC,OAAe,EAAE,SAAS,IAA2B,CAAA,MAAO;AACtE,WAAK,oBAAoB;AAEzB,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGF,WAAK,gBAAgB,KAAK,gBAAgB,IAAI,OAAO;QACnD,aAAa;QACb;MAAA,CACD;IACH;AAEA,SAAA,eAAe,MAAM;;AACb,YAAA,eAAe,KAAK,gBAAgB;AAEtC,UAAA;AAIA,UAAA,aAAa,WAAW,GAAG;AAC7B,cAAM,KAAK,QAAQ;MACV,WAAA,KAAK,QAAQ,UAAU,GAAG;AACnC,gBAAM,KAAA,aAAa,aAAa,SAAS,CAAC,MAApC,OAAA,SAAA,GAAuC,QAAO;MAAA,OAC/C;AACL,cAAM,YAAY,MAAqB,KAAK,QAAQ,KAAK,EAAE,KAAK,IAAI;AAChE,YAAA,WAAW,aAAa,SAAS;AAC9B,eAAA,WAAW,KAAK,UAAU,KAAK,CAAC,QAAQ,QAAQ,IAAI,GAAG;AACtD,gBAAA,OAAO,aAAa,QAAQ;AAClC,cAAI,UAAU,KAAK,IAAI,MAAM,MAAM;AACvB,sBAAA,KAAK,IAAI,IAAI,KAAK;UAAA;AAG9B;QAAA;AAGI,cAAA,KAAK,IAAI,GAAG,UAAU,OAAO,CAAC,QAAuB,QAAQ,IAAI,CAAC;MAAA;AAG1E,aAAO,KAAK;QACV,MAAM,KAAK,QAAQ,eAAe,KAAK,QAAQ;QAC/C;MACF;IACF;AAEQ,SAAA,kBAAkB,CACxBA,SACA;MACE;MACA;IAAA,MAKC;AACH,WAAK,QAAQ,WAAWA,SAAQ,EAAE,UAAU,YAAA,GAAe,IAAI;IACjE;AAEA,SAAA,UAAU,MAAM;AACT,WAAA,gBAAA,oBAAoB,IAAI;AAC7B,WAAK,OAAO,KAAK;IACnB;AAhqBE,SAAK,WAAW,IAAI;EAAA;AAiqBxB;AAEA,IAAM,0BAA0B,CAC9B,KACA,MACA,iBACA,UACG;AACH,SAAO,OAAO,MAAM;AACZ,UAAA,UAAW,MAAM,QAAQ,IAAK;AAC9B,UAAA,eAAe,gBAAgB,MAAM;AAE3C,QAAI,eAAe,OAAO;AACxB,YAAM,SAAS;IAAA,WACN,eAAe,OAAO;AAC/B,aAAO,SAAS;IAAA,OACX;AACE,aAAA;IAAA;EACT;AAGF,MAAI,MAAM,GAAG;AACX,WAAO,MAAM;EAAA,OACR;AACE,WAAA;EAAA;AAEX;AAEA,SAAS,eAAe;EACtB;EACA;EACA;EACA;AACF,GAKG;AACK,QAAA,YAAY,aAAa,SAAS;AACxC,QAAM,YAAY,CAAC,UAAkB,aAAa,KAAK,EAAG;AAE1D,MAAI,aAAa;IACf;IACA;IACA;IACA;EACF;AACA,MAAI,WAAW;AAEf,MAAI,UAAU,GAAG;AACf,WACE,WAAW,aACX,aAAa,QAAQ,EAAG,MAAM,eAAe,WAC7C;AACA;IAAA;EACF,WACS,QAAQ,GAAG;AAGpB,UAAM,aAAa,MAAM,KAAK,EAAE,KAAK,CAAC;AAEpC,WAAA,WAAW,aACX,WAAW,KAAK,CAAC,QAAQ,MAAM,eAAe,SAAS,GACvD;AACM,YAAA,OAAO,aAAa,QAAQ;AACvB,iBAAA,KAAK,IAAI,IAAI,KAAK;AAC7B;IAAA;AAKF,UAAM,eAAe,MAAM,KAAK,EAAE,KAAK,eAAe,SAAS;AACxD,WAAA,aAAa,KAAK,aAAa,KAAK,CAAC,QAAQ,OAAO,YAAY,GAAG;AAClE,YAAA,OAAO,aAAa,UAAU;AACvB,mBAAA,KAAK,IAAI,IAAI,KAAK;AAC/B;IAAA;AAIF,iBAAa,KAAK,IAAI,GAAG,aAAc,aAAa,KAAM;AAE1D,eAAW,KAAK,IAAI,WAAW,YAAY,QAAQ,IAAK,WAAW,MAAO;EAAA;AAGrE,SAAA,EAAE,YAAY,SAAS;AAChC;;;;ACznCA,SAAS,mBAIP,SACgD;AAChD,QAAM,cAAc,IAAI,YAAY,MAAM,OAAO,CAAC;AAC5C,QAAA,QAAQ,WAAW,WAAW;AAE9B,QAAA,UAAU,YAAY,UAAU;AAEtC;IACE,MAAM,MAAM,OAAO,EAAE,iBAAiB;IACtC,CAAC,OAAO;AACN,UAAI,IAAI;AACN,oBAAY,YAAY;MAAA;IAE5B;IACA;MACE,WAAW;IAAA;EAEf;AAEA;IACE,MAAM,MAAM,OAAO;IACnB,CAACU,aAAY;AACX,kBAAY,WAAW;QACrB,GAAGA;QACH,UAAU,CAAC,UAAU,SAAS;;AAC5B,qBAAW,KAAK;AAChBA,WAAAA,KAAAA,SAAQ,aAARA,OAAAA,SAAAA,GAAAA,KAAAA,UAAmB,UAAU,IAAA;QAAI;MACnC,CACD;AAED,kBAAY,YAAY;AACxB,iBAAW,KAAK;IAClB;IACA;MACE,WAAW;IAAA;EAEf;AAEA,iBAAe,OAAO;AAEf,SAAA;AACT;AAEO,SAAS,eAId,SAMgD;AACzC,SAAA;IACL,SAAS,OAAO;MACd;MACA;MACA,YAAY;MACZ,GAAG,MAAM,OAAO;IAAA,EAChB;EACJ;AACF;;;AC1FwD,SAAO,cAAc,IAAG,YAAY,GAAE,mBAAmBC,IAAE,YAAY,IAAG,KAAK,GAAE,UAAU,IAAG,YAAYC,IAAE,aAAa,GAAE,eAAe,IAAG,WAAW,IAAG,YAAY,IAAG,OAAOC,IAAE,SAAS,GAAE,SAAS,GAAE,eAAe,SAAM;;;ACAtR,SAAO,YAAY,GAAE,OAAO,SAAM;AAAM,SAAS,EAAEC,KAAEC,IAAEC,IAAE;AAAC,MAAIC,MAAE,EAAED,MAAG,OAAK,SAAOA,GAAE,KAAK,GAAEE,KAAE,EAAE,MAAIJ,IAAE,UAAQ,MAAM;AAAE,SAAM,CAAC,EAAE,MAAII,GAAE,QAAMJ,IAAE,QAAMG,IAAE,KAAK,GAAE,SAASE,IAAE;AAAC,WAAOD,GAAE,UAAQD,IAAE,QAAME,KAAGJ,MAAG,OAAK,SAAOA,GAAEI,EAAC;AAAA,EAAC,CAAC;AAAC;;;ACAvN,SAAO,eAAeC,UAAM;;;ACA5B,SAAS,EAAEC,IAAE;AAAC,SAAO,kBAAgB,aAAW,eAAeA,EAAC,IAAE,QAAQ,QAAQ,EAAE,KAAKA,EAAC,EAAE,MAAM,CAAAC,QAAG,WAAW,MAAI;AAAC,UAAMA;AAAA,EAAC,CAAC,CAAC;AAAC;;;ACAnF,SAAS,IAAG;AAAC,MAAIC,KAAE,CAAC,GAAEC,MAAE,EAAC,iBAAiBC,IAAEC,IAAEC,IAAEC,KAAE;AAAC,WAAOH,GAAE,iBAAiBC,IAAEC,IAAEC,GAAC,GAAEJ,IAAE,IAAI,MAAIC,GAAE,oBAAoBC,IAAEC,IAAEC,GAAC,CAAC;AAAA,EAAC,GAAE,yBAAyBH,IAAE;AAAC,QAAIC,KAAE,sBAAsB,GAAGD,EAAC;AAAE,IAAAD,IAAE,IAAI,MAAI,qBAAqBE,EAAC,CAAC;AAAA,EAAC,GAAE,aAAaD,IAAE;AAAC,IAAAD,IAAE,sBAAsB,MAAI;AAAC,MAAAA,IAAE,sBAAsB,GAAGC,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,cAAcA,IAAE;AAAC,QAAIC,KAAE,WAAW,GAAGD,EAAC;AAAE,IAAAD,IAAE,IAAI,MAAI,aAAaE,EAAC,CAAC;AAAA,EAAC,GAAE,aAAaD,IAAE;AAAC,QAAIC,KAAE,EAAC,SAAQ,KAAE;AAAE,WAAO,EAAE,MAAI;AAAC,MAAAA,GAAE,WAASD,GAAE,CAAC,EAAE;AAAA,IAAC,CAAC,GAAED,IAAE,IAAI,MAAI;AAAC,MAAAE,GAAE,UAAQ;AAAA,IAAE,CAAC;AAAA,EAAC,GAAE,MAAMD,IAAEC,IAAEC,IAAE;AAAC,QAAIC,MAAEH,GAAE,MAAM,iBAAiBC,EAAC;AAAE,WAAO,OAAO,OAAOD,GAAE,OAAM,EAAC,CAACC,EAAC,GAAEC,GAAC,CAAC,GAAE,KAAK,IAAI,MAAI;AAAC,aAAO,OAAOF,GAAE,OAAM,EAAC,CAACC,EAAC,GAAEE,IAAC,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,MAAMH,IAAE;AAAC,QAAIC,KAAE,EAAE;AAAE,WAAOD,GAAEC,EAAC,GAAE,KAAK,IAAI,MAAIA,GAAE,QAAQ,CAAC;AAAA,EAAC,GAAE,IAAID,IAAE;AAAC,WAAOF,GAAE,KAAKE,EAAC,GAAE,MAAI;AAAC,UAAIC,KAAEH,GAAE,QAAQE,EAAC;AAAE,UAAGC,MAAG,EAAE,UAAQC,MAAKJ,GAAE,OAAOG,IAAE,CAAC,EAAE,CAAAC,GAAE;AAAA,IAAC;AAAA,EAAC,GAAE,UAAS;AAAC,aAAQF,MAAKF,GAAE,OAAO,CAAC,EAAE,CAAAE,GAAE;AAAA,EAAC,EAAC;AAAE,SAAOD;AAAC;;;AFA9uB,SAAS,IAAG;AAAC,MAAIK,MAAE,EAAE;AAAE,SAAOC,GAAE,MAAID,IAAE,QAAQ,CAAC,GAAEA;AAAC;;;AGApF,SAASE,KAAG;AAAC,MAAIC,KAAE,EAAE;AAAE,SAAO,CAAAC,QAAG;AAAC,IAAAD,GAAE,QAAQ,GAAEA,GAAE,UAAUC,GAAC;AAAA,EAAC;AAAC;;;ACA7G,YAAU,OAAM;AAAtB,IAAI;AAAwB,IAAI,IAAE,OAAO,kBAAkB;AAA/B,IAAiCC,KAAE;AAAE,IAAMC,MAAG,IAAI,YAAQ,OAAK,IAAE,WAAU;AAAC,SAAS,SAAO,GAAE,MAAI,GAAG,EAAED,EAAC,EAAE,EAAE;AAAC;;;ACAzI,SAAO,YAAYE,IAAE,OAAOC,UAAM;;;ACAlC,SAASC,GAAEC,IAAE;AAAC,MAAIC;AAAE,MAAGD,MAAG,QAAMA,GAAE,SAAO,KAAK,QAAO;AAAK,MAAIE,MAAGD,KAAED,GAAE,MAAM,QAAM,OAAKC,KAAED,GAAE;AAAM,SAAOE,cAAa,OAAKA,KAAE;AAAI;;;ACA/H,SAAO,YAAY,SAAM;;;ACAzB,SAAS,EAAEC,IAAEC,OAAKC,IAAE;AAAC,MAAGF,MAAKC,IAAE;AAAC,QAAIE,KAAEF,GAAED,EAAC;AAAE,WAAO,OAAOG,MAAG,aAAWA,GAAE,GAAGD,EAAC,IAAEC;AAAA,EAAC;AAAC,MAAIC,KAAE,IAAI,MAAM,oBAAoBJ,EAAC,iEAAiE,OAAO,KAAKC,EAAC,EAAE,IAAI,CAAAE,OAAG,IAAIA,EAAC,GAAG,EAAE,KAAK,IAAI,CAAC,GAAG;AAAE,QAAM,MAAM,qBAAmB,MAAM,kBAAkBC,IAAE,CAAC,GAAEA;AAAC;;;ACAnS,IAAIC,KAAE,OAAO;AAAe,IAAIC,KAAE,CAACC,IAAEC,IAAEC,OAAID,MAAKD,KAAEF,GAAEE,IAAEC,IAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAMC,GAAC,CAAC,IAAEF,GAAEC,EAAC,IAAEC;AAAE,IAAIC,KAAE,CAACH,IAAEC,IAAEC,QAAKH,GAAEC,IAAE,OAAOC,MAAG,WAASA,KAAE,KAAGA,IAAEC,EAAC,GAAEA;AAAG,IAAME,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,IAAAD,GAAE,MAAK,WAAU,KAAK,OAAO,CAAC;AAAE,IAAAA,GAAE,MAAK,aAAY,CAAC;AAAA,EAAC;AAAA,EAAC,IAAIF,IAAE;AAAC,SAAK,YAAUA,OAAI,KAAK,YAAU,GAAE,KAAK,UAAQA;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,SAAK,IAAI,KAAK,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAM,EAAE,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,YAAU;AAAA,EAAQ;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,YAAU;AAAA,EAAQ;AAAA,EAAC,SAAQ;AAAC,WAAO,OAAO,UAAQ,eAAa,OAAO,YAAU,cAAY,WAAS;AAAA,EAAQ;AAAC;AAAC,IAAI,IAAE,IAAIG;;;ACAnf,SAASC,GAAEC,IAAE;AAAC,MAAG,EAAE,SAAS,QAAO;AAAK,MAAGA,cAAa,KAAK,QAAOA,GAAE;AAAc,MAAGA,MAAG,QAAMA,GAAE,eAAe,OAAO,GAAE;AAAC,QAAIC,KAAEC,GAAEF,EAAC;AAAE,QAAGC,GAAE,QAAOA,GAAE;AAAA,EAAa;AAAC,SAAO;AAAQ;;;AHA/H,IAAIE,KAAE,CAAC,0BAAyB,cAAa,WAAU,cAAa,0BAAyB,UAAS,yBAAwB,0BAAyB,0BAA0B,EAAE,IAAI,CAAAC,OAAG,GAAGA,EAAC,uBAAuB,EAAE,KAAK,GAAG;AAAE,IAAI,KAAG,CAAAC,QAAIA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,aAAW,EAAE,IAAE,cAAaA,GAAEA,GAAE,WAAS,EAAE,IAAE,YAAWA,KAAI,KAAG,CAAC,CAAC;AAAtK,IAAwK,KAAG,CAAAC,SAAIA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,UAAQ,CAAC,IAAE,WAAUA,IAAEA,IAAE,YAAU,CAAC,IAAE,aAAYA,MAAI,KAAG,CAAC,CAAC;AAA9R,IAAgS,KAAG,CAAAC,QAAIA,GAAEA,GAAE,WAAS,EAAE,IAAE,YAAWA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,KAAI,KAAG,CAAC,CAAC;AAAE,SAAS,EAAEH,KAAE,SAAS,MAAK;AAAC,SAAOA,MAAG,OAAK,CAAC,IAAE,MAAM,KAAKA,GAAE,iBAAiBD,EAAC,CAAC,EAAE,KAAK,CAACK,IAAED,OAAI,KAAK,MAAMC,GAAE,YAAU,OAAO,qBAAmBD,GAAE,YAAU,OAAO,iBAAiB,CAAC;AAAC;AAAC,IAAI,KAAG,CAAAA,QAAIA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,KAAG,CAAC,CAAC;AAAE,SAAS,EAAEH,IAAEI,KAAE,GAAE;AAAC,MAAID;AAAE,SAAOH,SAAMG,KAAEE,GAAEL,EAAC,MAAI,OAAK,SAAOG,GAAE,QAAM,QAAG,EAAEC,IAAE,EAAC,CAAC,CAAC,IAAG;AAAC,WAAOJ,GAAE,QAAQD,EAAC;AAAA,EAAC,GAAE,CAAC,CAAC,IAAG;AAAC,QAAIO,KAAEN;AAAE,WAAKM,OAAI,QAAM;AAAC,UAAGA,GAAE,QAAQP,EAAC,EAAE,QAAM;AAAG,MAAAO,KAAEA,GAAE;AAAA,IAAa;AAAC,WAAM;AAAA,EAAE,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEN,IAAE;AAAC,MAAII,KAAEC,GAAEL,EAAC;AAAE,IAAE,MAAI;AAAC,IAAAI,MAAG,CAAC,EAAEA,GAAE,eAAc,CAAC,KAAG,EAAEJ,EAAC;AAAA,EAAC,CAAC;AAAC;AAAC,IAAI,KAAG,CAAAG,QAAIA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,KAAG,CAAC,CAAC;AAAE,OAAO,UAAQ,eAAa,OAAO,YAAU,gBAAc,SAAS,iBAAiB,WAAU,CAAAH,OAAG;AAAC,EAAAA,GAAE,WAASA,GAAE,UAAQA,GAAE,YAAU,SAAS,gBAAgB,QAAQ,yBAAuB;AAAG,GAAE,IAAE,GAAE,SAAS,iBAAiB,SAAQ,CAAAA,OAAG;AAAC,EAAAA,GAAE,WAAS,IAAE,OAAO,SAAS,gBAAgB,QAAQ,yBAAuBA,GAAE,WAAS,MAAI,SAAS,gBAAgB,QAAQ,yBAAuB;AAAG,GAAE,IAAE;AAAG,SAAS,EAAEA,IAAE;AAAC,EAAAA,MAAG,QAAMA,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAC;AAAC,IAAI,IAAE,CAAC,YAAW,OAAO,EAAE,KAAK,GAAG;AAAE,SAAS,EAAEA,IAAE;AAAC,MAAII,IAAED;AAAE,UAAOA,MAAGC,KAAEJ,MAAG,OAAK,SAAOA,GAAE,YAAU,OAAK,SAAOI,GAAE,KAAKJ,IAAE,CAAC,MAAI,OAAKG,KAAE;AAAE;AAAC,SAAS,EAAEH,IAAEI,KAAE,CAAAD,OAAGA,IAAE;AAAC,SAAOH,GAAE,MAAM,EAAE,KAAK,CAACG,IAAEG,OAAI;AAAC,QAAIJ,MAAEE,GAAED,EAAC,GAAEE,MAAED,GAAEE,EAAC;AAAE,QAAGJ,QAAI,QAAMG,QAAI,KAAK,QAAO;AAAE,QAAIJ,KAAEC,IAAE,wBAAwBG,GAAC;AAAE,WAAOJ,KAAE,KAAK,8BAA4B,KAAGA,KAAE,KAAK,8BAA4B,IAAE;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,EAAED,IAAEI,IAAE;AAAC,SAAO,EAAE,EAAE,GAAEA,IAAE,EAAC,YAAWJ,GAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAEI,IAAE,EAAC,QAAOD,KAAE,MAAG,YAAWG,KAAE,MAAK,cAAaJ,MAAE,CAAC,EAAC,IAAE,CAAC,GAAE;AAAC,MAAIK;AAAE,MAAIF,OAAGE,MAAE,MAAM,QAAQP,EAAC,IAAEA,GAAE,SAAO,IAAEA,GAAE,CAAC,EAAE,gBAAc,WAASA,MAAG,OAAK,SAAOA,GAAE,kBAAgB,OAAKO,MAAE,UAASN,KAAE,MAAM,QAAQD,EAAC,IAAEG,KAAE,EAAEH,EAAC,IAAEA,KAAE,EAAEA,EAAC;AAAE,EAAAE,IAAE,SAAO,KAAGD,GAAE,SAAO,MAAIA,KAAEA,GAAE,OAAO,CAAAO,QAAG,CAACN,IAAE,SAASM,GAAC,CAAC,IAAGF,KAAEA,MAAG,OAAKA,KAAED,IAAE;AAAc,MAAII,MAAG,MAAI;AAAC,QAAGL,KAAE,EAAE,QAAO;AAAE,QAAGA,KAAE,GAAG,QAAM;AAAG,UAAM,IAAI,MAAM,+DAA+D;AAAA,EAAC,GAAG,GAAEM,OAAG,MAAI;AAAC,QAAGN,KAAE,EAAE,QAAO;AAAE,QAAGA,KAAE,EAAE,QAAO,KAAK,IAAI,GAAEH,GAAE,QAAQK,EAAC,CAAC,IAAE;AAAE,QAAGF,KAAE,EAAE,QAAO,KAAK,IAAI,GAAEH,GAAE,QAAQK,EAAC,CAAC,IAAE;AAAE,QAAGF,KAAE,EAAE,QAAOH,GAAE,SAAO;AAAE,UAAM,IAAI,MAAM,+DAA+D;AAAA,EAAC,GAAG,GAAEU,KAAEP,KAAE,KAAG,EAAC,eAAc,KAAE,IAAE,CAAC,GAAEQ,KAAE,GAAEC,MAAEZ,GAAE,QAAOa;AAAE,KAAE;AAAC,QAAGF,MAAGC,OAAGD,KAAEC,OAAG,EAAE,QAAO;AAAE,QAAIL,MAAEE,MAAEE;AAAE,QAAGR,KAAE,GAAG,CAAAI,OAAGA,MAAEK,OAAGA;AAAA,SAAM;AAAC,UAAGL,MAAE,EAAE,QAAO;AAAE,UAAGA,OAAGK,IAAE,QAAO;AAAA,IAAC;AAAC,IAAAC,MAAEb,GAAEO,GAAC,GAAEM,OAAG,QAAMA,IAAE,MAAMH,EAAC,GAAEC,MAAGH;AAAA,EAAC,SAAOK,QAAIT,IAAE;AAAe,SAAOD,KAAE,KAAG,EAAEU,GAAC,KAAGA,IAAE,OAAO,GAAE;AAAC;;;AIAv6F,SAASC,KAAG;AAAC,SAAM,WAAW,KAAK,OAAO,UAAU,QAAQ,KAAG,QAAQ,KAAK,OAAO,UAAU,QAAQ,KAAG,OAAO,UAAU,iBAAe;AAAC;AAAC,SAASC,KAAG;AAAC,SAAM,YAAY,KAAK,OAAO,UAAU,SAAS;AAAC;AAAC,SAASC,KAAG;AAAC,SAAOF,GAAE,KAAGC,GAAE;AAAC;;;ACAtO,SAAO,eAAeE,UAAM;AAA4C,SAASC,GAAEC,IAAEC,IAAEC,IAAE;AAAC,IAAE,YAAUC,GAAE,CAAAC,QAAG;AAAC,aAAS,iBAAiBJ,IAAEC,IAAEC,EAAC,GAAEE,IAAE,MAAI,SAAS,oBAAoBJ,IAAEC,IAAEC,EAAC,CAAC;AAAA,EAAC,CAAC;AAAC;;;ACAzL,SAAO,eAAeG,UAAM;AAA4C,SAASC,GAAEC,IAAEC,IAAEC,IAAE;AAAC,IAAE,YAAUC,GAAE,CAAAC,QAAG;AAAC,WAAO,iBAAiBJ,IAAEC,IAAEC,EAAC,GAAEE,IAAE,MAAI,OAAO,oBAAoBJ,IAAEC,IAAEC,EAAC,CAAC;AAAA,EAAC,CAAC;AAAC;;;ARAgJ,SAASG,GAAEC,IAAEC,KAAEC,KAAEC,GAAE,MAAI,IAAE,GAAE;AAAC,WAASC,GAAEC,IAAEC,IAAE;AAAC,QAAG,CAACJ,GAAE,SAAOG,GAAE,iBAAiB;AAAO,QAAIE,KAAED,GAAED,EAAC;AAAE,QAAGE,OAAI,QAAM,CAACA,GAAE,YAAY,EAAE,SAASA,EAAC,EAAE;AAAO,QAAIC,KAAE,SAASC,IAAEC,IAAE;AAAC,aAAO,OAAOA,MAAG,aAAWD,IAAEC,GAAE,CAAC,IAAE,MAAM,QAAQA,EAAC,KAAGA,cAAa,MAAIA,KAAE,CAACA,EAAC;AAAA,IAAC,EAAEV,EAAC;AAAE,aAAQS,OAAKD,IAAE;AAAC,UAAGC,QAAI,KAAK;AAAS,UAAIC,KAAED,eAAa,cAAYA,MAAEA,GAAEA,GAAC;AAAE,UAAGC,MAAG,QAAMA,GAAE,SAASH,EAAC,KAAGF,GAAE,YAAUA,GAAE,aAAa,EAAE,SAASK,EAAC,EAAE;AAAA,IAAM;AAAC,WAAM,CAAC,EAAEH,IAAE,EAAE,KAAK,KAAGA,GAAE,aAAW,MAAIF,GAAE,eAAe,GAAEJ,IAAEI,IAAEE,EAAC;AAAA,EAAC;AAAC,MAAII,MAAEC,GAAE,IAAI;AAAE,EAAAD,GAAE,eAAc,CAAAN,OAAG;AAAC,QAAIC,IAAEC;AAAE,IAAAL,GAAE,UAAQS,IAAE,UAAQJ,MAAGD,KAAED,GAAE,iBAAe,OAAK,SAAOC,GAAE,KAAKD,EAAC,MAAI,OAAK,SAAOE,GAAE,CAAC,MAAIF,GAAE;AAAA,EAAO,GAAE,IAAE,GAAEM,GAAE,aAAY,CAAAN,OAAG;AAAC,QAAIC,IAAEC;AAAE,IAAAL,GAAE,UAAQS,IAAE,UAAQJ,MAAGD,KAAED,GAAE,iBAAe,OAAK,SAAOC,GAAE,KAAKD,EAAC,MAAI,OAAK,SAAOE,GAAE,CAAC,MAAIF,GAAE;AAAA,EAAO,GAAE,IAAE,GAAEM,GAAE,SAAQ,CAAAN,OAAG;AAAC,IAAAK,GAAE,KAAGC,IAAE,UAAQP,GAAEC,IAAE,MAAIM,IAAE,KAAK,GAAEA,IAAE,QAAM;AAAA,EAAK,GAAE,IAAE,GAAEA,GAAE,YAAW,CAAAN,OAAGD,GAAEC,IAAE,MAAIA,GAAE,kBAAkB,cAAYA,GAAE,SAAO,IAAI,GAAE,IAAE,GAAEN,GAAE,QAAO,CAAAM,OAAGD,GAAEC,IAAE,MAAI,OAAO,SAAS,yBAAyB,oBAAkB,OAAO,SAAS,gBAAc,IAAI,GAAE,IAAE;AAAC;;;ASA5xC,SAAO,aAAaQ,IAAE,OAAO,GAAE,eAAe,SAAM;AAA4C,SAASC,GAAEC,IAAEC,IAAE;AAAC,MAAGD,GAAE,QAAOA;AAAE,MAAIE,KAAED,MAAG,OAAKA,KAAE;AAAS,MAAG,OAAOC,MAAG,YAAUA,GAAE,YAAY,MAAI,SAAS,QAAM;AAAQ;AAAC,SAASC,GAAEH,IAAEC,IAAE;AAAC,MAAIC,KAAE,EAAEH,GAAEC,GAAE,MAAM,MAAKA,GAAE,MAAM,EAAE,CAAC;AAAE,SAAOI,GAAE,MAAI;AAAC,IAAAF,GAAE,QAAMH,GAAEC,GAAE,MAAM,MAAKA,GAAE,MAAM,EAAE;AAAA,EAAC,CAAC,GAAE,EAAE,MAAI;AAAC,QAAIK;AAAE,IAAAH,GAAE,SAAOI,GAAEL,EAAC,KAAGK,GAAEL,EAAC,aAAY,qBAAmB,GAAGI,MAAEC,GAAEL,EAAC,MAAI,QAAMI,IAAE,aAAa,MAAM,OAAKH,GAAE,QAAM;AAAA,EAAS,CAAC,GAAEA;AAAC;;;ACA9b,SAAO,OAAOK,UAAM;AAAM,SAASC,GAAEC,IAAE;AAAC,SAAM,CAACA,GAAE,SAAQA,GAAE,OAAO;AAAC;AAAC,SAASC,KAAG;AAAC,MAAID,KAAEF,GAAE,CAAC,IAAG,EAAE,CAAC;AAAE,SAAM,EAAC,SAASI,IAAE;AAAC,QAAIC,KAAEJ,GAAEG,EAAC;AAAE,WAAOF,GAAE,MAAM,CAAC,MAAIG,GAAE,CAAC,KAAGH,GAAE,MAAM,CAAC,MAAIG,GAAE,CAAC,IAAE,SAAIH,GAAE,QAAMG,IAAE;AAAA,EAAG,GAAE,OAAOD,IAAE;AAAC,IAAAF,GAAE,QAAMD,GAAEG,EAAC;AAAA,EAAC,EAAC;AAAC;;;ACAvN,SAAO,eAAeE,UAAM;AAA2D,SAASC,GAAE,EAAC,WAAUC,IAAE,QAAOC,IAAE,MAAKC,KAAE,SAAQC,IAAC,GAAE;AAAC,EAAAC,GAAE,MAAI;AAAC,QAAIC,KAAEL,GAAE;AAAM,QAAG,CAACK,MAAGF,QAAI,UAAQ,CAACA,IAAE,MAAM;AAAO,QAAIG,KAAEP,GAAEC,EAAC;AAAE,QAAG,CAACM,GAAE;AAAO,QAAIC,KAAE,OAAO,OAAO,CAAAC,OAAGP,GAAEO,EAAC,GAAE,EAAC,YAAWP,GAAC,CAAC,GAAEQ,KAAEH,GAAE,iBAAiBD,IAAE,WAAW,cAAaE,IAAE,KAAE;AAAE,WAAKE,GAAE,SAAS,IAAG,CAAAP,IAAEO,GAAE,WAAW;AAAA,EAAC,CAAC;AAAC;;;ACAjW,SAAO,mBAAmB,SAAM;;;ACAhC,SAAO,cAAcC,IAAE,YAAY,GAAE,KAAK,SAAM;AAAyC,IAAIC,MAAG,CAAAC,SAAIA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,iBAAe,CAAC,IAAE,kBAAiBA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,MAAID,MAAG,CAAC,CAAC;AAArG,IAAuGE,MAAG,CAAAC,QAAIA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,MAAG,CAAC,CAAC;AAAE,SAAS,EAAE,EAAC,SAAQE,KAAE,MAAG,UAASC,KAAE,GAAE,UAASF,IAAE,YAAWF,KAAE,GAAGK,IAAC,GAAE;AAAC,MAAIC;AAAE,MAAIC,KAAE,EAAEP,KAAEE,EAAC,GAAEM,KAAE,OAAO,OAAOH,KAAE,EAAC,OAAME,GAAC,CAAC;AAAE,MAAGJ,MAAGC,KAAE,KAAGG,GAAE,OAAO,QAAOE,GAAED,EAAC;AAAE,MAAGJ,KAAE,GAAE;AAAC,QAAIM,OAAGJ,KAAEC,GAAE,YAAU,QAAMD,KAAE,IAAE;AAAE,WAAO,EAAEI,KAAE,EAAC,CAAC,CAAC,IAAG;AAAC,aAAO;AAAA,IAAI,GAAE,CAAC,CAAC,IAAG;AAAC,aAAOD,GAAE,EAAC,GAAGJ,KAAE,OAAM,EAAC,GAAGE,IAAE,QAAO,MAAG,OAAM,EAAC,SAAQ,OAAM,EAAC,EAAC,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,SAAOE,GAAED,EAAC;AAAC;AAAC,SAASC,GAAE,EAAC,OAAMN,IAAE,OAAMC,IAAE,OAAMF,IAAE,MAAKF,KAAE,MAAKK,IAAC,GAAE;AAAC,MAAIM,KAAEC;AAAE,MAAG,EAAC,IAAGL,IAAE,GAAGC,GAAC,IAAEK,GAAEV,IAAE,CAAC,WAAU,QAAQ,CAAC,GAAEG,MAAGK,MAAET,GAAE,YAAU,OAAK,SAAOS,IAAE,KAAKT,IAAEF,GAAC,GAAEU,MAAE,CAAC;AAAE,MAAGV,KAAE;AAAC,QAAIc,MAAE,OAAGC,KAAE,CAAC;AAAE,aAAO,CAACC,KAAEC,EAAC,KAAI,OAAO,QAAQjB,GAAC,EAAE,QAAOiB,MAAG,cAAYH,MAAE,OAAIG,OAAI,QAAIF,GAAE,KAAKC,GAAC;AAAE,IAAAF,QAAIJ,IAAE,uBAAuB,IAAEK,GAAE,KAAK,GAAG;AAAA,EAAE;AAAC,MAAGR,OAAI,YAAW;AAAC,QAAGD,KAAEY,GAAEZ,MAAG,OAAKA,KAAE,CAAC,CAAC,GAAE,OAAO,KAAKE,EAAC,EAAE,SAAO,KAAG,OAAO,KAAKJ,EAAC,EAAE,SAAO,GAAE;AAAC,UAAG,CAACU,KAAE,GAAGC,EAAC,IAAET,MAAG,OAAKA,KAAE,CAAC;AAAE,UAAG,CAACa,GAAEL,GAAC,KAAGC,GAAE,SAAO,EAAE,OAAM,IAAI,MAAM,CAAC,gCAA+B,IAAG,0BAA0BV,GAAC,kCAAiC,uDAAsD,OAAO,KAAKG,EAAC,EAAE,OAAO,OAAO,KAAKJ,EAAC,CAAC,EAAE,IAAI,CAAAgB,QAAGA,IAAE,KAAK,CAAC,EAAE,OAAO,CAACA,KAAEC,IAAEC,QAAIA,IAAE,QAAQF,GAAC,MAAIC,EAAC,EAAE,KAAK,CAACD,KAAEC,OAAID,IAAE,cAAcC,EAAC,CAAC,EAAE,IAAI,CAAAD,QAAG,OAAOA,GAAC,EAAE,EAAE,KAAK;AAAA,CAClxC,GAAE,IAAG,kCAAiC,CAAC,+FAA8F,0FAA0F,EAAE,IAAI,CAAAA,QAAG,OAAOA,GAAC,EAAE,EAAE,KAAK;AAAA,CACzP,CAAC,EAAE,KAAK;AAAA,CACR,CAAC;AAAE,UAAIJ,MAAE,GAAGJ,KAAEE,IAAE,UAAQ,OAAKF,KAAE,CAAC,GAAEJ,IAAEE,GAAC,GAAEO,KAAEM,GAAET,KAAEE,KAAE,IAAE;AAAE,eAAQI,OAAKJ,IAAE,CAAAI,IAAE,WAAW,IAAI,MAAIH,GAAE,UAAQA,GAAE,QAAM,CAAC,IAAGA,GAAE,MAAMG,GAAC,IAAEJ,IAAEI,GAAC;AAAG,aAAOH;AAAA,IAAC;AAAC,WAAO,MAAM,QAAQX,EAAC,KAAGA,GAAE,WAAS,IAAEA,GAAE,CAAC,IAAEA;AAAA,EAAC;AAAC,SAAO,EAAEC,IAAE,OAAO,OAAO,CAAC,GAAEC,IAAEE,GAAC,GAAE,EAAC,SAAQ,MAAIJ,GAAC,CAAC;AAAC;AAAC,SAASY,GAAEf,IAAE;AAAC,SAAOA,GAAE,QAAQ,CAAAC,OAAGA,GAAE,SAAO,IAAEc,GAAEd,GAAE,QAAQ,IAAE,CAACA,EAAC,CAAC;AAAC;AAAC,SAAS,KAAKD,IAAE;AAAC,MAAIH;AAAE,MAAGG,GAAE,WAAS,EAAE,QAAM,CAAC;AAAE,MAAGA,GAAE,WAAS,EAAE,QAAOA,GAAE,CAAC;AAAE,MAAIC,KAAE,CAAC,GAAEF,KAAE,CAAC;AAAE,WAAQG,OAAKF,GAAE,UAAQI,MAAKF,IAAE,CAAAE,GAAE,WAAW,IAAI,KAAG,OAAOF,IAAEE,EAAC,KAAG,eAAaP,MAAEE,GAAEK,EAAC,MAAI,SAAOL,GAAEK,EAAC,IAAE,CAAC,IAAGL,GAAEK,EAAC,EAAE,KAAKF,IAAEE,EAAC,CAAC,KAAGH,GAAEG,EAAC,IAAEF,IAAEE,EAAC;AAAE,MAAGH,GAAE,YAAUA,GAAE,eAAe,EAAE,QAAO,OAAO,OAAOA,IAAE,OAAO,YAAY,OAAO,KAAKF,EAAC,EAAE,IAAI,CAAAG,QAAG,CAACA,KAAE,MAAM,CAAC,CAAC,CAAC;AAAE,WAAQA,OAAKH,GAAE,QAAO,OAAOE,IAAE,EAAC,CAACC,GAAC,EAAEE,OAAKC,IAAE;AAAC,QAAIF,KAAEJ,GAAEG,GAAC;AAAE,aAAQK,OAAKJ,IAAE;AAAC,UAAGC,cAAa,SAAOA,GAAE,iBAAiB;AAAO,MAAAG,IAAEH,IAAE,GAAGC,EAAC;AAAA,IAAC;AAAA,EAAC,EAAC,CAAC;AAAE,SAAOJ;AAAC;AAAC,SAASoB,GAAErB,IAAE;AAAC,MAAIC,KAAE,OAAO,OAAO,CAAC,GAAED,EAAC;AAAE,WAAQD,MAAKE,GAAE,CAAAA,GAAEF,EAAC,MAAI,UAAQ,OAAOE,GAAEF,EAAC;AAAE,SAAOE;AAAC;AAAC,SAASS,GAAEV,IAAEC,KAAE,CAAC,GAAE;AAAC,MAAIF,KAAE,OAAO,OAAO,CAAC,GAAEC,EAAC;AAAE,WAAQH,OAAKI,GAAE,CAAAJ,OAAKE,MAAG,OAAOA,GAAEF,GAAC;AAAE,SAAOE;AAAC;AAAC,SAASiB,GAAEhB,IAAE;AAAC,SAAOA,MAAG,OAAK,QAAG,OAAOA,GAAE,QAAM,YAAU,OAAOA,GAAE,QAAM,YAAU,OAAOA,GAAE,QAAM;AAAU;;;ADH78B,IAAIsB,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,MAAG,CAAC,CAAC;AAAE,IAAIE,KAAE,EAAE,EAAC,MAAK,UAAS,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,EAAC,EAAC,GAAE,MAAMC,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAC,GAAE;AAAC,SAAM,MAAI;AAAC,QAAIC;AAAE,QAAG,EAAC,UAASL,IAAE,GAAGM,IAAC,IAAEJ,IAAEK,MAAE,EAAC,gBAAeP,KAAE,OAAK,IAAE,QAAIK,KAAEC,IAAE,aAAa,MAAI,OAAKD,KAAE,QAAO,SAAQL,KAAE,OAAK,IAAE,OAAG,QAAO,OAAM,EAAC,UAAS,SAAQ,KAAI,GAAE,MAAK,GAAE,OAAM,GAAE,QAAO,GAAE,SAAQ,GAAE,QAAO,IAAG,UAAS,UAAS,MAAK,oBAAmB,YAAW,UAAS,aAAY,KAAI,IAAIA,KAAE,OAAK,MAAIA,KAAE,OAAK,KAAG,EAAC,SAAQ,OAAM,EAAC,EAAC;AAAE,WAAO,EAAE,EAAC,UAASO,KAAE,YAAWD,KAAE,MAAK,CAAC,GAAE,OAAMF,KAAE,OAAMD,IAAE,MAAK,SAAQ,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;AEAvsB,SAAO,UAAUK,IAAE,WAAWC,UAAM;AAAM,IAAIC,KAAE,OAAO,SAAS;AAAE,IAAIC,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,KAAID,MAAG,CAAC,CAAC;AAAE,SAASE,KAAG;AAAC,SAAOC,GAAE,MAAI;AAAI;AAAC,SAASA,KAAG;AAAC,SAAON,GAAEE,IAAE,IAAI;AAAC;AAAC,SAASK,GAAEC,KAAE;AAAC,EAAAP,GAAEC,IAAEM,GAAC;AAAC;;;ACArQ,IAAIC,MAAG,CAAAC,QAAIA,GAAE,QAAM,KAAIA,GAAE,QAAM,SAAQA,GAAE,SAAO,UAASA,GAAE,YAAU,aAAYA,GAAE,SAAO,UAASA,GAAE,YAAU,aAAYA,GAAE,UAAQ,WAAUA,GAAE,aAAW,cAAaA,GAAE,YAAU,aAAYA,GAAE,OAAK,QAAOA,GAAE,MAAI,OAAMA,GAAE,SAAO,UAASA,GAAE,WAAS,YAAWA,GAAE,MAAI,OAAMA,KAAID,MAAG,CAAC,CAAC;;;ACAxR,IAAI,KAAG,CAAAE,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,KAAG,CAAC,CAAC;;;ACA5D,SAASC,GAAEC,IAAE;AAAC,WAASC,KAAG;AAAC,aAAS,eAAa,cAAYD,GAAE,GAAE,SAAS,oBAAoB,oBAAmBC,EAAC;AAAA,EAAE;AAAC,SAAO,UAAQ,eAAa,OAAO,YAAU,gBAAc,SAAS,iBAAiB,oBAAmBA,EAAC,GAAEA,GAAE;AAAE;;;ACA9K,IAAIC,KAAE,CAAC;AAAEA,GAAE,MAAI;AAAC,WAASC,GAAEC,IAAE;AAAC,IAAAA,GAAE,kBAAkB,eAAaA,GAAE,WAAS,SAAS,QAAMF,GAAE,CAAC,MAAIE,GAAE,WAASF,GAAE,QAAQE,GAAE,MAAM,GAAEF,KAAEA,GAAE,OAAO,CAAAG,OAAGA,MAAG,QAAMA,GAAE,WAAW,GAAEH,GAAE,OAAO,EAAE;AAAA,EAAE;AAAC,SAAO,iBAAiB,SAAQC,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,OAAO,iBAAiB,aAAYA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,OAAO,iBAAiB,SAAQA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,SAAS,KAAK,iBAAiB,SAAQA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,SAAS,KAAK,iBAAiB,aAAYA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,SAAS,KAAK,iBAAiB,SAAQA,IAAE,EAAC,SAAQ,KAAE,CAAC;AAAC,CAAC;;;ACAtiB,SAASG,GAAEC,IAAE;AAAC,QAAM,IAAI,MAAM,wBAAsBA,EAAC;AAAC;AAAC,IAAIC,MAAG,CAAAC,SAAIA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,UAAQ,CAAC,IAAE,WAAUA,MAAID,MAAG,CAAC,CAAC;AAAE,SAASE,GAAEH,IAAEI,IAAE;AAAC,MAAIC,KAAED,GAAE,aAAa;AAAE,MAAGC,GAAE,UAAQ,EAAE,QAAO;AAAK,MAAIC,KAAEF,GAAE,mBAAmB,GAAEG,MAAED,MAAG,OAAKA,KAAE;AAAG,UAAON,GAAE,OAAM;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQQ,KAAE,GAAEA,KAAEH,GAAE,QAAO,EAAEG,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,GAAEG,EAAC,GAAEA,IAAEH,EAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,MAAAC,QAAI,OAAKA,MAAEF,GAAE;AAAQ,eAAQG,KAAED,MAAE,GAAEC,MAAG,GAAE,EAAEA,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,GAAEG,EAAC,GAAEA,IAAEH,EAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQE,KAAED,MAAE,GAAEC,KAAEH,GAAE,QAAO,EAAEG,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,GAAEG,EAAC,GAAEA,IAAEH,EAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQE,KAAEH,GAAE,SAAO,GAAEG,MAAG,GAAE,EAAEA,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,GAAEG,EAAC,GAAEA,IAAEH,EAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQE,KAAE,GAAEA,KAAEH,GAAE,QAAO,EAAEG,GAAE,KAAGJ,GAAE,UAAUC,GAAEG,EAAC,GAAEA,IAAEH,EAAC,MAAIL,GAAE,GAAG,QAAOQ;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK;AAAE,aAAO;AAAA,IAAK;AAAQ,MAAAP,GAAEC,EAAC;AAAA,EAAC;AAAC;;;ACApzB,SAASS,GAAEC,MAAE,CAAC,GAAEC,MAAE,MAAKC,KAAE,CAAC,GAAE;AAAC,WAAO,CAACC,IAAEC,EAAC,KAAI,OAAO,QAAQJ,GAAC,EAAE,CAAAK,GAAEH,IAAEI,GAAEL,KAAEE,EAAC,GAAEC,EAAC;AAAE,SAAOF;AAAC;AAAC,SAASI,GAAEN,KAAEC,KAAE;AAAC,SAAOD,MAAEA,MAAE,MAAIC,MAAE,MAAIA;AAAC;AAAC,SAASI,GAAEL,KAAEC,KAAEC,IAAE;AAAC,MAAG,MAAM,QAAQA,EAAC,EAAE,UAAO,CAACC,IAAEC,EAAC,KAAIF,GAAE,QAAQ,EAAE,CAAAG,GAAEL,KAAEM,GAAEL,KAAEE,GAAE,SAAS,CAAC,GAAEC,EAAC;AAAA,MAAO,CAAAF,cAAa,OAAKF,IAAE,KAAK,CAACC,KAAEC,GAAE,YAAY,CAAC,CAAC,IAAE,OAAOA,MAAG,YAAUF,IAAE,KAAK,CAACC,KAAEC,KAAE,MAAI,GAAG,CAAC,IAAE,OAAOA,MAAG,WAASF,IAAE,KAAK,CAACC,KAAEC,EAAC,CAAC,IAAE,OAAOA,MAAG,WAASF,IAAE,KAAK,CAACC,KAAE,GAAGC,EAAC,EAAE,CAAC,IAAEA,MAAG,OAAKF,IAAE,KAAK,CAACC,KAAE,EAAE,CAAC,IAAEF,GAAEG,IAAED,KAAED,GAAC;AAAC;AAAC,SAASO,GAAEP,KAAE;AAAC,MAAIE,IAAEC;AAAE,MAAIF,OAAGC,KAAEF,OAAG,OAAK,SAAOA,IAAE,SAAO,OAAKE,KAAEF,IAAE,QAAQ,MAAM;AAAE,MAAGC,KAAE;AAAC,aAAQG,MAAKH,IAAE,SAAS,KAAGG,OAAIJ,QAAII,GAAE,YAAU,WAASA,GAAE,SAAO,YAAUA,GAAE,YAAU,YAAUA,GAAE,SAAO,YAAUA,GAAE,aAAW,WAASA,GAAE,SAAO,UAAS;AAAC,MAAAA,GAAE,MAAM;AAAE;AAAA,IAAM;AAAC,KAACD,KAAEF,IAAE,kBAAgB,QAAME,GAAE,KAAKF,GAAC;AAAA,EAAC;AAAC;;;A3BAk5B,SAAS,GAAGO,IAAEC,IAAE;AAAC,SAAOD,OAAIC;AAAC;AAAC,IAAI,MAAI,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAI,MAAI,CAAC,CAAC;AAAhE,IAAkE,MAAI,CAAAA,QAAIA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,MAAI,CAAC,CAAC;AAAhI,IAAkI,MAAI,CAAAC,SAAIA,IAAEA,IAAE,UAAQ,CAAC,IAAE,WAAUA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,MAAI,MAAI,CAAC,CAAC;AAAE,IAAI,KAAG,OAAO,iBAAiB;AAAE,SAAS,EAAEH,IAAE;AAAC,MAAIC,KAAE,GAAG,IAAG,IAAI;AAAE,MAAGA,OAAI,MAAK;AAAC,QAAIC,KAAE,IAAI,MAAM,IAAIF,EAAC,iDAAiD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBE,IAAE,CAAC,GAAEA;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,IAAI,KAAG,OAAO,gBAAgB;AAA9B,IAAgC,KAAGG,GAAE,EAAC,MAAK,mBAAkB,MAAMJ,IAAE,EAAC,OAAMC,GAAC,GAAE;AAAC,MAAIC,KAAE,EAAE,iBAAiB,GAAEC,MAAE,EAAE,MAAI;AAAC,QAAIE,KAAEC,GAAEJ,GAAE,UAAU;AAAE,QAAG,CAACG,GAAE,QAAM,EAAC,OAAM,GAAE,KAAI,EAAC;AAAE,QAAIE,KAAE,OAAO,iBAAiBF,EAAC;AAAE,WAAM,EAAC,OAAM,WAAWE,GAAE,qBAAmBA,GAAE,UAAU,GAAE,KAAI,WAAWA,GAAE,mBAAiBA,GAAE,aAAa,EAAC;AAAA,EAAC,CAAC,GAAED,MAAE,eAAG,EAAE,OAAK,EAAC,oBAAmBH,IAAE,MAAM,OAAM,kBAAiBA,IAAE,MAAM,KAAI,OAAMD,GAAE,QAAQ,MAAM,QAAQ,QAAO,eAAc;AAAC,WAAO;AAAA,EAAE,GAAE,mBAAkB;AAAC,WAAOI,GAAEJ,GAAE,UAAU;AAAA,EAAC,GAAE,UAAS,GAAE,EAAE,CAAC,GAAEM,MAAE,EAAE,MAAI;AAAC,QAAIH;AAAE,YAAOA,KAAEH,GAAE,QAAQ,UAAQ,OAAK,SAAOG,GAAE;AAAA,EAAO,CAAC,GAAEI,KAAEC,GAAE,CAAC;AAAE,SAAO,EAAE,CAACF,GAAC,GAAE,MAAI;AAAC,IAAAC,GAAE,SAAO;AAAA,EAAC,CAAC,GAAE,GAAG,IAAGP,GAAE,QAAQ,QAAMI,MAAE,IAAI,GAAE,MAAI,CAAC,EAAE,OAAM,EAAC,OAAM,EAAC,UAAS,YAAW,OAAM,QAAO,QAAO,GAAGA,IAAE,MAAM,aAAa,CAAC,KAAI,GAAE,KAAI,CAAAD,OAAG;AAAC,QAAGA,IAAE;AAAC,UAAG,OAAO,WAAS,eAAa,QAAQ,IAAI,mBAAiB,UAAQH,GAAE,kBAAkB,UAAQ,EAAE;AAAO,MAAAA,GAAE,kBAAkB,UAAQ,QAAMA,GAAE,QAAQ,MAAM,QAAQ,SAAOA,GAAE,kBAAkB,SAAOI,IAAE,MAAM,cAAcJ,GAAE,kBAAkB,KAAK;AAAA,IAAC;AAAA,EAAC,EAAC,GAAEI,IAAE,MAAM,gBAAgB,EAAE,IAAI,CAAAD,OAAG,GAAGJ,GAAE,QAAQ,EAAC,QAAOC,GAAE,QAAQ,MAAM,QAAQG,GAAE,KAAK,GAAE,MAAKH,GAAE,cAAc,UAAQ,EAAC,CAAC,EAAE,CAAC,GAAE,EAAC,KAAI,GAAGO,GAAE,KAAK,IAAIJ,GAAE,KAAK,IAAG,cAAaA,GAAE,OAAM,gBAAeH,GAAE,QAAQ,MAAM,QAAQ,QAAO,iBAAgBG,GAAE,QAAM,GAAE,OAAM,EAAC,UAAS,YAAW,KAAI,GAAE,MAAK,GAAE,WAAU,cAAcA,GAAE,KAAK,OAAM,gBAAe,OAAM,EAAC,CAAC,CAAC,CAAC,CAAC;AAAC,EAAC,CAAC;AAAt0C,IAAw0C,KAAGD,GAAE,EAAC,MAAK,YAAW,OAAM,EAAC,qBAAoB,CAAAJ,OAAG,KAAE,GAAE,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,CAAC,QAAO,QAAQ,GAAE,UAAS,MAAG,SAAQ,KAAI,GAAE,YAAW,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,cAAa,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,WAAU,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,cAAa,OAAG,MAAMA,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,MAAKC,IAAC,GAAE;AAAC,MAAIG,MAAEI,GAAE,CAAC,GAAEF,MAAEE,GAAE,IAAI,GAAED,KAAEC,GAAE,IAAI,GAAEL,KAAEK,GAAE,IAAI,GAAEH,KAAEG,GAAE,IAAI,GAAEC,KAAED,GAAE,EAAC,QAAO,OAAG,MAAK,MAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,CAAC,GAAEG,MAAEH,GAAE,IAAI,GAAEI,KAAEJ,GAAE,CAAC,GAAEK,MAAEL,GAAE,KAAE;AAAE,WAASM,IAAEC,KAAE,CAAAC,OAAGA,IAAE;AAAC,QAAIA,KAAEL,IAAE,UAAQ,OAAKD,GAAE,MAAMC,IAAE,KAAK,IAAE,MAAKM,MAAEF,GAAEL,GAAE,MAAM,MAAM,CAAC,GAAEQ,KAAED,IAAE,SAAO,KAAGA,IAAE,CAAC,EAAE,QAAQ,MAAM,UAAQ,OAAKA,IAAE,KAAK,CAACE,IAAEC,OAAID,GAAE,QAAQ,MAAM,QAAMC,GAAE,QAAQ,MAAM,KAAK,IAAE,EAAGH,KAAE,CAAAE,OAAGf,GAAEe,GAAE,QAAQ,MAAM,CAAC,GAAEE,KAAEL,KAAEE,GAAE,QAAQF,EAAC,IAAE;AAAK,WAAOK,OAAI,OAAKA,KAAE,OAAM,EAAC,SAAQH,IAAE,mBAAkBG,GAAC;AAAA,EAAC;AAAC,MAAIC,KAAE,EAAE,MAAIxB,GAAE,WAAS,IAAE,CAAC,GAAEyB,KAAE,EAAE,MAAIzB,GAAE,QAAQ,GAAE,CAAC0B,IAAEC,GAAC,IAAE,EAAG,EAAE,MAAI3B,GAAE,UAAU,GAAE,CAAAiB,OAAGd,IAAE,qBAAoBc,EAAC,GAAE,EAAE,MAAIjB,GAAE,YAAY,CAAC,GAAE4B,MAAE,EAAE,MAAIF,GAAE,UAAQ,SAAO,EAAEF,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,OAAM,CAAC,IAAEE,GAAE,KAAK,GAAEG,KAAE,MAAKC,MAAE;AAAK,WAASC,GAAEd,IAAE;AAAC,WAAO,EAAEO,GAAE,OAAM,EAAC,CAAC,CAAC,IAAG;AAAC,aAAOG,OAAG,OAAK,SAAOA,IAAEV,EAAC;AAAA,IAAC,GAAE,CAAC,CAAC,GAAE,MAAI;AAAC,UAAIC,KAAE,EAAEc,GAAE,MAAM,KAAK,EAAE,MAAM,GAAEb,MAAE,EAAEF,EAAC,GAAEG,KAAEF,GAAE,UAAU,CAAAK,OAAGS,GAAE,QAAQb,KAAE,EAAEI,EAAC,CAAC,CAAC;AAAE,aAAOH,OAAI,KAAGF,GAAE,KAAKC,GAAC,IAAED,GAAE,OAAOE,IAAE,CAAC,GAAEO,OAAG,OAAK,SAAOA,IAAET,EAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,MAAIe,KAAE,EAAE,MAAI;AAAA,EAAC,CAAC;AAAE,IAAE,CAACA,EAAC,GAAE,CAAC,CAAChB,EAAC,GAAE,CAACC,EAAC,MAAI;AAAC,QAAGc,GAAE,QAAQ,SAAOf,MAAGC,MAAGL,IAAE,UAAQ,MAAK;AAAC,UAAIM,MAAEF,GAAE,QAAQC,GAAEL,IAAE,KAAK,CAAC;AAAE,MAAAM,QAAI,KAAGN,IAAE,QAAMM,MAAEN,IAAE,QAAM;AAAA,IAAI;AAAA,EAAC,CAAC;AAAE,MAAImB,KAAE,EAAC,eAAc1B,KAAE,OAAMsB,KAAE,MAAKJ,IAAE,QAAQP,IAAEC,IAAE;AAAC,QAAG,OAAOlB,GAAE,MAAI,UAAS;AAAC,UAAImB,MAAEnB,GAAE;AAAG,cAAOiB,MAAG,OAAK,SAAOA,GAAEE,GAAC,QAAMD,MAAG,OAAK,SAAOA,GAAEC,GAAC;AAAA,IAAE;AAAC,WAAOnB,GAAE,OAAK,OAAK,GAAGiB,IAAEC,EAAC,IAAElB,GAAE,GAAGiB,IAAEC,EAAC;AAAA,EAAC,GAAE,eAAeD,IAAE;AAAC,WAAOe,GAAE,QAAQ,QAAMhC,GAAE,OAAK,OAAKgC,GAAE,QAAQ,MAAM,QAAQ,QAAQf,EAAC,IAAEe,GAAE,QAAQ,MAAM,QAAQ,UAAU,CAAAd,OAAGc,GAAE,QAAQd,IAAED,EAAC,CAAC,IAAEL,GAAE,MAAM,UAAU,CAAAM,OAAGc,GAAE,QAAQd,GAAE,QAAQ,OAAMD,EAAC,CAAC;AAAA,EAAC,GAAE,cAAa,EAAE,MAAIjB,GAAE,YAAY,GAAE,UAASyB,IAAE,WAAU,EAAE,MAAI,KAAE,GAAE,SAAQ,EAAE,MAAI,IAAI,GAAE,UAAShB,IAAE,UAASD,KAAE,WAAUH,IAAE,YAAWE,IAAE,UAAS,EAAE,MAAIP,GAAE,QAAQ,GAAE,SAAQY,IAAE,OAAOK,IAAE;AAAC,IAAAU,IAAEV,EAAC;AAAA,EAAC,GAAE,mBAAkB,EAAE,MAAI;AAAC,QAAGF,IAAE,SAAOF,IAAE,UAAQ,SAAOmB,GAAE,QAAQ,QAAMA,GAAE,QAAQ,MAAM,QAAQ,SAAO,IAAEpB,GAAE,MAAM,SAAO,IAAG;AAAC,UAAGoB,GAAE,QAAQ,OAAM;AAAC,YAAId,KAAEc,GAAE,QAAQ,MAAM,QAAQ,UAAU,CAAAb,QAAG;AAAC,cAAIC;AAAE,iBAAM,GAAGA,KAAEY,GAAE,QAAQ,UAAQ,QAAMZ,GAAE,SAASD,GAAC;AAAA,QAAE,CAAC;AAAE,YAAGD,OAAI,GAAG,QAAOA;AAAA,MAAC;AAAC,UAAID,KAAEL,GAAE,MAAM,UAAU,CAAAM,OAAG,CAACA,GAAE,QAAQ,QAAQ;AAAE,UAAGD,OAAI,GAAG,QAAOA;AAAA,IAAC;AAAC,WAAOJ,IAAE;AAAA,EAAK,CAAC,GAAE,mBAAkBC,IAAE,iBAAgBH,IAAE,gBAAe;AAAC,IAAAI,IAAE,QAAM,OAAG,CAACf,GAAE,YAAUM,IAAE,UAAQ,MAAIA,IAAE,QAAM,GAAEO,IAAE,QAAM;AAAA,EAAK,GAAE,eAAc;AAAC,QAAGE,IAAE,QAAM,MAAG,CAACf,GAAE,YAAUM,IAAE,UAAQ,GAAE;AAAC,UAAG0B,GAAE,MAAM,OAAM;AAAC,YAAIf,KAAEe,GAAE,eAAeA,GAAE,MAAM,KAAK;AAAE,QAAAf,OAAI,OAAKJ,IAAE,QAAMI;AAAA,MAAE;AAAC,MAAAX,IAAE,QAAM;AAAA,IAAC;AAAA,EAAC,GAAE,qBAAqBW,IAAE;AAAC,IAAAH,GAAE,QAAMG;AAAA,EAAC,GAAE,WAAWA,IAAEC,IAAEC,KAAE;AAAC,IAAAJ,IAAE,QAAM,OAAGc,OAAI,QAAM,qBAAqBA,EAAC,GAAEA,KAAE,sBAAsB,MAAI;AAAC,UAAG7B,GAAE,YAAUO,GAAE,SAAO,CAACI,GAAE,MAAM,UAAQL,IAAE,UAAQ,EAAE;AAAO,UAAG0B,GAAE,QAAQ,OAAM;AAAC,QAAAnB,IAAE,QAAMI,OAAIZ,GAAE,WAASa,KAAEX,GAAG,EAAC,OAAMU,GAAC,GAAE,EAAC,cAAa,MAAIe,GAAE,QAAQ,MAAM,SAAQ,oBAAmB,MAAI;AAAC,cAAIX,IAAEC;AAAE,kBAAOA,MAAGD,KAAEW,GAAE,kBAAkB,UAAQ,OAAKX,KAAEW,GAAE,QAAQ,MAAM,QAAQ,UAAU,CAAAE,QAAG;AAAC,gBAAIC;AAAE,mBAAM,GAAGA,KAAEH,GAAE,QAAQ,UAAQ,QAAMG,GAAE,SAASD,GAAC;AAAA,UAAE,CAAC,MAAI,OAAKZ,KAAE;AAAA,QAAI,GAAE,iBAAgB,CAAAD,OAAGW,GAAE,QAAQ,MAAM,SAASX,EAAC,GAAE,YAAW;AAAC,gBAAM,IAAI,MAAM,2BAA2B;AAAA,QAAC,EAAC,CAAC,GAAEP,GAAE,QAAMK,OAAG,OAAKA,MAAE;AAAE;AAAA,MAAM;AAAC,UAAIC,KAAEJ,IAAE;AAAE,UAAGI,GAAE,sBAAoB,MAAK;AAAC,YAAIC,KAAED,GAAE,QAAQ,UAAU,CAAAE,OAAG,CAACA,GAAE,QAAQ,QAAQ;AAAE,QAAAD,OAAI,OAAKD,GAAE,oBAAkBC;AAAA,MAAE;AAAC,UAAIE,KAAEN,OAAIZ,GAAE,WAASa,KAAEX,GAAG,EAAC,OAAMU,GAAC,GAAE,EAAC,cAAa,MAAIG,GAAE,SAAQ,oBAAmB,MAAIA,GAAE,mBAAkB,WAAU,CAAAC,OAAGA,GAAE,IAAG,iBAAgB,CAAAA,OAAGA,GAAE,QAAQ,SAAQ,CAAC;AAAE,MAAAR,IAAE,QAAMU,IAAET,GAAE,QAAMK,OAAG,OAAKA,MAAE,GAAEP,GAAE,QAAMQ,GAAE;AAAA,IAAO,CAAC;AAAA,EAAC,GAAE,aAAaH,IAAE;AAAC,QAAIC,KAAEN,GAAE,MAAM,KAAK,CAAAQ,OAAGA,GAAE,OAAKH,EAAC;AAAE,QAAG,CAACC,GAAE;AAAO,QAAG,EAAC,SAAQC,IAAC,IAAED;AAAE,IAAAa,GAAEZ,IAAE,KAAK;AAAA,EAAC,GAAE,qBAAoB;AAAC,QAAGa,GAAE,kBAAkB,UAAQ,MAAK;AAAC,UAAGA,GAAE,QAAQ,MAAM,CAAAD,GAAEC,GAAE,QAAQ,MAAM,QAAQA,GAAE,kBAAkB,KAAK,CAAC;AAAA,WAAM;AAAC,YAAG,EAAC,SAAQf,GAAC,IAAEL,GAAE,MAAMoB,GAAE,kBAAkB,KAAK;AAAE,QAAAD,GAAEd,GAAE,KAAK;AAAA,MAAC;AAAC,MAAAe,GAAE,WAAW3B,GAAE,UAAS2B,GAAE,kBAAkB,KAAK;AAAA,IAAC;AAAA,EAAC,GAAE,eAAef,IAAEC,IAAE;AAAC,QAAIC,MAAE,GAAG,EAAC,IAAGF,IAAE,SAAQC,GAAC,CAAC;AAAE,QAAGc,GAAE,QAAQ,OAAM;AAAC,MAAApB,GAAE,MAAM,KAAKO,GAAC;AAAE;AAAA,IAAM;AAAC,IAAAW,OAAG,qBAAqBA,GAAC;AAAE,QAAIV,KAAEJ,IAAE,CAAAO,QAAIA,GAAE,KAAKJ,GAAC,GAAEI,GAAE;AAAE,IAAAV,IAAE,UAAQ,QAAMmB,GAAE,WAAWd,GAAE,MAAM,KAAK,MAAIE,GAAE,oBAAkBA,GAAE,QAAQ,QAAQD,GAAC,IAAGP,GAAE,QAAMQ,GAAE,SAAQP,IAAE,QAAMO,GAAE,mBAAkBN,GAAE,QAAM,GAAEM,GAAE,QAAQ,KAAK,CAAAG,OAAG,CAACjB,GAAEiB,GAAE,QAAQ,MAAM,CAAC,MAAIO,MAAE,sBAAsB,MAAI;AAAC,UAAIP,KAAEP,IAAE;AAAE,MAAAJ,GAAE,QAAMW,GAAE,SAAQV,IAAE,QAAMU,GAAE;AAAA,IAAiB,CAAC;AAAA,EAAE,GAAE,iBAAiBN,IAAEC,IAAE;AAAC,QAAGW,OAAI,QAAM,qBAAqBA,EAAC,GAAEX,OAAIH,IAAE,QAAM,OAAIiB,GAAE,QAAQ,OAAM;AAAC,MAAApB,GAAE,QAAMA,GAAE,MAAM,OAAO,CAAAQ,OAAGA,GAAE,OAAKH,EAAC;AAAE;AAAA,IAAM;AAAC,QAAIE,MAAEH,IAAE,CAAAI,OAAG;AAAC,UAAIG,KAAEH,GAAE,UAAU,CAAAC,OAAGA,GAAE,OAAKJ,EAAC;AAAE,aAAOM,OAAI,MAAIH,GAAE,OAAOG,IAAE,CAAC,GAAEH;AAAA,IAAC,CAAC;AAAE,IAAAR,GAAE,QAAMO,IAAE,SAAQN,IAAE,QAAMM,IAAE,mBAAkBL,GAAE,QAAM;AAAA,EAAC,GAAE,WAAWG,IAAE;AAAC,WAAO,EAAEO,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE,MAAIQ,GAAE,QAAQ,EAAEA,GAAE,MAAM,KAAK,GAAE,EAAEf,EAAC,CAAC,GAAE,CAAC,CAAC,GAAE,MAAI,EAAEe,GAAE,MAAM,KAAK,EAAE,KAAK,CAAAd,OAAGc,GAAE,QAAQ,EAAEd,EAAC,GAAE,EAAED,EAAC,CAAC,CAAC,EAAC,CAAC;AAAA,EAAC,GAAE,SAASA,IAAE;AAAC,WAAOJ,IAAE,UAAQmB,GAAE,eAAef,EAAC;AAAA,EAAC,EAAC;AAAE,EAAAD,GAAG,CAACP,IAAEJ,IAAEE,EAAC,GAAE,MAAIyB,GAAE,cAAc,GAAE,EAAE,MAAI1B,IAAE,UAAQ,CAAC,CAAC,GAAE,GAAG,IAAG0B,EAAC,GAAEf,GAAG,EAAE,MAAI,EAAEX,IAAE,OAAM,EAAC,CAAC,CAAC,GAAEwB,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,CAAC,CAAC;AAAE,MAAIM,KAAE,EAAE,MAAI;AAAC,QAAInB;AAAE,YAAOA,KAAEX,GAAEG,EAAC,MAAI,OAAK,SAAOQ,GAAE,QAAQ,MAAM;AAAA,EAAC,CAAC;AAAE,SAAO,EAAE,MAAI;AAAC,MAAE,CAACmB,EAAC,GAAE,MAAI;AAAC,UAAG,CAACA,GAAE,SAAOpC,GAAE,iBAAe,OAAO;AAAO,eAASiB,KAAG;AAAC,QAAAe,GAAE,OAAOhC,GAAE,YAAY;AAAA,MAAC;AAAC,aAAOoC,GAAE,MAAM,iBAAiB,SAAQnB,EAAC,GAAE,MAAI;AAAC,YAAIC;AAAE,SAACA,KAAEkB,GAAE,UAAQ,QAAMlB,GAAE,oBAAoB,SAAQD,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAII,IAAEC,IAAEY;AAAE,QAAG,EAAC,MAAKjB,IAAE,UAASC,IAAE,MAAKC,KAAE,GAAGC,GAAC,IAAEpB,IAAEuB,KAAE,EAAC,MAAKjB,IAAE,UAAQ,GAAE,UAASY,IAAE,aAAYc,GAAE,kBAAkB,OAAM,cAAaA,GAAE,kBAAkB,UAAQ,OAAK,OAAKA,GAAE,QAAQ,QAAMA,GAAE,QAAQ,MAAM,SAASX,KAAEW,GAAE,kBAAkB,UAAQ,OAAKX,KAAE,CAAC,KAAGa,OAAGZ,KAAEU,GAAE,QAAQ,MAAMA,GAAE,kBAAkB,KAAK,MAAI,OAAK,SAAOV,GAAE,QAAQ,UAAQ,OAAKY,MAAE,MAAK,OAAMN,IAAE,MAAK;AAAE,WAAO,EAAE,IAAG,CAAC,GAAGX,MAAG,QAAMW,IAAE,SAAO,OAAKnB,GAAG,EAAC,CAACQ,EAAC,GAAEW,IAAE,MAAK,CAAC,EAAE,IAAI,CAAC,CAACO,IAAEE,GAAE,MAAI,EAAE9B,IAAGQ,GAAG,EAAC,UAASP,GAAG,QAAO,KAAI2B,IAAE,IAAG,SAAQ,MAAK,UAAS,QAAO,MAAG,UAAS,MAAG,MAAKhB,KAAE,UAASD,IAAE,MAAKiB,IAAE,OAAME,IAAE,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,EAAE,EAAC,YAAW,EAAC,GAAGnC,IAAE,GAAG+B,GAAEb,IAAE,CAAC,MAAK,gBAAe,aAAY,cAAa,YAAW,YAAW,uBAAsB,SAAS,CAAC,EAAC,GAAE,UAAS,CAAC,GAAE,MAAKG,IAAE,OAAMtB,IAAE,OAAMC,IAAE,MAAK,WAAU,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAzsO,IAA2sO,KAAGE,GAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,QAAO,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMJ,IAAE,EAAC,OAAMC,IAAE,OAAMC,GAAC,GAAE;AAAC,MAAIO;AAAE,MAAIN,OAAGM,KAAET,GAAE,OAAK,OAAKS,KAAE,6BAA6BqB,GAAE,CAAC,IAAGxB,MAAE,EAAE,eAAe;AAAE,WAASE,MAAG;AAAC,QAAIH;AAAE,KAACA,KAAEC,GAAEA,IAAE,QAAQ,MAAI,QAAMD,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAIA,KAAE,EAAC,MAAKC,IAAE,cAAc,UAAQ,GAAE,UAASA,IAAE,SAAS,MAAK,GAAE,EAAC,GAAGC,GAAC,IAAEP,IAAEW,KAAE,EAAC,IAAGR,KAAE,KAAIG,IAAE,UAAS,SAAQE,IAAC;AAAE,WAAO,EAAE,EAAC,UAASG,IAAE,YAAWJ,IAAE,MAAKF,IAAE,OAAMJ,IAAE,OAAMC,IAAE,MAAK,gBAAe,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAvrP,IAAyrP,KAAGE,GAAE,EAAC,MAAK,kBAAiB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMJ,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOC,IAAC,GAAE;AAAC,MAAIQ;AAAE,MAAIL,OAAGK,KAAEX,GAAE,OAAK,OAAKW,KAAE,8BAA8BmB,GAAE,CAAC,IAAGtB,MAAE,EAAE,gBAAgB;AAAE,EAAAL,IAAE,EAAC,IAAGK,IAAE,WAAU,KAAIA,IAAE,UAAS,CAAC;AAAE,WAASC,GAAEG,IAAE;AAAC,IAAAJ,IAAE,SAAS,UAAQA,IAAE,cAAc,UAAQ,IAAEA,IAAE,cAAc,KAAGI,GAAE,eAAe,GAAEJ,IAAE,aAAa,IAAG8B,GAAE,MAAI;AAAC,UAAIzB;AAAE,cAAOA,MAAEP,GAAEE,IAAE,QAAQ,MAAI,OAAK,SAAOK,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAC,WAASR,GAAEO,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKN,GAAE;AAAU,QAAAM,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEJ,IAAE,cAAc,UAAQ,KAAGA,IAAE,aAAa,GAAE8B,GAAE,MAAI;AAAC,cAAIzB;AAAE,kBAAOA,MAAEL,IAAE,SAAS,UAAQ,OAAK,SAAOK,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAE;AAAA,MAAO,KAAKP,GAAE;AAAQ,QAAAM,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEJ,IAAE,cAAc,UAAQ,MAAIA,IAAE,aAAa,GAAE8B,GAAE,MAAI;AAAC,UAAA9B,IAAE,MAAM,SAAOA,IAAE,WAAWH,GAAE,IAAI;AAAA,QAAC,CAAC,IAAGiC,GAAE,MAAI;AAAC,cAAIzB;AAAE,kBAAOA,MAAEL,IAAE,SAAS,UAAQ,OAAK,SAAOK,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAE;AAAA,MAAO,KAAKP,GAAE;AAAO,YAAGE,IAAE,cAAc,UAAQ,EAAE;AAAO,QAAAI,GAAE,eAAe,GAAEJ,IAAE,WAAW,SAAO,CAACA,IAAE,gBAAgB,MAAM,UAAQI,GAAE,gBAAgB,GAAEJ,IAAE,cAAc,GAAE8B,GAAE,MAAI;AAAC,cAAIzB;AAAE,kBAAOA,MAAEL,IAAE,SAAS,UAAQ,OAAK,SAAOK,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAE;AAAA,IAAM;AAAA,EAAC;AAAC,MAAIN,KAAEY,GAAG,EAAE,OAAK,EAAC,IAAGnB,GAAE,IAAG,MAAKC,GAAE,KAAI,EAAE,GAAEO,IAAE,SAAS;AAAE,SAAM,MAAI;AAAC,QAAIO,KAAEC;AAAE,QAAIJ,KAAE,EAAC,MAAKJ,IAAE,cAAc,UAAQ,GAAE,UAASA,IAAE,SAAS,OAAM,OAAMA,IAAE,MAAM,MAAK,GAAE,EAAC,GAAGK,IAAC,IAAEb,IAAEc,KAAE,EAAC,KAAIN,IAAE,WAAU,IAAGF,KAAE,MAAKC,GAAE,OAAM,UAAS,MAAK,iBAAgB,WAAU,kBAAiBQ,MAAET,GAAEE,IAAE,UAAU,MAAI,OAAK,SAAOO,IAAE,IAAG,iBAAgBP,IAAE,cAAc,UAAQ,GAAE,mBAAkBA,IAAE,SAAS,QAAM,EAAEQ,MAAEV,GAAEE,IAAE,QAAQ,MAAI,OAAK,SAAOQ,IAAE,IAAGV,GAAC,EAAE,KAAK,GAAG,IAAE,QAAO,UAASE,IAAE,SAAS,UAAQ,OAAG,OAAG,QAAO,WAAUH,IAAE,SAAQI,GAAC;AAAE,WAAO,EAAE,EAAC,UAASK,IAAE,YAAWD,KAAE,MAAKD,IAAE,OAAMX,IAAE,OAAMC,IAAE,MAAK,iBAAgB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA34S,IAA64S,KAAGE,GAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,QAAO,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,cAAa,EAAC,MAAK,SAAQ,GAAE,cAAa,EAAC,MAAK,QAAO,SAAQ,OAAM,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,OAAM,EAAC,QAAO,CAAAJ,OAAG,KAAE,GAAE,MAAMA,IAAE,EAAC,MAAKC,IAAE,OAAMC,IAAE,OAAMC,KAAE,QAAOG,IAAC,GAAE;AAAC,MAAIuB;AAAE,MAAIrB,OAAGqB,KAAE7B,GAAE,OAAK,OAAK6B,KAAE,6BAA6BC,GAAE,CAAC,IAAGrB,KAAE,EAAE,eAAe,GAAEJ,KAAE,EAAE,MAAIyB,GAAGxB,GAAEG,GAAE,QAAQ,CAAC,CAAC,GAAEF,KAAE,EAAC,OAAM,MAAE;AAAE,EAAAD,IAAE,EAAC,IAAGG,GAAE,UAAS,KAAIA,GAAE,SAAQ,CAAC;AAAE,WAASE,KAAG;AAAC,IAAAF,GAAE,OAAO,IAAI;AAAE,QAAIqB,MAAExB,GAAEG,GAAE,UAAU;AAAE,IAAAqB,QAAIA,IAAE,YAAU,IAAGrB,GAAE,WAAWJ,GAAE,OAAO;AAAA,EAAC;AAAC,MAAIO,KAAE,EAAE,MAAI;AAAC,QAAImB;AAAE,QAAID,MAAErB,GAAE,MAAM;AAAM,WAAOH,GAAEG,GAAE,QAAQ,IAAE,OAAOT,GAAE,gBAAc,eAAa8B,QAAI,UAAQC,KAAE/B,GAAE,aAAa8B,GAAC,MAAI,OAAKC,KAAE,KAAG,OAAOD,OAAG,WAASA,MAAE,KAAG;AAAA,EAAE,CAAC;AAAE,IAAE,MAAI;AAAC,MAAE,CAAClB,IAAEH,GAAE,eAAcJ,EAAC,GAAE,CAAC,CAACyB,KAAEC,EAAC,GAAE,CAACE,IAAED,EAAC,MAAI;AAAC,UAAGzB,GAAE,MAAM;AAAO,UAAI6B,KAAE9B,GAAEG,GAAE,QAAQ;AAAE,MAAA2B,QAAKJ,OAAI,KAAGD,OAAI,KAAGD,QAAIG,QAAKG,GAAE,QAAMN,MAAG,sBAAsB,MAAI;AAAC,YAAIX;AAAE,YAAGZ,GAAE,SAAO,CAAC6B,QAAKjB,MAAEd,GAAE,UAAQ,OAAK,SAAOc,IAAE,mBAAiBiB,GAAE;AAAO,YAAG,EAAC,gBAAenB,IAAE,cAAaC,GAAC,IAAEkB;AAAE,aAAK,KAAKlB,MAAG,OAAKA,KAAE,MAAID,MAAG,OAAKA,KAAE,EAAE,MAAI,KAAGA,OAAI,KAAGmB,GAAE,kBAAkBA,GAAE,MAAM,QAAOA,GAAE,MAAM,MAAM;AAAA,MAAC,CAAC;AAAA,IAAE,GAAE,EAAC,WAAU,KAAE,CAAC,GAAE,EAAE,CAAC3B,GAAE,aAAa,GAAE,CAAC,CAACqB,GAAC,GAAE,CAACC,EAAC,MAAI;AAAC,UAAGD,QAAI,KAAGC,OAAI,GAAE;AAAC,YAAGxB,GAAE,MAAM;AAAO,YAAI0B,KAAE3B,GAAEG,GAAE,QAAQ;AAAE,YAAG,CAACwB,GAAE;AAAO,YAAID,KAAEC,GAAE,OAAM,EAAC,gBAAeG,IAAE,cAAanB,IAAE,oBAAmBC,GAAC,IAAEe;AAAE,QAAAA,GAAE,QAAM,IAAGA,GAAE,QAAMD,IAAEd,OAAI,OAAKe,GAAE,kBAAkBG,IAAEnB,IAAEC,EAAC,IAAEe,GAAE,kBAAkBG,IAAEnB,EAAC;AAAA,MAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAE,MAAIJ,MAAEH,GAAE,KAAE;AAAE,WAASI,KAAG;AAAC,IAAAD,IAAE,QAAM;AAAA,EAAE;AAAC,WAASE,MAAG;AAAC,MAAG,EAAE,UAAU,MAAI;AAAC,MAAAF,IAAE,QAAM;AAAA,IAAE,CAAC;AAAA,EAAC;AAAC,MAAIG,MAAEC,GAAG;AAAE,WAASO,GAAEM,KAAE;AAAC,YAAOvB,GAAE,QAAM,MAAGS,IAAE,MAAI;AAAC,MAAAT,GAAE,QAAM;AAAA,IAAE,CAAC,GAAEuB,IAAE,KAAI;AAAA,MAAC,KAAKxB,GAAE;AAAM,YAAGC,GAAE,QAAM,OAAGE,GAAE,cAAc,UAAQ,KAAGI,IAAE,MAAM;AAAO,YAAGiB,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAErB,GAAE,kBAAkB,UAAQ,MAAK;AAAC,UAAAA,GAAE,cAAc;AAAE;AAAA,QAAM;AAAC,QAAAA,GAAE,mBAAmB,GAAEA,GAAE,KAAK,UAAQ,KAAGA,GAAE,cAAc;AAAE;AAAA,MAAM,KAAKH,GAAE;AAAU,eAAOC,GAAE,QAAM,OAAGuB,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAE,EAAErB,GAAE,cAAc,OAAM,EAAC,CAAC,CAAC,GAAE,MAAIA,GAAE,WAAWJ,GAAE,IAAI,GAAE,CAAC,CAAC,GAAE,MAAII,GAAE,aAAa,EAAC,CAAC;AAAA,MAAE,KAAKH,GAAE;AAAQ,eAAOC,GAAE,QAAM,OAAGuB,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAE,EAAErB,GAAE,cAAc,OAAM,EAAC,CAAC,CAAC,GAAE,MAAIA,GAAE,WAAWJ,GAAE,QAAQ,GAAE,CAAC,CAAC,GAAE,MAAI;AAAC,UAAAI,GAAE,aAAa,GAAE6B,GAAE,MAAI;AAAC,YAAA7B,GAAE,MAAM,SAAOA,GAAE,WAAWJ,GAAE,IAAI;AAAA,UAAC,CAAC;AAAA,QAAC,EAAC,CAAC;AAAA,MAAE,KAAKC,GAAE;AAAK,YAAGwB,IAAE,SAAS;AAAM,eAAOvB,GAAE,QAAM,OAAGuB,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAErB,GAAE,WAAWJ,GAAE,KAAK;AAAA,MAAE,KAAKC,GAAE;AAAO,eAAOC,GAAE,QAAM,OAAGuB,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAErB,GAAE,WAAWJ,GAAE,KAAK;AAAA,MAAE,KAAKC,GAAE;AAAI,YAAGwB,IAAE,SAAS;AAAM,eAAOvB,GAAE,QAAM,OAAGuB,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAErB,GAAE,WAAWJ,GAAE,IAAI;AAAA,MAAE,KAAKC,GAAE;AAAS,eAAOC,GAAE,QAAM,OAAGuB,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAErB,GAAE,WAAWJ,GAAE,IAAI;AAAA,MAAE,KAAKC,GAAE;AAAO,YAAGC,GAAE,QAAM,OAAGE,GAAE,cAAc,UAAQ,EAAE;AAAO,QAAAqB,IAAE,eAAe,GAAErB,GAAE,WAAW,SAAO,CAACA,GAAE,gBAAgB,MAAM,UAAQqB,IAAE,gBAAgB,GAAErB,GAAE,SAAS,SAAOA,GAAE,KAAK,UAAQ,KAAGA,GAAE,MAAM,UAAQ,QAAME,GAAE,GAAEF,GAAE,cAAc;AAAE;AAAA,MAAM,KAAKH,GAAE;AAAI,YAAGC,GAAE,QAAM,OAAGE,GAAE,cAAc,UAAQ,EAAE;AAAO,QAAAA,GAAE,KAAK,UAAQ,KAAGA,GAAE,kBAAkB,UAAQ,KAAGA,GAAE,mBAAmB,GAAEA,GAAE,cAAc;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASgB,GAAEK,KAAE;AAAC,IAAA7B,GAAE,UAAS6B,GAAC,GAAErB,GAAE,SAAS,SAAOA,GAAE,KAAK,UAAQ,KAAGqB,IAAE,OAAO,UAAQ,MAAInB,GAAE,GAAEF,GAAE,aAAa;AAAA,EAAC;AAAC,WAASiB,GAAEI,KAAE;AAAC,QAAIG,IAAED,IAAEI;AAAE,QAAIL,MAAGE,KAAEH,IAAE,kBAAgB,OAAKG,KAAEhB,GAAG,KAAK,CAAAA,OAAGA,OAAIa,IAAE,aAAa;AAAE,QAAGvB,GAAE,QAAM,OAAG,GAAGyB,KAAE1B,GAAEG,GAAE,UAAU,MAAI,QAAMuB,GAAE,SAASD,EAAC,MAAI,GAAGK,KAAE9B,GAAEG,GAAE,SAAS,MAAI,QAAM2B,GAAE,SAASL,EAAC,MAAItB,GAAE,cAAc,UAAQ,EAAE,QAAOqB,IAAE,eAAe,GAAErB,GAAE,KAAK,UAAQ,MAAIA,GAAE,SAAS,SAAOA,GAAE,MAAM,UAAQ,OAAKE,GAAE,IAAEF,GAAE,kBAAkB,UAAQ,KAAGA,GAAE,mBAAmB,IAAGA,GAAE,cAAc;AAAA,EAAC;AAAC,WAASkB,IAAEG,KAAE;AAAC,QAAIG,IAAED,IAAEI;AAAE,QAAIL,MAAGE,KAAEH,IAAE,kBAAgB,OAAKG,KAAEhB,GAAG,KAAK,CAAAA,OAAGA,OAAIa,IAAE,aAAa;AAAE,KAACE,KAAE1B,GAAEG,GAAE,SAAS,MAAI,QAAMuB,GAAE,SAASD,EAAC,MAAIK,KAAE9B,GAAEG,GAAE,UAAU,MAAI,QAAM2B,GAAE,SAASL,EAAC,KAAGtB,GAAE,SAAS,SAAOA,GAAE,UAAU,SAAOA,GAAE,cAAc,UAAQ,MAAIA,GAAE,aAAa,GAAE,EAAG,EAAE,UAAU,MAAI;AAAC,MAAAA,GAAE,qBAAqB,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAC,MAAImB,MAAE,EAAE,MAAI;AAAC,QAAIE,KAAEC,IAAEE,IAAED;AAAE,YAAOA,MAAGC,MAAGF,KAAE/B,GAAE,iBAAe,OAAK+B,KAAEtB,GAAE,aAAa,UAAQ,UAAQqB,MAAE9B,GAAE,iBAAe,OAAK,SAAO8B,IAAE,KAAK9B,IAAES,GAAE,aAAa,KAAK,IAAE,SAAO,OAAKwB,KAAExB,GAAE,aAAa,UAAQ,OAAKuB,KAAE;AAAA,EAAE,CAAC;AAAE,SAAM,MAAI;AAAC,QAAIf,IAAEC,IAAEC,KAAEC,IAAEG,IAAEF,IAAEC;AAAE,QAAIQ,MAAE,EAAC,MAAKrB,GAAE,cAAc,UAAQ,EAAC,GAAE,EAAC,cAAasB,IAAE,UAASE,IAAE,GAAGD,GAAC,IAAEhC,IAAEoC,KAAE,EAAC,kBAAiBnB,KAAER,GAAE,WAAW,UAAQ,OAAK,SAAOQ,GAAE,IAAG,iBAAgBR,GAAE,cAAc,UAAQ,GAAE,yBAAwBA,GAAE,kBAAkB,UAAQ,OAAK,SAAOA,GAAE,QAAQ,SAAOS,KAAET,GAAE,QAAQ,MAAM,KAAK,CAAAyB,QAAG,CAACzB,GAAE,QAAQ,MAAM,SAASyB,IAAE,QAAQ,KAAK,KAAGzB,GAAE,QAAQyB,IAAE,QAAQ,OAAMzB,GAAE,QAAQ,MAAM,QAAQA,GAAE,kBAAkB,KAAK,CAAC,CAAC,MAAI,OAAK,SAAOS,GAAE,MAAIC,MAAEV,GAAE,QAAQ,MAAMA,GAAE,kBAAkB,KAAK,MAAI,OAAK,SAAOU,IAAE,IAAG,oBAAmBE,MAAGD,KAAEd,GAAEG,GAAE,QAAQ,MAAI,OAAK,SAAOW,GAAE,OAAK,OAAKC,MAAGE,KAAEjB,GAAEG,GAAE,SAAS,MAAI,OAAK,SAAOc,GAAE,IAAG,qBAAoB,QAAO,IAAGf,KAAE,oBAAmBM,IAAE,kBAAiBC,KAAE,WAAUS,IAAE,SAAQC,IAAE,SAAQE,KAAE,QAAOD,IAAE,MAAK,YAAW,OAAMJ,KAAEpB,GAAE,SAAO,OAAKoB,KAAE,QAAO,UAAS,GAAE,KAAIb,GAAE,UAAS,cAAamB,IAAE,OAAM,UAASnB,GAAE,SAAS,UAAQ,OAAG,OAAG,OAAM;AAAE,WAAO,EAAE,EAAC,UAAS2B,IAAE,YAAWJ,IAAE,MAAKF,KAAE,OAAM5B,IAAE,OAAMC,KAAE,UAASmC,GAAE,iBAAeA,GAAE,QAAO,MAAK,gBAAe,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAvrc,IAAyrc,KAAGlC,GAAE,EAAC,MAAK,mBAAkB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,KAAI,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,MAAK,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,EAAC,GAAE,MAAMJ,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOC,IAAC,GAAE;AAAC,MAAIG,MAAE,EAAE,iBAAiB,GAAEE,MAAE,+BAA+BsB,GAAE,CAAC;AAAG,EAAA3B,IAAE,EAAC,IAAGG,IAAE,YAAW,KAAIA,IAAE,WAAU,CAAC,GAAE,EAAE,MAAI;AAAC,IAAAA,IAAE,gBAAgB,MAAM,SAAON,GAAE;AAAA,EAAM,CAAC,GAAE,EAAE,MAAI;AAAC,IAAAM,IAAE,gBAAgB,MAAM,OAAKN,GAAE;AAAA,EAAI,CAAC;AAAE,MAAIS,KAAEuB,GAAG,GAAE3B,KAAE,EAAE,MAAII,OAAI,QAAMA,GAAE,QAAMqB,GAAE,UAAQA,GAAE,OAAKxB,IAAE,cAAc,UAAQ,CAAC;AAAE,EAAAwB,GAAG,EAAC,WAAU,EAAE,MAAIxB,GAAEA,IAAE,UAAU,CAAC,GAAE,SAAQ,EAAE,MAAIA,IAAE,cAAc,UAAQ,CAAC,GAAE,OAAOK,IAAE;AAAC,WAAOA,GAAE,aAAa,MAAM,MAAI,WAAS,WAAW,gBAAcA,GAAE,aAAa,MAAM,IAAE,WAAW,cAAY,WAAW;AAAA,EAAa,GAAE,KAAKA,IAAE;AAAC,IAAAA,GAAE,aAAa,QAAO,MAAM;AAAA,EAAC,EAAC,CAAC;AAAE,WAASJ,GAAEI,IAAE;AAAC,IAAAA,GAAE,eAAe;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAIG,IAAEC,KAAEC;AAAE,QAAIL,KAAE,EAAC,MAAKL,IAAE,cAAc,UAAQ,EAAC,GAAEM,KAAE,EAAC,oBAAmBI,OAAGF,KAAER,GAAEA,IAAE,QAAQ,MAAI,OAAK,SAAOQ,GAAE,OAAK,OAAKE,OAAGD,MAAET,GAAEA,IAAE,SAAS,MAAI,OAAK,SAAOS,IAAE,IAAG,IAAGP,KAAE,KAAIF,IAAE,YAAW,MAAK,WAAU,wBAAuBA,IAAE,KAAK,UAAQ,IAAE,OAAG,QAAO,aAAYC,GAAC,GAAEM,MAAEoB,GAAEjC,IAAE,CAAC,MAAM,CAAC;AAAE,WAAO,EAAE,EAAC,UAASY,IAAE,YAAWC,KAAE,MAAKF,IAAE,OAAMV,IAAE,OAAMK,IAAE,QAAQ,SAAOA,IAAE,cAAc,UAAQ,IAAE,EAAC,GAAGJ,IAAE,SAAQ,MAAI,CAAC,EAAE,IAAG,CAAC,GAAEA,GAAE,OAAO,CAAC,EAAC,IAAEA,IAAE,UAASoC,GAAE,iBAAeA,GAAE,QAAO,SAAQjC,GAAE,OAAM,MAAK,kBAAiB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAt8e,IAAw8e,KAAGD,GAAE,EAAC,MAAK,kBAAiB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,KAAI,GAAE,OAAM,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,EAAC,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,OAAM,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,KAAI,EAAC,GAAE,MAAMJ,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOC,IAAC,GAAE;AAAC,MAAIG,MAAE,EAAE,gBAAgB,GAAEE,MAAE,8BAA8BsB,GAAE,CAAC,IAAGrB,KAAEC,GAAE,IAAI,GAAEL,KAAE,EAAE,MAAIL,GAAE,QAAQ;AAAE,EAAAG,IAAE,EAAC,IAAGM,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAIF,KAAE,EAAE,MAAI;AAAC,QAAIoB;AAAE,WAAOrB,IAAE,QAAQ,QAAMA,IAAE,kBAAkB,UAAQA,IAAE,eAAeN,GAAE,KAAK,IAAEM,IAAE,kBAAkB,UAAQ,OAAK,UAAKqB,MAAErB,IAAE,QAAQ,MAAMA,IAAE,kBAAkB,KAAK,MAAI,OAAK,SAAOqB,IAAE,QAAMnB;AAAA,EAAC,CAAC,GAAEG,KAAE,EAAE,MAAIL,IAAE,WAAWN,GAAE,KAAK,CAAC,GAAEY,KAAE,GAAG,IAAG,IAAI,GAAEC,MAAE,EAAE,OAAK,EAAC,UAASb,GAAE,UAAS,OAAMA,GAAE,OAAM,QAAOS,IAAE,OAAM,EAAE,MAAIT,GAAE,KAAK,EAAC,EAAE;AAAE,IAAE,MAAIM,IAAE,eAAeE,KAAEK,GAAC,CAAC,GAAE,GAAG,MAAIP,IAAE,iBAAiBE,KAAED,GAAE,KAAK,CAAC,GAAE,EAAE,MAAI;AAAC,QAAIoB,MAAErB,GAAEG,EAAC;AAAE,IAAAkB,QAAIf,MAAG,QAAMA,GAAE,MAAM,eAAee,GAAC;AAAA,EAAE,CAAC,GAAE,EAAE,MAAI;AAAC,IAAArB,IAAE,cAAc,UAAQ,KAAGC,GAAE,UAAQD,IAAE,QAAQ,SAAOA,IAAE,kBAAkB,UAAQ,KAAGgC,GAAE,MAAI;AAAC,UAAIX,KAAEC;AAAE,cAAOA,OAAGD,MAAErB,GAAEG,EAAC,MAAI,OAAK,SAAOkB,IAAE,mBAAiB,OAAK,SAAOC,IAAE,KAAKD,KAAE,EAAC,OAAM,UAAS,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE,CAAC;AAAE,WAASb,GAAEa,KAAE;AAAC,IAAAA,IAAE,eAAe,GAAEA,IAAE,WAAS,EAAG,SAAOtB,GAAE,UAAQC,IAAE,aAAaE,GAAC,GAAEU,GAAG,KAAG,sBAAsB,MAAI;AAAC,UAAIU;AAAE,cAAOA,MAAEtB,GAAEA,IAAE,QAAQ,MAAI,OAAK,SAAOsB,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC,GAAEtB,IAAE,KAAK,UAAQ,KAAGA,IAAE,cAAc;AAAA,EAAG;AAAC,WAASS,MAAG;AAAC,QAAIa;AAAE,QAAG5B,GAAE,aAAW4B,MAAEtB,IAAE,QAAQ,UAAQ,QAAMsB,IAAE,SAAS5B,GAAE,KAAK,EAAE,QAAOM,IAAE,WAAWD,GAAE,OAAO;AAAE,QAAIsB,MAAErB,IAAE,eAAeN,GAAE,KAAK;AAAE,IAAAM,IAAE,WAAWD,GAAE,UAASsB,GAAC;AAAA,EAAC;AAAC,MAAIX,MAAER,GAAG;AAAE,WAASgB,GAAEG,KAAE;AAAC,IAAAX,IAAE,OAAOW,GAAC;AAAA,EAAC;AAAC,WAASF,GAAEE,KAAE;AAAC,QAAIE;AAAE,QAAG,CAACb,IAAE,SAASW,GAAC,KAAG3B,GAAE,aAAW6B,KAAEvB,IAAE,QAAQ,UAAQ,QAAMuB,GAAE,SAAS7B,GAAE,KAAK,KAAGO,GAAE,MAAM;AAAO,QAAIqB,MAAEtB,IAAE,eAAeN,GAAE,KAAK;AAAE,IAAAM,IAAE,WAAWD,GAAE,UAASuB,KAAE,CAAC;AAAA,EAAC;AAAC,WAASF,GAAEC,KAAE;AAAC,QAAIC;AAAE,IAAAZ,IAAE,SAASW,GAAC,MAAI3B,GAAE,aAAW4B,MAAEtB,IAAE,QAAQ,UAAQ,QAAMsB,IAAE,SAAS5B,GAAE,KAAK,KAAGO,GAAE,UAAQD,IAAE,gBAAgB,MAAM,QAAMA,IAAE,WAAWD,GAAE,OAAO;AAAA,EAAG;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,UAASsB,IAAC,IAAE3B,IAAE4B,MAAE,EAAC,QAAOrB,GAAE,OAAM,UAASI,GAAE,OAAM,UAASgB,IAAC,GAAEE,KAAE,EAAC,IAAGrB,KAAE,KAAIC,IAAE,MAAK,UAAS,UAASkB,QAAI,OAAG,SAAO,IAAG,iBAAgBA,QAAI,OAAG,OAAG,QAAO,iBAAgBhB,GAAE,OAAM,UAAS,QAAO,aAAYG,IAAE,SAAQC,KAAE,gBAAeS,IAAE,cAAaA,IAAE,eAAcC,IAAE,aAAYA,IAAE,gBAAeC,IAAE,cAAaA,GAAC,GAAEI,MAAEG,GAAEjC,IAAE,CAAC,SAAQ,OAAO,CAAC;AAAE,WAAO,EAAE,EAAC,UAAS6B,IAAE,YAAWC,KAAE,MAAKF,KAAE,OAAM1B,IAAE,OAAMD,IAAE,MAAK,iBAAgB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;A4BAjonB,SAAO,YAAYsC,IAAE,mBAAmBC,IAAE,KAAKC,IAAE,UAAUC,IAAE,YAAYC,KAAG,aAAaC,IAAE,eAAe,IAAG,WAAWC,KAAG,OAAOC,IAAE,eAAeC,WAAO;;;ACA1J,SAAO,YAAYC,IAAE,mBAAmBC,IAAE,YAAYC,IAAE,KAAK,GAAE,aAAa,GAAE,eAAeC,IAAE,OAAOC,IAAE,SAASC,IAAE,eAAeC,UAAM;;;ACAxI,SAAO,eAAeC,WAAM;AAA4C,SAASC,GAAEC,IAAEC,IAAEC,KAAEC,IAAE;AAAC,IAAE,YAAUC,IAAE,CAAAC,OAAG;AAAC,IAAAL,KAAEA,MAAG,OAAKA,KAAE,QAAOA,GAAE,iBAAiBC,IAAEC,KAAEC,EAAC,GAAEE,GAAE,MAAIL,GAAE,oBAAoBC,IAAEC,KAAEC,EAAC,CAAC;AAAA,EAAC,CAAC;AAAC;;;ACAhM,SAAO,OAAOG,UAAM;AAA6D,IAAIC,MAAG,CAAAC,QAAIA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,KAAID,MAAG,CAAC,CAAC;AAAE,SAASE,KAAG;AAAC,MAAIC,MAAEC,GAAE,CAAC;AAAE,SAAOC,GAAE,WAAU,CAAAC,OAAG;AAAC,IAAAA,GAAE,QAAM,UAAQH,IAAE,QAAMG,GAAE,WAAS,IAAE;AAAA,EAAE,CAAC,GAAEH;AAAC;;;AFAugB,SAAS,EAAEI,IAAE;AAAC,MAAG,CAACA,GAAE,QAAO,oBAAI;AAAI,MAAG,OAAOA,MAAG,WAAW,QAAO,IAAI,IAAIA,GAAE,CAAC;AAAE,MAAIC,KAAE,oBAAI;AAAI,WAAQC,MAAKF,GAAE,OAAM;AAAC,QAAIG,KAAEC,GAAEF,EAAC;AAAE,IAAAC,cAAa,eAAaF,GAAE,IAAIE,EAAC;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAC,IAAII,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,eAAa,CAAC,IAAE,gBAAeA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,eAAa,EAAE,IAAE,gBAAeA,GAAEA,GAAE,MAAI,EAAE,IAAE,OAAMA,KAAID,MAAG,CAAC,CAAC;AAAE,IAAI,KAAG,OAAO,OAAOE,GAAE,EAAC,MAAK,aAAY,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,cAAa,EAAC,MAAK,QAAO,SAAQ,KAAI,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,GAAE,GAAE,YAAW,EAAC,MAAK,CAAC,QAAO,QAAQ,GAAE,SAAQC,GAAE,oBAAI,KAAG,EAAC,EAAC,GAAE,cAAa,OAAG,MAAMR,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOC,GAAC,GAAE;AAAC,MAAIC,MAAEI,GAAE,IAAI;AAAE,EAAAL,GAAE,EAAC,IAAGC,KAAE,KAAIA,IAAC,CAAC;AAAE,MAAIK,MAAEC,GAAE,MAAID,GAAEL,GAAC,CAAC,GAAEE,KAAEE,GAAE,KAAE;AAAE,IAAE,MAAIF,GAAE,QAAM,IAAE,GAAEK,GAAE,MAAIL,GAAE,QAAM,KAAE,GAAE,EAAE,EAAC,eAAcG,IAAC,GAAEC,GAAE,MAAIJ,GAAE,SAAO,QAAQN,GAAE,WAAS,EAAE,CAAC,CAAC;AAAE,MAAIY,MAAEC,GAAE,EAAC,eAAcJ,KAAE,WAAUL,KAAE,cAAaM,GAAE,MAAIV,GAAE,YAAY,EAAC,GAAEU,GAAE,MAAIJ,GAAE,SAAO,QAAQN,GAAE,WAAS,CAAC,CAAC,CAAC;AAAE,EAAAc,GAAE,EAAC,eAAcL,KAAE,WAAUL,KAAE,YAAWJ,GAAE,YAAW,uBAAsBY,IAAC,GAAEF,GAAE,MAAIJ,GAAE,SAAO,QAAQN,GAAE,WAAS,CAAC,CAAC,CAAC;AAAE,MAAIe,KAAEd,GAAE;AAAE,WAASe,GAAEC,KAAE;AAAC,QAAIC,KAAEd,GAAEA,GAAC;AAAE,QAAG,CAACc,GAAE;AAAO,KAAC,CAAAC,QAAGA,IAAE,GAAG,MAAI;AAAC,QAAEJ,GAAE,OAAM,EAAC,CAACK,GAAE,QAAQ,GAAE,MAAI;AAAC,UAAEF,IAAE,EAAE,OAAM,EAAC,cAAa,CAACD,IAAE,aAAa,EAAC,CAAC;AAAA,MAAC,GAAE,CAACG,GAAE,SAAS,GAAE,MAAI;AAAC,UAAEF,IAAE,EAAE,MAAK,EAAC,cAAa,CAACD,IAAE,aAAa,EAAC,CAAC;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC;AAAC,MAAII,MAAEb,GAAE,KAAE;AAAE,WAASc,GAAEL,KAAE;AAAC,IAAAA,IAAE,QAAM,UAAQI,IAAE,QAAM,MAAG,sBAAsB,MAAI;AAAC,MAAAA,IAAE,QAAM;AAAA,IAAE,CAAC;AAAA,EAAE;AAAC,WAASE,GAAEN,KAAE;AAAC,QAAG,CAACX,GAAE,MAAM;AAAO,QAAIY,KAAE,EAAElB,GAAE,UAAU;AAAE,IAAAI,GAAEA,GAAC,aAAY,eAAac,GAAE,IAAId,GAAEA,GAAC,CAAC;AAAE,QAAIgB,MAAEH,IAAE;AAAc,IAAAG,eAAa,eAAaA,IAAE,QAAQ,yBAAuB,WAASI,GAAEN,IAAEE,GAAC,MAAIC,IAAE,QAAM,EAAEjB,GAAEA,GAAC,GAAE,EAAEW,GAAE,OAAM,EAAC,CAACK,GAAE,QAAQ,GAAE,MAAI,EAAE,MAAK,CAACA,GAAE,SAAS,GAAE,MAAI,EAAE,SAAQ,CAAC,IAAE,EAAE,YAAW,EAAC,YAAWH,IAAE,OAAM,CAAC,IAAEA,IAAE,kBAAkB,eAAa,EAAEA,IAAE,MAAM;AAAA,EAAG;AAAC,SAAM,MAAI;AAAC,QAAIA,MAAE,CAAC,GAAEC,KAAE,EAAC,KAAId,KAAE,WAAUkB,IAAE,YAAWC,GAAC,GAAE,EAAC,UAASH,KAAE,cAAaD,KAAE,YAAWM,IAAE,GAAGC,GAAC,IAAE1B;AAAE,WAAO,EAAE2B,IAAE,CAAC,QAAQP,MAAE,CAAC,KAAG,EAAEL,IAAE,EAAC,IAAG,UAAS,MAAK,UAAS,+BAA8B,MAAG,SAAQC,IAAE,UAASC,GAAE,UAAS,CAAC,GAAE,EAAE,EAAC,UAASC,IAAE,YAAW,EAAC,GAAGjB,IAAE,GAAGyB,GAAC,GAAE,MAAKT,KAAE,OAAMhB,IAAE,OAAMC,IAAE,MAAK,YAAW,CAAC,GAAE,QAAQkB,MAAE,CAAC,KAAG,EAAEL,IAAE,EAAC,IAAG,UAAS,MAAK,UAAS,+BAA8B,MAAG,SAAQC,IAAE,UAASC,GAAE,UAAS,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC,GAAE,EAAC,UAASZ,GAAC,CAAC;AAAE,SAAS,EAAEL,IAAE;AAAC,MAAIC,KAAEO,GAAER,GAAE,MAAM,CAAC;AAAE,SAAO4B,GAAE,CAAC5B,EAAC,GAAE,CAAC,CAACE,EAAC,GAAE,CAACC,EAAC,MAAI;AAAC,IAAAA,OAAI,QAAID,OAAI,QAAG,EAAE,MAAI;AAAC,MAAAD,GAAE,MAAM,OAAO,CAAC;AAAA,IAAC,CAAC,IAAEE,OAAI,SAAID,OAAI,SAAKD,GAAE,QAAMD,GAAE,MAAM;AAAA,EAAE,GAAE,EAAC,OAAM,OAAM,CAAC,GAAE,MAAI;AAAC,QAAIE;AAAE,YAAOA,KAAED,GAAE,MAAM,KAAK,CAAAE,OAAGA,MAAG,QAAMA,GAAE,WAAW,MAAI,OAAKD,KAAE;AAAA,EAAI;AAAC;AAAC,SAAS,EAAE,EAAC,eAAcF,GAAC,GAAEC,IAAE;AAAC,MAAIC,KAAE,EAAED,EAAC;AAAE,IAAE,MAAI;AAAC,IAAA4B,GAAE,MAAI;AAAC,UAAI1B,IAAEC;AAAE,MAAAH,GAAE,WAASE,KAAEH,GAAE,UAAQ,OAAK,SAAOG,GAAE,qBAAmBC,MAAEJ,GAAE,UAAQ,OAAK,SAAOI,IAAE,SAAO,EAAEF,GAAE,CAAC;AAAA,IAAC,GAAE,EAAC,OAAM,OAAM,CAAC;AAAA,EAAC,CAAC,GAAES,GAAE,MAAI;AAAC,IAAAV,GAAE,SAAO,EAAEC,GAAE,CAAC;AAAA,EAAC,CAAC;AAAC;AAAC,SAASW,GAAE,EAAC,eAAcb,IAAE,WAAUC,IAAE,cAAaC,GAAC,GAAEC,IAAE;AAAC,MAAIC,MAAEI,GAAE,IAAI,GAAEC,MAAED,GAAE,KAAE;AAAE,SAAO,EAAE,MAAIC,IAAE,QAAM,IAAE,GAAEE,GAAE,MAAIF,IAAE,QAAM,KAAE,GAAE,EAAE,MAAI;AAAC,IAAAmB,GAAE,CAAC3B,IAAEC,IAAEC,EAAC,GAAE,CAACG,IAAEM,QAAI;AAAC,UAAGN,GAAE,MAAM,CAACU,IAAEK,SAAKT,OAAG,OAAK,SAAOA,IAAES,GAAC,OAAKL,EAAC,KAAG,CAACb,GAAE,MAAM;AAAO,UAAIY,KAAEX,GAAEH,EAAC;AAAE,MAAAc,MAAG,EAAE,MAAI;AAAC,YAAIO,IAAEC;AAAE,YAAG,CAACd,IAAE,MAAM;AAAO,YAAIO,KAAEZ,GAAEF,EAAC,GAAEmB,OAAGC,KAAEtB,GAAE,UAAQ,OAAK,SAAOsB,GAAE;AAAc,YAAGN,IAAE;AAAC,cAAGA,OAAIK,KAAE;AAAC,YAAAjB,IAAE,QAAMiB;AAAE;AAAA,UAAM;AAAA,QAAC,WAASN,GAAE,SAASM,GAAC,GAAE;AAAC,UAAAjB,IAAE,QAAMiB;AAAE;AAAA,QAAM;AAAC,QAAAL,KAAE,EAAEA,EAAC,IAAE,EAAED,IAAE,EAAE,QAAM,EAAE,QAAQ,MAAI,EAAE,SAAO,QAAQ,KAAK,0DAA0D,GAAEX,IAAE,SAAOmB,KAAEvB,GAAE,UAAQ,OAAK,SAAOuB,GAAE;AAAA,MAAa,CAAC;AAAA,IAAC,GAAE,EAAC,WAAU,MAAG,OAAM,OAAM,CAAC;AAAA,EAAC,CAAC,GAAEnB;AAAC;AAAC,SAASU,GAAE,EAAC,eAAcd,IAAE,WAAUC,IAAE,YAAWC,IAAE,uBAAsBC,GAAC,GAAEC,KAAE;AAAC,MAAIK;AAAE,EAAAD,IAAGC,MAAET,GAAE,UAAQ,OAAK,SAAOS,IAAE,aAAY,SAAQ,CAAAH,OAAG;AAAC,QAAG,CAACF,IAAE,MAAM;AAAO,QAAIQ,MAAE,EAAEV,EAAC;AAAE,IAAAE,GAAEH,EAAC,aAAY,eAAaW,IAAE,IAAIR,GAAEH,EAAC,CAAC;AAAE,QAAIc,KAAEZ,GAAE;AAAM,QAAG,CAACY,GAAE;AAAO,QAAIC,KAAEV,GAAE;AAAO,IAAAU,MAAGA,cAAa,cAAYQ,GAAEZ,KAAEI,EAAC,KAAGb,GAAE,QAAMa,IAAE,EAAEA,EAAC,MAAIV,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAES,EAAC,KAAG,EAAEZ,GAAE,KAAK;AAAA,EAAC,GAAE,IAAE;AAAC;AAAC,SAASqB,GAAExB,IAAEC,IAAE;AAAC,WAAQC,MAAKF,GAAE,KAAGE,GAAE,SAASD,EAAC,EAAE,QAAM;AAAG,SAAM;AAAE;;;AGAjuI,SAAO,YAAY6B,IAAE,SAASC,UAAM;;;ACApC,SAAO,eAAeC,IAAE,cAAcC,UAAM;AAAM,SAASC,GAAEC,IAAE;AAAC,MAAIC,KAAEH,GAAEE,GAAE,YAAY,CAAC;AAAE,SAAOH,GAAEG,GAAE,UAAU,MAAI;AAAC,IAAAC,GAAE,QAAMD,GAAE,YAAY;AAAA,EAAC,CAAC,CAAC,GAAEC;AAAC;;;ACA/I,SAASC,GAAEC,KAAEC,IAAE;AAAC,MAAIC,KAAEF,IAAE,GAAEG,KAAE,oBAAI;AAAI,SAAM,EAAC,cAAa;AAAC,WAAOD;AAAA,EAAC,GAAE,UAAUE,IAAE;AAAC,WAAOD,GAAE,IAAIC,EAAC,GAAE,MAAID,GAAE,OAAOC,EAAC;AAAA,EAAC,GAAE,SAASA,OAAKC,KAAE;AAAC,QAAIC,MAAEL,GAAEG,EAAC,EAAE,KAAKF,IAAE,GAAGG,GAAC;AAAE,IAAAC,QAAIJ,KAAEI,KAAEH,GAAE,QAAQ,CAAAI,OAAGA,GAAE,CAAC;AAAA,EAAE,EAAC;AAAC;;;ACAtL,SAASC,KAAG;AAAC,MAAIC;AAAE,SAAM,EAAC,OAAO,EAAC,KAAIC,GAAC,GAAE;AAAC,QAAIC;AAAE,QAAIC,KAAEF,GAAE;AAAgB,IAAAD,QAAIE,KAAED,GAAE,gBAAc,OAAKC,KAAE,QAAQ,aAAWC,GAAE;AAAA,EAAW,GAAE,MAAM,EAAC,KAAIF,IAAE,GAAEE,GAAC,GAAE;AAAC,QAAIC,KAAEH,GAAE,iBAAgBC,KAAEE,GAAE,cAAYA,GAAE,aAAYC,KAAEL,MAAEE;AAAE,IAAAC,GAAE,MAAMC,IAAE,gBAAe,GAAGC,EAAC,IAAI;AAAA,EAAC,EAAC;AAAC;;;ACAjJ,SAASC,KAAG;AAAC,SAAOC,GAAE,IAAE,EAAC,OAAO,EAAC,KAAIC,IAAE,GAAEC,IAAE,MAAKC,GAAC,GAAE;AAAC,aAASC,GAAEC,KAAE;AAAC,aAAOF,GAAE,WAAW,QAAQ,CAAAG,OAAGA,GAAE,CAAC,EAAE,KAAK,CAAAA,OAAGA,GAAE,SAASD,GAAC,CAAC;AAAA,IAAC;AAAC,IAAAH,GAAE,UAAU,MAAI;AAAC,UAAIK;AAAE,UAAG,OAAO,iBAAiBN,GAAE,eAAe,EAAE,mBAAiB,QAAO;AAAC,YAAID,KAAE,EAAE;AAAE,QAAAA,GAAE,MAAMC,GAAE,iBAAgB,kBAAiB,MAAM,GAAEC,GAAE,IAAI,MAAIA,GAAE,UAAU,MAAIF,GAAE,QAAQ,CAAC,CAAC;AAAA,MAAC;AAAC,UAAIK,OAAGE,MAAE,OAAO,YAAU,OAAKA,MAAE,OAAO,aAAYD,KAAE;AAAK,MAAAJ,GAAE,iBAAiBD,IAAE,SAAQ,CAAAD,OAAG;AAAC,YAAGA,GAAE,kBAAkB,YAAY,KAAG;AAAC,cAAIQ,KAAER,GAAE,OAAO,QAAQ,GAAG;AAAE,cAAG,CAACQ,GAAE;AAAO,cAAG,EAAC,MAAKC,GAAC,IAAE,IAAI,IAAID,GAAE,IAAI,GAAEE,MAAET,GAAE,cAAcQ,EAAC;AAAE,UAAAC,OAAG,CAACN,GAAEM,GAAC,MAAIJ,KAAEI;AAAA,QAAE,QAAM;AAAA,QAAC;AAAA,MAAC,GAAE,IAAE,GAAER,GAAE,iBAAiBD,IAAE,cAAa,CAAAD,OAAG;AAAC,YAAGA,GAAE,kBAAkB,YAAY,KAAGI,GAAEJ,GAAE,MAAM,GAAE;AAAC,cAAIQ,KAAER,GAAE;AAAO,iBAAKQ,GAAE,iBAAeJ,GAAEI,GAAE,aAAa,IAAG,CAAAA,KAAEA,GAAE;AAAc,UAAAN,GAAE,MAAMM,IAAE,sBAAqB,SAAS;AAAA,QAAC,MAAM,CAAAN,GAAE,MAAMF,GAAE,QAAO,eAAc,MAAM;AAAA,MAAC,CAAC,GAAEE,GAAE,iBAAiBD,IAAE,aAAY,CAAAD,OAAG;AAAC,YAAGA,GAAE,kBAAkB,aAAY;AAAC,cAAGA,GAAE,OAAO,YAAU,QAAQ;AAAO,cAAGI,GAAEJ,GAAE,MAAM,GAAE;AAAC,gBAAIQ,KAAER,GAAE;AAAO,mBAAKQ,GAAE,iBAAeA,GAAE,QAAQ,qBAAmB,MAAI,EAAEA,GAAE,eAAaA,GAAE,gBAAcA,GAAE,cAAYA,GAAE,eAAc,CAAAA,KAAEA,GAAE;AAAc,YAAAA,GAAE,QAAQ,qBAAmB,MAAIR,GAAE,eAAe;AAAA,UAAC,MAAM,CAAAA,GAAE,eAAe;AAAA,QAAC;AAAA,MAAC,GAAE,EAAC,SAAQ,MAAE,CAAC,GAAEE,GAAE,IAAI,MAAI;AAAC,YAAIM;AAAE,YAAIR,MAAGQ,KAAE,OAAO,YAAU,OAAKA,KAAE,OAAO;AAAY,QAAAH,QAAIL,MAAG,OAAO,SAAS,GAAEK,GAAC,GAAEC,MAAGA,GAAE,gBAAcA,GAAE,eAAe,EAAC,OAAM,UAAS,CAAC,GAAEA,KAAE;AAAA,MAAK,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,EAAC,IAAE,CAAC;AAAC;;;ACAx7C,SAASK,KAAG;AAAC,SAAM,EAAC,OAAO,EAAC,KAAIC,IAAE,GAAEC,IAAC,GAAE;AAAC,IAAAA,IAAE,MAAMD,GAAE,iBAAgB,YAAW,QAAQ;AAAA,EAAC,EAAC;AAAC;;;ACA0M,SAASE,GAAEC,IAAE;AAAC,MAAIC,KAAE,CAAC;AAAE,WAAQC,MAAKF,GAAE,QAAO,OAAOC,IAAEC,GAAED,EAAC,CAAC;AAAE,SAAOA;AAAC;AAAC,IAAIE,KAAEA,GAAE,MAAI,oBAAI,OAAI,EAAC,KAAKH,IAAEC,IAAE;AAAC,MAAIG;AAAE,MAAIF,MAAGE,MAAE,KAAK,IAAIJ,EAAC,MAAI,OAAKI,MAAE,EAAC,KAAIJ,IAAE,OAAM,GAAE,GAAE,EAAE,GAAE,MAAK,oBAAI,MAAG;AAAE,SAAOE,GAAE,SAAQA,GAAE,KAAK,IAAID,EAAC,GAAE,KAAK,IAAID,IAAEE,EAAC,GAAE;AAAI,GAAE,IAAIF,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAK,IAAIF,EAAC;AAAE,SAAOE,OAAIA,GAAE,SAAQA,GAAE,KAAK,OAAOD,EAAC,IAAG;AAAI,GAAE,eAAe,EAAC,KAAID,IAAE,GAAEC,IAAE,MAAKC,GAAC,GAAE;AAAC,MAAIE,MAAE,EAAC,KAAIJ,IAAE,GAAEC,IAAE,MAAKF,GAAEG,EAAC,EAAC,GAAEG,KAAE,CAACC,GAAE,GAAED,GAAE,GAAEE,GAAE,CAAC;AAAE,EAAAF,GAAE,QAAQ,CAAC,EAAC,QAAOG,GAAC,MAAIA,MAAG,OAAK,SAAOA,GAAEJ,GAAC,CAAC,GAAEC,GAAE,QAAQ,CAAC,EAAC,OAAMG,GAAC,MAAIA,MAAG,OAAK,SAAOA,GAAEJ,GAAC,CAAC;AAAC,GAAE,aAAa,EAAC,GAAEJ,GAAC,GAAE;AAAC,EAAAA,GAAE,QAAQ;AAAC,GAAE,SAAS,EAAC,KAAIA,GAAC,GAAE;AAAC,OAAK,OAAOA,EAAC;AAAC,EAAC,CAAC;AAAEG,GAAE,UAAU,MAAI;AAAC,MAAIH,KAAEG,GAAE,YAAY,GAAEF,KAAE,oBAAI;AAAI,WAAO,CAACC,EAAC,KAAIF,GAAE,CAAAC,GAAE,IAAIC,IAAEA,GAAE,gBAAgB,MAAM,QAAQ;AAAE,WAAQA,MAAKF,GAAE,OAAO,GAAE;AAAC,QAAII,MAAEH,GAAE,IAAIC,GAAE,GAAG,MAAI,UAASG,KAAEH,GAAE,UAAQ;AAAE,KAACG,MAAG,CAACD,OAAG,CAACC,MAAGD,QAAID,GAAE,SAASD,GAAE,QAAM,IAAE,mBAAiB,gBAAeA,EAAC,GAAEA,GAAE,UAAQ,KAAGC,GAAE,SAAS,YAAWD,EAAC;AAAA,EAAC;AAAC,CAAC;;;ANAt8B,SAASO,GAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,MAAEC,GAAEH,EAAC,GAAEI,KAAEC,GAAE,MAAI;AAAC,QAAIC,KAAEP,GAAE,QAAMG,IAAE,MAAM,IAAIH,GAAE,KAAK,IAAE;AAAO,WAAOO,KAAEA,GAAE,QAAM,IAAE;AAAA,EAAE,CAAC;AAAE,SAAOC,GAAE,CAACR,IAAEC,EAAC,GAAE,CAAC,CAACM,IAAEH,GAAC,GAAE,CAACK,EAAC,GAAEC,QAAI;AAAC,QAAG,CAACH,MAAG,CAACH,IAAE;AAAO,IAAAH,GAAE,SAAS,QAAOM,IAAEL,EAAC;AAAE,QAAIS,KAAE;AAAG,IAAAD,IAAE,MAAI;AAAC,MAAAC,OAAIV,GAAE,SAAS,OAAMQ,MAAG,OAAKA,KAAEF,IAAEL,EAAC,GAAES,KAAE;AAAA,IAAG,CAAC;AAAA,EAAC,GAAE,EAAC,WAAU,KAAE,CAAC,GAAEN;AAAC;;;AOA3Y,SAAO,OAAOO,IAAE,eAAeC,UAAM;AAA4C,IAAIC,MAAE,oBAAI;AAAV,IAAcC,KAAE,oBAAI;AAAI,SAASC,GAAEC,KAAEC,KAAEC,GAAE,IAAE,GAAE;AAAC,EAAAC,GAAE,CAAAC,QAAG;AAAC,QAAIC;AAAE,QAAG,CAACJ,GAAE,MAAM;AAAO,QAAIK,KAAEF,GAAEJ,GAAC;AAAE,QAAG,CAACM,GAAE;AAAO,IAAAF,IAAE,WAAU;AAAC,UAAIG;AAAE,UAAG,CAACD,GAAE;AAAO,UAAIE,MAAGD,MAAET,GAAE,IAAIQ,EAAC,MAAI,OAAKC,MAAE;AAAE,UAAGC,OAAI,IAAEV,GAAE,OAAOQ,EAAC,IAAER,GAAE,IAAIQ,IAAEE,KAAE,CAAC,GAAEA,OAAI,EAAE;AAAO,UAAIC,KAAEZ,IAAE,IAAIS,EAAC;AAAE,MAAAG,OAAIA,GAAE,aAAa,MAAI,OAAKH,GAAE,gBAAgB,aAAa,IAAEA,GAAE,aAAa,eAAcG,GAAE,aAAa,CAAC,GAAEH,GAAE,QAAMG,GAAE,OAAMZ,IAAE,OAAOS,EAAC;AAAA,IAAE,CAAC;AAAE,QAAII,MAAGL,KAAEP,GAAE,IAAIQ,EAAC,MAAI,OAAKD,KAAE;AAAE,IAAAP,GAAE,IAAIQ,IAAEI,KAAE,CAAC,GAAEA,OAAI,MAAIb,IAAE,IAAIS,IAAE,EAAC,eAAcA,GAAE,aAAa,aAAa,GAAE,OAAMA,GAAE,MAAK,CAAC,GAAEA,GAAE,aAAa,eAAc,MAAM,GAAEA,GAAE,QAAM;AAAA,EAAG,CAAC;AAAC;;;ACArmB,SAAO,KAAKK,IAAE,OAAOC,UAAM;AAA8J,SAASC,GAAE,EAAC,mBAAkBC,MAAE,CAAC,GAAE,SAAQC,KAAE,iBAAgBC,GAAC,IAAE,CAAC,GAAE;AAAC,MAAIC,KAAEC,GAAE,IAAI,GAAEC,KAAEJ,GAAEE,EAAC;AAAE,WAASG,MAAG;AAAC,QAAIC,IAAEC,IAAEC;AAAE,QAAIC,KAAE,CAAC;AAAE,aAAQC,MAAKX,IAAE,CAAAW,OAAI,SAAOA,cAAa,cAAYD,GAAE,KAAKC,EAAC,IAAE,WAAUA,MAAGA,GAAE,iBAAiB,eAAaD,GAAE,KAAKC,GAAE,KAAK;AAAG,QAAGV,OAAG,QAAMA,IAAE,MAAM,UAAQU,MAAKV,IAAE,MAAM,CAAAS,GAAE,KAAKC,EAAC;AAAE,aAAQA,OAAKJ,KAAEF,MAAG,OAAK,SAAOA,GAAE,iBAAiB,oBAAoB,MAAI,OAAKE,KAAE,CAAC,EAAE,CAAAI,OAAI,SAAS,QAAMA,OAAI,SAAS,QAAMA,cAAa,eAAaA,GAAE,OAAK,6BAA2BA,GAAE,SAASX,GAAEG,EAAC,CAAC,KAAGQ,GAAE,UAAUF,MAAGD,KAAER,GAAEG,EAAC,MAAI,OAAK,SAAOK,GAAE,YAAY,MAAI,OAAK,SAAOC,GAAE,IAAI,KAAGC,GAAE,KAAK,CAAAE,OAAGD,GAAE,SAASC,EAAC,CAAC,KAAGF,GAAE,KAAKC,EAAC;AAAG,WAAOD;AAAA,EAAC;AAAC,SAAM,EAAC,mBAAkBJ,KAAE,SAASI,IAAE;AAAC,WAAOJ,IAAE,EAAE,KAAK,CAAAC,OAAGA,GAAE,SAASG,EAAC,CAAC;AAAA,EAAC,GAAE,iBAAgBP,IAAE,eAAc;AAAC,WAAOD,MAAG,OAAK,OAAKW,GAAEL,IAAE,EAAC,UAASF,GAAE,QAAO,KAAIH,GAAC,CAAC;AAAA,EAAC,EAAC;AAAC;AAAC,SAASW,KAAG;AAAC,MAAId,MAAEI,GAAE,IAAI;AAAE,SAAM,EAAC,iBAAgBJ,KAAE,eAAc;AAAC,WAAOa,GAAEL,IAAE,EAAC,UAASF,GAAE,QAAO,KAAIN,IAAC,CAAC;AAAA,EAAC,EAAC;AAAC;;;ACA1iC,SAAO,mBAAmBe,IAAE,UAAUC,IAAE,WAAWC,UAAM;AAAkD,IAAIC,KAAE,OAAO,wBAAwB;AAAE,SAASC,MAAG;AAAC,SAAOC,GAAEF,IAAE,KAAE;AAAC;AAAC,IAAIG,KAAEC,GAAE,EAAC,MAAK,mBAAkB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,OAAM,EAAC,MAAK,SAAQ,SAAQ,MAAE,EAAC,GAAE,MAAMC,KAAE,EAAC,OAAMC,IAAE,OAAMC,GAAC,GAAE;AAAC,SAAOC,GAAER,IAAEK,IAAE,KAAK,GAAE,MAAI;AAAC,QAAG,EAAC,OAAMI,IAAE,GAAGC,GAAC,IAAEL;AAAE,WAAO,EAAE,EAAC,YAAWK,IAAE,UAAS,CAAC,GAAE,MAAK,CAAC,GAAE,OAAMJ,IAAE,OAAMC,IAAE,MAAK,kBAAiB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACAjc,SAAO,UAAUI,IAAE,aAAaC,IAAE,eAAeC,IAAE,WAAWC,IAAE,SAASC,UAAM;AAAM,IAAIC,KAAE,OAAO,cAAc;AAAE,IAAIC,OAAG,CAAAC,QAAIA,GAAEA,GAAE,MAAI,CAAC,IAAE,OAAMA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,OAAG,CAAC,CAAC;AAAE,SAASE,KAAG;AAAC,SAAOR,GAAEK,IAAE,MAAI;AAAA,EAAC,CAAC;AAAC;AAAC,SAASI,GAAE,EAAC,MAAKC,KAAE,SAAQC,IAAE,SAAQJ,IAAE,UAASK,IAAC,GAAE;AAAC,MAAIC,KAAEL,GAAE;AAAE,WAASM,MAAKC,IAAE;AAAC,IAAAH,OAAG,QAAMA,IAAE,GAAGG,EAAC,GAAEF,GAAE,GAAGE,EAAC;AAAA,EAAC;AAAC,EAAAd,GAAE,MAAI;AAAC,IAAAG,GAAEO,IAAE,CAACI,IAAEC,QAAI;AAAC,MAAAD,KAAED,GAAE,GAAEJ,KAAEH,EAAC,IAAES,QAAI,QAAIF,GAAE,GAAEJ,KAAEH,EAAC;AAAA,IAAC,GAAE,EAAC,WAAU,MAAG,OAAM,OAAM,CAAC;AAAA,EAAC,CAAC,GAAEL,GAAE,MAAI;AAAC,IAAAS,GAAE,SAAOG,GAAE,GAAEJ,KAAEH,EAAC;AAAA,EAAC,CAAC,GAAEJ,GAAEE,IAAES,EAAC;AAAC;;;ACAxa,SAAO,YAAYG,IAAE,mBAAmBC,IAAE,UAAUC,IAAE,aAAaC,IAAE,eAAe,GAAE,WAAWC,IAAE,OAAOC,IAAE,SAAS,SAAM;AAAmG,IAAIC,KAAE,OAAO,oBAAoB;AAAE,SAASC,KAAG;AAAC,MAAIC,KAAEC,GAAEH,IAAE,IAAI;AAAE,MAAGE,OAAI,KAAK,OAAM,IAAI,MAAM,gBAAgB;AAAE,SAAOA;AAAC;AAAC,SAASE,GAAE,EAAC,MAAKF,KAAEG,GAAE,CAAC,CAAC,GAAE,MAAKC,MAAE,eAAc,OAAMC,MAAE,CAAC,EAAC,IAAE,CAAC,GAAE;AAAC,MAAIC,KAAEH,GAAE,CAAC,CAAC;AAAE,WAASI,GAAEC,IAAE;AAAC,WAAOF,GAAE,MAAM,KAAKE,EAAC,GAAE,MAAI;AAAC,UAAIC,MAAEH,GAAE,MAAM,QAAQE,EAAC;AAAE,MAAAC,QAAI,MAAIH,GAAE,MAAM,OAAOG,KAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOC,GAAEZ,IAAE,EAAC,UAASS,IAAE,MAAKP,IAAE,MAAKI,KAAE,OAAMC,IAAC,CAAC,GAAEM,GAAE,MAAIL,GAAE,MAAM,SAAO,IAAEA,GAAE,MAAM,KAAK,GAAG,IAAE,MAAM;AAAC;AAAC,IAAIM,KAAEC,GAAE,EAAC,MAAK,eAAc,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,IAAG,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMb,IAAE,EAAC,OAAMI,KAAE,OAAMC,IAAC,GAAE;AAAC,MAAIG;AAAE,MAAIF,MAAGE,KAAER,GAAE,OAAK,OAAKQ,KAAE,0BAA0BC,GAAE,CAAC,IAAGF,KAAER,GAAE;AAAE,SAAOe,GAAE,MAAI,EAAEP,GAAE,SAASD,EAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,MAAKG,MAAE,eAAc,MAAKM,KAAEZ,GAAE,CAAC,CAAC,GAAE,OAAMa,MAAE,CAAC,EAAC,IAAET,IAAE,EAAC,GAAGU,GAAC,IAAEjB,IAAEkB,KAAE,EAAC,GAAG,OAAO,QAAQF,GAAC,EAAE,OAAO,CAACG,IAAE,CAACC,IAAEC,GAAC,MAAI,OAAO,OAAOF,IAAE,EAAC,CAACC,EAAC,GAAE,EAAEC,GAAC,EAAC,CAAC,GAAE,CAAC,CAAC,GAAE,IAAGf,GAAC;AAAE,WAAO,EAAE,EAAC,UAASY,IAAE,YAAWD,IAAE,MAAKF,GAAE,OAAM,OAAMX,KAAE,OAAMC,KAAE,MAAKI,IAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACAhiC,SAAO,YAAYa,IAAE,mBAAmBC,IAAE,sBAAsBC,IAAE,KAAKC,IAAE,UAAUC,KAAE,aAAaC,IAAE,eAAeC,IAAE,WAAWC,IAAE,YAAY,GAAE,OAAOC,IAAE,YAAYC,IAAE,SAASC,IAAE,eAAeC,UAAM;AAA0N,SAASC,GAAEC,IAAE;AAAC,MAAIC,KAAEC,GAAEF,EAAC;AAAE,MAAG,CAACC,IAAE;AAAC,QAAGD,OAAI,KAAK,QAAO;AAAK,UAAM,IAAI,MAAM,gEAAgEA,EAAC,EAAE;AAAA,EAAC;AAAC,MAAIG,KAAEF,GAAE,eAAe,wBAAwB;AAAE,MAAGE,GAAE,QAAOA;AAAE,MAAIC,KAAEH,GAAE,cAAc,KAAK;AAAE,SAAOG,GAAE,aAAa,MAAK,wBAAwB,GAAEH,GAAE,KAAK,YAAYG,EAAC;AAAC;AAAC,IAAMC,KAAE,oBAAI;AAAQ,SAAS,EAAEL,IAAE;AAAC,MAAIC;AAAE,UAAOA,KAAEI,GAAE,IAAIL,EAAC,MAAI,OAAKC,KAAE;AAAC;AAAC,SAASK,GAAEN,IAAEC,IAAE;AAAC,MAAIE,KAAEF,GAAE,EAAED,EAAC,CAAC;AAAE,SAAOG,MAAG,IAAEE,GAAE,OAAOL,EAAC,IAAEK,GAAE,IAAIL,IAAEG,EAAC,GAAEA;AAAC;AAAC,IAAII,KAAEC,GAAE,EAAC,MAAK,UAAS,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,EAAC,GAAE,MAAMR,IAAE,EAAC,OAAMC,IAAE,OAAME,GAAC,GAAE;AAAC,MAAIC,KAAEK,GAAE,IAAI,GAAEP,MAAEQ,GAAE,MAAIR,GAAEE,EAAC,CAAC,GAAEO,MAAEC,IAAE,GAAEC,MAAED,IAAEE,IAAE,IAAI,GAAEC,KAAEN,GAAEE,QAAI,QAAIE,OAAG,OAAKd,GAAEK,GAAE,KAAK,IAAES,IAAE,cAAc,CAAC;AAAE,EAAAE,GAAE,SAAOT,GAAES,GAAE,OAAM,CAAAC,OAAGA,KAAE,CAAC;AAAE,MAAIC,KAAER,GAAE,KAAE;AAAE,EAAAS,GAAE,MAAI;AAAC,IAAAD,GAAE,QAAM;AAAA,EAAE,CAAC,GAAEE,GAAE,MAAI;AAAC,IAAAR,OAAGE,OAAG,SAAOE,GAAE,QAAMF,IAAE,cAAc;AAAA,EAAE,CAAC;AAAE,MAAIO,KAAER,IAAES,IAAE,IAAI,GAAEC,KAAE,OAAGC,KAAEC,GAAE;AAAE,SAAOC,GAAErB,IAAE,MAAI;AAAC,QAAGkB,MAAG,CAACF,GAAE;AAAO,QAAIJ,KAAEL,GAAEP,EAAC;AAAE,IAAAY,OAAIU,GAAEN,GAAE,SAASJ,EAAC,GAAEO,EAAC,GAAED,KAAE;AAAA,EAAG,CAAC,GAAEI,GAAE,MAAI;AAAC,QAAIC,IAAEC;AAAE,QAAIZ,MAAGW,KAAEzB,IAAE,UAAQ,OAAK,SAAOyB,GAAE,eAAe,wBAAwB;AAAE,KAACX,MAAGD,GAAE,UAAQC,MAAGV,GAAES,GAAE,OAAM,CAAAc,OAAGA,KAAE,CAAC,KAAGd,GAAE,MAAM,SAAS,SAAO,MAAIa,KAAEb,GAAE,MAAM,kBAAgB,QAAMa,GAAE,YAAYb,GAAE,KAAK;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,CAACE,GAAE,SAAOF,GAAE,UAAQ,KAAK,QAAO;AAAK,QAAIC,KAAE,EAAC,KAAIZ,IAAE,0BAAyB,GAAE;AAAE,WAAO0B,GAAEC,IAAE,EAAC,IAAGhB,GAAE,MAAK,GAAE,EAAE,EAAC,UAASC,IAAE,YAAWhB,IAAE,MAAK,CAAC,GAAE,OAAMG,IAAE,OAAMF,IAAE,MAAK,SAAQ,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA1xB,IAA4xBoB,KAAE,OAAO,qBAAqB;AAAE,SAAS,IAAG;AAAC,MAAIrB,KAAEY,IAAES,IAAE,IAAI,GAAEpB,KAAEQ,GAAE,CAAC,CAAC;AAAE,WAASN,GAAEQ,KAAE;AAAC,WAAOV,GAAE,MAAM,KAAKU,GAAC,GAAEX,MAAGA,GAAE,SAASW,GAAC,GAAE,MAAIP,GAAEO,GAAC;AAAA,EAAC;AAAC,WAASP,GAAEO,KAAE;AAAC,QAAIE,MAAEZ,GAAE,MAAM,QAAQU,GAAC;AAAE,IAAAE,QAAI,MAAIZ,GAAE,MAAM,OAAOY,KAAE,CAAC,GAAEb,MAAGA,GAAE,WAAWW,GAAC;AAAA,EAAC;AAAC,MAAIT,MAAE,EAAC,UAASC,IAAE,YAAWC,IAAE,SAAQH,GAAC;AAAE,SAAM,CAACA,IAAEO,GAAE,EAAC,MAAK,iBAAgB,MAAMG,KAAE,EAAC,OAAME,IAAC,GAAE;AAAC,WAAOmB,GAAEX,IAAEnB,GAAC,GAAE,MAAI;AAAC,UAAIa;AAAE,cAAOA,KAAEF,IAAE,YAAU,OAAK,SAAOE,GAAE,KAAKF,GAAC;AAAA,IAAC;AAAA,EAAC,EAAC,CAAC,CAAC;AAAC;AAAC,IAAIC,KAAE,OAAO,oBAAoB;AAAjC,IAAmCmB,KAAEzB,GAAE,EAAC,MAAK,eAAc,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,QAAO,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMR,IAAE,EAAC,OAAMC,IAAE,OAAME,GAAC,GAAE;AAAC,MAAIC,KAAE,EAAE,EAAC,gBAAe;AAAC,WAAOJ,GAAE;AAAA,EAAM,EAAC,CAAC;AAAE,SAAOgC,GAAElB,IAAEV,EAAC,GAAE,MAAI;AAAC,QAAG,EAAC,QAAOF,KAAE,GAAGS,IAAC,IAAEX;AAAE,WAAO,EAAE,EAAC,YAAWW,KAAE,UAAS,CAAC,GAAE,MAAK,CAAC,GAAE,OAAMV,IAAE,OAAME,IAAE,MAAK,cAAa,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;AhBApkC,IAAI,MAAI,CAAA+B,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAI,MAAI,CAAC,CAAC;AAAE,IAAIC,KAAE,OAAO,eAAe;AAAE,SAASC,GAAEC,IAAE;AAAC,MAAIC,MAAEC,GAAEJ,IAAE,IAAI;AAAE,MAAGG,QAAI,MAAK;AAAC,QAAIJ,KAAE,IAAI,MAAM,IAAIG,EAAC,+CAA+C;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBH,IAAEE,EAAC,GAAEF;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,IAAIE,KAAE;AAAN,IAA6C,KAAGC,GAAE,EAAC,MAAK,UAAS,cAAa,OAAG,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,MAAK,EAAC,MAAK,CAAC,SAAQ,MAAM,GAAE,SAAQD,GAAC,GAAE,cAAa,EAAC,MAAK,QAAO,SAAQ,KAAI,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,GAAE,MAAK,EAAC,MAAK,QAAO,SAAQ,SAAQ,EAAC,GAAE,OAAM,EAAC,OAAM,CAAAH,OAAG,KAAE,GAAE,MAAMA,IAAE,EAAC,MAAKC,KAAE,OAAMJ,IAAE,OAAMQ,KAAE,QAAOC,IAAC,GAAE;AAAC,MAAIC,IAAEC;AAAE,MAAIC,MAAGF,KAAEP,GAAE,OAAK,OAAKO,KAAE,qBAAqBN,GAAE,CAAC,IAAGS,MAAEC,GAAE,KAAE;AAAE,EAAAC,GAAE,MAAI;AAAC,IAAAF,IAAE,QAAM;AAAA,EAAE,CAAC;AAAE,MAAIG,KAAE,OAAGC,KAAEC,GAAE,MAAIf,GAAE,SAAO,YAAUA,GAAE,SAAO,gBAAcA,GAAE,QAAMa,OAAIA,KAAE,MAAG,QAAQ,KAAK,iBAAiBC,EAAC,0GAA0G,IAAG,SAAS,GAAEE,KAAEL,GAAE,CAAC,GAAEM,KAAEpB,GAAG,GAAEqB,MAAEH,GAAE,MAAIf,GAAE,SAAOG,MAAGc,OAAI,QAAMA,GAAE,QAAMhB,GAAE,UAAQA,GAAE,OAAKD,GAAE,IAAI,GAAEmB,MAAER,GAAE,IAAI,GAAES,MAAEL,GAAE,MAAId,GAAGkB,GAAC,CAAC;AAAE,MAAGb,IAAE,EAAC,IAAGa,KAAE,KAAIA,IAAC,CAAC,GAAE,EAAEnB,GAAE,SAAOG,MAAGc,OAAI,MAAM,OAAM,IAAI,MAAM,uDAAuD;AAAE,MAAG,OAAOC,IAAE,SAAO,UAAU,OAAM,IAAI,MAAM,8FAA8FA,IAAE,UAAQf,KAAE,SAAOH,GAAE,IAAI,EAAE;AAAE,MAAIqB,KAAEN,GAAE,MAAIL,IAAE,SAAOQ,IAAE,QAAM,IAAE,CAAC,GAAEI,KAAEP,GAAE,MAAIM,GAAE,UAAQ,CAAC,GAAEE,MAAER,GAAE,MAAIC,GAAE,QAAM,CAAC,GAAEQ,MAAEtB,GAAEJ,IAAE,IAAI,MAAI,MAAK,CAAC2B,IAAEC,EAAC,IAAE,EAAG,GAAE,EAAC,mBAAkBC,IAAE,iBAAgBC,KAAE,cAAaC,GAAC,IAAEL,GAAG,EAAC,SAAQC,IAAE,mBAAkB,CAACV,GAAE,MAAI;AAAC,QAAIe;AAAE,YAAOA,KAAEC,GAAE,SAAS,UAAQ,OAAKD,KAAEX,IAAE;AAAA,EAAK,CAAC,CAAC,EAAC,CAAC,GAAEa,MAAGjB,GAAE,MAAIQ,IAAE,QAAM,WAAS,MAAM,GAAEU,KAAElB,GAAE,MAAIE,OAAI,QAAMA,GAAE,QAAMhB,GAAE,aAAWA,GAAE,UAAQ,KAAE,GAAEiC,MAAGnB,GAAE,MAAIS,OAAGS,GAAE,QAAM,QAAGX,GAAE,KAAK,GAAEa,MAAGpB,GAAE,MAAI;AAAC,QAAIe,IAAEM,IAAEC;AAAE,YAAOA,MAAE,MAAM,MAAMD,MAAGN,KAAEV,IAAE,UAAQ,OAAK,SAAOU,GAAE,iBAAiB,UAAU,MAAI,OAAKM,KAAE,CAAC,CAAC,EAAE,KAAK,CAAAE,OAAGA,GAAE,OAAK,2BAAyB,QAAGA,GAAE,SAASvB,GAAEa,GAAC,CAAC,KAAGU,cAAa,WAAW,MAAI,OAAKD,MAAE;AAAA,EAAI,CAAC;AAAE,EAAAjB,GAAEe,KAAGD,GAAE;AAAE,MAAIK,MAAGxB,GAAE,MAAIQ,IAAE,QAAM,OAAGD,GAAE,KAAK,GAAEkB,MAAGzB,GAAE,MAAI;AAAC,QAAIe,IAAEM,IAAEC;AAAE,YAAOA,MAAE,MAAM,MAAMD,MAAGN,KAAEV,IAAE,UAAQ,OAAK,SAAOU,GAAE,iBAAiB,0BAA0B,MAAI,OAAKM,KAAE,CAAC,CAAC,EAAE,KAAK,CAAAE,OAAGA,GAAE,SAASvB,GAAEa,GAAC,CAAC,KAAGU,cAAa,WAAW,MAAI,OAAKD,MAAE;AAAA,EAAI,CAAC;AAAE,EAAAjB,GAAEoB,KAAGD,GAAE,GAAErB,GAAG,EAAC,MAAK,UAAS,SAAQH,GAAE,MAAIM,GAAE,UAAQ,CAAC,GAAE,SAAQF,KAAE,UAAS,CAACW,IAAEM,OAAI;AAAC,QAAGA,OAAI,SAAS,QAAO,EAAEN,IAAE,EAAC,CAACxB,IAAE,GAAG,GAAE,MAAIU,GAAE,SAAO,GAAE,CAACV,IAAE,MAAM,GAAE,MAAIU,GAAE,SAAO,EAAC,CAAC;AAAA,EAAC,EAAC,CAAC;AAAE,MAAIyB,MAAGnB,GAAG,EAAC,MAAK,qBAAoB,MAAKP,GAAE,OAAK,EAAC,MAAKG,IAAE,MAAK,EAAE,EAAC,CAAC,GAAEwB,KAAE/B,GAAE,IAAI,GAAEoB,KAAE,EAAC,SAAQW,IAAE,UAAS/B,GAAE,IAAI,GAAE,aAAYU,IAAE,WAAWS,IAAE;AAAC,IAAAY,GAAE,UAAQZ,OAAIY,GAAE,QAAMZ;AAAA,EAAE,GAAE,QAAO;AAAC,IAAA7B,IAAE,SAAQ,KAAE;AAAA,EAAC,EAAC;AAAE,EAAA0C,IAAG7C,IAAEiC,EAAC;AAAE,MAAIa,MAAG7B,GAAE,MAAI,EAAE,CAACO,GAAE,SAAOC,IAAE,MAAM;AAAE,EAAAA,GAAGI,IAAE,CAACG,IAAEM,OAAI;AAAC,IAAAN,GAAE,eAAe,GAAEC,GAAE,MAAM,GAAEc,IAAG,MAAIT,MAAG,OAAK,SAAOA,GAAE,MAAM,CAAC;AAAA,EAAC,GAAEQ,GAAE;AAAE,MAAIE,MAAG/B,GAAE,MAAI,EAAEQ,IAAE,SAAOF,GAAE,UAAQ,EAAE;AAAE,EAAAD,IAAIZ,KAAEY,IAAE,UAAQ,OAAK,SAAOZ,GAAE,aAAY,WAAU,CAAAsB,OAAG;AAAC,IAAAgB,IAAG,UAAQhB,GAAE,oBAAkBA,GAAE,QAAMf,GAAG,WAASe,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEC,GAAE,MAAM;AAAA,EAAG,CAAC;AAAE,MAAIgB,MAAGhC,GAAE,MAAI,EAAEkB,GAAE,SAAOZ,GAAE,UAAQ,KAAGG,IAAE;AAAE,SAAOa,GAAGjB,KAAE2B,KAAG,CAAAjB,OAAG;AAAC,QAAIM;AAAE,WAAM,EAAC,YAAW,CAAC,IAAIA,KAAEN,GAAE,eAAa,OAAKM,KAAE,CAAC,GAAET,EAAC,EAAC;AAAA,EAAC,CAAC,GAAEqB,IAAG,CAAAlB,OAAG;AAAC,QAAGT,GAAE,UAAQ,EAAE;AAAO,QAAIe,KAAErB,GAAEI,GAAC;AAAE,QAAG,CAACiB,GAAE;AAAO,QAAIC,MAAE,IAAI,eAAe,CAAAC,OAAG;AAAC,eAAQW,MAAKX,IAAE;AAAC,YAAIY,KAAED,GAAE,OAAO,sBAAsB;AAAE,QAAAC,GAAE,MAAI,KAAGA,GAAE,MAAI,KAAGA,GAAE,UAAQ,KAAGA,GAAE,WAAS,KAAGnB,GAAE,MAAM;AAAA,MAAC;AAAA,IAAC,CAAC;AAAE,IAAAM,IAAE,QAAQD,EAAC,GAAEN,GAAE,MAAIO,IAAE,WAAW,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,MAAKP,IAAE,cAAaM,IAAE,GAAGC,IAAC,IAAErC,IAAEsC,KAAE,EAAC,GAAGzC,IAAE,KAAIsB,KAAE,IAAGV,IAAE,MAAKK,GAAE,OAAM,cAAaO,GAAE,UAAQ,IAAE,OAAG,QAAO,mBAAkBqB,GAAE,OAAM,oBAAmBD,IAAG,MAAK,GAAEQ,KAAE,EAAC,MAAK5B,GAAE,UAAQ,EAAC;AAAE,WAAO8B,GAAEzC,IAAE,EAAC,OAAM,KAAE,GAAE,MAAI,CAACyC,GAAEvC,IAAE,MAAIuC,GAAEC,IAAG,EAAC,QAAOjC,IAAE,MAAK,GAAE,MAAIgC,GAAEzC,IAAE,EAAC,OAAM,MAAE,GAAE,MAAIyC,GAAE,IAAE,EAAC,cAAaf,IAAE,YAAWT,IAAE,UAASL,GAAE,QAAM,EAAEU,IAAG,OAAM,EAAC,QAAO,GAAE,SAAS,cAAa,MAAK,GAAE,SAAS,MAAI,CAAC,GAAE,SAAS,UAAS,CAAC,IAAE,GAAE,SAAS,KAAI,GAAE,MAAImB,GAAEzB,IAAE,CAAC,GAAE,MAAI,EAAE,EAAC,UAASY,IAAE,YAAW,EAAC,GAAGD,KAAE,GAAGxC,GAAC,GAAE,MAAKoD,IAAE,OAAMpD,IAAE,OAAMQ,KAAE,SAAQgB,GAAE,UAAQ,GAAE,UAASG,GAAE,iBAAeA,GAAE,QAAO,MAAK,SAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE2B,GAAEtB,EAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAv8G,IAAy8G,KAAGzB,GAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMJ,IAAE,EAAC,OAAMC,KAAE,OAAMJ,GAAC,GAAE;AAAC,MAAIa;AAAE,MAAIL,OAAGK,MAAEV,GAAE,OAAK,OAAKU,MAAE,6BAA6BT,GAAE,CAAC,IAAGK,MAAEP,GAAE,eAAe;AAAE,WAASU,GAAEI,IAAE;AAAC,IAAAA,GAAE,WAASA,GAAE,kBAAgBA,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEP,IAAE,MAAM;AAAA,EAAE;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,GAAGO,GAAC,IAAEb;AAAE,WAAO,EAAE,EAAC,UAAS,EAAC,IAAGK,KAAE,eAAc,MAAG,SAAQI,GAAC,GAAE,YAAWI,IAAE,MAAK,EAAC,MAAKP,IAAE,YAAY,UAAQ,EAAC,GAAE,OAAML,KAAE,OAAMJ,IAAE,MAAK,gBAAe,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAr6H,IAAu6H,KAAGO,GAAE,EAAC,MAAK,kBAAiB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,cAAa,OAAG,MAAMJ,IAAE,EAAC,OAAMC,KAAE,OAAMJ,IAAE,QAAOQ,IAAC,GAAE;AAAC,MAAIQ;AAAE,MAAIP,OAAGO,KAAEb,GAAE,OAAK,OAAKa,KAAE,8BAA8BZ,GAAE,CAAC,IAAGQ,KAAEV,GAAE,gBAAgB,GAAEW,MAAEC,GAAE,IAAI;AAAE,SAAON,IAAE,EAAC,IAAGK,KAAE,KAAIA,IAAC,CAAC,GAAEE,GAAE,MAAI;AAAC,QAAGH,GAAE,SAAS,UAAQ,KAAK,OAAM,IAAI,MAAM,2FAA2F;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,GAAGK,GAAC,IAAEd,IAAEgB,KAAE,EAAC,IAAGV,KAAE,KAAII,KAAE,eAAc,KAAE;AAAE,WAAOyC,GAAEzC,IAAE,EAAC,OAAM,KAAE,GAAE,MAAIyC,GAAEvC,IAAE,MAAI,EAAE,EAAC,UAASI,IAAE,YAAW,EAAC,GAAGf,KAAE,GAAGa,GAAC,GAAE,MAAK,EAAC,MAAKL,GAAE,YAAY,UAAQ,EAAC,GAAE,OAAMR,KAAE,OAAMJ,IAAE,MAAK,iBAAgB,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAthJ,IAAwhJ,KAAGO,GAAE,EAAC,MAAK,eAAc,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMJ,IAAE,EAAC,OAAMC,KAAE,OAAMJ,IAAE,QAAOQ,IAAC,GAAE;AAAC,MAAIQ;AAAE,MAAIP,OAAGO,KAAEb,GAAE,OAAK,OAAKa,KAAE,2BAA2BZ,GAAE,CAAC,IAAGQ,KAAEV,GAAE,aAAa;AAAE,EAAAM,IAAE,EAAC,IAAGI,GAAE,UAAS,KAAIA,GAAE,SAAQ,CAAC;AAAE,WAASC,IAAEI,IAAE;AAAC,IAAAA,GAAE,gBAAgB;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,GAAGA,GAAC,IAAEd,IAAEgB,KAAE,EAAC,IAAGV,KAAE,KAAIG,GAAE,UAAS,SAAQC,IAAC;AAAE,WAAO,EAAE,EAAC,UAASM,IAAE,YAAWF,IAAE,MAAK,EAAC,MAAKL,GAAE,YAAY,UAAQ,EAAC,GAAE,OAAMR,KAAE,OAAMJ,IAAE,MAAK,cAAa,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA99J,IAAg+JwD,MAAGjD,GAAE,EAAC,MAAK,eAAc,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,KAAI,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMJ,IAAE,EAAC,OAAMC,KAAE,OAAMJ,GAAC,GAAE;AAAC,MAAIY;AAAE,MAAIJ,OAAGI,KAAET,GAAE,OAAK,OAAKS,KAAE,2BAA2BR,GAAE,CAAC,IAAGK,MAAEP,GAAE,aAAa;AAAE,SAAOa,GAAE,MAAI;AAAC,IAAAN,IAAE,WAAWD,GAAC,GAAE,GAAG,MAAIC,IAAE,WAAW,IAAI,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,GAAGI,IAAC,IAAEV;AAAE,WAAO,EAAE,EAAC,UAAS,EAAC,IAAGK,IAAC,GAAE,YAAWK,KAAE,MAAK,EAAC,MAAKJ,IAAE,YAAY,UAAQ,EAAC,GAAE,OAAML,KAAE,OAAMJ,IAAE,MAAK,cAAa,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;AiBAj5N,SAAO,YAAYyD,IAAE,mBAAmBC,IAAE,UAAUC,IAAE,aAAaC,IAAE,eAAeC,IAAE,WAAWC,IAAE,OAAOC,IAAE,eAAeC,UAAM;AAAka,IAAIC,MAAG,CAAAC,SAAIA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,MAAID,MAAG,CAAC,CAAC;AAAE,IAAIE,KAAE,OAAO,mBAAmB;AAAE,SAASC,GAAEC,IAAE;AAAC,MAAIC,KAAEC,GAAEJ,IAAE,IAAI;AAAE,MAAGG,OAAI,MAAK;AAAC,QAAIJ,MAAE,IAAI,MAAM,IAAIG,EAAC,mDAAmD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBH,KAAEE,EAAC,GAAEF;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,IAAIE,KAAE,OAAO,wBAAwB;AAAE,SAASC,KAAG;AAAC,SAAOF,GAAEC,IAAE,IAAI;AAAC;AAAC,IAAIE,KAAEC,GAAE,EAAC,MAAK,cAAa,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,aAAY,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,EAAC,GAAE,MAAMN,IAAE,EAAC,OAAMC,IAAE,OAAMJ,IAAC,GAAE;AAAC,MAAIU,MAAEC,GAAER,GAAE,cAAY,IAAE,CAAC,GAAES,KAAED,GAAE,IAAI,GAAEE,MAAEF,GAAE,IAAI,GAAEG,KAAE,EAAC,UAASH,GAAE,gCAAgCE,GAAE,CAAC,EAAE,GAAE,SAAQF,GAAE,+BAA+BE,GAAE,CAAC,EAAE,GAAE,iBAAgBH,KAAE,OAAME,IAAE,QAAOC,KAAE,mBAAkB;AAAC,IAAAH,IAAE,QAAM,EAAEA,IAAE,OAAM,EAAC,CAAC,CAAC,GAAE,GAAE,CAAC,CAAC,GAAE,EAAC,CAAC;AAAA,EAAC,GAAE,kBAAiB;AAAC,IAAAA,IAAE,UAAQ,MAAIA,IAAE,QAAM;AAAA,EAAE,GAAE,MAAMK,IAAE;AAAC,IAAAD,GAAE,gBAAgB;AAAE,QAAIE,MAAG,MAAID,KAAEA,cAAa,cAAYA,KAAEA,GAAE,iBAAiB,cAAYf,GAAEe,EAAC,IAAEf,GAAEc,GAAE,MAAM,IAAEd,GAAEc,GAAE,MAAM,GAAG;AAAE,IAAAE,MAAG,QAAMA,GAAE,MAAM;AAAA,EAAC,EAAC;AAAE,SAAOC,GAAEhB,IAAEa,EAAC,GAAEX,GAAEe,GAAE,MAAI,EAAER,IAAE,OAAM,EAAC,CAAC,CAAC,GAAEG,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,aAAYE,IAAE,GAAGC,GAAC,IAAEb,IAAEgB,KAAE,EAAC,MAAKT,IAAE,UAAQ,GAAE,OAAMI,GAAE,MAAK;AAAE,WAAO,EAAE,EAAC,YAAWE,IAAE,UAAS,CAAC,GAAE,MAAKG,IAAE,OAAMf,IAAE,OAAMJ,KAAE,MAAK,aAAY,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAnxB,IAAqxB,IAAES,GAAE,EAAC,MAAK,oBAAmB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMN,IAAE,EAAC,OAAMC,IAAE,OAAMJ,KAAE,QAAOU,IAAC,GAAE;AAAC,MAAIE,KAAEV,GAAE,kBAAkB,GAAEW,MAAEN,GAAE,GAAEO,KAAEI,GAAE,MAAIL,QAAI,OAAK,QAAGA,IAAE,UAAQD,GAAE,QAAQ,KAAK;AAAE,EAAAQ,GAAE,MAAI;AAAC,IAAAN,GAAE,SAAOX,GAAE,OAAK,SAAOS,GAAE,SAAS,QAAMT,GAAE;AAAA,EAAG,CAAC,GAAEkB,GAAE,MAAI;AAAC,IAAAP,GAAE,UAAQF,GAAE,SAAS,QAAM;AAAA,EAAK,CAAC;AAAE,MAAIG,KAAEJ,GAAE,IAAI;AAAE,EAAAD,IAAE,EAAC,IAAGK,IAAE,KAAIA,GAAC,CAAC,GAAED,GAAE,SAAOQ,GAAE,MAAI;AAAC,IAAAV,GAAE,OAAO,QAAMG,GAAE;AAAA,EAAK,CAAC;AAAE,MAAIC,KAAEN,GAAEQ,GAAE,OAAK,EAAC,IAAGf,GAAE,IAAG,MAAKC,GAAE,KAAI,EAAE,GAAEW,EAAC;AAAE,WAASI,KAAG;AAAC,QAAII;AAAE,IAAApB,GAAE,aAAWW,GAAE,SAAOF,GAAE,iBAAiB,IAAGW,MAAEvB,GAAEY,GAAE,MAAM,MAAI,QAAMW,IAAE,MAAM,KAAGX,GAAE,iBAAiB;AAAA,EAAE;AAAC,WAASY,GAAED,KAAE;AAAC,QAAIE;AAAE,QAAG,CAACtB,GAAE,SAAS,KAAGW,GAAE,MAAM,SAAOS,IAAE,KAAI;AAAA,MAAC,KAAKvB,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAM,QAAAuB,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAEX,GAAE,iBAAiB,IAAGa,KAAEzB,GAAEY,GAAE,MAAM,MAAI,QAAMa,GAAE,MAAM;AAAE;AAAA,IAAK;AAAA,QAAM,SAAOF,IAAE,KAAI;AAAA,MAAC,KAAKvB,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAM,QAAAuB,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAEX,GAAE,iBAAiB;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASc,GAAEH,KAAE;AAAC,YAAOA,IAAE,KAAI;AAAA,MAAC,KAAKvB,GAAE;AAAM,QAAAuB,IAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAII;AAAE,QAAIJ,MAAE,EAAC,MAAKX,GAAE,gBAAgB,UAAQ,EAAC,GAAE,EAAC,IAAGa,IAAE,GAAGG,IAAC,IAAEzB,IAAE0B,KAAEf,GAAE,QAAM,EAAC,KAAIC,IAAE,MAAKC,GAAE,OAAM,SAAQG,IAAE,WAAUK,GAAC,IAAE,EAAC,KAAIG,KAAEf,GAAE,SAAS,UAAQ,OAAKe,KAAEF,IAAE,KAAIV,IAAE,MAAKC,GAAE,OAAM,iBAAgBJ,GAAE,gBAAgB,UAAQ,GAAE,iBAAgBA,GAAE,gBAAgB,UAAQ,KAAGZ,GAAEY,GAAE,KAAK,IAAEA,GAAE,QAAQ,QAAM,QAAO,UAAST,GAAE,WAAS,OAAG,QAAO,SAAQgB,IAAE,WAAUK,IAAE,SAAQE,GAAC;AAAE,WAAO,EAAE,EAAC,UAASG,IAAE,YAAWD,KAAE,MAAKL,KAAE,OAAMnB,IAAE,OAAMJ,KAAE,MAAK,mBAAkB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAnpE,IAAqpE,IAAES,GAAE,EAAC,MAAK,mBAAkB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMN,IAAE,EAAC,OAAMC,IAAE,OAAMJ,KAAE,QAAOU,IAAC,GAAE;AAAC,MAAIE,KAAEV,GAAE,iBAAiB;AAAE,EAAAkB,GAAE,MAAI;AAAC,IAAAjB,GAAE,OAAK,SAAOS,GAAE,QAAQ,QAAMT,GAAE;AAAA,EAAG,CAAC,GAAEkB,GAAE,MAAI;AAAC,IAAAT,GAAE,QAAQ,QAAM;AAAA,EAAI,CAAC,GAAEF,IAAE,EAAC,IAAGE,GAAE,OAAM,KAAIA,GAAE,MAAK,CAAC,GAAEK,GAAEX,IAAEM,GAAE,OAAO;AAAE,MAAIC,MAAEE,GAAE,GAAED,KAAEI,GAAE,MAAIL,QAAI,QAAMA,IAAE,QAAMA,GAAE,UAAQA,GAAE,OAAKD,GAAE,gBAAgB,UAAQ,CAAC;AAAE,SAAM,MAAI;AAAC,QAAIc;AAAE,QAAIX,KAAE,EAAC,MAAKH,GAAE,gBAAgB,UAAQ,GAAE,OAAMA,GAAE,MAAK,GAAE,EAAC,IAAGI,IAAE,GAAGG,GAAC,IAAEhB,IAAEqB,KAAE,EAAC,KAAIE,KAAEd,GAAE,QAAQ,UAAQ,OAAKc,KAAEV,IAAE,KAAIJ,GAAE,MAAK;AAAE,WAAO,EAAE,EAAC,UAASY,IAAE,YAAWL,IAAE,MAAKJ,IAAE,OAAMX,IAAE,OAAMJ,KAAE,UAASQ,GAAE,iBAAeA,GAAE,QAAO,SAAQM,GAAE,OAAM,MAAK,kBAAiB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACA7tH,SAAO,YAAYgB,IAAE,mBAAmBC,IAAE,YAAYC,IAAE,KAAKC,IAAE,UAAUC,IAAE,YAAYC,IAAE,aAAaC,IAAE,eAAeC,IAAE,WAAWC,IAAE,OAAOC,IAAE,SAASC,IAAE,SAASC,IAAE,eAAeC,UAAM;;;ACA1L,SAAO,OAAOC,UAAM;;;ACApB,IAAIC,KAAE;AAAuH,SAASC,GAAEC,IAAE;AAAC,MAAIC,IAAEC;AAAE,MAAIC,MAAGF,KAAED,GAAE,cAAY,OAAKC,KAAE,IAAGG,KAAEJ,GAAE,UAAU,IAAE;AAAE,MAAG,EAAEI,cAAa,aAAa,QAAOD;AAAE,MAAIE,MAAE;AAAG,WAAQC,MAAKF,GAAE,iBAAiB,qCAAqC,EAAE,CAAAE,GAAE,OAAO,GAAED,MAAE;AAAG,MAAIE,KAAEF,OAAGH,MAAEE,GAAE,cAAY,OAAKF,MAAE,KAAGC;AAAE,SAAOL,GAAE,KAAKS,EAAC,MAAIA,KAAEA,GAAE,QAAQT,IAAE,EAAE,IAAGS;AAAC;AAAC,SAASC,GAAER,IAAE;AAAC,MAAIG,KAAEH,GAAE,aAAa,YAAY;AAAE,MAAG,OAAOG,MAAG,SAAS,QAAOA,GAAE,KAAK;AAAE,MAAIC,KAAEJ,GAAE,aAAa,iBAAiB;AAAE,MAAGI,IAAE;AAAC,QAAIC,MAAED,GAAE,MAAM,GAAG,EAAE,IAAI,CAAAG,OAAG;AAAC,UAAIN,KAAE,SAAS,eAAeM,EAAC;AAAE,UAAGN,IAAE;AAAC,YAAIC,MAAED,GAAE,aAAa,YAAY;AAAE,eAAO,OAAOC,OAAG,WAASA,IAAE,KAAK,IAAEH,GAAEE,EAAC,EAAE,KAAK;AAAA,MAAC;AAAC,aAAO;AAAA,IAAI,CAAC,EAAE,OAAO,OAAO;AAAE,QAAGI,IAAE,SAAO,EAAE,QAAOA,IAAE,KAAK,IAAI;AAAA,EAAC;AAAC,SAAON,GAAEC,EAAC,EAAE,KAAK;AAAC;;;ADA5oB,SAASS,GAAEC,IAAE;AAAC,MAAIC,KAAEC,GAAE,EAAE,GAAEC,KAAED,GAAE,EAAE;AAAE,SAAM,MAAI;AAAC,QAAIE,KAAEC,GAAEL,EAAC;AAAE,QAAG,CAACI,GAAE,QAAM;AAAG,QAAIE,KAAEF,GAAE;AAAU,QAAGH,GAAE,UAAQK,GAAE,QAAOH,GAAE;AAAM,QAAII,MAAEC,GAAEJ,EAAC,EAAE,KAAK,EAAE,YAAY;AAAE,WAAOH,GAAE,QAAMK,IAAEH,GAAE,QAAMI,KAAEA;AAAA,EAAC;AAAC;;;ADAu5B,SAASE,IAAGC,KAAEC,IAAE;AAAC,SAAOD,QAAIC;AAAC;AAAC,IAAI,MAAI,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAI,MAAI,CAAC,CAAC;AAAhE,IAAkEC,OAAI,CAAAD,QAAIA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAIC,OAAI,CAAC,CAAC;AAAhI,IAAkI,MAAI,CAAAD,QAAIA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAI,MAAI,CAAC,CAAC;AAAE,SAAS,GAAGF,KAAE;AAAC,wBAAsB,MAAI,sBAAsBA,GAAC,CAAC;AAAC;AAAC,IAAII,KAAE,OAAO,gBAAgB;AAAE,SAASC,GAAEL,KAAE;AAAC,MAAIC,KAAEK,GAAEF,IAAE,IAAI;AAAE,MAAGH,OAAI,MAAK;AAAC,QAAIC,KAAE,IAAI,MAAM,IAAIF,GAAC,gDAAgD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBE,IAAEG,EAAC,GAAEH;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,IAAI,KAAGM,GAAE,EAAC,MAAK,WAAU,OAAM,EAAC,qBAAoB,CAAAP,QAAG,KAAE,GAAE,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,CAAC,QAAO,QAAQ,GAAE,SAAQ,MAAID,IAAE,GAAE,YAAW,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,YAAW,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,cAAa,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,EAAC,GAAE,cAAa,OAAG,MAAMC,KAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,MAAKM,IAAC,GAAE;AAAC,MAAIC,KAAEC,GAAE,CAAC,GAAEC,KAAED,GAAE,IAAI,GAAEE,KAAEF,GAAE,IAAI,GAAEG,KAAEH,GAAE,IAAI,GAAEI,MAAEJ,GAAE,CAAC,CAAC,GAAEK,MAAEL,GAAE,EAAE,GAAEM,MAAEN,GAAE,IAAI,GAAEO,KAAEP,GAAE,CAAC;AAAE,WAASQ,IAAEC,KAAE,CAAAC,QAAGA,KAAE;AAAC,QAAIA,MAAEJ,IAAE,UAAQ,OAAKF,IAAE,MAAME,IAAE,KAAK,IAAE,MAAKK,KAAE,EAAGF,GAAEL,IAAE,MAAM,MAAM,CAAC,GAAE,CAAAQ,OAAGtB,GAAEsB,GAAE,QAAQ,MAAM,CAAC,GAAEC,MAAEH,MAAEC,GAAE,QAAQD,GAAC,IAAE;AAAK,WAAOG,QAAI,OAAKA,MAAE,OAAM,EAAC,SAAQF,IAAE,mBAAkBE,IAAC;AAAA,EAAC;AAAC,MAAIC,KAAEC,GAAE,MAAIzB,IAAE,WAAS,IAAE,CAAC,GAAE,CAAC0B,KAAEC,EAAC,IAAE,EAAEF,GAAE,MAAIzB,IAAE,UAAU,GAAE,CAAAmB,OAAGX,IAAE,qBAAoBW,EAAC,GAAEM,GAAE,MAAIzB,IAAE,YAAY,CAAC,GAAE4B,KAAEH,GAAE,MAAIC,IAAE,UAAQ,SAAO,EAAEF,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,OAAM,CAAC,IAAEE,IAAE,KAAK,GAAEG,KAAE,EAAC,cAAapB,IAAE,OAAMmB,IAAE,MAAKJ,IAAE,QAAQL,IAAEC,KAAE;AAAC,QAAG,OAAOpB,IAAE,MAAI,UAAS;AAAC,UAAIqB,KAAErB,IAAE;AAAG,cAAOmB,MAAG,OAAK,SAAOA,GAAEE,EAAC,QAAMD,OAAG,OAAK,SAAOA,IAAEC,EAAC;AAAA,IAAE;AAAC,WAAOrB,IAAE,GAAGmB,IAAEC,GAAC;AAAA,EAAC,GAAE,aAAYK,GAAE,MAAIzB,IAAE,aAAW,eAAa,UAAU,GAAE,UAASW,IAAE,WAAUC,IAAE,YAAWC,IAAE,UAASY,GAAE,MAAIzB,IAAE,QAAQ,GAAE,SAAQc,KAAE,aAAYC,KAAE,mBAAkBC,KAAE,mBAAkBC,IAAE,eAAc;AAAC,IAAAjB,IAAE,YAAUS,GAAE,UAAQ,MAAIA,GAAE,QAAM,GAAEO,IAAE,QAAM;AAAA,EAAK,GAAE,cAAa;AAAC,IAAAhB,IAAE,YAAUS,GAAE,UAAQ,MAAIA,GAAE,QAAM;AAAA,EAAE,GAAE,WAAWU,IAAEC,KAAEC,IAAE;AAAC,QAAGrB,IAAE,YAAUS,GAAE,UAAQ,EAAE;AAAO,QAAIc,MAAEL,IAAE,GAAEI,KAAEV,GAAGO,OAAIW,GAAE,WAAS,EAAC,OAAMA,GAAE,UAAS,IAAGV,IAAC,IAAE,EAAC,OAAMD,GAAC,GAAE,EAAC,cAAa,MAAII,IAAE,SAAQ,oBAAmB,MAAIA,IAAE,mBAAkB,WAAU,CAAAQ,OAAGA,GAAE,IAAG,iBAAgB,CAAAA,OAAGA,GAAE,QAAQ,SAAQ,CAAC;AAAE,IAAAhB,IAAE,QAAM,IAAGC,IAAE,QAAMM,IAAEL,GAAE,QAAMI,MAAG,OAAKA,KAAE,GAAEP,IAAE,QAAMS,IAAE;AAAA,EAAO,GAAE,OAAOJ,IAAE;AAAC,QAAGnB,IAAE,YAAUS,GAAE,UAAQ,EAAE;AAAO,QAAIY,KAAEN,IAAE,UAAQ,KAAG,IAAE;AAAE,IAAAA,IAAE,SAAOI,GAAE,YAAY;AAAE,QAAIG,MAAGN,IAAE,UAAQ,OAAKF,IAAE,MAAM,MAAME,IAAE,QAAMK,EAAC,EAAE,OAAOP,IAAE,MAAM,MAAM,GAAEE,IAAE,QAAMK,EAAC,CAAC,IAAEP,IAAE,OAAO,KAAK,CAAAkB,OAAGA,GAAE,QAAQ,UAAU,WAAWjB,IAAE,KAAK,KAAG,CAACiB,GAAE,QAAQ,QAAQ,GAAED,KAAET,KAAER,IAAE,MAAM,QAAQQ,EAAC,IAAE;AAAG,IAAAS,OAAI,MAAIA,OAAIf,IAAE,UAAQA,IAAE,QAAMe,IAAEd,GAAE,QAAM;AAAA,EAAE,GAAE,cAAa;AAAC,IAAAjB,IAAE,YAAUS,GAAE,UAAQ,KAAGM,IAAE,UAAQ,OAAKA,IAAE,QAAM;AAAA,EAAG,GAAE,eAAeI,IAAEC,KAAE;AAAC,QAAIC,KAAEH,IAAE,CAAAK,QAAG,CAAC,GAAGA,KAAE,EAAC,IAAGJ,IAAE,SAAQC,IAAC,CAAC,CAAC;AAAE,IAAAN,IAAE,QAAMO,GAAE,SAAQL,IAAE,QAAMK,GAAE;AAAA,EAAiB,GAAE,iBAAiBF,IAAE;AAAC,QAAIC,MAAEF,IAAE,CAAAG,OAAG;AAAC,UAAIE,MAAEF,GAAE,UAAU,CAAAC,OAAGA,GAAE,OAAKH,EAAC;AAAE,aAAOI,QAAI,MAAIF,GAAE,OAAOE,KAAE,CAAC,GAAEF;AAAA,IAAC,CAAC;AAAE,IAAAP,IAAE,QAAMM,IAAE,SAAQJ,IAAE,QAAMI,IAAE,mBAAkBH,GAAE,QAAM;AAAA,EAAC,GAAE,cAAcE,IAAE;AAAC,IAAAnB,IAAE,YAAU2B,GAAER,EAAC;AAAA,EAAC,GAAE,OAAOA,IAAE;AAAC,IAAAnB,IAAE,YAAU2B,GAAE,EAAEH,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE,MAAIL,IAAE,CAAC,CAAC,GAAE,MAAI;AAAC,UAAIC,MAAEa,GAAEJ,GAAE,MAAM,KAAK,EAAE,MAAM,GAAER,KAAEY,GAAEd,EAAC,GAAEI,MAAEH,IAAE,UAAU,CAAAE,OAAGO,GAAE,QAAQR,IAAEY,GAAEX,EAAC,CAAC,CAAC;AAAE,aAAOC,QAAI,KAAGH,IAAE,KAAKC,EAAC,IAAED,IAAE,OAAOG,KAAE,CAAC,GAAEH;AAAA,IAAC,EAAC,CAAC,CAAC;AAAA,EAAC,EAAC;AAAE,EAAAZ,GAAE,CAACI,IAAEC,EAAC,GAAE,CAACM,IAAEC,QAAI;AAAC,QAAIC;AAAE,IAAAQ,GAAE,aAAa,GAAE,EAAGT,KAAE,EAAG,KAAK,MAAID,GAAE,eAAe,IAAGE,KAAErB,GAAEY,EAAC,MAAI,QAAMS,GAAE,MAAM;AAAA,EAAE,GAAEI,GAAE,MAAIhB,GAAE,UAAQ,CAAC,CAAC,GAAEyB,GAAE9B,IAAEyB,EAAC,GAAEV,GAAGM,GAAE,MAAI,EAAEhB,GAAE,OAAM,EAAC,CAAC,CAAC,GAAEW,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,CAAC,CAAC;AAAE,MAAIe,KAAEV,GAAE,MAAI;AAAC,QAAIN;AAAE,YAAOA,KAAEnB,GAAEY,EAAC,MAAI,OAAK,SAAOO,GAAE,QAAQ,MAAM;AAAA,EAAC,CAAC;AAAE,SAAOiB,GAAE,MAAI;AAAC,IAAAC,GAAE,CAACF,EAAC,GAAE,MAAI;AAAC,UAAG,CAACA,GAAE,SAAOnC,IAAE,iBAAe,OAAO;AAAO,eAASmB,KAAG;AAAC,QAAAU,GAAE,cAAc7B,IAAE,YAAY;AAAA,MAAC;AAAC,aAAOmC,GAAE,MAAM,iBAAiB,SAAQhB,EAAC,GAAE,MAAI;AAAC,YAAIC;AAAE,SAACA,MAAEe,GAAE,UAAQ,QAAMf,IAAE,oBAAoB,SAAQD,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,MAAKA,IAAE,YAAWC,KAAE,UAASC,IAAE,MAAKE,KAAE,GAAGD,GAAC,IAAEtB,KAAE+B,KAAE,EAAC,MAAKtB,GAAE,UAAQ,GAAE,UAASY,IAAE,OAAMO,GAAE,MAAK;AAAE,WAAOU,GAAEC,IAAE,CAAC,GAAGpB,MAAG,QAAMS,GAAE,SAAO,OAAKjB,GAAG,EAAC,CAACQ,EAAC,GAAES,GAAE,MAAK,CAAC,EAAE,IAAI,CAAC,CAACI,IAAEQ,EAAC,MAAIF,GAAE1B,IAAGL,GAAG,EAAC,UAASW,GAAG,QAAO,KAAIc,IAAE,IAAG,SAAQ,MAAK,UAAS,QAAO,MAAG,UAAS,MAAG,MAAKT,KAAE,UAASF,IAAE,MAAKW,IAAE,OAAMQ,GAAC,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,EAAE,EAAC,UAAS,CAAC,GAAE,YAAW,EAAC,GAAGtC,IAAE,GAAGQ,GAAGY,IAAE,CAAC,gBAAe,uBAAsB,cAAa,YAAW,IAAI,CAAC,EAAC,GAAE,MAAKS,IAAE,OAAM9B,IAAE,OAAMC,IAAE,MAAK,UAAS,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAh6G,IAAk6GuC,MAAGlC,GAAE,EAAC,MAAK,gBAAe,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,QAAO,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMP,KAAE,EAAC,OAAMC,IAAE,OAAMC,GAAC,GAAE;AAAC,MAAIU;AAAE,MAAIJ,OAAGI,KAAEZ,IAAE,OAAK,OAAKY,KAAE,4BAA4BQ,GAAE,CAAC,IAAGX,KAAEJ,GAAE,cAAc;AAAE,WAASM,KAAG;AAAC,QAAIE;AAAE,KAACA,KAAEb,GAAES,GAAE,SAAS,MAAI,QAAMI,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAIA,KAAE,EAAC,MAAKJ,GAAE,aAAa,UAAQ,GAAE,UAASA,GAAE,SAAS,MAAK,GAAE,EAAC,GAAGK,IAAC,IAAEd,KAAEe,MAAE,EAAC,IAAGP,KAAE,KAAIC,GAAE,UAAS,SAAQE,GAAC;AAAE,WAAO,EAAE,EAAC,UAASI,KAAE,YAAWD,KAAE,MAAKD,IAAE,OAAMZ,IAAE,OAAMC,IAAE,MAAK,eAAc,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA14H,IAA44H,KAAGK,GAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMP,KAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOM,IAAC,GAAE;AAAC,MAAIQ;AAAE,MAAIP,MAAGO,MAAEhB,IAAE,OAAK,OAAKgB,MAAE,6BAA6BI,GAAE,CAAC,IAAGT,KAAEN,GAAE,eAAe;AAAE,EAAAG,IAAE,EAAC,IAAGG,GAAE,WAAU,KAAIA,GAAE,UAAS,CAAC;AAAE,WAASC,GAAEK,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKjB,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAU,QAAAiB,GAAE,eAAe,GAAEN,GAAE,YAAY,GAAE+B,GAAE,MAAI;AAAC,cAAIxB;AAAE,WAACA,MAAElB,GAAEW,GAAE,UAAU,MAAI,QAAMO,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAEP,GAAE,MAAM,SAAOA,GAAE,WAAWmB,GAAE,KAAK;AAAA,QAAC,CAAC;AAAE;AAAA,MAAM,KAAK9B,GAAE;AAAQ,QAAAiB,GAAE,eAAe,GAAEN,GAAE,YAAY,GAAE+B,GAAE,MAAI;AAAC,cAAIxB;AAAE,WAACA,MAAElB,GAAEW,GAAE,UAAU,MAAI,QAAMO,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAEP,GAAE,MAAM,SAAOA,GAAE,WAAWmB,GAAE,IAAI;AAAA,QAAC,CAAC;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASjB,GAAEI,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKjB,GAAE;AAAM,QAAAiB,GAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASH,IAAEG,IAAE;AAAC,IAAAN,GAAE,SAAS,UAAQA,GAAE,aAAa,UAAQ,KAAGA,GAAE,aAAa,GAAE+B,GAAE,MAAI;AAAC,UAAIxB;AAAE,cAAOA,MAAElB,GAAEW,GAAE,SAAS,MAAI,OAAK,SAAOO,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC,MAAID,GAAE,eAAe,GAAEN,GAAE,YAAY,GAAE,GAAG,MAAI;AAAC,UAAIO;AAAE,cAAOA,MAAElB,GAAEW,GAAE,UAAU,MAAI,OAAK,SAAOO,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC;AAAA,EAAG;AAAC,MAAIH,MAAED,GAAEW,GAAE,OAAK,EAAC,IAAGzB,IAAE,IAAG,MAAKC,GAAE,KAAI,EAAE,GAAEU,GAAE,SAAS;AAAE,SAAM,MAAI;AAAC,QAAIe,KAAEC;AAAE,QAAIV,KAAE,EAAC,MAAKN,GAAE,aAAa,UAAQ,GAAE,UAASA,GAAE,SAAS,OAAM,OAAMA,GAAE,MAAM,MAAK,GAAE,EAAC,GAAGO,IAAC,IAAElB,KAAEwB,KAAE,EAAC,KAAIb,GAAE,WAAU,IAAGF,IAAE,MAAKM,IAAE,OAAM,iBAAgB,WAAU,kBAAiBW,MAAE1B,GAAEW,GAAE,UAAU,MAAI,OAAK,SAAOe,IAAE,IAAG,iBAAgBf,GAAE,aAAa,UAAQ,GAAE,mBAAkBA,GAAE,SAAS,QAAM,EAAEgB,KAAE3B,GAAEW,GAAE,QAAQ,MAAI,OAAK,SAAOgB,GAAE,IAAGlB,EAAC,EAAE,KAAK,GAAG,IAAE,QAAO,UAASE,GAAE,SAAS,UAAQ,OAAG,OAAG,QAAO,WAAUC,IAAE,SAAQC,IAAE,SAAQC,IAAC;AAAE,WAAO,EAAE,EAAC,UAASU,IAAE,YAAWN,KAAE,MAAKD,IAAE,OAAMhB,IAAE,OAAMC,IAAE,MAAK,gBAAe,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAv6K,IAAy6KyC,MAAGpC,GAAE,EAAC,MAAK,kBAAiB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,KAAI,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMP,KAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOM,IAAC,GAAE;AAAC,MAAIQ;AAAE,MAAIP,MAAGO,MAAEhB,IAAE,OAAK,OAAKgB,MAAE,8BAA8BI,GAAE,CAAC,IAAGT,KAAEN,GAAE,gBAAgB,GAAEO,KAAEF,GAAE,IAAI;AAAE,EAAAF,IAAE,EAAC,IAAGG,GAAE,YAAW,KAAIA,GAAE,WAAU,CAAC;AAAE,WAASE,GAAEI,IAAE;AAAC,YAAOL,GAAE,SAAO,aAAaA,GAAE,KAAK,GAAEK,GAAE,KAAI;AAAA,MAAC,KAAKjB,GAAE;AAAM,YAAGW,GAAE,YAAY,UAAQ,GAAG,QAAOM,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,OAAOM,GAAE,GAAG;AAAA,MAAE,KAAKjB,GAAE;AAAM,YAAGiB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,kBAAkB,UAAQ,MAAK;AAAC,cAAIO,MAAEP,GAAE,QAAQ,MAAMA,GAAE,kBAAkB,KAAK;AAAE,UAAAA,GAAE,OAAOO,IAAE,QAAQ,KAAK;AAAA,QAAC;AAAC,QAAAP,GAAE,KAAK,UAAQ,MAAIA,GAAE,aAAa,GAAE+B,GAAE,MAAI;AAAC,cAAIxB;AAAE,kBAAOA,MAAElB,GAAEW,GAAE,SAAS,MAAI,OAAK,SAAOO,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAG;AAAA,MAAM,KAAK,EAAEP,GAAE,YAAY,OAAM,EAAC,UAASX,GAAE,WAAU,YAAWA,GAAE,WAAU,CAAC;AAAE,eAAOiB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,WAAWmB,GAAE,IAAI;AAAA,MAAE,KAAK,EAAEnB,GAAE,YAAY,OAAM,EAAC,UAASX,GAAE,SAAQ,YAAWA,GAAE,UAAS,CAAC;AAAE,eAAOiB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,WAAWmB,GAAE,QAAQ;AAAA,MAAE,KAAK9B,GAAE;AAAA,MAAK,KAAKA,GAAE;AAAO,eAAOiB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,WAAWmB,GAAE,KAAK;AAAA,MAAE,KAAK9B,GAAE;AAAA,MAAI,KAAKA,GAAE;AAAS,eAAOiB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,WAAWmB,GAAE,IAAI;AAAA,MAAE,KAAK9B,GAAE;AAAO,QAAAiB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,aAAa,GAAE+B,GAAE,MAAI;AAAC,cAAIxB;AAAE,kBAAOA,MAAElB,GAAEW,GAAE,SAAS,MAAI,OAAK,SAAOO,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAE;AAAA,MAAM,KAAKlB,GAAE;AAAI,QAAAiB,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAE;AAAA,MAAM;AAAQ,QAAAA,GAAE,IAAI,WAAS,MAAIN,GAAE,OAAOM,GAAE,GAAG,GAAEL,GAAE,QAAM,WAAW,MAAID,GAAE,YAAY,GAAE,GAAG;AAAG;AAAA,IAAK;AAAA,EAAC;AAAC,MAAIG,MAAEO,GAAG,GAAEN,MAAEU,GAAE,MAAIX,QAAI,QAAMA,IAAE,QAAMM,GAAE,UAAQA,GAAE,OAAKT,GAAE,aAAa,UAAQ,CAAC;AAAE,SAAM,MAAI;AAAC,QAAIe,KAAEC;AAAE,QAAIV,KAAE,EAAC,MAAKN,GAAE,aAAa,UAAQ,EAAC,GAAE,EAAC,GAAGO,IAAC,IAAElB,KAAEwB,KAAE,EAAC,yBAAwBb,GAAE,kBAAkB,UAAQ,SAAOe,MAAEf,GAAE,QAAQ,MAAMA,GAAE,kBAAkB,KAAK,MAAI,OAAK,SAAOe,IAAE,IAAG,wBAAuBf,GAAE,KAAK,UAAQ,IAAE,OAAG,QAAO,oBAAmBgB,KAAE3B,GAAEW,GAAE,SAAS,MAAI,OAAK,SAAOgB,GAAE,IAAG,oBAAmBhB,GAAE,YAAY,OAAM,IAAGF,IAAE,WAAUI,IAAE,MAAK,WAAU,UAAS,GAAE,KAAIF,GAAE,WAAU;AAAE,WAAO,EAAE,EAAC,UAASa,IAAE,YAAWN,KAAE,MAAKD,IAAE,OAAMhB,IAAE,OAAMC,IAAE,UAASoC,GAAE,iBAAeA,GAAE,QAAO,SAAQvB,IAAE,OAAM,MAAK,iBAAgB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAljP,IAAojP,KAAGR,GAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,KAAI,GAAE,OAAM,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,EAAC,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMP,KAAE,EAAC,OAAMC,IAAE,OAAMC,IAAE,QAAOM,IAAC,GAAE;AAAC,MAAI2B;AAAE,MAAI1B,MAAG0B,KAAEnC,IAAE,OAAK,OAAKmC,KAAE,6BAA6Bf,GAAE,CAAC,IAAGT,KAAEN,GAAE,eAAe,GAAEO,KAAEF,GAAE,IAAI;AAAE,EAAAF,IAAE,EAAC,IAAGI,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAIC,KAAEY,GAAE,MAAId,GAAE,kBAAkB,UAAQ,OAAKA,GAAE,QAAQ,MAAMA,GAAE,kBAAkB,KAAK,EAAE,OAAKF,KAAE,KAAE,GAAEK,MAAEW,GAAE,MAAI,EAAEd,GAAE,KAAK,OAAM,EAAC,CAAC,CAAC,GAAE,MAAIA,GAAE,QAAQsB,GAAEtB,GAAE,MAAM,KAAK,GAAEsB,GAAEjC,IAAE,KAAK,CAAC,GAAE,CAAC,CAAC,GAAE,MAAIiC,GAAEtB,GAAE,MAAM,KAAK,EAAE,KAAK,CAAAQ,OAAGR,GAAE,QAAQsB,GAAEd,EAAC,GAAEc,GAAEjC,IAAE,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAEe,MAAEU,GAAE,MAAI,EAAEd,GAAE,KAAK,OAAM,EAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAIS;AAAE,QAAID,KAAEc,GAAEtB,GAAE,MAAM,KAAK;AAAE,aAAQS,MAAET,GAAE,QAAQ,MAAM,KAAK,CAAAU,OAAGF,GAAE,KAAK,CAAAI,QAAGZ,GAAE,QAAQsB,GAAEV,GAAC,GAAEU,GAAEZ,GAAE,QAAQ,KAAK,CAAC,CAAC,CAAC,MAAI,OAAK,SAAOD,IAAE,QAAMX;AAAA,EAAC,GAAE,CAAC,CAAC,GAAE,MAAIK,IAAE,MAAK,CAAC,CAAC,GAAEE,MAAEA,GAAEJ,EAAC,GAAEK,KAAEQ,GAAE,OAAK,EAAC,UAASzB,IAAE,UAAS,OAAMA,IAAE,OAAM,IAAI,YAAW;AAAC,WAAOgB,IAAE;AAAA,EAAC,GAAE,QAAOJ,GAAC,EAAE;AAAE,EAAAwB,GAAE,MAAIzB,GAAE,eAAeF,IAAEQ,EAAC,CAAC,GAAE2B,GAAE,MAAIjC,GAAE,iBAAiBF,EAAC,CAAC,GAAE2B,GAAE,MAAI;AAAC,IAAAC,GAAE,CAAC1B,GAAE,cAAaG,GAAC,GAAE,MAAI;AAAC,MAAAH,GAAE,aAAa,UAAQ,KAAGG,IAAE,SAAO,EAAEH,GAAE,KAAK,OAAM,EAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAAI,IAAE,SAAOJ,GAAE,WAAWmB,GAAE,UAASrB,EAAC;AAAA,MAAC,GAAE,CAAC,CAAC,GAAE,MAAI;AAAC,QAAAE,GAAE,WAAWmB,GAAE,UAASrB,EAAC;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAEoC,GAAE,MAAI;AAAC,IAAAlC,GAAE,aAAa,UAAQ,KAAGE,GAAE,SAAOF,GAAE,kBAAkB,UAAQ,KAAG+B,GAAE,MAAI;AAAC,UAAIvB,IAAEC;AAAE,cAAOA,OAAGD,KAAEnB,GAAEY,EAAC,MAAI,OAAK,SAAOO,GAAE,mBAAiB,OAAK,SAAOC,IAAE,KAAKD,IAAE,EAAC,OAAM,UAAS,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAE,WAASD,IAAEC,IAAE;AAAC,QAAGnB,IAAE,SAAS,QAAOmB,GAAE,eAAe;AAAE,IAAAR,GAAE,OAAOX,IAAE,KAAK,GAAEW,GAAE,KAAK,UAAQ,MAAIA,GAAE,aAAa,GAAE+B,GAAE,MAAI;AAAC,UAAItB;AAAE,cAAOA,MAAEpB,GAAEW,GAAE,SAAS,MAAI,OAAK,SAAOS,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAC,WAASI,KAAG;AAAC,QAAGxB,IAAE,SAAS,QAAOW,GAAE,WAAWmB,GAAE,OAAO;AAAE,IAAAnB,GAAE,WAAWmB,GAAE,UAASrB,EAAC;AAAA,EAAC;AAAC,MAAIiB,MAAER,GAAG;AAAE,WAASS,GAAER,IAAE;AAAC,IAAAO,IAAE,OAAOP,EAAC;AAAA,EAAC;AAAC,WAASS,GAAET,IAAE;AAAC,IAAAO,IAAE,SAASP,EAAC,MAAInB,IAAE,YAAUa,GAAE,SAAOF,GAAE,WAAWmB,GAAE,UAASrB,IAAE,CAAC;AAAA,EAAE;AAAC,WAASoB,GAAEV,IAAE;AAAC,IAAAO,IAAE,SAASP,EAAC,MAAInB,IAAE,YAAUa,GAAE,SAAOF,GAAE,WAAWmB,GAAE,OAAO;AAAA,EAAE;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,UAASX,GAAC,IAAEnB,KAAEoB,MAAE,EAAC,QAAOP,GAAE,OAAM,UAASC,IAAE,OAAM,UAASK,GAAC,GAAE,EAAC,OAAME,IAAE,UAASE,KAAE,GAAGD,GAAC,IAAEtB,KAAE+B,KAAE,EAAC,IAAGtB,IAAE,KAAIG,IAAE,MAAK,UAAS,UAASO,OAAI,OAAG,SAAO,IAAG,iBAAgBA,OAAI,OAAG,OAAG,QAAO,iBAAgBL,IAAE,OAAM,UAAS,QAAO,SAAQI,KAAE,SAAQM,IAAE,gBAAeG,IAAE,cAAaA,IAAE,eAAcC,IAAE,aAAYA,IAAE,gBAAeC,IAAE,cAAaA,GAAC;AAAE,WAAO,EAAE,EAAC,UAASE,IAAE,YAAWT,IAAE,MAAKF,KAAE,OAAMlB,IAAE,OAAMD,IAAE,MAAK,gBAAe,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;AGA72W,SAAO,YAAY6C,IAAE,mBAAmBC,IAAE,UAAUC,IAAE,YAAYC,IAAE,aAAaC,IAAE,eAAeC,IAAE,WAAWC,IAAE,OAAOC,IAAE,eAAeC,UAAM;AAAk6B,IAAI,KAAG,CAAAC,SAAIA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,MAAI,KAAG,CAAC,CAAC;AAA9D,IAAgEC,OAAI,CAAAD,SAAIA,IAAEA,IAAE,UAAQ,CAAC,IAAE,WAAUA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,MAAIC,OAAI,CAAC,CAAC;AAAE,SAASC,IAAGC,KAAE;AAAC,wBAAsB,MAAI,sBAAsBA,GAAC,CAAC;AAAC;AAAC,IAAIC,KAAE,OAAO,aAAa;AAAE,SAASC,GAAEF,KAAE;AAAC,MAAIG,KAAEC,GAAEH,IAAE,IAAI;AAAE,MAAGE,OAAI,MAAK;AAAC,QAAIN,MAAE,IAAI,MAAM,IAAIG,GAAC,6CAA6C;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBH,KAAEK,EAAC,GAAEL;AAAA,EAAC;AAAC,SAAOM;AAAC;AAAC,IAAI,KAAGE,GAAE,EAAC,MAAK,QAAO,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,EAAC,GAAE,MAAML,KAAE,EAAC,OAAMG,IAAE,OAAMN,IAAC,GAAE;AAAC,MAAIS,KAAEC,GAAE,CAAC,GAAEC,MAAED,GAAE,IAAI,GAAEE,KAAEF,GAAE,IAAI,GAAEG,KAAEH,GAAE,CAAC,CAAC,GAAEI,KAAEJ,GAAE,EAAE,GAAEK,MAAEL,GAAE,IAAI,GAAEM,KAAEN,GAAE,CAAC;AAAE,WAASO,GAAEC,KAAE,CAAAC,OAAGA,IAAE;AAAC,QAAIA,KAAEJ,IAAE,UAAQ,OAAKF,GAAE,MAAME,IAAE,KAAK,IAAE,MAAKK,KAAE,EAAEF,GAAEL,GAAE,MAAM,MAAM,CAAC,GAAE,CAAAQ,OAAGlB,GAAEkB,GAAE,QAAQ,MAAM,CAAC,GAAEC,MAAEH,KAAEC,GAAE,QAAQD,EAAC,IAAE;AAAK,WAAOG,QAAI,OAAKA,MAAE,OAAM,EAAC,OAAMF,IAAE,iBAAgBE,IAAC;AAAA,EAAC;AAAC,MAAIC,KAAE,EAAC,WAAUd,IAAE,WAAUE,KAAE,UAASC,IAAE,OAAMC,IAAE,aAAYC,IAAE,iBAAgBC,KAAE,mBAAkBC,IAAE,WAAU,MAAI;AAAC,IAAAP,GAAE,QAAM,GAAEM,IAAE,QAAM;AAAA,EAAI,GAAE,UAAS,MAAIN,GAAE,QAAM,GAAE,SAASS,IAAEC,IAAEC,IAAE;AAAC,QAAIE,MAAEL,GAAE,GAAEI,KAAEP,GAAEI,OAAIM,GAAE,WAAS,EAAC,OAAMA,GAAE,UAAS,IAAGL,GAAC,IAAE,EAAC,OAAMD,GAAC,GAAE,EAAC,cAAa,MAAII,IAAE,OAAM,oBAAmB,MAAIA,IAAE,iBAAgB,WAAU,CAAAG,QAAGA,IAAE,IAAG,iBAAgB,CAAAA,QAAGA,IAAE,QAAQ,SAAQ,CAAC;AAAE,IAAAX,GAAE,QAAM,IAAGC,IAAE,QAAMM,IAAEL,GAAE,QAAMI,MAAG,OAAKA,KAAE,GAAEP,GAAE,QAAMS,IAAE;AAAA,EAAK,GAAE,OAAOJ,IAAE;AAAC,QAAIE,KAAEN,GAAE,UAAQ,KAAG,IAAE;AAAE,IAAAA,GAAE,SAAOI,GAAE,YAAY;AAAE,QAAIG,MAAGN,IAAE,UAAQ,OAAKF,GAAE,MAAM,MAAME,IAAE,QAAMK,EAAC,EAAE,OAAOP,GAAE,MAAM,MAAM,GAAEE,IAAE,QAAMK,EAAC,CAAC,IAAEP,GAAE,OAAO,KAAK,CAAAa,OAAGA,GAAE,QAAQ,UAAU,WAAWZ,GAAE,KAAK,KAAG,CAACY,GAAE,QAAQ,QAAQ,GAAED,MAAEJ,KAAER,GAAE,MAAM,QAAQQ,EAAC,IAAE;AAAG,IAAAI,QAAI,MAAIA,QAAIV,IAAE,UAAQA,IAAE,QAAMU,KAAET,GAAE,QAAM;AAAA,EAAE,GAAE,cAAa;AAAC,IAAAF,GAAE,QAAM;AAAA,EAAE,GAAE,aAAaI,IAAEC,IAAE;AAAC,QAAIC,KAAEH,GAAE,CAAAK,QAAG,CAAC,GAAGA,KAAE,EAAC,IAAGJ,IAAE,SAAQC,GAAC,CAAC,CAAC;AAAE,IAAAN,GAAE,QAAMO,GAAE,OAAML,IAAE,QAAMK,GAAE,iBAAgBJ,GAAE,QAAM;AAAA,EAAC,GAAE,eAAeE,IAAE;AAAC,QAAIC,KAAEF,GAAE,CAAAG,OAAG;AAAC,UAAIE,MAAEF,GAAE,UAAU,CAAAC,OAAGA,GAAE,OAAKH,EAAC;AAAE,aAAOI,QAAI,MAAIF,GAAE,OAAOE,KAAE,CAAC,GAAEF;AAAA,IAAC,CAAC;AAAE,IAAAP,GAAE,QAAMM,GAAE,OAAMJ,IAAE,QAAMI,GAAE,iBAAgBH,GAAE,QAAM;AAAA,EAAC,EAAC;AAAE,SAAOW,GAAE,CAAChB,KAAEC,EAAC,GAAE,CAACM,IAAEC,OAAI;AAAC,QAAIC;AAAE,IAAAG,GAAE,UAAU,GAAE,EAAEJ,IAAE,EAAE,KAAK,MAAID,GAAE,eAAe,IAAGE,KAAEjB,GAAEQ,GAAC,MAAI,QAAMS,GAAE,MAAM;AAAA,EAAE,GAAEQ,GAAE,MAAInB,GAAE,UAAQ,CAAC,CAAC,GAAEoB,GAAEzB,IAAEmB,EAAC,GAAEL,GAAEU,GAAE,MAAI,EAAEnB,GAAE,OAAM,EAAC,CAAC,CAAC,GAAET,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAIkB,KAAE,EAAC,MAAKT,GAAE,UAAQ,GAAE,OAAMc,GAAE,UAAS;AAAE,WAAO,EAAE,EAAC,UAAS,CAAC,GAAE,YAAWpB,KAAE,MAAKe,IAAE,OAAMZ,IAAE,OAAMN,KAAE,MAAK,OAAM,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA9mD,IAAgnD,KAAGQ,GAAE,EAAC,MAAK,cAAa,OAAM,EAAC,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAML,KAAE,EAAC,OAAMG,IAAE,OAAMN,KAAE,QAAOS,GAAC,GAAE;AAAC,MAAIQ;AAAE,MAAIN,OAAGM,KAAEd,IAAE,OAAK,OAAKc,KAAE,0BAA0BjB,GAAE,CAAC,IAAGY,KAAEP,GAAE,YAAY;AAAE,EAAAI,GAAE,EAAC,IAAGG,GAAE,WAAU,KAAIA,GAAE,UAAS,CAAC;AAAE,WAASC,GAAEU,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKpB,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAU,QAAAoB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEX,GAAE,SAAS,GAAEkB,GAAE,MAAI;AAAC,cAAIZ;AAAE,WAACA,KAAEf,GAAES,GAAE,QAAQ,MAAI,QAAMM,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAEN,GAAE,SAASY,GAAE,KAAK;AAAA,QAAC,CAAC;AAAE;AAAA,MAAM,KAAKrB,GAAE;AAAQ,QAAAoB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEX,GAAE,SAAS,GAAEkB,GAAE,MAAI;AAAC,cAAIZ;AAAE,WAACA,KAAEf,GAAES,GAAE,QAAQ,MAAI,QAAMM,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAEN,GAAE,SAASY,GAAE,IAAI;AAAA,QAAC,CAAC;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASV,GAAES,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKpB,GAAE;AAAM,QAAAoB,GAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASR,IAAEQ,IAAE;AAAC,IAAApB,IAAE,aAAWS,GAAE,UAAU,UAAQ,KAAGA,GAAE,UAAU,GAAEkB,GAAE,MAAI;AAAC,UAAIZ;AAAE,cAAOA,KAAEf,GAAES,GAAE,SAAS,MAAI,OAAK,SAAOM,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC,MAAIK,GAAE,eAAe,GAAEX,GAAE,SAAS,GAAEV,IAAG,MAAI;AAAC,UAAIgB;AAAE,cAAOA,KAAEf,GAAES,GAAE,QAAQ,MAAI,OAAK,SAAOM,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC;AAAA,EAAG;AAAC,MAAIF,KAAEM,GAAEM,GAAE,OAAK,EAAC,IAAGzB,IAAE,IAAG,MAAKG,GAAE,KAAI,EAAE,GAAEM,GAAE,SAAS;AAAE,SAAM,MAAI;AAAC,QAAIQ;AAAE,QAAIG,KAAE,EAAC,MAAKX,GAAE,UAAU,UAAQ,EAAC,GAAE,EAAC,GAAGM,GAAC,IAAEf,KAAEgB,KAAE,EAAC,KAAIP,GAAE,WAAU,IAAGD,KAAE,MAAKK,GAAE,OAAM,iBAAgB,QAAO,kBAAiBI,KAAEjB,GAAES,GAAE,QAAQ,MAAI,OAAK,SAAOQ,GAAE,IAAG,iBAAgBR,GAAE,UAAU,UAAQ,GAAE,WAAUC,IAAE,SAAQC,IAAE,SAAQC,IAAC;AAAE,WAAO,EAAE,EAAC,UAASI,IAAE,YAAWD,IAAE,MAAKK,IAAE,OAAMjB,IAAE,OAAMN,KAAE,MAAK,aAAY,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA78F,IAA+8F,KAAGQ,GAAE,EAAC,MAAK,aAAY,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAML,KAAE,EAAC,OAAMG,IAAE,OAAMN,KAAE,QAAOS,GAAC,GAAE;AAAC,MAAIc;AAAE,MAAIZ,OAAGY,KAAEpB,IAAE,OAAK,OAAKoB,KAAE,yBAAyBvB,GAAE,CAAC,IAAGY,KAAEP,GAAE,WAAW,GAAEQ,KAAEH,GAAE,IAAI;AAAE,EAAAD,GAAE,EAAC,IAAGG,GAAE,UAAS,KAAIA,GAAE,SAAQ,CAAC,GAAEZ,GAAE,EAAC,WAAU4B,GAAE,MAAIzB,GAAES,GAAE,QAAQ,CAAC,GAAE,SAAQgB,GAAE,MAAIhB,GAAE,UAAU,UAAQ,CAAC,GAAE,OAAOM,IAAE;AAAC,WAAOA,GAAE,aAAa,MAAM,MAAI,aAAW,WAAW,gBAAcA,GAAE,aAAa,MAAM,IAAE,WAAW,cAAY,WAAW;AAAA,EAAa,GAAE,KAAKA,IAAE;AAAC,IAAAA,GAAE,aAAa,QAAO,MAAM;AAAA,EAAC,EAAC,CAAC;AAAE,WAASJ,GAAEI,IAAE;AAAC,QAAIC;AAAE,YAAON,GAAE,SAAO,aAAaA,GAAE,KAAK,GAAEK,GAAE,KAAI;AAAA,MAAC,KAAKf,GAAE;AAAM,YAAGS,GAAE,YAAY,UAAQ,GAAG,QAAOM,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,OAAOM,GAAE,GAAG;AAAA,MAAE,KAAKf,GAAE;AAAM,YAAGe,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,gBAAgB,UAAQ,MAAK;AAAC,cAAIU,MAAEV,GAAE,MAAM,MAAMA,GAAE,gBAAgB,KAAK;AAAE,WAACO,KAAEhB,GAAEmB,IAAE,QAAQ,MAAM,MAAI,QAAMH,GAAE,MAAM;AAAA,QAAC;AAAC,QAAAP,GAAE,UAAU,GAAE,EAAET,GAAES,GAAE,SAAS,CAAC;AAAE;AAAA,MAAM,KAAKT,GAAE;AAAU,eAAOe,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,SAASY,GAAE,IAAI;AAAA,MAAE,KAAKrB,GAAE;AAAQ,eAAOe,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,SAASY,GAAE,QAAQ;AAAA,MAAE,KAAKrB,GAAE;AAAA,MAAK,KAAKA,GAAE;AAAO,eAAOe,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,SAASY,GAAE,KAAK;AAAA,MAAE,KAAKrB,GAAE;AAAA,MAAI,KAAKA,GAAE;AAAS,eAAOe,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,SAASY,GAAE,IAAI;AAAA,MAAE,KAAKrB,GAAE;AAAO,QAAAe,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,UAAU,GAAEkB,GAAE,MAAI;AAAC,cAAIV;AAAE,kBAAOA,KAAEjB,GAAES,GAAE,SAAS,MAAI,OAAK,SAAOQ,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAE;AAAA,MAAM,KAAKjB,GAAE;AAAI,QAAAe,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEN,GAAE,UAAU,GAAEkB,GAAE,MAAI,EAAE3B,GAAES,GAAE,SAAS,GAAEM,GAAE,WAAS,EAAE,WAAS,EAAE,IAAI,CAAC;AAAE;AAAA,MAAM;AAAQ,QAAAA,GAAE,IAAI,WAAS,MAAIN,GAAE,OAAOM,GAAE,GAAG,GAAEL,GAAE,QAAM,WAAW,MAAID,GAAE,YAAY,GAAE,GAAG;AAAG;AAAA,IAAK;AAAA,EAAC;AAAC,WAASG,IAAEG,IAAE;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKf,GAAE;AAAM,QAAAe,GAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,MAAIF,KAAEO,GAAE,GAAEN,KAAEW,GAAE,MAAIZ,OAAI,QAAMA,GAAE,QAAMhB,GAAE,UAAQA,GAAE,OAAKY,GAAE,UAAU,UAAQ,CAAC;AAAE,SAAM,MAAI;AAAC,QAAIU,KAAED;AAAE,QAAIH,KAAE,EAAC,MAAKN,GAAE,UAAU,UAAQ,EAAC,GAAE,EAAC,GAAGO,GAAC,IAAEhB,KAAEiB,KAAE,EAAC,yBAAwBR,GAAE,gBAAgB,UAAQ,SAAOU,MAAEV,GAAE,MAAM,MAAMA,GAAE,gBAAgB,KAAK,MAAI,OAAK,SAAOU,IAAE,IAAG,oBAAmBD,KAAElB,GAAES,GAAE,SAAS,MAAI,OAAK,SAAOS,GAAE,IAAG,IAAGV,KAAE,WAAUG,IAAE,SAAQC,KAAE,MAAK,QAAO,UAAS,GAAE,KAAIH,GAAE,SAAQ;AAAE,WAAO,EAAE,EAAC,UAASQ,IAAE,YAAWD,IAAE,MAAKD,IAAE,OAAMZ,IAAE,OAAMN,KAAE,UAAS+B,GAAE,iBAAeA,GAAE,QAAO,SAAQd,GAAE,OAAM,MAAK,YAAW,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAxqK,IAA0qKe,MAAGxB,GAAE,EAAC,MAAK,YAAW,cAAa,OAAG,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAML,KAAE,EAAC,OAAMG,IAAE,OAAMN,KAAE,QAAOS,GAAC,GAAE;AAAC,MAAIY;AAAE,MAAIV,OAAGU,KAAElB,IAAE,OAAK,OAAKkB,KAAE,wBAAwBrB,GAAE,CAAC,IAAGY,KAAEP,GAAE,UAAU,GAAEQ,KAAEH,GAAE,IAAI;AAAE,EAAAD,GAAE,EAAC,IAAGI,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAIC,KAAEc,GAAE,MAAIhB,GAAE,gBAAgB,UAAQ,OAAKA,GAAE,MAAM,MAAMA,GAAE,gBAAgB,KAAK,EAAE,OAAKD,MAAE,KAAE,GAAEI,MAAEJ,GAAEE,EAAC,GAAEG,KAAEY,GAAE,OAAK,EAAC,UAASzB,IAAE,UAAS,IAAI,YAAW;AAAC,WAAOY,IAAE;AAAA,EAAC,GAAE,QAAOF,GAAC,EAAE;AAAE,EAAAkB,GAAE,MAAInB,GAAE,aAAaD,KAAEK,EAAC,CAAC,GAAEiB,GAAE,MAAIrB,GAAE,eAAeD,GAAC,CAAC,GAAEuB,GAAE,MAAI;AAAC,IAAAtB,GAAE,UAAU,UAAQ,KAAGE,GAAE,SAAOF,GAAE,kBAAkB,UAAQ,KAAGkB,GAAE,MAAI;AAAC,UAAIL,KAAEC;AAAE,cAAOA,MAAGD,MAAEtB,GAAEU,EAAC,MAAI,OAAK,SAAOY,IAAE,mBAAiB,OAAK,SAAOC,GAAE,KAAKD,KAAE,EAAC,OAAM,UAAS,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAE,WAASR,GAAEQ,KAAE;AAAC,QAAGtB,IAAE,SAAS,QAAOsB,IAAE,eAAe;AAAE,IAAAb,GAAE,UAAU,GAAE,EAAET,GAAES,GAAE,SAAS,CAAC;AAAA,EAAC;AAAC,WAASW,KAAG;AAAC,QAAGpB,IAAE,SAAS,QAAOS,GAAE,SAASY,GAAE,OAAO;AAAE,IAAAZ,GAAE,SAASY,GAAE,UAASb,GAAC;AAAA,EAAC;AAAC,MAAIO,KAAEO,GAAE;AAAE,WAASN,GAAEM,KAAE;AAAC,IAAAP,GAAE,OAAOO,GAAC;AAAA,EAAC;AAAC,WAASL,GAAEK,KAAE;AAAC,IAAAP,GAAE,SAASO,GAAC,MAAItB,IAAE,YAAUW,GAAE,SAAOF,GAAE,SAASY,GAAE,UAASb,KAAE,CAAC;AAAA,EAAE;AAAC,WAASW,IAAEG,KAAE;AAAC,IAAAP,GAAE,SAASO,GAAC,MAAItB,IAAE,YAAUW,GAAE,SAAOF,GAAE,SAASY,GAAE,OAAO;AAAA,EAAE;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,UAASC,KAAE,GAAGC,GAAC,IAAEvB,KAAEgC,KAAE,EAAC,QAAOrB,GAAE,OAAM,UAASW,KAAE,OAAMb,GAAE,UAAS;AAAE,WAAO,EAAE,EAAC,UAAS,EAAC,IAAGD,KAAE,KAAIE,IAAE,MAAK,YAAW,UAASY,QAAI,OAAG,SAAO,IAAG,iBAAgBA,QAAI,OAAG,OAAG,QAAO,SAAQR,IAAE,SAAQM,IAAE,gBAAeJ,IAAE,cAAaA,IAAE,eAAcC,IAAE,aAAYA,IAAE,gBAAeE,KAAE,cAAaA,IAAC,GAAE,YAAW,EAAC,GAAGtB,KAAE,GAAG0B,GAAC,GAAE,MAAKS,IAAE,OAAMnC,KAAE,OAAMM,IAAE,MAAK,WAAU,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACA//P,SAAO,YAAY8B,IAAE,mBAAmBC,IAAE,YAAYC,IAAE,KAAKC,IAAE,UAAUC,IAAE,aAAaC,KAAG,eAAeC,KAAG,WAAWC,IAAE,OAAOC,IAAE,cAAcC,KAAG,eAAeC,UAAM;AAA6lC,IAAIC,OAAI,CAAAC,SAAIA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,MAAID,OAAI,CAAC,CAAC;AAAE,IAAI,KAAG,OAAO,gBAAgB;AAAE,SAASE,GAAEC,KAAE;AAAC,MAAIC,KAAEC,GAAE,IAAG,IAAI;AAAE,MAAGD,OAAI,MAAK;AAAC,QAAIH,MAAE,IAAI,MAAM,IAAIE,GAAC,4BAA4B,GAAG,IAAI,gBAAgB;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBF,KAAEC,EAAC,GAAED;AAAA,EAAC;AAAC,SAAOG;AAAC;AAAC,IAAI,KAAG,OAAO,qBAAqB;AAAE,SAAS,KAAI;AAAC,SAAOC,GAAE,IAAG,IAAI;AAAC;AAAC,IAAIC,MAAG,OAAO,qBAAqB;AAAE,SAASC,MAAI;AAAC,SAAOF,GAAEC,KAAG,IAAI;AAAC;AAAC,IAAI,KAAGE,GAAE,EAAC,MAAK,WAAU,cAAa,OAAG,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,EAAC,GAAE,MAAML,KAAE,EAAC,OAAMC,IAAE,OAAMH,KAAE,QAAOQ,GAAC,GAAE;AAAC,MAAIC;AAAE,MAAIC,KAAEC,GAAE,IAAI;AAAE,EAAAH,GAAE,EAAC,IAAGE,IAAE,KAAIA,GAAC,CAAC;AAAE,MAAIE,KAAED,GAAE,CAAC,GAAEE,MAAEF,GAAE,IAAI,GAAEG,MAAEH,GAAE,IAAI,GAAEI,KAAEJ,GAAE,IAAI,GAAEK,MAAEL,GAAE,IAAI,GAAEM,KAAEC,GAAE,MAAIC,GAAET,EAAC,CAAC,GAAEU,MAAEF,GAAE,MAAI;AAAC,QAAIG,IAAEC;AAAE,QAAG,CAACT,GAAEA,GAAC,KAAG,CAACA,GAAEG,GAAC,EAAE,QAAM;AAAG,aAAQO,MAAK,SAAS,iBAAiB,UAAU,EAAE,KAAG,OAAOA,MAAG,OAAK,SAAOA,GAAE,SAASV,GAAEA,GAAC,CAAC,CAAC,IAAE,OAAOU,MAAG,OAAK,SAAOA,GAAE,SAASV,GAAEG,GAAC,CAAC,CAAC,EAAE,QAAM;AAAG,QAAIQ,KAAE,EAAE,GAAEC,KAAED,GAAE,QAAQX,GAAEA,GAAC,CAAC,GAAEa,MAAGD,KAAED,GAAE,SAAO,KAAGA,GAAE,QAAOG,MAAGF,KAAE,KAAGD,GAAE,QAAOI,KAAEJ,GAAEE,EAAC,GAAEG,KAAEL,GAAEG,EAAC;AAAE,WAAM,GAAGN,KAAER,GAAEG,GAAC,MAAI,QAAMK,GAAE,SAASO,EAAC,MAAI,GAAGN,KAAET,GAAEG,GAAC,MAAI,QAAMM,GAAE,SAASO,EAAC;AAAA,EAAE,CAAC,GAAEC,KAAE,EAAC,cAAalB,IAAE,UAASD,GAAE,IAAI,GAAE,SAAQA,GAAE,IAAI,GAAE,OAAMK,KAAE,QAAOH,KAAE,aAAYO,KAAE,qBAAoBN,KAAE,oBAAmBC,IAAE,gBAAe;AAAC,IAAAH,GAAE,QAAM,EAAEA,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE,GAAE,CAAC,CAAC,GAAE,EAAC,CAAC;AAAA,EAAC,GAAE,eAAc;AAAC,IAAAA,GAAE,UAAQ,MAAIA,GAAE,QAAM;AAAA,EAAE,GAAE,MAAMY,IAAE;AAAC,IAAAM,GAAE,aAAa;AAAE,QAAIL,MAAG,MAAID,KAAEA,cAAa,cAAYA,KAAEA,GAAE,iBAAiB,cAAYX,GAAEW,EAAC,IAAEX,GAAEiB,GAAE,MAAM,IAAEjB,GAAEiB,GAAE,MAAM,GAAG;AAAE,IAAAL,MAAG,QAAMA,GAAE,MAAM;AAAA,EAAC,EAAC;AAAE,EAAAM,GAAE,IAAGD,EAAC,GAAElB,GAAGM,GAAE,MAAI,EAAEN,GAAE,OAAM,EAAC,CAAC,CAAC,GAAEO,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,CAAC,CAAC;AAAE,MAAIa,KAAE,EAAC,UAASF,GAAE,UAAS,SAAQA,GAAE,SAAQ,QAAO;AAAC,IAAAA,GAAE,aAAa;AAAA,EAAC,EAAC,GAAEG,KAAE,GAAG,GAAEC,KAAED,MAAG,OAAK,SAAOA,GAAE,iBAAgB,CAACE,IAAEC,GAAC,IAAE,EAAG,GAAEjB,MAAEkB,GAAG,EAAC,iBAAgBJ,MAAG,OAAK,SAAOA,GAAE,iBAAgB,SAAQE,IAAE,mBAAkB,CAACtB,KAAEG,GAAC,EAAC,CAAC;AAAE,WAASsB,MAAG;AAAC,QAAId,IAAEC,IAAEC,IAAEC;AAAE,YAAOA,KAAEM,MAAG,OAAK,SAAOA,GAAE,0BAA0B,MAAI,OAAKN,OAAIH,KAAEP,GAAE,UAAQ,OAAK,SAAOO,GAAE,qBAAmBC,KAAEZ,GAAEA,GAAC,MAAI,OAAK,SAAOY,GAAE,SAASR,GAAE,MAAM,aAAa,QAAMS,KAAEb,GAAEG,GAAC,MAAI,OAAK,SAAOU,GAAE,SAAST,GAAE,MAAM,aAAa;AAAA,EAAG;AAAC,SAAOsB,GAAE,MAAIL,MAAG,OAAK,SAAOA,GAAEF,EAAC,CAAC,GAAEZ,IAAIX,MAAEQ,GAAE,UAAQ,OAAK,SAAOR,IAAE,aAAY,SAAQ,CAAAe,OAAG;AAAC,QAAIC,IAAEC;AAAE,IAAAF,GAAE,WAAS,UAAQA,GAAE,kBAAkB,eAAaZ,GAAE,UAAQ,MAAI0B,IAAE,KAAGzB,OAAGG,QAAIG,IAAE,SAASK,GAAE,MAAM,MAAIC,KAAEZ,GAAEiB,GAAE,mBAAmB,MAAI,QAAML,GAAE,SAASD,GAAE,MAAM,MAAIE,KAAEb,GAAEiB,GAAE,kBAAkB,MAAI,QAAMJ,GAAE,SAASF,GAAE,MAAM,KAAGM,GAAE,aAAa;AAAA,EAAG,GAAE,IAAE,GAAEM,GAAGjB,IAAE,mBAAkB,CAACK,IAAEC,OAAI;AAAC,QAAIC;AAAE,IAAAI,GAAE,aAAa,GAAE,EAAGL,IAAE,EAAG,KAAK,MAAID,GAAE,eAAe,IAAGE,KAAEb,GAAEA,GAAC,MAAI,QAAMa,GAAE,MAAM;AAAA,EAAE,GAAER,GAAE,MAAIN,GAAE,UAAQ,CAAC,CAAC,GAAE,MAAI;AAAC,QAAIY,KAAE,EAAC,MAAKZ,GAAE,UAAQ,GAAE,OAAMkB,GAAE,MAAK;AAAE,WAAOU,GAAEC,IAAE,CAACD,GAAEJ,KAAE,CAAC,GAAE,MAAI,EAAE,EAAC,YAAW,EAAC,GAAGlC,KAAE,GAAGF,IAAC,GAAE,UAAS,EAAC,KAAIU,GAAC,GAAE,MAAKc,IAAE,OAAMrB,IAAE,OAAMH,KAAE,MAAK,UAAS,CAAC,CAAC,GAAEwC,GAAErB,IAAE,YAAY,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA1jE,IAA4jEuB,MAAGnC,GAAE,EAAC,MAAK,iBAAgB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,cAAa,OAAG,MAAML,KAAE,EAAC,OAAMC,IAAE,OAAMH,KAAE,QAAOQ,GAAC,GAAE;AAAC,MAAIC;AAAE,MAAIC,MAAGD,MAAEP,IAAE,OAAK,OAAKO,MAAE,6BAA6BU,GAAE,CAAC,IAAGP,KAAEX,GAAE,eAAe,GAAEY,MAAEK,GAAE,MAAIC,GAAEP,GAAE,MAAM,CAAC;AAAE,EAAAJ,GAAE,EAAC,IAAGI,GAAE,QAAO,KAAIA,GAAE,OAAM,CAAC,GAAE+B,IAAG,MAAI;AAAC,IAAA/B,GAAE,SAAS,QAAMF;AAAA,EAAC,CAAC,GAAEkC,IAAG,MAAI;AAAC,IAAAhC,GAAE,SAAS,QAAM;AAAA,EAAI,CAAC;AAAE,MAAIE,MAAE,GAAG,GAAEC,KAAED,OAAG,OAAK,SAAOA,IAAE,aAAYE,MAAEV,IAAG,GAAEW,KAAEC,GAAE,MAAIF,QAAI,OAAK,QAAGA,IAAE,UAAQJ,GAAE,QAAQ,KAAK,GAAEQ,MAAET,GAAE,IAAI,GAAEmB,KAAE,6BAA6BX,GAAE,CAAC;AAAG,EAAAF,GAAE,SAAOsB,GAAE,MAAI;AAAC,IAAA3B,GAAE,OAAO,QAAMC,GAAEO,GAAC;AAAA,EAAC,CAAC;AAAE,MAAIY,KAAEhC,GAAGkB,GAAE,OAAK,EAAC,IAAGhB,IAAE,IAAG,MAAKC,GAAE,KAAI,EAAE,GAAEiB,GAAC;AAAE,WAASa,GAAET,IAAE;AAAC,QAAIC,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,QAAGZ,GAAE,OAAM;AAAC,UAAGL,GAAE,aAAa,UAAQ,EAAE;AAAO,cAAOY,GAAE,KAAI;AAAA,QAAC,KAAKX,GAAE;AAAA,QAAM,KAAKA,GAAE;AAAM,UAAAW,GAAE,eAAe,IAAGE,MAAGD,KAAED,GAAE,QAAQ,UAAQ,QAAME,GAAE,KAAKD,EAAC,GAAEb,GAAE,aAAa,IAAGe,KAAEd,GAAED,GAAE,MAAM,MAAI,QAAMe,GAAE,MAAM;AAAE;AAAA,MAAK;AAAA,IAAC,MAAM,SAAOH,GAAE,KAAI;AAAA,MAAC,KAAKX,GAAE;AAAA,MAAM,KAAKA,GAAE;AAAM,QAAAW,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEZ,GAAE,aAAa,UAAQ,MAAIG,MAAG,QAAMA,GAAEH,GAAE,SAAS,KAAK,IAAGA,GAAE,cAAc;AAAE;AAAA,MAAM,KAAKC,GAAE;AAAO,YAAGD,GAAE,aAAa,UAAQ,EAAE,QAAOG,MAAG,OAAK,SAAOA,GAAEH,GAAE,SAAS,KAAK;AAAE,YAAG,CAACC,GAAED,GAAE,MAAM,MAAIgB,KAAEf,IAAE,UAAQ,QAAMe,GAAE,iBAAe,GAAGC,KAAEhB,GAAED,GAAE,MAAM,MAAI,QAAMiB,GAAE,SAAShB,IAAE,MAAM,aAAa,GAAG;AAAO,QAAAW,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEZ,GAAE,aAAa;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASsB,GAAEV,IAAE;AAAC,IAAAP,GAAE,SAAOO,GAAE,QAAMX,GAAE,SAAOW,GAAE,eAAe;AAAA,EAAC;AAAC,WAASW,GAAEX,IAAE;AAAC,QAAIC,IAAEC;AAAE,IAAAxB,IAAE,aAAWe,GAAE,SAAOL,GAAE,aAAa,IAAGa,KAAEZ,GAAED,GAAE,MAAM,MAAI,QAAMa,GAAE,MAAM,MAAID,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEZ,GAAE,aAAa,UAAQ,MAAIG,MAAG,QAAMA,GAAEH,GAAE,SAAS,KAAK,IAAGA,GAAE,cAAc,IAAGc,KAAEb,GAAED,GAAE,MAAM,MAAI,QAAMc,GAAE,MAAM;AAAA,EAAG;AAAC,WAASU,IAAEZ,IAAE;AAAC,IAAAA,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAA,EAAC;AAAC,MAAIL,MAAE0B,GAAG;AAAE,WAASP,MAAG;AAAC,QAAId,KAAEX,GAAED,GAAE,KAAK;AAAE,QAAG,CAACY,GAAE;AAAO,aAASC,KAAG;AAAC,QAAEN,IAAE,OAAM,EAAC,CAACjB,GAAE,QAAQ,GAAE,MAAI,EAAEsB,IAAE,EAAE,KAAK,GAAE,CAACtB,GAAE,SAAS,GAAE,MAAI,EAAEsB,IAAE,EAAE,IAAI,EAAC,CAAC,MAAI,EAAE,SAAO,EAAE,EAAE,EAAE,OAAO,CAAAG,OAAGA,GAAE,QAAQ,yBAAuB,MAAM,GAAE,EAAER,IAAE,OAAM,EAAC,CAACjB,GAAE,QAAQ,GAAE,EAAE,MAAK,CAACA,GAAE,SAAS,GAAE,EAAE,SAAQ,CAAC,GAAE,EAAC,YAAWW,GAAED,GAAE,MAAM,EAAC,CAAC;AAAA,IAAC;AAAC,IAAAa,GAAE;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAID,KAAEZ,GAAE,aAAa,UAAQ,GAAEa,KAAE,EAAC,MAAKD,GAAC,GAAE,EAAC,GAAGE,GAAC,IAAExB,KAAEyB,KAAEV,GAAE,QAAM,EAAC,KAAIG,KAAE,MAAKY,GAAE,OAAM,WAAUC,IAAE,SAAQE,GAAC,IAAE,EAAC,KAAIf,KAAE,IAAGV,IAAE,MAAKsB,GAAE,OAAM,iBAAgBpB,GAAE,aAAa,UAAQ,GAAE,iBAAgBC,GAAED,GAAE,KAAK,IAAEA,GAAE,QAAQ,QAAM,QAAO,UAASV,IAAE,WAAS,OAAG,QAAO,WAAU+B,IAAE,SAAQC,IAAE,SAAQC,IAAE,aAAYC,IAAC;AAAE,WAAOI,GAAEC,IAAE,CAAC,EAAE,EAAC,UAASd,IAAE,YAAW,EAAC,GAAGxB,IAAE,GAAGuB,GAAC,GAAE,MAAKD,IAAE,OAAMtB,IAAE,OAAMH,KAAE,MAAK,gBAAe,CAAC,GAAEwB,MAAG,CAACP,GAAE,SAAOL,GAAE,YAAY,SAAO4B,GAAE9B,IAAE,EAAC,IAAGoB,IAAE,UAASrB,GAAE,WAAU,+BAA8B,MAAG,IAAG,UAAS,MAAK,UAAS,SAAQ6B,IAAC,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAz+I,IAA2+I,KAAG/B,GAAE,EAAC,MAAK,kBAAiB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,EAAC,GAAE,MAAML,KAAE,EAAC,OAAMC,IAAE,OAAMH,IAAC,GAAE;AAAC,MAAIQ,KAAEP,GAAE,gBAAgB,GAAES,KAAE,8BAA8BS,GAAE,CAAC,IAAGP,KAAEc,GAAG,GAAEb,MAAEK,GAAE,MAAIN,OAAI,QAAMA,GAAE,QAAMO,GAAE,UAAQA,GAAE,OAAKX,GAAE,aAAa,UAAQ,CAAC;AAAE,WAASM,MAAG;AAAC,IAAAN,GAAE,aAAa;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAIO,KAAE,EAAC,MAAKP,GAAE,aAAa,UAAQ,EAAC;AAAE,WAAO,EAAE,EAAC,UAAS,EAAC,IAAGE,IAAE,eAAc,MAAG,SAAQI,IAAC,GAAE,YAAWZ,KAAE,MAAKa,IAAE,OAAMZ,IAAE,OAAMH,KAAE,UAASqC,GAAE,iBAAeA,GAAE,QAAO,SAAQxB,IAAE,OAAM,MAAK,iBAAgB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAthK,IAAwhKiC,MAAGvC,GAAE,EAAC,MAAK,gBAAe,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,OAAM,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,cAAa,OAAG,MAAML,KAAE,EAAC,OAAMC,IAAE,OAAMH,KAAE,QAAOQ,GAAC,GAAE;AAAC,MAAI4B;AAAE,MAAI1B,MAAG0B,MAAElC,IAAE,OAAK,OAAKkC,MAAE,4BAA4BjB,GAAE,CAAC,IAAG,EAAC,OAAMP,GAAC,IAAEV,KAAEW,MAAEZ,GAAE,cAAc,GAAEa,MAAEI,GAAE,MAAIC,GAAEN,IAAE,KAAK,CAAC,GAAEE,KAAE,oCAAoCI,GAAE,CAAC,IAAGH,MAAE,mCAAmCG,GAAE,CAAC;AAAG,EAAAX,GAAE,EAAC,IAAGK,IAAE,OAAM,KAAIA,IAAE,MAAK,CAAC,GAAE8B,IAAG,MAAI;AAAC,IAAA9B,IAAE,QAAQ,QAAMH;AAAA,EAAC,CAAC,GAAEkC,IAAG,MAAI;AAAC,IAAA/B,IAAE,QAAQ,QAAM;AAAA,EAAI,CAAC,GAAEkB,GAAE1B,KAAGQ,IAAE,OAAO,GAAE0B,GAAE,MAAI;AAAC,QAAID,KAAE7B;AAAE,QAAG,CAACG,MAAGC,IAAE,aAAa,UAAQ,KAAG,CAACA,IAAE,MAAM;AAAO,QAAIM,OAAGmB,MAAExB,IAAE,UAAQ,OAAK,SAAOwB,IAAE;AAAc,KAAC7B,MAAEI,GAAEA,IAAE,KAAK,MAAI,QAAMJ,IAAE,SAASU,GAAC,KAAG,EAAEN,GAAEA,IAAE,KAAK,GAAE,EAAE,KAAK;AAAA,EAAC,CAAC;AAAE,MAAII,KAAES,GAAG,GAAEN,MAAEF,GAAE,MAAID,OAAI,QAAMA,GAAE,QAAME,GAAE,UAAQA,GAAE,OAAKN,IAAE,aAAa,UAAQ,CAAC;AAAE,WAASiB,GAAEX,KAAE;AAAC,QAAImB,KAAE7B;AAAE,YAAOU,IAAE,KAAI;AAAA,MAAC,KAAKN,GAAE;AAAO,YAAGA,IAAE,aAAa,UAAQ,KAAG,CAACA,GAAEA,IAAE,KAAK,KAAGC,IAAE,SAAO,GAAGwB,MAAEzB,GAAEA,IAAE,KAAK,MAAI,QAAMyB,IAAE,SAASxB,IAAE,MAAM,aAAa,GAAG;AAAO,QAAAK,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAEN,IAAE,aAAa,IAAGJ,MAAEI,GAAEA,IAAE,MAAM,MAAI,QAAMJ,IAAE,MAAM;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,WAASuB,GAAEb,KAAE;AAAC,QAAIV,KAAEe,IAAEC,IAAEC,IAAEC;AAAE,QAAIW,MAAEnB,IAAE;AAAc,IAAAmB,OAAGzB,GAAEA,IAAE,KAAK,OAAKJ,MAAEI,GAAEA,IAAE,KAAK,MAAI,QAAMJ,IAAE,SAAS6B,GAAC,MAAIzB,IAAE,aAAa,KAAIY,MAAGD,KAAEX,GAAEA,IAAE,mBAAmB,MAAI,OAAK,SAAOW,GAAE,aAAW,QAAMC,GAAE,KAAKD,IAAEc,GAAC,MAAIX,MAAGD,KAAEb,GAAEA,IAAE,kBAAkB,MAAI,OAAK,SAAOa,GAAE,aAAW,QAAMC,GAAE,KAAKD,IAAEY,GAAC,MAAIA,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAG;AAAC,MAAIL,KAAEY,GAAG;AAAE,WAASX,KAAG;AAAC,QAAIf,MAAEN,GAAEA,IAAE,KAAK;AAAE,QAAG,CAACM,IAAE;AAAO,aAASmB,MAAG;AAAC,QAAEL,GAAE,OAAM,EAAC,CAAC/B,GAAE,QAAQ,GAAE,MAAI;AAAC,YAAIsB;AAAE,UAAEL,KAAE,EAAE,KAAK,MAAI,EAAE,WAASK,KAAEX,GAAEA,IAAE,kBAAkB,MAAI,QAAMW,GAAE,MAAM;AAAA,MAAE,GAAE,CAACtB,GAAE,SAAS,GAAE,MAAI;AAAC,YAAIO;AAAE,SAACA,MAAEI,GAAEA,IAAE,MAAM,MAAI,QAAMJ,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC;AAAC,IAAA6B,IAAE;AAAA,EAAC;AAAC,WAASH,KAAG;AAAC,QAAIhB,MAAEN,GAAEA,IAAE,KAAK;AAAE,QAAG,CAACM,IAAE;AAAO,aAASmB,MAAG;AAAC,QAAEL,GAAE,OAAM,EAAC,CAAC/B,GAAE,QAAQ,GAAE,MAAI;AAAC,YAAIO,MAAEI,GAAEA,IAAE,MAAM,GAAEW,KAAEX,GAAEA,IAAE,KAAK;AAAE,YAAG,CAACJ,IAAE;AAAO,YAAIgB,KAAE,EAAE,GAAEC,KAAED,GAAE,QAAQhB,GAAC,GAAEkB,KAAEF,GAAE,MAAM,GAAEC,KAAE,CAAC,GAAEG,KAAE,CAAC,GAAGJ,GAAE,MAAMC,KAAE,CAAC,GAAE,GAAGC,EAAC;AAAE,iBAAQN,MAAKQ,GAAE,MAAM,EAAE,KAAGR,GAAE,QAAQ,yBAAuB,UAAQG,MAAG,QAAMA,GAAE,SAASH,EAAC,GAAE;AAAC,cAAIC,KAAEO,GAAE,QAAQR,EAAC;AAAE,UAAAC,OAAI,MAAIO,GAAE,OAAOP,IAAE,CAAC;AAAA,QAAC;AAAC,UAAEO,IAAE,EAAE,OAAM,EAAC,QAAO,MAAE,CAAC;AAAA,MAAC,GAAE,CAAC3B,GAAE,SAAS,GAAE,MAAI;AAAC,YAAIsB;AAAE,UAAEL,KAAE,EAAE,QAAQ,MAAI,EAAE,WAASK,KAAEX,GAAEA,IAAE,MAAM,MAAI,QAAMW,GAAE,MAAM;AAAA,MAAE,EAAC,CAAC;AAAA,IAAC;AAAC,IAAAc,IAAE;AAAA,EAAC;AAAC,SAAM,MAAI;AAAC,QAAInB,MAAE,EAAC,MAAKN,IAAE,aAAa,UAAQ,GAAE,OAAMA,IAAE,MAAK,GAAE,EAAC,OAAMyB,KAAE,GAAG7B,IAAC,IAAEP,KAAEsB,KAAE,EAAC,KAAIX,IAAE,OAAM,IAAGH,IAAE,WAAUoB,IAAE,YAAWlB,MAAGC,IAAE,aAAa,UAAQ,IAAEmB,KAAE,QAAO,UAAS,GAAE;AAAE,WAAO,EAAE,EAAC,UAASR,IAAE,YAAW,EAAC,GAAGrB,IAAE,GAAGM,IAAC,GAAE,OAAMN,IAAE,MAAKgB,KAAE,OAAM,EAAC,GAAGnB,KAAE,SAAQ,IAAIyB,OAAI;AAAC,UAAIC;AAAE,aAAM,CAACc,GAAEC,IAAE,CAACrB,IAAE,SAAOP,IAAE,YAAY,SAAO2B,GAAE9B,IAAE,EAAC,IAAGK,IAAE,KAAIF,IAAE,qBAAoB,UAASJ,GAAE,WAAU,+BAA8B,MAAG,IAAG,UAAS,MAAK,UAAS,SAAQyB,GAAC,CAAC,IAAGR,KAAE1B,IAAE,YAAU,OAAK,SAAO0B,GAAE,KAAK1B,KAAE,GAAGyB,EAAC,GAAEL,IAAE,SAAOP,IAAE,YAAY,SAAO2B,GAAE9B,IAAE,EAAC,IAAGM,KAAE,KAAIH,IAAE,oBAAmB,UAASJ,GAAE,WAAU,+BAA8B,MAAG,IAAG,UAAS,MAAK,UAAS,SAAQ0B,GAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IAAC,EAAC,GAAE,UAASE,GAAE,iBAAeA,GAAE,QAAO,SAAQjB,IAAE,OAAM,MAAK,eAAc,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA1wP,IAA4wP2B,MAAGxC,GAAE,EAAC,MAAK,gBAAe,cAAa,OAAG,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,EAAC,GAAE,MAAML,KAAE,EAAC,OAAMC,IAAE,OAAMH,KAAE,QAAOQ,GAAC,GAAE;AAAC,MAAIE,KAAEC,GAAE,IAAI,GAAEC,KAAEoC,IAAG,CAAC,CAAC,GAAEnC,MAAEK,GAAE,MAAIC,GAAET,EAAC,CAAC,GAAEI,MAAEC,GAAG;AAAE,EAAAP,GAAE,EAAC,IAAGE,IAAE,KAAIA,GAAC,CAAC;AAAE,WAASK,GAAEe,IAAE;AAAC,QAAIE,KAAEpB,GAAE,MAAM,QAAQkB,EAAC;AAAE,IAAAE,OAAI,MAAIpB,GAAE,MAAM,OAAOoB,IAAE,CAAC;AAAA,EAAC;AAAC,WAAShB,IAAEc,IAAE;AAAC,WAAOlB,GAAE,MAAM,KAAKkB,EAAC,GAAE,MAAI;AAAC,MAAAf,GAAEe,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAASb,KAAG;AAAC,QAAIgB;AAAE,QAAIH,KAAEjB,IAAE;AAAM,QAAG,CAACiB,GAAE,QAAM;AAAG,QAAIE,KAAEF,GAAE;AAAc,YAAOG,KAAEpB,GAAEH,EAAC,MAAI,QAAMuB,GAAE,SAASD,EAAC,IAAE,OAAGpB,GAAE,MAAM,KAAK,CAAAsB,OAAG;AAAC,UAAIC,IAAEC;AAAE,eAAQD,KAAEL,GAAE,eAAeI,GAAE,SAAS,KAAK,MAAI,OAAK,SAAOC,GAAE,SAASH,EAAC,QAAMI,MAAEN,GAAE,eAAeI,GAAE,QAAQ,KAAK,MAAI,OAAK,SAAOE,IAAE,SAASJ,EAAC;AAAA,IAAE,CAAC;AAAA,EAAC;AAAC,WAASZ,IAAEU,IAAE;AAAC,aAAQE,MAAKpB,GAAE,MAAM,CAAAoB,GAAE,SAAS,UAAQF,MAAGE,GAAE,MAAM;AAAA,EAAC;AAAC,SAAOD,GAAE,IAAG,EAAC,iBAAgBf,KAAE,mBAAkBD,IAAE,2BAA0BE,IAAE,aAAYG,KAAE,iBAAgBN,IAAE,gBAAe,CAAC,GAAE,MAAI0B,GAAEC,IAAE,CAAC,EAAE,EAAC,UAAS,EAAC,KAAI/B,GAAC,GAAE,YAAW,EAAC,GAAGR,KAAE,GAAGC,GAAC,GAAE,MAAK,CAAC,GAAE,OAAMA,IAAE,OAAMH,KAAE,MAAK,eAAc,CAAC,GAAEwC,GAAE1B,IAAE,YAAY,CAAC,CAAC;AAAC,EAAC,CAAC;;;ACAzzU,SAAO,YAAYmC,KAAE,mBAAmBC,IAAE,YAAYC,IAAE,KAAKC,IAAE,UAAUC,IAAE,aAAaC,IAAE,eAAeC,IAAE,WAAWC,IAAE,OAAOC,IAAE,SAASC,IAAE,SAASC,UAAM;;;ACA3J,SAAO,YAAYC,IAAE,mBAAmBC,IAAE,UAAUC,IAAE,aAAaC,IAAE,eAAeC,IAAE,WAAWC,IAAE,OAAOC,IAAE,SAASC,UAAM;AAAmG,IAAIC,KAAE,OAAO,cAAc;AAAE,SAASC,KAAG;AAAC,MAAIC,KAAEC,GAAEH,IAAE,IAAI;AAAE,MAAGE,OAAI,MAAK;AAAC,QAAIE,KAAE,IAAI,MAAM,gEAAgE;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBA,IAAEH,EAAC,GAAEG;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAC,SAASG,GAAE,EAAC,MAAKH,KAAE,CAAC,GAAE,MAAKE,KAAE,SAAQ,OAAME,MAAE,CAAC,EAAC,IAAE,CAAC,GAAE;AAAC,MAAIC,KAAEC,GAAE,CAAC,CAAC;AAAE,WAASC,IAAEC,IAAE;AAAC,WAAOH,GAAE,MAAM,KAAKG,EAAC,GAAE,MAAI;AAAC,UAAIC,KAAEJ,GAAE,MAAM,QAAQG,EAAC;AAAE,MAAAC,OAAI,MAAIJ,GAAE,MAAM,OAAOI,IAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOC,GAAEZ,IAAE,EAAC,UAASS,KAAE,MAAKP,IAAE,MAAKE,IAAE,OAAME,IAAC,CAAC,GAAEO,GAAE,MAAIN,GAAE,MAAM,SAAO,IAAEA,GAAE,MAAM,KAAK,GAAG,IAAE,MAAM;AAAC;AAAC,IAAIO,KAAEC,GAAE,EAAC,MAAK,SAAQ,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,QAAO,GAAE,SAAQ,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMb,IAAE,EAAC,OAAME,IAAE,OAAME,IAAC,GAAE;AAAC,MAAII;AAAE,MAAIH,MAAGG,KAAER,GAAE,OAAK,OAAKQ,KAAE,oBAAoBJ,GAAE,CAAC,IAAGG,MAAER,GAAE;AAAE,SAAOe,GAAE,MAAIC,GAAER,IAAE,SAASF,EAAC,CAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,MAAKI,KAAE,SAAQ,MAAKO,MAAE,CAAC,GAAE,OAAMC,KAAE,CAAC,EAAC,IAAEV,KAAE,EAAC,SAAQW,IAAE,GAAGC,IAAC,IAAEnB,IAAEoB,MAAE,EAAC,GAAG,OAAO,QAAQH,EAAC,EAAE,OAAO,CAACI,IAAE,CAACC,IAAEC,GAAC,MAAI,OAAO,OAAOF,IAAE,EAAC,CAACC,EAAC,GAAEE,GAAED,GAAC,EAAC,CAAC,GAAE,CAAC,CAAC,GAAE,IAAGlB,GAAC;AAAE,WAAOa,OAAI,OAAOE,IAAE,SAAQ,OAAOA,IAAE,SAAQ,OAAOD,IAAE,UAAS,EAAE,EAAC,UAASC,KAAE,YAAWD,KAAE,MAAKH,KAAE,OAAMZ,KAAE,OAAMF,IAAE,MAAKO,GAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ADA1S,SAASgB,IAAGC,IAAEC,KAAE;AAAC,SAAOD,OAAIC;AAAC;AAAC,IAAIC,KAAE,OAAO,mBAAmB;AAAE,SAASC,GAAEH,IAAE;AAAC,MAAIC,MAAEG,GAAEF,IAAE,IAAI;AAAE,MAAGD,QAAI,MAAK;AAAC,QAAII,MAAE,IAAI,MAAM,IAAIL,EAAC,mDAAmD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBK,KAAEF,EAAC,GAAEE;AAAA,EAAC;AAAC,SAAOJ;AAAC;AAAC,IAAI,KAAGK,GAAE,EAAC,MAAK,cAAa,OAAM,EAAC,qBAAoB,CAAAN,OAAG,KAAE,GAAE,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,CAAC,QAAO,QAAQ,GAAE,SAAQ,MAAID,IAAE,GAAE,YAAW,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,cAAa,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,cAAa,OAAG,MAAMC,IAAE,EAAC,MAAKC,KAAE,OAAMI,KAAE,OAAME,IAAE,QAAOC,GAAC,GAAE;AAAC,MAAIC;AAAE,MAAIC,OAAGD,KAAET,GAAE,OAAK,OAAKS,KAAE,yBAAyBE,GAAE,CAAC,IAAGC,MAAEC,GAAE,IAAI,GAAEC,KAAED,GAAE,CAAC,CAAC,GAAEE,MAAEC,GAAE,EAAC,MAAK,kBAAiB,CAAC,GAAEC,KAAEJ,GAAE,EAAC,MAAK,wBAAuB,CAAC;AAAE,EAAAL,GAAE,EAAC,IAAGI,KAAE,KAAIA,IAAC,CAAC;AAAE,MAAG,CAACM,IAAEC,EAAC,IAAE,EAAEC,IAAE,MAAIpB,GAAE,UAAU,GAAE,CAAAqB,OAAGpB,IAAE,qBAAoBoB,EAAC,GAAED,IAAE,MAAIpB,GAAE,YAAY,CAAC,GAAEsB,MAAE,EAAC,SAAQR,IAAE,OAAMI,IAAE,UAASE,IAAE,MAAIpB,GAAE,QAAQ,GAAE,aAAYoB,IAAE,MAAIN,GAAE,MAAM,KAAK,CAAAO,OAAG,CAACA,GAAE,SAAS,QAAQ,CAAC,GAAE,uBAAsBD,IAAE,MAAIN,GAAE,MAAM,KAAK,CAAAO,OAAGC,IAAE,QAAQC,GAAEF,GAAE,SAAS,KAAK,GAAEE,GAAEvB,GAAE,UAAU,CAAC,CAAC,CAAC,GAAE,QAAQqB,IAAEG,IAAE;AAAC,QAAG,OAAOxB,GAAE,MAAI,UAAS;AAAC,UAAIyB,KAAEzB,GAAE;AAAG,cAAOqB,MAAG,OAAK,SAAOA,GAAEI,EAAC,QAAMD,MAAG,OAAK,SAAOA,GAAEC,EAAC;AAAA,IAAE;AAAC,WAAOzB,GAAE,GAAGqB,IAAEG,EAAC;AAAA,EAAC,GAAE,OAAOH,IAAE;AAAC,QAAII;AAAE,QAAGzB,GAAE,YAAUsB,IAAE,QAAQC,GAAEL,GAAE,KAAK,GAAEK,GAAEF,EAAC,CAAC,EAAE,QAAM;AAAG,QAAIG,MAAGC,KAAEX,GAAE,MAAM,KAAK,CAAAH,QAAGW,IAAE,QAAQC,GAAEZ,IAAE,SAAS,KAAK,GAAEY,GAAEF,EAAC,CAAC,CAAC,MAAI,OAAK,SAAOI,GAAE;AAAS,WAAOD,MAAG,QAAMA,GAAE,WAAS,SAAIL,GAAEE,EAAC,GAAE;AAAA,EAAG,GAAE,eAAeA,IAAE;AAAC,IAAAP,GAAE,MAAM,KAAKO,EAAC,GAAEP,GAAE,QAAM,EAAEA,GAAE,OAAM,CAAAU,OAAGA,GAAE,OAAO;AAAA,EAAC,GAAE,iBAAiBH,IAAE;AAAC,QAAIG,KAAEV,GAAE,MAAM,UAAU,CAAAW,OAAGA,GAAE,OAAKJ,EAAC;AAAE,IAAAG,OAAI,MAAIV,GAAE,MAAM,OAAOU,IAAE,CAAC;AAAA,EAAC,EAAC;AAAE,EAAAE,GAAExB,IAAEoB,GAAC,GAAEX,GAAE,EAAC,WAAUS,IAAE,MAAIA,GAAER,GAAC,CAAC,GAAE,OAAOS,IAAE;AAAC,WAAOA,GAAE,aAAa,MAAM,MAAI,UAAQ,WAAW,gBAAcA,GAAE,aAAa,MAAM,IAAE,WAAW,cAAY,WAAW;AAAA,EAAa,GAAE,KAAKA,IAAE;AAAC,IAAAA,GAAE,aAAa,QAAO,MAAM;AAAA,EAAC,EAAC,CAAC;AAAE,WAASM,GAAEN,IAAE;AAAC,QAAG,CAACT,IAAE,SAAO,CAACA,IAAE,MAAM,SAASS,GAAE,MAAM,EAAE;AAAO,QAAIG,KAAEV,GAAE,MAAM,OAAO,CAAAW,OAAGA,GAAE,SAAS,aAAW,KAAE,EAAE,IAAI,CAAAA,OAAGA,GAAE,OAAO;AAAE,YAAOJ,GAAE,KAAI;AAAA,MAAC,KAAKD,GAAE;AAAM,QAAAR,GAAES,GAAE,aAAa;AAAE;AAAA,MAAM,KAAKD,GAAE;AAAA,MAAU,KAAKA,GAAE;AAAQ,YAAGC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAEG,IAAE,EAAE,WAAS,EAAE,UAAU,MAAI,EAAE,SAAQ;AAAC,cAAIb,MAAEG,GAAE,MAAM,KAAK,CAAAc,OAAG;AAAC,gBAAIC;AAAE,mBAAOD,GAAE,cAAYC,KAAElB,GAAEC,GAAC,MAAI,OAAK,SAAOiB,GAAE;AAAA,UAAc,CAAC;AAAE,UAAAlB,OAAGW,IAAE,OAAOX,IAAE,SAAS,KAAK;AAAA,QAAC;AAAC;AAAA,MAAM,KAAKS,GAAE;AAAA,MAAW,KAAKA,GAAE;AAAU,YAAGC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAEG,IAAE,EAAE,OAAK,EAAE,UAAU,MAAI,EAAE,SAAQ;AAAC,cAAIb,MAAEG,GAAE,MAAM,KAAK,CAAAc,OAAG;AAAC,gBAAIC;AAAE,mBAAOD,GAAE,cAAYC,KAAElB,GAAEiB,GAAE,OAAO,MAAI,OAAK,SAAOC,GAAE;AAAA,UAAc,CAAC;AAAE,UAAAlB,OAAGW,IAAE,OAAOX,IAAE,SAAS,KAAK;AAAA,QAAC;AAAC;AAAA,MAAM,KAAKS,GAAE;AAAM;AAAC,UAAAC,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAE,cAAII,KAAEX,GAAE,MAAM,KAAK,CAAAH,QAAG;AAAC,gBAAIiB;AAAE,mBAAOjB,IAAE,cAAYiB,KAAEjB,GAAEA,IAAE,OAAO,MAAI,OAAK,SAAOiB,GAAE;AAAA,UAAc,CAAC;AAAE,UAAAH,MAAGH,IAAE,OAAOG,GAAE,SAAS,KAAK;AAAA,QAAC;AAAC;AAAA,IAAK;AAAA,EAAC;AAAC,MAAIK,KAAEV,IAAE,MAAI;AAAC,QAAIC;AAAE,YAAOA,KAAED,GAAER,GAAC,MAAI,OAAK,SAAOS,GAAE,QAAQ,MAAM;AAAA,EAAC,CAAC;AAAE,SAAOU,GAAE,MAAI;AAAC,IAAAC,GAAE,CAACF,EAAC,GAAE,MAAI;AAAC,UAAG,CAACA,GAAE,SAAO9B,GAAE,iBAAe,OAAO;AAAO,eAASqB,KAAG;AAAC,QAAAC,IAAE,OAAOtB,GAAE,YAAY;AAAA,MAAC;AAAC,aAAO8B,GAAE,MAAM,iBAAiB,SAAQT,EAAC,GAAE,MAAI;AAAC,YAAIG;AAAE,SAACA,KAAEM,GAAE,UAAQ,QAAMN,GAAE,oBAAoB,SAAQH,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,UAASA,IAAE,MAAKG,IAAE,MAAKC,IAAE,GAAGd,IAAC,IAAEX,IAAE4B,KAAE,EAAC,KAAIhB,KAAE,IAAGF,KAAE,MAAK,cAAa,mBAAkBK,IAAE,OAAM,oBAAmBE,GAAE,OAAM,WAAUU,GAAC;AAAE,WAAOM,GAAEC,IAAE,CAAC,GAAGV,MAAG,QAAMN,GAAE,SAAO,OAAKG,GAAG,EAAC,CAACG,EAAC,GAAEN,GAAE,MAAK,CAAC,EAAE,IAAI,CAAC,CAACW,IAAEM,EAAC,MAAIF,GAAEf,IAAEF,GAAG,EAAC,UAASX,GAAE,QAAO,KAAIwB,IAAE,IAAG,SAAQ,MAAK,UAAS,QAAO,MAAG,UAAS,MAAG,MAAKJ,IAAE,UAASJ,IAAE,MAAKQ,IAAE,OAAMM,GAAC,CAAC,CAAC,CAAC,IAAE,CAAC,GAAE,EAAE,EAAC,UAASP,IAAE,YAAW,EAAC,GAAGvB,KAAE,GAAGY,GAAGN,KAAE,CAAC,cAAa,gBAAe,IAAI,CAAC,EAAC,GAAE,MAAK,CAAC,GAAE,OAAMN,KAAE,OAAME,IAAE,MAAK,aAAY,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAE,IAAI6B,OAAI,CAAA/B,SAAIA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,MAAI+B,OAAI,CAAC,CAAC;AAAE,IAAI,KAAG9B,GAAE,EAAC,MAAK,oBAAmB,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,OAAM,EAAC,MAAK,CAAC,QAAO,QAAO,QAAO,OAAO,EAAC,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAMN,IAAE,EAAC,OAAMC,KAAE,OAAMI,KAAE,QAAOE,GAAC,GAAE;AAAC,MAAII;AAAE,MAAIH,MAAGG,MAAEX,GAAE,OAAK,OAAKW,MAAE,gCAAgCA,GAAE,CAAC,IAAGD,MAAEP,GAAE,kBAAkB,GAAES,MAAEI,GAAE,EAAC,MAAK,kBAAiB,CAAC,GAAEF,KAAED,GAAE,EAAC,MAAK,wBAAuB,CAAC,GAAEE,MAAEF,GAAE,IAAI,GAAEI,KAAEG,IAAE,OAAK,EAAC,OAAMpB,GAAE,OAAM,UAASA,GAAE,SAAQ,EAAE,GAAEkB,KAAEL,GAAE,CAAC;AAAE,EAAAN,GAAE,EAAC,IAAGQ,KAAE,KAAIA,IAAC,CAAC;AAAE,MAAII,KAAEC,IAAE,MAAIA,GAAEL,GAAC,CAAC;AAAE,EAAAgB,GAAE,MAAIrB,IAAE,eAAe,EAAC,IAAGF,IAAE,SAAQW,IAAE,UAASF,GAAC,CAAC,CAAC,GAAEoB,GAAE,MAAI3B,IAAE,iBAAiBF,EAAC,CAAC;AAAE,MAAIc,MAAEF,IAAE,MAAI;AAAC,QAAIQ;AAAE,aAAQA,KAAElB,IAAE,YAAY,UAAQ,OAAK,SAAOkB,GAAE,QAAMpB;AAAA,EAAC,CAAC,GAAEmB,KAAEP,IAAE,MAAIV,IAAE,SAAS,SAAOV,GAAE,QAAQ,GAAE8B,KAAEV,IAAE,MAAIV,IAAE,QAAQa,GAAEb,IAAE,MAAM,KAAK,GAAEa,GAAEvB,GAAE,KAAK,CAAC,CAAC,GAAES,KAAEW,IAAE,MAAIO,GAAE,QAAM,KAAGG,GAAE,SAAO,CAACpB,IAAE,sBAAsB,SAAOY,IAAE,QAAM,IAAE,EAAE;AAAE,WAASD,KAAG;AAAC,QAAIO;AAAE,IAAAlB,IAAE,OAAOV,GAAE,KAAK,MAAIkB,GAAE,SAAO,IAAGU,KAAER,GAAEL,GAAC,MAAI,QAAMa,GAAE,MAAM;AAAA,EAAE;AAAC,WAASJ,KAAG;AAAC,IAAAN,GAAE,SAAO;AAAA,EAAC;AAAC,WAASO,KAAG;AAAC,IAAAP,GAAE,SAAO;AAAA,EAAE;AAAC,SAAM,MAAI;AAAC,QAAG,EAAC,OAAMU,IAAE,UAASC,IAAE,GAAGM,GAAC,IAAEnC,IAAEsC,MAAE,EAAC,SAAQR,GAAE,OAAM,UAASH,GAAE,OAAM,QAAO,QAAQT,GAAE,QAAM,CAAC,EAAC,GAAEqB,KAAE,EAAC,IAAG/B,IAAE,KAAIO,KAAE,MAAK,SAAQ,gBAAee,GAAE,QAAM,SAAO,SAAQ,mBAAkBlB,IAAE,OAAM,oBAAmBE,GAAE,OAAM,iBAAgBa,GAAE,QAAM,OAAG,QAAO,UAASlB,GAAE,OAAM,SAAQkB,GAAE,QAAM,SAAON,IAAE,SAAQM,GAAE,QAAM,SAAOH,IAAE,QAAOG,GAAE,QAAM,SAAOF,GAAC;AAAE,WAAO,EAAE,EAAC,UAASc,IAAE,YAAWJ,IAAE,MAAKG,KAAE,OAAMrC,KAAE,OAAMI,KAAE,MAAK,mBAAkB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;AEAnsL,SAAO,YAAYmC,IAAE,mBAAmBC,IAAE,YAAYC,IAAE,KAAKC,IAAE,UAAUC,IAAE,aAAaC,IAAE,WAAWC,IAAE,OAAOC,IAAE,SAASC,UAAM;AAA0lB,IAAIC,KAAE,OAAO,cAAc;AAA3B,IAA6B,KAAGC,GAAE,EAAC,MAAK,eAAc,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,EAAC,GAAE,MAAMC,IAAE,EAAC,OAAMC,IAAE,OAAMC,IAAC,GAAE;AAAC,MAAIC,KAAEC,GAAE,IAAI,GAAEC,KAAEC,GAAE,EAAC,MAAK,eAAc,OAAM,EAAC,SAAQC,GAAE,MAAI;AAAC,QAAIC;AAAE,YAAOA,KAAEL,GAAE,UAAQ,OAAK,SAAOK,GAAE;AAAA,EAAE,CAAC,GAAE,QAAQA,IAAE;AAAC,IAAAL,GAAE,UAAQK,GAAE,cAAc,YAAU,WAASA,GAAE,eAAe,GAAEL,GAAE,MAAM,MAAM,GAAEA,GAAE,MAAM,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAE,EAAC,EAAC,CAAC,GAAEM,MAAEC,GAAE,EAAC,MAAK,oBAAmB,CAAC;AAAE,SAAOC,GAAEb,IAAE,EAAC,WAAUK,IAAE,YAAWE,IAAE,aAAYI,IAAC,CAAC,GAAE,MAAI,EAAE,EAAC,YAAWT,IAAE,UAAS,CAAC,GAAE,MAAK,CAAC,GAAE,OAAMC,IAAE,OAAMC,KAAE,MAAK,cAAa,CAAC;AAAC,EAAC,CAAC;AAAnhB,IAAqhBU,MAAGb,GAAE,EAAC,MAAK,UAAS,OAAM,EAAC,qBAAoB,CAAAC,OAAG,KAAE,GAAE,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,YAAW,EAAC,MAAK,SAAQ,SAAQ,OAAM,GAAE,gBAAe,EAAC,MAAK,SAAQ,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,OAAM,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,EAAC,EAAC,GAAE,cAAa,OAAG,MAAMA,IAAE,EAAC,MAAKC,IAAE,OAAMC,KAAE,OAAMC,IAAE,QAAOE,GAAC,GAAE;AAAC,MAAIQ;AAAE,MAAIJ,OAAGI,KAAEb,GAAE,OAAK,OAAKa,KAAE,qBAAqBX,GAAE,CAAC,IAAGY,KAAEC,GAAEjB,IAAE,IAAI,GAAE,CAACU,IAAEQ,GAAC,IAAE,EAAET,GAAE,MAAIP,GAAE,UAAU,GAAE,CAAAiB,OAAGhB,GAAE,qBAAoBgB,EAAC,GAAEV,GAAE,MAAIP,GAAE,cAAc,CAAC;AAAE,WAASkB,MAAG;AAAC,IAAAF,IAAE,CAACR,GAAE,KAAK;AAAA,EAAC;AAAC,MAAIF,MAAEF,GAAE,IAAI,GAAEe,MAAEL,OAAI,OAAKR,MAAEQ,GAAE,WAAUM,KAAEJ,GAAET,GAAE,OAAK,EAAC,IAAGP,GAAE,IAAG,MAAKE,IAAE,KAAI,EAAE,GAAEiB,GAAC;AAAE,EAAAd,GAAE,EAAC,IAAGc,KAAE,KAAIA,IAAC,CAAC;AAAE,WAASE,GAAEJ,IAAE;AAAC,IAAAA,GAAE,eAAe,GAAEC,IAAE;AAAA,EAAC;AAAC,WAASI,IAAEL,IAAE;AAAC,IAAAA,GAAE,QAAME,GAAE,SAAOF,GAAE,eAAe,GAAEC,IAAE,KAAGD,GAAE,QAAME,GAAE,SAAOV,GAAEQ,GAAE,aAAa;AAAA,EAAC;AAAC,WAASM,GAAEN,IAAE;AAAC,IAAAA,GAAE,eAAe;AAAA,EAAC;AAAC,MAAIO,MAAEjB,GAAE,MAAI;AAAC,QAAIU,IAAEQ;AAAE,YAAOA,MAAGR,KAAEE,GAAEA,GAAC,MAAI,OAAK,SAAOF,GAAE,YAAU,OAAK,SAAOQ,GAAE,KAAKR,IAAE,MAAM;AAAA,EAAC,CAAC;AAAE,SAAOS,GAAE,MAAI;AAAC,IAAAC,GAAE,CAACH,GAAC,GAAE,MAAI;AAAC,UAAG,CAACA,IAAE,SAAOxB,GAAE,mBAAiB,OAAO;AAAO,eAASiB,KAAG;AAAC,QAAAD,IAAEhB,GAAE,cAAc;AAAA,MAAC;AAAC,aAAOwB,IAAE,MAAM,iBAAiB,SAAQP,EAAC,GAAE,MAAI;AAAC,YAAIQ;AAAE,SAACA,KAAED,IAAE,UAAQ,QAAMC,GAAE,oBAAoB,SAAQR,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,MAAKA,IAAE,OAAMQ,IAAE,MAAKG,KAAE,UAASC,KAAE,GAAGC,GAAC,IAAE9B,IAAE+B,KAAE,EAAC,SAAQvB,GAAE,MAAK,GAAEwB,KAAE,EAAC,IAAGvB,KAAE,KAAIU,KAAE,MAAK,UAAS,MAAKC,GAAE,OAAM,UAASS,QAAI,KAAG,IAAEA,KAAE,gBAAerB,GAAE,OAAM,mBAAkBM,MAAG,OAAK,SAAOA,GAAE,WAAW,OAAM,oBAAmBA,MAAG,OAAK,SAAOA,GAAE,YAAY,OAAM,SAAQO,IAAE,SAAQC,KAAE,YAAWC,GAAC;AAAE,WAAOU,GAAEC,IAAE,CAACjB,MAAG,QAAMT,GAAE,SAAO,OAAKyB,GAAE5B,IAAEC,GAAE,EAAC,UAASC,GAAE,QAAO,IAAG,SAAQ,MAAK,YAAW,QAAO,MAAG,UAAS,MAAG,SAAQC,GAAE,OAAM,MAAKoB,KAAE,UAASE,GAAE,UAAS,MAAKb,IAAE,OAAMQ,GAAC,CAAC,CAAC,IAAE,MAAK,EAAE,EAAC,UAASO,IAAE,YAAW,EAAC,GAAG9B,KAAE,GAAG6B,GAAED,IAAE,CAAC,cAAa,gBAAgB,CAAC,EAAC,GAAE,MAAKC,IAAE,OAAM7B,KAAE,OAAMC,IAAE,MAAK,SAAQ,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;ACA99F,SAAO,YAAYgC,IAAE,mBAAmBC,IAAE,YAAYC,IAAE,KAAKC,IAAE,UAAUC,IAAE,aAAaC,IAAE,eAAeC,IAAE,WAAWC,KAAE,OAAOC,IAAE,SAASC,IAAE,eAAeC,UAAM;;;ACAjK,SAAO,mBAAmBC,KAAE,KAAKC,IAAE,OAAOC,UAAM;AAAyD,IAAIC,KAAEC,IAAE,EAAC,OAAM,EAAC,SAAQ,EAAC,MAAK,UAAS,UAAS,KAAE,EAAC,GAAE,MAAMC,IAAE;AAAC,MAAIC,KAAEC,GAAE,IAAE;AAAE,SAAM,MAAID,GAAE,QAAME,GAAED,IAAE,EAAC,IAAG,UAAS,MAAK,UAAS,UAASE,GAAE,WAAU,QAAQC,KAAE;AAAC,IAAAA,IAAE,eAAe;AAAE,QAAIC,IAAEC,KAAE;AAAG,aAASC,KAAG;AAAC,UAAIJ;AAAE,UAAGG,QAAK,GAAE;AAAC,QAAAD,MAAG,qBAAqBA,EAAC;AAAE;AAAA,MAAM;AAAC,WAAIF,MAAEJ,GAAE,YAAU,QAAMI,IAAE,KAAKJ,EAAC,GAAE;AAAC,QAAAC,GAAE,QAAM,OAAG,qBAAqBK,EAAC;AAAE;AAAA,MAAM;AAAC,MAAAA,KAAE,sBAAsBE,EAAC;AAAA,IAAC;AAAC,IAAAF,KAAE,sBAAsBE,EAAC;AAAA,EAAC,EAAC,CAAC,IAAE;AAAI,EAAC,CAAC;;;ADAsU,IAAIC,OAAI,CAAAC,SAAIA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,YAAU,CAAC,IAAE,aAAYA,MAAID,OAAI,CAAC,CAAC;AAA9E,IAAgFE,OAAI,CAAAC,SAAIA,IAAEA,IAAE,OAAK,EAAE,IAAE,QAAOA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,IAAEA,IAAE,UAAQ,CAAC,IAAE,WAAUA,MAAID,OAAI,CAAC,CAAC;AAAE,IAAIE,KAAE,OAAO,aAAa;AAAE,SAASC,GAAEC,IAAE;AAAC,MAAIC,KAAEC,GAAEJ,IAAE,IAAI;AAAE,MAAGG,OAAI,MAAK;AAAC,QAAIN,MAAE,IAAI,MAAM,IAAIK,EAAC,iDAAiD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBL,KAAEI,EAAC,GAAEJ;AAAA,EAAC;AAAC,SAAOM;AAAC;AAAC,IAAIE,KAAE,OAAO,gBAAgB;AAA7B,IAA+BC,MAAGC,GAAE,EAAC,MAAK,YAAW,OAAM,EAAC,QAAO,CAAAL,OAAG,KAAE,GAAE,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,WAAU,GAAE,eAAc,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,KAAI,GAAE,cAAa,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,EAAC,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,QAAO,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,EAAC,GAAE,cAAa,OAAG,MAAMA,IAAE,EAAC,OAAMC,IAAE,OAAMN,KAAE,MAAKE,IAAC,GAAE;AAAC,MAAIS;AAAE,MAAIC,MAAEC,IAAGF,MAAEN,GAAE,kBAAgB,OAAKM,MAAEN,GAAE,YAAY,GAAES,KAAED,GAAE,CAAC,CAAC,GAAEE,KAAEF,GAAE,CAAC,CAAC,GAAEG,MAAEC,GAAE,MAAIZ,GAAE,kBAAgB,IAAI,GAAEa,MAAED,GAAE,MAAID,IAAE,QAAMX,GAAE,gBAAcO,IAAE,KAAK;AAAE,WAASO,IAAEC,IAAE;AAAC,QAAIC;AAAE,QAAIC,KAAE,EAAEC,IAAE,KAAK,OAAMC,EAAC,GAAEA,MAAE,EAAED,IAAE,OAAO,OAAMC,EAAC,GAAEC,KAAEH,GAAE,OAAO,CAAAI,OAAG;AAAC,UAAIC;AAAE,aAAM,GAAGA,MAAEH,GAAEE,EAAC,MAAI,QAAMC,IAAE,aAAa,UAAU;AAAA,IAAE,CAAC;AAAE,QAAGP,KAAE,KAAGA,KAAEE,GAAE,SAAO,GAAE;AAAC,UAAII,KAAE,EAAEd,IAAE,UAAQ,OAAK,IAAE,KAAK,KAAKQ,KAAER,IAAE,KAAK,GAAE,EAAC,CAAC,EAAE,GAAE,MAAI,GAAE,CAAC,CAAC,GAAE,MAAI,EAAE,KAAK,KAAKQ,EAAC,GAAE,EAAC,CAAC,EAAE,GAAE,MAAI,GAAE,CAAC,CAAC,GAAE,MAAI,GAAE,CAAC,CAAC,GAAE,MAAI,EAAC,CAAC,GAAE,CAAC,CAAC,GAAE,MAAI,EAAC,CAAC,GAAEO,MAAE,EAAED,IAAE,EAAC,CAAC,CAAC,GAAE,MAAIJ,GAAE,QAAQG,GAAE,CAAC,CAAC,GAAE,CAAC,CAAC,GAAE,MAAIH,GAAE,QAAQG,GAAEA,GAAE,SAAO,CAAC,CAAC,EAAC,CAAC;AAAE,MAAAE,QAAI,OAAKf,IAAE,QAAMe,MAAGJ,IAAE,KAAK,QAAMD,IAAEC,IAAE,OAAO,QAAMC;AAAA,IAAC,OAAK;AAAC,UAAIE,KAAEJ,GAAE,MAAM,GAAEF,EAAC,GAAEQ,KAAE,CAAC,GAAGN,GAAE,MAAMF,EAAC,GAAE,GAAGM,EAAC,EAAE,KAAK,CAAAG,OAAGJ,GAAE,SAASI,EAAC,CAAC;AAAE,UAAG,CAACD,GAAE;AAAO,UAAIE,MAAGT,KAAEC,GAAE,QAAQM,EAAC,MAAI,OAAKP,KAAEE,IAAE,cAAc;AAAM,MAAAO,OAAI,OAAKA,KAAEP,IAAE,cAAc,QAAOX,IAAE,QAAMkB,IAAEP,IAAE,KAAK,QAAMD,IAAEC,IAAE,OAAO,QAAMC;AAAA,IAAC;AAAA,EAAC;AAAC,MAAID,MAAE,EAAC,eAAcN,GAAE,MAAI;AAAC,QAAIG,IAAEE;AAAE,YAAOA,MAAGF,KAAER,IAAE,UAAQ,OAAKQ,KAAEf,GAAE,iBAAe,OAAKiB,KAAE;AAAA,EAAI,CAAC,GAAE,aAAYL,GAAE,MAAIZ,GAAE,WAAS,aAAW,YAAY,GAAE,YAAWY,GAAE,MAAIZ,GAAE,SAAO,WAAS,MAAM,GAAE,MAAKS,IAAE,QAAOC,IAAE,iBAAiBK,IAAE;AAAC,IAAAF,IAAE,UAAQE,MAAGlB,IAAE,UAASkB,EAAC,GAAEJ,IAAE,SAAOG,IAAEC,EAAC;AAAA,EAAC,GAAE,YAAYA,IAAE;AAAC,QAAII;AAAE,QAAGV,GAAE,MAAM,SAASM,EAAC,EAAE;AAAO,QAAIE,KAAER,GAAE,MAAMF,IAAE,KAAK;AAAE,QAAGE,GAAE,MAAM,KAAKM,EAAC,GAAEN,GAAE,QAAM,EAAEA,GAAE,OAAMU,EAAC,GAAE,CAACR,IAAE,OAAM;AAAC,UAAIS,MAAGD,MAAEV,GAAE,MAAM,QAAQQ,EAAC,MAAI,OAAKE,MAAEZ,IAAE;AAAM,MAAAa,OAAI,OAAKb,IAAE,QAAMa;AAAA,IAAE;AAAA,EAAC,GAAE,cAAcL,IAAE;AAAC,QAAIE,KAAER,GAAE,MAAM,QAAQM,EAAC;AAAE,IAAAE,OAAI,MAAIR,GAAE,MAAM,OAAOQ,IAAE,CAAC;AAAA,EAAC,GAAE,cAAcF,IAAE;AAAC,IAAAL,GAAE,MAAM,SAASK,EAAC,MAAIL,GAAE,MAAM,KAAKK,EAAC,GAAEL,GAAE,QAAM,EAAEA,GAAE,OAAMS,EAAC;AAAA,EAAE,GAAE,gBAAgBJ,IAAE;AAAC,QAAIE,KAAEP,GAAE,MAAM,QAAQK,EAAC;AAAE,IAAAE,OAAI,MAAIP,GAAE,MAAM,OAAOO,IAAE,CAAC;AAAA,EAAC,EAAC;AAAE,EAAAS,IAAE5B,IAAEoB,GAAC;AAAE,MAAIS,KAAEnB,GAAE,EAAC,MAAK,CAAC,GAAE,QAAO,CAAC,EAAC,CAAC,GAAEoB,KAAEpB,GAAE,KAAE;AAAE,EAAAqB,GAAE,MAAI;AAAC,IAAAD,GAAE,QAAM;AAAA,EAAE,CAAC,GAAEF,IAAEvB,IAAES,GAAE,MAAIgB,GAAE,QAAM,OAAKD,GAAE,KAAK,CAAC;AAAE,MAAIG,MAAElB,GAAE,MAAIZ,GAAE,aAAa;AAAE,SAAO6B,GAAE,MAAI;AAAC,IAAAE,GAAE,CAACD,GAAC,GAAE,MAAI;AAAC,UAAIf;AAAE,aAAOD,KAAGC,KAAEf,GAAE,kBAAgB,OAAKe,KAAEf,GAAE,YAAY;AAAA,IAAC,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAEgC,GAAE,MAAI;AAAC,QAAG,CAACrB,IAAE,SAAOE,IAAE,SAAO,QAAMK,IAAE,KAAK,MAAM,UAAQ,EAAE;AAAO,QAAIH,KAAE,EAAEG,IAAE,KAAK,OAAMC,EAAC;AAAE,IAAAJ,GAAE,KAAK,CAACI,KAAEC,OAAID,GAAED,IAAE,KAAK,MAAME,EAAC,CAAC,MAAID,GAAEA,GAAC,CAAC,KAAGD,IAAE,iBAAiBH,GAAE,UAAU,CAAAI,QAAGA,GAAEA,GAAC,MAAIA,GAAED,IAAE,KAAK,MAAML,IAAE,KAAK,CAAC,CAAC,CAAC;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,QAAIE,KAAE,EAAC,eAAcR,IAAE,MAAK;AAAE,WAAO0B,GAAEC,IAAE,CAACzB,GAAE,MAAM,UAAQ,KAAGwB,GAAEpC,IAAE,EAAC,SAAQ,MAAI;AAAC,eAAQoB,MAAKR,GAAE,OAAM;AAAC,YAAIU,MAAEA,GAAEF,EAAC;AAAE,aAAIE,OAAG,OAAK,SAAOA,IAAE,cAAY,EAAE,QAAOA,IAAE,MAAM,GAAE;AAAA,MAAE;AAAC,aAAM;AAAA,IAAE,EAAC,CAAC,GAAE,EAAE,EAAC,YAAW,EAAC,GAAGxB,KAAE,GAAGgC,GAAG3B,IAAE,CAAC,iBAAgB,gBAAe,UAAS,YAAW,UAAU,CAAC,EAAC,GAAE,UAAS,CAAC,GAAE,MAAKe,IAAE,OAAMd,IAAE,OAAMN,KAAE,MAAK,WAAU,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAr/E,IAAu/EwC,MAAG9B,GAAE,EAAC,MAAK,WAAU,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,EAAC,GAAE,MAAML,IAAE,EAAC,OAAMC,IAAE,OAAMN,IAAC,GAAE;AAAC,MAAIE,MAAEE,GAAE,SAAS;AAAE,SAAM,MAAI;AAAC,QAAIQ,MAAE,EAAC,eAAcV,IAAE,cAAc,MAAK,GAAEY,KAAE,EAAC,MAAK,WAAU,oBAAmBZ,IAAE,YAAY,MAAK;AAAE,WAAO,EAAE,EAAC,UAASY,IAAE,YAAWT,IAAE,MAAKO,KAAE,OAAMN,IAAE,OAAMN,KAAE,MAAK,UAAS,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAvyF,IAAyyF,KAAGU,GAAE,EAAC,MAAK,OAAM,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,SAAQ,GAAE,UAAS,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,EAAC,GAAE,MAAML,IAAE,EAAC,OAAMC,IAAE,OAAMN,KAAE,QAAOE,IAAC,GAAE;AAAC,MAAIsB;AAAE,MAAIZ,OAAGY,MAAEnB,GAAE,OAAK,OAAKmB,MAAE,uBAAuBZ,GAAE,CAAC,IAAGE,KAAEV,GAAE,KAAK,GAAEW,KAAEF,GAAE,IAAI;AAAE,EAAAX,IAAE,EAAC,IAAGa,IAAE,KAAIA,GAAC,CAAC,GAAEmB,GAAE,MAAIpB,GAAE,YAAYC,EAAC,CAAC,GAAE0B,GAAE,MAAI3B,GAAE,cAAcC,EAAC,CAAC;AAAE,MAAIC,MAAET,GAAEC,EAAC,GAAEU,MAAED,GAAE,MAAI;AAAC,QAAGD,IAAE,OAAM;AAAC,UAAIS,KAAET,IAAE,MAAM,KAAK,QAAQJ,GAAC;AAAE,aAAOa,OAAI,KAAGT,IAAE,MAAM,KAAK,KAAKJ,GAAC,IAAE,IAAEa;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE,CAAC,GAAEN,MAAEF,GAAE,MAAI;AAAC,QAAIQ,KAAEX,GAAE,KAAK,MAAM,QAAQC,EAAC;AAAE,WAAOU,OAAI,KAAGP,IAAE,QAAMO;AAAA,EAAC,CAAC,GAAEF,MAAEN,GAAE,MAAIE,IAAE,UAAQL,GAAE,cAAc,KAAK;AAAE,WAASkB,GAAEP,IAAE;AAAC,QAAIC;AAAE,QAAIL,KAAEI,GAAE;AAAE,QAAGJ,OAAI,EAAE,WAASP,GAAE,WAAW,UAAQ,QAAO;AAAC,UAAIa,OAAGD,KAAEd,GAAEG,EAAC,MAAI,OAAK,SAAOW,GAAE,eAAcE,KAAEd,GAAE,KAAK,MAAM,UAAU,CAAAgB,OAAGN,GAAEM,EAAC,MAAIH,GAAC;AAAE,MAAAC,OAAI,MAAId,GAAE,iBAAiBc,EAAC;AAAA,IAAC;AAAC,WAAOP;AAAA,EAAC;AAAC,WAASY,GAAER,IAAE;AAAC,QAAIJ,KAAEP,GAAE,KAAK,MAAM,IAAI,CAAAa,QAAGH,GAAEG,GAAC,CAAC,EAAE,OAAO,OAAO;AAAE,QAAGF,GAAE,QAAMD,GAAE,SAAOC,GAAE,QAAMD,GAAE,OAAM;AAAC,MAAAC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEX,GAAE,iBAAiBK,IAAE,KAAK;AAAE;AAAA,IAAM;AAAC,YAAOM,GAAE,KAAI;AAAA,MAAC,KAAKD,GAAE;AAAA,MAAK,KAAKA,GAAE;AAAO,eAAOC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEO,GAAE,MAAI,EAAEX,IAAE,EAAE,KAAK,CAAC;AAAA,MAAE,KAAKG,GAAE;AAAA,MAAI,KAAKA,GAAE;AAAS,eAAOC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEO,GAAE,MAAI,EAAEX,IAAE,EAAE,IAAI,CAAC;AAAA,IAAC;AAAC,QAAGW,GAAE,MAAI,EAAElB,GAAE,YAAY,OAAM,EAAC,WAAU;AAAC,aAAOW,GAAE,QAAMD,GAAE,UAAQ,EAAEH,IAAE,EAAE,WAAS,EAAE,UAAU,IAAEI,GAAE,QAAMD,GAAE,YAAU,EAAEH,IAAE,EAAE,OAAK,EAAE,UAAU,IAAE,EAAE;AAAA,IAAK,GAAE,aAAY;AAAC,aAAOI,GAAE,QAAMD,GAAE,YAAU,EAAEH,IAAE,EAAE,WAAS,EAAE,UAAU,IAAEI,GAAE,QAAMD,GAAE,aAAW,EAAEH,IAAE,EAAE,OAAK,EAAE,UAAU,IAAE,EAAE;AAAA,IAAK,EAAC,CAAC,CAAC,MAAI,EAAE,QAAQ,QAAOI,GAAE,eAAe;AAAA,EAAC;AAAC,MAAIU,MAAEtB,GAAE,KAAE;AAAE,WAASF,MAAG;AAAC,QAAIc;AAAE,IAAAU,IAAE,UAAQA,IAAE,QAAM,MAAG,CAAC9B,GAAE,cAAYoB,KAAED,GAAET,EAAC,MAAI,QAAMU,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAEX,GAAE,iBAAiBK,IAAE,KAAK,GAAE,EAAE,MAAI;AAAC,MAAAgB,IAAE,QAAM;AAAA,IAAE,CAAC;AAAA,EAAG;AAAC,WAASf,GAAEK,IAAE;AAAC,IAAAA,GAAE,eAAe;AAAA,EAAC;AAAC,MAAIH,KAAEtB,GAAEiB,GAAE,OAAK,EAAC,IAAGZ,GAAE,IAAG,MAAKC,GAAE,KAAI,EAAE,GAAES,EAAC;AAAE,SAAM,MAAI;AAAC,QAAIY,KAAEC;AAAE,QAAIH,KAAE,EAAC,UAASF,IAAE,OAAM,WAAUI,MAAEtB,GAAE,aAAW,OAAKsB,MAAE,MAAE,GAAE,EAAC,GAAGN,GAAC,IAAEhB,IAAEqB,KAAE,EAAC,KAAIX,IAAE,WAAUkB,IAAE,aAAYb,IAAE,SAAQT,KAAE,IAAGC,KAAE,MAAK,OAAM,MAAKU,GAAE,OAAM,kBAAiBM,KAAEJ,GAAEV,GAAE,OAAO,MAAMK,IAAE,KAAK,CAAC,MAAI,OAAK,SAAOS,GAAE,IAAG,iBAAgBL,IAAE,OAAM,UAASA,IAAE,QAAM,IAAE,IAAG,UAASlB,GAAE,WAAS,OAAG,OAAM;AAAE,WAAO,EAAE,EAAC,UAASqB,IAAE,YAAWL,IAAE,MAAKI,IAAE,OAAMnB,IAAE,OAAMN,KAAE,MAAK,MAAK,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAvxJ,IAAyxJ0C,MAAGhC,GAAE,EAAC,MAAK,aAAY,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,EAAC,GAAE,MAAML,IAAE,EAAC,OAAMC,IAAE,OAAMN,IAAC,GAAE;AAAC,MAAIE,MAAEE,GAAE,WAAW;AAAE,SAAM,MAAI;AAAC,QAAIQ,MAAE,EAAC,eAAcV,IAAE,cAAc,MAAK;AAAE,WAAO,EAAE,EAAC,YAAWG,IAAE,UAAS,CAAC,GAAE,MAAKO,KAAE,OAAMZ,KAAE,OAAMM,IAAE,MAAK,YAAW,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAthK,IAAwhKqC,MAAGjC,GAAE,EAAC,MAAK,YAAW,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,IAAG,EAAC,MAAK,QAAO,SAAQ,KAAI,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,EAAC,EAAC,GAAE,MAAML,IAAE,EAAC,OAAMC,IAAE,OAAMN,KAAE,QAAOE,IAAC,GAAE;AAAC,MAAI8B;AAAE,MAAIpB,OAAGoB,KAAE3B,GAAE,OAAK,OAAK2B,KAAE,yBAAyBpB,GAAE,CAAC,IAAGE,KAAEV,GAAE,UAAU,GAAEW,KAAEF,GAAE,IAAI;AAAE,EAAAX,IAAE,EAAC,IAAGa,IAAE,KAAIA,GAAC,CAAC,GAAEmB,GAAE,MAAIpB,GAAE,cAAcC,EAAC,CAAC,GAAE0B,GAAE,MAAI3B,GAAE,gBAAgBC,EAAC,CAAC;AAAE,MAAIC,MAAET,GAAEC,EAAC,GAAEU,MAAED,GAAE,MAAI;AAAC,QAAGD,IAAE,OAAM;AAAC,UAAIiB,KAAEjB,IAAE,MAAM,OAAO,QAAQJ,GAAC;AAAE,aAAOqB,OAAI,KAAGjB,IAAE,MAAM,OAAO,KAAKJ,GAAC,IAAE,IAAEqB;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE,CAAC,GAAEd,MAAEF,GAAE,MAAI;AAAC,QAAIgB,KAAEnB,GAAE,OAAO,MAAM,QAAQC,EAAC;AAAE,WAAOkB,OAAI,KAAGf,IAAE,QAAMe;AAAA,EAAC,CAAC,GAAEV,MAAEN,GAAE,MAAIE,IAAE,UAAQL,GAAE,cAAc,KAAK;AAAE,SAAM,MAAI;AAAC,QAAIQ;AAAE,QAAIW,KAAE,EAAC,UAASV,IAAE,MAAK,GAAE,EAAC,UAASY,KAAE,GAAGxB,IAAC,IAAEN,IAAEe,KAAE,EAAC,KAAIL,IAAE,IAAGH,KAAE,MAAK,YAAW,oBAAmBU,KAAEE,GAAEV,GAAE,KAAK,MAAMK,IAAE,KAAK,CAAC,MAAI,OAAK,SAAOG,GAAE,IAAG,UAASC,IAAE,QAAMY,MAAE,GAAE;AAAE,WAAM,CAACZ,IAAE,SAAOlB,GAAE,WAAS,CAACA,GAAE,SAAOiC,GAAEM,IAAE,EAAC,IAAG,QAAO,eAAc,MAAG,GAAGxB,GAAC,CAAC,IAAE,EAAE,EAAC,UAASA,IAAE,YAAWT,KAAE,MAAKsB,IAAE,OAAM3B,IAAE,OAAMN,KAAE,UAAS+B,GAAE,SAAOA,GAAE,gBAAe,SAAQR,IAAE,OAAM,MAAK,WAAU,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;AEAzqO,SAAO,YAAYsB,IAAE,mBAAmBC,IAAE,KAAKC,IAAE,UAAUC,IAAE,kBAAkBC,KAAG,aAAaC,IAAE,eAAeC,IAAE,WAAWC,IAAE,OAAOC,KAAE,SAASC,KAAG,eAAeC,UAAM;;;ACAzK,SAASC,GAAEC,IAAE;AAAC,MAAIC,KAAE,EAAC,QAAO,MAAE;AAAE,SAAM,IAAIC,OAAI;AAAC,QAAG,CAACD,GAAE,OAAO,QAAOA,GAAE,SAAO,MAAGD,GAAE,GAAGE,EAAC;AAAA,EAAC;AAAC;;;ACAmB,SAASC,IAAEC,OAAKC,IAAE;AAAC,EAAAD,MAAGC,GAAE,SAAO,KAAGD,GAAE,UAAU,IAAI,GAAGC,EAAC;AAAC;AAAC,SAASC,GAAEF,OAAKC,IAAE;AAAC,EAAAD,MAAGC,GAAE,SAAO,KAAGD,GAAE,UAAU,OAAO,GAAGC,EAAC;AAAC;AAAC,IAAIE,MAAG,CAAAC,SAAIA,IAAE,WAAS,YAAWA,IAAE,YAAU,aAAYA,MAAID,MAAG,CAAC,CAAC;AAAE,SAASE,GAAEL,IAAEC,IAAE;AAAC,MAAIG,MAAE,EAAE;AAAE,MAAG,CAACJ,GAAE,QAAOI,IAAE;AAAQ,MAAG,EAAC,oBAAmBE,IAAE,iBAAgBC,GAAC,IAAE,iBAAiBP,EAAC,GAAE,CAACQ,IAAEC,GAAC,IAAE,CAACH,IAAEC,EAAC,EAAE,IAAI,CAAAG,QAAG;AAAC,QAAG,CAACC,MAAE,CAAC,IAAED,IAAE,MAAM,GAAG,EAAE,OAAO,OAAO,EAAE,IAAI,CAAAE,OAAGA,GAAE,SAAS,IAAI,IAAE,WAAWA,EAAC,IAAE,WAAWA,EAAC,IAAE,GAAG,EAAE,KAAK,CAACA,IAAEC,OAAIA,KAAED,EAAC;AAAE,WAAOD;AAAA,EAAC,CAAC;AAAE,SAAOH,OAAI,IAAEJ,IAAE,WAAW,MAAIH,GAAE,UAAU,GAAEO,KAAEC,GAAC,IAAER,GAAE,UAAU,GAAEG,IAAE,IAAI,MAAIH,GAAE,WAAW,CAAC,GAAEG,IAAE;AAAO;AAAC,SAASU,GAAEd,IAAEC,IAAEG,KAAEE,IAAEC,IAAEC,IAAE;AAAC,MAAIC,MAAE,EAAE,GAAEC,MAAEF,OAAI,SAAOA,GAAEA,EAAC,IAAE,MAAI;AAAA,EAAC;AAAE,SAAON,GAAEF,IAAE,GAAGO,EAAC,GAAER,IAAEC,IAAE,GAAGC,IAAE,GAAGG,GAAC,GAAEK,IAAE,UAAU,MAAI;AAAC,IAAAP,GAAEF,IAAE,GAAGI,GAAC,GAAEL,IAAEC,IAAE,GAAGM,EAAC,GAAEG,IAAE,IAAIJ,GAAEL,IAAE,CAAAW,SAAIT,GAAEF,IAAE,GAAGM,IAAE,GAAGL,EAAC,GAAEF,IAAEC,IAAE,GAAGO,EAAC,GAAEG,IAAEC,GAAC,EAAE,CAAC;AAAA,EAAC,CAAC,GAAEF,IAAE,IAAI,MAAIP,GAAEF,IAAE,GAAGC,IAAE,GAAGG,KAAE,GAAGE,IAAE,GAAGC,EAAC,CAAC,GAAEE,IAAE,IAAI,MAAIC,IAAE,WAAW,CAAC,GAAED,IAAE;AAAO;;;AFAxQ,SAASM,GAAEC,KAAE,IAAG;AAAC,SAAOA,GAAE,MAAM,KAAK,EAAE,OAAO,CAAAC,OAAGA,GAAE,SAAO,CAAC;AAAC;AAAC,IAAIC,KAAE,OAAO,mBAAmB;AAAE,IAAIC,OAAI,CAAAC,QAAIA,GAAE,UAAQ,WAAUA,GAAE,SAAO,UAASA,KAAID,OAAI,CAAC,CAAC;AAAE,SAASE,MAAI;AAAC,SAAOC,GAAEJ,IAAE,IAAI,MAAI;AAAI;AAAC,SAASK,MAAI;AAAC,MAAIP,KAAEM,GAAEJ,IAAE,IAAI;AAAE,MAAGF,OAAI,KAAK,OAAM,IAAI,MAAM,8EAA8E;AAAE,SAAOA;AAAC;AAAC,SAASQ,MAAI;AAAC,MAAIR,KAAEM,GAAEG,KAAE,IAAI;AAAE,MAAGT,OAAI,KAAK,OAAM,IAAI,MAAM,8EAA8E;AAAE,SAAOA;AAAC;AAAC,IAAIS,MAAE,OAAO,gBAAgB;AAAE,SAASC,GAAEV,IAAE;AAAC,SAAM,cAAaA,KAAEU,GAAEV,GAAE,QAAQ,IAAEA,GAAE,MAAM,OAAO,CAAC,EAAC,OAAMC,GAAC,MAAIA,OAAI,SAAS,EAAE,SAAO;AAAC;AAAC,SAASU,GAAEX,IAAE;AAAC,MAAIC,KAAEW,IAAE,CAAC,CAAC,GAAER,KAAEQ,IAAE,KAAE;AAAE,EAAAC,GAAE,MAAIT,GAAE,QAAM,IAAE,GAAEU,GAAE,MAAIV,GAAE,QAAM,KAAE;AAAE,WAASW,IAAEC,IAAEC,KAAEC,GAAE,QAAO;AAAC,QAAIC,KAAElB,GAAE,MAAM,UAAU,CAAC,EAAC,IAAGmB,GAAC,MAAIA,OAAIJ,EAAC;AAAE,IAAAG,OAAI,OAAK,EAAEF,IAAE,EAAC,CAACC,GAAE,OAAO,IAAG;AAAC,MAAAjB,GAAE,MAAM,OAAOkB,IAAE,CAAC;AAAA,IAAC,GAAE,CAACD,GAAE,MAAM,IAAG;AAAC,MAAAjB,GAAE,MAAMkB,EAAC,EAAE,QAAM;AAAA,IAAQ,EAAC,CAAC,GAAE,CAACT,GAAET,EAAC,KAAGG,GAAE,UAAQJ,MAAG,QAAMA,GAAE;AAAA,EAAG;AAAC,WAASqB,GAAEL,IAAE;AAAC,QAAIC,KAAEhB,GAAE,MAAM,KAAK,CAAC,EAAC,IAAGkB,GAAC,MAAIA,OAAIH,EAAC;AAAE,WAAOC,KAAEA,GAAE,UAAQ,cAAYA,GAAE,QAAM,aAAWhB,GAAE,MAAM,KAAK,EAAC,IAAGe,IAAE,OAAM,UAAS,CAAC,GAAE,MAAID,IAAEC,IAAEE,GAAE,OAAO;AAAA,EAAC;AAAC,SAAM,EAAC,UAASjB,IAAE,UAASoB,IAAE,YAAWN,IAAC;AAAC;AAAC,IAAIO,KAAEb,GAAG;AAAT,IAAwBc,MAAGC,GAAE,EAAC,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,MAAK,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,KAAI,GAAE,SAAQ,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,KAAE,GAAE,QAAO,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,OAAM,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,WAAU,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,OAAM,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,WAAU,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,EAAC,GAAE,OAAM,EAAC,aAAY,MAAI,MAAG,YAAW,MAAI,MAAG,aAAY,MAAI,MAAG,YAAW,MAAI,KAAE,GAAE,MAAMxB,IAAE,EAAC,MAAKC,IAAE,OAAMG,IAAE,OAAMW,KAAE,QAAOM,GAAC,GAAE;AAAC,MAAIL,KAAEJ,IAAE,CAAC;AAAE,WAASK,KAAG;AAAC,IAAAD,GAAE,SAAOS,GAAE,SAAQxB,GAAE,aAAa;AAAA,EAAC;AAAC,WAASkB,KAAG;AAAC,IAAAH,GAAE,SAAO,CAACS,GAAE,SAAQxB,GAAE,YAAY;AAAA,EAAC;AAAC,WAASmB,KAAG;AAAC,IAAAJ,GAAE,SAAOS,GAAE,SAAQxB,GAAE,aAAa;AAAA,EAAC;AAAC,WAASiB,KAAG;AAAC,IAAAF,GAAE,SAAO,CAACS,GAAE,SAAQxB,GAAE,YAAY;AAAA,EAAC;AAAC,MAAG,CAACI,IAAG,KAAGU,GAAG,EAAE,QAAM,MAAIW,GAAEC,KAAG,EAAC,GAAG3B,IAAE,eAAciB,IAAE,cAAaE,IAAE,eAAcC,IAAE,cAAaF,GAAC,GAAEH,GAAC;AAAE,MAAIa,MAAEhB,IAAE,IAAI,GAAEiB,MAAEC,GAAE,MAAI9B,GAAE,UAAQkB,GAAE,UAAQA,GAAE,MAAM;AAAE,EAAAG,GAAE,EAAC,IAAGO,KAAE,KAAIA,IAAC,CAAC;AAAE,MAAG,EAAC,MAAKG,IAAE,QAAOC,GAAC,IAAEzB,IAAG,GAAE,EAAC,UAAS0B,IAAE,YAAWC,GAAC,IAAE1B,IAAG,GAAEiB,MAAEb,IAAEmB,GAAE,QAAM,YAAU,QAAQ,GAAEI,KAAE,EAAC,OAAM,KAAE,GAAEC,KAAEX,GAAG,GAAEY,KAAE,EAAC,OAAM,MAAE,GAAEC,KAAE3B,GAAE,MAAI;AAAC,KAAC0B,GAAE,SAAOZ,IAAE,UAAQ,aAAWA,IAAE,QAAM,UAASS,GAAEE,EAAC,GAAElB,GAAE;AAAA,EAAE,CAAC;AAAE,EAAAL,GAAE,MAAI;AAAC,QAAI0B,MAAEN,GAAEG,EAAC;AAAE,IAAAtB,GAAEyB,GAAC;AAAA,EAAC,CAAC,GAAEC,GAAE,MAAI;AAAC,QAAGX,IAAE,UAAQX,GAAE,UAAQkB,IAAE;AAAC,UAAGL,GAAE,SAAON,IAAE,UAAQ,WAAU;AAAC,QAAAA,IAAE,QAAM;AAAU;AAAA,MAAM;AAAC,QAAEA,IAAE,OAAM,EAAC,CAAC,QAAQ,GAAE,MAAIS,GAAEE,EAAC,GAAE,CAAC,SAAS,GAAE,MAAIH,GAAEG,EAAC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC,CAAC;AAAE,MAAIK,MAAE1C,GAAEC,GAAE,KAAK,GAAE0C,KAAE3C,GAAEC,GAAE,SAAS,GAAE2C,KAAE5C,GAAEC,GAAE,OAAO,GAAE4C,KAAE7C,GAAEC,GAAE,OAAO,GAAE6C,KAAE9C,GAAEC,GAAE,KAAK,GAAE8C,KAAE/C,GAAEC,GAAE,SAAS,GAAE+C,MAAGhD,GAAEC,GAAE,OAAO;AAAE,EAAAa,GAAE,MAAI;AAAC,IAAA2B,GAAE,MAAI;AAAC,UAAGf,IAAE,UAAQ,WAAU;AAAC,YAAIc,MAAEA,GAAEX,GAAC;AAAE,YAAGW,eAAa,WAASA,IAAE,SAAO,GAAG,OAAM,IAAI,MAAM,iEAAiE;AAAA,MAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAE,WAASS,IAAGT,KAAE;AAAC,QAAIU,MAAEd,GAAE,SAAO,CAACH,GAAE,OAAMkB,MAAEX,GAAEX,GAAC;AAAE,KAACsB,OAAG,EAAEA,eAAa,gBAAcD,QAAIZ,GAAE,QAAM,MAAGN,GAAE,SAAOd,GAAE,GAAEc,GAAE,SAAOX,GAAE,GAAEmB,IAAER,GAAE,QAAMrB,GAAEwC,KAAET,KAAEC,IAAEC,IAAEC,IAAE,CAAAO,OAAG;AAAC,MAAAd,GAAE,QAAM,OAAGc,OAAIpD,GAAE,YAAUoB,GAAE;AAAA,IAAC,CAAC,IAAET,GAAEwC,KAAEL,IAAEC,IAAEC,KAAGH,IAAE,CAAAO,OAAG;AAAC,MAAAd,GAAE,QAAM,OAAGc,OAAIpD,GAAE,aAAWW,GAAE4B,EAAC,MAAIb,IAAE,QAAM,UAASS,GAAEE,EAAC,GAAElB,GAAE;AAAA,IAAG,CAAC,CAAC;AAAA,EAAE;AAAC,SAAOL,GAAE,MAAI;AAAC,IAAAuC,IAAG,CAACrB,EAAC,GAAE,CAACQ,KAAEU,KAAEC,QAAI;AAAC,MAAAF,IAAGE,GAAC,GAAEf,GAAE,QAAM;AAAA,IAAE,GAAE,EAAC,WAAU,KAAE,CAAC;AAAA,EAAC,CAAC,GAAEkB,GAAE5C,KAAE6B,EAAC,GAAErC,GAAG6B,GAAE,MAAI,EAAEL,IAAE,OAAM,EAAC,CAAC,SAAS,GAAEA,GAAE,MAAK,CAAC,QAAQ,GAAEA,GAAE,OAAM,CAAC,IAAET,GAAE,KAAK,CAAC,GAAE,MAAI;AAAC,QAAG,EAAC,QAAOuB,KAAE,MAAKU,KAAE,OAAMC,KAAE,WAAUC,IAAE,SAAQG,KAAG,SAAQC,KAAG,OAAMC,KAAG,WAAUC,KAAG,SAAQC,KAAG,GAAGC,GAAC,IAAE3D,IAAE4D,MAAG,EAAC,KAAIhC,IAAC,GAAEiC,MAAG,EAAC,GAAGF,IAAE,GAAG3B,GAAE,SAAOD,GAAE,SAAO,EAAG,WAAS,EAAC,OAAM+B,IAAG,CAAC1D,GAAE,OAAMuD,GAAE,OAAM,GAAGlB,KAAE,GAAGC,EAAC,CAAC,EAAC,IAAE,CAAC,EAAC;AAAE,WAAO,EAAE,EAAC,YAAWmB,KAAG,UAASD,KAAG,MAAK,CAAC,GAAE,OAAM7C,KAAE,OAAMX,IAAE,UAASkB,IAAE,SAAQG,IAAE,UAAQ,WAAU,MAAK,kBAAiB,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAA9wE,IAAgxEsC,MAAGxC;AAAnxE,IAAsxEI,MAAGH,GAAE,EAAC,cAAa,OAAG,OAAM,EAAC,IAAG,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,MAAK,GAAE,MAAK,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,KAAI,GAAE,SAAQ,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,KAAE,GAAE,QAAO,EAAC,MAAK,CAAC,OAAO,GAAE,SAAQ,MAAE,GAAE,OAAM,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,WAAU,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,OAAM,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,WAAU,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,MAAM,GAAE,SAAQ,GAAE,EAAC,GAAE,OAAM,EAAC,aAAY,MAAI,MAAG,YAAW,MAAI,MAAG,aAAY,MAAI,MAAG,YAAW,MAAI,KAAE,GAAE,MAAMxB,IAAE,EAAC,MAAKC,IAAE,OAAMG,IAAE,OAAMW,IAAC,GAAE;AAAC,MAAIM,KAAEF,GAAG,GAAEH,KAAEc,GAAE,MAAI9B,GAAE,SAAO,QAAMqB,OAAI,QAAMA,GAAE,QAAMI,GAAE,UAAQA,GAAE,OAAKzB,GAAE,IAAI;AAAE,EAAAwC,GAAE,MAAI;AAAC,QAAG,CAAC,CAAC,MAAG,KAAE,EAAE,SAASxB,GAAE,KAAK,EAAE,OAAM,IAAI,MAAM,2EAA2E;AAAA,EAAC,CAAC;AAAE,MAAIC,KAAEL,IAAEI,GAAE,QAAM,YAAU,QAAQ,GAAEG,KAAER,GAAE,MAAI;AAAC,IAAAM,GAAE,QAAM;AAAA,EAAQ,CAAC,GAAEG,KAAER,IAAE,IAAE,GAAEM,KAAE,EAAC,MAAKF,IAAE,QAAOc,GAAE,MAAI9B,GAAE,UAAQ,CAACoB,GAAE,KAAK,EAAC;AAAE,SAAOP,GAAE,MAAI;AAAC,IAAA2B,GAAE,MAAI;AAAC,MAAApB,GAAE,QAAM,OAAGJ,GAAE,QAAMC,GAAE,QAAM,YAAUP,GAAES,EAAC,MAAIF,GAAE,QAAM;AAAA,IAAS,CAAC;AAAA,EAAC,CAAC,GAAEoC,GAAE5C,KAAEU,EAAC,GAAEkC,GAAEnD,IAAEgB,EAAC,GAAE,MAAI;AAAC,QAAIU,MAAEoC,GAAGhE,IAAE,CAAC,QAAO,UAAS,WAAU,iBAAgB,iBAAgB,gBAAe,cAAc,CAAC,GAAE6B,MAAE,EAAC,SAAQ7B,GAAE,QAAO;AAAE,WAAO,EAAE,EAAC,UAAS,EAAC,GAAG6B,KAAE,IAAG,WAAU,GAAE,YAAW,CAAC,GAAE,MAAK,CAAC,GAAE,OAAM,EAAC,GAAGd,KAAE,SAAQ,MAAI,CAACW,GAAEqC,KAAG,EAAC,eAAc,MAAI9D,GAAE,aAAa,GAAE,cAAa,MAAIA,GAAE,YAAY,GAAE,eAAc,MAAIA,GAAE,aAAa,GAAE,cAAa,MAAIA,GAAE,YAAY,GAAE,GAAGG,IAAE,GAAGyB,KAAE,GAAGD,IAAC,GAAEb,IAAE,OAAO,CAAC,EAAC,GAAE,OAAM,CAAC,GAAE,UAASO,IAAE,SAAQL,GAAE,UAAQ,WAAU,MAAK,aAAY,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;;;AGK70K,IAAM,QAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAM,aAAa,CAAC,SAAS,KAAK;AAClC,IAAM,aAA0B,MAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,WAAW,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AACxI,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,eAAe,CAAAgD,QAAM;AAAA,EACzB,GAAGA;AAAA,EACH,GAAGA;AACL;AACA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,KAAK;AACP;AACA,SAAS,MAAM,OAAO,OAAO,KAAK;AAChC,SAAO,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AACnC;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,SAAS,MAAM,WAAW;AACnC;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,CAAC,OAAO,QAAQ,EAAE,SAAS,QAAQ,SAAS,CAAC,IAAI,MAAM;AAChE;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,gBAAgB,YAAY,SAAS,CAAC;AAC/C;AACA,SAAS,kBAAkB,WAAW,OAAO,KAAK;AAChD,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,SAAS,cAAc,aAAa;AAC1C,MAAI,oBAAoB,kBAAkB,MAAM,eAAe,MAAM,QAAQ,WAAW,UAAU,SAAS,cAAc,UAAU,WAAW;AAC9I,MAAI,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG;AACpD,wBAAoB,qBAAqB,iBAAiB;AAAA,EAC5D;AACA,SAAO,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACpE;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,oBAAoB,qBAAqB,SAAS;AACxD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,eAAa,qBAAqB,SAAS,CAAC;AACrF;AACA,SAAS,YAAY,MAAM,SAAS,KAAK;AACvC,QAAM,KAAK,CAAC,QAAQ,OAAO;AAC3B,QAAM,KAAK,CAAC,SAAS,MAAM;AAC3B,QAAM,KAAK,CAAC,OAAO,QAAQ;AAC3B,QAAM,KAAK,CAAC,UAAU,KAAK;AAC3B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,UAAI,IAAK,QAAO,UAAU,KAAK;AAC/B,aAAO,UAAU,KAAK;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,KAAK;AAAA,IACxB;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AACA,SAAS,0BAA0B,WAAW,eAAe,WAAW,KAAK;AAC3E,QAAM,YAAY,aAAa,SAAS;AACxC,MAAI,OAAO,YAAY,QAAQ,SAAS,GAAG,cAAc,SAAS,GAAG;AACrE,MAAI,WAAW;AACb,WAAO,KAAK,IAAI,UAAQ,OAAO,MAAM,SAAS;AAC9C,QAAI,eAAe;AACjB,aAAO,KAAK,OAAO,KAAK,IAAI,6BAA6B,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,UAAQ,gBAAgB,IAAI,CAAC;AAClF;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,GAAG;AAAA,EACL;AACF;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,OAAO,YAAY,WAAW,oBAAoB,OAAO,IAAI;AAAA,IAClE,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM;AAAA,IACJ,GAAAC;AAAA,IACA,GAAAC;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAKA;AAAA,IACL,MAAMD;AAAA,IACN,OAAOA,KAAI;AAAA,IACX,QAAQC,MAAI;AAAA,IACZ,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF;AACF;;;ACpIA,SAAS,2BAA2B,MAAM,WAAW,KAAK;AACxD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,SAAS,QAAQ;AACrE,QAAM,UAAU,UAAU,IAAI,UAAU,SAAS,IAAI,SAAS,SAAS;AACvE,QAAM,cAAc,UAAU,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI;AACzE,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,SAAS;AAAA,MAC5B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,UAAU;AAAA,MAC7B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,UAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,SAAS;AAAA,QAC1B,GAAG;AAAA,MACL;AACA;AAAA,IACF;AACE,eAAS;AAAA,QACP,GAAG,UAAU;AAAA,QACb,GAAG,UAAU;AAAA,MACf;AAAA,EACJ;AACA,UAAQ,aAAa,SAAS,GAAG;AAAA,IAC/B,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,IACF,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,EACJ;AACA,SAAO;AACT;AASA,IAAM,kBAAkB,OAAO,WAAW,UAAU,WAAW;AAC7D,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAC;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,WAAW,OAAO,OAAO;AACjD,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,QAAQ;AAC5E,MAAI,QAAQ,MAAMA,UAAS,gBAAgB;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI;AAAA,IACF,GAAAC;AAAA,IACA,GAAAC;AAAA,EACF,IAAI,2BAA2B,OAAO,WAAW,GAAG;AACpD,MAAI,oBAAoB;AACxB,MAAI,iBAAiB,CAAC;AACtB,MAAI,aAAa;AACjB,WAASC,MAAI,GAAGA,MAAI,gBAAgB,QAAQA,OAAK;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgBA,GAAC;AACrB,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI,MAAM,GAAG;AAAA,MACX,GAAAF;AAAA,MACA,GAAAC;AAAA,MACA,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAF;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AACD,IAAAC,KAAI,SAAS,OAAO,QAAQA;AAC5B,IAAAC,MAAI,SAAS,OAAO,QAAQA;AAC5B,qBAAiB;AAAA,MACf,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,QACN,GAAG,eAAe,IAAI;AAAA,QACtB,GAAG;AAAA,MACL;AAAA,IACF;AACA,QAAI,SAAS,cAAc,IAAI;AAC7B;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,WAAW;AACnB,8BAAoB,MAAM;AAAA,QAC5B;AACA,YAAI,MAAM,OAAO;AACf,kBAAQ,MAAM,UAAU,OAAO,MAAMF,UAAS,gBAAgB;AAAA,YAC5D;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC,IAAI,MAAM;AAAA,QACb;AACA,SAAC;AAAA,UACC,GAAAC;AAAA,UACA,GAAAC;AAAA,QACF,IAAI,2BAA2B,OAAO,mBAAmB,GAAG;AAAA,MAC9D;AACA,MAAAC,MAAI;AAAA,IACN;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAAF;AAAA,IACA,GAAAC;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;AAUA,eAAe,eAAe,OAAO,SAAS;AAC5C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,GAAAD;AAAA,IACA,GAAAC;AAAA,IACA,UAAAF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,IAAI,SAAS,SAAS,KAAK;AAC3B,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,aAAa,mBAAmB,aAAa,cAAc;AACjE,QAAM,UAAU,SAAS,cAAc,aAAa,cAAc;AAClE,QAAM,qBAAqB,iBAAiB,MAAMA,UAAS,gBAAgB;AAAA,IACzE,WAAW,wBAAwB,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,OAAO,OAAO,OAAO,wBAAwB,QAAQ,UAAU,QAAQ,kBAAmB,OAAOA,UAAS,sBAAsB,OAAO,SAASA,UAAS,mBAAmB,SAAS,QAAQ;AAAA,IAChS;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACF,QAAM,OAAO,mBAAmB,aAAa;AAAA,IAC3C,GAAAC;AAAA,IACA,GAAAC;AAAA,IACA,OAAO,MAAM,SAAS;AAAA,IACtB,QAAQ,MAAM,SAAS;AAAA,EACzB,IAAI,MAAM;AACV,QAAM,eAAe,OAAOF,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,SAAS,QAAQ;AAClH,QAAM,cAAe,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,YAAY,KAAO,OAAOA,UAAS,YAAY,OAAO,SAASA,UAAS,SAAS,YAAY,MAAO;AAAA,IACvL,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB,iBAAiBA,UAAS,wDAAwD,MAAMA,UAAS,sDAAsD;AAAA,IAC/K;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,IAAI,IAAI;AACT,SAAO;AAAA,IACL,MAAM,mBAAmB,MAAM,kBAAkB,MAAM,cAAc,OAAO,YAAY;AAAA,IACxF,SAAS,kBAAkB,SAAS,mBAAmB,SAAS,cAAc,UAAU,YAAY;AAAA,IACpG,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,cAAc,QAAQ,YAAY;AAAA,IAC5F,QAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc,SAAS,YAAY;AAAA,EAClG;AACF;AAOA,IAAM,QAAQ,cAAY;AAAA,EACxB,MAAM;AAAA,EACN;AAAA,EACA,MAAM,GAAG,OAAO;AACd,UAAM;AAAA,MACJ,GAAAC;AAAA,MACA,GAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,IACZ,IAAI,SAAS,SAAS,KAAK,KAAK,CAAC;AACjC,QAAI,WAAW,MAAM;AACnB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,UAAM,SAAS;AAAA,MACb,GAAAC;AAAA,MACA,GAAAC;AAAA,IACF;AACA,UAAM,OAAO,iBAAiB,SAAS;AACvC,UAAM,SAAS,cAAc,IAAI;AACjC,UAAM,kBAAkB,MAAMF,UAAS,cAAc,OAAO;AAC5D,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,UAAU,UAAU,WAAW;AACrC,UAAM,aAAa,UAAU,iBAAiB;AAC9C,UAAM,UAAU,MAAM,UAAU,MAAM,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,SAAS,MAAM;AACtG,UAAM,YAAY,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AACrD,UAAM,oBAAoB,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,OAAO;AAC7G,QAAI,aAAa,oBAAoB,kBAAkB,UAAU,IAAI;AAGrE,QAAI,CAAC,cAAc,CAAE,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,iBAAiB,IAAK;AACzG,mBAAa,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,MAAM;AAAA,IACrE;AACA,UAAM,oBAAoB,UAAU,IAAI,YAAY;AAIpD,UAAM,yBAAyB,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9E,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AACrE,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AAIrE,UAAM,QAAQ;AACd,UAAMI,OAAM,aAAa,gBAAgB,MAAM,IAAI;AACnD,UAAM,SAAS,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9D,UAAMC,UAAS,MAAM,OAAO,QAAQD,IAAG;AAMvC,UAAM,kBAAkB,CAAC,eAAe,SAAS,aAAa,SAAS,KAAK,QAAQ,WAAWC,WAAU,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,QAAQ,aAAa,cAAc,gBAAgB,MAAM,IAAI,IAAI;AAClN,UAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAASD,OAAM;AAC3F,WAAO;AAAA,MACL,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,MACvB,MAAM;AAAA,QACJ,CAAC,IAAI,GAAGC;AAAA,QACR,cAAc,SAASA,UAAS;AAAA,QAChC,GAAI,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,WAAW,eAAe,mBAAmB;AACrE,QAAM,qCAAqC,YAAY,CAAC,GAAG,kBAAkB,OAAO,eAAa,aAAa,SAAS,MAAM,SAAS,GAAG,GAAG,kBAAkB,OAAO,eAAa,aAAa,SAAS,MAAM,SAAS,CAAC,IAAI,kBAAkB,OAAO,eAAa,QAAQ,SAAS,MAAM,SAAS;AAClS,SAAO,mCAAmC,OAAO,eAAa;AAC5D,QAAI,WAAW;AACb,aAAO,aAAa,SAAS,MAAM,cAAc,gBAAgB,8BAA8B,SAAS,MAAM,YAAY;AAAA,IAC5H;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAOA,IAAM,gBAAgB,SAAU,SAAS;AACvC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB,wBAAwB;AACnD,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAL;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,YAAY;AAAA,QACZ;AAAA,QACA,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,eAAe,cAAc,UAAa,sBAAsB,aAAa,iBAAiB,aAAa,MAAM,eAAe,iBAAiB,IAAI;AAC3J,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,iBAAiB,wBAAwB,eAAe,kBAAkB,OAAO,SAAS,sBAAsB,UAAU;AAChI,YAAM,mBAAmB,aAAa,YAAY;AAClD,UAAI,oBAAoB,MAAM;AAC5B,eAAO,CAAC;AAAA,MACV;AACA,YAAM,iBAAiB,kBAAkB,kBAAkB,OAAO,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ,EAAE;AAG7I,UAAI,cAAc,kBAAkB;AAClC,eAAO;AAAA,UACL,OAAO;AAAA,YACL,WAAW,aAAa,CAAC;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AACA,YAAM,mBAAmB,CAAC,SAAS,QAAQ,gBAAgB,CAAC,GAAG,SAAS,eAAe,CAAC,CAAC,GAAG,SAAS,eAAe,CAAC,CAAC,CAAC;AACvH,YAAM,eAAe,CAAC,KAAM,yBAAyB,eAAe,kBAAkB,OAAO,SAAS,uBAAuB,cAAc,CAAC,GAAI;AAAA,QAC9I,WAAW;AAAA,QACX,WAAW;AAAA,MACb,CAAC;AACD,YAAM,gBAAgB,aAAa,eAAe,CAAC;AAGnD,UAAI,eAAe;AACjB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,OAAO,eAAe;AAAA,YACtB,WAAW;AAAA,UACb;AAAA,UACA,OAAO;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,YAAM,8BAA8B,aAAa,IAAI,CAAAM,QAAK;AACxD,cAAMC,aAAY,aAAaD,IAAE,SAAS;AAC1C,eAAO,CAACA,IAAE,WAAWC,cAAa;AAAA;AAAA,UAElCD,IAAE,UAAU,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,KAAKE,OAAM,MAAMA,IAAG,CAAC;AAAA;AAAA;AAAA,UAErDF,IAAE,UAAU,CAAC;AAAA,WAAGA,IAAE,SAAS;AAAA,MAC7B,CAAC,EAAE,KAAK,CAACG,IAAGC,OAAMD,GAAE,CAAC,IAAIC,GAAE,CAAC,CAAC;AAC7B,YAAM,8BAA8B,4BAA4B,OAAO,CAAAJ,QAAKA,IAAE,CAAC,EAAE;AAAA,QAAM;AAAA;AAAA;AAAA,QAGvF,aAAaA,IAAE,CAAC,CAAC,IAAI,IAAI;AAAA,MAAC,EAAE,MAAM,CAAAE,OAAKA,MAAK,CAAC,CAAC;AAC9C,YAAM,mBAAmB,wBAAwB,4BAA4B,CAAC,MAAM,OAAO,SAAS,sBAAsB,CAAC,MAAM,4BAA4B,CAAC,EAAE,CAAC;AACjK,UAAI,mBAAmB,WAAW;AAChC,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,OAAO,eAAe;AAAA,YACtB,WAAW;AAAA,UACb;AAAA,UACA,OAAO;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AAQA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAR;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,4BAA4B;AAAA,QAC5B,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAM3B,WAAK,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACnG,eAAO,CAAC;AAAA,MACV;AACA,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,kBAAkB,YAAY,gBAAgB;AACpD,YAAM,kBAAkB,QAAQ,gBAAgB,MAAM;AACtD,YAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,YAAM,qBAAqB,gCAAgC,mBAAmB,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,sBAAsB,gBAAgB;AAChL,YAAM,+BAA+B,8BAA8B;AACnE,UAAI,CAAC,+BAA+B,8BAA8B;AAChE,2BAAmB,KAAK,GAAG,0BAA0B,kBAAkB,eAAe,2BAA2B,GAAG,CAAC;AAAA,MACvH;AACA,YAAMW,cAAa,CAAC,kBAAkB,GAAG,kBAAkB;AAC3D,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,CAAC;AACnB,UAAI,kBAAkB,uBAAuB,eAAe,SAAS,OAAO,SAAS,qBAAqB,cAAc,CAAC;AACzH,UAAI,eAAe;AACjB,kBAAU,KAAK,SAAS,IAAI,CAAC;AAAA,MAC/B;AACA,UAAI,gBAAgB;AAClB,cAAMC,SAAQ,kBAAkB,WAAW,OAAO,GAAG;AACrD,kBAAU,KAAK,SAASA,OAAM,CAAC,CAAC,GAAG,SAASA,OAAM,CAAC,CAAC,CAAC;AAAA,MACvD;AACA,sBAAgB,CAAC,GAAG,eAAe;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AAGD,UAAI,CAAC,UAAU,MAAM,CAAAC,UAAQA,SAAQ,CAAC,GAAG;AACvC,YAAI,uBAAuB;AAC3B,cAAM,eAAe,wBAAwB,eAAe,SAAS,OAAO,SAAS,sBAAsB,UAAU,KAAK;AAC1H,cAAM,gBAAgBF,YAAW,SAAS;AAC1C,YAAI,eAAe;AAEjB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,YACA,OAAO;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAIA,YAAI,kBAAkB,wBAAwB,cAAc,OAAO,CAAAL,QAAKA,IAAE,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAACG,IAAGC,OAAMD,GAAE,UAAU,CAAC,IAAIC,GAAE,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB;AAG1L,YAAI,CAAC,gBAAgB;AACnB,kBAAQ,kBAAkB;AAAA,YACxB,KAAK,WACH;AACE,kBAAI;AACJ,oBAAMI,cAAa,yBAAyB,cAAc,OAAO,CAAAR,QAAK;AACpE,oBAAI,8BAA8B;AAChC,wBAAM,kBAAkB,YAAYA,IAAE,SAAS;AAC/C,yBAAO,oBAAoB;AAAA;AAAA,kBAG3B,oBAAoB;AAAA,gBACtB;AACA,uBAAO;AAAA,cACT,CAAC,EAAE,IAAI,CAAAA,QAAK,CAACA,IAAE,WAAWA,IAAE,UAAU,OAAO,CAAAS,cAAYA,YAAW,CAAC,EAAE,OAAO,CAAC,KAAKA,cAAa,MAAMA,WAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAACN,IAAGC,OAAMD,GAAE,CAAC,IAAIC,GAAE,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,uBAAuB,CAAC;AACjM,kBAAII,YAAW;AACb,iCAAiBA;AAAA,cACnB;AACA;AAAA,YACF;AAAA,YACF,KAAK;AACH,+BAAiB;AACjB;AAAA,UACJ;AAAA,QACF;AACA,YAAI,cAAc,gBAAgB;AAChC,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AAEA,SAAS,eAAe,UAAU,MAAM;AACtC,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK;AAAA,IACzB,OAAO,SAAS,QAAQ,KAAK;AAAA,IAC7B,QAAQ,SAAS,SAAS,KAAK;AAAA,IAC/B,MAAM,SAAS,OAAO,KAAK;AAAA,EAC7B;AACF;AACA,SAAS,sBAAsB,UAAU;AACvC,SAAO,MAAM,KAAK,UAAQ,SAAS,IAAI,KAAK,CAAC;AAC/C;AAMA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,WAAW;AAAA,QACX,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,cAAQ,UAAU;AAAA,QAChB,KAAK,mBACH;AACE,gBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,YAC3C,GAAG;AAAA,YACH,gBAAgB;AAAA,UAClB,CAAC;AACD,gBAAM,UAAU,eAAe,UAAU,MAAM,SAAS;AACxD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,wBAAwB;AAAA,cACxB,iBAAiB,sBAAsB,OAAO;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAAA,QACF,KAAK,WACH;AACE,gBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,YAC3C,GAAG;AAAA,YACH,aAAa;AAAA,UACf,CAAC;AACD,gBAAM,UAAU,eAAe,UAAU,MAAM,QAAQ;AACvD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,gBAAgB;AAAA,cAChB,SAAS,sBAAsB,OAAO;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,QACF,SACE;AACE,iBAAO,CAAC;AAAA,QACV;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;AAwIA,eAAe,qBAAqB,OAAO,SAAS;AAClD,QAAM;AAAA,IACJ;AAAA,IACA,UAAAE;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,YAAY,SAAS,MAAM;AAC9C,QAAM,gBAAgB,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK;AAC5D,QAAM,iBAAiB,OAAO,aAAa,KAAK;AAChD,QAAM,WAAW,SAAS,SAAS,KAAK;AAGxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO,aAAa,WAAW;AAAA,IACjC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,IAAI;AAAA,IACF,UAAU,SAAS,YAAY;AAAA,IAC/B,WAAW,SAAS,aAAa;AAAA,IACjC,eAAe,SAAS;AAAA,EAC1B;AACA,MAAI,aAAa,OAAO,kBAAkB,UAAU;AAClD,gBAAY,cAAc,QAAQ,gBAAgB,KAAK;AAAA,EACzD;AACA,SAAO,aAAa;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,WAAW;AAAA,EAChB,IAAI;AAAA,IACF,GAAG,WAAW;AAAA,IACd,GAAG,YAAY;AAAA,EACjB;AACF;AASA,IAAM,SAAS,SAAU,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ,GAAAC;AAAA,QACA,GAAAC;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,aAAa,MAAM,qBAAqB,OAAO,OAAO;AAI5D,UAAI,gBAAgB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,eAAe,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACzN,eAAO,CAAC;AAAA,MACV;AACA,aAAO;AAAA,QACL,GAAGD,KAAI,WAAW;AAAA,QAClB,GAAGC,MAAI,WAAW;AAAA,QAClB,MAAM;AAAA,UACJ,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,IAAM,QAAQ,SAAU,SAAS;AAC/B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ,GAAAD;AAAA,QACA,GAAAC;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,UAAU;AAAA,UACR,IAAI,UAAQ;AACV,gBAAI;AAAA,cACF,GAAAD;AAAA,cACA,GAAAC;AAAA,YACF,IAAI;AACJ,mBAAO;AAAA,cACL,GAAAD;AAAA,cACA,GAAAC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,SAAS;AAAA,QACb,GAAAD;AAAA,QACA,GAAAC;AAAA,MACF;AACA,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,YAAY,QAAQ,SAAS,CAAC;AAChD,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,gBAAgB,OAAO,QAAQ;AACnC,UAAI,iBAAiB,OAAO,SAAS;AACrC,UAAI,eAAe;AACjB,cAAM,UAAU,aAAa,MAAM,QAAQ;AAC3C,cAAM,UAAU,aAAa,MAAM,WAAW;AAC9C,cAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,cAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,wBAAgB,MAAMD,MAAK,eAAeC,IAAG;AAAA,MAC/C;AACA,UAAI,gBAAgB;AAClB,cAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,cAAM,UAAU,cAAc,MAAM,WAAW;AAC/C,cAAMD,OAAM,iBAAiB,SAAS,OAAO;AAC7C,cAAMC,OAAM,iBAAiB,SAAS,OAAO;AAC7C,yBAAiB,MAAMD,MAAK,gBAAgBC,IAAG;AAAA,MACjD;AACA,YAAM,gBAAgB,QAAQ,GAAG;AAAA,QAC/B,GAAG;AAAA,QACH,CAAC,QAAQ,GAAG;AAAA,QACZ,CAAC,SAAS,GAAG;AAAA,MACf,CAAC;AACD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,UACJ,GAAG,cAAc,IAAIH;AAAA,UACrB,GAAG,cAAc,IAAIC;AAAA,UACrB,SAAS;AAAA,YACP,CAAC,QAAQ,GAAG;AAAA,YACZ,CAAC,SAAS,GAAG;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACt3BA,SAAS,YAAY;AACnB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,MAAM;AACzB,MAAI,OAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,SAAS,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AAC5H;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQ,OAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAAS,OAAO,OAAO;AACrB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,QAAQ,iBAAiB,UAAU,KAAK,EAAE;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,WAAW,iBAAiB,UAAU,KAAK,EAAE;AACvE;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,eAAe,iBAAiB,UAAU,KAAK,EAAE;AAC3E;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,UAAU,KAAK,OAAO,eAAe,aAAa;AACrD,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiB,UAAU,KAAK,EAAE;AAC1E;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIG,kBAAiB,OAAO;AAC5B,SAAO,kCAAkC,KAAK,WAAW,YAAY,SAAS,KAAK,CAAC,CAAC,UAAU,UAAU,EAAE,SAAS,OAAO;AAC7H;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,SAAS,YAAY,OAAO,CAAC;AAC5D;AACA,SAAS,WAAW,SAAS;AAC3B,SAAO,CAAC,iBAAiB,QAAQ,EAAE,KAAK,cAAY;AAClD,QAAI;AACF,aAAO,QAAQ,QAAQ,QAAQ;AAAA,IACjC,SAASC,IAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,SAAS,kBAAkB,cAAc;AACvC,QAAM,SAAS,SAAS;AACxB,QAAM,MAAM,UAAU,YAAY,IAAID,kBAAiB,YAAY,IAAI;AAIvE,SAAO,CAAC,aAAa,aAAa,SAAS,UAAU,aAAa,EAAE,KAAK,WAAS,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,SAAS,KAAK,MAAM,IAAI,gBAAgB,IAAI,kBAAkB,WAAW,UAAU,CAAC,WAAW,IAAI,iBAAiB,IAAI,mBAAmB,SAAS,UAAU,CAAC,WAAW,IAAI,SAAS,IAAI,WAAW,SAAS,UAAU,CAAC,aAAa,aAAa,SAAS,UAAU,eAAe,QAAQ,EAAE,KAAK,YAAU,IAAI,cAAc,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,SAAS,UAAU,UAAU,SAAS,EAAE,KAAK,YAAU,IAAI,WAAW,IAAI,SAAS,KAAK,CAAC;AACniB;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,cAAc,cAAc,OAAO;AACvC,SAAO,cAAc,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACxE,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO;AAAA,IACT,WAAW,WAAW,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,MAAI,OAAO,QAAQ,eAAe,CAAC,IAAI,SAAU,QAAO;AACxD,SAAO,IAAI,SAAS,2BAA2B,MAAM;AACvD;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAS,YAAY,IAAI,CAAC;AACjE;AACA,SAASA,kBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,UAAU,OAAO,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,YAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAEL,aAAa,IAAI,KAAK,KAAK;AAAA,IAE3B,mBAAmB,IAAI;AAAA;AACvB,SAAO,aAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;AACA,SAAS,2BAA2B,MAAM;AACxC,QAAM,aAAa,cAAc,IAAI;AACrC,MAAI,sBAAsB,UAAU,GAAG;AACrC,WAAO,KAAK,gBAAgB,KAAK,cAAc,OAAO,KAAK;AAAA,EAC7D;AACA,MAAI,cAAc,UAAU,KAAK,kBAAkB,UAAU,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,UAAU;AAC9C;AACA,SAAS,qBAAqB,MAAM,MAAM,iBAAiB;AACzD,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,qBAAqB,2BAA2B,IAAI;AAC1D,QAAM,SAAS,yBAAyB,uBAAuB,KAAK,kBAAkB,OAAO,SAAS,qBAAqB;AAC3H,QAAM,MAAM,UAAU,kBAAkB;AACxC,MAAI,QAAQ;AACV,UAAM,eAAe,gBAAgB,GAAG;AACxC,WAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,GAAG,kBAAkB,kBAAkB,IAAI,qBAAqB,CAAC,GAAG,gBAAgB,kBAAkB,qBAAqB,YAAY,IAAI,CAAC,CAAC;AAAA,EAC9L;AACA,SAAO,KAAK,OAAO,oBAAoB,qBAAqB,oBAAoB,CAAC,GAAG,eAAe,CAAC;AACtG;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,UAAU,OAAO,eAAe,IAAI,MAAM,IAAI,IAAI,eAAe;AAC9E;;;AClJA,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAME,kBAAiB,OAAO;AAGpC,MAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;AACrC,MAAI,SAAS,WAAW,IAAI,MAAM,KAAK;AACvC,QAAM,YAAY,cAAc,OAAO;AACvC,QAAM,cAAc,YAAY,QAAQ,cAAc;AACtD,QAAM,eAAe,YAAY,QAAQ,eAAe;AACxD,QAAM,iBAAiB,MAAM,KAAK,MAAM,eAAe,MAAM,MAAM,MAAM;AACzE,MAAI,gBAAgB;AAClB,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,CAAC,UAAU,OAAO,IAAI,QAAQ,iBAAiB;AACxD;AAEA,SAAS,SAAS,SAAS;AACzB,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,WAAO,aAAa,CAAC;AAAA,EACvB;AACA,QAAM,OAAO,WAAW,sBAAsB;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,GAAAC;AAAA,EACF,IAAI,iBAAiB,UAAU;AAC/B,MAAIC,MAAKD,KAAI,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AAC/C,MAAIE,OAAKF,KAAI,MAAM,KAAK,MAAM,IAAI,KAAK,UAAU;AAIjD,MAAI,CAACC,MAAK,CAAC,OAAO,SAASA,EAAC,GAAG;AAC7B,IAAAA,KAAI;AAAA,EACN;AACA,MAAI,CAACC,OAAK,CAAC,OAAO,SAASA,GAAC,GAAG;AAC7B,IAAAA,MAAI;AAAA,EACN;AACA,SAAO;AAAA,IACL,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF;AACF;AAEA,IAAM,YAAyB,aAAa,CAAC;AAC7C,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,CAAC,SAAS,KAAK,CAAC,IAAI,gBAAgB;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG,IAAI,eAAe;AAAA,IACtB,GAAG,IAAI,eAAe;AAAA,EACxB;AACF;AACA,SAAS,uBAAuB,SAAS,SAAS,sBAAsB;AACtE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,CAAC,wBAAwB,WAAW,yBAAyB,UAAU,OAAO,GAAG;AACnF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,SAAS,cAAc,iBAAiB,cAAc;AACnF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,aAAa,QAAQ,sBAAsB;AACjD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,QAAQ,aAAa,CAAC;AAC1B,MAAI,cAAc;AAChB,QAAI,cAAc;AAChB,UAAI,UAAU,YAAY,GAAG;AAC3B,gBAAQ,SAAS,YAAY;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,cAAQ,SAAS,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,YAAY,IAAI,iBAAiB,UAAU,IAAI,aAAa,CAAC;AACvI,MAAID,MAAK,WAAW,OAAO,cAAc,KAAK,MAAM;AACpD,MAAIC,OAAK,WAAW,MAAM,cAAc,KAAK,MAAM;AACnD,MAAI,QAAQ,WAAW,QAAQ,MAAM;AACrC,MAAI,SAAS,WAAW,SAAS,MAAM;AACvC,MAAI,YAAY;AACd,UAAM,MAAM,UAAU,UAAU;AAChC,UAAM,YAAY,gBAAgB,UAAU,YAAY,IAAI,UAAU,YAAY,IAAI;AACtF,QAAI,aAAa;AACjB,QAAI,gBAAgB,gBAAgB,UAAU;AAC9C,WAAO,iBAAiB,gBAAgB,cAAc,YAAY;AAChE,YAAM,cAAc,SAAS,aAAa;AAC1C,YAAM,aAAa,cAAc,sBAAsB;AACvD,YAAM,MAAMH,kBAAiB,aAAa;AAC1C,YAAM,OAAO,WAAW,QAAQ,cAAc,aAAa,WAAW,IAAI,WAAW,KAAK,YAAY;AACtG,YAAM,MAAM,WAAW,OAAO,cAAc,YAAY,WAAW,IAAI,UAAU,KAAK,YAAY;AAClG,MAAAE,MAAK,YAAY;AACjB,MAAAC,OAAK,YAAY;AACjB,eAAS,YAAY;AACrB,gBAAU,YAAY;AACtB,MAAAD,MAAK;AACL,MAAAC,OAAK;AACL,mBAAa,UAAU,aAAa;AACpC,sBAAgB,gBAAgB,UAAU;AAAA,IAC5C;AAAA,EACF;AACA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF,CAAC;AACH;AAIA,SAAS,oBAAoB,SAAS,MAAM;AAC1C,QAAM,aAAa,cAAc,OAAO,EAAE;AAC1C,MAAI,CAAC,MAAM;AACT,WAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO;AAAA,EACnE;AACA,SAAO,KAAK,OAAO;AACrB;AAEA,SAAS,cAAc,iBAAiB,QAAQ,kBAAkB;AAChE,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,EACrB;AACA,QAAM,WAAW,gBAAgB,sBAAsB;AACvD,QAAMD,KAAI,SAAS,OAAO,OAAO,cAAc,mBAAmB;AAAA;AAAA,IAElE,oBAAoB,iBAAiB,QAAQ;AAAA;AAC7C,QAAMC,MAAI,SAAS,MAAM,OAAO;AAChC,SAAO;AAAA,IACL,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF;AACF;AAEA,SAAS,sDAAsD,MAAM;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,aAAa;AAC7B,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,WAAW,WAAW,WAAW,SAAS,QAAQ,IAAI;AAC5D,MAAI,iBAAiB,mBAAmB,YAAY,SAAS;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,QAAQ,aAAa,CAAC;AAC1B,QAAM,UAAU,aAAa,CAAC;AAC9B,QAAM,0BAA0B,cAAc,YAAY;AAC1D,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,cAAc,YAAY,GAAG;AAC/B,YAAM,aAAa,sBAAsB,YAAY;AACrD,cAAQ,SAAS,YAAY;AAC7B,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,QAAQ,IAAI,IAAI,aAAa,CAAC;AAC1I,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ,MAAM;AAAA,IAC1B,QAAQ,KAAK,SAAS,MAAM;AAAA,IAC5B,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,aAAa,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,IAC3E,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,YAAY,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,EAC5E;AACF;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,MAAM,KAAK,QAAQ,eAAe,CAAC;AAC5C;AAIA,SAAS,gBAAgB,SAAS;AAChC,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,SAAS,cAAc,OAAO;AACpC,QAAM,OAAO,QAAQ,cAAc;AACnC,QAAM,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,WAAW;AACxF,QAAM,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,YAAY;AAC7F,MAAID,KAAI,CAAC,OAAO,aAAa,oBAAoB,OAAO;AACxD,QAAMC,MAAI,CAAC,OAAO;AAClB,MAAIH,kBAAiB,IAAI,EAAE,cAAc,OAAO;AAC9C,IAAAE,MAAK,IAAI,KAAK,aAAa,KAAK,WAAW,IAAI;AAAA,EACjD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAAA;AAAA,IACA,GAAAC;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,iBAAiB,IAAI;AAC3B,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAID,KAAI;AACR,MAAIC,MAAI;AACR,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,UAAM,sBAAsB,SAAS;AACrC,QAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;AACvE,MAAAD,KAAI,eAAe;AACnB,MAAAC,MAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF;AACF;AAGA,SAAS,2BAA2B,SAAS,UAAU;AACrD,QAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa,OAAO;AAC5E,QAAM,MAAM,WAAW,MAAM,QAAQ;AACrC,QAAM,OAAO,WAAW,OAAO,QAAQ;AACvC,QAAM,QAAQ,cAAc,OAAO,IAAI,SAAS,OAAO,IAAI,aAAa,CAAC;AACzE,QAAM,QAAQ,QAAQ,cAAc,MAAM;AAC1C,QAAM,SAAS,QAAQ,eAAe,MAAM;AAC5C,QAAMD,KAAI,OAAO,MAAM;AACvB,QAAMC,MAAI,MAAM,MAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAAD;AAAA,IACA,GAAAC;AAAA,EACF;AACF;AACA,SAAS,kCAAkC,SAAS,kBAAkB,UAAU;AAC9E,MAAI;AACJ,MAAI,qBAAqB,YAAY;AACnC,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C,WAAW,qBAAqB,YAAY;AAC1C,WAAO,gBAAgB,mBAAmB,OAAO,CAAC;AAAA,EACpD,WAAW,UAAU,gBAAgB,GAAG;AACtC,WAAO,2BAA2B,kBAAkB,QAAQ;AAAA,EAC9D,OAAO;AACL,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,WAAO;AAAA,MACL,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,OAAO,iBAAiB;AAAA,MACxB,QAAQ,iBAAiB;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,iBAAiB,IAAI;AAC9B;AACA,SAAS,yBAAyB,SAAS,UAAU;AACnD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,eAAe,YAAY,CAAC,UAAU,UAAU,KAAK,sBAAsB,UAAU,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,SAAOH,kBAAiB,UAAU,EAAE,aAAa,WAAW,yBAAyB,YAAY,QAAQ;AAC3G;AAKA,SAAS,4BAA4B,SAAS,OAAO;AACnD,QAAM,eAAe,MAAM,IAAI,OAAO;AACtC,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,qBAAqB,SAAS,CAAC,GAAG,KAAK,EAAE,OAAO,QAAM,UAAU,EAAE,KAAK,YAAY,EAAE,MAAM,MAAM;AAC9G,MAAI,sCAAsC;AAC1C,QAAM,iBAAiBA,kBAAiB,OAAO,EAAE,aAAa;AAC9D,MAAI,cAAc,iBAAiB,cAAc,OAAO,IAAI;AAG5D,SAAO,UAAU,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACpE,UAAM,gBAAgBA,kBAAiB,WAAW;AAClD,UAAM,0BAA0B,kBAAkB,WAAW;AAC7D,QAAI,CAAC,2BAA2B,cAAc,aAAa,SAAS;AAClE,4CAAsC;AAAA,IACxC;AACA,UAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,aAAa,YAAY,CAAC,CAAC,uCAAuC,CAAC,YAAY,OAAO,EAAE,SAAS,oCAAoC,QAAQ,KAAK,kBAAkB,WAAW,KAAK,CAAC,2BAA2B,yBAAyB,SAAS,WAAW;AACzZ,QAAI,uBAAuB;AAEzB,eAAS,OAAO,OAAO,cAAY,aAAa,WAAW;AAAA,IAC7D,OAAO;AAEL,4CAAsC;AAAA,IACxC;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,QAAM,IAAI,SAAS,MAAM;AACzB,SAAO;AACT;AAIA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,2BAA2B,aAAa,sBAAsB,WAAW,OAAO,IAAI,CAAC,IAAI,4BAA4B,SAAS,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;AACjK,QAAM,oBAAoB,CAAC,GAAG,0BAA0B,YAAY;AACpE,QAAM,wBAAwB,kBAAkB,CAAC;AACjD,QAAM,eAAe,kBAAkB,OAAO,CAAC,SAAS,qBAAqB;AAC3E,UAAM,OAAO,kCAAkC,SAAS,kBAAkB,QAAQ;AAClF,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,kCAAkC,SAAS,uBAAuB,QAAQ,CAAC;AAC9E,SAAO;AAAA,IACL,OAAO,aAAa,QAAQ,aAAa;AAAA,IACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,IAC3C,GAAG,aAAa;AAAA,IAChB,GAAG,aAAa;AAAA,EAClB;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,SAAS,cAAc,UAAU;AACtE,QAAM,0BAA0B,cAAc,YAAY;AAC1D,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,UAAU,aAAa;AAC7B,QAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS,YAAY;AACvE,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,QAAM,UAAU,aAAa,CAAC;AAC9B,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,yBAAyB;AAC3B,YAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS,YAAY;AAClF,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C,WAAW,iBAAiB;AAG1B,cAAQ,IAAI,oBAAoB,eAAe;AAAA,IACjD;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,MAAM,IAAI,aAAa,CAAC;AACpI,QAAME,KAAI,KAAK,OAAO,OAAO,aAAa,QAAQ,IAAI,WAAW;AACjE,QAAMC,MAAI,KAAK,MAAM,OAAO,YAAY,QAAQ,IAAI,WAAW;AAC/D,SAAO;AAAA,IACL,GAAAD;AAAA,IACA,GAAAC;AAAA,IACA,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AAEA,SAAS,mBAAmB,SAAS;AACnC,SAAOH,kBAAiB,OAAO,EAAE,aAAa;AAChD;AAEA,SAAS,oBAAoB,SAAS,UAAU;AAC9C,MAAI,CAAC,cAAc,OAAO,KAAKA,kBAAiB,OAAO,EAAE,aAAa,SAAS;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACZ,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,MAAI,kBAAkB,QAAQ;AAM9B,MAAI,mBAAmB,OAAO,MAAM,iBAAiB;AACnD,sBAAkB,gBAAgB,cAAc;AAAA,EAClD;AACA,SAAO;AACT;AAIA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,OAAO,GAAG;AAC3B,QAAI,kBAAkB,cAAc,OAAO;AAC3C,WAAO,mBAAmB,CAAC,sBAAsB,eAAe,GAAG;AACjE,UAAI,UAAU,eAAe,KAAK,CAAC,mBAAmB,eAAe,GAAG;AACtE,eAAO;AAAA,MACT;AACA,wBAAkB,cAAc,eAAe;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACA,MAAI,eAAe,oBAAoB,SAAS,QAAQ;AACxD,SAAO,gBAAgB,eAAe,YAAY,KAAK,mBAAmB,YAAY,GAAG;AACvF,mBAAe,oBAAoB,cAAc,QAAQ;AAAA,EAC3D;AACA,MAAI,gBAAgB,sBAAsB,YAAY,KAAK,mBAAmB,YAAY,KAAK,CAAC,kBAAkB,YAAY,GAAG;AAC/H,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,mBAAmB,OAAO,KAAK;AACxD;AAEA,IAAM,kBAAkB,eAAgB,MAAM;AAC5C,QAAM,oBAAoB,KAAK,mBAAmB;AAClD,QAAM,kBAAkB,KAAK;AAC7B,QAAM,qBAAqB,MAAM,gBAAgB,KAAK,QAAQ;AAC9D,SAAO;AAAA,IACL,WAAW,8BAA8B,KAAK,WAAW,MAAM,kBAAkB,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,IAC9G,UAAU;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO,mBAAmB;AAAA,MAC1B,QAAQ,mBAAmB;AAAA,IAC7B;AAAA,EACF;AACF;AAEA,SAAS,MAAM,SAAS;AACtB,SAAOA,kBAAiB,OAAO,EAAE,cAAc;AACjD;AAEA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,cAAcI,IAAGC,IAAG;AAC3B,SAAOD,GAAE,MAAMC,GAAE,KAAKD,GAAE,MAAMC,GAAE,KAAKD,GAAE,UAAUC,GAAE,SAASD,GAAE,WAAWC,GAAE;AAC7E;AAGA,SAAS,YAAY,SAAS,QAAQ;AACpC,MAAI,KAAK;AACT,MAAI;AACJ,QAAM,OAAO,mBAAmB,OAAO;AACvC,WAAS,UAAU;AACjB,QAAI;AACJ,iBAAa,SAAS;AACtB,KAAC,MAAM,OAAO,QAAQ,IAAI,WAAW;AACrC,SAAK;AAAA,EACP;AACA,WAAS,QAAQ,MAAM,WAAW;AAChC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,YAAQ;AACR,UAAM,2BAA2B,QAAQ,sBAAsB;AAC/D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,CAAC,QAAQ;AACrB;AAAA,IACF;AACA,UAAM,WAAW,MAAM,GAAG;AAC1B,UAAM,aAAa,MAAM,KAAK,eAAe,OAAO,MAAM;AAC1D,UAAM,cAAc,MAAM,KAAK,gBAAgB,MAAM,OAAO;AAC5D,UAAM,YAAY,MAAM,IAAI;AAC5B,UAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;AACjG,UAAM,UAAU;AAAA,MACd;AAAA,MACA,WAAW,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC,KAAK;AAAA,IAC1C;AACA,QAAI,gBAAgB;AACpB,aAAS,cAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,UAAI,UAAU,WAAW;AACvB,YAAI,CAAC,eAAe;AAClB,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,CAAC,OAAO;AAGV,sBAAY,WAAW,MAAM;AAC3B,oBAAQ,OAAO,IAAI;AAAA,UACrB,GAAG,GAAI;AAAA,QACT,OAAO;AACL,kBAAQ,OAAO,KAAK;AAAA,QACtB;AAAA,MACF;AACA,UAAI,UAAU,KAAK,CAAC,cAAc,0BAA0B,QAAQ,sBAAsB,CAAC,GAAG;AAQ5F,gBAAQ;AAAA,MACV;AACA,sBAAgB;AAAA,IAClB;AAIA,QAAI;AACF,WAAK,IAAI,qBAAqB,eAAe;AAAA,QAC3C,GAAG;AAAA;AAAA,QAEH,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH,SAASC,IAAG;AACV,WAAK,IAAI,qBAAqB,eAAe,OAAO;AAAA,IACtD;AACA,OAAG,QAAQ,OAAO;AAAA,EACpB;AACA,UAAQ,IAAI;AACZ,SAAO;AACT;AAUA,SAAS,WAAW,WAAW,UAAU,QAAQ,SAAS;AACxD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,gBAAgB,OAAO,mBAAmB;AAAA,IAC1C,cAAc,OAAO,yBAAyB;AAAA,IAC9C,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAM,cAAc,cAAc,SAAS;AAC3C,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,GAAI,cAAc,qBAAqB,WAAW,IAAI,CAAC,GAAI,GAAG,qBAAqB,QAAQ,CAAC,IAAI,CAAC;AACvJ,YAAU,QAAQ,cAAY;AAC5B,sBAAkB,SAAS,iBAAiB,UAAU,QAAQ;AAAA,MAC5D,SAAS;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS,iBAAiB,UAAU,MAAM;AAAA,EAC9D,CAAC;AACD,QAAM,YAAY,eAAe,cAAc,YAAY,aAAa,MAAM,IAAI;AAClF,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACjB,qBAAiB,IAAI,eAAe,UAAQ;AAC1C,UAAI,CAAC,UAAU,IAAI;AACnB,UAAI,cAAc,WAAW,WAAW,eAAe,gBAAgB;AAGrE,uBAAe,UAAU,QAAQ;AACjC,6BAAqB,cAAc;AACnC,yBAAiB,sBAAsB,MAAM;AAC3C,cAAI;AACJ,WAAC,kBAAkB,mBAAmB,QAAQ,gBAAgB,QAAQ,QAAQ;AAAA,QAChF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,eAAe,CAAC,gBAAgB;AAClC,qBAAe,QAAQ,WAAW;AAAA,IACpC;AACA,mBAAe,QAAQ,QAAQ;AAAA,EACjC;AACA,MAAI;AACJ,MAAI,cAAc,iBAAiB,sBAAsB,SAAS,IAAI;AACtE,MAAI,gBAAgB;AAClB,cAAU;AAAA,EACZ;AACA,WAAS,YAAY;AACnB,UAAM,cAAc,sBAAsB,SAAS;AACnD,QAAI,eAAe,CAAC,cAAc,aAAa,WAAW,GAAG;AAC3D,aAAO;AAAA,IACT;AACA,kBAAc;AACd,cAAU,sBAAsB,SAAS;AAAA,EAC3C;AACA,SAAO;AACP,SAAO,MAAM;AACX,QAAI;AACJ,cAAU,QAAQ,cAAY;AAC5B,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAC/D,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAAA,IACjE,CAAC;AACD,iBAAa,QAAQ,UAAU;AAC/B,KAAC,mBAAmB,mBAAmB,QAAQ,iBAAiB,WAAW;AAC3E,qBAAiB;AACjB,QAAI,gBAAgB;AAClB,2BAAqB,OAAO;AAAA,IAC9B;AAAA,EACF;AACF;AAmBA,IAAMC,UAAS;AAQf,IAAMC,iBAAgB;AAOtB,IAAMC,SAAQ;AAQd,IAAMC,QAAO;AAeb,IAAMC,QAAO;AAOb,IAAMC,SAAQ;AAkBd,IAAMC,mBAAkB,CAAC,WAAW,UAAU,YAAY;AAIxD,QAAM,QAAQ,oBAAI,IAAI;AACtB,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB;AAAA,IACxB,GAAG,cAAc;AAAA,IACjB,IAAI;AAAA,EACN;AACA,SAAO,gBAAkB,WAAW,UAAU;AAAA,IAC5C,GAAG;AAAA,IACH,UAAU;AAAA,EACZ,CAAC;AACH;;;AC1uBA,SAAS,SAAAC,QAAO,YAAAC,WAAU,KAAK,cAAAC,aAAY,SAAAC,QAAO,iBAAiB,kBAAAC,iBAAgB,uBAAuB;AAE1G,SAAS,0BAA0B,QAAQ;AACzC,SAAO,UAAU,QAAQ,OAAO,WAAW,YAAY,SAAS;AAClE;AACA,SAASC,eAAc,QAAQ;AAC7B,MAAI,0BAA0B,MAAM,GAAG;AACrC,UAAM,UAAU,OAAO;AACvB,WAAO,OAAO,OAAO,KAAK,YAAY,OAAO,MAAM,aAAa,OAAO;AAAA,EACzE;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,QAAQ;AACvB,SAAO,OAAO,WAAW,aAAa,OAAO,IAAIL,OAAM,MAAM;AAC/D;AAOA,SAASM,OAAM,SAAS;AACtB,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,GAAG,MAAM;AACP,YAAM,UAAUD,eAAc,QAAQ,QAAQ,OAAO,CAAC;AACtD,UAAI,WAAW,MAAM;AACnB,eAAO,CAAC;AAAA,MACV;AACA,aAAOC,OAAQ;AAAA,QACb;AAAA,QACA,SAAS,QAAQ;AAAA,MACnB,CAAC,EAAE,GAAG,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,QAAQ,cAAc,eAAe;AACjD,SAAO,IAAI,oBAAoB;AACjC;AAEA,SAAS,WAAW,SAAS,OAAO;AAClC,QAAM,MAAM,OAAO,OAAO;AAC1B,SAAO,KAAK,MAAM,QAAQ,GAAG,IAAI;AACnC;AASA,SAAS,YAAY,WAAW,UAAU,SAAS;AACjD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM,6BAA6B,QAAQ;AAC3C,QAAM,aAAaL,UAAS,MAAM;AAChC,QAAI;AACJ,YAAQ,WAAW,QAAQ,QAAQ,IAAI,MAAM,OAAO,WAAW;AAAA,EACjE,CAAC;AACD,QAAM,mBAAmBA,UAAS,MAAM,QAAQ,QAAQ,UAAU,CAAC;AACnE,QAAM,kBAAkBA,UAAS,MAAM;AACrC,QAAI;AACJ,YAAQ,YAAY,QAAQ,QAAQ,SAAS,MAAM,OAAO,YAAY;AAAA,EACxE,CAAC;AACD,QAAM,iBAAiBA,UAAS,MAAM;AACpC,QAAI;AACJ,YAAQ,YAAY,QAAQ,QAAQ,QAAQ,MAAM,OAAO,YAAY;AAAA,EACvE,CAAC;AACD,QAAM,kBAAkBA,UAAS,MAAM;AACrC,QAAI;AACJ,YAAQ,YAAY,QAAQ,QAAQ,SAAS,MAAM,OAAO,YAAY;AAAA,EACxE,CAAC;AACD,QAAM,mBAAmBA,UAAS,MAAMI,eAAc,UAAU,KAAK,CAAC;AACtE,QAAM,kBAAkBJ,UAAS,MAAMI,eAAc,SAAS,KAAK,CAAC;AACpE,QAAME,KAAI,IAAI,CAAC;AACf,QAAMC,MAAI,IAAI,CAAC;AACf,QAAM,WAAW,IAAI,eAAe,KAAK;AACzC,QAAM,YAAY,IAAI,gBAAgB,KAAK;AAC3C,QAAM,iBAAiBN,YAAW,CAAC,CAAC;AACpC,QAAM,eAAe,IAAI,KAAK;AAC9B,QAAM,iBAAiBD,UAAS,MAAM;AACpC,UAAM,gBAAgB;AAAA,MACpB,UAAU,SAAS;AAAA,MACnB,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AACA,QAAI,CAAC,gBAAgB,OAAO;AAC1B,aAAO;AAAA,IACT;AACA,UAAM,OAAO,WAAW,gBAAgB,OAAOM,GAAE,KAAK;AACtD,UAAM,OAAO,WAAW,gBAAgB,OAAOC,IAAE,KAAK;AACtD,QAAI,gBAAgB,OAAO;AACzB,aAAO;AAAA,QACL,GAAG;AAAA,QACH,WAAW,eAAe,OAAO,SAAS,OAAO;AAAA,QACjD,GAAI,OAAO,gBAAgB,KAAK,KAAK,OAAO;AAAA,UAC1C,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU,SAAS;AAAA,MACnB,MAAM,OAAO;AAAA,MACb,KAAK,OAAO;AAAA,IACd;AAAA,EACF,CAAC;AACD,MAAI;AACJ,WAAS,SAAS;AAChB,QAAI,iBAAiB,SAAS,QAAQ,gBAAgB,SAAS,MAAM;AACnE;AAAA,IACF;AACA,UAAM,OAAO,WAAW;AACxB,IAAAC,iBAAgB,iBAAiB,OAAO,gBAAgB,OAAO;AAAA,MAC7D,YAAY,iBAAiB;AAAA,MAC7B,WAAW,gBAAgB;AAAA,MAC3B,UAAU,eAAe;AAAA,IAC3B,CAAC,EAAE,KAAK,cAAY;AAClB,MAAAF,GAAE,QAAQ,SAAS;AACnB,MAAAC,IAAE,QAAQ,SAAS;AACnB,eAAS,QAAQ,SAAS;AAC1B,gBAAU,QAAQ,SAAS;AAC3B,qBAAe,QAAQ,SAAS;AAOhC,mBAAa,QAAQ,SAAS;AAAA,IAChC,CAAC;AAAA,EACH;AACA,WAAS,UAAU;AACjB,QAAI,OAAO,gCAAgC,YAAY;AACrD,kCAA4B;AAC5B,oCAA8B;AAAA,IAChC;AAAA,EACF;AACA,WAAS,SAAS;AAChB,YAAQ;AACR,QAAI,+BAA+B,QAAW;AAC5C,aAAO;AACP;AAAA,IACF;AACA,QAAI,iBAAiB,SAAS,QAAQ,gBAAgB,SAAS,MAAM;AACnE,oCAA8B,2BAA2B,iBAAiB,OAAO,gBAAgB,OAAO,MAAM;AAC9G;AAAA,IACF;AAAA,EACF;AACA,WAAS,QAAQ;AACf,QAAI,CAAC,WAAW,OAAO;AACrB,mBAAa,QAAQ;AAAA,IACvB;AAAA,EACF;AACA,EAAAL,OAAM,CAAC,kBAAkB,iBAAiB,gBAAgB,UAAU,GAAG,QAAQ;AAAA,IAC7E,OAAO;AAAA,EACT,CAAC;AACD,EAAAA,OAAM,CAAC,kBAAkB,eAAe,GAAG,QAAQ;AAAA,IACjD,OAAO;AAAA,EACT,CAAC;AACD,EAAAA,OAAM,YAAY,OAAO;AAAA,IACvB,OAAO;AAAA,EACT,CAAC;AACD,MAAI,gBAAgB,GAAG;AACrB,IAAAC,gBAAe,OAAO;AAAA,EACxB;AACA,SAAO;AAAA,IACL,GAAG,gBAAgBG,EAAC;AAAA,IACpB,GAAG,gBAAgBC,GAAC;AAAA,IACpB,UAAU,gBAAgB,QAAQ;AAAA,IAClC,WAAW,gBAAgB,SAAS;AAAA,IACpC,gBAAgB,gBAAgB,cAAc;AAAA,IAC9C,cAAc,gBAAgB,YAAY;AAAA,IAC1C;AAAA,IACA;AAAA,EACF;AACF;;;AnE3LA,IAAIE,MAAK,OAAO;AAChB,IAAIC,MAAK,CAACC,IAAGC,IAAGC,QAAMD,MAAKD,KAAIF,IAAGE,IAAGC,IAAG,EAAE,YAAY,MAAI,cAAc,MAAI,UAAU,MAAI,OAAOC,IAAE,CAAC,IAAIF,GAAEC,EAAC,IAAIC;AAC/G,IAAIC,KAAI,CAACH,IAAGC,IAAGC,QAAMH,IAAGC,IAAG,OAAOC,MAAK,WAAWA,KAAI,KAAKA,IAAGC,GAAC;AAK/D,SAASE,GAAEJ,IAAG;AACZ,SAAOA,MAAK,QAAQA,GAAE,SAAS,OAAO,OAAOA,GAAE,iBAAiB,OAAOA,GAAE,QAAQ,SAASA,GAAE,SAASA,GAAE,MAAM,MAAMI,GAAEC,GAAEL,GAAE,MAAM,GAAG,CAAC,IAAI,2BAA2BA,GAAE,QAAQA,GAAE,QAAQ;AACxL;AACA,SAAS,GAAGA,IAAG;AACb,SAAO,OAAO,SAAS,MAAM,KAAKA,GAAE,cAAc,eAAe,QAAQ,oBAAoB;AAC/F;AACA,SAASM,IAAEN,IAAGC,IAAG;AACf,QAAMC,MAAI,GAAGF,EAAC;AACd,SAAO,KAAK,MAAMC,KAAIC,GAAC,IAAIA;AAC7B;AACA,SAASK,GAAEP,IAAG;AACZ,SAAOA,GAAE,OAAO,CAACC,IAAGC,QAAMA,IAAE,SAASM,MAAKP,GAAE,OAAOM,GAAEL,IAAE,QAAQ,CAAC,IAAID,GAAE,OAAOC,GAAC,GAAG,CAAC,CAAC;AACrF;AACA,SAASO,GAAET,IAAG;AACZ,SAAOA,MAAK,OAAO,QAAK,OAAOA,GAAE,QAAQ,YAAY,OAAOA,GAAE,QAAQ,YAAY,OAAOA,GAAE,QAAQ;AACrG;AACA,SAASU,GAAEV,IAAG;AACZ,SAAOA,KAAI,GAAGA,EAAC,GAAGA,OAAMA,MAAK,OAAO,SAASA,GAAE,cAAc,KAAK;AACpE;AACA,IAAMW,MAAN,MAAS;AAAA,EACP,cAAc;AACZ,IAAAR,GAAE,MAAM,WAAW,KAAK,OAAO,CAAC;AAAA,EAClC;AAAA,EACA,IAAIF,IAAG;AACL,SAAK,YAAYA,OAAM,KAAK,UAAUA;AAAA,EACxC;AAAA,EACA,QAAQ;AACN,SAAK,IAAI,KAAK,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,SAAS;AACP,WAAO,OAAO,SAAS,OAAO,OAAO,WAAW,MAAM,WAAW;AAAA,EACnE;AACF;AACA,IAAMW,KAAI,IAAID,IAAG;AACjB,SAASE,IAAGb,IAAG;AACb,MAAIY,GAAE,SAAU,QAAO;AACvB,MAAIZ,cAAa,KAAM,QAAOA,GAAE;AAChC,MAAIA,MAAK,OAAO,UAAU,eAAe,KAAKA,IAAG,OAAO,GAAG;AACzD,UAAMC,KAAIG,GAAEJ,EAAC;AACb,QAAIC,GAAG,QAAOA,GAAE;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAASa,GAAEd,IAAGC,IAAG;AACf,GAACA,GAAE,kBAAkBA,GAAE,kBAAkBA,GAAE,mBAAmB,QAAQ,KAAK,wBAAwBD,EAAC,0FAA0F;AAChM;AACA,SAAS,GAAGA,IAAGC,IAAGC,KAAGa,IAAGC,IAAG;AACzB,EAAAC,GAAE;AAAA,IACA,MAAMD,GAAE;AAAA,IACR,MAAMA,GAAE;AAAA,IACR,MAAMA,GAAE;AAAA,IACR,MAAMA,GAAE;AAAA,IACR,MAAMA,GAAE;AAAA,IACR,MAAMA,GAAE;AAAA,IACR,MAAMA,GAAE;AAAA,EACV,GAAG,MAAM;AACP,UAAME,MAAI,CAAC;AACX,KAAC,OAAOF,GAAE,UAAU,YAAY,OAAOA,GAAE,UAAU,YAAY,OAAOA,GAAE,UAAU,eAAeE,IAAE,KAAKC,QAAGH,GAAE,MAAM,CAAC,IAAIA,GAAE,SAAS,QAAM,OAAOA,GAAE,QAAQ,YAAY,OAAOA,GAAE,QAAQ,aAAaE,IAAE,KAAKE,MAAG;AAAA,MAC5M,SAAS,OAAOJ,GAAE,QAAQ,WAAWA,GAAE,OAAO;AAAA,MAC9C,GAAG,OAAOA,GAAE,QAAQ,WAAWA,GAAE,OAAO,CAAC;AAAA,IAC3C,CAAC,CAAC,IAAIA,GAAE,UAAU,QAAM,OAAOA,GAAE,SAAS,YAAY,OAAOA,GAAE,SAAS,aAAaE,IAAE,KAAKG,OAAG;AAAA,MAC7F,SAAS,OAAOL,GAAE,SAAS,WAAWA,GAAE,QAAQ;AAAA,MAChD,GAAG,OAAOA,GAAE,SAAS,WAAWA,GAAE,QAAQ,CAAC;AAAA,IAC7C,CAAC,CAAC,IAAIA,GAAE,kBAAkB,QAAM,OAAOA,GAAE,iBAAiB,aAAaE,IAAE,KAAKI;AAAA,MAC5E,OAAON,GAAE,iBAAiB,WAAWA,GAAE,gBAAgB;AAAA,IACzD,CAAC,GAAGE,IAAE,KAAK,GAAG,OAAOF,GAAE,cAAc,aAAaA,GAAE,WAAW;AAAA,MAC7D,aAAaf;AAAA,MACb,YAAYC;AAAA,IACd,CAAC,IAAIc,GAAE,cAAc,CAAC,CAAC,IAAIA,GAAE,UAAU,QAAM,OAAOA,GAAE,SAAS,aAAaE,IAAE,KAAKK,OAAG;AAAA,MACpF,SAASR;AAAA,MACT,SAASC,GAAE,UAAU,OAAK,IAAIA,GAAE;AAAA,IAClC,CAAC,CAAC,IAAIA,GAAE,SAAS,QAAM,OAAOA,GAAE,QAAQ,YAAY,MAAM,QAAQA,GAAE,IAAI,OAAO,MAAM,QAAQA,GAAE,IAAI,IAAIA,GAAE,OAAO,CAACA,GAAE,IAAI,GAAG,QAAQ,CAACQ,QAAM;AACvI,MAAAN,IAAE,KAAKO;AAAA,QACL,OAAOD,OAAK,WAAWA,MAAI;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC,GAAGxB,GAAE,QAAQkB;AAAA,EAChB,GAAG,EAAE,WAAW,KAAG,CAAC;AACtB;AACA,SAASQ,IAAG1B,IAAGC,IAAGC,KAAG;AACnB,MAAIa,KAAI,MAAM;AAAA,EACd;AACA,EAAAY,GAAE,MAAM;AACN,QAAI3B,MAAKY,GAAE,YAAY,OAAO,iBAAiB,OAAOX,GAAE,SAASA,GAAE,iBAAiB,SAAS;AAC3F,YAAMe,KAAI,IAAI,eAAe,CAAC,CAACE,GAAC,MAAM;AACpC,QAAAhB,IAAE,QAAQgB,IAAE,cAAc,OAAO,CAACM,KAAG,EAAE,YAAYI,IAAE,MAAMJ,MAAII,KAAG,CAAC;AAAA,MACrE,CAAC;AACD,MAAAZ,GAAE,QAAQf,GAAE,KAAK,GAAGc,KAAI,MAAM;AAC5B,QAAAC,GAAE,WAAW,GAAGd,IAAE,QAAQ;AAAA,MAC5B;AAAA,IACF;AAAA,EACF,CAAC,GAAG2B,IAAG,MAAM;AACX,IAAAd,GAAE;AAAA,EACJ,CAAC;AACH;AACA,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AATA,IASGe,MAAK,CAAC9B,OAAM;AACb,UAAQA,IAAG;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAlCA,IAkCG+B,MAAK;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AA3CA,IA2CG,KAAK,CAAC/B,OAAM;AACb,UAAQA,IAAG;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AACA,SAASgC,IAAGhC,IAAGC,IAAG;AAChB,QAAMC,MAAI+B,GAAE,MAAM;AAChB,QAAI,OAAOjC,GAAE,eAAe;AAC1B,aAAOA,GAAE,YAAYC,GAAE,KAAK;AAC9B,QAAI,OAAOD,GAAE,eAAe;AAC1B,aAAOA,GAAE;AACX,QAAIA,GAAE;AACJ,aAAO8B,IAAG7B,GAAE,KAAK;AAAA,EACrB,CAAC,GAAGc,KAAIkB;AAAA,IACN,MAAMjC,GAAE,SAASE,IAAE,QAAQ,GAAGF,GAAE,SAAS,EAAE,IAAIE,IAAE,SAAS,EAAE,KAAK;AAAA,EACnE,GAAGc,KAAIiB;AAAA,IACL,MAAMjC,GAAE,SAASE,IAAE,QAAQ,GAAGF,GAAE,SAAS,EAAE,IAAIE,IAAE,SAAS,EAAE,KAAK;AAAA,EACnE;AACA,SAAO,EAAE,gBAAgBA,KAAG,qBAAqBa,IAAG,qBAAqBC,GAAE;AAC7E;AACA,SAASkB,IAAGlC,IAAGC,OAAMC,KAAG;AACtB,MAAIF,MAAKC,IAAG;AACV,UAAMe,KAAIf,GAAED,EAAC;AACb,WAAO,OAAOgB,MAAK,aAAaA,GAAE,GAAGd,GAAC,IAAIc;AAAA,EAC5C;AACA,QAAMD,KAAI,IAAI;AAAA,IACZ,oBAAoBf,EAAC,iEAAiE,OAAO;AAAA,MAC3FC;AAAA,IACF,EAAE,IAAI,CAACe,OAAM,IAAIA,EAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAAA,EACnC;AACA,QAAM,MAAM,qBAAqB,MAAM,kBAAkBD,IAAGmB,GAAE,GAAGnB;AACnE;AACA,IAAMoB,KAAI;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE;AAAA;AAAA;AAAA;AAAA,EAIA,QAAkC,CAACnC,OAAM,GAAGA,EAAC,wDAAwD,CAACA,OAAM,GAAGA,EAAC;AAClH,EAAE,KAAK,GAAG;AACV,IAAIoC,OAAsB,CAACpC,QAAOA,GAAEA,GAAE,SAAS,CAAC,IAAI,UAAUA,GAAEA,GAAE,QAAQ,CAAC,IAAI,SAASA,KAAIoC,OAAM,CAAC,CAAC;AACpG,SAASC,IAAGrC,IAAGC,KAAI,GAAG;AACpB,MAAIC;AACJ,SAAOF,SAAQE,MAAIW,IAAGb,EAAC,MAAM,OAAO,SAASE,IAAE,QAAQ,QAAKgC,IAAGjC,IAAG;AAAA,IAChE,IAAI;AACF,aAAOD,GAAE,QAAQmC,EAAC;AAAA,IACpB;AAAA,IACA,IAAI;AACF,UAAIpB,KAAIf;AACR,aAAOe,OAAM,QAAQ;AACnB,YAAIA,GAAE,QAAQoB,EAAC,EAAG,QAAO;AACzB,QAAApB,KAAIA,GAAE;AAAA,MACR;AACA,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,SAASuB,GAAEtC,IAAGC,IAAGC,KAAG;AAClB,EAAAU,GAAE,YAAY2B,IAAG,CAACxB,OAAM;AACtB,aAAS,iBAAiBf,IAAGC,IAAGC,GAAC,GAAGa,GAAE,MAAM,SAAS,oBAAoBf,IAAGC,IAAGC,GAAC,CAAC;AAAA,EACnF,CAAC;AACH;AACA,SAASsC,IAAGxC,IAAGC,IAAGC,MAAI+B,GAAE,MAAM,IAAE,GAAG;AACjC,WAASlB,GAAEG,KAAGM,KAAG;AACf,QAAI,CAACtB,IAAE,SAASgB,IAAE,iBAAkB;AACpC,UAAMU,MAAIJ,IAAEN,GAAC;AACb,QAAIU,QAAM,QAAQ,CAACA,IAAE,YAAY,EAAE,SAASA,GAAC,EAAG;AAChD,UAAMa,MAAI,SAASC,GAAEC,IAAG;AACtB,aAAO,OAAOA,MAAK,aAAaD,GAAEC,GAAE,CAAC,IAAI,MAAM,QAAQA,EAAC,KAAKA,cAAa,MAAMA,KAAI,CAACA,EAAC;AAAA,IACxF,EAAE3C,EAAC;AACH,eAAW0C,MAAKD,KAAG;AACjB,UAAIC,OAAM,KAAM;AAChB,YAAMC,KAAID,cAAa,cAAcA,KAAItC,GAAEsC,EAAC;AAC5C,UAAIC,MAAK,QAAQA,GAAE,SAASf,GAAC,KAAKV,IAAE,YAAYA,IAAE,aAAa,EAAE,SAASyB,EAAC;AACzE;AAAA,IACJ;AACA;AAAA;AAAA;AAAA;AAAA;AAAA,MAKE,CAACN,IAAGT,KAAGQ,IAAG,KAAK;AAAA;AAAA;AAAA,MAGfR,IAAE,aAAa,MAAMV,IAAE,eAAe,GAAGjB,GAAEiB,KAAGU,GAAC;AAAA;AAAA,EAEnD;AACA,QAAMZ,KAAIX,GAAE,IAAI;AAChB,EAAAiC,GAAE,aAAa,CAACpB,QAAM;AACpB,QAAIM,KAAGI;AACP,IAAA1B,IAAE,UAAUc,GAAE,UAAUY,OAAKJ,MAAIN,IAAE,iBAAiB,OAAO,SAASM,IAAE,KAAKN,GAAC,MAAM,OAAO,SAASU,IAAE,CAAC,MAAMV,IAAE;AAAA,EAC/G,GAAG,IAAE,GAAGoB;AAAA,IACN;AAAA,IACA,CAACpB,QAAM;AACL,MAAAF,GAAE,UAAUD,GAAEG,KAAG,MAAMF,GAAE,KAAK,GAAGA,GAAE,QAAQ;AAAA,IAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,EACF,GAAGsB,GAAE,QAAQ,CAACpB,QAAMH;AAAA,IAClBG;AAAA,IACA,MAAM,OAAO,SAAS,yBAAyB,oBAAoB,OAAO,SAAS,gBAAgB;AAAA,EACrG,GAAG,IAAE;AACP;AACA,IAAM0B,MAAK,OAAO,kBAAkB;AAApC,IAAuCC,MAAK,OAAO,iBAAiB;AAApE,IAAuEC,MAAK,OAAO,cAAc;AACjG,SAASC,IAAG/C,IAAG;AACb,QAAMC,KAAI+C,GAAEJ,KAAI,IAAI;AACpB,MAAI3C,OAAM,MAAM;AACd,UAAMC,MAAI,IAAI,MAAM,IAAIF,EAAC,yCAAyC;AAClE,UAAM,MAAM,qBAAqB,MAAM,kBAAkBE,KAAG6C,GAAE,GAAG7C;AAAA,EACnE;AACA,SAAOD;AACT;AACA,SAASgD,IAAGjD,IAAG;AACb,QAAMC,KAAI+C,GAAEH,KAAI,IAAI;AACpB,MAAI5C,OAAM,MAAM;AACd,UAAMC,MAAI,IAAI,MAAM,IAAIF,EAAC,yCAAyC;AAClE,UAAM,MAAM,qBAAqB,MAAM,kBAAkBE,KAAG+C,GAAE,GAAG/C;AAAA,EACnE;AACA,SAAOD;AACT;AACA,SAASiD,IAAGlD,IAAG;AACb,QAAMC,KAAI+C,GAAEF,KAAI,IAAI;AACpB,MAAI7C,OAAM,MAAM;AACd,UAAMC,MAAI,IAAI,MAAM,IAAIF,EAAC,yCAAyC;AAClE,UAAM,MAAM,qBAAqB,MAAM,kBAAkBE,KAAGgD,GAAE,GAAGhD;AAAA,EACnE;AACA,SAAOD;AACT;AACA,IAAMkD,KAAI;AAAA,EACR,IAAI;AAAA,IACF,MAAM,CAAC,QAAQ,QAAQ;AAAA,IACvB,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,MAAM,CAAC,QAAQ,QAAQ;AAAA,IACvB,SAAS;AAAA,EACX;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,QAAQ,CAAC,QAAQ,UAAU,MAAM;AAAA,EACjC,OAAO;AAAA,IACL,MAAM,CAAC,SAAS,QAAQ,MAAM;AAAA,IAC9B,SAAS;AAAA,EACX;AAAA,EACA,MAAM;AAAA,IACJ,MAAM,CAAC,SAAS,QAAQ,MAAM;AAAA,IAC9B,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACb,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA,MAAM;AAAA,IACJ,MAAM,CAAC,SAAS,QAAQ,KAAK;AAAA,IAC7B,SAAS;AAAA,EACX;AAAA,EACA,sBAAsB;AAAA,EACtB,cAAc;AAAA,EACd,YAAY;AAAA,IACV,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,SAAS;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,aAAa,CAAC,QAAQ,QAAQ;AAAA,EAC9B,wBAAwB;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACb,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,MAAM,CAAC,OAAO,QAAQ;AAAA,IACtB,SAAS,MAAM,CAAC;AAAA,EAClB;AACF;AACA,SAASC,IAAGpD,IAAGC,IAAGC,KAAGa,IAAG;AACtB,QAAM,EAAE,cAAcC,GAAE,IAAID,IAAGG,MAAIjB,IAAGuB,MAAI6B,IAAEnD,KAAG;AAAA,IAC7C,KAAKc;AAAA,EACP,CAAC,GAAGY,MAAI0B;AAAA,IACNtD;AAAA,IACAkB,IAAE,OAAO,aAAaM,MAAI,CAAC;AAAA,EAC7B;AACA,SAAON,IAAE,OAAO,aAAaU,MAAI,OAAOV,IAAE,MAAM,WAAWqC,GAAErC,IAAE,IAAIM,KAAG,CAACI,GAAC,CAAC,IAAI2B,GAAErC,IAAE,IAAIM,KAAG,MAAM,CAACI,GAAC,CAAC;AACnG;AACA,SAAS4B,GAAExD,IAAGC,IAAGC,KAAGa,IAAG;AACrB,QAAM,EAAE,aAAaC,IAAG,OAAOE,KAAG,SAASM,KAAG,MAAMI,KAAG,iBAAiBa,KAAG,SAASC,IAAG,WAAWC,IAAG,gBAAgBc,KAAG,kBAAkBC,KAAG,gBAAgBC,GAAE,IAAI5C,IAAG6C,KAAIP;AAAA,IACxK,EAAE,GAAGnC,KAAG,IAAIA,IAAE,WAAW;AAAA,IACzBjB;AAAA,EACF,GAAG,EAAE,qBAAqB4D,IAAG,qBAAqBC,IAAE,IAAI9B,IAAG4B,IAAGjB,EAAC,GAAGoB,KAAI;AAAA,IACpE,MAAMvC,IAAE,QAAQoC,GAAE,OAAO;AAAA,IACzB,OAAOC,GAAE;AAAA,IACT,WAAWD,GAAE;AAAA,IACb,SAASA,GAAE;AAAA,IACX,OAAOE,IAAE;AAAA,IACT,WAAWF,GAAE;AAAA,IACb,SAASA,GAAE;AAAA,IACX,gBAAgB;AACd,MAAAhC,IAAE,QAAQ;AAAA,IACZ;AAAA,IACA,eAAe;AACb,MAAAA,IAAE,QAAQ;AAAA,IACZ;AAAA,EACF,GAAGoC,KAAI;AAAA,IACL,MAAMJ,GAAE;AAAA,IACR,MAAMA,GAAE;AAAA,IACR,QAAQ;AAAA,IACR,GAAGA,GAAE,iBAAiB,CAAC,IAAI;AAAA,MACzB,kBAAkBC,GAAE;AAAA,MACpB,gBAAgBD,GAAE;AAAA,MAClB,cAAcA,GAAE;AAAA,MAChB,kBAAkBE,IAAE;AAAA,MACpB,gBAAgBF,GAAE;AAAA,MAClB,cAAcA,GAAE;AAAA,IAClB;AAAA,IACA,gBAAgB;AACd,MAAAhC,IAAE,QAAQ;AAAA,IACZ;AAAA,IACA,eAAe;AACb,MAAAA,IAAE,QAAQ;AAAA,IACZ;AAAA,EACF,GAAGqC,MAAI;AAAA,IACL,OAAO;AAAA,MACLxB,IAAE,QAAQmB,GAAE,uBAAuB;AAAA,MACnClB,GAAE,QAAQkB,GAAE,eAAe;AAAA,IAC7B,EAAE,OAAO,CAACM,OAAM,CAAC,CAACA,EAAC,EAAE,KAAK,GAAG;AAAA,IAC7B,OAAO;AAAA,MACL,GAAGT,IAAE;AAAA,MACL,QAAQG,GAAE;AAAA,IACZ;AAAA,EACF;AACA,MAAIA,GAAE,iBAAiB,OAAOF,IAAE,SAAS,UAAU;AACjD,UAAMQ,KAAI;AAAA,MACR,WAAW;AAAA,MACX,GAAG,OAAON,GAAE,iBAAiB,WAAWA,GAAE,gBAAgB,CAAC;AAAA,IAC7D;AACA,IAAAK,IAAE,MAAMC,GAAE,SAAS,IAAI,GAAGR,IAAE,KAAK;AAAA,EACnC;AACA,WAASS,GAAED,IAAG;AACZ,WAAON,GAAE,SAASpC,IAAE,QAAQ+B,GAAEa,IAAI,MAAMF,EAAC,IAAIG,GAAE,IAAIH;AAAA,EACrD;AACA,WAASI,GAAEJ,IAAG;AACZ,UAAMK,KAAIlB;AAAA,MACRY;AAAA,MACA/D;AAAA,MACA0D,GAAE,SAAS,CAAC,IAAI,EAAE,KAAK5C,GAAE;AAAA,IAC3B;AACA,WAAO4C,GAAE,OAAO,aAAaM,KAAI,OAAON,GAAE,MAAM,WAAWL,GAAEK,GAAE,IAAIW,IAAGL,EAAC,IAAIX,GAAEK,GAAE,IAAIW,IAAG,MAAML,EAAC;AAAA,EAC/F;AACA,WAASM,KAAI;AACX,aAASN,KAAI;AACX,UAAIO;AACJ,YAAMF,KAAIX,GAAE,OAAO,aAAaP;AAAA,QAC9BY;AAAA,QACA/D;AAAA,QACA0D,GAAE,SAAS,CAAC,IAAI,EAAE,KAAK5C,GAAE;AAAA,MAC3B,IAAI,MAAM0D,MAAIpB,GAAEtD,IAAGuE,EAAC;AACpB,eAASE,KAAIzE,GAAE,UAAU,OAAO,SAASyE,GAAE,aAAa,SAAMd,GAAE,GAAGe,OAAKd,GAAE,iBAAiBA,GAAE,SAAS,QAAKS,GAAE,IAAIK;AAAA,IACnH;AACA,WAAOlD,IAAE,QAAQoC,GAAE,gBAAgBL,GAAEoB,KAAI;AAAA,MACvC,GAAGf,GAAE,SAAS,EAAE,KAAK5C,GAAE,IAAI,CAAC;AAAA,MAC5B,GAAGgD;AAAA,IACL,GAAGE,EAAC,IAAIX,GAAEK,GAAE,kBAAkB9D,MAAK8E,KAAI;AAAA,MACrC,KAAK,aAAajC,GAAE,KAAK;AAAA,MACzB,GAAGiB,GAAE,SAAS,EAAE,KAAK5C,GAAE,IAAI,CAAC;AAAA,MAC5B,IAAI;AAAA,MACJ,GAAG+C;AAAA,IACL,GAAGG,EAAC,IAAIG,GAAE;AAAA,EACZ;AACA,SAAOF;AAAA,IACLG;AAAA,MACEE,GAAE;AAAA,IACJ;AAAA,EACF;AACF;AACA,SAASK,IAAG7E,IAAGC,IAAGC,KAAGa,IAAGC,IAAG;AACzB,QAAME,MAAIb,GAAE,KAAE,GAAGmB,MAAIsD,GAAE/D,IAAG,WAAW,GAAGa,MAAIkD,GAAE/D,IAAG,UAAU,GAAG0B,MAAIsC,IAAG,CAAC,CAAC,GAAGrC,KAAIrC,GAAE,MAAM,GAAGsC,KAAItC,GAAE,MAAM,GAAGoD,MAAIpD,GAAE,IAAI,GAAGqD,MAAIrD,GAAE,MAAM,GAAGsD,KAAItD,GAAE,MAAM,GAAGuD,KAAI3B,GAAE,MAAM7B,GAAEH,EAAC,CAAC,GAAG4D,KAAI5B,GAAE,MAAM7B,GAAEF,GAAC,CAAC,GAAG4D,MAAI7B;AAAA,IAC3L,MAAMvB,GAAEkD,EAAC,KAAKlD,GAAEmD,EAAC;AAAA,EACnB,GAAG,EAAE,WAAWE,IAAG,gBAAgBC,IAAG,cAAcC,KAAG,gBAAgBE,IAAG,QAAQG,GAAE,IAAI,YAAGV,IAAGC,IAAG;AAAA,IAC/F,WAAWrC;AAAA,IACX,UAAUI;AAAA,IACV,YAAYa;AAAA;AAAA,IAEZ,WAAW1B,GAAE,SAAS,QAAKA,GAAE;AAAA;AAAA,IAE7B,sBAAsBA,GAAE,SAAS,MAAM,MAAM;AAAA,IAC7C,IAAI;AAAA,EACN,CAAC,GAAGyD,KAAInE,GAAE,IAAI;AACd,EAAAsB,GAAE,MAAM;AACN,IAAAT,IAAE,QAAQ;AAAA,EACZ,CAAC,GAAGD,GAAEjB,IAAG,CAACgF,KAAGC,OAAM;AACjB,IAAAD,OAAK,CAACC,KAAIjE,GAAE,MAAM,IAAI,CAACgE,OAAKC,MAAKjE,GAAE,MAAM;AAAA,EAC3C,GAAG,EAAE,WAAW,KAAG,CAAC;AACpB,WAASkD,KAAI;AACX,IAAAJ,IAAE,UAAUQ,GAAE,GAAGtD,GAAE,QAAQ;AAAA,EAC7B;AACA,EAAAC,GAAE,CAACO,KAAGI,KAAGa,GAAC,GAAGyB,IAAG,EAAE,OAAO,OAAO,CAAC,GAAG;AAAA,IAClCzB;AAAA,IACAmB;AAAA,IACAC;AAAA,IACAJ;AAAA,IACA1C;AAAA,EACF,GAAGE,GAAE,CAAC+C,IAAG,MAAMjD,GAAE,MAAMkD,GAAC,GAAG,MAAM;AAC/B,QAAIe,KAAGC;AACP,KAAClE,GAAE,SAAS,QAAM,OAAOA,GAAE,QAAQ,YAAY,MAAM,QAAQA,GAAE,IAAI,OAAO2B,GAAE,UAAUsC,MAAIhB,GAAE,MAAM,SAAS,OAAO,SAASgB,IAAE,oBAAoB,CAACf,IAAE,OAAOtB,GAAE,SAASsC,KAAIjB,GAAE,MAAM,SAAS,OAAO,SAASiB,GAAE;AAAA,EAC/M,CAAC,GAAGhE,GAAE+C,IAAG,MAAM;AACb,UAAMgB,MAAIhB,GAAE,MAAM;AAClB,IAAAN,IAAE,QAAQsB,OAAK,OAAO,SAASA,IAAE,GAAGrB,GAAE,QAAQqB,OAAK,OAAO,SAASA,IAAE;AAAA,EACvE,CAAC,GAAGtD,IAAG,CAAC,CAACX,GAAE,eAAe6C,IAAGY,EAAC,GAAGvD,GAAE,CAACjB,IAAG8D,GAAC,GAAG,OAAOkB,KAAGC,IAAGC,QAAO;AAC7D,QAAI,MAAM,GAAG,GAAGlF,GAAE,SAAS8D,IAAE,SAAS/C,GAAE,YAAY;AAClD,YAAMoE,MAAK;AAAA,QACTvB,GAAE;AAAA,QACFC,GAAE;AAAA,QACFK;AAAA,QACA,OAAOnD,GAAE,cAAc,WAAWA,GAAE,aAAa;AAAA,MACnD;AACA,MAAAmE,IAAGC,GAAE;AAAA,IACP;AAAA,EACF,GAAG,EAAE,OAAO,QAAQ,WAAW,KAAG,CAAC;AACnC,QAAMZ,KAAIlE,GAAE,IAAE;AACd,EAAAY,GAAE2C,IAAG,MAAM;AACT,MAAEA,GAAE,iBAAiB,YAAYE,IAAE,SAASS,GAAE,UAAUA,GAAE,QAAQ,OAAI,OAAO,sBAAsB,MAAM;AACvG,MAAAA,GAAE,QAAQ,MAAIL,GAAE;AAAA,IAClB,CAAC;AAAA,EACH,GAAG,EAAE,OAAO,OAAO,CAAC;AACpB,QAAMQ,MAAI;AAAA,IACR,cAAczE;AAAA,IACd,WAAW8D;AAAA,EACb,GAAGU,KAAI;AAAA,IACL,aAAavE;AAAA,IACb,OAAOa;AAAA,IACP,SAASG;AAAA,IACT,MAAMlB;AAAA,IACN,iBAAiB0C;AAAA,IACjB,SAASC;AAAA,IACT,WAAWoB;AAAA,IACX,gBAAgBI;AAAA,IAChB,kBAAkBK;AAAA,IAClB,gBAAgBN;AAAA,EAClB,GAAGkB,KAAI;AAAA,IACL,KAAK3B;AAAA,IACL,WAAWM;AAAA,IACX,GAAGL;AAAA,IACH,GAAGC;AAAA,EACL;AACA,SAAOS,GAAEtB,KAAIsC,EAAC,GAAG,EAAE,cAAcV,KAAG,aAAaD,IAAG,UAAUW,IAAG,WAAWrB,IAAG,aAAaH,IAAG,YAAYC,IAAG,gBAAgBG,IAAG,QAAQE,GAAE;AAC7I;AACA,IAAM,KAAKmB,IAAE;AAAA,EACX,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAOlC;AAAA,EACP,OAAO,CAAC,QAAQ,QAAQ,QAAQ;AAAA,EAChC,MAAMnD,IAAG,EAAE,MAAMC,IAAG,OAAOC,KAAG,OAAOa,GAAE,GAAG;AACxC,IAAAD,GAAE,SAASd,EAAC;AACZ,UAAMgB,KAAIX,GAAEL,GAAE,QAAQ,KAAE,GAAGkB,MAAIb,GAAE,IAAI,GAAGmB,MAAInB,GAAE,IAAI,GAAG;AAAA,MACnD,cAAcuB;AAAA,MACd,aAAaa;AAAA,MACb,WAAWC;AAAA,IACb,IAAImC,IAAG7D,IAAGE,KAAGM,KAAGxB,IAAGC,EAAC;AACpB,aAAS0C,GAAEe,KAAG;AACZ,aAAO1D,GAAE,OAAO,aAAa0D,MAAI,OAAO1D,GAAE,MAAM,WAAWuD,GAAEvD,GAAE,IAAIe,IAAG2C,GAAC,IAAIH,GAAEvD,GAAE,IAAIe,IAAG,MAAM2C,GAAC;AAAA,IAC/F;AACA,UAAMD,MAAI;AAAA,MACR,WAAWf,GAAE;AAAA,IACf;AACA,WAAO1C,GAAE,cAAcA,GAAE,UAAUoE,GAAExB,KAAIhB,GAAC,GAAGwC,GAAEvB,KAAIJ,GAAC,GAAG,MAAM;AAC3D,UAAIvC,IAAE;AACJ,eAAOyC,GAAEzC,IAAE,QAAQuD,GAAC,CAAC;AAAA,IACzB,KAAK,MAAM;AACT,UAAI,CAACvD,IAAE,QAAS;AAChB,YAAM,CAACwD,KAAGC,EAAC,IAAIpD,GAAEL,IAAE,QAAQuD,GAAC,CAAC,EAAE,OAAOhD,EAAC;AACvC,UAAI,CAACA,GAAEiD,GAAC;AACN;AACF,YAAME,KAAIR;AAAA,QACRM;AAAA,QACA,EAAE,IAAI,WAAW;AAAA,QACjB,CAAC;AAAA,QACD9B;AAAA,MACF,GAAGiC,KAAIL;AAAA,QACLG;AAAA,QACA,EAAE,IAAI3D,GAAE,WAAW;AAAA,QACnB,CAAC;AAAA,QACDyC;AAAA,MACF;AACA,aAAOE,GAAE;AAAA,QACPiB;AAAA,QACAC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;AA3CD,IA2CI,KAAK;AA3CT,IA2Ca,KAAK;AAAA,EAChB,IAAIV,GAAE;AACR;AA7CA,IA6CGmC,MAAKD,IAAE;AAAA,EACR,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,EACP,MAAMrF,IAAG,EAAE,OAAOC,IAAG,OAAOC,IAAE,GAAG;AAC/B,UAAMa,KAAIgC,IAAG,gBAAgB,GAAG,EAAE,WAAW/B,GAAE,IAAID;AACnD,WAAO,MAAM;AACX,UAAI,CAACd,GAAE,QAAS;AAChB,YAAMiB,MAAI;AAAA,QACR,WAAWF,GAAE;AAAA,MACf;AACA,aAAOoC;AAAA,QACLnD,GAAE,QAAQiB,GAAC,EAAE,CAAC;AAAA,QACdlB;AAAA,QACAE;AAAA,QACAa;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAhED,IAgEI,KAAKuE;AAhET,IAgEaC,MAAK;AAAA,EAChB,IAAIpC,GAAE;AAAA,EACN,eAAeA,GAAE;AAAA,EACjB,gBAAgBA,GAAE;AAAA,EAClB,gBAAgBA,GAAE;AAAA,EAClB,OAAOA,GAAE;AAAA,EACT,WAAWA,GAAE;AAAA,EACb,SAASA,GAAE;AAAA,EACX,OAAOA,GAAE;AAAA,EACT,WAAWA,GAAE;AAAA,EACb,SAASA,GAAE;AAAA,EACX,aAAaA,GAAE;AAAA,EACf,wBAAwBA,GAAE;AAAA,EAC1B,iBAAiB;AAAA,IACf,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AAjFA,IAiFGqC,MAAKH,IAAE;AAAA,EACR,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAOE;AAAA,EACP,MAAMvF,IAAG,EAAE,OAAOC,IAAG,OAAOC,IAAE,GAAG;AAC/B,IAAAY,GAAE,gBAAgBd,EAAC;AACnB,UAAMe,KAAIkC,IAAG,cAAc,GAAG,EAAE,WAAWjC,GAAE,IAAID;AACjD,WAAO,MAAM;AACX,UAAI,CAACd,GAAE,QAAS;AAChB,YAAMiB,MAAI;AAAA,QACR,WAAWF,GAAE;AAAA,MACf,GAAGQ,MAAI,OAAO,QAAQxB,EAAC,EAAE,OAAO,CAAC4B,KAAG,CAACa,KAAGC,EAAC,MAAM;AAC7C,cAAMC,KAAI4C;AACV,gBAAQ,OAAO5C,GAAEF,GAAC,KAAK,YAAYC,OAAMC,GAAEF,GAAC,EAAE,WAAWC,OAAM,WAAW,OAAOd,IAAEa,GAAC,GAAGb;AAAA,MACzF,GAAG,EAAE,GAAG5B,GAAE,CAAC;AACX,aAAOwD;AAAA,QACLvD,GAAE,QAAQiB,GAAC,EAAE,CAAC;AAAA,QACdM;AAAA,QACAtB;AAAA,QACAa;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAxGD,IAwGI,KAAKyE;AAxGT,IAwGa,KAAK;AAAA,EAChB,IAAI;AAAA,IACF,GAAGrC,GAAE;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AAjHA,IAiHGsC,MAAKJ,IAAE;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAMrF,IAAG,EAAE,OAAOC,IAAG,OAAOC,IAAE,GAAG;AAC/B,UAAM,EAAE,KAAKa,IAAG,WAAWC,IAAG,GAAGE,KAAG,GAAGM,IAAE,IAAI0B,IAAG,YAAY;AAC5D,WAAO,MAAM;AACX,UAAIR;AACJ,YAAMd,MAAI;AAAA,QACR,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,EAAEZ,GAAE,MAAM,MAAM,GAAG,EAAE,CAAC,CAAC,GAAGyB,MAAI;AAAA,QAC5B,MAAM1B,GAAE,SAAS,OAAOG,IAAE,SAAS,WAAW,GAAGZ,IAAES,GAAE,OAAOG,IAAE,KAAK,CAAC,OAAO;AAAA,QAC3E,KAAKH,GAAE,SAAS,OAAOS,IAAE,SAAS,WAAW,GAAGlB,IAAES,GAAE,OAAOS,IAAE,KAAK,CAAC,OAAO;AAAA,QAC1E,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,CAACI,GAAC,GAAG,GAAG5B,GAAE,SAAS,EAAE;AAAA,MACvB;AACA,UAAIA,GAAE,OAAO,YAAY;AACvB,cAAM2C,KAAI;AAAA,UACR,WAAW3B,GAAE;AAAA,QACf,GAAGyC,OAAKf,KAAIzC,GAAE,YAAY,OAAO,SAASyC,GAAE,KAAKzC,IAAG0C,EAAC,EAAE,CAAC;AACxD,eAAO,CAACc,OAAK,CAAChD,GAAEgD,GAAC,IAAI,SAASH,GAAEG,KAAG,EAAE,KAAK1C,IAAG,OAAO0B,IAAE,CAAC;AAAA,MACzD;AACA,aAAOc,GAAEvD,GAAE,IAAIqD,IAAEnD,KAAG,EAAE,KAAKa,IAAG,OAAO0B,IAAE,CAAC,CAAC;AAAA,IAC3C;AAAA,EACF;AACF,CAAC;AA7ID,IA6II,KAAKgD;AA7IT,IA6Ia,KAAK;AAAA,EAChB,IAAItC,GAAE;AAAA,EACN,MAAMA,GAAE;AAAA,EACR,WAAWA,GAAE;AAAA,EACb,UAAUA,GAAE;AAAA,EACZ,QAAQA,GAAE;AAAA,EACV,OAAOA,GAAE;AAAA,EACT,MAAMA,GAAE;AAAA,EACR,OAAOA,GAAE;AAAA,EACT,eAAeA,GAAE;AAAA,EACjB,YAAYA,GAAE;AAAA,EACd,QAAQA,GAAE;AAAA,EACV,eAAeA,GAAE;AAAA,EACjB,gBAAgBA,GAAE;AAAA,EAClB,gBAAgBA,GAAE;AAAA,EAClB,OAAOA,GAAE;AAAA,EACT,WAAWA,GAAE;AAAA,EACb,SAASA,GAAE;AAAA,EACX,OAAOA,GAAE;AAAA,EACT,WAAWA,GAAE;AAAA,EACb,SAASA,GAAE;AAAA,EACX,aAAaA,GAAE;AAAA,EACf,wBAAwBA,GAAE;AAAA,EAC1B,QAAQA,GAAE;AAAA,EACV,WAAWA,GAAE;AAAA,EACb,YAAYA,GAAE;AAChB;AAvKA,IAuKG,KAAKkC,IAAE;AAAA,EACR,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,EACP,OAAO,CAAC,WAAW,QAAQ,QAAQ,QAAQ;AAAA,EAC3C,MAAMrF,IAAG,EAAE,MAAMC,IAAG,OAAOC,KAAG,OAAOa,GAAE,GAAG;AACxC,IAAAD,GAAE,gBAAgBd,EAAC;AACnB,UAAMgB,KAAIX,GAAEL,GAAE,QAAQ,KAAE,GAAGkB,MAAIb,GAAE;AAAA,MAC/B,wBAAwB;AACtB,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG;AAAA,UACH,KAAK;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF,CAAC,GAAGmB,MAAInB,GAAE,IAAI,GAAG;AAAA,MACf,aAAauB;AAAA,MACb,WAAWa;AAAA,IACb,IAAIoC,IAAG7D,IAAGE,KAAGM,KAAGxB,IAAGC,EAAC;AACpB,IAAAgB,GAAE,MAAMjB,GAAE,MAAM,MAAM;AACpB,MAAAgB,GAAE,QAAQhB,GAAE,QAAQ;AAAA,IACtB,CAAC;AACD,aAAS0C,KAAI;AACX,MAAA1B,GAAE,QAAQ;AAAA,IACZ;AACA,WAAOf,GAAE,WAAW;AAAA,MAClB,MAAMe;AAAA,MACN,WAAWyB;AAAA,MACX,WAAWvB;AAAA,MACX,UAAUM;AAAA,IACZ,CAAC,GAAG,MAAM;AACR,UAAI,CAACtB,IAAE,QAAS;AAChB,YAAMyC,KAAI;AAAA,QACR,WAAWF,IAAE;AAAA,QACb,OAAOC;AAAA,MACT,GAAG,CAACe,GAAC,IAAIlD,GAAEL,IAAE,QAAQyC,EAAC,CAAC,EAAE,OAAOlC,EAAC;AACjC,aAAO+C;AAAA,QACLC;AAAA,QACA;AAAA,UACE,IAAIzD,GAAE;AAAA,UACN,MAAMgB,GAAE;AAAA,QACV;AAAA,QACAD;AAAA,QACAa;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AA3ND,IA2NI8D,MAAK;AA3NT,IA2Na,KAAK;AAAA,EAChB,IAAIvC,GAAE;AAAA,EACN,WAAWA,GAAE;AAAA,EACb,UAAUA,GAAE;AAAA,EACZ,QAAQA,GAAE;AAAA,EACV,OAAOA,GAAE;AAAA,EACT,MAAM;AAAA,IACJ,GAAGA,GAAE;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,OAAOA,GAAE;AAAA,EACT,eAAeA,GAAE;AAAA,EACjB,YAAYA,GAAE;AAAA,EACd,QAAQA,GAAE;AAAA,EACV,eAAeA,GAAE;AAAA,EACjB,gBAAgBA,GAAE;AAAA,EAClB,gBAAgBA,GAAE;AAAA,EAClB,OAAOA,GAAE;AAAA,EACT,WAAWA,GAAE;AAAA,EACb,SAASA,GAAE;AAAA,EACX,OAAOA,GAAE;AAAA,EACT,WAAWA,GAAE;AAAA,EACb,SAASA,GAAE;AAAA,EACX,aAAaA,GAAE;AAAA,EACf,wBAAwBA,GAAE;AAAA,EAC1B,WAAWA,GAAE;AAAA,EACb,YAAYA,GAAE;AAChB;AAtPA,IAsPG,KAAKkC,IAAE;AAAA,EACR,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,EACP,OAAO,CAAC,QAAQ,QAAQ,QAAQ;AAAA,EAChC,MAAMrF,IAAG,EAAE,MAAMC,IAAG,OAAOC,KAAG,OAAOa,GAAE,GAAG;AACxC,UAAMC,KAAIX,GAAE,KAAE;AACd,aAASa,IAAE,EAAE,MAAMM,KAAG,WAAWI,KAAG,UAAUa,IAAE,GAAG;AACjD,MAAAH,GAAE,eAAe,CAACI,OAAM;AACtB,QAAAA,GAAE,eAAe,GAAGd,IAAE,QAAQ;AAAA,UAC5B,wBAAwB;AACtB,mBAAO;AAAA,cACL,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,GAAGc,GAAE;AAAA,cACL,GAAGA,GAAE;AAAA,cACL,KAAKA,GAAE;AAAA,cACP,MAAMA,GAAE;AAAA,cACR,OAAOA,GAAE;AAAA,cACT,QAAQA,GAAE;AAAA,YACZ;AAAA,UACF;AAAA,QACF,GAAGlB,IAAE,QAAQ;AAAA,MACf,CAAC,GAAGgB,IAAGC,KAAG,MAAM;AACd,QAAAjB,IAAE,QAAQ;AAAA,MACZ,GAAGS,GAAE,MAAMT,IAAE,KAAK,CAAC;AAAA,IACrB;AACA,WAAOG,GAAE,MAAM;AACb,MAAAX,GAAE,QAAQ;AAAA,IACZ,CAAC,GAAG,MAAM;AACR,UAAId,IAAE,WAAWc,GAAE;AACjB,eAAOuC,GAAEmC,KAAI;AAAA,UACX,GAAG1F;AAAA,UACH,GAAGe;AAAA,UACH,QAAQ;AAAA,UACR,WAAWG;AAAA,UACX,QAAQ,MAAMjB,GAAE,MAAM;AAAA,UACtB,QAAQ,MAAMA,GAAE,MAAM;AAAA,UACtB,UAAU,MAAMA,GAAE,QAAQ;AAAA,QAC5B,GAAGC,IAAE,OAAO;AAAA,IAChB;AAAA,EACF;AACF,CAAC;AAhSD,IAgSI,KAAK;AAhST,IAgSa,KAAK;AAAA,EAChB,IAAIiD,GAAE;AAAA,EACN,WAAWA,GAAE;AAAA,EACb,UAAUA,GAAE;AAAA,EACZ,QAAQA,GAAE;AAAA,EACV,OAAOA,GAAE;AAAA,EACT,MAAMA,GAAE;AAAA,EACR,OAAOA,GAAE;AAAA,EACT,eAAeA,GAAE;AAAA,EACjB,YAAYA,GAAE;AAAA,EACd,QAAQA,GAAE;AAAA,EACV,eAAeA,GAAE;AAAA,EACjB,gBAAgBA,GAAE;AAAA,EAClB,gBAAgBA,GAAE;AAAA,EAClB,OAAOA,GAAE;AAAA,EACT,WAAWA,GAAE;AAAA,EACb,SAASA,GAAE;AAAA,EACX,OAAOA,GAAE;AAAA,EACT,WAAWA,GAAE;AAAA,EACb,SAASA,GAAE;AAAA,EACX,aAAaA,GAAE;AAAA,EACf,wBAAwBA,GAAE;AAAA,EAC1B,WAAWA,GAAE;AAAA,EACb,YAAYA,GAAE;AAAA,EACd,kBAAkB;AAAA,IAChB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AA5TA,IA4TG,KAAKkC,IAAE;AAAA,EACR,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,EACP,OAAO,CAAC,QAAQ,QAAQ,QAAQ;AAAA,EAChC,MAAM,EAAE,kBAAkBrF,IAAG,GAAGC,GAAE,GAAG,EAAE,MAAMC,KAAG,OAAOa,IAAG,OAAOC,GAAE,GAAG;AACpE,UAAME,MAAIb,GAAE,KAAE;AACd,aAASmB,IAAE,EAAE,MAAMI,KAAG,WAAWa,KAAG,UAAUC,GAAE,GAAG;AACjD,eAASC,KAAI;AACX,QAAAf,IAAE,QAAQ;AAAA,MACZ;AACA,eAAS6B,MAAI;AACX,QAAA7B,IAAE,QAAQ;AAAA,MACZ;AACA,eAAS8B,IAAEI,KAAG;AACZ,QAAArB,IAAE,QAAQ;AAAA,UACR,wBAAwB;AACtB,mBAAO;AAAA,cACL,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,GAAGqB,IAAE;AAAA,cACL,GAAGA,IAAE;AAAA,cACL,KAAKA,IAAE;AAAA,cACP,MAAMA,IAAE;AAAA,cACR,OAAOA,IAAE;AAAA,cACT,QAAQA,IAAE;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,eAASH,GAAEG,KAAG;AACZ,QAAAnB,GAAE,GAAGe,IAAEI,GAAC;AAAA,MACV;AACA,eAASF,GAAEE,KAAG;AACZ,QAAAnB,GAAE,GAAGe,IAAEI,IAAE,QAAQ,CAAC,CAAC;AAAA,MACrB;AACA,YAAMD,KAAIhD,IAAG6B,EAAC;AACd,MAAAmB,OAAMtB,IAAG,CAACuB,QAAM;AACd,YAAI9D,MAAK,CAAC6D,GAAE,eAAe,8BAA8B,GAAG;AAC1D,gBAAME,KAAIF,GAAE,cAAc,OAAO;AACjC,WAACA,GAAE,QAAQA,GAAE,qBAAqB,MAAM,EAAE,CAAC,GAAG,YAAYE,EAAC,GAAGA,GAAE,KAAK,gCAAgCA,GAAE,YAAYF,GAAE,eAAe;AAAA,YAClI;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,EAAE,KAAK;AAAA,CAChB,CAAC,CAAC,GAAGC,IAAE,MAAM;AACF,gBAAIG;AACJ,oBAAQA,MAAIJ,GAAE,eAAe,8BAA8B,MAAM,OAAO,SAASI,IAAE,OAAO;AAAA,UAC5F,CAAC;AAAA,QACH;AAAA,MACF,GAAG,EAAE,OAAO,OAAO,CAAC,GAAG,kBAAkB,UAAU,UAAU,iBAAiB,KAAK3B,GAAE,cAAcsB,EAAC,GAAGtB,GAAE,YAAYmB,GAAC,GAAGnB,GAAE,aAAasB,EAAC,MAAMtB,GAAE,cAAcqB,EAAC,GAAGrB,GAAE,cAAcmB,GAAC,GAAGnB,GAAE,aAAaqB,EAAC;AAAA,IACzM;AACA,WAAOhC,GAAE,MAAM;AACb,MAAAT,IAAE,QAAQ;AAAA,IACZ,CAAC,GAAG,MAAM;AACR,UAAIH,GAAE,WAAWG,IAAE;AACjB,eAAOqC,GAAEmC,KAAI;AAAA,UACX,GAAGzF;AAAA,UACH,GAAGe;AAAA,UACH,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,WAAWQ;AAAA,UACX,QAAQ,MAAMtB,IAAE,MAAM;AAAA,UACtB,QAAQ,MAAMA,IAAE,MAAM;AAAA,UACtB,UAAU,MAAMA,IAAE,QAAQ;AAAA,QAC5B,GAAGa,GAAE,OAAO;AAAA,IAChB;AAAA,EACF;AACF,CAAC;AAnYD,IAmYI,KAAK;AACT,SAAS,GAAGf,IAAG;AACb,SAAOqF,IAAE;AAAA,IACP,MAAM;AAAA,IACN,MAAMnF,KAAG,EAAE,OAAOa,GAAE,GAAG;AACrB,aAAO,MAAMwC,GAAE,IAAIF;AAAA,QACjBrD;AAAA,QACAE;AAAA,MACF,GAAGa,EAAC;AAAA,IACN;AAAA,EACF,CAAC;AACH;AACA,IAAM4E,MAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,GAAG3F,KAAI,CAAC,GAAG;AAClB,QAAM,EAAE,QAAQC,KAAI,GAAG,IAAID;AAC3B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS,CAACE,QAAM;AACd,UAAIA,IAAE,WAAWD,EAAC,GAAG;AACnB,cAAMc,KAAIb,IAAE,UAAUD,GAAE,MAAM;AAC9B,YAAI0F,IAAG,SAAS5E,EAAC;AACf,iBAAO;AAAA,YACL,MAAMA;AAAA,YACN,MAAM;AAAA,UACR;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;", "names": ["h", "ye", "P", "U", "be", "O", "te", "N", "k", "b", "J", "Fe", "$", "R", "W", "M", "Te", "a", "b", "i", "offset", "size", "offset", "opts", "d", "m", "a", "b", "min", "i", "size", "k", "options", "H", "N", "k", "u", "e", "r", "i", "f", "t", "s", "e", "o", "a", "s", "e", "t", "r", "i", "o", "s", "t", "e", "o", "o", "i", "s", "E", "o", "e", "l", "n", "r", "n", "a", "e", "t", "i", "d", "t", "e", "r", "n", "s", "i", "r", "n", "o", "c", "e", "n", "o", "t", "r", "i", "l", "m", "s", "x", "p", "L", "a", "d", "u", "t", "i", "n", "r", "u", "e", "t", "n", "r", "o", "i", "w", "e", "n", "t", "i", "o", "w", "f", "m", "l", "s", "a", "e", "r", "t", "c", "o", "n", "u", "E", "i", "r", "t", "e", "n", "s", "i", "u", "o", "o", "r", "e", "u", "n", "t", "p", "i", "e", "t", "d", "o", "p", "r", "l", "c", "f", "n", "O", "N", "o", "S", "e", "r", "t", "i", "a", "n", "l", "y", "d", "m", "h", "T", "u", "c", "p", "f", "b", "v", "s", "g", "R", "O", "E", "u", "e", "f", "t", "n", "i", "r", "d", "o", "p", "r", "n", "i", "e", "s", "l", "t", "o", "o", "r", "f", "t", "n", "e", "t", "e", "n", "r", "u", "l", "c", "i", "f", "n", "t", "r", "s", "e", "e", "i", "s", "t", "r", "n", "o", "f", "p", "a", "h", "r", "y", "H", "c", "o", "f", "u", "e", "k", "S", "v", "d", "D", "E", "w", "t", "n", "s", "b", "C", "A", "O", "M", "$", "B", "p", "R", "V", "i", "I", "l", "T", "j", "q", "g", "ue", "N", "o", "O", "v", "Y", "se", "$", "de", "y", "fe", "L", "I", "j", "h", "E", "g", "K", "i", "E", "n", "e", "o", "r", "i", "t", "a", "d", "r", "n", "o", "a", "w", "e", "t", "n", "r", "l", "o", "A", "e", "I", "E", "i", "L", "h", "m", "z", "J", "f", "a", "u", "T", "w", "d", "s", "F", "H", "N", "Q", "O", "j", "g", "K", "p", "s", "o", "n", "m", "t", "e", "a", "o", "r", "t", "n", "e", "s", "i", "c", "c", "o", "e", "l", "n", "t", "r", "w", "t", "r", "n", "c", "a", "o", "l", "s", "e", "f", "i", "l", "e", "o", "m", "e", "n", "t", "a", "o", "c", "w", "l", "r", "d", "t", "a", "n", "i", "m", "l", "p", "e", "s", "r", "o", "f", "m", "s", "i", "t", "E", "d", "f", "m", "s", "o", "a", "e", "u", "r", "n", "l", "m", "s", "N", "o", "i", "H", "t", "s", "r", "u", "l", "f", "a", "n", "e", "M", "m", "v", "l", "a", "c", "e", "s", "a", "u", "l", "o", "t", "r", "c", "f", "n", "f", "m", "l", "c", "p", "u", "s", "e", "y", "R", "o", "r", "i", "a", "t", "n", "d", "x", "y", "R", "v", "j", "p", "u", "w", "t", "R", "k", "p", "o", "s", "e", "r", "n", "i", "j", "x", "K", "y", "v", "l", "d", "c", "f", "a", "g", "m", "w", "m", "j", "I", "s", "R", "y", "E", "p", "O", "D", "K", "x", "e", "t", "i", "l", "r", "f", "M", "$", "m", "p", "w", "o", "s", "u", "H", "n", "a", "c", "R", "K", "v", "d", "g", "b", "j", "D", "y", "P", "T", "L", "I", "O", "E", "z", "l", "H", "T", "t", "i", "Y", "A", "O", "p", "s", "q", "W", "n", "u", "y", "$", "r", "g", "o", "D", "S", "R", "m", "E", "c", "k", "w", "N", "Q", "X", "B", "K", "Z", "e", "h", "ee", "U", "te", "le", "a", "d", "f", "ae", "oe", "re", "M", "de", "ne", "se", "ie", "ue", "fe", "L", "x", "v", "z", "Ve", "m", "b", "I", "P", "h", "R", "d", "w", "$", "o", "T", "O", "t", "r", "I", "k", "U", "N", "b", "s", "d", "e", "i", "n", "l", "a", "R", "m", "c", "P", "h", "w", "u", "D", "S", "v", "C", "K", "M", "x", "E", "z", "N", "_", "V", "K", "q", "W", "T", "R", "H", "G", "n", "a", "o", "e", "r", "i", "n", "t", "u", "f", "l", "g", "p", "a", "t", "n", "r", "e", "o", "l", "u", "g", "pe", "o", "b", "r", "ve", "$", "A", "_", "E", "w", "n", "T", "e", "f", "v", "s", "m", "p", "a", "u", "t", "i", "l", "O", "d", "D", "x", "y", "L", "M", "k", "c", "h", "I", "R", "W", "C", "K", "H", "N", "z", "Q", "Ee", "V", "Ae", "q", "G", "y", "T", "K", "x", "N", "j", "L", "R", "B", "i", "ee", "te", "o", "A", "O", "M", "K", "T", "I", "R", "p", "e", "r", "f", "d", "g", "b", "t", "a", "n", "v", "s", "l", "c", "u", "h", "w", "y", "L", "x", "N", "be", "j", "B", "C", "O", "j", "W", "T", "q", "ee", "te", "z", "R", "ie", "J", "Se", "s", "U", "d", "P", "q", "ue", "ge", "j", "h", "u", "f", "R", "t", "o", "y", "v", "m", "b", "O", "i", "E", "L", "$", "x", "e", "r", "l", "g", "G", "C", "a", "z", "S", "c", "I", "F", "w", "N", "p", "J", "T", "W", "Ge", "ee", "te", "n", "je", "Ae", "ie", "o", "F", "_", "C", "$", "D", "U", "W", "k", "y", "J", "v", "x", "L", "k", "C", "j", "y", "h", "a", "d", "t", "L", "n", "E", "i", "e", "y", "o", "r", "l", "j", "v", "K", "x", "k", "C", "p", "c", "f", "s", "u", "b", "g", "m", "h", "le", "t", "m", "H", "N", "$", "u", "F", "S", "g", "O", "d", "i", "p", "k", "l", "R", "E", "T", "f", "G", "o", "e", "s", "y", "a", "n", "W", "v", "r", "c", "b", "D", "J", "C", "_", "L", "ie", "U", "K", "M", "u", "v", "H", "S", "M", "I", "P", "w", "j", "C", "v", "l", "c", "i", "r", "w", "f", "E", "u", "t", "p", "k", "P", "ue", "h", "n", "M", "s", "e", "m", "o", "L", "D", "R", "x", "d", "a", "I", "j", "K", "y", "b", "T", "B", "S", "H", "v", "L", "z", "A", "j", "F", "K", "N", "P", "_", "J", "i", "m", "f", "d", "i", "t", "n", "f", "m", "u", "o", "e", "a", "r", "te", "s", "le", "d", "U", "C", "a", "b", "j", "G", "me", "L", "E", "i", "P", "l", "r", "p", "v", "R", "y", "t", "c", "n", "u", "o", "e", "I", "m", "h", "W", "O", "N", "T", "x", "F", "w", "_", "J", "A", "z", "pe", "K", "Ie", "ye", "f", "w", "K", "k", "F", "ae", "C", "z", "B", "m", "le", "x", "l", "r", "e", "t", "m", "e", "t", "d", "g", "i", "F", "n", "a", "l", "s", "o", "u", "r", "c", "L", "g", "e", "t", "R", "pe", "a", "me", "F", "Te", "ge", "N", "L", "Q", "m", "C", "z", "s", "n", "r", "S", "l", "f", "h", "W", "he", "K", "i", "k", "Se", "d", "y", "w", "v", "A", "D", "H", "I", "c", "b", "P", "o", "x", "j", "M", "X", "_", "Y", "Z", "ee", "te", "E", "p", "V", "le", "B", "Ce", "ye", "be", "Ee", "Ve", "U", "ne", "re", "ae", "ce", "T", "v", "x", "y", "platform", "x", "y", "i", "max", "offset", "d", "alignment", "v", "a", "b", "placements", "sides", "side", "placement", "overflow", "platform", "x", "y", "min", "max", "getComputedStyle", "e", "getComputedStyle", "$", "x", "y", "a", "b", "e", "offset", "autoPlacement", "shift", "flip", "hide", "arrow", "computePosition", "unref", "computed", "shallowRef", "watch", "onScopeDispose", "unwrapElement", "arrow", "x", "y", "computePosition", "he", "pe", "e", "r", "o", "G", "B", "h", "K", "L", "ye", "I", "Q", "Ie", "X", "ne", "Y", "a", "t", "P", "i", "offset", "flip", "shift", "autoPlacement", "arrow", "u", "hide", "ze", "U", "s", "be", "De", "ut", "Me", "O", "re", "Z", "oe", "$e", "C", "te", "ke", "d", "f", "c", "ae", "ie", "le", "se", "W", "ue", "fe", "n", "ce", "N", "k", "b", "_", "p", "y", "T", "l", "g", "m", "F", "x", "E", "v", "z", "$", "M", "D", "A", "V", "H", "j", "Te", "Se", "de", "J", "Fe", "w", "S", "ve", "ge", "q", "R", "Ye", "ee", "_e", "Ge", "me", "nt"]}