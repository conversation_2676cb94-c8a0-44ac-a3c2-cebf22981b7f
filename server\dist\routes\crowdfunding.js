"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const crowdfunding_controller_js_1 = require("../controllers/crowdfunding.controller.js");
const router = express_1.default.Router();
// Public routes
router.get("/stats", (0, route_helpers_js_1.wrapController)(crowdfunding_controller_js_1.getCrowdfundingStats));
// Protected routes
router.post("/supporter-payment", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(crowdfunding_controller_js_1.createSupporterPayment));
exports.default = router;
//# sourceMappingURL=crowdfunding.js.map