"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// <PERSON>ript to migrate existing approved join requests to the Employee model
const prisma_js_1 = require("../lib/prisma.js");
async function migrateApprovedRequestsToEmployees() {
    console.log("Starting migration of approved join requests to Employee model...");
    try {
        // Get all approved join requests
        const approvedRequests = await prisma_js_1.prisma.companyJoinRequest.findMany({
            where: {
                status: "APPROVED",
            },
            include: {
                user: true,
            },
        });
        console.log(`Found ${approvedRequests.length} approved join requests`);
        // Process each approved request
        for (const request of approvedRequests) {
            try {
                // Check if an employee record already exists
                const existingEmployee = await prisma_js_1.prisma.employee.findFirst({
                    where: {
                        userId: request.userId,
                        companyId: request.companyId,
                    },
                });
                if (existingEmployee) {
                    console.log(`Employee record already exists for user ${request.userId} in company ${request.companyId}`);
                    continue;
                }
                // Create a new employee record
                await prisma_js_1.prisma.employee.create({
                    data: {
                        userId: request.userId,
                        companyId: request.companyId,
                        position: request.role === "PROJECTLEADER"
                            ? "Project Leader"
                            : request.role === "SALESMAN"
                                ? "Sales Representative"
                                : request.role === "WORKER"
                                    ? "Worker"
                                    : request.role === "CLIENT"
                                        ? "Client"
                                        : "Employee",
                        department: request.role === "PROJECTLEADER"
                            ? "Project Management"
                            : request.role === "SALESMAN"
                                ? "Sales"
                                : request.role === "WORKER"
                                    ? "Operations"
                                    : request.role === "CLIENT"
                                        ? "Client Relations"
                                        : "General",
                        startDate: request.updatedAt || new Date(),
                        salary: 0, // Default salary, to be updated later
                        status: "ACTIVE",
                        employmentType: "FULL_TIME", // Default employment type
                    },
                });
                console.log(`Created employee record for user ${request.userId} in company ${request.companyId}`);
            }
            catch (error) {
                console.error(`Error processing request ${request.id}:`, error);
            }
        }
        // Get all approved invitations
        const approvedInvitations = await prisma_js_1.prisma.companyInvitation.findMany({
            where: {
                status: "APPROVED",
            },
        });
        console.log(`Found ${approvedInvitations.length} approved invitations`);
        // Process each approved invitation
        for (const invitation of approvedInvitations) {
            try {
                // Find the user who accepted the invitation
                const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
                    where: {
                        companyId: invitation.companyId,
                    },
                    include: {
                        user: true,
                    },
                });
                if (!userCompany) {
                    console.log(`No user found for invitation ${invitation.id}`);
                    continue;
                }
                // Check if an employee record already exists
                const existingEmployee = await prisma_js_1.prisma.employee.findFirst({
                    where: {
                        userId: userCompany.userId,
                        companyId: invitation.companyId,
                    },
                });
                if (existingEmployee) {
                    console.log(`Employee record already exists for user ${userCompany.userId} in company ${invitation.companyId}`);
                    continue;
                }
                // Create a new employee record
                await prisma_js_1.prisma.employee.create({
                    data: {
                        userId: userCompany.userId,
                        companyId: invitation.companyId,
                        position: invitation.role === "PROJECTLEADER"
                            ? "Project Leader"
                            : invitation.role === "SALESMAN"
                                ? "Sales Representative"
                                : invitation.role === "WORKER"
                                    ? "Worker"
                                    : invitation.role === "CLIENT"
                                        ? "Client"
                                        : "Employee",
                        department: invitation.role === "PROJECTLEADER"
                            ? "Project Management"
                            : invitation.role === "SALESMAN"
                                ? "Sales"
                                : invitation.role === "WORKER"
                                    ? "Operations"
                                    : invitation.role === "CLIENT"
                                        ? "Client Relations"
                                        : "General",
                        startDate: invitation.updatedAt || new Date(),
                        salary: 0, // Default salary, to be updated later
                        status: "ACTIVE",
                        employmentType: "FULL_TIME", // Default employment type
                    },
                });
                console.log(`Created employee record for user ${userCompany.userId} in company ${invitation.companyId}`);
            }
            catch (error) {
                console.error(`Error processing invitation ${invitation.id}:`, error);
            }
        }
        console.log("Migration completed successfully");
    }
    catch (error) {
        console.error("Error during migration:", error);
    }
    finally {
        await prisma_js_1.prisma.$disconnect();
    }
}
// Run the migration
migrateApprovedRequestsToEmployees()
    .then(() => {
    console.log("Script execution completed");
    process.exit(0);
})
    .catch((error) => {
    console.error("Script execution failed:", error);
    process.exit(1);
});
//# sourceMappingURL=migrate-approved-requests-to-employees.js.map