"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedEmailData = seedEmailData;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function seedEmailData() {
    console.log("🌱 Seeding email data...");
    try {
        // Get the first user for testing
        const user = await prisma.user.findFirst();
        if (!user) {
            console.log("❌ No users found. Please seed users first.");
            return;
        }
        console.log(`📧 Creating email data for user: ${user.email}`);
        // Create default email account
        let emailAccount = await prisma.emailAccount.findFirst({
            where: {
                userId: user.id,
                email: user.email,
            },
        });
        if (!emailAccount) {
            emailAccount = await prisma.emailAccount.create({
                data: {
                    userId: user.id,
                    email: user.email,
                    name: user.name || "Default Account",
                    provider: "CUSTOM",
                    isDefault: true,
                },
            });
        }
        console.log(`📧 Email account created: ${emailAccount.email}`);
        // Create email folders
        const folders = [
            { name: "Inbox", type: "INBOX" },
            { name: "Sent", type: "SENT" },
            { name: "Important", type: "IMPORTANT" },
            { name: "Spam", type: "SPAM" },
            { name: "Drafts", type: "DRAFTS" },
        ];
        const createdFolders = [];
        for (const folder of folders) {
            let emailFolder = await prisma.emailFolder.findFirst({
                where: {
                    accountId: emailAccount.id,
                    type: folder.type,
                },
            });
            if (!emailFolder) {
                emailFolder = await prisma.emailFolder.create({
                    data: {
                        accountId: emailAccount.id,
                        name: folder.name,
                        type: folder.type,
                    },
                });
            }
            createdFolders.push(emailFolder);
            console.log(`📁 Folder created: ${emailFolder.name}`);
        }
        // Get folders by type
        const inboxFolder = createdFolders.find((f) => f.type === "INBOX");
        const sentFolder = createdFolders.find((f) => f.type === "SENT");
        const importantFolder = createdFolders.find((f) => f.type === "IMPORTANT");
        const spamFolder = createdFolders.find((f) => f.type === "SPAM");
        // Sample email data
        const sampleEmails = [
            // Inbox emails
            {
                folderId: inboxFolder.id,
                from: "Joshua Miller <<EMAIL>>",
                to: [user.email],
                subject: "Welcome to CoManager!",
                body: "Hi there!\n\nWelcome to CoManager, your comprehensive business management platform. We're excited to have you on board!\n\nThis platform will help you manage all aspects of your business from HR to accounting, production to sales. Take some time to explore the different modules and see how they can streamline your operations.\n\nIf you have any questions, don't hesitate to reach out to our support team.\n\nBest regards,\nThe CoManager Team",
                status: "UNREAD",
                receivedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            },
            {
                folderId: inboxFolder.id,
                from: "Hermann Schmidt <<EMAIL>>",
                to: [user.email],
                subject: "Monthly Business Report Available",
                body: "Hello,\n\nYour monthly business report is now available in the system. This report includes:\n\n• Financial overview\n• Sales performance\n• HR metrics\n• Production statistics\n\nYou can access it from the Reports section in your dashboard.\n\nRegards,\nHermann Schmidt\nBusiness Analyst",
                status: "READ",
                receivedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
            },
            {
                folderId: inboxFolder.id,
                from: "Clarissa Johnson <<EMAIL>>",
                to: [user.email],
                subject: "New Feature: AI Assistant",
                body: "Hi!\n\nWe're thrilled to announce our new AI Assistant feature! This intelligent helper can:\n\n• Answer questions about your business data\n• Generate reports and insights\n• Help with task automation\n• Provide recommendations\n\nTry it out by clicking the AI button in your toolbar.\n\nHappy exploring!\nClarissa",
                status: "UNREAD",
                receivedAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
            },
            // Sent emails
            {
                folderId: sentFolder.id,
                from: user.email,
                to: ["<EMAIL>"],
                subject: "Question about invoice generation",
                body: "Hello Support Team,\n\nI have a question about the invoice generation feature. How can I customize the invoice template to include our company logo?\n\nThanks for your help!\n\nBest regards",
                status: "READ",
                receivedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
            },
            {
                folderId: sentFolder.id,
                from: user.email,
                to: ["<EMAIL>"],
                subject: "Weekly team meeting notes",
                body: "Hi Team,\n\nHere are the notes from our weekly meeting:\n\n1. Q4 goals review\n2. New project assignments\n3. Budget planning for next quarter\n\nPlease review and let me know if I missed anything.\n\nThanks!",
                status: "READ",
                receivedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
            },
            // Important emails
            {
                folderId: importantFolder.id,
                from: "Eddy Wilson <<EMAIL>>",
                to: [user.email],
                subject: "URGENT: Security Update Required",
                body: "IMPORTANT SECURITY NOTICE\n\nWe have identified a security vulnerability that requires immediate attention. Please:\n\n1. Update your password immediately\n2. Enable two-factor authentication\n3. Review your recent login activity\n\nThis is critical for the security of your account and business data.\n\nSecurity Team\nCoManager",
                status: "UNREAD",
                receivedAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
            },
            {
                folderId: importantFolder.id,
                from: "Clark Johnson <<EMAIL>>",
                to: [user.email],
                subject: "Contract Renewal Reminder",
                body: "Dear Valued Customer,\n\nThis is a friendly reminder that your CoManager subscription will expire in 30 days.\n\nTo ensure uninterrupted service, please renew your subscription before the expiration date.\n\nYou can manage your subscription from the Billing section in your account.\n\nThank you for choosing CoManager!\n\nBest regards,\nClark Johnson\nAccount Manager",
                status: "READ",
                receivedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
            },
            // Spam emails
            {
                folderId: spamFolder.id,
                from: "<EMAIL>",
                to: [user.email],
                subject: "You have won $1,000,000!",
                body: "Congratulations! You have been selected as our lucky winner!\n\nClick here to claim your prize: [SUSPICIOUS LINK]\n\nThis is definitely not a scam...",
                status: "DELETED",
                receivedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
            },
            {
                folderId: spamFolder.id,
                from: "<EMAIL>",
                to: [user.email],
                subject: "Urgent: Verify your account",
                body: "Your account will be suspended unless you verify it immediately!\n\nClick this link: [PHISHING LINK]\n\nDo not fall for this!",
                status: "DELETED",
                receivedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
            },
        ];
        // Create email messages
        for (const email of sampleEmails) {
            await prisma.emailMessage.create({
                data: {
                    accountId: emailAccount.id,
                    folderId: email.folderId,
                    from: email.from,
                    to: email.to,
                    subject: email.subject,
                    body: email.body,
                    status: email.status,
                    receivedAt: email.receivedAt,
                },
            });
        }
        console.log(`📧 Created ${sampleEmails.length} sample emails`);
        console.log("✅ Email data seeding completed successfully!");
    }
    catch (error) {
        console.error("❌ Error seeding email data:", error);
        throw error;
    }
}
// Run if called directly
if (require.main === module) {
    seedEmailData()
        .catch((e) => {
        console.error(e);
        process.exit(1);
    })
        .finally(async () => {
        await prisma.$disconnect();
    });
}
//# sourceMappingURL=email-data.js.map