"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getQuotationTemplates = exports.rejectQuotation = exports.acceptQuotation = exports.sendQuotation = exports.deleteQuotationItem = exports.updateQuotationItem = exports.addQuotationItem = exports.deleteQuotation = exports.updateQuotation = exports.createQuotation = exports.getQuotationById = exports.getAllQuotations = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Get all quotations
const getAllQuotations = async (req, res) => {
    try {
        const { status, companyId, search, minAmount, maxAmount, startDate, endDate, sortBy = "createdAt", sortOrder = "desc", page = 1, limit = 10, } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        // Build filter conditions
        const where = {};
        if (status) {
            where.status = status;
        }
        if (companyId) {
            where.companyId = companyId;
        }
        if (minAmount) {
            where.totalAmount = {
                ...where.totalAmount,
                gte: Number(minAmount),
            };
        }
        if (maxAmount) {
            where.totalAmount = {
                ...where.totalAmount,
                lte: Number(maxAmount),
            };
        }
        if (startDate) {
            where.issueDate = {
                ...where.issueDate,
                gte: new Date(startDate),
            };
        }
        if (endDate) {
            where.issueDate = {
                ...where.issueDate,
                lte: new Date(endDate),
            };
        }
        if (search) {
            where.OR = [
                { title: { contains: search, mode: "insensitive" } },
                {
                    quotationNumber: { contains: search, mode: "insensitive" },
                },
                { contactName: { contains: search, mode: "insensitive" } },
                { contactEmail: { contains: search, mode: "insensitive" } },
            ];
        }
        // Get total count for pagination
        const totalCount = await prisma_js_1.prisma.quotation.count({ where });
        // Get quotations with pagination, sorting and filtering
        const quotations = await prisma_js_1.prisma.quotation.findMany({
            where,
            include: {
                company: {
                    select: {
                        id: true,
                        name: true,
                        logo: true,
                    },
                },
                deal: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                _count: {
                    select: {
                        items: true,
                    },
                },
            },
            orderBy: {
                [sortBy]: sortOrder,
            },
            skip,
            take: Number(limit),
        });
        res.status(200).json({
            quotations,
            pagination: {
                total: totalCount,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(totalCount / Number(limit)),
            },
        });
    }
    catch (error) {
        console.error("Error fetching quotations:", error);
        res.status(500).json({ error: "Failed to fetch quotations" });
    }
};
exports.getAllQuotations = getAllQuotations;
// Get quotation by ID
const getQuotationById = async (req, res) => {
    try {
        const { id } = req.params;
        const quotation = await prisma_js_1.prisma.quotation.findUnique({
            where: { id: Number(id) },
            include: {
                company: {
                    select: {
                        id: true,
                        name: true,
                        logo: true,
                        email: true,
                        phone: true,
                        address: true,
                    },
                },
                deal: {
                    select: {
                        id: true,
                        name: true,
                        stage: true,
                    },
                },
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
                items: {
                    include: {
                        product: true,
                    },
                    orderBy: {
                        createdAt: "asc",
                    },
                },
            },
        });
        if (!quotation) {
            res.status(404).json({ error: "Quotation not found" });
            return;
        }
        res.status(200).json(quotation);
    }
    catch (error) {
        console.error("Error fetching quotation:", error);
        res.status(500).json({ error: "Failed to fetch quotation" });
    }
};
exports.getQuotationById = getQuotationById;
// Create a new quotation
const createQuotation = async (req, res) => {
    try {
        const { title, dealId, companyId, contactName, contactEmail, contactPhone, issueDate, expiryDate, terms, notes, items, } = req.body;
        const userId = req.user.id;
        // Generate quotation number
        const quotationNumber = await generateQuotationNumber();
        // Calculate totals
        let subtotal = 0;
        let taxAmount = 0;
        let discountAmount = 0;
        // Start a transaction
        const result = await prisma_js_1.prisma.$transaction(async (prisma) => {
            // Create quotation
            const quotation = await prisma.quotation.create({
                data: {
                    quotationNumber,
                    title,
                    dealId,
                    companyId,
                    contactName,
                    contactEmail,
                    contactPhone,
                    status: "DRAFT",
                    issueDate: new Date(issueDate),
                    expiryDate: new Date(expiryDate),
                    subtotal: 0,
                    taxAmount: 0,
                    discountAmount: 0,
                    totalAmount: 0,
                    terms,
                    notes,
                    createdById: userId,
                },
                include: {
                    company: {
                        select: {
                            id: true,
                            name: true,
                            logo: true,
                        },
                    },
                    deal: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    createdBy: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            avatar: true,
                        },
                    },
                },
            });
            // Add items if provided
            if (items && Array.isArray(items) && items.length > 0) {
                for (const item of items) {
                    const { productId, description, quantity, unitPrice, discount = 0, taxRate = 0, } = item;
                    // Calculate item total
                    const itemSubtotal = quantity * unitPrice;
                    const itemDiscount = itemSubtotal * (discount / 100);
                    const itemTax = (itemSubtotal - itemDiscount) * (taxRate / 100);
                    const itemTotal = itemSubtotal - itemDiscount + itemTax;
                    // Add to totals
                    subtotal += itemSubtotal;
                    discountAmount += itemDiscount;
                    taxAmount += itemTax;
                    // Create quotation item
                    await prisma.quotationItem.create({
                        data: {
                            quotationId: quotation.id,
                            productId,
                            description,
                            quantity,
                            unitPrice,
                            discount,
                            taxRate,
                            total: itemTotal,
                        },
                    });
                }
                // Update quotation with totals
                await prisma.quotation.update({
                    where: { id: quotation.id },
                    data: {
                        subtotal,
                        taxAmount,
                        discountAmount,
                        totalAmount: subtotal - discountAmount + taxAmount,
                    },
                });
            }
            // Get the updated quotation with items
            return await prisma.quotation.findUnique({
                where: { id: quotation.id },
                include: {
                    company: {
                        select: {
                            id: true,
                            name: true,
                            logo: true,
                        },
                    },
                    deal: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    createdBy: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            avatar: true,
                        },
                    },
                    items: {
                        include: {
                            product: true,
                        },
                    },
                },
            });
        });
        res.status(201).json(result);
    }
    catch (error) {
        console.error("Error creating quotation:", error);
        res.status(500).json({ error: "Failed to create quotation" });
    }
};
exports.createQuotation = createQuotation;
// Update a quotation
const updateQuotation = async (req, res) => {
    try {
        const { id } = req.params;
        const { title, dealId, companyId, contactName, contactEmail, contactPhone, status, issueDate, expiryDate, terms, notes, } = req.body;
        // Check if quotation exists
        const existingQuotation = await prisma_js_1.prisma.quotation.findUnique({
            where: { id: Number(id) },
        });
        if (!existingQuotation) {
            res.status(404).json({ error: "Quotation not found" });
            return;
        }
        // Prevent updating if quotation is already accepted or rejected
        if ((existingQuotation.status === "ACCEPTED" ||
            existingQuotation.status === "REJECTED") &&
            status !== "ACCEPTED" &&
            status !== "REJECTED") {
            res.status(400).json({
                error: "Cannot update a quotation that has been accepted or rejected",
            });
            return;
        }
        // Update quotation
        const updatedQuotation = await prisma_js_1.prisma.quotation.update({
            where: { id: Number(id) },
            data: {
                title,
                dealId,
                companyId,
                contactName,
                contactEmail,
                contactPhone,
                status,
                issueDate: issueDate ? new Date(issueDate) : undefined,
                expiryDate: expiryDate ? new Date(expiryDate) : undefined,
                terms,
                notes,
                updatedAt: new Date(),
            },
            include: {
                company: {
                    select: {
                        id: true,
                        name: true,
                        logo: true,
                    },
                },
                deal: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                items: {
                    include: {
                        product: true,
                    },
                },
            },
        });
        res.status(200).json(updatedQuotation);
    }
    catch (error) {
        console.error("Error updating quotation:", error);
        res.status(500).json({ error: "Failed to update quotation" });
    }
};
exports.updateQuotation = updateQuotation;
// Delete a quotation
const deleteQuotation = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if quotation exists
        const quotation = await prisma_js_1.prisma.quotation.findUnique({
            where: { id: Number(id) },
        });
        if (!quotation) {
            res.status(404).json({ error: "Quotation not found" });
            return;
        }
        // Prevent deleting if quotation is already accepted
        if (quotation.status === "ACCEPTED") {
            res
                .status(400)
                .json({ error: "Cannot delete a quotation that has been accepted" });
            return;
        }
        // Delete quotation (cascade will handle items)
        await prisma_js_1.prisma.quotation.delete({
            where: { id: Number(id) },
        });
        res.status(200).json({ message: "Quotation deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting quotation:", error);
        res.status(500).json({ error: "Failed to delete quotation" });
    }
};
exports.deleteQuotation = deleteQuotation;
// Add item to quotation
const addQuotationItem = async (req, res) => {
    try {
        const { id } = req.params;
        const { productId, description, quantity, unitPrice, discount = 0, taxRate = 0, } = req.body;
        // Check if quotation exists
        const quotation = await prisma_js_1.prisma.quotation.findUnique({
            where: { id: Number(id) },
            include: {
                items: true,
            },
        });
        if (!quotation) {
            res.status(404).json({ error: "Quotation not found" });
            return;
        }
        // Prevent updating if quotation is already accepted or rejected
        if (quotation.status === "ACCEPTED" || quotation.status === "REJECTED") {
            res.status(400).json({
                error: "Cannot modify a quotation that has been accepted or rejected",
            });
            return;
        }
        // Calculate item total
        const itemSubtotal = quantity * unitPrice;
        const itemDiscount = itemSubtotal * (discount / 100);
        const itemTax = (itemSubtotal - itemDiscount) * (taxRate / 100);
        const itemTotal = itemSubtotal - itemDiscount + itemTax;
        // Add item to quotation
        const quotationItem = await prisma_js_1.prisma.quotationItem.create({
            data: {
                quotationId: Number(id),
                productId,
                description,
                quantity,
                unitPrice,
                discount,
                taxRate,
                total: itemTotal,
            },
            include: {
                product: true,
            },
        });
        // Recalculate quotation totals
        const items = [...quotation.items, quotationItem];
        const subtotal = items.reduce((sum, item) => sum + item.quantity * item.unitPrice, 0);
        const discountAmount = items.reduce((sum, item) => {
            const itemSubtotal = item.quantity * item.unitPrice;
            return sum + itemSubtotal * (item.discount / 100);
        }, 0);
        const taxAmount = items.reduce((sum, item) => {
            const itemSubtotal = item.quantity * item.unitPrice;
            const itemDiscount = itemSubtotal * (item.discount / 100);
            return sum + (itemSubtotal - itemDiscount) * (item.taxRate / 100);
        }, 0);
        const totalAmount = subtotal - discountAmount + taxAmount;
        // Update quotation with new totals
        await prisma_js_1.prisma.quotation.update({
            where: { id: Number(id) },
            data: {
                subtotal,
                taxAmount,
                discountAmount,
                totalAmount,
                updatedAt: new Date(),
            },
        });
        res.status(201).json(quotationItem);
    }
    catch (error) {
        console.error("Error adding quotation item:", error);
        res.status(500).json({ error: "Failed to add quotation item" });
    }
};
exports.addQuotationItem = addQuotationItem;
// Update quotation item
const updateQuotationItem = async (req, res) => {
    try {
        const { id, itemId } = req.params;
        const { productId, description, quantity, unitPrice, discount = 0, taxRate = 0, } = req.body;
        // Check if quotation exists
        const quotation = await prisma_js_1.prisma.quotation.findUnique({
            where: { id: Number(id) },
            include: {
                items: true,
            },
        });
        if (!quotation) {
            res.status(404).json({ error: "Quotation not found" });
            return;
        }
        // Check if item exists
        const existingItem = quotation.items.find((item) => item.id === Number(itemId));
        if (!existingItem) {
            res.status(404).json({ error: "Quotation item not found" });
            return;
        }
        // Prevent updating if quotation is already accepted or rejected
        if (quotation.status === "ACCEPTED" || quotation.status === "REJECTED") {
            res.status(400).json({
                error: "Cannot modify a quotation that has been accepted or rejected",
            });
            return;
        }
        // Calculate item total
        const itemSubtotal = quantity * unitPrice;
        const itemDiscount = itemSubtotal * (discount / 100);
        const itemTax = (itemSubtotal - itemDiscount) * (taxRate / 100);
        const itemTotal = itemSubtotal - itemDiscount + itemTax;
        // Update quotation item
        const updatedItem = await prisma_js_1.prisma.quotationItem.update({
            where: { id: Number(itemId) },
            data: {
                productId,
                description,
                quantity,
                unitPrice,
                discount,
                taxRate,
                total: itemTotal,
                updatedAt: new Date(),
            },
            include: {
                product: true,
            },
        });
        // Recalculate quotation totals
        const updatedItems = quotation.items.map((item) => item.id === Number(itemId) ? updatedItem : item);
        const subtotal = updatedItems.reduce((sum, item) => sum + item.quantity * item.unitPrice, 0);
        const discountAmount = updatedItems.reduce((sum, item) => {
            const itemSubtotal = item.quantity * item.unitPrice;
            return sum + itemSubtotal * (item.discount / 100);
        }, 0);
        const taxAmount = updatedItems.reduce((sum, item) => {
            const itemSubtotal = item.quantity * item.unitPrice;
            const itemDiscount = itemSubtotal * (item.discount / 100);
            return sum + (itemSubtotal - itemDiscount) * (item.taxRate / 100);
        }, 0);
        const totalAmount = subtotal - discountAmount + taxAmount;
        // Update quotation with new totals
        await prisma_js_1.prisma.quotation.update({
            where: { id: Number(id) },
            data: {
                subtotal,
                taxAmount,
                discountAmount,
                totalAmount,
                updatedAt: new Date(),
            },
        });
        res.status(200).json(updatedItem);
    }
    catch (error) {
        console.error("Error updating quotation item:", error);
        res.status(500).json({ error: "Failed to update quotation item" });
    }
};
exports.updateQuotationItem = updateQuotationItem;
// Delete quotation item
const deleteQuotationItem = async (req, res) => {
    try {
        const { id, itemId } = req.params;
        // Check if quotation exists
        const quotation = await prisma_js_1.prisma.quotation.findUnique({
            where: { id: Number(id) },
            include: {
                items: true,
            },
        });
        if (!quotation) {
            res.status(404).json({ error: "Quotation not found" });
            return;
        }
        // Check if item exists
        const existingItem = quotation.items.find((item) => item.id === Number(itemId));
        if (!existingItem) {
            res.status(404).json({ error: "Quotation item not found" });
            return;
        }
        // Prevent updating if quotation is already accepted or rejected
        if (quotation.status === "ACCEPTED" || quotation.status === "REJECTED") {
            res.status(400).json({
                error: "Cannot modify a quotation that has been accepted or rejected",
            });
            return;
        }
        // Delete quotation item
        await prisma_js_1.prisma.quotationItem.delete({
            where: { id: Number(itemId) },
        });
        // Recalculate quotation totals
        const remainingItems = quotation.items.filter((item) => item.id !== Number(itemId));
        const subtotal = remainingItems.reduce((sum, item) => sum + item.quantity * item.unitPrice, 0);
        const discountAmount = remainingItems.reduce((sum, item) => {
            const itemSubtotal = item.quantity * item.unitPrice;
            return sum + itemSubtotal * (item.discount / 100);
        }, 0);
        const taxAmount = remainingItems.reduce((sum, item) => {
            const itemSubtotal = item.quantity * item.unitPrice;
            const itemDiscount = itemSubtotal * (item.discount / 100);
            return sum + (itemSubtotal - itemDiscount) * (item.taxRate / 100);
        }, 0);
        const totalAmount = subtotal - discountAmount + taxAmount;
        // Update quotation with new totals
        await prisma_js_1.prisma.quotation.update({
            where: { id: Number(id) },
            data: {
                subtotal,
                taxAmount,
                discountAmount,
                totalAmount,
                updatedAt: new Date(),
            },
        });
        res.status(200).json({ message: "Quotation item deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting quotation item:", error);
        res.status(500).json({ error: "Failed to delete quotation item" });
    }
};
exports.deleteQuotationItem = deleteQuotationItem;
// Send quotation
const sendQuotation = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if quotation exists
        const quotation = await prisma_js_1.prisma.quotation.findUnique({
            where: { id: Number(id) },
            include: {
                company: true,
                items: true,
            },
        });
        if (!quotation) {
            res.status(404).json({ error: "Quotation not found" });
            return;
        }
        // Prevent sending if quotation has no items
        if (quotation.items.length === 0) {
            res.status(400).json({ error: "Cannot send a quotation with no items" });
            return;
        }
        // Update quotation status to SENT
        const updatedQuotation = await prisma_js_1.prisma.quotation.update({
            where: { id: Number(id) },
            data: {
                status: "SENT",
                sentAt: new Date(),
                updatedAt: new Date(),
            },
        });
        // In a real implementation, this would send an email to the client
        // sendQuotationEmail(quotation)
        res.status(200).json({
            ...updatedQuotation,
            message: "Quotation sent successfully",
        });
    }
    catch (error) {
        console.error("Error sending quotation:", error);
        res.status(500).json({ error: "Failed to send quotation" });
    }
};
exports.sendQuotation = sendQuotation;
// Accept quotation
const acceptQuotation = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if quotation exists
        const quotation = await prisma_js_1.prisma.quotation.findUnique({
            where: { id: Number(id) },
            include: {
                deal: true,
            },
        });
        if (!quotation) {
            res.status(404).json({ error: "Quotation not found" });
            return;
        }
        // Update quotation status to ACCEPTED
        const updatedQuotation = await prisma_js_1.prisma.quotation.update({
            where: { id: Number(id) },
            data: {
                status: "ACCEPTED",
                acceptedAt: new Date(),
                updatedAt: new Date(),
            },
        });
        // If quotation is linked to a deal, update the deal
        if (quotation.deal) {
            await prisma_js_1.prisma.deal.update({
                where: { id: quotation.deal.id },
                data: {
                    stage: "CLOSED_WON",
                    probability: 100,
                    actualCloseDate: new Date(),
                    updatedAt: new Date(),
                },
            });
        }
        res.status(200).json({
            ...updatedQuotation,
            message: "Quotation accepted successfully",
        });
    }
    catch (error) {
        console.error("Error accepting quotation:", error);
        res.status(500).json({ error: "Failed to accept quotation" });
    }
};
exports.acceptQuotation = acceptQuotation;
// Reject quotation
const rejectQuotation = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if quotation exists
        const quotation = await prisma_js_1.prisma.quotation.findUnique({
            where: { id: Number(id) },
        });
        if (!quotation) {
            res.status(404).json({ error: "Quotation not found" });
            return;
        }
        // Update quotation status to REJECTED
        const updatedQuotation = await prisma_js_1.prisma.quotation.update({
            where: { id: Number(id) },
            data: {
                status: "REJECTED",
                rejectedAt: new Date(),
                updatedAt: new Date(),
            },
        });
        res.status(200).json({
            ...updatedQuotation,
            message: "Quotation rejected successfully",
        });
    }
    catch (error) {
        console.error("Error rejecting quotation:", error);
        res.status(500).json({ error: "Failed to reject quotation" });
    }
};
exports.rejectQuotation = rejectQuotation;
// Get quotation templates
const getQuotationTemplates = async (req, res) => {
    try {
        // In a real implementation, this would fetch quotation templates from the database
        // For now, return some predefined templates
        const templates = [
            {
                id: "1",
                name: "Standard Quotation",
                description: "Basic quotation template for general use",
                content: {
                    title: "Quotation for {company_name}",
                    terms: "Payment due within 30 days of invoice date. All prices are exclusive of taxes unless otherwise stated.",
                    notes: "Thank you for your business!",
                },
            },
            {
                id: "2",
                name: "Detailed Service Quotation",
                description: "Detailed template for service-based offerings",
                content: {
                    title: "Service Quotation for {company_name}",
                    terms: "Payment terms: 50% upfront, 50% upon completion. Valid for 30 days from issue date.",
                    notes: "This quotation includes all labor and materials as specified. Any additional services will require a separate quotation.",
                },
            },
            {
                id: "3",
                name: "Product Bundle Quotation",
                description: "Template for product bundles with discounts",
                content: {
                    title: "Product Bundle Quotation for {company_name}",
                    terms: "Payment due within 14 days of invoice date. Delivery within 10 business days after payment receipt.",
                    notes: "Bundle discounts applied as shown. Prices valid for 15 days from issue date.",
                },
            },
            {
                id: "4",
                name: "Enterprise Solution Quotation",
                description: "Comprehensive template for enterprise clients",
                content: {
                    title: "Enterprise Solution Proposal for {company_name}",
                    terms: "Payment schedule: 30% upon signing, 40% at project midpoint, 30% upon completion. Valid for 60 days.",
                    notes: "This quotation includes implementation services, training, and 12 months of support as detailed in the attached statement of work.",
                },
            },
            {
                id: "5",
                name: "Subscription Service Quotation",
                description: "Template for recurring subscription services",
                content: {
                    title: "Subscription Service Quotation for {company_name}",
                    terms: "Monthly subscription billed in advance. Minimum commitment of 12 months. 30-day cancellation notice required thereafter.",
                    notes: "Subscription includes all features and updates as described in the service level agreement.",
                },
            },
        ];
        res.status(200).json(templates);
    }
    catch (error) {
        console.error("Error fetching quotation templates:", error);
        res.status(500).json({ error: "Failed to fetch quotation templates" });
    }
};
exports.getQuotationTemplates = getQuotationTemplates;
// Helper function to generate a unique quotation number
const generateQuotationNumber = async () => {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    // Get the count of quotations for this month
    const count = await prisma_js_1.prisma.quotation.count({
        where: {
            createdAt: {
                gte: new Date(date.getFullYear(), date.getMonth(), 1),
                lt: new Date(date.getFullYear(), date.getMonth() + 1, 1),
            },
        },
    });
    // Generate a sequential number
    const sequence = (count + 1).toString().padStart(4, "0");
    return `Q${year}${month}-${sequence}`;
};
//# sourceMappingURL=quotations.controller.js.map