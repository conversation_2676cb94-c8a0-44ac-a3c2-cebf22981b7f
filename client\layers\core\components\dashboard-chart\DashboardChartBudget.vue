<script setup lang="ts">
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";

const { t, locale } = useI18n();
const currentLocale = computed(() => locale.value);

const colorMode = useColorMode();
const isDark = computed(() => colorMode.value === "dark");

// Budget categories with different colors
const categories = ref([
  {
    name: computed(() =>
      currentLocale.value === "et" ? "Seadmed" : "Equipment"
    ),
    value: 35,
    color: "var(--color-primary-500)",
    tooltip: false,
  },
  {
    name: computed(() =>
      currentLocale.value === "et" ? "Turundus" : "Marketing"
    ),
    value: 25,
    color: "var(--color-info-500)",
    tooltip: false,
  },
  {
    name: computed(() =>
      currentLocale.value === "et" ? "Tarkvara" : "Software"
    ),
    value: 20,
    color: "var(--color-success-500)",
    tooltip: false,
  },
  {
    name: computed(() =>
      currentLocale.value === "et" ? "Reisimine" : "Travel"
    ),
    value: 15,
    color: "var(--color-warning-500)",
    tooltip: false,
  },
  {
    name: computed(() => (currentLocale.value === "et" ? "Muu" : "Other")),
    value: 5,
    color: "var(--color-muted-500)",
    tooltip: false,
  },
]);

// Calculate the total
const total = computed(() =>
  categories.value.reduce((sum, category) => sum + category.value, 0)
);

// Format currency
function formatCurrency(value: number) {
  return new Intl.NumberFormat(
    currentLocale.value === "et" ? "et-EE" : "en-US",
    {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }
  ).format(value);
}

// Calculate the start and end angles for each category
function calculateAngles() {
  let startAngle = 0;
  return categories.value.map((category) => {
    const angle = (category.value / total.value) * 360;
    const result = {
      ...category,
      startAngle,
      endAngle: startAngle + angle,
      percentage: Math.round((category.value / total.value) * 100),
    };
    startAngle += angle;
    return result;
  });
}

const categoriesWithAngles = computed(() => calculateAngles());

// Function to convert polar coordinates to cartesian
function polarToCartesian(
  centerX: number,
  centerY: number,
  radius: number,
  angleInDegrees: number
) {
  const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180.0;
  return {
    x: centerX + radius * Math.cos(angleInRadians),
    y: centerY + radius * Math.sin(angleInRadians),
  };
}

// Function to create an SVG arc path
function describeArc(
  x: number,
  y: number,
  radius: number,
  startAngle: number,
  endAngle: number
) {
  const start = polarToCartesian(x, y, radius, endAngle);
  const end = polarToCartesian(x, y, radius, startAngle);
  const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
  return [
    "M",
    start.x,
    start.y,
    "A",
    radius,
    radius,
    0,
    largeArcFlag,
    0,
    end.x,
    end.y,
    "L",
    x,
    y,
    "Z",
  ].join(" ");
}

// Calculate the position for the percentage label
function getLabelPosition(
  startAngle: number,
  endAngle: number,
  radius: number = 60
) {
  const midAngle = (startAngle + endAngle) / 2;
  const pos = polarToCartesian(100, 100, radius, midAngle);
  return pos;
}

// Show tooltip for a category
function showTooltip(index: number) {
  categories.value = categories.value.map((cat, i) => ({
    ...cat,
    tooltip: i === index,
  }));
}

// Hide all tooltips
function hideTooltips() {
  categories.value = categories.value.map((cat) => ({
    ...cat,
    tooltip: false,
  }));
}
</script>

<template>
  <div class="flex items-start justify-between">
    <!-- Donut Chart -->
    <div class="relative h-40 w-40">
      <svg
        class="h-full w-full"
        viewBox="0 0 200 200"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <!-- Pie chart segments -->
        <g v-for="(category, index) in categoriesWithAngles" :key="index">
          <path
            :d="
              describeArc(100, 100, 80, category.startAngle, category.endAngle)
            "
            :fill="category.color"
            @mouseenter="showTooltip(index)"
            @mouseleave="hideTooltips"
            class="cursor-pointer transition-opacity hover:opacity-80"
          />

          <!-- Percentage labels -->
          <text
            :x="getLabelPosition(category.startAngle, category.endAngle).x"
            :y="getLabelPosition(category.startAngle, category.endAngle).y"
            text-anchor="middle"
            dominant-baseline="middle"
            fill="white"
            font-size="10"
            font-weight="bold"
          >
            {{ category.percentage }}%
          </text>
        </g>

        <!-- Center circle (for donut chart) -->
        <circle
          cx="100"
          cy="100"
          r="40"
          :fill="isDark ? 'var(--color-muted-900)' : 'var(--color-muted-100)'"
        />

        <!-- Total in the center -->
        <text
          x="100"
          y="95"
          text-anchor="middle"
          class="text-muted-500 dark:text-muted-300"
          font-size="10"
        >
          {{ currentLocale === "et" ? "Kokku" : "Total" }}
        </text>
        <text
          x="100"
          y="110"
          text-anchor="middle"
          class="text-muted-800 dark:text-white"
          font-size="12"
          font-weight="bold"
        >
          {{ formatCurrency(total * 1000) }}
        </text>
      </svg>

      <!-- Tooltips -->
      <div
        v-for="(category, index) in categories"
        :key="`tooltip-${index}`"
        v-show="category.tooltip"
        class="absolute top-0 left-0 z-10 bg-white dark:bg-muted-800 shadow-lg rounded-md p-2 text-xs min-w-32 transform -translate-y-full"
        :style="{
          left: `${
            getLabelPosition(
              categoriesWithAngles[index].startAngle,
              categoriesWithAngles[index].endAngle,
              80
            ).x
          }px`,
          top: `${
            getLabelPosition(
              categoriesWithAngles[index].startAngle,
              categoriesWithAngles[index].endAngle,
              80
            ).y
          }px`,
        }"
      >
        <div class="flex items-center gap-2 mb-1">
          <div
            :style="{ backgroundColor: category.color }"
            class="w-2 h-2 rounded-full"
          ></div>
          <span class="font-medium text-muted-800 dark:text-white">{{
            category.name
          }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-muted-500"
            >{{ categoriesWithAngles[index].percentage }}%</span
          >
          <span class="font-medium text-muted-800 dark:text-white">{{
            formatCurrency(category.value * 1000)
          }}</span>
        </div>
      </div>
    </div>

    <!-- Legend on the side -->
    <div class="flex flex-col gap-2 mt-2">
      <div
        v-for="(category, index) in categoriesWithAngles"
        :key="index"
        class="flex items-center gap-2 group cursor-pointer"
        @mouseenter="showTooltip(index)"
        @mouseleave="hideTooltips"
      >
        <div
          :style="{ backgroundColor: category.color }"
          class="w-3 h-3 rounded-full"
        ></div>
        <div class="flex flex-col">
          <span
            class="text-xs font-medium text-muted-800 dark:text-white group-hover:text-primary-500 dark:group-hover:text-primary-400 transition-colors"
          >
            {{ category.name }}
          </span>
          <span class="text-[10px] text-muted-500">
            {{ formatCurrency(category.value * 1000) }} ({{
              category.percentage
            }}%)
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
