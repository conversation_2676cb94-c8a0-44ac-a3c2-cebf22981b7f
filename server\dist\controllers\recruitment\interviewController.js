"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getInterviewsByStatus = exports.getUpcomingInterviews = exports.rescheduleInterview = exports.completeInterview = exports.cancelInterview = exports.updateInterview = exports.scheduleInterview = exports.getInterview = exports.getInterviews = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// Get all interviews for a company
const getInterviews = async (req, res) => {
    try {
        const { companyId } = req.params;
        const { status, type, page = 1, limit = 10 } = req.query;
        const where = {
            application: {
                job: { companyId: parseInt(companyId) }
            }
        };
        if (status)
            where.status = status;
        if (type)
            where.type = type;
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const [interviews, total] = await Promise.all([
            prisma.interview.findMany({
                where,
                include: {
                    application: {
                        include: {
                            job: {
                                select: { id: true, title: true, department: true }
                            },
                            applicant: {
                                select: { id: true, firstName: true, lastName: true, email: true, avatar: true }
                            }
                        }
                    },
                    interviewer: {
                        select: { id: true, firstName: true, lastName: true, email: true }
                    }
                },
                orderBy: { scheduledAt: 'asc' },
                skip,
                take: parseInt(limit)
            }),
            prisma.interview.count({ where })
        ]);
        res.json({
            data: interviews,
            total,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(total / parseInt(limit))
        });
    }
    catch (error) {
        console.error('Error fetching interviews:', error);
        res.status(500).json({ error: 'Failed to fetch interviews' });
    }
};
exports.getInterviews = getInterviews;
// Get single interview
const getInterview = async (req, res) => {
    try {
        const { id } = req.params;
        const interview = await prisma.interview.findUnique({
            where: { id: parseInt(id) },
            include: {
                application: {
                    include: {
                        job: {
                            include: {
                                company: {
                                    select: { id: true, name: true, logo: true }
                                }
                            }
                        },
                        applicant: {
                            select: { id: true, firstName: true, lastName: true, email: true, phone: true, avatar: true }
                        }
                    }
                },
                interviewer: {
                    select: { id: true, firstName: true, lastName: true, email: true }
                }
            }
        });
        if (!interview) {
            return res.status(404).json({ error: 'Interview not found' });
        }
        res.json(interview);
    }
    catch (error) {
        console.error('Error fetching interview:', error);
        res.status(500).json({ error: 'Failed to fetch interview' });
    }
};
exports.getInterview = getInterview;
// Schedule new interview
const scheduleInterview = async (req, res) => {
    try {
        const { applicationId } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }
        const interviewData = {
            ...req.body,
            applicationId: parseInt(applicationId),
            interviewerId: userId
        };
        const interview = await prisma.interview.create({
            data: interviewData,
            include: {
                application: {
                    include: {
                        job: {
                            select: { id: true, title: true }
                        },
                        applicant: {
                            select: { id: true, firstName: true, lastName: true, email: true }
                        }
                    }
                },
                interviewer: {
                    select: { id: true, firstName: true, lastName: true }
                }
            }
        });
        // Update application stage to interview
        await prisma.jobApplication.update({
            where: { id: parseInt(applicationId) },
            data: { stage: 'INTERVIEW' }
        });
        res.status(201).json(interview);
    }
    catch (error) {
        console.error('Error scheduling interview:', error);
        res.status(500).json({ error: 'Failed to schedule interview' });
    }
};
exports.scheduleInterview = scheduleInterview;
// Update interview
const updateInterview = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }
        const interview = await prisma.interview.update({
            where: { id: parseInt(id) },
            data: req.body,
            include: {
                application: {
                    include: {
                        job: {
                            select: { id: true, title: true }
                        },
                        applicant: {
                            select: { id: true, firstName: true, lastName: true, email: true }
                        }
                    }
                },
                interviewer: {
                    select: { id: true, firstName: true, lastName: true }
                }
            }
        });
        res.json(interview);
    }
    catch (error) {
        console.error('Error updating interview:', error);
        res.status(500).json({ error: 'Failed to update interview' });
    }
};
exports.updateInterview = updateInterview;
// Cancel interview
const cancelInterview = async (req, res) => {
    try {
        const { id } = req.params;
        const interview = await prisma.interview.update({
            where: { id: parseInt(id) },
            data: { status: 'CANCELLED' }
        });
        res.json(interview);
    }
    catch (error) {
        console.error('Error cancelling interview:', error);
        res.status(500).json({ error: 'Failed to cancel interview' });
    }
};
exports.cancelInterview = cancelInterview;
// Complete interview with feedback
const completeInterview = async (req, res) => {
    try {
        const { id } = req.params;
        const { feedback, rating } = req.body;
        if (rating && (rating < 1 || rating > 5)) {
            return res.status(400).json({ error: 'Rating must be between 1 and 5' });
        }
        const interview = await prisma.interview.update({
            where: { id: parseInt(id) },
            data: {
                status: 'COMPLETED',
                feedback,
                rating
            },
            include: {
                application: true
            }
        });
        // Update application stage if this was the final interview
        if (interview.application) {
            await prisma.jobApplication.update({
                where: { id: interview.application.id },
                data: { stage: 'ASSESSMENT' }
            });
        }
        res.json(interview);
    }
    catch (error) {
        console.error('Error completing interview:', error);
        res.status(500).json({ error: 'Failed to complete interview' });
    }
};
exports.completeInterview = completeInterview;
// Reschedule interview
const rescheduleInterview = async (req, res) => {
    try {
        const { id } = req.params;
        const { scheduledAt, duration, location, meetingUrl } = req.body;
        const interview = await prisma.interview.update({
            where: { id: parseInt(id) },
            data: {
                scheduledAt: new Date(scheduledAt),
                duration,
                location,
                meetingUrl,
                status: 'RESCHEDULED'
            }
        });
        res.json(interview);
    }
    catch (error) {
        console.error('Error rescheduling interview:', error);
        res.status(500).json({ error: 'Failed to reschedule interview' });
    }
};
exports.rescheduleInterview = rescheduleInterview;
// Get upcoming interviews
const getUpcomingInterviews = async (req, res) => {
    try {
        const { companyId } = req.params;
        const { days = 7 } = req.query;
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(startDate.getDate() + parseInt(days));
        const interviews = await prisma.interview.findMany({
            where: {
                application: {
                    job: { companyId: parseInt(companyId) }
                },
                scheduledAt: {
                    gte: startDate,
                    lte: endDate
                },
                status: {
                    in: ['SCHEDULED', 'RESCHEDULED']
                }
            },
            include: {
                application: {
                    include: {
                        job: {
                            select: { id: true, title: true }
                        },
                        applicant: {
                            select: { id: true, firstName: true, lastName: true, email: true, avatar: true }
                        }
                    }
                },
                interviewer: {
                    select: { id: true, firstName: true, lastName: true }
                }
            },
            orderBy: { scheduledAt: 'asc' }
        });
        res.json(interviews);
    }
    catch (error) {
        console.error('Error fetching upcoming interviews:', error);
        res.status(500).json({ error: 'Failed to fetch upcoming interviews' });
    }
};
exports.getUpcomingInterviews = getUpcomingInterviews;
// Get interviews by status
const getInterviewsByStatus = async (req, res) => {
    try {
        const { companyId, status } = req.params;
        const { page = 1, limit = 10 } = req.query;
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const [interviews, total] = await Promise.all([
            prisma.interview.findMany({
                where: {
                    application: {
                        job: { companyId: parseInt(companyId) }
                    },
                    status: status.toUpperCase()
                },
                include: {
                    application: {
                        include: {
                            job: {
                                select: { id: true, title: true, department: true }
                            },
                            applicant: {
                                select: { id: true, firstName: true, lastName: true, email: true, avatar: true }
                            }
                        }
                    },
                    interviewer: {
                        select: { id: true, firstName: true, lastName: true }
                    }
                },
                orderBy: { scheduledAt: 'asc' },
                skip,
                take: parseInt(limit)
            }),
            prisma.interview.count({
                where: {
                    application: {
                        job: { companyId: parseInt(companyId) }
                    },
                    status: status.toUpperCase()
                }
            })
        ]);
        res.json({
            data: interviews,
            total,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(total / parseInt(limit))
        });
    }
    catch (error) {
        console.error('Error fetching interviews by status:', error);
        res.status(500).json({ error: 'Failed to fetch interviews' });
    }
};
exports.getInterviewsByStatus = getInterviewsByStatus;
//# sourceMappingURL=interviewController.js.map