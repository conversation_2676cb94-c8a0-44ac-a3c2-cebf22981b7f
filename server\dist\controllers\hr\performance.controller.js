"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.acknowledgePerformanceReview = exports.getEmployeePerformanceReviews = exports.deletePerformanceReview = exports.updatePerformanceReview = exports.createPerformanceReview = exports.getPerformanceReviewById = exports.getAllPerformanceReviews = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Using centralized prisma instance from lib/prisma.js
// Get all performance reviews
const getAllPerformanceReviews = async (req, res, next) => {
    try {
        const { companyId } = req.query;
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        const reviews = await prisma_js_1.prisma.performanceReview.findMany({
            where: {
                employee: {
                    companyId: Number(companyId),
                },
            },
            include: {
                employee: {
                    include: {
                        user: true,
                    },
                },
                reviewer: true,
            },
            orderBy: {
                reviewDate: "desc",
            },
        });
        res.json(reviews);
    }
    catch (error) {
        console.error("Error fetching performance reviews:", error);
        next(error);
    }
};
exports.getAllPerformanceReviews = getAllPerformanceReviews;
// Get a specific performance review by ID
const getPerformanceReviewById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const review = await prisma_js_1.prisma.performanceReview.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                employee: {
                    include: {
                        user: true,
                    },
                },
                reviewer: true,
            },
        });
        if (!review) {
            return next(createHttpError(404, "Performance review not found"));
        }
        res.json(review);
    }
    catch (error) {
        console.error("Error fetching performance review:", error);
        next(error);
    }
};
exports.getPerformanceReviewById = getPerformanceReviewById;
// Create a new performance review
const createPerformanceReview = async (req, res, next) => {
    try {
        const { employeeId, reviewDate, rating, strengths, weaknesses, goals, feedback, nextReviewDate, } = req.body;
        if (!employeeId || !reviewDate || !rating) {
            return next(createHttpError(400, "Employee ID, review date, and rating are required"));
        }
        // Check if employee exists
        const employee = await prisma_js_1.prisma.employee.findUnique({
            where: {
                id: Number(employeeId),
            },
        });
        if (!employee) {
            return next(createHttpError(404, "Employee not found"));
        }
        const review = await prisma_js_1.prisma.performanceReview.create({
            data: {
                employeeId: Number(employeeId),
                reviewerId: req.user?.id || 1, // Add null check
                reviewDate: new Date(reviewDate),
                rating,
                strengths,
                weaknesses,
                goals,
                feedback,
                nextReviewDate: nextReviewDate ? new Date(nextReviewDate) : null,
                isAcknowledged: false,
            },
        });
        res.status(201).json(review);
    }
    catch (error) {
        console.error("Error creating performance review:", error);
        next(error);
    }
};
exports.createPerformanceReview = createPerformanceReview;
// Update a performance review
const updatePerformanceReview = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { reviewDate, rating, strengths, weaknesses, goals, feedback, nextReviewDate, } = req.body;
        const review = await prisma_js_1.prisma.performanceReview.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!review) {
            return next(createHttpError(404, "Performance review not found"));
        }
        const updatedReview = await prisma_js_1.prisma.performanceReview.update({
            where: {
                id: Number(id),
            },
            data: {
                reviewDate: reviewDate ? new Date(reviewDate) : undefined,
                rating: rating !== undefined ? rating : undefined,
                strengths: strengths !== undefined ? strengths : undefined,
                weaknesses: weaknesses !== undefined ? weaknesses : undefined,
                goals: goals !== undefined ? goals : undefined,
                feedback: feedback !== undefined ? feedback : undefined,
                nextReviewDate: nextReviewDate ? new Date(nextReviewDate) : undefined,
            },
        });
        res.json(updatedReview);
    }
    catch (error) {
        console.error("Error updating performance review:", error);
        next(error);
    }
};
exports.updatePerformanceReview = updatePerformanceReview;
// Delete a performance review
const deletePerformanceReview = async (req, res, next) => {
    try {
        const { id } = req.params;
        const review = await prisma_js_1.prisma.performanceReview.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!review) {
            return next(createHttpError(404, "Performance review not found"));
        }
        await prisma_js_1.prisma.performanceReview.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Performance review deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting performance review:", error);
        next(error);
    }
};
exports.deletePerformanceReview = deletePerformanceReview;
// Get all performance reviews for an employee
const getEmployeePerformanceReviews = async (req, res, next) => {
    try {
        const { employeeId } = req.params;
        const reviews = await prisma_js_1.prisma.performanceReview.findMany({
            where: {
                employeeId: Number(employeeId),
            },
            include: {
                reviewer: true,
                employee: {
                    include: {
                        user: true,
                    },
                },
            },
            orderBy: {
                reviewDate: "desc",
            },
        });
        res.json(reviews);
    }
    catch (error) {
        console.error("Error fetching employee performance reviews:", error);
        next(error);
    }
};
exports.getEmployeePerformanceReviews = getEmployeePerformanceReviews;
// Acknowledge a performance review
const acknowledgePerformanceReview = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { employeeComments } = req.body;
        const review = await prisma_js_1.prisma.performanceReview.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                employee: true,
            },
        });
        if (!review) {
            return next(createHttpError(404, "Performance review not found"));
        }
        // Check if the current user is the employee being reviewed
        if (review.employee.userId !== req.user?.id) {
            return next(createHttpError(403, "Only the reviewed employee can acknowledge this review"));
        }
        if (review.isAcknowledged) {
            return next(createHttpError(400, "Performance review is already acknowledged"));
        }
        const updatedReview = await prisma_js_1.prisma.performanceReview.update({
            where: {
                id: Number(id),
            },
            data: {
                isAcknowledged: true,
                acknowledgedAt: new Date(),
                employeeComments,
            },
        });
        res.json(updatedReview);
    }
    catch (error) {
        console.error("Error acknowledging performance review:", error);
        next(error);
    }
};
exports.acknowledgePerformanceReview = acknowledgePerformanceReview;
//# sourceMappingURL=performance.controller.js.map