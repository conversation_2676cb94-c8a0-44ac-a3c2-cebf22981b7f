"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const companyJoinRequest_controller_js_1 = require("../controllers/company/companyJoinRequest.controller.js");
const router = express_1.default.Router();
// Join request routes
router.post("/", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(companyJoinRequest_controller_js_1.createJoinRequest));
router.get("/company/:companyId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(companyJoinRequest_controller_js_1.getCompanyJoinRequests));
router.get("/user/:userId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(companyJoinRequest_controller_js_1.getUserJoinRequests));
router.put("/:requestId/respond", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(companyJoinRequest_controller_js_1.respondToJoinRequest));
// Invitation routes
router.post("/invite", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(companyJoinRequest_controller_js_1.createInvitation));
router.get("/invitations/company/:companyId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(companyJoinRequest_controller_js_1.getCompanyInvitations));
router.get("/invitations/verify/:token", (0, route_helpers_js_1.wrapController)(companyJoinRequest_controller_js_1.verifyInvitation));
router.post("/invitations/accept/:token", (0, route_helpers_js_1.wrapController)(companyJoinRequest_controller_js_1.acceptInvitation));
exports.default = router;
//# sourceMappingURL=companyJoinRequest.js.map