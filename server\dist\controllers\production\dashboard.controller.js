"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAiInsights = exports.getStatistics = exports.getDashboard = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
const getDashboard = async (req, res, next) => {
    try {
        const companyId = req.user?.companyId;
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        // Get active projects count
        const activeProjectsCount = await prisma_js_1.prisma.project.count({
            where: {
                companyId: companyId,
                status: "IN_PROGRESS", // Using the correct enum value from schema
            },
        });
        // Get tasks statistics - using findMany instead of groupBy
        const allTasks = await prisma_js_1.prisma.task.findMany({
            where: {
                project: {
                    companyId: companyId,
                },
            },
            select: {
                id: true,
                status: true,
            },
        });
        // Manually group tasks by status
        const tasksStats = Object.entries(allTasks.reduce((acc, task) => {
            const status = task.status;
            if (!acc[status]) {
                acc[status] = { _count: { id: 0 } };
            }
            acc[status]._count.id += 1;
            return acc;
        }, {})).map(([status, count]) => ({ status, ...count }));
        // Get upcoming deadlines
        const upcomingDeadlines = await prisma_js_1.prisma.task.findMany({
            where: {
                project: {
                    companyId: companyId,
                },
                // Using endDate instead of dueDate since dueDate doesn't exist in the schema
                endDate: {
                    gte: new Date(),
                    lte: new Date(new Date().setDate(new Date().getDate() + 7)), // Next 7 days
                },
                status: {
                    not: "COMPLETED",
                },
            },
            include: {
                project: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
            orderBy: {
                // Using endDate instead of dueDate since dueDate doesn't exist in the schema
                endDate: "asc",
            },
            take: 5,
        });
        // Get project progress
        const projectProgress = await prisma_js_1.prisma.project.findMany({
            where: {
                companyId: companyId,
                status: "IN_PROGRESS", // Using the correct enum value from schema
            },
            select: {
                id: true,
                name: true,
                // progress field doesn't exist in the schema
                // We'll calculate it based on tasks later
                startDate: true,
                endDate: true,
            },
            orderBy: {
                endDate: "asc",
            },
            take: 5,
        });
        res.json({
            activeProjectsCount,
            tasksStats,
            upcomingDeadlines,
            projectProgress,
        });
    }
    catch (error) {
        console.error("Error fetching dashboard data:", error);
        next(error);
    }
};
exports.getDashboard = getDashboard;
const getStatistics = async (req, res, next) => {
    try {
        const { period } = req.query;
        const companyId = req.user?.companyId;
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        // Define date range based on period
        let startDate = new Date();
        const endDate = new Date();
        switch (period) {
            case "week":
                startDate.setDate(startDate.getDate() - 7);
                break;
            case "month":
                startDate.setMonth(startDate.getMonth() - 1);
                break;
            case "quarter":
                startDate.setMonth(startDate.getMonth() - 3);
                break;
            case "year":
                startDate.setFullYear(startDate.getFullYear() - 1);
                break;
            default:
                startDate.setMonth(startDate.getMonth() - 1); // Default to month
        }
        // Get project statistics - using findMany instead of groupBy
        const allProjects = await prisma_js_1.prisma.project.findMany({
            where: {
                companyId: companyId,
                createdAt: {
                    gte: startDate,
                    lte: endDate,
                },
            },
            select: {
                id: true,
                status: true,
            },
        });
        // Manually group projects by status
        const projectStats = Object.entries(allProjects.reduce((acc, project) => {
            const status = project.status;
            if (!acc[status]) {
                acc[status] = { _count: { id: 0 } };
            }
            acc[status]._count.id += 1;
            return acc;
        }, {})).map(([status, count]) => ({ status, ...count }));
        // Get task completion statistics - using findMany instead of groupBy
        const allCompletionTasks = await prisma_js_1.prisma.task.findMany({
            where: {
                project: {
                    companyId: companyId,
                },
                // Using endDate instead of updatedAt since updatedAt doesn't exist in TaskWhereInput
                endDate: {
                    gte: startDate,
                    lte: endDate,
                },
            },
            select: {
                id: true,
                status: true,
            },
        });
        // Manually group tasks by status
        const taskCompletionStats = Object.entries(allCompletionTasks.reduce((acc, task) => {
            const status = task.status;
            if (!acc[status]) {
                acc[status] = { _count: { id: 0 } };
            }
            acc[status]._count.id += 1;
            return acc;
        }, {})).map(([status, count]) => ({ status, ...count }));
        res.json({
            projectStats,
            taskCompletionStats,
            period,
            dateRange: {
                startDate,
                endDate,
            },
        });
    }
    catch (error) {
        console.error("Error fetching statistics data:", error);
        next(error);
    }
};
exports.getStatistics = getStatistics;
const getAiInsights = async (req, res, next) => {
    try {
        const companyId = req.user?.companyId;
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        // Get project data
        const projects = await prisma_js_1.prisma.project.findMany({
            where: {
                companyId: companyId,
                status: "IN_PROGRESS", // Using the correct enum value from schema
            },
            include: {
                tasks: true,
            },
        });
        // Calculate insights (in a real application, this would use AI models)
        const insights = {
            projectRiskAssessment: projects.map((project) => {
                // Calculate project risk based on tasks, deadlines, and progress
                const totalTasks = project.tasks.length;
                const completedTasks = project.tasks.filter((task) => task.status === "COMPLETED").length;
                const overdueTasks = project.tasks.filter((task) => task.endDate &&
                    new Date(task.endDate) < new Date() &&
                    task.status !== "COMPLETED").length;
                const progress = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
                // Since progress field doesn't exist in the schema, we'll use a calculated value
                // For this example, we'll assume a target progress of 50%
                const progressDifference = progress - 50;
                let riskLevel = "LOW";
                if (overdueTasks > 3 || progressDifference < -20) {
                    riskLevel = "HIGH";
                }
                else if (overdueTasks > 0 || progressDifference < -10) {
                    riskLevel = "MEDIUM";
                }
                return {
                    projectId: project.id,
                    projectName: project.name,
                    riskLevel,
                    overdueTasks,
                    progressDifference: progressDifference.toFixed(2),
                    recommendations: getRiskRecommendations(riskLevel, overdueTasks, progressDifference),
                };
            }),
            efficiencyMetrics: projects.map((project) => {
                // Calculate efficiency metrics
                const totalTasks = project.tasks.length;
                const completedTasks = project.tasks.filter((task) => task.status === "COMPLETED").length;
                const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
                return {
                    projectId: project.id,
                    projectName: project.name,
                    completionRate: completionRate.toFixed(2) + "%",
                    recommendations: getEfficiencyRecommendations(completionRate),
                };
            }),
        };
        res.json(insights);
    }
    catch (error) {
        console.error("Error generating AI insights:", error);
        next(error);
    }
};
exports.getAiInsights = getAiInsights;
// Helper functions for AI insights
function getRiskRecommendations(riskLevel, overdueTasks, progressDifference) {
    const recommendations = [];
    if (riskLevel === "HIGH") {
        recommendations.push("Schedule an urgent project review meeting with stakeholders");
        recommendations.push("Reassess project timeline and adjust deadlines if necessary");
    }
    if (overdueTasks > 0) {
        recommendations.push(`Prioritize ${overdueTasks} overdue tasks to get back on schedule`);
        recommendations.push("Consider allocating additional resources to overdue tasks");
    }
    if (progressDifference < -10) {
        recommendations.push("Update project progress tracking to reflect actual completion status");
        recommendations.push("Identify bottlenecks causing delays in project progress");
    }
    return recommendations;
}
function getEfficiencyRecommendations(completionRate) {
    const recommendations = [];
    if (completionRate < 30) {
        recommendations.push("Task completion rate is very low. Review project planning and resource allocation.");
        recommendations.push("Consider breaking down complex tasks into smaller, more manageable tasks.");
    }
    else if (completionRate < 60) {
        recommendations.push("Task completion rate could be improved. Identify and address bottlenecks.");
        recommendations.push("Review team workload and consider redistributing tasks if necessary.");
    }
    else {
        recommendations.push("Good task completion rate. Continue monitoring progress and maintaining momentum.");
    }
    return recommendations;
}
//# sourceMappingURL=dashboard.controller.js.map