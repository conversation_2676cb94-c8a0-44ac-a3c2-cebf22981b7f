"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteSupplierRelationship = exports.updateSupplierRelationship = exports.createSupplierRelationship = exports.getSupplierRelationships = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Get all supplier relationships for a company
const getSupplierRelationships = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        // Get relationships where the company is either the buyer or the supplier
        const supplierRelationships = await prisma_js_1.prisma.supplierRelationship.findMany({
            where: {
                OR: [
                    { buyerCompanyId: Number(companyId) },
                    { supplierCompanyId: Number(companyId) },
                ],
            },
            include: {
                buyerCompany: true,
                supplierCompany: true,
            },
        });
        res.json(supplierRelationships);
    }
    catch (error) {
        next(createHttpError(500, "Failed to fetch supplier relationships"));
    }
};
exports.getSupplierRelationships = getSupplierRelationships;
// Create a new supplier relationship
const createSupplierRelationship = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        const { buyerCompanyId, supplierCompanyId, status, contractValue, notes } = req.body;
        // Ensure the company is part of the relationship
        if (companyId !== buyerCompanyId && companyId !== supplierCompanyId) {
            return next(createHttpError(400, "The company must be part of the relationship"));
        }
        // Check if relationship already exists
        const existingRelationship = await prisma_js_1.prisma.supplierRelationship.findFirst({
            where: {
                buyerCompanyId: Number(buyerCompanyId),
                supplierCompanyId: Number(supplierCompanyId),
            },
        });
        if (existingRelationship) {
            return next(createHttpError(409, "Relationship already exists"));
        }
        const relationship = await prisma_js_1.prisma.supplierRelationship.create({
            data: {
                buyerCompanyId: Number(buyerCompanyId),
                supplierCompanyId: Number(supplierCompanyId),
                status,
                contractValue,
                notes,
            },
            include: {
                buyerCompany: true,
                supplierCompany: true,
            },
        });
        res.status(201).json(relationship);
    }
    catch (error) {
        next(createHttpError(500, "Failed to create supplier relationship"));
    }
};
exports.createSupplierRelationship = createSupplierRelationship;
// Update a supplier relationship
const updateSupplierRelationship = async (req, res, next) => {
    try {
        const { relationshipId } = req.params;
        const { status, contractValue, notes, endDate, lifetimeValue, lastTransactionDate, transactionCount, reliabilityScore, qualityScore, } = req.body;
        const relationship = await prisma_js_1.prisma.supplierRelationship.update({
            where: { id: Number(relationshipId) },
            data: {
                status,
                contractValue,
                notes,
                endDate,
                lifetimeValue,
                lastTransactionDate,
                transactionCount,
                reliabilityScore,
                qualityScore,
                updatedAt: new Date(),
            },
            include: {
                buyerCompany: true,
                supplierCompany: true,
            },
        });
        res.json(relationship);
    }
    catch (error) {
        if (error.code === "P2025") {
            return next(createHttpError(404, "Relationship not found"));
        }
        next(createHttpError(500, "Failed to update supplier relationship"));
    }
};
exports.updateSupplierRelationship = updateSupplierRelationship;
// Delete a supplier relationship
const deleteSupplierRelationship = async (req, res, next) => {
    try {
        const { relationshipId } = req.params;
        await prisma_js_1.prisma.supplierRelationship.delete({
            where: { id: Number(relationshipId) },
        });
        res.status(204).send();
    }
    catch (error) {
        if (error.code === "P2025") {
            return next(createHttpError(404, "Relationship not found"));
        }
        next(createHttpError(500, "Failed to delete supplier relationship"));
    }
};
exports.deleteSupplierRelationship = deleteSupplierRelationship;
//# sourceMappingURL=supplierRelationship.controller.js.map