"use strict";
// services/codeGenerator.ts
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateBarcode = generateBarcode;
exports.generateQRCode = generateQRCode;
exports.generateCodes = generateCodes;
const nanoid_1 = require("nanoid");
const qrcode_1 = __importDefault(require("qrcode"));
// Use nanoid to generate a unique barcode
const nanoid = (0, nanoid_1.customAlphabet)("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", 10);
/**
 * Generates a unique barcode.
 * @returns A unique barcode string.
 */
function generateBarcode() {
    return nanoid();
}
/**
 * Generates a QR code data URL from the given data.
 * @param data - The data to encode in the QR code.
 * @returns A promise that resolves to the QR code data URL.
 */
async function generateQRCode(data) {
    try {
        const qrCodeDataURL = await qrcode_1.default.toDataURL(data);
        return qrCodeDataURL;
    }
    catch (err) {
        console.error("Error generating QR code:", err);
        throw err;
    }
}
/**
 * Generates both a unique barcode and a QR code.
 * @returns An object containing both the barcode and QR code data URL.
 */
async function generateCodes() {
    const barcode = generateBarcode();
    const qrCode = await generateQRCode(barcode);
    return { barcode, qrCode };
}
//# sourceMappingURL=codeGenerator.js.map