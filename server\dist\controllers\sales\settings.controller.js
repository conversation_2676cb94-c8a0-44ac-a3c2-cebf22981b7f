"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateIntegration = exports.getIntegrations = exports.deleteAutomationRule = exports.updateAutomationRule = exports.createAutomationRule = exports.getAutomationRules = exports.deleteSalesGoal = exports.updateSalesGoal = exports.createSalesGoal = exports.getSalesGoals = exports.deletePipelineStage = exports.updatePipelineStage = exports.createPipelineStage = exports.getPipelineStages = exports.updateSettings = exports.getSettings = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
// Get sales settings
const getSettings = async (req, res) => {
    try {
        // In a real implementation, this would fetch settings from the database
        // For now, return some default settings
        const settings = {
            general: {
                defaultCurrency: "USD",
                taxRate: 20,
                leadScoring: {
                    enabled: true,
                    thresholds: {
                        cold: 0,
                        warm: 40,
                        hot: 70,
                    },
                },
                dealStages: [
                    {
                        id: "PROSPECTING",
                        name: "Prospecting",
                        order: 1,
                        color: "#6b7280",
                    },
                    {
                        id: "QUALIFICATION",
                        name: "Qualification",
                        order: 2,
                        color: "#3b82f6",
                    },
                    {
                        id: "NEEDS_ANALYSIS",
                        name: "Needs Analysis",
                        order: 3,
                        color: "#8b5cf6",
                    },
                    {
                        id: "VALUE_PROPOSITION",
                        name: "Value Proposition",
                        order: 4,
                        color: "#f59e0b",
                    },
                    { id: "PROPOSAL", name: "Proposal", order: 5, color: "#10b981" },
                    {
                        id: "NEGOTIATION",
                        name: "Negotiation",
                        order: 6,
                        color: "#ef4444",
                    },
                    { id: "CLOSED_WON", name: "Closed Won", order: 7, color: "#22c55e" },
                    {
                        id: "CLOSED_LOST",
                        name: "Closed Lost",
                        order: 8,
                        color: "#dc2626",
                    },
                ],
            },
            notifications: {
                leadAssigned: true,
                dealStageChanged: true,
                dealWon: true,
                dealLost: true,
                quotationSent: true,
                quotationAccepted: true,
                quotationRejected: true,
                activityReminder: true,
                emailNotifications: true,
                pushNotifications: true,
            },
            automation: {
                leadScoring: {
                    enabled: true,
                    rules: [
                        { field: "email", condition: "exists", points: 10 },
                        { field: "phone", condition: "exists", points: 10 },
                        { field: "company", condition: "exists", points: 15 },
                        { field: "jobTitle", condition: "exists", points: 10 },
                        { field: "website", condition: "exists", points: 5 },
                    ],
                },
                leadAssignment: {
                    enabled: false,
                    method: "round_robin",
                    users: [],
                },
                activityReminders: {
                    enabled: true,
                    reminderTime: 30, // minutes before activity
                },
                dealStageProgression: {
                    enabled: false,
                    rules: [],
                },
            },
            customFields: {
                lead: [
                    {
                        id: "industry",
                        name: "Industry",
                        type: "select",
                        options: [
                            "Technology",
                            "Healthcare",
                            "Finance",
                            "Education",
                            "Manufacturing",
                            "Retail",
                            "Other",
                        ],
                    },
                    {
                        id: "leadSource",
                        name: "Lead Source",
                        type: "select",
                        options: [
                            "Website",
                            "Referral",
                            "Social Media",
                            "Email Campaign",
                            "Cold Call",
                            "Event",
                            "Partner",
                            "Advertisement",
                            "Other",
                        ],
                    },
                    { id: "budget", name: "Budget", type: "currency" },
                ],
                deal: [
                    { id: "decisionMaker", name: "Decision Maker", type: "text" },
                    { id: "competitorInfo", name: "Competitor Info", type: "textarea" },
                    { id: "reasonWonLost", name: "Reason Won/Lost", type: "textarea" },
                ],
            },
            emailTemplates: [
                {
                    id: "lead_welcome",
                    name: "Lead Welcome Email",
                    subject: "Welcome to {company_name}",
                    body: "Dear {first_name},\n\nThank you for your interest in our products/services...",
                },
                {
                    id: "meeting_confirmation",
                    name: "Meeting Confirmation",
                    subject: "Confirmation: Meeting on {meeting_date}",
                    body: "Dear {first_name},\n\nThis is to confirm our meeting scheduled for {meeting_date} at {meeting_time}...",
                },
                {
                    id: "quotation_follow_up",
                    name: "Quotation Follow-up",
                    subject: "Following up on your quotation",
                    body: "Dear {first_name},\n\nI wanted to follow up on the quotation we sent you on {quotation_date}...",
                },
                {
                    id: "deal_won",
                    name: "Deal Won Thank You",
                    subject: "Thank you for choosing {company_name}",
                    body: "Dear {first_name},\n\nThank you for choosing {company_name}. We are excited to work with you...",
                },
            ],
            integrations: [
                {
                    id: "email",
                    name: "Email Integration",
                    enabled: false,
                    provider: "gmail",
                    config: {},
                },
                {
                    id: "calendar",
                    name: "Calendar Integration",
                    enabled: false,
                    provider: "google_calendar",
                    config: {},
                },
                {
                    id: "stripe",
                    name: "Stripe Integration",
                    enabled: true,
                    provider: "stripe",
                    config: {},
                },
                {
                    id: "zapier",
                    name: "Zapier Integration",
                    enabled: false,
                    provider: "zapier",
                    config: {},
                },
            ],
        };
        res.status(200).json(settings);
    }
    catch (error) {
        console.error("Error fetching sales settings:", error);
        res.status(500).json({ error: "Failed to fetch sales settings" });
    }
};
exports.getSettings = getSettings;
// Update sales settings
const updateSettings = async (req, res) => {
    try {
        const { general, notifications, automation, customFields, emailTemplates } = req.body;
        // In a real implementation, this would update settings in the database
        // For now, just return the updated settings
        const updatedSettings = {
            general,
            notifications,
            automation,
            customFields,
            emailTemplates,
        };
        res.status(200).json({
            message: "Settings updated successfully",
            settings: updatedSettings,
        });
    }
    catch (error) {
        console.error("Error updating sales settings:", error);
        res.status(500).json({ error: "Failed to update sales settings" });
    }
};
exports.updateSettings = updateSettings;
// Get pipeline stages
const getPipelineStages = async (req, res) => {
    try {
        const stages = await prisma_js_1.prisma.salesPipelineStage.findMany({
            orderBy: {
                order: "asc",
            },
        });
        res.status(200).json(stages);
    }
    catch (error) {
        console.error("Error fetching pipeline stages:", error);
        res.status(500).json({ error: "Failed to fetch pipeline stages" });
    }
};
exports.getPipelineStages = getPipelineStages;
// Create pipeline stage
const createPipelineStage = async (req, res) => {
    try {
        const { id, name, order, color, isActive = true } = req.body;
        // Check if stage with same ID already exists
        const existingStage = await prisma_js_1.prisma.salesPipelineStage.findUnique({
            where: { id },
        });
        if (existingStage) {
            res.status(400).json({ error: "A stage with this ID already exists" });
            return;
        }
        // Create stage
        const stage = await prisma_js_1.prisma.salesPipelineStage.create({
            data: {
                id: Number(id),
                name,
                order,
                // Add required properties from the schema
                probability: 0,
                isActive,
            },
        });
        res.status(201).json(stage);
    }
    catch (error) {
        console.error("Error creating pipeline stage:", error);
        res.status(500).json({ error: "Failed to create pipeline stage" });
    }
};
exports.createPipelineStage = createPipelineStage;
// Update pipeline stage
const updatePipelineStage = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, order, color, isActive } = req.body;
        // Check if stage exists
        const existingStage = await prisma_js_1.prisma.salesPipelineStage.findUnique({
            where: { id: Number(id) },
        });
        if (!existingStage) {
            res.status(404).json({ error: "Pipeline stage not found" });
            return;
        }
        // Update stage
        const updatedStage = await prisma_js_1.prisma.salesPipelineStage.update({
            where: { id: Number(id) },
            data: {
                name,
                order,
                // Add required properties from the schema
                probability: 0,
                isActive,
            },
        });
        res.status(200).json(updatedStage);
    }
    catch (error) {
        console.error("Error updating pipeline stage:", error);
        res.status(500).json({ error: "Failed to update pipeline stage" });
    }
};
exports.updatePipelineStage = updatePipelineStage;
// Delete pipeline stage
const deletePipelineStage = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if stage exists
        const stage = await prisma_js_1.prisma.salesPipelineStage.findUnique({
            where: { id: Number(id) },
        });
        if (!stage) {
            res.status(404).json({ error: "Pipeline stage not found" });
            return;
        }
        // Check if stage is used in any deals
        const dealsCount = await prisma_js_1.prisma.deal.count({
            where: { stage: id },
        });
        if (dealsCount > 0) {
            res.status(400).json({
                error: "Cannot delete stage as it is used in deals",
                dealsCount,
            });
            return;
        }
        // Delete stage
        await prisma_js_1.prisma.salesPipelineStage.delete({
            where: { id: Number(id) },
        });
        res.status(200).json({ message: "Pipeline stage deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting pipeline stage:", error);
        res.status(500).json({ error: "Failed to delete pipeline stage" });
    }
};
exports.deletePipelineStage = deletePipelineStage;
// Get sales goals
const getSalesGoals = async (req, res) => {
    try {
        const { userId, timeframe } = req.query;
        // Build filter conditions
        const where = {};
        if (userId) {
            where.assignedToId = Number(userId);
        }
        if (timeframe) {
            const now = new Date();
            switch (timeframe) {
                case "current":
                    where.startDate = { lte: now };
                    where.endDate = { gte: now };
                    break;
                case "past":
                    where.endDate = { lt: now };
                    break;
                case "future":
                    where.startDate = { gt: now };
                    break;
            }
        }
        // Get sales goals
        const goals = await prisma_js_1.prisma.salesGoal.findMany({
            where,
            include: {
                assignedTo: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
            },
            orderBy: {
                endDate: "asc",
            },
        });
        res.status(200).json(goals);
    }
    catch (error) {
        console.error("Error fetching sales goals:", error);
        res.status(500).json({ error: "Failed to fetch sales goals" });
    }
};
exports.getSalesGoals = getSalesGoals;
// Create sales goal
const createSalesGoal = async (req, res) => {
    try {
        const { name, type, target, startDate, endDate, assignedToId, description, } = req.body;
        // Create sales goal
        const goal = await prisma_js_1.prisma.salesGoal.create({
            data: {
                name,
                // Add required properties from the schema
                targetAmount: target ? Number(target) : 0,
                goalType: type || "REVENUE",
                progress: 0,
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                assignedToId: assignedToId ? Number(assignedToId) : null,
                description,
            },
            include: {
                assignedTo: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
            },
        });
        res.status(201).json(goal);
    }
    catch (error) {
        console.error("Error creating sales goal:", error);
        res.status(500).json({ error: "Failed to create sales goal" });
    }
};
exports.createSalesGoal = createSalesGoal;
// Update sales goal
const updateSalesGoal = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, type, target, progress, startDate, endDate, assignedToId, description, } = req.body;
        // Check if goal exists
        const existingGoal = await prisma_js_1.prisma.salesGoal.findUnique({
            where: { id: Number(id) },
        });
        if (!existingGoal) {
            res.status(404).json({ error: "Sales goal not found" });
            return;
        }
        // Update sales goal
        const updatedGoal = await prisma_js_1.prisma.salesGoal.update({
            where: { id: Number(id) },
            data: {
                name,
                // Add required properties from the schema
                targetAmount: target ? Number(target) : undefined,
                goalType: type || undefined,
                progress,
                startDate: startDate ? new Date(startDate) : undefined,
                endDate: endDate ? new Date(endDate) : undefined,
                assignedToId: assignedToId ? Number(assignedToId) : null,
                description,
                updatedAt: new Date(),
            },
            include: {
                assignedTo: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
            },
        });
        res.status(200).json(updatedGoal);
    }
    catch (error) {
        console.error("Error updating sales goal:", error);
        res.status(500).json({ error: "Failed to update sales goal" });
    }
};
exports.updateSalesGoal = updateSalesGoal;
// Delete sales goal
const deleteSalesGoal = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if goal exists
        const goal = await prisma_js_1.prisma.salesGoal.findUnique({
            where: { id: Number(id) },
        });
        if (!goal) {
            res.status(404).json({ error: "Sales goal not found" });
            return;
        }
        // Delete sales goal
        await prisma_js_1.prisma.salesGoal.delete({
            where: { id: Number(id) },
        });
        res.status(200).json({ message: "Sales goal deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting sales goal:", error);
        res.status(500).json({ error: "Failed to delete sales goal" });
    }
};
exports.deleteSalesGoal = deleteSalesGoal;
// Get automation rules
const getAutomationRules = async (req, res) => {
    try {
        // In a real implementation, this would fetch automation rules from the database
        // For now, return some predefined rules
        const rules = [
            {
                id: "1",
                name: "Lead Scoring - Website Visit",
                type: "LEAD_SCORING",
                condition: "EVENT_OCCURRED",
                eventType: "WEBSITE_VISIT",
                points: 5,
                isActive: true,
                createdAt: "2023-05-10T14:30:00Z",
            },
            {
                id: "2",
                name: "Lead Scoring - Email Open",
                type: "LEAD_SCORING",
                condition: "EVENT_OCCURRED",
                eventType: "EMAIL_OPEN",
                points: 3,
                isActive: true,
                createdAt: "2023-05-10T14:35:00Z",
            },
            {
                id: "3",
                name: "Lead Assignment - New Leads",
                type: "LEAD_ASSIGNMENT",
                condition: "LEAD_CREATED",
                assignToUserId: 1,
                isActive: false,
                createdAt: "2023-05-11T09:20:00Z",
            },
            {
                id: "4",
                name: "Deal Stage Update - Proposal Sent",
                type: "DEAL_STAGE_UPDATE",
                condition: "EVENT_OCCURRED",
                eventType: "QUOTATION_SENT",
                newStage: "PROPOSAL",
                isActive: true,
                createdAt: "2023-05-12T11:45:00Z",
            },
            {
                id: "5",
                name: "Activity Creation - New Lead",
                type: "ACTIVITY_CREATION",
                condition: "LEAD_CREATED",
                activityType: "FOLLOW_UP",
                activitySubject: "Initial follow-up with {lead.firstName}",
                daysDelay: 2,
                assignToCreator: true,
                isActive: true,
                createdAt: "2023-05-15T16:10:00Z",
            },
        ];
        res.status(200).json(rules);
    }
    catch (error) {
        console.error("Error fetching automation rules:", error);
        res.status(500).json({ error: "Failed to fetch automation rules" });
    }
};
exports.getAutomationRules = getAutomationRules;
// Create automation rule
const createAutomationRule = async (req, res) => {
    try {
        const { name, type, condition, eventType, points, assignToUserId, newStage, activityType, activitySubject, daysDelay, assignToCreator, isActive, } = req.body;
        // In a real implementation, this would save the rule to the database
        // For now, just return a mock response
        const rule = {
            id: Math.random().toString(36).substring(2, 15),
            name,
            type,
            condition,
            eventType,
            points,
            assignToUserId,
            newStage,
            activityType,
            activitySubject,
            daysDelay,
            assignToCreator,
            isActive,
            createdAt: new Date().toISOString(),
        };
        res.status(201).json(rule);
    }
    catch (error) {
        console.error("Error creating automation rule:", error);
        res.status(500).json({ error: "Failed to create automation rule" });
    }
};
exports.createAutomationRule = createAutomationRule;
// Update automation rule
const updateAutomationRule = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, type, condition, eventType, points, assignToUserId, newStage, activityType, activitySubject, daysDelay, assignToCreator, isActive, } = req.body;
        // In a real implementation, this would update the rule in the database
        // For now, just return a mock response
        const rule = {
            id,
            name,
            type,
            condition,
            eventType,
            points,
            assignToUserId,
            newStage,
            activityType,
            activitySubject,
            daysDelay,
            assignToCreator,
            isActive,
            updatedAt: new Date().toISOString(),
        };
        res.status(200).json(rule);
    }
    catch (error) {
        console.error("Error updating automation rule:", error);
        res.status(500).json({ error: "Failed to update automation rule" });
    }
};
exports.updateAutomationRule = updateAutomationRule;
// Delete automation rule
const deleteAutomationRule = async (req, res) => {
    try {
        const { id } = req.params;
        // In a real implementation, this would delete the rule from the database
        // For now, just return a success message
        res.status(200).json({ message: "Automation rule deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting automation rule:", error);
        res.status(500).json({ error: "Failed to delete automation rule" });
    }
};
exports.deleteAutomationRule = deleteAutomationRule;
// Get integrations
const getIntegrations = async (req, res) => {
    try {
        // In a real implementation, this would fetch integrations from the database
        // For now, return some predefined integrations
        const integrations = [
            {
                id: "email",
                name: "Email Integration",
                provider: "gmail",
                enabled: false,
                config: {
                    clientId: "",
                    clientSecret: "",
                    redirectUri: "",
                    authorized: false,
                },
                features: [
                    { id: "email_sync", name: "Email Synchronization", enabled: false },
                    { id: "email_tracking", name: "Email Tracking", enabled: false },
                    { id: "email_templates", name: "Email Templates", enabled: false },
                ],
            },
            {
                id: "calendar",
                name: "Calendar Integration",
                provider: "google_calendar",
                enabled: false,
                config: {
                    clientId: "",
                    clientSecret: "",
                    redirectUri: "",
                    authorized: false,
                },
                features: [
                    {
                        id: "calendar_sync",
                        name: "Calendar Synchronization",
                        enabled: false,
                    },
                    {
                        id: "meeting_scheduling",
                        name: "Meeting Scheduling",
                        enabled: false,
                    },
                    { id: "reminders", name: "Reminders", enabled: false },
                ],
            },
            {
                id: "stripe",
                name: "Stripe Integration",
                provider: "stripe",
                enabled: true,
                config: {
                    apiKey: "***********",
                    webhookSecret: "***********",
                    authorized: true,
                },
                features: [
                    {
                        id: "payment_processing",
                        name: "Payment Processing",
                        enabled: true,
                    },
                    {
                        id: "subscription_management",
                        name: "Subscription Management",
                        enabled: true,
                    },
                    {
                        id: "invoice_generation",
                        name: "Invoice Generation",
                        enabled: true,
                    },
                ],
            },
            {
                id: "zapier",
                name: "Zapier Integration",
                provider: "zapier",
                enabled: false,
                config: {
                    apiKey: "",
                    authorized: false,
                },
                features: [
                    {
                        id: "workflow_automation",
                        name: "Workflow Automation",
                        enabled: false,
                    },
                    {
                        id: "third_party_integration",
                        name: "Third-party Integration",
                        enabled: false,
                    },
                ],
            },
        ];
        res.status(200).json(integrations);
    }
    catch (error) {
        console.error("Error fetching integrations:", error);
        res.status(500).json({ error: "Failed to fetch integrations" });
    }
};
exports.getIntegrations = getIntegrations;
// Update integration
const updateIntegration = async (req, res) => {
    try {
        const { id } = req.params;
        const { enabled, config, features } = req.body;
        // In a real implementation, this would update the integration in the database
        // For now, just return a mock response
        const integration = {
            id,
            enabled,
            config,
            features,
            updatedAt: new Date().toISOString(),
        };
        res.status(200).json({
            message: "Integration updated successfully",
            integration,
        });
    }
    catch (error) {
        console.error("Error updating integration:", error);
        res.status(500).json({ error: "Failed to update integration" });
    }
};
exports.updateIntegration = updateIntegration;
//# sourceMappingURL=settings.controller.js.map