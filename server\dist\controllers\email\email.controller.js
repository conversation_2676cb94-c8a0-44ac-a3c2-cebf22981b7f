"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEmailFolders = getEmailFolders;
exports.getEmailsByFolder = getEmailsByFolder;
exports.sendEmail = sendEmail;
exports.updateEmailStatus = updateEmailStatus;
exports.deleteEmail = deleteEmail;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
/**
 * Get all email folders for a user
 */
async function getEmailFolders(req, res) {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Get user's email accounts
        const accounts = await prisma.emailAccount.findMany({
            where: { userId },
            include: {
                folders: {
                    include: {
                        _count: {
                            select: {
                                messages: true,
                            },
                        },
                    },
                },
            },
        });
        // Create default folders if none exist
        if (accounts.length === 0) {
            // Create default email account
            const defaultAccount = await prisma.emailAccount.create({
                data: {
                    userId,
                    email: req.user?.email || "<EMAIL>",
                    name: req.user?.name || "Default Account",
                    provider: "CUSTOM",
                    isDefault: true,
                },
            });
            // Create default folders
            const defaultFolders = [
                { name: "Inbox", type: "INBOX" },
                { name: "Sent", type: "SENT" },
                { name: "Important", type: "IMPORTANT" },
                { name: "Spam", type: "SPAM" },
                { name: "Drafts", type: "DRAFTS" },
            ];
            for (const folder of defaultFolders) {
                await prisma.emailFolder.create({
                    data: {
                        accountId: defaultAccount.id,
                        name: folder.name,
                        type: folder.type,
                    },
                });
            }
            // Fetch the updated account with folders
            const updatedAccount = await prisma.emailAccount.findUnique({
                where: { id: defaultAccount.id },
                include: {
                    folders: {
                        include: {
                            _count: {
                                select: {
                                    messages: true,
                                },
                            },
                        },
                    },
                },
            });
            return res.json([updatedAccount]);
        }
        res.json(accounts);
    }
    catch (error) {
        res
            .status(500)
            .json({ error: "Failed to get email folders", message: error.message });
    }
}
/**
 * Get emails by folder type
 */
async function getEmailsByFolder(req, res) {
    try {
        const userId = req.user?.id;
        const { folderType } = req.params;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Get user's email accounts
        const accounts = await prisma.emailAccount.findMany({
            where: { userId },
            include: {
                folders: {
                    where: { type: folderType.toUpperCase() },
                    include: {
                        messages: {
                            include: {
                                attachments: true,
                            },
                            orderBy: { receivedAt: "desc" },
                        },
                    },
                },
            },
        });
        // Flatten messages from all accounts and folders
        const messages = accounts.flatMap((account) => account.folders.flatMap((folder) => folder.messages.map((message) => ({
            ...message,
            sender: {
                name: message.from.split("<")[0].trim() || message.from,
                email: message.from.includes("<")
                    ? message.from.split("<")[1].replace(">", "")
                    : message.from,
                photo: `/img/avatars/placeholder.svg`,
            },
            title: message.subject,
            abstract: message.body.substring(0, 150) +
                (message.body.length > 150 ? "..." : ""),
            content: message.body,
            time: new Date(message.receivedAt).toLocaleTimeString("en-US", {
                hour: "2-digit",
                minute: "2-digit",
            }),
            attachments: message.attachments.map((att) => ({
                name: att.name,
                type: att.type.split("/")[1] || "doc",
                size: `${Math.round(att.size / 1024)}KB`,
            })),
        }))));
        res.json(messages);
    }
    catch (error) {
        res
            .status(500)
            .json({ error: "Failed to get emails", message: error.message });
    }
}
/**
 * Send an email
 */
async function sendEmail(req, res) {
    try {
        const userId = req.user?.id;
        const { to, cc, bcc, subject, body, isHtml } = req.body;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Get user's default email account
        let account = await prisma.emailAccount.findFirst({
            where: { userId, isDefault: true },
        });
        if (!account) {
            // Create default account if none exists
            account = await prisma.emailAccount.create({
                data: {
                    userId,
                    email: req.user?.email || "<EMAIL>",
                    name: req.user?.name || "Default Account",
                    provider: "CUSTOM",
                    isDefault: true,
                },
            });
        }
        // Get or create sent folder
        let sentFolder = await prisma.emailFolder.findFirst({
            where: { accountId: account.id, type: "SENT" },
        });
        if (!sentFolder) {
            sentFolder = await prisma.emailFolder.create({
                data: {
                    accountId: account.id,
                    name: "Sent",
                    type: "SENT",
                },
            });
        }
        // Create email message
        const message = await prisma.emailMessage.create({
            data: {
                accountId: account.id,
                folderId: sentFolder.id,
                from: account.email,
                to: Array.isArray(to) ? to : [to],
                cc: cc || [],
                bcc: bcc || [],
                subject,
                body,
                isHtml: isHtml || false,
                status: "READ", // Sent emails are marked as read
                receivedAt: new Date(),
            },
        });
        res
            .status(201)
            .json({ message: "Email sent successfully", id: message.id });
    }
    catch (error) {
        res
            .status(500)
            .json({ error: "Failed to send email", message: error.message });
    }
}
/**
 * Mark email as read/unread
 */
async function updateEmailStatus(req, res) {
    try {
        const userId = req.user?.id;
        const { messageId } = req.params;
        const { status } = req.body;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Verify the message belongs to the user
        const message = await prisma.emailMessage.findFirst({
            where: {
                id: parseInt(messageId),
                account: { userId },
            },
        });
        if (!message) {
            return res.status(404).json({ error: "Message not found" });
        }
        // Update message status
        const updatedMessage = await prisma.emailMessage.update({
            where: { id: parseInt(messageId) },
            data: { status },
        });
        res.json(updatedMessage);
    }
    catch (error) {
        res
            .status(500)
            .json({ error: "Failed to update email status", message: error.message });
    }
}
/**
 * Delete email
 */
async function deleteEmail(req, res) {
    try {
        const userId = req.user?.id;
        const { messageId } = req.params;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Verify the message belongs to the user
        const message = await prisma.emailMessage.findFirst({
            where: {
                id: parseInt(messageId),
                account: { userId },
            },
        });
        if (!message) {
            return res.status(404).json({ error: "Message not found" });
        }
        // Move to spam folder or delete permanently
        const spamFolder = await prisma.emailFolder.findFirst({
            where: {
                accountId: message.accountId,
                type: "SPAM",
            },
        });
        if (spamFolder && message.folderId !== spamFolder.id) {
            // Move to spam
            await prisma.emailMessage.update({
                where: { id: parseInt(messageId) },
                data: {
                    folderId: spamFolder.id,
                    status: "DELETED",
                },
            });
            res.json({ message: "Email moved to spam" });
        }
        else {
            // Delete permanently
            await prisma.emailMessage.delete({
                where: { id: parseInt(messageId) },
            });
            res.json({ message: "Email deleted permanently" });
        }
    }
    catch (error) {
        res
            .status(500)
            .json({ error: "Failed to delete email", message: error.message });
    }
}
//# sourceMappingURL=email.controller.js.map