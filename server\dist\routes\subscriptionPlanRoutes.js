"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const subscriptionPlanController_js_1 = require("../controllers/subscriptionPlanController.js");
const authorizeRoles_js_1 = require("../middleware/authorizeRoles.js");
const router = express_1.default.Router();
// Public routes - accessible to anyone
router.get("/", (0, route_helpers_js_1.wrapController)(subscriptionPlanController_js_1.subscriptionPlanController.getAllPlans));
router.get("/:id", (0, route_helpers_js_1.wrapController)(subscriptionPlanController_js_1.subscriptionPlanController.getPlanById));
// Protected routes - only accessible to SUPERADMIN
router.post("/", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(subscriptionPlanController_js_1.subscriptionPlanController.createPlan));
router.put("/:id", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(subscriptionPlanController_js_1.subscriptionPlanController.updatePlan));
router.delete("/:id", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(subscriptionPlanController_js_1.subscriptionPlanController.deletePlan));
exports.default = router;
//# sourceMappingURL=subscriptionPlanRoutes.js.map