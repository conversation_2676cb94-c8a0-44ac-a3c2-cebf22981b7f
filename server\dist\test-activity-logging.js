"use strict";
// Test script to verify activity logging is working
const { ActivityLogger } = require("./dist/services/activityLogger.js");
async function testActivityLogging() {
    console.log("Testing activity logging...");
    try {
        // Test system event logging
        await ActivityLogger.logSystemEvent("SYSTEM_START", "System Started", "CoManager system was started successfully", { version: "1.0.0", environment: "development" }, "INFO");
        console.log("✅ System event logged successfully");
        // Test user login logging (simulated)
        const mockRequest = {
            headers: {
                "user-agent": "Test Browser",
                "x-forwarded-for": "127.0.0.1",
            },
            connection: { remoteAddress: "127.0.0.1" },
            socket: { remoteAddress: "127.0.0.1" },
        };
        await ActivityLogger.logUserLogin(1, mockRequest, true);
        console.log("✅ User login logged successfully");
        // Test data operation logging
        await ActivityLogger.logDataOperation("CREATE", "test_entity", "123", "Test Entity Created", "A test entity was created for testing purposes", mockRequest, 1);
        console.log("✅ Data operation logged successfully");
        console.log("\n🎉 All activity logging tests passed!");
        console.log("You can now check the ActivityLog table in your database to see the logged activities.");
    }
    catch (error) {
        console.error("❌ Activity logging test failed:", error);
    }
}
// Run the test
testActivityLogging()
    .then(() => {
    console.log("Test completed.");
    process.exit(0);
})
    .catch((error) => {
    console.error("Test failed:", error);
    process.exit(1);
});
//# sourceMappingURL=test-activity-logging.js.map