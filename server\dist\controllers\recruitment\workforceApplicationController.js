"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getReferralStats = exports.verifyReferralCode = exports.searchApplications = exports.getApplicationsByStatus = exports.deleteApplication = exports.updateApplicationStatus = exports.createApplication = exports.getApplicationById = exports.getAllApplications = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Helper function to generate a unique referral code
const generateReferralCode = () => {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    const codeLength = 8;
    let result = "";
    for (let i = 0; i < codeLength; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
};
// Using centralized prisma instance from lib/prisma.js
// Get all workforce applications
const getAllApplications = async (req, res) => {
    try {
        const applications = await prisma_js_1.prisma.workforceApplication.findMany({
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json(applications);
    }
    catch (error) {
        console.error("Error fetching applications:", error);
        return res.status(500).json({ error: "Failed to fetch applications" });
    }
};
exports.getAllApplications = getAllApplications;
// Get a single workforce application by ID
const getApplicationById = async (req, res) => {
    const { id } = req.params;
    try {
        const application = await prisma_js_1.prisma.workforceApplication.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                previousJobs: true,
                secondarySpecialties: true,
                certifications: true,
                licenses: true,
                professionalReferences: true,
                emergencyContacts: true,
                languages: true,
            },
        });
        if (!application) {
            return res.status(404).json({ error: "Application not found" });
        }
        return res.status(200).json(application);
    }
    catch (error) {
        console.error("Error fetching application:", error);
        return res.status(500).json({ error: "Failed to fetch application" });
    }
};
exports.getApplicationById = getApplicationById;
// Create a new workforce application
const createApplication = async (req, res) => {
    const { personalInfo, experience, specialties, qualifications, references, additionalInfo, referralCode, } = req.body;
    try {
        // Generate a unique referral code for this application
        const newReferralCode = generateReferralCode();
        // Create the application with all related data
        const application = await prisma_js_1.prisma.workforceApplication.create({
            data: {
                // Personal Information
                fullName: personalInfo.name,
                email: personalInfo.email,
                phone: personalInfo.phone,
                dateOfBirth: personalInfo.dateOfBirth
                    ? new Date(personalInfo.dateOfBirth)
                    : null,
                gender: personalInfo.gender,
                nationality: personalInfo.nationality,
                address: personalInfo.address,
                preferredContact: personalInfo.preferredContact,
                // Professional Experience
                currentJobTitle: experience.currentJobTitle,
                currentEmployer: experience.currentEmployer,
                yearsOfExperience: experience.yearsOfExperience
                    ? parseInt(experience.yearsOfExperience)
                    : null,
                previousJobs: {
                    create: experience.previousJobs.map((job) => ({
                        company: job.company,
                        role: job.role,
                        startDate: job.startDate ? new Date(job.startDate) : null,
                        endDate: job.endDate ? new Date(job.endDate) : null,
                        duties: job.duties,
                    })),
                },
                // Specialties and Skills
                category: specialties.category,
                role: specialties.role,
                proficiencyLevel: specialties.proficiencyLevel,
                secondarySpecialties: {
                    create: specialties.secondarySpecialties.map((specialty) => ({
                        specialty: specialty.specialty,
                        proficiency: specialty.proficiency,
                    })),
                },
                additionalSkills: specialties.additionalSkills,
                // Qualifications
                educationLevel: qualifications.educationLevel,
                fieldOfStudy: qualifications.fieldOfStudy,
                certifications: {
                    create: qualifications.certifications.map((cert) => ({
                        name: cert.name,
                        organization: cert.organization,
                        dateObtained: cert.dateObtained
                            ? new Date(cert.dateObtained)
                            : null,
                        expirationDate: cert.expirationDate
                            ? new Date(cert.expirationDate)
                            : null,
                    })),
                },
                licenses: {
                    create: qualifications.licenses.map((license) => ({
                        name: license.name,
                        organization: license.organization,
                        dateObtained: license.dateObtained
                            ? new Date(license.dateObtained)
                            : null,
                        expirationDate: license.expirationDate
                            ? new Date(license.expirationDate)
                            : null,
                    })),
                },
                portfolioLinks: qualifications.portfolioLinks,
                // References and Contacts
                professionalReferences: {
                    create: references.professionalReferences.map((ref) => ({
                        name: ref.name,
                        relationship: ref.relationship,
                        contact: ref.contact,
                        company: ref.company,
                    })),
                },
                emergencyContacts: {
                    create: references.emergencyContacts.map((contact) => ({
                        name: contact.name,
                        relationship: contact.relationship,
                        contact: contact.contact,
                    })),
                },
                // Additional Information
                languages: {
                    create: additionalInfo.languages.map((lang) => ({
                        language: lang.language,
                        proficiency: lang.proficiency,
                    })),
                },
                availability: additionalInfo.availability,
                willingToRelocate: additionalInfo.willingToRelocate,
                salaryExpectations: additionalInfo.salaryExpectations,
                preferredLocations: additionalInfo.preferredLocations,
                specializedTools: additionalInfo.specializedTools,
                safetyTraining: additionalInfo.safetyTraining,
                strengths: additionalInfo.strengths,
                areasForImprovement: additionalInfo.areasForImprovement,
                hobbies: additionalInfo.hobbies,
                // Referral
                referralCode: newReferralCode,
                referredBy: referralCode,
            },
        });
        return res.status(201).json({
            message: "Application created successfully",
            application,
            referralCode: newReferralCode,
        });
    }
    catch (error) {
        console.error("Error creating application:", error);
        return res.status(500).json({ error: "Failed to create application" });
    }
};
exports.createApplication = createApplication;
// Update an application's status
const updateApplicationStatus = async (req, res) => {
    const { id } = req.params;
    const { status } = req.body;
    try {
        const application = await prisma_js_1.prisma.workforceApplication.update({
            where: {
                id: Number(id),
            },
            data: {
                status,
            },
        });
        return res.status(200).json({
            message: "Application status updated successfully",
            application,
        });
    }
    catch (error) {
        console.error("Error updating application status:", error);
        return res
            .status(500)
            .json({ error: "Failed to update application status" });
    }
};
exports.updateApplicationStatus = updateApplicationStatus;
// Delete an application
const deleteApplication = async (req, res) => {
    const { id } = req.params;
    try {
        await prisma_js_1.prisma.workforceApplication.delete({
            where: {
                id: Number(id),
            },
        });
        return res.status(200).json({
            message: "Application deleted successfully",
        });
    }
    catch (error) {
        console.error("Error deleting application:", error);
        return res.status(500).json({ error: "Failed to delete application" });
    }
};
exports.deleteApplication = deleteApplication;
// Get applications by status
const getApplicationsByStatus = async (req, res) => {
    const { status } = req.params;
    try {
        const applications = await prisma_js_1.prisma.workforceApplication.findMany({
            where: {
                status: status,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json(applications);
    }
    catch (error) {
        console.error("Error fetching applications by status:", error);
        return res
            .status(500)
            .json({ error: "Failed to fetch applications by status" });
    }
};
exports.getApplicationsByStatus = getApplicationsByStatus;
// Search applications
const searchApplications = async (req, res) => {
    const { query } = req.query;
    try {
        const applications = await prisma_js_1.prisma.workforceApplication.findMany({
            where: {
                OR: [
                    {
                        fullName: {
                            contains: query,
                            mode: "insensitive",
                        },
                    },
                    {
                        email: {
                            contains: query,
                            mode: "insensitive",
                        },
                    },
                    {
                        phone: {
                            contains: query,
                            mode: "insensitive",
                        },
                    },
                    {
                        role: {
                            contains: query,
                            mode: "insensitive",
                        },
                    },
                ],
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json(applications);
    }
    catch (error) {
        console.error("Error searching applications:", error);
        return res.status(500).json({ error: "Failed to search applications" });
    }
};
exports.searchApplications = searchApplications;
// Verify referral code
const verifyReferralCode = async (req, res) => {
    const { code } = req.params;
    try {
        const application = await prisma_js_1.prisma.workforceApplication.findUnique({
            where: {
                referralCode: code,
            },
            select: {
                id: true,
                fullName: true,
            },
        });
        if (!application) {
            return res.status(404).json({ error: "Invalid referral code" });
        }
        return res.status(200).json({
            valid: true,
            referrer: application.fullName,
        });
    }
    catch (error) {
        console.error("Error verifying referral code:", error);
        return res.status(500).json({ error: "Failed to verify referral code" });
    }
};
exports.verifyReferralCode = verifyReferralCode;
// Get referral statistics
const getReferralStats = async (req, res) => {
    const { code } = req.params;
    try {
        // Find the application with this referral code
        const application = await prisma_js_1.prisma.workforceApplication.findUnique({
            where: {
                referralCode: code,
            },
            select: {
                id: true,
                fullName: true,
            },
        });
        if (!application) {
            return res.status(404).json({ error: "Invalid referral code" });
        }
        // Count how many applications were referred by this code
        const referralCount = await prisma_js_1.prisma.workforceApplication.count({
            where: {
                referredBy: code,
            },
        });
        return res.status(200).json({
            referralCode: code,
            referrer: application.fullName,
            referralCount,
        });
    }
    catch (error) {
        console.error("Error getting referral stats:", error);
        return res.status(500).json({ error: "Failed to get referral statistics" });
    }
};
exports.getReferralStats = getReferralStats;
//# sourceMappingURL=workforceApplicationController.js.map