"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkActiveSubscription = exports.checkSubscription = void 0;
const client_1 = require("@prisma/client");
const http_errors_1 = __importDefault(require("http-errors"));
const prisma = new client_1.PrismaClient();
/**
 * Middleware to check if the user has an active subscription to the required package.
 * @param requiredPackages - Array of package names required for the route.
 */
const checkSubscription = (requiredPackages) => {
    return async (req, res, next) => {
        try {
            // Skip check for SUPERADMIN users
            if (req.user?.roles.includes("SUPERADMIN")) {
                return next();
            }
            const userId = req.user?.id;
            if (!userId) {
                return next((0, http_errors_1.default)(401, "Unauthorized: User ID not found"));
            }
            // Legacy check for old subscription model
            const legacySubscriptions = await prisma.subscription.findMany({
                where: {
                    userId,
                    // Use a custom filter for active subscriptions
                    status: client_1.SubscriptionStatus.ACTIVE,
                    endDate: {
                        gt: new Date(),
                    },
                },
                include: {
                    plan: true, // Use plan instead of package
                },
            });
            if (legacySubscriptions.length > 0) {
                const userPackages = legacySubscriptions.map((sub) => sub.plan?.name || "");
                const hasRequiredPackage = requiredPackages.some((pkg) => userPackages.includes(pkg));
                if (hasRequiredPackage) {
                    return next();
                }
            }
            // New subscription model check
            const subscription = await prisma.subscription.findFirst({
                where: {
                    userId,
                    OR: [
                        { status: client_1.SubscriptionStatus.ACTIVE },
                        { status: client_1.SubscriptionStatus.TRIAL },
                    ],
                },
                include: {
                    plan: true,
                },
                orderBy: {
                    createdAt: "desc",
                },
            });
            if (!subscription) {
                return next((0, http_errors_1.default)(403, "Access denied: Active subscription required"));
            }
            // Check if trial has expired
            if (subscription.status === client_1.SubscriptionStatus.TRIAL &&
                subscription.trialEndDate) {
                const now = new Date();
                if (now > subscription.trialEndDate) {
                    // Update subscription status to EXPIRED
                    await prisma.subscription.update({
                        where: { id: subscription.id },
                        data: { status: client_1.SubscriptionStatus.EXPIRED },
                    });
                    return next((0, http_errors_1.default)(403, "Access denied: Trial period has expired"));
                }
            }
            // Check if the subscription plan includes the required modules
            if (subscription.plan && subscription.plan.modules) {
                const planModules = subscription.plan.modules;
                const hasRequiredModule = requiredPackages.some((pkg) => planModules.includes(pkg));
                if (!hasRequiredModule) {
                    return next((0, http_errors_1.default)(403, "Access denied: Your subscription plan does not include this module"));
                }
            }
            // Subscription is valid, proceed
            next();
        }
        catch (error) {
            console.error("Error checking subscription:", error);
            next((0, http_errors_1.default)(500, "Internal Server Error"));
        }
    };
};
exports.checkSubscription = checkSubscription;
/**
 * Simplified middleware to just check if the user has any active subscription
 * This can be used for routes that don't require specific modules
 */
const checkActiveSubscription = async (req, res, next) => {
    try {
        // Skip check for SUPERADMIN users
        if (req.user?.roles.includes("SUPERADMIN")) {
            return next();
        }
        const userId = req.user?.id;
        if (!userId) {
            return next((0, http_errors_1.default)(401, "Unauthorized: User ID not found"));
        }
        // Check if user has an active subscription (legacy or new model)
        const legacySubscription = await prisma.subscription.findFirst({
            where: {
                userId,
                status: client_1.SubscriptionStatus.ACTIVE,
                endDate: {
                    gt: new Date(),
                },
            },
        });
        if (legacySubscription) {
            return next();
        }
        // Check new subscription model
        const subscription = await prisma.subscription.findFirst({
            where: {
                userId,
                OR: [
                    { status: client_1.SubscriptionStatus.ACTIVE },
                    { status: client_1.SubscriptionStatus.TRIAL },
                ],
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        if (!subscription) {
            return next((0, http_errors_1.default)(403, "Subscription required: No active subscription found"));
        }
        // Check if trial has expired
        if (subscription.status === client_1.SubscriptionStatus.TRIAL &&
            subscription.trialEndDate) {
            const now = new Date();
            if (now > subscription.trialEndDate) {
                // Update subscription status to EXPIRED
                await prisma.subscription.update({
                    where: { id: subscription.id },
                    data: { status: client_1.SubscriptionStatus.EXPIRED },
                });
                return next((0, http_errors_1.default)(403, "Subscription required: Trial period has expired"));
            }
        }
        // Subscription is valid, proceed
        next();
    }
    catch (error) {
        console.error("Error checking subscription:", error);
        next((0, http_errors_1.default)(500, "Internal Server Error"));
    }
};
exports.checkActiveSubscription = checkActiveSubscription;
//# sourceMappingURL=checkSubscription.js.map