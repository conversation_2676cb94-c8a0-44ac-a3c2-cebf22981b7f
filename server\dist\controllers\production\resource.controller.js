"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteResourceAllocation = exports.updateResourceAllocation = exports.allocateResource = exports.getWorkforce = exports.createMaterialTransaction = exports.deleteMaterial = exports.updateMaterial = exports.createMaterial = exports.getMaterialById = exports.getAllMaterials = exports.createMaintenanceLog = exports.deleteEquipment = exports.updateEquipment = exports.createEquipment = exports.getEquipmentById = exports.getAllEquipment = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Get all equipment
const getAllEquipment = async (req, res, next) => {
    try {
        const { status, type } = req.query;
        const whereClause = {
            companyId: req.user?.companyId,
        };
        if (status) {
            whereClause.status = status;
        }
        if (type) {
            whereClause.type = type;
        }
        const equipment = await prisma_js_1.prisma.equipment.findMany({
            where: whereClause,
            include: {
                maintenanceLogs: {
                    orderBy: {
                        maintenanceDate: "desc",
                    },
                    take: 1,
                },
                allocations: {
                    where: {
                        endDate: {
                            gte: new Date(),
                        },
                    },
                    include: {
                        project: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                        task: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                    take: 1,
                },
            },
            orderBy: {
                name: "asc",
            },
        });
        res.json(equipment);
    }
    catch (error) {
        console.error("Error fetching equipment:", error);
        next(error);
    }
};
exports.getAllEquipment = getAllEquipment;
// Get equipment by ID
const getEquipmentById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const equipment = await prisma_js_1.prisma.equipment.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                maintenanceLogs: {
                    orderBy: {
                        maintenanceDate: "desc",
                    },
                    include: {
                        performedBy: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                avatar: true,
                            },
                        },
                    },
                },
                allocations: {
                    include: {
                        project: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                        task: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                    orderBy: {
                        startDate: "desc",
                    },
                },
            },
        });
        if (!equipment) {
            return next(createHttpError(404, "Equipment not found"));
        }
        res.json(equipment);
    }
    catch (error) {
        console.error("Error fetching equipment:", error);
        next(error);
    }
};
exports.getEquipmentById = getEquipmentById;
// Create equipment
const createEquipment = async (req, res, next) => {
    try {
        const { name, description, type, status, serialNumber, purchaseDate, purchasePrice, currentValue, location, } = req.body;
        const equipment = await prisma_js_1.prisma.equipment.create({
            data: {
                name,
                description,
                type,
                status: status || "AVAILABLE",
                serialNumber,
                purchaseDate: purchaseDate ? new Date(purchaseDate) : undefined,
                purchasePrice,
                currentValue,
                location,
                companyId: req.user?.companyId || 1,
            },
        });
        res.status(201).json(equipment);
    }
    catch (error) {
        console.error("Error creating equipment:", error);
        next(error);
    }
};
exports.createEquipment = createEquipment;
// Update equipment
const updateEquipment = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { name, description, type, status, serialNumber, purchaseDate, purchasePrice, currentValue, location, } = req.body;
        const equipment = await prisma_js_1.prisma.equipment.update({
            where: {
                id: Number(id),
            },
            data: {
                name,
                description,
                type,
                status,
                serialNumber,
                purchaseDate: purchaseDate ? new Date(purchaseDate) : undefined,
                purchasePrice,
                currentValue,
                location,
            },
        });
        res.json(equipment);
    }
    catch (error) {
        console.error("Error updating equipment:", error);
        next(error);
    }
};
exports.updateEquipment = updateEquipment;
// Delete equipment
const deleteEquipment = async (req, res, next) => {
    try {
        const { id } = req.params;
        await prisma_js_1.prisma.equipment.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Equipment deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting equipment:", error);
        next(error);
    }
};
exports.deleteEquipment = deleteEquipment;
// Create maintenance log
const createMaintenanceLog = async (req, res, next) => {
    try {
        const { equipmentId } = req.params;
        const { maintenanceDate, description, cost, status, notes } = req.body;
        const maintenanceLog = await prisma_js_1.prisma.equipmentMaintenance.create({
            data: {
                equipmentId: Number(equipmentId),
                maintenanceDate: new Date(maintenanceDate),
                description,
                cost,
                performedById: req.user?.id || null,
                status: status || "COMPLETED",
                notes,
            },
            include: {
                equipment: true,
                performedBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
            },
        });
        // Update equipment status if maintenance is completed
        if (status === "COMPLETED") {
            await prisma_js_1.prisma.equipment.update({
                where: {
                    id: Number(equipmentId),
                },
                data: {
                    status: "AVAILABLE",
                },
            });
        }
        else if (status === "IN_PROGRESS") {
            await prisma_js_1.prisma.equipment.update({
                where: {
                    id: Number(equipmentId),
                },
                data: {
                    status: "MAINTENANCE",
                },
            });
        }
        res.status(201).json(maintenanceLog);
    }
    catch (error) {
        console.error("Error creating maintenance log:", error);
        next(error);
    }
};
exports.createMaintenanceLog = createMaintenanceLog;
// Get all materials
const getAllMaterials = async (req, res, next) => {
    try {
        const { type } = req.query;
        const whereClause = {
            companyId: req.user?.companyId || undefined,
        };
        if (type) {
            whereClause.type = type;
        }
        const materials = await prisma_js_1.prisma.material.findMany({
            where: whereClause,
            include: {
                transactions: {
                    orderBy: {
                        date: "desc",
                    },
                    take: 1,
                },
                allocations: {
                    where: {
                        endDate: {
                            gte: new Date(),
                        },
                    },
                    include: {
                        project: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                    take: 1,
                },
            },
            orderBy: {
                name: "asc",
            },
        });
        res.json(materials);
    }
    catch (error) {
        console.error("Error fetching materials:", error);
        next(error);
    }
};
exports.getAllMaterials = getAllMaterials;
// Get material by ID
const getMaterialById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const material = await prisma_js_1.prisma.material.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                transactions: {
                    orderBy: {
                        date: "desc",
                    },
                    include: {
                        project: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                        performedBy: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                avatar: true,
                            },
                        },
                    },
                },
                allocations: {
                    include: {
                        project: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                        task: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                    orderBy: {
                        startDate: "desc",
                    },
                },
            },
        });
        if (!material) {
            return next(createHttpError(404, "Material not found"));
        }
        res.json(material);
    }
    catch (error) {
        console.error("Error fetching material:", error);
        next(error);
    }
};
exports.getMaterialById = getMaterialById;
// Create material
const createMaterial = async (req, res, next) => {
    try {
        const { name, description, type, unit, unitPrice, quantity, minimumStock, location, } = req.body;
        const material = await prisma_js_1.prisma.material.create({
            data: {
                name,
                description,
                type,
                unit: unit || "PIECE",
                unitPrice,
                quantity: quantity || 0,
                minimumStock,
                location,
                companyId: req.user?.companyId || 1,
            },
        });
        // Create initial transaction if quantity is provided
        if (quantity && quantity > 0) {
            await prisma_js_1.prisma.materialTransaction.create({
                data: {
                    materialId: material.id,
                    type: "PURCHASE",
                    quantity,
                    unitPrice,
                    totalPrice: quantity * (unitPrice || 0),
                    performedById: req.user?.id || 1,
                    notes: "Initial stock",
                },
            });
        }
        res.status(201).json(material);
    }
    catch (error) {
        console.error("Error creating material:", error);
        next(error);
    }
};
exports.createMaterial = createMaterial;
// Update material
const updateMaterial = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { name, description, type, unit, unitPrice, minimumStock, location } = req.body;
        const material = await prisma_js_1.prisma.material.update({
            where: {
                id: Number(id),
            },
            data: {
                name,
                description,
                type,
                unit,
                unitPrice,
                minimumStock,
                location,
            },
        });
        res.json(material);
    }
    catch (error) {
        console.error("Error updating material:", error);
        next(error);
    }
};
exports.updateMaterial = updateMaterial;
// Delete material
const deleteMaterial = async (req, res, next) => {
    try {
        const { id } = req.params;
        await prisma_js_1.prisma.material.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Material deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting material:", error);
        next(error);
    }
};
exports.deleteMaterial = deleteMaterial;
// Create material transaction
const createMaterialTransaction = async (req, res, next) => {
    try {
        const { materialId } = req.params;
        const { type, quantity, unitPrice, projectId, notes } = req.body;
        // Get current material
        const material = await prisma_js_1.prisma.material.findUnique({
            where: {
                id: Number(materialId),
            },
        });
        if (!material) {
            return next(createHttpError(404, "Material not found"));
        }
        // Calculate new quantity
        let newQuantity = material.quantity;
        if (type === "PURCHASE" || type === "RETURN" || type === "ADJUSTMENT") {
            newQuantity += quantity;
        }
        else if (type === "USAGE") {
            newQuantity -= quantity;
            if (newQuantity < 0) {
                return next(createHttpError(400, "Insufficient material quantity"));
            }
        }
        // Create transaction
        const transaction = await prisma_js_1.prisma.materialTransaction.create({
            data: {
                materialId: Number(materialId),
                type,
                quantity,
                unitPrice: unitPrice || material.unitPrice,
                totalPrice: quantity * (unitPrice || material.unitPrice || 0),
                projectId: projectId ? Number(projectId) : undefined,
                performedById: req.user?.id || 1,
                notes,
            },
            include: {
                material: true,
                project: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                performedBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
            },
        });
        // Update material quantity
        await prisma_js_1.prisma.material.update({
            where: {
                id: Number(materialId),
            },
            data: {
                quantity: newQuantity,
            },
        });
        res.status(201).json(transaction);
    }
    catch (error) {
        console.error("Error creating material transaction:", error);
        next(error);
    }
};
exports.createMaterialTransaction = createMaterialTransaction;
// Get workforce
const getWorkforce = async (req, res, next) => {
    try {
        const { status, projectId } = req.query;
        // Get all employees for the company
        const employees = await prisma_js_1.prisma.employee.findMany({
            where: {
                companyId: req.user?.companyId || undefined,
                ...(status ? { status: status } : {}),
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        phone: true,
                        avatar: true,
                    },
                },
            },
        });
        // If projectId is provided, get resource allocations for the project
        let allocations = [];
        if (projectId) {
            allocations = await prisma_js_1.prisma.resourceAllocation.findMany({
                where: {
                    projectId: Number(projectId),
                    userId: {
                        not: null,
                    },
                },
                include: {
                    user: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            avatar: true,
                        },
                    },
                    task: {
                        select: {
                            id: true,
                            name: true,
                            status: true,
                        },
                    },
                },
            });
        }
        res.json({
            employees,
            allocations: projectId ? allocations : [],
        });
    }
    catch (error) {
        console.error("Error fetching workforce:", error);
        next(error);
    }
};
exports.getWorkforce = getWorkforce;
// Allocate resource
const allocateResource = async (req, res, next) => {
    try {
        const { projectId, taskId, equipmentId, materialId, userId, startDate, endDate, quantity, notes, } = req.body;
        // Validate that at least one resource type is provided
        if (!equipmentId && !materialId && !userId) {
            return next(createHttpError(400, "At least one resource type must be provided"));
        }
        // Check if equipment is available
        if (equipmentId) {
            const equipment = await prisma_js_1.prisma.equipment.findUnique({
                where: {
                    id: Number(equipmentId),
                },
            });
            if (!equipment) {
                return next(createHttpError(404, "Equipment not found"));
            }
            if (equipment.status !== "AVAILABLE") {
                return next(createHttpError(400, "Equipment is not available"));
            }
            // Check for overlapping allocations
            const overlappingAllocations = await prisma_js_1.prisma.resourceAllocation.findMany({
                where: {
                    equipmentId: Number(equipmentId),
                    OR: [
                        {
                            startDate: {
                                lte: new Date(endDate),
                            },
                            endDate: {
                                gte: new Date(startDate),
                            },
                        },
                    ],
                },
            });
            if (overlappingAllocations.length > 0) {
                return next(createHttpError(400, "Equipment is already allocated during this period"));
            }
        }
        // Check if material has sufficient quantity
        if (materialId && quantity) {
            const material = await prisma_js_1.prisma.material.findUnique({
                where: {
                    id: Number(materialId),
                },
            });
            if (!material) {
                return next(createHttpError(404, "Material not found"));
            }
            if (material.quantity < quantity) {
                return next(createHttpError(400, "Insufficient material quantity"));
            }
        }
        // Create allocation
        const allocation = await prisma_js_1.prisma.resourceAllocation.create({
            data: {
                projectId: Number(projectId),
                taskId: taskId ? Number(taskId) : undefined,
                equipmentId: equipmentId ? Number(equipmentId) : undefined,
                materialId: materialId ? Number(materialId) : undefined,
                userId: userId ? Number(userId) : undefined,
                startDate: new Date(startDate),
                endDate: endDate ? new Date(endDate) : undefined,
                quantity,
                status: "SCHEDULED",
                notes,
            },
            include: {
                project: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                task: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                equipment: true,
                material: true,
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
            },
        });
        // Update equipment status if allocated
        if (equipmentId) {
            await prisma_js_1.prisma.equipment.update({
                where: {
                    id: Number(equipmentId),
                },
                data: {
                    status: "IN_USE",
                },
            });
        }
        // Create material transaction if material is allocated
        if (materialId && quantity) {
            await prisma_js_1.prisma.materialTransaction.create({
                data: {
                    materialId: Number(materialId),
                    type: "USAGE",
                    quantity,
                    projectId: Number(projectId),
                    performedById: req.user?.id || 1,
                    notes: `Allocated to project ${projectId}${taskId ? ` for task ${taskId}` : ""}`,
                },
            });
            // Update material quantity
            await prisma_js_1.prisma.material.update({
                where: {
                    id: Number(materialId),
                },
                data: {
                    quantity: {
                        decrement: quantity,
                    },
                },
            });
        }
        res.status(201).json(allocation);
    }
    catch (error) {
        console.error("Error allocating resource:", error);
        next(error);
    }
};
exports.allocateResource = allocateResource;
// Update resource allocation
const updateResourceAllocation = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { startDate, endDate, quantity, status, notes } = req.body;
        // Get current allocation
        const currentAllocation = await prisma_js_1.prisma.resourceAllocation.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!currentAllocation) {
            return next(createHttpError(404, "Resource allocation not found"));
        }
        // Update allocation
        const allocation = await prisma_js_1.prisma.resourceAllocation.update({
            where: {
                id: Number(id),
            },
            data: {
                startDate: startDate ? new Date(startDate) : undefined,
                endDate: endDate ? new Date(endDate) : undefined,
                quantity,
                status,
                notes,
            },
            include: {
                project: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                task: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                equipment: true,
                material: true,
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
            },
        });
        // Update equipment status if allocation is completed or cancelled
        if (currentAllocation.equipmentId &&
            (status === "COMPLETED" || status === "CANCELLED")) {
            await prisma_js_1.prisma.equipment.update({
                where: {
                    id: Number(currentAllocation.equipmentId),
                },
                data: {
                    status: "AVAILABLE",
                },
            });
        }
        // Handle material quantity adjustments if quantity changed
        if (currentAllocation.materialId &&
            currentAllocation.quantity !== quantity &&
            quantity !== undefined) {
            const quantityDifference = quantity - (currentAllocation.quantity || 0);
            if (quantityDifference !== 0) {
                // Create adjustment transaction
                await prisma_js_1.prisma.materialTransaction.create({
                    data: {
                        materialId: Number(currentAllocation.materialId),
                        type: quantityDifference > 0 ? "USAGE" : "RETURN",
                        quantity: Math.abs(quantityDifference),
                        projectId: Number(currentAllocation.projectId),
                        performedById: req.user?.id || 1,
                        notes: `Allocation adjustment for ${currentAllocation.id}`,
                    },
                });
                // Update material quantity
                await prisma_js_1.prisma.material.update({
                    where: {
                        id: Number(currentAllocation.materialId),
                    },
                    data: {
                        quantity: {
                            decrement: quantityDifference,
                        },
                    },
                });
            }
        }
        res.json(allocation);
    }
    catch (error) {
        console.error("Error updating resource allocation:", error);
        next(error);
    }
};
exports.updateResourceAllocation = updateResourceAllocation;
// Delete resource allocation
const deleteResourceAllocation = async (req, res, next) => {
    try {
        const { id } = req.params;
        // Get allocation before deleting
        const allocation = await prisma_js_1.prisma.resourceAllocation.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!allocation) {
            return next(createHttpError(404, "Resource allocation not found"));
        }
        // Delete allocation
        await prisma_js_1.prisma.resourceAllocation.delete({
            where: {
                id: Number(id),
            },
        });
        // Update equipment status if it was allocated
        if (allocation.equipmentId) {
            await prisma_js_1.prisma.equipment.update({
                where: {
                    id: allocation.equipmentId,
                },
                data: {
                    status: "AVAILABLE",
                },
            });
        }
        // Return material to inventory if it was allocated
        if (allocation.materialId && allocation.quantity) {
            // Create return transaction
            await prisma_js_1.prisma.materialTransaction.create({
                data: {
                    materialId: Number(allocation.materialId),
                    type: "RETURN",
                    quantity: allocation.quantity,
                    projectId: Number(allocation.projectId),
                    performedById: Number(req.user?.id) || 1,
                    notes: "Returned from cancelled allocation",
                },
            });
            // Update material quantity
            await prisma_js_1.prisma.material.update({
                where: {
                    id: allocation.materialId,
                },
                data: {
                    quantity: {
                        increment: allocation.quantity,
                    },
                },
            });
        }
        res.json({ message: "Resource allocation deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting resource allocation:", error);
        next(error);
    }
};
exports.deleteResourceAllocation = deleteResourceAllocation;
//# sourceMappingURL=resource.controller.js.map