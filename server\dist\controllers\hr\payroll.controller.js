"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePayrollStatus = exports.generatePayroll = exports.getPayrollById = exports.getPayrollHistory = exports.getPayrollData = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
const getPayrollData = async (req, res, next) => {
    try {
        const payroll = await prisma_js_1.prisma.payroll.findMany({
            where: {
                employee: {
                    companyId: Number(req.user?.companyId),
                },
            },
            include: {
                employee: true,
            },
        });
        res.json(payroll);
    }
    catch (error) {
        next(createHttpError(500, "Failed to fetch payroll data"));
    }
};
exports.getPayrollData = getPayrollData;
const getPayrollHistory = async (req, res, next) => {
    try {
        const history = await prisma_js_1.prisma.payroll.findMany({
            where: {
                employee: {
                    companyId: Number(req.user?.companyId),
                },
                status: "COMPLETED", // Using a valid PayrollStatus enum value
            },
            orderBy: { periodEnd: "desc" },
            include: {
                employee: true,
            },
        });
        res.json(history);
    }
    catch (error) {
        next(createHttpError(500, "Failed to fetch payroll history"));
    }
};
exports.getPayrollHistory = getPayrollHistory;
const getPayrollById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const payroll = await prisma_js_1.prisma.payroll.findUnique({
            where: { id: Number(id) },
            include: {
                employee: true,
            },
        });
        if (!payroll) {
            return next(createHttpError(404, "Payroll record not found"));
        }
        res.json(payroll);
    }
    catch (error) {
        next(createHttpError(500, "Failed to fetch payroll record"));
    }
};
exports.getPayrollById = getPayrollById;
const generatePayroll = async (req, res, next) => {
    try {
        const payload = req.body;
        // Validate required fields
        if (!payload.employeeId || !payload.periodStart || !payload.periodEnd) {
            return next(createHttpError(400, "Employee ID, period start, and period end are required"));
        }
        const payroll = await prisma_js_1.prisma.payroll.create({
            data: {
                employeeId: Number(payload.employeeId),
                periodStart: new Date(payload.periodStart),
                periodEnd: new Date(payload.periodEnd),
                basicSalary: payload.basicSalary,
                allowances: payload.allowances,
                deductions: payload.deductions, // This is a number in the schema, not a JSON string
                taxes: payload.taxes,
                netSalary: payload.netSalary,
                status: payload.status || "DRAFT", // Default to DRAFT if not provided
                processedBy: req.user?.id,
                processedAt: payload.processedAt || new Date(),
            },
        });
        res.status(201).json(payroll);
    }
    catch (error) {
        next(createHttpError(500, "Failed to generate payroll"));
    }
};
exports.generatePayroll = generatePayroll;
const updatePayrollStatus = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { status } = req.body;
        // Validate status is a valid PayrollStatus enum value
        if (!["DRAFT", "PROCESSING", "COMPLETED", "CANCELLED"].includes(status)) {
            return next(createHttpError(400, "Invalid status value"));
        }
        const payroll = await prisma_js_1.prisma.payroll.update({
            where: { id: Number(id) },
            data: {
                status: status, // Cast to PayrollStatus enum
                processedBy: req.user?.id,
                processedAt: new Date(),
            },
        });
        res.json(payroll);
    }
    catch (error) {
        if (error.code === "P2025") {
            return next(createHttpError(404, "Payroll record not found"));
        }
        next(createHttpError(500, "Failed to update payroll status"));
    }
};
exports.updatePayrollStatus = updatePayrollStatus;
//# sourceMappingURL=payroll.controller.js.map