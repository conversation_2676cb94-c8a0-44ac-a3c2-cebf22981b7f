"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteProduct = exports.updateProduct = exports.createProduct = exports.getProductById = exports.getAllProducts = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Get all products
const getAllProducts = async (req, res) => {
    try {
        const { category, isActive, search, minPrice, maxPrice, sortBy = "name", sortOrder = "asc", page = 1, limit = 10, } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        // Build filter conditions
        const where = {};
        if (category) {
            where.category = category;
        }
        if (isActive !== undefined) {
            where.isActive = isActive === "true";
        }
        if (minPrice) {
            where.price = {
                ...where.price,
                gte: Number(minPrice),
            };
        }
        if (maxPrice) {
            where.price = {
                ...where.price,
                lte: Number(maxPrice),
            };
        }
        if (search) {
            where.OR = [
                { name: { contains: search, mode: "insensitive" } },
                { description: { contains: search, mode: "insensitive" } },
                { sku: { contains: search, mode: "insensitive" } },
            ];
        }
        // Get total count for pagination
        const totalCount = await prisma_js_1.prisma.product.count({ where });
        // Get products with pagination, sorting and filtering
        const products = await prisma_js_1.prisma.product.findMany({
            where,
            orderBy: {
                [sortBy]: sortOrder,
            },
            skip,
            take: Number(limit),
        });
        res.status(200).json({
            products,
            pagination: {
                total: totalCount,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(totalCount / Number(limit)),
            },
        });
    }
    catch (error) {
        console.error("Error fetching products:", error);
        res.status(500).json({ error: "Failed to fetch products" });
    }
};
exports.getAllProducts = getAllProducts;
// Get product by ID
const getProductById = async (req, res) => {
    try {
        const { id } = req.params;
        const product = await prisma_js_1.prisma.product.findUnique({
            where: { id: Number(id) },
            include: {
                _count: {
                    select: {
                        deals: true,
                        quotationItems: true,
                    },
                },
            },
        });
        if (!product) {
            res.status(404).json({ error: "Product not found" });
            return;
        }
        res.status(200).json(product);
    }
    catch (error) {
        console.error("Error fetching product:", error);
        res.status(500).json({ error: "Failed to fetch product" });
    }
};
exports.getProductById = getProductById;
// Create a new product
const createProduct = async (req, res) => {
    try {
        const { name, description, sku, category, price, currency, cost, taxRate, isActive, imageUrl, } = req.body;
        // Check if SKU already exists
        if (sku) {
            const existingProduct = await prisma_js_1.prisma.product.findUnique({
                where: { sku },
            });
            if (existingProduct) {
                res
                    .status(400)
                    .json({ error: "A product with this SKU already exists" });
                return;
            }
        }
        // Create product
        const product = await prisma_js_1.prisma.product.create({
            data: {
                name,
                description,
                sku,
                category,
                price,
                currency: currency || "USD",
                cost,
                taxRate: taxRate || 0,
                isActive: isActive !== undefined ? isActive : true,
                imageUrl,
            },
        });
        res.status(201).json(product);
    }
    catch (error) {
        console.error("Error creating product:", error);
        res.status(500).json({ error: "Failed to create product" });
    }
};
exports.createProduct = createProduct;
// Update a product
const updateProduct = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description, sku, category, price, currency, cost, taxRate, isActive, imageUrl, } = req.body;
        // Check if product exists
        const existingProduct = await prisma_js_1.prisma.product.findUnique({
            where: { id: Number(id) },
        });
        if (!existingProduct) {
            res.status(404).json({ error: "Product not found" });
            return;
        }
        // Check if SKU already exists (if changed)
        if (sku && sku !== existingProduct.sku) {
            const productWithSku = await prisma_js_1.prisma.product.findUnique({
                where: { sku },
            });
            if (productWithSku) {
                res
                    .status(400)
                    .json({ error: "A product with this SKU already exists" });
                return;
            }
        }
        // Update product
        const updatedProduct = await prisma_js_1.prisma.product.update({
            where: { id: Number(id) },
            data: {
                name,
                description,
                sku,
                category,
                price,
                currency,
                cost,
                taxRate,
                isActive,
                imageUrl,
                updatedAt: new Date(),
            },
        });
        res.status(200).json(updatedProduct);
    }
    catch (error) {
        console.error("Error updating product:", error);
        res.status(500).json({ error: "Failed to update product" });
    }
};
exports.updateProduct = updateProduct;
// Delete a product
const deleteProduct = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if product exists
        const product = await prisma_js_1.prisma.product.findUnique({
            where: { id: Number(id) },
        });
        if (!product) {
            res.status(404).json({ error: "Product not found" });
            return;
        }
        // Check if product is used in any deals or quotations
        const dealProductCount = await prisma_js_1.prisma.dealProduct.count({
            where: { productId: Number(id) },
        });
        const quotationItemCount = await prisma_js_1.prisma.quotationItem.count({
            where: { productId: Number(id) },
        });
        if (dealProductCount > 0 || quotationItemCount > 0) {
            res.status(400).json({
                error: "Cannot delete product as it is used in deals or quotations",
                dealCount: dealProductCount,
                quotationCount: quotationItemCount,
            });
            return;
        }
        // Delete product
        await prisma_js_1.prisma.product.delete({
            where: { id: Number(id) },
        });
        res.status(200).json({ message: "Product deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting product:", error);
        res.status(500).json({ error: "Failed to delete product" });
    }
};
exports.deleteProduct = deleteProduct;
//# sourceMappingURL=products.controller.js.map