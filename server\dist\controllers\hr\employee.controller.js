"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEmployeeStats = exports.uploadEmployeeDocument = exports.getEmployeeDocuments = exports.getDepartments = exports.deleteEmployee = exports.updateEmployee = exports.createEmployee = exports.getEmployeeById = exports.getAllEmployees = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
const getAllEmployees = async (req, res, next) => {
    try {
        const { page = 1, limit = 10, search = "", status, department, employmentType, } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        // Check if Employee model exists in Prisma schema
        if (!prisma_js_1.prisma.employee) {
            return res.status(200).json({
                data: [],
                total: 0,
                page: Number(page),
                limit: Number(limit),
                departments: [],
                message: "Employee model not found in schema. Please run prisma:migrate and prisma:generate.",
            });
        }
        // Build filter conditions
        const where = {
            companyId: Number(req.user?.companyId),
        };
        if (search) {
            where.OR = [
                {
                    user: {
                        firstName: { contains: search, mode: "insensitive" },
                    },
                },
                {
                    user: {
                        lastName: { contains: search, mode: "insensitive" },
                    },
                },
                {
                    user: { email: { contains: search, mode: "insensitive" } },
                },
                { position: { contains: search, mode: "insensitive" } },
                { department: { contains: search, mode: "insensitive" } },
            ];
        }
        if (status) {
            where.status = status;
        }
        if (department) {
            where.department = department;
        }
        if (employmentType) {
            where.employmentType = employmentType;
        }
        // Get total count for pagination
        let total = 0;
        try {
            total = await prisma_js_1.prisma.employee.count({ where });
        }
        catch (error) {
            console.error("Error counting employees:", error);
            // Return empty result instead of throwing error
            return res.status(200).json({
                data: [],
                total: 0,
                page: Number(page),
                limit: Number(limit),
                departments: [],
                message: "Error counting employees. Please check Prisma schema.",
            });
        }
        // Get employees with pagination
        let employees = [];
        let departments = [];
        try {
            employees = await prisma_js_1.prisma.employee.findMany({
                where,
                include: {
                    user: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                            phone: true,
                            avatar: true,
                        },
                    },
                    leaveRequests: {
                        where: {
                            status: "PENDING",
                        },
                        select: {
                            id: true,
                        },
                    },
                },
                skip,
                take: Number(limit),
                orderBy: {
                    createdAt: "desc",
                },
            });
            // Get departments for filtering
            const deptEmployees = await prisma_js_1.prisma.employee.findMany({
                where: {
                    companyId: Number(req.user?.companyId),
                },
                select: {
                    department: true,
                },
                distinct: ["department"],
            });
            departments = deptEmployees.map((e) => ({ department: e.department }));
        }
        catch (error) {
            console.error("Error fetching employees:", error);
            // Return empty result instead of throwing error
            return res.status(200).json({
                data: [],
                total: 0,
                page: Number(page),
                limit: Number(limit),
                departments: [],
                message: "Error fetching employees. Please check Prisma schema.",
            });
        }
        res.json({
            data: employees,
            total,
            page: Number(page),
            limit: Number(limit),
            departments: departments.map((d) => d.department),
        });
    }
    catch (error) {
        console.error("Error fetching employees:", error);
        next(createHttpError(500, "Failed to fetch employees"));
    }
};
exports.getAllEmployees = getAllEmployees;
const getEmployeeById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const employee = await prisma_js_1.prisma.employee.findUnique({
            where: { id: Number(id) },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        phone: true,
                        address: true,
                        city: true,
                        state: true,
                        country: true,
                        postalCode: true,
                        avatar: true,
                        birthdate: true,
                    },
                },
                leaveRequests: {
                    orderBy: {
                        createdAt: "desc",
                    },
                    take: 5,
                },
                payrollHistory: {
                    orderBy: {
                        createdAt: "desc",
                    },
                    take: 5,
                },
                attendanceRecords: {
                    orderBy: {
                        date: "desc",
                    },
                    take: 10,
                },
                benefits: true,
                bonuses: true,
                documents: true,
                performanceReviews: {
                    orderBy: {
                        reviewDate: "desc",
                    },
                    take: 3,
                },
                trainings: {
                    orderBy: {
                        startDate: "desc",
                    },
                },
                careerPath: true,
            },
        });
        if (!employee) {
            return next(createHttpError(404, "Employee not found"));
        }
        // Check if the employee belongs to the user's company
        if (employee.companyId !== Number(req.user?.companyId)) {
            return next(createHttpError(403, "You don't have permission to view this employee"));
        }
        res.json(employee);
    }
    catch (error) {
        console.error("Error fetching employee:", error);
        next(createHttpError(500, "Failed to fetch employee"));
    }
};
exports.getEmployeeById = getEmployeeById;
const createEmployee = async (req, res, next) => {
    try {
        const { userId, position, department, startDate, salary, status, employmentType, emergencyContact, emergencyPhone, managerUserId, managerId, } = req.body;
        // Check if user exists
        if (userId) {
            const user = await prisma_js_1.prisma.user.findUnique({
                where: { id: userId },
            });
            if (!user) {
                return next(createHttpError(404, "User not found"));
            }
        }
        // Create employee
        const employee = await prisma_js_1.prisma.employee.create({
            data: {
                userId,
                companyId: Number(req.user?.companyId),
                position,
                department,
                startDate: new Date(startDate),
                salary: parseFloat(salary),
                status,
                employmentType,
                emergencyContact,
                emergencyPhone,
                managerUserId,
                managerId,
                createdBy: req.user?.id,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        phone: true,
                        avatar: true,
                    },
                },
            },
        });
        res.status(201).json(employee);
    }
    catch (error) {
        console.error("Error creating employee:", error);
        next(createHttpError(500, "Failed to create employee"));
    }
};
exports.createEmployee = createEmployee;
const updateEmployee = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { userId, position, department, startDate, endDate, salary, status, employmentType, emergencyContact, emergencyPhone, managerUserId, managerId, } = req.body;
        // Check if employee exists and belongs to the user's company
        const existingEmployee = await prisma_js_1.prisma.employee.findUnique({
            where: { id: Number(id) },
        });
        if (!existingEmployee) {
            return next(createHttpError(404, "Employee not found"));
        }
        if (existingEmployee.companyId !== Number(req.user?.companyId)) {
            return next(createHttpError(403, "You don't have permission to update this employee"));
        }
        // Update employee
        const employee = await prisma_js_1.prisma.employee.update({
            where: { id: Number(id) },
            data: {
                userId,
                position,
                department,
                startDate: startDate ? new Date(startDate) : undefined,
                endDate: endDate ? new Date(endDate) : undefined,
                salary: salary ? parseFloat(salary) : undefined,
                status,
                employmentType,
                emergencyContact,
                emergencyPhone,
                managerUserId,
                managerId,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        phone: true,
                        avatar: true,
                    },
                },
            },
        });
        res.json(employee);
    }
    catch (error) {
        console.error("Error updating employee:", error);
        next(createHttpError(500, "Failed to update employee"));
    }
};
exports.updateEmployee = updateEmployee;
const deleteEmployee = async (req, res, next) => {
    try {
        const { id } = req.params;
        // Check if employee exists and belongs to the user's company
        const existingEmployee = await prisma_js_1.prisma.employee.findUnique({
            where: { id: Number(id) },
        });
        if (!existingEmployee) {
            return next(createHttpError(404, "Employee not found"));
        }
        if (existingEmployee.companyId !== Number(req.user?.companyId)) {
            return next(createHttpError(403, "You don't have permission to delete this employee"));
        }
        // Delete employee
        await prisma_js_1.prisma.employee.delete({
            where: { id: Number(id) },
        });
        res.status(204).send();
    }
    catch (error) {
        console.error("Error deleting employee:", error);
        next(createHttpError(500, "Failed to delete employee"));
    }
};
exports.deleteEmployee = deleteEmployee;
const getDepartments = async (req, res, next) => {
    try {
        const departments = await prisma_js_1.prisma.department.findMany({
            where: {
                companyId: Number(req.user?.companyId),
            },
            orderBy: {
                name: "asc",
            },
        });
        res.json(departments);
    }
    catch (error) {
        console.error("Error fetching departments:", error);
        next(createHttpError(500, "Failed to fetch departments"));
    }
};
exports.getDepartments = getDepartments;
const getEmployeeDocuments = async (req, res, next) => {
    try {
        const { id } = req.params;
        // Check if employee exists and belongs to the user's company
        const existingEmployee = await prisma_js_1.prisma.employee.findUnique({
            where: { id: Number(id) },
        });
        if (!existingEmployee) {
            return next(createHttpError(404, "Employee not found"));
        }
        if (existingEmployee.companyId !== Number(req.user?.companyId)) {
            return next(createHttpError(403, "You don't have permission to view this employee's documents"));
        }
        const documents = await prisma_js_1.prisma.document.findMany({
            where: {
                Employee: {
                    some: {
                        id: Number(id),
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(documents);
    }
    catch (error) {
        console.error("Error fetching employee documents:", error);
        next(createHttpError(500, "Failed to fetch employee documents"));
    }
};
exports.getEmployeeDocuments = getEmployeeDocuments;
const uploadEmployeeDocument = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { name, type, url } = req.body;
        // Check if employee exists and belongs to the user's company
        const existingEmployee = await prisma_js_1.prisma.employee.findUnique({
            where: { id: Number(id) },
        });
        if (!existingEmployee) {
            return next(createHttpError(404, "Employee not found"));
        }
        if (existingEmployee.companyId !== Number(req.user?.companyId)) {
            return next(createHttpError(403, "You don't have permission to upload documents for this employee"));
        }
        const document = await prisma_js_1.prisma.document.create({
            data: {
                name,
                type: type,
                url,
                companyId: Number(req.user?.companyId),
                uploadedBy: req.user?.id || 0,
                Employee: {
                    connect: {
                        id: Number(id),
                    },
                },
            },
        });
        res.status(201).json(document);
    }
    catch (error) {
        console.error("Error uploading employee document:", error);
        next(createHttpError(500, "Failed to upload employee document"));
    }
};
exports.uploadEmployeeDocument = uploadEmployeeDocument;
const getEmployeeStats = async (req, res, next) => {
    try {
        const companyId = Number(req.user?.companyId);
        // Get total employees count
        const totalEmployees = await prisma_js_1.prisma.employee.count({
            where: {
                companyId,
            },
        });
        // Get all employees with their status, department, and employmentType
        const employees = await prisma_js_1.prisma.employee.findMany({
            where: {
                companyId,
            },
            select: {
                status: true,
                department: true,
                employmentType: true,
            },
        });
        // Count employees by status
        const statusCounts = {};
        employees.forEach((emp) => {
            const status = emp.status;
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });
        const employeesByStatus = Object.entries(statusCounts).map(([status, count]) => ({
            status,
            _count: { id: count },
        }));
        // Count employees by department
        const departmentCounts = {};
        employees.forEach((emp) => {
            const department = emp.department;
            departmentCounts[department] = (departmentCounts[department] || 0) + 1;
        });
        const employeesByDepartment = Object.entries(departmentCounts).map(([department, count]) => ({
            department,
            _count: { id: count },
        }));
        // Count employees by employment type
        const typeCounts = {};
        employees.forEach((emp) => {
            const employmentType = emp.employmentType;
            typeCounts[employmentType] = (typeCounts[employmentType] || 0) + 1;
        });
        const employeesByType = Object.entries(typeCounts).map(([employmentType, count]) => ({
            employmentType,
            _count: { id: count },
        }));
        // Get recent hires (last 30 days)
        const recentHires = await prisma_js_1.prisma.employee.count({
            where: {
                companyId,
                startDate: {
                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                },
            },
        });
        // Get pending leave requests
        const pendingLeaveRequests = await prisma_js_1.prisma.leaveRequest.count({
            where: {
                employee: {
                    companyId,
                },
                status: "PENDING",
            },
        });
        res.json({
            totalEmployees,
            employeesByStatus: employeesByStatus.map((item) => ({
                status: item.status,
                count: item._count ? item._count.id || 0 : 0,
            })),
            employeesByDepartment: employeesByDepartment.map((item) => ({
                department: item.department,
                count: item._count ? item._count.id || 0 : 0,
            })),
            employeesByType: employeesByType.map((item) => ({
                type: item.employmentType,
                count: item._count ? item._count.id || 0 : 0,
            })),
            recentHires,
            pendingLeaveRequests,
        });
    }
    catch (error) {
        console.error("Error fetching employee stats:", error);
        next(createHttpError(500, "Failed to fetch employee statistics"));
    }
};
exports.getEmployeeStats = getEmployeeStats;
//# sourceMappingURL=employee.controller.js.map