"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
// Dashboard Controller
const dashboard_controller_js_1 = require("../controllers/production/dashboard.controller.js");
// Project Controller
const project_controller_js_1 = require("../controllers/production/project.controller.js");
// Document Controller
const document_controller_js_1 = require("../controllers/production/document.controller.js");
// Timeline Controller
const timeline_controller_js_1 = require("../controllers/production/timeline.controller.js");
// Task Controller
const task_controller_js_1 = require("../controllers/production/task.controller.js");
// Quality Controller
const quality_controller_js_1 = require("../controllers/production/quality.controller.js");
// Resource Controller
const resource_controller_js_1 = require("../controllers/production/resource.controller.js");
// Report Controller
const report_controller_js_1 = require("../controllers/production/report.controller.js");
const router = express_1.default.Router();
// Dashboard and Statistics
router.get("/dashboard", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(dashboard_controller_js_1.getDashboard));
router.get("/statistics", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(dashboard_controller_js_1.getStatistics));
router.get("/ai-insights", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(dashboard_controller_js_1.getAiInsights));
// Projects
router.get("/projects/active", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(project_controller_js_1.getActiveProjects));
router.get("/projects/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(project_controller_js_1.getProjectById));
router.post("/projects", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(project_controller_js_1.createProject));
router.put("/projects/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(project_controller_js_1.updateProject));
router.delete("/projects/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(project_controller_js_1.deleteProject));
// Project Documents
router.get("/projects/:projectId/documents", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(document_controller_js_1.getProjectDocuments));
router.post("/projects/:projectId/documents", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(document_controller_js_1.uploadDocument));
router.delete("/projects/documents/:documentId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(document_controller_js_1.deleteDocument));
// Project Timeline
router.get("/projects/:projectId/timeline", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(project_controller_js_1.getProjectTimeline));
router.post("/projects/:projectId/timeline", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(timeline_controller_js_1.createTimelineEvent));
router.put("/projects/timeline/:eventId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(timeline_controller_js_1.updateTimelineEvent));
router.delete("/projects/timeline/:eventId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(timeline_controller_js_1.deleteTimelineEvent));
// Tasks
router.get("/tasks", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(task_controller_js_1.getAllTasks));
// Task Calendar and Assignments (must come before /tasks/:id)
router.get("/tasks/calendar", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(task_controller_js_1.getTaskCalendar));
router.get("/tasks/assignments", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(task_controller_js_1.getTaskAssignments));
// Task CRUD operations
router.get("/tasks/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(task_controller_js_1.getTaskById));
router.post("/tasks", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(task_controller_js_1.createTask));
router.put("/tasks/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(task_controller_js_1.updateTask));
router.put("/tasks/:id/status", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(task_controller_js_1.updateTaskStatus));
router.delete("/tasks/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(task_controller_js_1.deleteTask));
router.post("/tasks/:taskId/assignments", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(task_controller_js_1.assignTask));
router.delete("/tasks/assignments/:assignmentId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(task_controller_js_1.removeTaskAssignment));
// Quality Control
router.get("/quality/dashboard", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.getQualityDashboard));
router.get("/quality/checklists", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.getAllChecklists));
router.get("/quality/checklists/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.getChecklistById));
router.post("/quality/checklists", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.createQualityChecklist));
router.put("/quality/checklists/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.updateQualityChecklist));
router.delete("/quality/checklists/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.deleteQualityChecklist));
// Quality Check Items
router.post("/quality/checklists/:checklistId/items", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.createQualityCheckItem));
router.put("/quality/items/:itemId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.updateQualityCheckItem));
router.delete("/quality/items/:itemId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.deleteQualityCheckItem));
// Photo Evidence
router.get("/quality/photos", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.getAllPhotoEvidence));
router.post("/quality/photos", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.uploadPhotoEvidence));
router.delete("/quality/photos/:photoId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(quality_controller_js_1.deletePhotoEvidence));
// Equipment
router.get("/resources/equipment", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.getAllEquipment));
router.get("/resources/equipment/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.getEquipmentById));
router.post("/resources/equipment", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.createEquipment));
router.put("/resources/equipment/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.updateEquipment));
router.delete("/resources/equipment/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.deleteEquipment));
router.post("/resources/equipment/:equipmentId/maintenance", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.createMaintenanceLog));
// Materials
router.get("/resources/materials", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.getAllMaterials));
router.get("/resources/materials/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.getMaterialById));
router.post("/resources/materials", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.createMaterial));
router.put("/resources/materials/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.updateMaterial));
router.delete("/resources/materials/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.deleteMaterial));
router.post("/resources/materials/:materialId/transactions", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.createMaterialTransaction));
// Workforce
router.get("/resources/workforce", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.getWorkforce));
// Resource Allocation
router.post("/resources/allocations", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.allocateResource));
router.put("/resources/allocations/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.updateResourceAllocation));
router.delete("/resources/allocations/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(resource_controller_js_1.deleteResourceAllocation));
// Production Reports
router.get("/reports", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(report_controller_js_1.getAllReports));
router.get("/reports/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(report_controller_js_1.getReportById));
router.post("/reports", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(report_controller_js_1.createReport));
router.put("/reports/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(report_controller_js_1.updateReport));
router.delete("/reports/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(report_controller_js_1.deleteReport));
// Report Metrics
router.post("/reports/:reportId/metrics", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(report_controller_js_1.addMetric));
router.put("/reports/metrics/:metricId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(report_controller_js_1.updateMetric));
router.delete("/reports/metrics/:metricId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(report_controller_js_1.deleteMetric));
// Quality Metrics
router.get("/reports/quality", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(report_controller_js_1.getQualityMetrics));
// Efficiency Metrics
router.get("/reports/efficiency", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(report_controller_js_1.getEfficiencyMetrics));
exports.default = router;
//# sourceMappingURL=production.js.map