"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handlePublicEndpoint = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
// Handle public endpoint requests
const handlePublicEndpoint = async (req, res) => {
    try {
        const { endpointPath } = req.params;
        const apiKey = req.headers["x-api-key"];
        const method = req.method;
        // Validate required parameters
        if (!endpointPath) {
            res.status(400).json({ error: "Endpoint path is required" });
            return;
        }
        if (!apiKey) {
            res.status(401).json({ error: "API key is required" });
            return;
        }
        // Find the endpoint by path and method
        const endpoint = await prisma_js_1.prisma.communicationEndpoint.findFirst({
            where: {
                path: `/${endpointPath}`,
                method: method,
                apiKey,
                status: "ACTIVE",
            },
        });
        if (!endpoint) {
            res.status(404).json({ error: "Endpoint not found or inactive" });
            return;
        }
        // Get the request data
        const requestData = req.body;
        const startTime = Date.now();
        // Validate the request data against the endpoint fields
        const validationErrors = validateRequestData(requestData, endpoint.fields);
        if (validationErrors.length > 0) {
            const responseData = {
                success: false,
                errors: validationErrors,
            };
            // Log the request
            await logEndpointRequest(endpoint.id, req, requestData, responseData, 400, startTime);
            res.status(400).json(responseData);
            return;
        }
        // Process the request (in a real app, this would do something with the data)
        const responseData = {
            success: true,
            message: "Request processed successfully",
            data: requestData,
        };
        // Log the request
        await logEndpointRequest(endpoint.id, req, requestData, responseData, 200, startTime);
        res.status(200).json(responseData);
    }
    catch (error) {
        console.error("Error handling public endpoint:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};
exports.handlePublicEndpoint = handlePublicEndpoint;
// Validate request data against endpoint fields
function validateRequestData(data, fields) {
    const errors = [];
    // Check for required fields
    for (const field of fields) {
        if (field.required &&
            (data[field.name] === undefined ||
                data[field.name] === null ||
                data[field.name] === "")) {
            errors.push(`Field '${field.name}' is required`);
            continue;
        }
        // Skip validation if field is not present and not required
        if (!field.required &&
            (data[field.name] === undefined || data[field.name] === null)) {
            continue;
        }
        // Validate field type
        switch (field.type) {
            case "string":
                if (typeof data[field.name] !== "string") {
                    errors.push(`Field '${field.name}' must be a string`);
                }
                break;
            case "number":
                if (typeof data[field.name] !== "number") {
                    errors.push(`Field '${field.name}' must be a number`);
                }
                break;
            case "boolean":
                if (typeof data[field.name] !== "boolean") {
                    errors.push(`Field '${field.name}' must be a boolean`);
                }
                break;
            case "email":
                if (typeof data[field.name] !== "string" ||
                    !isValidEmail(data[field.name])) {
                    errors.push(`Field '${field.name}' must be a valid email address`);
                }
                break;
            // Add more type validations as needed
        }
        // Apply custom validation rules if defined
        if (field.validationRules) {
            try {
                const rules = JSON.parse(field.validationRules);
                if (field.type === "string") {
                    // Validate string length
                    if (rules.minLength && data[field.name].length < rules.minLength) {
                        errors.push(`Field '${field.name}' must be at least ${rules.minLength} characters long`);
                    }
                    if (rules.maxLength && data[field.name].length > rules.maxLength) {
                        errors.push(`Field '${field.name}' must be at most ${rules.maxLength} characters long`);
                    }
                    // Validate pattern
                    if (rules.pattern &&
                        !new RegExp(rules.pattern).test(data[field.name])) {
                        errors.push(`Field '${field.name}' does not match the required pattern`);
                    }
                }
                if (field.type === "number") {
                    // Validate number range
                    if (rules.min !== undefined && data[field.name] < rules.min) {
                        errors.push(`Field '${field.name}' must be at least ${rules.min}`);
                    }
                    if (rules.max !== undefined && data[field.name] > rules.max) {
                        errors.push(`Field '${field.name}' must be at most ${rules.max}`);
                    }
                }
            }
            catch (error) {
                console.error(`Error parsing validation rules for field '${field.name}':`, error);
            }
        }
    }
    return errors;
}
// Validate email format
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
// Log endpoint request
async function logEndpointRequest(endpointId, req, requestData, responseData, statusCode, startTime) {
    try {
        const processingTime = Date.now() - startTime;
        await prisma_js_1.prisma.endpointRequest.create({
            data: {
                endpointId,
                ipAddress: req.ip || req.socket.remoteAddress || "unknown",
                userAgent: req.headers["user-agent"] || "unknown",
                requestData,
                responseData,
                statusCode,
                processingTime,
            },
        });
    }
    catch (error) {
        console.error("Error logging endpoint request:", error);
    }
}
//# sourceMappingURL=public.controller.js.map