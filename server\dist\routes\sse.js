"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const router = express_1.default.Router();
// Basic SSE setup
router.get("/connect", route_helpers_js_1.auth, (req, res) => {
    // Set headers for SSE
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
    // Send initial connection established message
    res.write(`data: ${JSON.stringify({
        type: "connection",
        message: "Connected to SSE",
    })}\n\n`);
    // Handle client disconnect
    req.on("close", () => {
        res.end();
    });
});
exports.default = router;
//# sourceMappingURL=sse.js.map