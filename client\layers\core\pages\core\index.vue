<!--  client/layers/core/pages/core/index.vue -->

<script setup lang="ts">
// Import dashboard chart components
import DashboardChartRevenue from "../../components/dashboard-chart/DashboardChartRevenue.vue";
import DashboardChartGoal from "../../components/dashboard-chart/DashboardChartGoal.vue";
import DashboardChartBudget from "../../components/dashboard-chart/DashboardChartBudget.vue";
import DashboardChartProduction from "../../components/dashboard-chart/DashboardChartProduction.vue";
import DashboardChartSales from "../../components/dashboard-chart/DashboardChartSales.vue";
import DashboardCalendar from "../../components/dashboard-chart/DashboardCalendar.vue";
import DashboardCheckbox from "../../components/dashboard-chart/DashboardCheckbox.vue";

definePageMeta({
  title: "Core Dashboard",
  preview: {
    title: "Core Dashboard",
    description: "Comprehensive overview of your business metrics",
    categories: ["core"],
    src: "/img/screens/core-dashboard.png",
    srcDark: "/img/screens/core-dashboard-dark.png",
    order: 10,
    new: true,
  },
});

const { t, locale, locales } = useI18n();
</script>

<template>
  <div class="relative overflow-hidden pb-20">
    <!-- Content Area -->
    <div class="mock-content flex-1 flex flex-col">
      <!-- Mock Dashboard Content -->
      <div class="p-4 bg-muted-100 dark:bg-black overflow-y-auto flex-1">
        <!-- Key Performance Indicators -->
        <div class="grid grid-cols-12 gap-4 mb-6">
          <!-- KPI: Total Projects -->
          <div class="col-span-12 sm:col-span-6 lg:col-span-3">
            <BaseCard rounded="md" class="p-4">
              <div class="mb-1 flex items-center justify-between">
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-500 dark:text-muted-400"
                >
                  <span>{{ t("dashboard.kpi.projects") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400 dark:border-primary-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon name="solar:widget-2-bold-duotone" class="size-5" />
                </BaseIconBox>
              </div>
              <div class="mb-2">
                <BaseHeading
                  as="h4"
                  size="3xl"
                  weight="bold"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>42</span>
                </BaseHeading>
              </div>
              <div
                class="text-success-500 flex items-center gap-1 font-sans text-sm"
              >
                <span>+12.8%</span>
                <Icon name="lucide:trending-up" class="size-5" />
                <span class="text-muted-400 text-xs">{{
                  t("dashboard.kpi.since_last_month")
                }}</span>
              </div>
            </BaseCard>
          </div>

          <!-- KPI: Active Tasks -->
          <div class="col-span-12 sm:col-span-6 lg:col-span-3">
            <BaseCard rounded="md" class="p-4">
              <div class="mb-1 flex items-center justify-between">
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-500 dark:text-muted-400"
                >
                  <span>{{ t("dashboard.kpi.tasks") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400 dark:border-info-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon
                    name="solar:checklist-minimalistic-bold-duotone"
                    class="size-5"
                  />
                </BaseIconBox>
              </div>
              <div class="mb-2">
                <BaseHeading
                  as="h4"
                  size="3xl"
                  weight="bold"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>156</span>
                </BaseHeading>
              </div>
              <div
                class="text-success-500 flex items-center gap-1 font-sans text-sm"
              >
                <span>+5.2%</span>
                <Icon name="lucide:trending-up" class="size-5" />
                <span class="text-muted-400 text-xs">{{
                  t("dashboard.kpi.this_week")
                }}</span>
              </div>
            </BaseCard>
          </div>

          <!-- KPI: Budget Utilization -->
          <div class="col-span-12 sm:col-span-6 lg:col-span-3">
            <BaseCard rounded="md" class="p-4">
              <div class="mb-1 flex items-center justify-between">
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-500 dark:text-muted-400"
                >
                  <span>{{ t("dashboard.kpi.budget") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400 dark:border-success-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon name="solar:banknote-bold-duotone" class="size-5" />
                </BaseIconBox>
              </div>
              <div class="mb-2">
                <BaseHeading
                  as="h4"
                  size="3xl"
                  weight="bold"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>87%</span>
                </BaseHeading>
              </div>
              <div
                class="text-destructive-500 flex items-center gap-1 font-sans text-sm"
              >
                <span>-2.3%</span>
                <Icon name="lucide:trending-down" class="size-5" />
                <span class="text-muted-400 text-xs">{{
                  t("dashboard.kpi.remaining")
                }}</span>
              </div>
            </BaseCard>
          </div>

          <!-- KPI: Team Members -->
          <div class="col-span-12 sm:col-span-6 lg:col-span-3">
            <BaseCard rounded="md" class="p-4">
              <div class="mb-1 flex items-center justify-between">
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-500 dark:text-muted-400"
                >
                  <span>{{ t("dashboard.kpi.team") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-warning-100 text-warning-500 dark:bg-warning-500/20 dark:text-warning-400 dark:border-warning-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon
                    name="solar:users-group-rounded-bold-duotone"
                    class="size-5"
                  />
                </BaseIconBox>
              </div>
              <div class="mb-2">
                <BaseHeading
                  as="h4"
                  size="3xl"
                  weight="bold"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>24</span>
                </BaseHeading>
              </div>
              <div
                class="text-success-500 flex items-center gap-1 font-sans text-sm"
              >
                <span>+2</span>
                <Icon name="lucide:user-plus" class="size-5" />
                <span class="text-muted-400 text-xs">{{
                  t("dashboard.kpi.new_hires")
                }}</span>
              </div>
            </BaseCard>
          </div>
        </div>

        <!-- Main Dashboard Content -->
        <div class="flex flex-col lg:flex-row gap-4 mb-6">
          <!-- Revenue Chart - 60% width -->
          <div style="flex: 3">
            <BaseCard rounded="md" class="p-6">
              <div class="mb-2 flex items-center justify-between">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>{{ t("dashboard.charts.revenue") }}</span>
                </BaseHeading>
                <div class="flex gap-2">
                  <BaseSelect size="sm" rounded="md" class="w-32">
                    <option value="monthly">
                      {{ t("dashboard.period.monthly") }}
                    </option>
                    <option value="quarterly">
                      {{ t("dashboard.period.quarterly") }}
                    </option>
                    <option value="yearly">
                      {{ t("dashboard.period.yearly") }}
                    </option>
                  </BaseSelect>
                  <BaseButton size="sm" rounded="md" href="#">
                    {{ t("dashboard.actions.details") }}
                  </BaseButton>
                </div>
              </div>
              <div class="flex gap-8 mb-4">
                <div>
                  <span
                    class="text-muted-600 dark:text-muted-400 font-sans text-xs"
                    >{{ t("dashboard.period.this_month") }}</span
                  >
                  <p
                    class="text-muted-900 dark:text-muted-100 font-sans text-lg font-medium"
                  >
                    $75,689
                  </p>
                </div>
                <div>
                  <span
                    class="text-muted-600 dark:text-muted-400 font-sans text-xs"
                    >{{ t("dashboard.period.last_month") }}</span
                  >
                  <p
                    class="text-muted-900 dark:text-muted-100 font-sans text-lg font-medium"
                  >
                    $59,724
                  </p>
                </div>
              </div>
              <!-- Revenue Chart -->
              <DashboardChartRevenue />
            </BaseCard>
          </div>

          <!-- Goal Overview - 40% width -->
          <div style="flex: 2">
            <BaseCard rounded="md" class="flex h-full flex-col p-6">
              <div class="mb-6 flex items-center justify-between">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>{{ t("dashboard.charts.goal_overview") }}</span>
                </BaseHeading>
              </div>
              <!-- Goal Overview Chart -->
              <div class="mb-6 flex items-center justify-center">
                <DashboardChartGoal />
              </div>
              <div class="mt-auto">
                <div
                  class="border-muted-200 dark:border-muted-700 flex w-full border-t pt-4 text-center"
                >
                  <div
                    class="border-muted-200 dark:border-muted-700 flex-1 border-r px-2"
                  >
                    <span class="text-muted-400 font-sans text-xs">
                      {{ t("dashboard.status.completed") }}
                    </span>
                    <p
                      class="text-muted-900 dark:text-muted-100 font-sans text-lg font-medium"
                    >
                      32
                    </p>
                  </div>
                  <div class="flex-1 px-2">
                    <span class="text-muted-400 font-sans text-xs">
                      {{ t("dashboard.status.in_progress") }}
                    </span>
                    <p
                      class="text-muted-900 dark:text-muted-100 font-sans text-lg font-medium"
                    >
                      9
                    </p>
                  </div>
                </div>
              </div>
            </BaseCard>
          </div>
        </div>

        <!-- Module-specific Widgets -->
        <div class="flex flex-col lg:flex-row gap-4 mb-6">
          <!-- Human Resources Module - 33% width -->
          <div style="flex: 1">
            <BaseCard rounded="md" class="p-6">
              <div class="mb-4 flex items-center justify-between">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>{{ t("navigation.human_resources") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-warning-100 text-warning-500 dark:bg-warning-500/20 dark:text-warning-400 dark:border-warning-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon
                    name="solar:users-group-rounded-bold-duotone"
                    class="size-5"
                  />
                </BaseIconBox>
              </div>

              <!-- Team Attendance -->
              <div class="mb-4">
                <div class="flex items-center justify-between mb-2">
                  <BaseHeading
                    as="h5"
                    size="sm"
                    weight="medium"
                    lead="tight"
                    class="text-muted-800 dark:text-muted-100"
                  >
                    <span>{{ t("dashboard.hr.attendance") }}</span>
                  </BaseHeading>
                  <span class="text-success-500 text-xs font-medium">92%</span>
                </div>
                <div
                  class="h-2 w-full bg-muted-200 dark:bg-muted-700 rounded-full overflow-hidden"
                >
                  <div
                    class="h-full bg-success-500 rounded-full"
                    style="width: 92%"
                  ></div>
                </div>
              </div>

              <!-- Upcoming Interviews -->
              <div class="mb-4">
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-800 dark:text-muted-100 mb-2"
                >
                  <span>{{ t("dashboard.hr.upcoming_interviews") }}</span>
                </BaseHeading>
                <div class="space-y-2">
                  <div
                    class="flex items-center justify-between p-2 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <div class="flex items-center gap-2">
                      <BaseAvatar size="xs" src="/img/avatars/john_doe.jpg" />
                      <span class="text-muted-800 dark:text-muted-100 text-sm">
                        {{ locale === "et" ? "Jaan Tamm" : "John Doe" }}
                      </span>
                    </div>
                    <span class="text-muted-400 text-xs">{{
                      t("dashboard.hr.today")
                    }}</span>
                  </div>
                  <div
                    class="flex items-center justify-between p-2 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <div class="flex items-center gap-2">
                      <BaseAvatar size="xs" src="/img/avatars/jane_smith.jpg" />
                      <span class="text-muted-800 dark:text-muted-100 text-sm">
                        {{ locale === "et" ? "Mari Mets" : "Jane Smith" }}
                      </span>
                    </div>
                    <span class="text-muted-400 text-xs">{{
                      t("dashboard.hr.tomorrow")
                    }}</span>
                  </div>
                  <div
                    class="flex items-center justify-between p-2 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <div class="flex items-center gap-2">
                      <BaseAvatar
                        size="xs"
                        src="/img/avatars/andrew_hill.jpg"
                      />
                      <span class="text-muted-800 dark:text-muted-100 text-sm">
                        {{ locale === "et" ? "Andres Kask" : "Andrew Hill" }}
                      </span>
                    </div>
                    <span class="text-muted-400 text-xs">{{
                      t("dashboard.hr.tomorrow")
                    }}</span>
                  </div>
                </div>
              </div>

              <!-- Recent Applications -->
              <div>
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-800 dark:text-muted-100 mb-2"
                >
                  <span>{{ t("dashboard.hr.recent_applications") }}</span>
                </BaseHeading>
                <div class="text-muted-400 text-sm">
                  <span>+12 {{ t("dashboard.hr.new_applications") }}</span>
                </div>
              </div>
            </BaseCard>
          </div>

          <!-- Budget Module - 33% width -->
          <div style="flex: 1">
            <BaseCard rounded="md" class="p-6">
              <div class="mb-4 flex items-center justify-between">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>{{ t("dashboard.budget.distribution") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400 dark:border-success-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon name="solar:banknote-bold-duotone" class="size-5" />
                </BaseIconBox>
              </div>

              <!-- Budget Distribution -->
              <div class="mb-4">
                <!-- Budget Distribution Chart -->
                <DashboardChartBudget class="mb-2" />
              </div>

              <!-- Recent Expenses -->
              <div>
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-800 dark:text-muted-100 mb-2"
                >
                  <span>{{ t("dashboard.budget.recent_expenses") }}</span>
                </BaseHeading>
                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <span class="text-muted-800 dark:text-muted-100 text-sm">{{
                      t("dashboard.budget.equipment")
                    }}</span>
                    <span class="text-destructive-500 text-sm font-medium"
                      >-$2,450</span
                    >
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-muted-800 dark:text-muted-100 text-sm">{{
                      t("dashboard.budget.marketing")
                    }}</span>
                    <span class="text-destructive-500 text-sm font-medium"
                      >-$1,800</span
                    >
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-muted-800 dark:text-muted-100 text-sm">{{
                      t("dashboard.budget.software")
                    }}</span>
                    <span class="text-destructive-500 text-sm font-medium"
                      >-$950</span
                    >
                  </div>
                </div>
              </div>
            </BaseCard>
          </div>

          <!-- Production Module - 33% width -->
          <div style="flex: 1">
            <BaseCard rounded="md" class="p-6">
              <div class="mb-4 flex items-center justify-between">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>{{ t("navigation.production") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400 dark:border-info-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon name="solar:buildings-3-outline" class="size-5" />
                </BaseIconBox>
              </div>

              <!-- Production Status -->
              <div class="mb-4">
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-800 dark:text-muted-100 mb-2"
                >
                  <span>{{ t("dashboard.production.status") }}</span>
                </BaseHeading>
                <div class="grid grid-cols-2 gap-2">
                  <div
                    class="bg-muted-100 dark:bg-muted-800 p-3 rounded-md text-center"
                  >
                    <div
                      class="text-muted-800 dark:text-muted-100 text-lg font-medium"
                    >
                      24
                    </div>
                    <div class="text-muted-400 text-xs">
                      {{ t("dashboard.production.active") }}
                    </div>
                  </div>
                  <div
                    class="bg-muted-100 dark:bg-muted-800 p-3 rounded-md text-center"
                  >
                    <div
                      class="text-muted-800 dark:text-muted-100 text-lg font-medium"
                    >
                      8
                    </div>
                    <div class="text-muted-400 text-xs">
                      {{ t("dashboard.production.pending") }}
                    </div>
                  </div>
                  <div
                    class="bg-muted-100 dark:bg-muted-800 p-3 rounded-md text-center"
                  >
                    <div
                      class="text-muted-800 dark:text-muted-100 text-lg font-medium"
                    >
                      95%
                    </div>
                    <div class="text-muted-400 text-xs">
                      {{ t("dashboard.production.efficiency") }}
                    </div>
                  </div>
                  <div
                    class="bg-muted-100 dark:bg-muted-800 p-3 rounded-md text-center"
                  >
                    <div
                      class="text-muted-800 dark:text-muted-100 text-lg font-medium"
                    >
                      3
                    </div>
                    <div class="text-muted-400 text-xs">
                      {{ t("dashboard.production.issues") }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Production Timeline -->
              <div>
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-800 dark:text-muted-100 mb-2"
                >
                  <span>{{ t("dashboard.production.timeline") }}</span>
                </BaseHeading>
                <!-- Production Timeline Chart -->
                <DashboardChartProduction />
              </div>
            </BaseCard>
          </div>
        </div>

        <!-- Communication and Sales Widgets -->
        <div class="flex flex-col lg:flex-row gap-4 mb-6">
          <!-- Communication Module - 50% width -->
          <div style="flex: 1">
            <BaseCard rounded="md" class="p-6">
              <div class="mb-4 flex items-center justify-between">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>{{ t("navigation.communication") }}</span>
                </BaseHeading>
                <BaseButton size="sm" rounded="md" href="#">
                  {{ t("dashboard.actions.view_all") }}
                </BaseButton>
              </div>

              <!-- Recent Messages -->
              <div class="space-y-3">
                <div
                  class="flex items-start gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                >
                  <BaseAvatar size="sm" src="/img/avatars/person-1.jpg" />
                  <div class="flex-1">
                    <div class="flex items-center justify-between mb-1">
                      <span
                        class="text-muted-800 dark:text-muted-100 text-sm font-medium"
                      >
                        {{
                          locale === "et" ? "Mihkel Jõesaar" : "Michael Johnson"
                        }}
                      </span>
                      <span class="text-muted-400 text-xs">10:32 AM</span>
                    </div>
                    <p class="text-muted-500 dark:text-muted-300 text-sm">
                      {{ t("dashboard.communication.message_1") }}
                    </p>
                  </div>
                </div>
                <div
                  class="flex items-start gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                >
                  <BaseAvatar size="sm" src="/img/avatars/sarah_williams.jpg" />
                  <div class="flex-1">
                    <div class="flex items-center justify-between mb-1">
                      <span
                        class="text-muted-800 dark:text-muted-100 text-sm font-medium"
                      >
                        {{
                          locale === "et" ? "Sandra Villem" : "Sarah Williams"
                        }}
                      </span>
                      <span class="text-muted-400 text-xs">Yesterday</span>
                    </div>
                    <p class="text-muted-500 dark:text-muted-300 text-sm">
                      {{ t("dashboard.communication.message_2") }}
                    </p>
                  </div>
                </div>
                <div
                  class="flex items-start gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                >
                  <BaseAvatar size="sm" src="/img/avatars/david_brown.jpg" />
                  <div class="flex-1">
                    <div class="flex items-center justify-between mb-1">
                      <span
                        class="text-muted-800 dark:text-muted-100 text-sm font-medium"
                      >
                        {{ locale === "et" ? "Taavi Pruun" : "David Brown" }}
                      </span>
                      <span class="text-muted-400 text-xs">Yesterday</span>
                    </div>
                    <p class="text-muted-500 dark:text-muted-300 text-sm">
                      {{ t("dashboard.communication.message_3") }}
                    </p>
                  </div>
                </div>
                <div
                  class="flex items-start gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                >
                  <BaseAvatar size="sm" src="/img/avatars/person-4.jpg" />
                  <div class="flex-1">
                    <div class="flex items-center justify-between mb-1">
                      <span
                        class="text-muted-800 dark:text-muted-100 text-sm font-medium"
                      >
                        {{ locale === "et" ? "Liisa Rebane" : "Lisa Fox" }}
                      </span>
                      <span class="text-muted-400 text-xs">2 days ago</span>
                    </div>
                    <p class="text-muted-500 dark:text-muted-300 text-sm">
                      {{ t("dashboard.communication.message_1") }}
                    </p>
                  </div>
                </div>
              </div>
            </BaseCard>
          </div>

          <!-- Sales Module - 50% width -->
          <div style="flex: 1">
            <BaseCard rounded="md" class="p-6">
              <div class="mb-4 flex items-center justify-between">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>{{ t("navigation.sales") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400 dark:border-primary-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon name="lucide:bar-chart-2" class="size-5" />
                </BaseIconBox>
              </div>

              <!-- Sales Performance -->
              <div class="mb-4">
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-800 dark:text-muted-100 mb-2"
                >
                  <span>{{ t("dashboard.sales.performance") }}</span>
                </BaseHeading>
                <!-- Sales Performance Chart -->
                <DashboardChartSales class="mb-2" />
              </div>

              <!-- Top Products -->
              <div>
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-800 dark:text-muted-100 mb-2"
                >
                  <span>{{ t("dashboard.sales.top_products") }}</span>
                </BaseHeading>
                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <span class="text-muted-800 dark:text-muted-100 text-sm"
                      >Product A</span
                    >
                    <div class="flex items-center gap-2">
                      <span class="text-success-500 text-sm font-medium"
                        >$12,450</span
                      >
                      <span class="text-success-500 text-xs">+8%</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-muted-800 dark:text-muted-100 text-sm"
                      >Product B</span
                    >
                    <div class="flex items-center gap-2">
                      <span class="text-success-500 text-sm font-medium"
                        >$9,820</span
                      >
                      <span class="text-success-500 text-xs">+5%</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-muted-800 dark:text-muted-100 text-sm"
                      >Product C</span
                    >
                    <div class="flex items-center gap-2">
                      <span class="text-destructive-500 text-sm font-medium"
                        >$6,380</span
                      >
                      <span class="text-destructive-500 text-xs">-2%</span>
                    </div>
                  </div>
                </div>
              </div>
            </BaseCard>
          </div>
        </div>

        <!-- Calendar and Tasks Widgets -->
        <div class="flex flex-col lg:flex-row gap-4">
          <!-- Calendar Widget - 60% width -->
          <div style="flex: 3">
            <BaseCard rounded="md" class="p-6">
              <div class="mb-4 flex items-center justify-between">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>{{ t("dashboard.calendar.title") }}</span>
                </BaseHeading>
                <div class="flex gap-2">
                  <BaseButton size="sm" rounded="md" variant="muted" href="#">
                    {{ t("dashboard.calendar.today") }}
                  </BaseButton>
                  <BaseButton size="sm" rounded="md" variant="muted" href="#">
                    <Icon name="lucide:chevron-left" class="size-4" />
                  </BaseButton>
                  <BaseButton size="sm" rounded="md" variant="muted" href="#">
                    <Icon name="lucide:chevron-right" class="size-4" />
                  </BaseButton>
                </div>
              </div>

              <!-- Calendar Component -->
              <DashboardCalendar />
            </BaseCard>
          </div>

          <!-- Tasks Widget - 40% width -->
          <div style="flex: 2">
            <BaseCard rounded="md" class="p-6">
              <div class="mb-4 flex items-center justify-between">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>{{ t("dashboard.tasks.title") }}</span>
                </BaseHeading>
                <BaseButton size="sm" rounded="md" variant="primary" href="#">
                  <Icon name="lucide:plus" class="size-4" />
                </BaseButton>
              </div>

              <!-- Tasks List -->
              <div class="space-y-3">
                <div
                  class="flex items-center gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                >
                  <DashboardCheckbox checked />
                  <span class="text-muted-400 text-sm line-through">{{
                    t("dashboard.tasks.task_1")
                  }}</span>
                </div>
                <div
                  class="flex items-center gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                >
                  <DashboardCheckbox checked />
                  <span class="text-muted-400 text-sm line-through">{{
                    t("dashboard.tasks.task_2")
                  }}</span>
                </div>
                <div
                  class="flex items-center gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                >
                  <DashboardCheckbox />
                  <span class="text-muted-800 dark:text-muted-100 text-sm">{{
                    t("dashboard.tasks.task_3")
                  }}</span>
                </div>
                <div
                  class="flex items-center gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                >
                  <DashboardCheckbox />
                  <span class="text-muted-800 dark:text-muted-100 text-sm">{{
                    t("dashboard.tasks.task_4")
                  }}</span>
                </div>
                <div
                  class="flex items-center gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                >
                  <DashboardCheckbox />
                  <span class="text-muted-800 dark:text-muted-100 text-sm">{{
                    t("dashboard.tasks.task_5")
                  }}</span>
                </div>
              </div>
            </BaseCard>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
