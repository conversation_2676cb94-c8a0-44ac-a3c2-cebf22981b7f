import { ref } from "vue";

const isOpen = ref(false);
const selectedActivityId = ref<string | null>(null);

export function useActivityDialog() {
  const openDialog = (activityId: string) => {
    selectedActivityId.value = activityId;
    isOpen.value = true;
  };

  const closeDialog = () => {
    isOpen.value = false;
    selectedActivityId.value = null;
  };

  return {
    isOpen,
    selectedActivityId,
    openDialog,
    closeDialog,
  };
}
