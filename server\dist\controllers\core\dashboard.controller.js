"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDashboardStats = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Using centralized prisma instance from lib/prisma.js
/**
 * Get dashboard statistics for the Core module
 */
const getDashboardStats = async (req, res, next) => {
    try {
        if (!req.user) {
            return next(createHttpError(401, "Unauthorized"));
        }
        const userId = req.user.id;
        const companyId = req.user.companyId;
        // Get date range for filtering
        const endDate = new Date();
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 1); // Last 30 days
        // Get module statistics
        const moduleStats = {
            budget: await getBudgetStats(String(companyId)),
            hr: await getHrStats(String(companyId)),
            production: await getProductionStats(String(companyId)),
            communication: await getCommunicationStats(String(userId)),
        };
        // Get project status data
        const projectStatus = await getProjectStatusData(String(companyId));
        // Get budget allocation data
        const budgetAllocation = await getBudgetAllocationData(companyId);
        // Get employee activity data
        const employeeActivity = await getEmployeeActivityData(companyId);
        // Get recent activities
        const recentActivities = await getRecentActivities(companyId);
        res.json({
            moduleStats,
            projectStatus,
            budgetAllocation,
            employeeActivity,
            recentActivities,
        });
    }
    catch (error) {
        console.error("Error fetching dashboard stats:", error);
        next(createHttpError(500, "Error fetching dashboard statistics"));
    }
};
exports.getDashboardStats = getDashboardStats;
/**
 * Get budget statistics
 */
async function getBudgetStats(companyId) {
    try {
        // In a real implementation, this would query the database
        // For now, using mock data
        return {
            totalBudget: 1250000,
            spent: 785000,
            remaining: 465000,
            projects: 12,
        };
    }
    catch (error) {
        console.error("Error fetching budget stats:", error);
        throw error;
    }
}
/**
 * Get HR statistics
 */
async function getHrStats(companyId) {
    try {
        // Count employees
        const totalEmployees = await prisma_js_1.prisma.employee.count({
            where: {
                companyId: Number(companyId),
            },
        });
        // Count new hires in the last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const newHires = await prisma_js_1.prisma.employee.count({
            where: {
                companyId: Number(companyId),
                startDate: {
                    gte: thirtyDaysAgo,
                },
            },
        });
        // Count open positions
        // Note: JobPosting model doesn't exist in the schema, using mock data
        let openPositions = 0;
        try {
            // This is a placeholder for when the JobPosting model is added
            // openPositions = await prisma.jobPosting.count({
            //   where: {
            //     companyId: Number(companyId),
            //     status: "OPEN",
            //   },
            // });
            openPositions = 5; // Mock data
        }
        catch (error) {
            console.error("Error counting open positions:", error);
            openPositions = 0;
        }
        // Count departments
        const departments = await prisma_js_1.prisma.department.count({
            where: {
                companyId: Number(companyId),
            },
        });
        return {
            totalEmployees,
            newHires,
            openPositions,
            departments,
        };
    }
    catch (error) {
        console.error("Error fetching HR stats:", error);
        // Return mock data as fallback
        return {
            totalEmployees: 162,
            newHires: 8,
            openPositions: 5,
            departments: 7,
        };
    }
}
/**
 * Get production statistics
 */
async function getProductionStats(companyId) {
    try {
        // Count active projects
        const activeProjects = await prisma_js_1.prisma.project.count({
            where: {
                companyId: Number(companyId),
                status: "IN_PROGRESS",
            },
        });
        // Count completed projects
        const completedProjects = await prisma_js_1.prisma.project.count({
            where: {
                companyId: Number(companyId),
                status: "COMPLETED",
            },
        });
        // Count tasks in progress
        const tasksInProgress = await prisma_js_1.prisma.task.count({
            where: {
                project: {
                    companyId: Number(companyId),
                },
                status: "IN_PROGRESS",
            },
        });
        // Count completed tasks
        const tasksCompleted = await prisma_js_1.prisma.task.count({
            where: {
                project: {
                    companyId: Number(companyId),
                },
                status: "COMPLETED",
            },
        });
        return {
            activeProjects,
            completedProjects,
            tasksInProgress,
            tasksCompleted,
        };
    }
    catch (error) {
        console.error("Error fetching production stats:", error);
        // Return mock data as fallback
        return {
            activeProjects: 18,
            completedProjects: 24,
            tasksInProgress: 87,
            tasksCompleted: 143,
        };
    }
}
/**
 * Get communication statistics
 */
async function getCommunicationStats(userId) {
    try {
        // Count unread emails
        // Note: Email model doesn't exist in the schema, using mock data
        let unreadEmails = 0;
        try {
            // This is a placeholder for when the Email model is added
            // unreadEmails = await prisma.email.count({
            //   where: {
            //     recipientId: Number(userId),
            //     isRead: false,
            //   },
            // });
            unreadEmails = 12; // Mock data
        }
        catch (error) {
            console.error("Error counting unread emails:", error);
            unreadEmails = 0;
        }
        // Count unread messages
        let unreadMessages = 0;
        try {
            unreadMessages = await prisma_js_1.prisma.chatMessage.count({
                where: {
                    conversation: {
                        participants: {
                            some: {
                                userId: Number(userId),
                            },
                        },
                    },
                    status: "SENT",
                    senderId: {
                        not: Number(userId),
                    },
                },
            });
        }
        catch (error) {
            console.error("Error counting unread messages:", error);
            unreadMessages = 5; // Mock data
        }
        // Count scheduled meetings
        // Note: Meeting model doesn't exist in the schema, using mock data
        let scheduledMeetings = 0;
        try {
            const now = new Date();
            const endOfDay = new Date();
            endOfDay.setHours(23, 59, 59, 999);
            // This is a placeholder for when the Meeting model is added
            // scheduledMeetings = await prisma.meeting.count({
            //   where: {
            //     attendees: {
            //       some: {
            //         userId: Number(userId),
            //       },
            //     },
            //     startTime: {
            //       gte: now,
            //       lte: endOfDay,
            //     },
            //   },
            // });
            scheduledMeetings = 3; // Mock data
        }
        catch (error) {
            console.error("Error counting scheduled meetings:", error);
            scheduledMeetings = 0;
        }
        // Calculate total unread
        const totalUnread = unreadEmails + unreadMessages;
        return {
            unreadEmails,
            unreadMessages,
            scheduledMeetings,
            totalUnread,
        };
    }
    catch (error) {
        console.error("Error fetching communication stats:", error);
        // Return mock data as fallback
        return {
            unreadEmails: 12,
            unreadMessages: 5,
            scheduledMeetings: 3,
            totalUnread: 17,
        };
    }
}
/**
 * Get project status data
 */
async function getProjectStatusData(companyId) {
    try {
        // Get project counts by status
        // Using findMany instead of groupBy to avoid TypeScript issues
        const projects = await prisma_js_1.prisma.project.findMany({
            where: {
                companyId: Number(companyId),
            },
            select: {
                status: true,
            },
        });
        // Count projects by status
        const statusCounts = {};
        projects.forEach((project) => {
            const status = project.status;
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });
        // Transform data for chart
        const statusMap = {
            IN_PROGRESS: "In Progress",
            COMPLETED: "Completed",
            PLANNING: "Planned",
            ON_HOLD: "On Hold",
            CANCELLED: "Cancelled",
        };
        const series = [];
        const labels = [];
        for (const [status, label] of Object.entries(statusMap)) {
            series.push(statusCounts[status] || 0);
            labels.push(label);
        }
        return {
            series,
            labels,
        };
    }
    catch (error) {
        console.error("Error fetching project status data:", error);
        // Return mock data as fallback
        return {
            series: [44, 55, 13, 33],
            labels: ["In Progress", "Completed", "Planned", "On Hold"],
        };
    }
}
/**
 * Get budget allocation data
 */
async function getBudgetAllocationData(companyId) {
    try {
        // In a real implementation, this would query the database
        // For now, using mock data
        return {
            series: [
                {
                    name: "Budget",
                    data: [44000, 55000, 57000, 56000, 61000, 58000, 63000, 60000, 66000],
                },
                {
                    name: "Expenses",
                    data: [35000, 41000, 36000, 26000, 45000, 48000, 52000, 53000, 41000],
                },
            ],
            categories: [
                "Jan",
                "Feb",
                "Mar",
                "Apr",
                "May",
                "Jun",
                "Jul",
                "Aug",
                "Sep",
            ],
        };
    }
    catch (error) {
        console.error("Error fetching budget allocation data:", error);
        throw error;
    }
}
/**
 * Get employee activity data
 */
async function getEmployeeActivityData(companyId) {
    try {
        // In a real implementation, this would query the database
        // For now, using mock data
        return {
            series: [
                {
                    name: "Active Tasks",
                    data: [31, 40, 28, 51, 42, 109, 100],
                },
                {
                    name: "Completed Tasks",
                    data: [11, 32, 45, 32, 34, 52, 41],
                },
            ],
            categories: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        };
    }
    catch (error) {
        console.error("Error fetching employee activity data:", error);
        throw error;
    }
}
/**
 * Get recent activities
 */
async function getRecentActivities(companyId) {
    try {
        // In a real implementation, this would query the database
        // For now, using mock data
        return [
            {
                id: 1,
                user: "John Doe",
                action: "completed task",
                target: "Website Redesign",
                time: "2 hours ago",
                avatar: "/img/avatars/10.svg",
                module: "Production",
            },
            {
                id: 2,
                user: "Jane Smith",
                action: "approved budget for",
                target: "Q3 Marketing Campaign",
                time: "4 hours ago",
                avatar: "/img/avatars/22.svg",
                module: "Budget",
            },
            {
                id: 3,
                user: "Mike Johnson",
                action: "hired new employee",
                target: "Sarah Williams",
                time: "Yesterday",
                avatar: "/img/avatars/16.svg",
                module: "HR",
            },
            {
                id: 4,
                user: "Anna Lee",
                action: "sent email to",
                target: "All Departments",
                time: "Yesterday",
                avatar: "/img/avatars/3.svg",
                module: "Communication",
            },
            {
                id: 5,
                user: "Robert Chen",
                action: "created new project",
                target: "Mobile App Development",
                time: "2 days ago",
                avatar: "/img/avatars/14.svg",
                module: "Production",
            },
        ];
    }
    catch (error) {
        console.error("Error fetching recent activities:", error);
        throw error;
    }
}
//# sourceMappingURL=dashboard.controller.js.map