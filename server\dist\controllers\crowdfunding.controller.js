"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSupporterPayment = exports.getCrowdfundingStats = void 0;
const prisma_js_1 = require("../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
/**
 * Get crowdfunding statistics
 * @route GET /api/v1/crowdfunding/stats
 * @access Public
 */
const getCrowdfundingStats = async (req, res) => {
    try {
        console.log("Fetching crowdfunding stats...");
        // Get total amount of supporter payments with all fields
        const supporterPayments = await prisma_js_1.prisma.payment.findMany({
            where: {
                isSupporterPayment: true,
                status: "succeeded", // Only count successful payments
            },
            select: {
                id: true,
                amount: true,
                currency: true,
                status: true,
                supporterUserId: true,
                packageType: true,
                packageAmount: true,
                createdAt: true,
            },
        });
        console.log(`Found ${supporterPayments.length} supporter payments`);
        // Log the payments for debugging
        console.log("Supporter payments:", JSON.stringify(supporterPayments, null, 2));
        // Calculate total amount with currency handling
        const totalAmount = supporterPayments.reduce((sum, payment) => {
            // Only add EUR payments or convert if needed
            if (payment.currency === "EUR") {
                return sum + payment.amount;
            }
            else if (payment.currency === "USD") {
                // Convert USD to EUR (approximate conversion)
                return sum + payment.amount * 0.92;
            }
            else {
                // For other currencies, just add as is for now
                console.log(`Warning: Payment ${payment.id} has currency ${payment.currency}`);
                return sum + payment.amount;
            }
        }, 0);
        console.log("Total amount calculated:", totalAmount);
        // Count unique supporters
        const uniqueSupporters = new Set(supporterPayments.map((payment) => payment.supporterUserId)).size;
        // Return statistics
        return res.status(200).json({
            success: true,
            data: {
                totalAmount,
                supportersCount: uniqueSupporters,
                goal: 150000, // Hardcoded goal amount
                percentage: Math.min(100, (totalAmount / 150000) * 100), // Calculate percentage with max of 100%
            },
        });
    }
    catch (error) {
        console.error("Error getting crowdfunding stats:", error);
        return res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
};
exports.getCrowdfundingStats = getCrowdfundingStats;
/**
 * Create a supporter payment
 * @route POST /api/v1/crowdfunding/supporter-payment
 * @access Private
 */
const createSupporterPayment = async (req, res) => {
    try {
        const { amount, packageType, stripePaymentId } = req.body;
        // Get userId from authenticated user
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "User not authenticated",
            });
        }
        // Create payment record
        const payment = await prisma_js_1.prisma.payment.create({
            data: {
                amount,
                currency: "EUR", // Explicitly set currency to EUR
                status: "succeeded",
                stripePaymentId,
                isSupporterPayment: true,
                supporterUserId: userId,
                packageType,
                packageAmount: amount,
            },
        });
        return res.status(201).json({
            success: true,
            data: payment,
        });
    }
    catch (error) {
        console.error("Error creating supporter payment:", error);
        return res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
};
exports.createSupporterPayment = createSupporterPayment;
//# sourceMappingURL=crowdfunding.controller.js.map