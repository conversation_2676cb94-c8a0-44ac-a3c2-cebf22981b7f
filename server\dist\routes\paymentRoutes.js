"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const paymentController_js_1 = require("../controllers/paymentController.js");
// Import new card management controllers
const cards_controller_js_1 = require("../controllers/payment/cards.controller.js");
// Import transaction controllers
const transactions_controller_js_1 = require("../controllers/payment/transactions.controller.js");
const router = express_1.default.Router();
// Public routes
router.post("/create-intent", (0, route_helpers_js_1.wrapController)(paymentController_js_1.createPaymentIntent));
router.post("/confirm", (0, route_helpers_js_1.wrapController)(paymentController_js_1.confirmPayment));
// Protected routes - Legacy Stripe integration
router.get("/stripe/cards", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(paymentController_js_1.getUserCards));
router.post("/create-subscription", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(paymentController_js_1.createSubscription));
// Protected routes - New Card Management System
router.get("/cards", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(cards_controller_js_1.getUserCards));
router.get("/cards/:cardId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(cards_controller_js_1.getCard));
router.post("/cards", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(cards_controller_js_1.createCard));
router.put("/cards/:cardId/set-primary", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(cards_controller_js_1.setPrimaryCard));
router.put("/cards/:cardId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(cards_controller_js_1.updateCard));
router.delete("/cards/:cardId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(cards_controller_js_1.deleteCard));
// Protected routes - Transaction Management
router.get("/transactions", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(transactions_controller_js_1.getUserTransactions));
router.get("/transactions/:transactionId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(transactions_controller_js_1.getTransaction));
router.post("/transactions", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(transactions_controller_js_1.createTransaction));
router.put("/transactions/:transactionId/status", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(transactions_controller_js_1.updateTransactionStatus));
router.get("/transactions/stats", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(transactions_controller_js_1.getTransactionStats));
exports.default = router;
//# sourceMappingURL=paymentRoutes.js.map