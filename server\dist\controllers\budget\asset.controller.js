"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteAsset = exports.updateAsset = exports.getAssets = exports.createAsset = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
/**
 * Create a new Asset.
 */
const createAsset = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const { method, customName, initialAmount, currentAmount } = req.body;
        const asset = await prisma_js_1.prisma.asset.create({
            data: {
                userId: userId,
                method: method, // Cast to AssetMethod enum
                customName: customName || null,
                initialAmount: initialAmount ?? 0,
                currentAmount: currentAmount !== undefined ? currentAmount : initialAmount ?? 0,
            },
        });
        res.status(201).json(asset);
    }
    catch (error) {
        next(error);
    }
};
exports.createAsset = createAsset;
/**
 * Get all Assets for the authenticated user.
 */
const getAssets = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const assets = await prisma_js_1.prisma.asset.findMany({
            where: { userId },
            orderBy: { createdAt: "desc" },
        });
        res.json(assets);
    }
    catch (error) {
        next(error);
    }
};
exports.getAssets = getAssets;
/**
 * Update an existing Asset.
 */
const updateAsset = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const assetId = Number(req.params.id);
        const { method, customName, initialAmount, currentAmount } = req.body;
        const existing = await prisma_js_1.prisma.asset.findUnique({
            where: { id: assetId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Asset not found or not yours." });
        }
        const updated = await prisma_js_1.prisma.asset.update({
            where: { id: assetId },
            data: {
                method: method, // Cast to AssetMethod enum
                customName: customName || null,
                initialAmount: initialAmount ?? existing.initialAmount,
                currentAmount: currentAmount !== undefined ? currentAmount : existing.currentAmount,
            },
        });
        res.json(updated);
    }
    catch (error) {
        next(error);
    }
};
exports.updateAsset = updateAsset;
/**
 * Delete an existing Asset.
 */
const deleteAsset = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const assetId = Number(req.params.id);
        const existing = await prisma_js_1.prisma.asset.findUnique({
            where: { id: assetId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Asset not found or not yours." });
        }
        await prisma_js_1.prisma.asset.delete({
            where: { id: assetId },
        });
        res.json({ success: true });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteAsset = deleteAsset;
//# sourceMappingURL=asset.controller.js.map