"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../../utils/route-helpers.js");
const jobController_js_1 = require("../../controllers/recruitment/jobController.js");
const router = express_1.default.Router();
// All routes require authentication
router.use(route_helpers_js_1.auth);
// Job routes
router.get("/company/:companyId", (0, route_helpers_js_1.wrapController)(jobController_js_1.getJobs));
router.get("/:id", (0, route_helpers_js_1.wrapController)(jobController_js_1.getJob));
router.post("/company/:companyId", (0, route_helpers_js_1.wrapController)(jobController_js_1.createJob));
router.put("/:id", (0, route_helpers_js_1.wrapController)(jobController_js_1.updateJob));
router.delete("/:id", (0, route_helpers_js_1.wrapController)(jobController_js_1.deleteJob));
router.patch("/:id/publish", (0, route_helpers_js_1.wrapController)(jobController_js_1.publishJob));
router.patch("/:id/close", (0, route_helpers_js_1.wrapController)(jobController_js_1.closeJob));
exports.default = router;
//# sourceMappingURL=jobs.js.map