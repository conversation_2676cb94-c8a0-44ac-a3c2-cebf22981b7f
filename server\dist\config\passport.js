"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// Use CommonJS-style imports with esModuleInterop
const passport_1 = __importDefault(require("passport"));
const passport_jwt_1 = require("passport-jwt");
const prisma_1 = require("../lib/prisma");
const JWT_SECRET = process.env.JWT_SECRET || "your_secret_key";
const options = {
    jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
    secretOrKey: JWT_SECRET,
};
passport_1.default.use(new passport_jwt_1.Strategy(options, async (jwt_payload, done) => {
    try {
        const user = await prisma_1.prisma.user.findUnique({
            where: { id: jwt_payload.userId },
        });
        if (user) {
            return done(null, user);
        }
        return done(null, false);
    }
    catch (error) {
        return done(error, false);
    }
}));
// Required for sessions, even if we're not using them
passport_1.default.serializeUser((user, done) => {
    done(null, user.id);
});
passport_1.default.deserializeUser(async (id, done) => {
    try {
        const user = await prisma_1.prisma.user.findUnique({
            where: { id },
        });
        // Add roles and companyId to match User interface
        const userWithExtras = user
            ? {
                ...user,
                roles: [],
                companyId: 0, // Use 0 instead of null to match the expected number type
            }
            : null;
        done(null, userWithExtras);
    }
    catch (error) {
        done(error, null);
    }
});
exports.default = passport_1.default;
//# sourceMappingURL=passport.js.map