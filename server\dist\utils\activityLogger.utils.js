"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logIntegrationChange = exports.logSettingsChange = exports.logPaymentOperation = exports.logDealOperation = exports.logProjectOperation = exports.logEmailOperation = exports.logFileOperation = exports.logDataOperation = void 0;
const activityLogger_js_1 = require("../services/activityLogger.js");
// Utility functions for common activity logging patterns
const logDataOperation = async (operation, entity, entityId, module, req, userId, changes, customTitle, customDescription) => {
    const titles = {
        CREATE: customTitle || `${entity.charAt(0).toUpperCase() + entity.slice(1)} Created`,
        UPDATE: customTitle || `${entity.charAt(0).toUpperCase() + entity.slice(1)} Updated`,
        DELETE: customTitle || `${entity.charAt(0).toUpperCase() + entity.slice(1)} Deleted`,
    };
    const descriptions = {
        CREATE: customDescription || `A new ${entity} was created`,
        UPDATE: customDescription || `An existing ${entity} was updated`,
        DELETE: customDescription || `A ${entity} was deleted`,
    };
    await activityLogger_js_1.ActivityLogger.logDataOperation(operation, entity, entityId.toString(), titles[operation], descriptions[operation], req, userId, changes);
};
exports.logDataOperation = logDataOperation;
const logFileOperation = async (operation, fileName, module, req, userId, metadata) => {
    const type = operation === "UPLOAD" ? "FILE_UPLOAD" :
        operation === "DOWNLOAD" ? "FILE_DOWNLOAD" : "FILE_DELETE";
    const titles = {
        UPLOAD: `File Uploaded: ${fileName}`,
        DOWNLOAD: `File Downloaded: ${fileName}`,
        DELETE: `File Deleted: ${fileName}`,
    };
    const descriptions = {
        UPLOAD: `File "${fileName}" was uploaded to the system`,
        DOWNLOAD: `File "${fileName}" was downloaded from the system`,
        DELETE: `File "${fileName}" was deleted from the system`,
    };
    await activityLogger_js_1.ActivityLogger.logFromRequest(req, {
        type,
        action: operation.toLowerCase(),
        module,
        entity: "file",
        entityId: fileName,
        title: titles[operation],
        description: descriptions[operation],
        userId,
        status: "SUCCESS",
        severity: "INFO",
        metadata,
    });
};
exports.logFileOperation = logFileOperation;
const logEmailOperation = async (operation, subject, recipient, req, userId, metadata) => {
    const titles = {
        SENT: `Email Sent: ${subject}`,
        RECEIVED: `Email Received: ${subject}`,
        DELETED: `Email Deleted: ${subject}`,
    };
    const descriptions = {
        SENT: `Email "${subject}" was sent to ${recipient}`,
        RECEIVED: `Email "${subject}" was received from ${recipient}`,
        DELETED: `Email "${subject}" was deleted`,
    };
    await activityLogger_js_1.ActivityLogger.logFromRequest(req, {
        type: "EMAIL_SENT",
        action: operation.toLowerCase(),
        module: "communication",
        entity: "email",
        title: titles[operation],
        description: descriptions[operation],
        userId,
        status: "SUCCESS",
        severity: "INFO",
        metadata: {
            ...metadata,
            subject,
            recipient,
        },
    });
};
exports.logEmailOperation = logEmailOperation;
const logProjectOperation = async (operation, projectName, projectId, req, userId, changes) => {
    const type = operation === "CREATE" ? "PROJECT_CREATE" : "PROJECT_UPDATE";
    const titles = {
        CREATE: `Project Created: ${projectName}`,
        UPDATE: `Project Updated: ${projectName}`,
        DELETE: `Project Deleted: ${projectName}`,
        COMPLETE: `Project Completed: ${projectName}`,
    };
    const descriptions = {
        CREATE: `New project "${projectName}" was created`,
        UPDATE: `Project "${projectName}" was updated`,
        DELETE: `Project "${projectName}" was deleted`,
        COMPLETE: `Project "${projectName}" was marked as completed`,
    };
    await activityLogger_js_1.ActivityLogger.logFromRequest(req, {
        type,
        action: operation.toLowerCase(),
        module: "project_management",
        entity: "project",
        entityId: projectId.toString(),
        title: titles[operation],
        description: descriptions[operation],
        userId,
        status: "SUCCESS",
        severity: "INFO",
        changes,
    });
};
exports.logProjectOperation = logProjectOperation;
const logDealOperation = async (operation, dealName, dealId, req, userId, changes) => {
    const type = operation === "CREATE" ? "DEAL_CREATE" : "DEAL_UPDATE";
    const titles = {
        CREATE: `Deal Created: ${dealName}`,
        UPDATE: `Deal Updated: ${dealName}`,
        DELETE: `Deal Deleted: ${dealName}`,
        WON: `Deal Won: ${dealName}`,
        LOST: `Deal Lost: ${dealName}`,
    };
    const descriptions = {
        CREATE: `New deal "${dealName}" was created`,
        UPDATE: `Deal "${dealName}" was updated`,
        DELETE: `Deal "${dealName}" was deleted`,
        WON: `Deal "${dealName}" was marked as won`,
        LOST: `Deal "${dealName}" was marked as lost`,
    };
    await activityLogger_js_1.ActivityLogger.logFromRequest(req, {
        type,
        action: operation.toLowerCase(),
        module: "sales",
        entity: "deal",
        entityId: dealId.toString(),
        title: titles[operation],
        description: descriptions[operation],
        userId,
        status: "SUCCESS",
        severity: "INFO",
        changes,
    });
};
exports.logDealOperation = logDealOperation;
const logPaymentOperation = async (operation, amount, currency, req, userId, metadata) => {
    const titles = {
        PROCESSED: `Payment Processed: ${amount} ${currency}`,
        FAILED: `Payment Failed: ${amount} ${currency}`,
        REFUNDED: `Payment Refunded: ${amount} ${currency}`,
    };
    const descriptions = {
        PROCESSED: `Payment of ${amount} ${currency} was successfully processed`,
        FAILED: `Payment of ${amount} ${currency} failed to process`,
        REFUNDED: `Payment of ${amount} ${currency} was refunded`,
    };
    await activityLogger_js_1.ActivityLogger.logFromRequest(req, {
        type: "PAYMENT_PROCESSED",
        action: operation.toLowerCase(),
        module: "payment",
        entity: "payment",
        title: titles[operation],
        description: descriptions[operation],
        userId,
        status: operation === "FAILED" ? "FAILED" : "SUCCESS",
        severity: operation === "FAILED" ? "ERROR" : "INFO",
        metadata: {
            ...metadata,
            amount,
            currency,
        },
    });
};
exports.logPaymentOperation = logPaymentOperation;
const logSettingsChange = async (settingName, oldValue, newValue, module, req, userId) => {
    await activityLogger_js_1.ActivityLogger.logFromRequest(req, {
        type: "SETTINGS_UPDATE",
        action: "update",
        module,
        entity: "settings",
        entityId: settingName,
        title: `Settings Updated: ${settingName}`,
        description: `Setting "${settingName}" was changed`,
        userId,
        status: "SUCCESS",
        severity: "INFO",
        changes: {
            [settingName]: {
                old: oldValue,
                new: newValue,
            },
        },
    });
};
exports.logSettingsChange = logSettingsChange;
const logIntegrationChange = async (integrationName, action, req, userId, metadata) => {
    const type = action === "ENABLED" ? "INTEGRATION_ENABLED" : "INTEGRATION_DISABLED";
    const titles = {
        ENABLED: `Integration Enabled: ${integrationName}`,
        DISABLED: `Integration Disabled: ${integrationName}`,
        CONFIGURED: `Integration Configured: ${integrationName}`,
    };
    const descriptions = {
        ENABLED: `Integration "${integrationName}" was enabled`,
        DISABLED: `Integration "${integrationName}" was disabled`,
        CONFIGURED: `Integration "${integrationName}" was configured`,
    };
    await activityLogger_js_1.ActivityLogger.logFromRequest(req, {
        type,
        action: action.toLowerCase(),
        module: "integrations",
        entity: "integration",
        entityId: integrationName,
        title: titles[action],
        description: descriptions[action],
        userId,
        status: "SUCCESS",
        severity: "INFO",
        metadata,
    });
};
exports.logIntegrationChange = logIntegrationChange;
//# sourceMappingURL=activityLogger.utils.js.map