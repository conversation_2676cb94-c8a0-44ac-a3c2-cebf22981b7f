{"version": 3, "file": "authenticateToken.js", "sourceRoot": "", "sources": ["../../middleware/authenticateToken.ts"], "names": [], "mappings": ";AAAA,0CAA0C;;;;;;AAG1C,gEAA+B;AAC/B,8DAAsC;AACtC,gDAA0C;AAE1C,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,iBAAiB,CAAC;AAE/D,iDAAiD;AAE1C,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAY,EACZ,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,MAAM,UAAU,GACd,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,IAAK,GAAG,CAAC,KAAK,CAAC,KAAgB,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;IACxC,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;IAEvC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,IAAA,qBAAW,EAAC,GAAG,EAAE,cAAc,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CACT,kCAAkC,EAClC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CACnC,CAAC;QACF,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAmB,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACvC,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;QAEzC,MAAM,MAAM,GAAG,MAAM,kBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,OAAO,EAAE;gBACP,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;aAChB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC,IAAA,qBAAW,EAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC,CAAC;QAClD,CAAC;QAED,wCAAwC;QACxC,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAE1C,oCAAoC;QACpC,+DAA+D;QAC/D,MAAM,YAAY,GAAG,MAAM,CAAC,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACrE,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QAE1E,gCAAgC;QAChC,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAClD,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;QAED,wCAAwC;QACxC,MAAM,SAAS,GACb,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAElE,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,SAAS,EAAE,SAAS;SACrB,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,IAAI,CAAC,IAAA,qBAAW,EAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC;IACtC,CAAC;AACH,CAAC,CAAC;AAtEW,QAAA,iBAAiB,qBAsE5B"}