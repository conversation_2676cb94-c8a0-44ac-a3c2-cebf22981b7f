"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.scheduleSubscriptionCheck = scheduleSubscriptionCheck;
exports.checkSubscriptions = checkSubscriptions;
const client_1 = require("@prisma/client");
const node_cron_1 = __importDefault(require("node-cron"));
const prisma = new client_1.PrismaClient();
/**
 * Check for expired subscriptions and update their status
 */
async function checkSubscriptions() {
    console.log('Running subscription status check...');
    try {
        const now = new Date();
        // Find trial subscriptions that have expired
        const expiredTrials = await prisma.subscription.findMany({
            where: {
                status: client_1.SubscriptionStatus.TRIAL,
                trialEndDate: {
                    lt: now
                }
            }
        });
        if (expiredTrials.length > 0) {
            console.log(`Found ${expiredTrials.length} expired trial subscriptions`);
            // Update status to EXPIRED
            await prisma.subscription.updateMany({
                where: {
                    id: {
                        in: expiredTrials.map(sub => sub.id)
                    }
                },
                data: {
                    status: client_1.SubscriptionStatus.EXPIRED
                }
            });
        }
        // Find active subscriptions that have passed their next billing date
        const expiredSubscriptions = await prisma.subscription.findMany({
            where: {
                status: client_1.SubscriptionStatus.ACTIVE,
                nextBillingDate: {
                    lt: now
                },
                cancelAtPeriodEnd: true
            }
        });
        if (expiredSubscriptions.length > 0) {
            console.log(`Found ${expiredSubscriptions.length} expired active subscriptions`);
            // Update status to CANCELLED
            await prisma.subscription.updateMany({
                where: {
                    id: {
                        in: expiredSubscriptions.map(sub => sub.id)
                    }
                },
                data: {
                    status: client_1.SubscriptionStatus.CANCELLED,
                    endDate: now
                }
            });
        }
        console.log('Subscription status check completed successfully');
    }
    catch (error) {
        console.error('Error checking subscriptions:', error);
    }
}
// Schedule the job to run every day at midnight
function scheduleSubscriptionCheck() {
    node_cron_1.default.schedule('0 0 * * *', checkSubscriptions);
    console.log('Subscription check scheduled to run daily at midnight');
}
// Run immediately on startup
checkSubscriptions();
//# sourceMappingURL=check-subscriptions.js.map