"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadDocument = exports.uploadCoverImage = exports.uploadCompanyCover = exports.uploadCompanyLogo = exports.uploadAvatar = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const uuid_1 = require("uuid");
const client_1 = require("@prisma/client");
// Use require for multer since we need to call it as a function
const multer = require("multer");
// Using centralized prisma instance from lib/prisma.js
// Helper function to validate document type
function validateDocumentType(type) {
    if (!type)
        return undefined;
    // Check if the type is a valid DocumentType enum value
    const upperType = type.toUpperCase();
    if (Object.values(client_1.DocumentType).includes(upperType)) {
        return upperType;
    }
    return undefined;
}
// Ensure upload directories exist
const uploadDir = path.join(process.cwd(), "public", "uploads");
const avatarDir = path.join(uploadDir, "avatars");
const userCoverDir = path.join(uploadDir, "user-covers");
const documentsDir = path.join(uploadDir, "documents");
const companyLogosDir = path.join(uploadDir, "company-logos");
const companyCoverDir = path.join(uploadDir, "company-covers");
// Create directories if they don't exist
[
    uploadDir,
    avatarDir,
    userCoverDir,
    documentsDir,
    companyLogosDir,
    companyCoverDir,
].forEach((dir) => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
});
// Configure storage for different file types
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const fileType = req.params.type || "documents";
        let destinationDir = documentsDir;
        if (fileType === "avatar") {
            destinationDir = avatarDir;
        }
        else if (fileType === "user-cover") {
            destinationDir = userCoverDir;
        }
        else if (fileType === "company-logo") {
            destinationDir = companyLogosDir;
        }
        else if (fileType === "company-cover") {
            destinationDir = companyCoverDir;
        }
        cb(null, destinationDir);
    },
    filename: (req, file, cb) => {
        const uniqueId = (0, uuid_1.v4)();
        const fileExtension = path.extname(file.originalname);
        const fileName = `${uniqueId}${fileExtension}`;
        cb(null, fileName);
    },
});
// File filter to restrict file types
const fileFilter = (req, file, cb) => {
    const fileType = req.params.type || "documents";
    if (fileType === "avatar" ||
        fileType === "company-logo" ||
        fileType === "company-cover") {
        // Only allow images for avatars and company logos/covers
        if (!file.mimetype.startsWith("image/")) {
            return cb(new Error("Only image files are allowed for avatars and logos"));
        }
    }
    else {
        // For documents, allow common document types
        const allowedMimeTypes = [
            "image/jpeg",
            "image/png",
            "image/gif",
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
        ];
        if (!allowedMimeTypes.includes(file.mimetype)) {
            return cb(new Error("File type not allowed"));
        }
    }
    cb(null, true);
};
// Configure multer upload
const upload = multer({
    storage,
    fileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
});
// Upload avatar
const uploadAvatar = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Override the destination to ensure it goes to avatars directory
        req.params.type = "avatar";
        // Use multer to handle the file upload
        upload.single("file")(req, res, async (err) => {
            if (err) {
                console.error("Error uploading avatar:", err);
                return res.status(400).json({ error: err.message });
            }
            const reqWithFile = req;
            if (!reqWithFile.file) {
                return res.status(400).json({ error: "No file uploaded" });
            }
            // Generate the URL for the uploaded file
            const baseUrl = process.env.SERVER_URL || "http://localhost:4004";
            const avatarUrl = `${baseUrl}/uploads/avatars/${reqWithFile.file.filename}`;
            // Update the user's avatar in the database
            await prisma_js_1.prisma.user.update({
                where: { id: Number(userId) },
                data: { avatar: avatarUrl },
            });
            res.status(200).json({ url: avatarUrl });
        });
    }
    catch (error) {
        console.error("Error in uploadAvatar:", error);
        res.status(500).json({ error: "Failed to upload avatar" });
    }
};
exports.uploadAvatar = uploadAvatar;
// Upload company logo
const uploadCompanyLogo = async (req, res) => {
    try {
        const { companyId } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Check if user has permission to update this company
        const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
            where: {
                userId: Number(userId),
                companyId: Number(companyId),
            },
        });
        if (!userCompany) {
            return res
                .status(403)
                .json({ error: "You don't have permission to update this company" });
        }
        // Override the destination to ensure it goes to company-logos directory
        req.params.type = "company-logo";
        // Use multer to handle the file upload
        upload.single("file")(req, res, async (err) => {
            if (err) {
                console.error("Error uploading company logo:", err);
                return res.status(400).json({ error: err.message });
            }
            const reqWithFile = req;
            if (!reqWithFile.file) {
                return res.status(400).json({ error: "No file uploaded" });
            }
            // Generate the URL for the uploaded file
            const baseUrl = process.env.SERVER_URL || "http://localhost:4004";
            const logoUrl = `${baseUrl}/uploads/company-logos/${reqWithFile.file.filename}`;
            // Update the company's logo in the database
            await prisma_js_1.prisma.company.update({
                where: { id: Number(companyId) },
                data: { logo: logoUrl },
            });
            res.status(200).json({ url: logoUrl });
        });
    }
    catch (error) {
        console.error("Error in uploadCompanyLogo:", error);
        res.status(500).json({ error: "Failed to upload company logo" });
    }
};
exports.uploadCompanyLogo = uploadCompanyLogo;
// Upload company cover image
const uploadCompanyCover = async (req, res) => {
    try {
        const { companyId } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Check if user has permission to update this company
        const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
            where: {
                userId: Number(userId),
                companyId: Number(companyId),
            },
        });
        if (!userCompany) {
            return res
                .status(403)
                .json({ error: "You don't have permission to update this company" });
        }
        // Override the destination to ensure it goes to company-covers directory
        req.params.type = "company-cover";
        // Use multer to handle the file upload
        upload.single("file")(req, res, async (err) => {
            if (err) {
                console.error("Error uploading company cover:", err);
                return res.status(400).json({ error: err.message });
            }
            const reqWithFile = req;
            if (!reqWithFile.file) {
                return res.status(400).json({ error: "No file uploaded" });
            }
            // Generate the URL for the uploaded file
            const baseUrl = process.env.SERVER_URL || "http://localhost:4004";
            const coverUrl = `${baseUrl}/uploads/company-covers/${reqWithFile.file.filename}`;
            // Update the company's cover image in the database
            await prisma_js_1.prisma.company.update({
                where: { id: Number(companyId) },
                data: { coverImage: coverUrl },
            });
            res.status(200).json({ url: coverUrl });
        });
    }
    catch (error) {
        console.error("Error in uploadCompanyCover:", error);
        res.status(500).json({ error: "Failed to upload company cover" });
    }
};
exports.uploadCompanyCover = uploadCompanyCover;
// Upload user cover image
const uploadCoverImage = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Override the destination to ensure it goes to user-covers directory
        req.params.type = "user-cover";
        // Use multer to handle the file upload
        upload.single("file")(req, res, async (err) => {
            if (err) {
                console.error("Error uploading cover image:", err);
                return res.status(400).json({ error: err.message });
            }
            const reqWithFile = req;
            if (!reqWithFile.file) {
                return res.status(400).json({ error: "No file uploaded" });
            }
            // Generate the URL for the uploaded file
            const baseUrl = process.env.SERVER_URL || "http://localhost:4004";
            const coverUrl = `${baseUrl}/uploads/user-covers/${reqWithFile.file.filename}`;
            // Update the user's cover image in the database
            await prisma_js_1.prisma.user.update({
                where: { id: Number(userId) },
                data: { coverImage: coverUrl },
            });
            res.status(200).json({ url: coverUrl });
        });
    }
    catch (error) {
        console.error("Error in uploadCoverImage:", error);
        res.status(500).json({ error: "Failed to upload cover image" });
    }
};
exports.uploadCoverImage = uploadCoverImage;
// Upload document
const uploadDocument = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { type } = req.params;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Use multer to handle the file upload
        upload.single("file")(req, res, async (err) => {
            if (err) {
                console.error("Error uploading document:", err);
                return res.status(400).json({ error: err.message });
            }
            const reqWithFile = req;
            if (!reqWithFile.file) {
                return res.status(400).json({ error: "No file uploaded" });
            }
            // Generate the URL for the uploaded file
            const baseUrl = process.env.SERVER_URL || "http://localhost:4004";
            const documentUrl = `${baseUrl}/uploads/documents/${reqWithFile.file.filename}`;
            // Create a document record in the database
            const document = await prisma_js_1.prisma.document.create({
                data: {
                    name: reqWithFile.file.originalname,
                    url: documentUrl,
                    // Convert the type string to a valid DocumentType enum value
                    type: validateDocumentType(type) || client_1.DocumentType.REPORT,
                    size: reqWithFile.file.size,
                    mimeType: reqWithFile.file.mimetype,
                    uploadedBy: Number(userId),
                    companyId: Number(req.user?.companyId) || 1, // Use the user's company ID or default to 1
                },
            });
            res.status(200).json({
                url: documentUrl,
                document,
            });
        });
    }
    catch (error) {
        console.error("Error in uploadDocument:", error);
        res.status(500).json({ error: "Failed to upload document" });
    }
};
exports.uploadDocument = uploadDocument;
//# sourceMappingURL=upload.controller.js.map