"use strict";
// server/utils/route-helpers.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.auth = void 0;
exports.wrapHandler = wrapHandler;
exports.wrapController = wrapController;
exports.createHandler = createHandler;
const authenticateToken_js_1 = require("../middleware/authenticateToken.js");
/**
 * A type-safe wrapper for the authenticateToken middleware
 * This resolves the TypeScript errors with Express type definitions
 */
exports.auth = authenticateToken_js_1.authenticateToken;
/**
 * A type-safe wrapper for any middleware function
 * Use this for any middleware that's causing TypeScript errors
 */
function wrapHandler(handler) {
    return handler;
}
/**
 * A type-safe wrapper for controller functions
 * Use this for any controller function that's causing TypeScript errors
 */
function wrapController(controller) {
    return controller;
}
/**
 * A utility function to create a route handler with proper typing
 */
function createHandler(handler) {
    return handler;
}
//# sourceMappingURL=route-helpers.js.map