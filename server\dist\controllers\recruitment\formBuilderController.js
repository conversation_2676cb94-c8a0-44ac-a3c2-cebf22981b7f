"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWorkerFromSubmission = exports.updateSubmissionStatus = exports.getSubmissionById = exports.getFormSubmissions = exports.submitForm = exports.deleteOption = exports.updateOption = exports.addOption = exports.deleteField = exports.updateField = exports.addField = exports.deleteStep = exports.updateStep = exports.addStepToForm = exports.deleteForm = exports.updateForm = exports.createForm = exports.getFormBySlug = exports.getFormById = exports.getAllForms = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const slugify_1 = __importDefault(require("slugify"));
const uuid_1 = require("uuid");
// Using centralized prisma instance from lib/prisma.js
// Get all forms
const getAllForms = async (req, res) => {
    try {
        const forms = await prisma_js_1.prisma.recruitmentForm.findMany({
            orderBy: {
                updatedAt: "desc",
            },
            include: {
                steps: {
                    orderBy: {
                        order: "asc",
                    },
                },
                _count: {
                    select: {
                        submissions: true,
                    },
                },
            },
        });
        return res.status(200).json(forms);
    }
    catch (error) {
        console.error("Error fetching forms:", error);
        return res.status(500).json({ error: "Failed to fetch forms" });
    }
};
exports.getAllForms = getAllForms;
// Get form by ID
const getFormById = async (req, res) => {
    const { id } = req.params;
    try {
        const form = await prisma_js_1.prisma.recruitmentForm.findUnique({
            where: { id: Number(id) },
            include: {
                steps: {
                    orderBy: {
                        order: "asc",
                    },
                    include: {
                        fields: {
                            orderBy: {
                                order: "asc",
                            },
                            include: {
                                options: {
                                    orderBy: {
                                        order: "asc",
                                    },
                                },
                            },
                        },
                    },
                },
                fields: {
                    where: {
                        stepId: null,
                    },
                    orderBy: {
                        order: "asc",
                    },
                    include: {
                        options: {
                            orderBy: {
                                order: "asc",
                            },
                        },
                    },
                },
            },
        });
        if (!form) {
            return res.status(404).json({ error: "Form not found" });
        }
        return res.status(200).json(form);
    }
    catch (error) {
        console.error("Error fetching form:", error);
        return res.status(500).json({ error: "Failed to fetch form" });
    }
};
exports.getFormById = getFormById;
// Get form by slug (public access)
const getFormBySlug = async (req, res) => {
    const { slug } = req.params;
    try {
        const form = await prisma_js_1.prisma.recruitmentForm.findUnique({
            where: {
                slug,
                isPublic: true,
                status: "published",
            },
            include: {
                steps: {
                    where: {
                        isEnabled: true,
                    },
                    orderBy: {
                        order: "asc",
                    },
                    include: {
                        fields: {
                            where: {
                                isEnabled: true,
                                isHidden: false,
                            },
                            orderBy: {
                                order: "asc",
                            },
                            include: {
                                options: {
                                    orderBy: {
                                        order: "asc",
                                    },
                                },
                            },
                        },
                    },
                },
                fields: {
                    where: {
                        stepId: null,
                        isEnabled: true,
                        isHidden: false,
                    },
                    orderBy: {
                        order: "asc",
                    },
                    include: {
                        options: {
                            orderBy: {
                                order: "asc",
                            },
                        },
                    },
                },
            },
        });
        if (!form) {
            return res.status(404).json({ error: "Form not found" });
        }
        // Increment view count
        await prisma_js_1.prisma.recruitmentForm.update({
            where: { id: Number(form.id) },
            data: {
                viewCount: { increment: 1 },
            },
        });
        return res.status(200).json(form);
    }
    catch (error) {
        console.error("Error fetching form by slug:", error);
        return res.status(500).json({ error: "Failed to fetch form" });
    }
};
exports.getFormBySlug = getFormBySlug;
// Create form
const createForm = async (req, res) => {
    const { name, description, isPublic, allowAnonymous, notifyEmails, redirectUrl, thankYouMessage, steps, fields, } = req.body;
    try {
        // Generate slug from name
        let slug = (0, slugify_1.default)(name, { lower: true, strict: true });
        // Check if slug already exists
        const existingForm = await prisma_js_1.prisma.recruitmentForm.findUnique({
            where: { slug },
        });
        // If slug exists, append a random string
        if (existingForm) {
            slug = `${slug}-${(0, uuid_1.v4)().substring(0, 8)}`;
        }
        // Create form
        const form = await prisma_js_1.prisma.recruitmentForm.create({
            data: {
                name,
                description,
                slug,
                isPublic: isPublic || false,
                allowAnonymous: allowAnonymous || false,
                notifyEmails: notifyEmails || [],
                redirectUrl,
                thankYouMessage,
                createdBy: req.user.id,
                steps: {
                    create: steps?.map((step, index) => ({
                        title: step.title,
                        description: step.description,
                        order: index + 1,
                        isEnabled: step.isEnabled !== undefined ? step.isEnabled : true,
                        translations: step.translations,
                    })) || [],
                },
                fields: {
                    create: fields?.map((field, index) => ({
                        type: field.type,
                        name: field.name,
                        label: field.label,
                        placeholder: field.placeholder,
                        helpText: field.helpText,
                        defaultValue: field.defaultValue,
                        order: index + 1,
                        isRequired: field.isRequired || false,
                        isEnabled: field.isEnabled !== undefined ? field.isEnabled : true,
                        isHidden: field.isHidden || false,
                        validations: field.validations,
                        conditionalLogic: field.conditionalLogic,
                        translations: field.translations,
                        options: field.options
                            ? {
                                create: field.options.map((option, optIndex) => ({
                                    label: option.label,
                                    value: option.value,
                                    order: optIndex + 1,
                                    isDefault: option.isDefault || false,
                                    translations: option.translations,
                                })),
                            }
                            : undefined,
                    })) || [],
                },
            },
            include: {
                steps: true,
                fields: {
                    include: {
                        options: true,
                    },
                },
            },
        });
        return res.status(201).json(form);
    }
    catch (error) {
        console.error("Error creating form:", error);
        return res.status(500).json({ error: "Failed to create form" });
    }
};
exports.createForm = createForm;
// Update form
const updateForm = async (req, res) => {
    const { id } = req.params;
    const { name, description, isPublic, allowAnonymous, notifyEmails, redirectUrl, thankYouMessage, status, } = req.body;
    try {
        // Check if form exists
        const existingForm = await prisma_js_1.prisma.recruitmentForm.findUnique({
            where: { id: Number(id) },
        });
        if (!existingForm) {
            return res.status(404).json({ error: "Form not found" });
        }
        // Update form
        const form = await prisma_js_1.prisma.recruitmentForm.update({
            where: { id: Number(id) },
            data: {
                name,
                description,
                isPublic,
                allowAnonymous,
                notifyEmails,
                redirectUrl,
                thankYouMessage,
                status,
                updatedBy: req.user.id,
                publishedAt: status === "published" && existingForm.status !== "published"
                    ? new Date()
                    : existingForm.publishedAt,
            },
        });
        return res.status(200).json(form);
    }
    catch (error) {
        console.error("Error updating form:", error);
        return res.status(500).json({ error: "Failed to update form" });
    }
};
exports.updateForm = updateForm;
// Delete form
const deleteForm = async (req, res) => {
    const { id } = req.params;
    try {
        // Check if form exists
        const existingForm = await prisma_js_1.prisma.recruitmentForm.findUnique({
            where: { id: Number(id) },
        });
        if (!existingForm) {
            return res.status(404).json({ error: "Form not found" });
        }
        // Delete form
        await prisma_js_1.prisma.recruitmentForm.delete({
            where: { id: Number(id) },
        });
        return res.status(200).json({ message: "Form deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting form:", error);
        return res.status(500).json({ error: "Failed to delete form" });
    }
};
exports.deleteForm = deleteForm;
// Add step to form
const addStepToForm = async (req, res) => {
    const { id } = req.params;
    const { title, description, isEnabled, translations } = req.body;
    try {
        // Check if form exists
        const existingForm = await prisma_js_1.prisma.recruitmentForm.findUnique({
            where: { id: Number(id) },
            include: {
                steps: {
                    orderBy: {
                        order: "asc",
                    },
                },
            },
        });
        if (!existingForm) {
            return res.status(404).json({ error: "Form not found" });
        }
        // Calculate next order
        const steps = existingForm.steps || [];
        const nextOrder = steps.length > 0 ? Math.max(...steps.map((step) => step.order)) + 1 : 1;
        // Add step
        const step = await prisma_js_1.prisma.formStep.create({
            data: {
                formId: Number(id),
                title,
                description,
                order: nextOrder,
                isEnabled: isEnabled !== undefined ? isEnabled : true,
                translations,
            },
        });
        return res.status(201).json(step);
    }
    catch (error) {
        console.error("Error adding step to form:", error);
        return res.status(500).json({ error: "Failed to add step to form" });
    }
};
exports.addStepToForm = addStepToForm;
// Update step
const updateStep = async (req, res) => {
    const { id, stepId } = req.params;
    const { title, description, order, isEnabled, translations } = req.body;
    try {
        // Check if step exists
        const existingStep = await prisma_js_1.prisma.formStep.findFirst({
            where: {
                id: Number(stepId),
                formId: Number(id),
            },
        });
        if (!existingStep) {
            return res.status(404).json({ error: "Step not found" });
        }
        // Update step
        const step = await prisma_js_1.prisma.formStep.update({
            where: { id: Number(stepId) },
            data: {
                title,
                description,
                order,
                isEnabled,
                translations,
            },
        });
        return res.status(200).json(step);
    }
    catch (error) {
        console.error("Error updating step:", error);
        return res.status(500).json({ error: "Failed to update step" });
    }
};
exports.updateStep = updateStep;
// Delete step
const deleteStep = async (req, res) => {
    const { id, stepId } = req.params;
    try {
        // Check if step exists
        const existingStep = await prisma_js_1.prisma.formStep.findFirst({
            where: {
                id: Number(stepId),
                formId: Number(id),
            },
        });
        if (!existingStep) {
            return res.status(404).json({ error: "Step not found" });
        }
        // Delete step
        await prisma_js_1.prisma.formStep.delete({
            where: { id: Number(stepId) },
        });
        return res.status(200).json({ message: "Step deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting step:", error);
        return res.status(500).json({ error: "Failed to delete step" });
    }
};
exports.deleteStep = deleteStep;
// Add field to form or step
const addField = async (req, res) => {
    const { id, stepId } = req.params;
    const { type, name, label, placeholder, helpText, defaultValue, isRequired, isEnabled, isHidden, validations, conditionalLogic, translations, options, } = req.body;
    try {
        // Check if form exists
        const existingForm = await prisma_js_1.prisma.recruitmentForm.findUnique({
            where: { id: Number(id) },
        });
        if (!existingForm) {
            return res.status(404).json({ error: "Form not found" });
        }
        // If stepId is provided, check if step exists
        if (stepId) {
            const existingStep = await prisma_js_1.prisma.formStep.findFirst({
                where: {
                    id: Number(stepId),
                    formId: Number(id),
                },
            });
            if (!existingStep) {
                return res.status(404).json({ error: "Step not found" });
            }
        }
        // Get existing fields to calculate next order
        const existingFields = await prisma_js_1.prisma.formField.findMany({
            where: {
                formId: Number(id),
                stepId: stepId ? Number(stepId) : null,
            },
            orderBy: {
                order: "asc",
            },
        });
        const nextOrder = existingFields.length > 0
            ? Math.max(...existingFields.map((field) => field.order)) + 1
            : 1;
        // Add field
        const field = await prisma_js_1.prisma.formField.create({
            data: {
                formId: Number(id),
                stepId: stepId ? Number(stepId) : null,
                type,
                name: name || label.toLowerCase().replace(/[^a-z0-9]/g, "_"),
                label,
                placeholder,
                helpText,
                defaultValue,
                order: nextOrder,
                isRequired: isRequired || false,
                isEnabled: isEnabled !== undefined ? isEnabled : true,
                isHidden: isHidden || false,
                validations,
                conditionalLogic,
                translations,
                options: options
                    ? {
                        create: options.map((option, index) => ({
                            label: option.label,
                            value: option.value ||
                                option.label.toLowerCase().replace(/[^a-z0-9]/g, "_"),
                            order: index + 1,
                            isDefault: option.isDefault || false,
                            translations: option.translations,
                        })),
                    }
                    : undefined,
            },
            include: {
                options: true,
            },
        });
        return res.status(201).json(field);
    }
    catch (error) {
        console.error("Error adding field:", error);
        return res.status(500).json({ error: "Failed to add field" });
    }
};
exports.addField = addField;
// Update field
const updateField = async (req, res) => {
    const { id, fieldId } = req.params;
    const { type, name, label, placeholder, helpText, defaultValue, order, isRequired, isEnabled, isHidden, validations, conditionalLogic, translations, stepId, } = req.body;
    try {
        // Check if field exists
        const existingField = await prisma_js_1.prisma.formField.findFirst({
            where: {
                id: Number(fieldId),
                formId: Number(id),
            },
        });
        if (!existingField) {
            return res.status(404).json({ error: "Field not found" });
        }
        // If stepId is provided, check if step exists
        if (stepId) {
            const existingStep = await prisma_js_1.prisma.formStep.findFirst({
                where: {
                    id: Number(stepId),
                    formId: Number(id),
                },
            });
            if (!existingStep) {
                return res.status(404).json({ error: "Step not found" });
            }
        }
        // Update field
        const field = await prisma_js_1.prisma.formField.update({
            where: { id: Number(fieldId) },
            data: {
                type,
                name,
                label,
                placeholder,
                helpText,
                defaultValue,
                order,
                isRequired,
                isEnabled,
                isHidden,
                validations,
                conditionalLogic,
                translations,
                stepId: stepId ? Number(stepId) : null,
            },
        });
        return res.status(200).json(field);
    }
    catch (error) {
        console.error("Error updating field:", error);
        return res.status(500).json({ error: "Failed to update field" });
    }
};
exports.updateField = updateField;
// Delete field
const deleteField = async (req, res) => {
    const { id, fieldId } = req.params;
    try {
        // Check if field exists
        const existingField = await prisma_js_1.prisma.formField.findFirst({
            where: {
                id: Number(fieldId),
                formId: Number(id),
            },
        });
        if (!existingField) {
            return res.status(404).json({ error: "Field not found" });
        }
        // Delete field
        await prisma_js_1.prisma.formField.delete({
            where: { id: Number(fieldId) },
        });
        return res.status(200).json({ message: "Field deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting field:", error);
        return res.status(500).json({ error: "Failed to delete field" });
    }
};
exports.deleteField = deleteField;
// Add option to field
const addOption = async (req, res) => {
    const { id, fieldId } = req.params;
    const { label, value, isDefault, translations } = req.body;
    try {
        // Check if field exists
        const existingField = await prisma_js_1.prisma.formField.findFirst({
            where: {
                id: Number(fieldId),
                formId: Number(id),
            },
            include: {
                options: {
                    orderBy: {
                        order: "asc",
                    },
                },
            },
        });
        if (!existingField) {
            return res.status(404).json({ error: "Field not found" });
        }
        // Calculate next order
        const options = existingField.options || [];
        const nextOrder = options.length > 0
            ? Math.max(...options.map((option) => option.order)) + 1
            : 1;
        // Add option
        const option = await prisma_js_1.prisma.fieldOption.create({
            data: {
                fieldId: Number(fieldId),
                label,
                value: value || label.toLowerCase().replace(/[^a-z0-9]/g, "_"),
                order: nextOrder,
                isDefault: isDefault || false,
                translations,
            },
        });
        return res.status(201).json(option);
    }
    catch (error) {
        console.error("Error adding option:", error);
        return res.status(500).json({ error: "Failed to add option" });
    }
};
exports.addOption = addOption;
// Update option
const updateOption = async (req, res) => {
    const { id, fieldId, optionId } = req.params;
    const { label, value, order, isDefault, translations } = req.body;
    try {
        // Check if option exists
        const existingOption = await prisma_js_1.prisma.fieldOption.findFirst({
            where: {
                id: Number(optionId),
                fieldId: Number(fieldId),
                field: {
                    formId: Number(id),
                },
            },
        });
        if (!existingOption) {
            return res.status(404).json({ error: "Option not found" });
        }
        // Update option
        const option = await prisma_js_1.prisma.fieldOption.update({
            where: { id: Number(optionId) },
            data: {
                label,
                value,
                order,
                isDefault,
                translations,
            },
        });
        return res.status(200).json(option);
    }
    catch (error) {
        console.error("Error updating option:", error);
        return res.status(500).json({ error: "Failed to update option" });
    }
};
exports.updateOption = updateOption;
// Delete option
const deleteOption = async (req, res) => {
    const { id, fieldId, optionId } = req.params;
    try {
        // Check if option exists
        const existingOption = await prisma_js_1.prisma.fieldOption.findFirst({
            where: {
                id: Number(optionId),
                fieldId: Number(fieldId),
                field: {
                    formId: Number(id),
                },
            },
        });
        if (!existingOption) {
            return res.status(404).json({ error: "Option not found" });
        }
        // Delete option
        await prisma_js_1.prisma.fieldOption.delete({
            where: { id: Number(optionId) },
        });
        return res.status(200).json({ message: "Option deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting option:", error);
        return res.status(500).json({ error: "Failed to delete option" });
    }
};
exports.deleteOption = deleteOption;
// Submit form
const submitForm = async (req, res) => {
    const { id } = req.params;
    const { data, submitterEmail, submitterName, referralCode, source } = req.body;
    try {
        // Check if form exists and is published
        const form = await prisma_js_1.prisma.recruitmentForm.findFirst({
            where: {
                id: Number(id),
                status: "published",
                isPublic: true,
            },
            include: {
                fields: {
                    include: {
                        options: true,
                    },
                },
            },
        });
        if (!form) {
            return res.status(404).json({ error: "Form not found or not available" });
        }
        // Validate required fields
        const requiredFields = form.fields.filter((field) => field.isRequired);
        for (const field of requiredFields) {
            if (!data[field.name] &&
                data[field.name] !== 0 &&
                data[field.name] !== false) {
                return res
                    .status(400)
                    .json({ error: `Field '${field.label}' is required` });
            }
        }
        // Create submission
        const submission = await prisma_js_1.prisma.formSubmission.create({
            data: {
                formId: Number(id),
                data,
                submittedBy: req.user.id,
                submitterEmail: submitterEmail || data.email || null,
                submitterName: submitterName ||
                    `${data.firstName || ""} ${data.lastName || ""}`.trim() ||
                    null,
                submitterIp: req.ip || null,
                referralCode,
                source,
                responses: {
                    create: Object.entries(data)
                        .map(([key, value]) => {
                        const field = form.fields.find((f) => f.name === key);
                        if (!field)
                            return null;
                        return {
                            fieldId: field.id,
                            value: typeof value === "object"
                                ? JSON.stringify(value)
                                : String(value),
                        };
                    })
                        .filter(Boolean),
                },
            },
        });
        // Update form submission count and conversion rate
        await prisma_js_1.prisma.recruitmentForm.update({
            where: { id: Number(id) },
            data: {
                submissionCount: { increment: 1 },
                conversionRate: {
                    set: form.viewCount > 0
                        ? (form.submissionCount + 1) / form.viewCount
                        : null,
                },
            },
        });
        // Send notification emails if configured
        if (form.notifyEmails && form.notifyEmails.length > 0) {
            // This would be implemented with your email service
            console.log(`Sending notification emails to: ${form.notifyEmails.join(", ")}`);
        }
        return res.status(201).json({
            id: submission.id,
            message: "Form submitted successfully",
            redirectUrl: form.redirectUrl,
            thankYouMessage: form.thankYouMessage,
        });
    }
    catch (error) {
        console.error("Error submitting form:", error);
        return res.status(500).json({ error: "Failed to submit form" });
    }
};
exports.submitForm = submitForm;
// Get form submissions
const getFormSubmissions = async (req, res) => {
    const { id } = req.params;
    const { status, page = 1, limit = 20 } = req.query;
    try {
        // Check if form exists
        const existingForm = await prisma_js_1.prisma.recruitmentForm.findUnique({
            where: { id: Number(id) },
        });
        if (!existingForm) {
            return res.status(404).json({ error: "Form not found" });
        }
        // Calculate pagination
        const skip = (Number(page) - 1) * Number(limit);
        // Get submissions
        const submissions = await prisma_js_1.prisma.formSubmission.findMany({
            where: {
                formId: Number(id),
                status: status || undefined,
            },
            orderBy: {
                createdAt: "desc",
            },
            skip,
            take: Number(limit),
            include: {
                responses: {
                    include: {
                        field: true,
                    },
                },
            },
        });
        // Get total count
        const totalCount = await prisma_js_1.prisma.formSubmission.count({
            where: {
                formId: Number(id),
                status: status || undefined,
            },
        });
        return res.status(200).json({
            submissions,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                totalCount,
                totalPages: Math.ceil(totalCount / Number(limit)),
            },
        });
    }
    catch (error) {
        console.error("Error fetching form submissions:", error);
        return res.status(500).json({ error: "Failed to fetch form submissions" });
    }
};
exports.getFormSubmissions = getFormSubmissions;
// Get submission by ID
const getSubmissionById = async (req, res) => {
    const { id, submissionId } = req.params;
    try {
        // Check if submission exists
        const submission = await prisma_js_1.prisma.formSubmission.findFirst({
            where: {
                id: Number(submissionId),
                formId: Number(id),
            },
            include: {
                form: {
                    include: {
                        fields: true,
                    },
                },
                responses: {
                    include: {
                        field: {
                            include: {
                                options: true,
                            },
                        },
                    },
                },
            },
        });
        if (!submission) {
            return res.status(404).json({ error: "Submission not found" });
        }
        return res.status(200).json(submission);
    }
    catch (error) {
        console.error("Error fetching submission:", error);
        return res.status(500).json({ error: "Failed to fetch submission" });
    }
};
exports.getSubmissionById = getSubmissionById;
// Update submission status
const updateSubmissionStatus = async (req, res) => {
    const { id, submissionId } = req.params;
    const { status, notes } = req.body;
    try {
        // Check if submission exists
        const existingSubmission = await prisma_js_1.prisma.formSubmission.findFirst({
            where: {
                id: Number(submissionId),
                formId: Number(id),
            },
        });
        if (!existingSubmission) {
            return res.status(404).json({ error: "Submission not found" });
        }
        // Update submission
        const submission = await prisma_js_1.prisma.formSubmission.update({
            where: { id: Number(submissionId) },
            data: {
                status,
                notes,
                reviewedBy: req.user.id,
                reviewedAt: new Date(),
            },
        });
        return res.status(200).json(submission);
    }
    catch (error) {
        console.error("Error updating submission status:", error);
        return res
            .status(500)
            .json({ error: "Failed to update submission status" });
    }
};
exports.updateSubmissionStatus = updateSubmissionStatus;
// Create worker profile from submission
const createWorkerFromSubmission = async (req, res) => {
    const { id, submissionId } = req.params;
    try {
        // Check if submission exists
        const submission = await prisma_js_1.prisma.formSubmission.findFirst({
            where: {
                id: Number(submissionId),
                formId: Number(id),
            },
            include: {
                responses: {
                    include: {
                        field: true,
                    },
                },
            },
        });
        if (!submission) {
            return res.status(404).json({ error: "Submission not found" });
        }
        // Check if worker already created
        if (submission.workerId) {
            return res
                .status(400)
                .json({ error: "Worker profile already created from this submission" });
        }
        // Extract data from submission
        const data = submission.data;
        // Create worker
        const worker = await prisma_js_1.prisma.worker.create({
            data: {
                firstName: data.firstName || data.name?.split(" ")[0] || "",
                lastName: data.lastName || data.name?.split(" ").slice(1).join(" ") || "",
                email: data.email || submission.submitterEmail || "",
                phone: data.phone || "",
                status: "AVAILABLE",
                skills: {
                    create: data.skills?.map((skill) => ({
                        name: skill,
                    })) || [],
                },
                certifications: {
                    create: data.certifications?.map((cert) => ({
                        name: cert.name || cert,
                        issuer: cert.issuer || "",
                        issueDate: cert.issueDate ? new Date(cert.issueDate) : null,
                        expiryDate: cert.expiryDate ? new Date(cert.expiryDate) : null,
                    })) || [],
                },
            },
        });
        // Create worker profile
        const workerProfile = await prisma_js_1.prisma.workerProfile.create({
            data: {
                worker: {
                    connect: { id: worker.id },
                },
                specialization: data.specialization || data.role || "",
                experienceYears: parseInt(data.yearsOfExperience || "0", 10) || 0,
                hourlyRate: parseFloat(data.hourlyRate || "0") || 0,
                currentlyAvailable: true,
                searchTags: data.skills || [],
            },
        });
        // Update submission with worker ID
        await prisma_js_1.prisma.formSubmission.update({
            where: { id: Number(submissionId) },
            data: {
                workerId: worker.id,
                status: "hired",
            },
        });
        return res.status(201).json({
            worker,
            workerProfile,
            message: "Worker profile created successfully",
        });
    }
    catch (error) {
        console.error("Error creating worker from submission:", error);
        return res
            .status(500)
            .json({ error: "Failed to create worker from submission" });
    }
};
exports.createWorkerFromSubmission = createWorkerFromSubmission;
//# sourceMappingURL=formBuilderController.js.map