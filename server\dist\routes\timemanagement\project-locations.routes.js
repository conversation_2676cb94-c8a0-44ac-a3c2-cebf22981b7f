"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const project_locations_controller_1 = require("../../controllers/timemanagement/project-locations.controller");
const route_helpers_js_1 = require("../../utils/route-helpers.js");
const router = express_1.default.Router();
// Get all project locations
router.get("/", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(project_locations_controller_1.getProjectLocations));
// Get project location by ID
router.get("/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(project_locations_controller_1.getProjectLocationById));
// Check if a user's location is within a project's radius
router.post("/check-compliance", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(project_locations_controller_1.checkLocationCompliance));
exports.default = router;
//# sourceMappingURL=project-locations.routes.js.map