// Generated by nitro

// App Config
import type { Defu } from 'defu'

import type { default as appConfig0 } from "../../app.config";
import type { default as appConfig1 } from "../../layers/tairo/app.config";
import type { default as appConfig2 } from "../../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/app.config";
import type { default as appConfig3 } from "../../layers/accounting/app.config";
import type { default as appConfig4 } from "../../layers/budget/app.config";
import type { default as appConfig5 } from "../../layers/companies/app.config";
import type { default as appConfig6 } from "../../layers/core/app.config";
import type { default as appConfig7 } from "../../layers/hr/app.config";
import type { default as appConfig8 } from "../../layers/production/app.config";
import type { default as appConfig9 } from "../../layers/tairo-layout-sidebar/app.config";
import type { default as appConfig10 } from "../../layers/tairo-layout-iconnav/app.config";

type UserAppConfig = Defu<{}, [typeof appConfig0, typeof appConfig1, typeof appConfig2, typeof appConfig3, typeof appConfig4, typeof appConfig5, typeof appConfig6, typeof appConfig7, typeof appConfig8, typeof appConfig9, typeof appConfig10]>

declare module "nitropack/types" {
  interface AppConfig extends UserAppConfig {}

}
export {}