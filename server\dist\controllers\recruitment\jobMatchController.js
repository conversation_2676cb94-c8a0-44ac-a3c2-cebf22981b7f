"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCompanyInterest = exports.updateWorkerInterest = exports.getMatchesForWorker = exports.getMatchesForJobRequest = exports.getAllMatches = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const client_1 = require("@prisma/client");
// Using centralized prisma instance from lib/prisma.js
// Get all matches
const getAllMatches = async (req, res) => {
    try {
        const matches = await prisma_js_1.prisma.jobMatch.findMany({
            include: {
                worker: {
                    include: {
                        worker: true,
                    },
                },
                jobRequest: {
                    include: {
                        company: true,
                    },
                },
            },
            orderBy: {
                matchScore: "desc",
            },
        });
        return res.status(200).json(matches);
    }
    catch (error) {
        console.error("Error fetching matches:", error);
        return res.status(500).json({ error: "Failed to fetch matches" });
    }
};
exports.getAllMatches = getAllMatches;
// Get matches for a job request
const getMatchesForJobRequest = async (req, res) => {
    const { jobRequestId } = req.params;
    try {
        const matches = await prisma_js_1.prisma.jobMatch.findMany({
            where: {
                jobRequestId: Number(jobRequestId),
            },
            include: {
                worker: {
                    include: {
                        worker: {
                            include: {
                                skills: true,
                                certifications: true,
                            },
                        },
                    },
                },
            },
            orderBy: {
                matchScore: "desc",
            },
        });
        return res.status(200).json(matches);
    }
    catch (error) {
        console.error("Error fetching matches for job request:", error);
        return res
            .status(500)
            .json({ error: "Failed to fetch matches for job request" });
    }
};
exports.getMatchesForJobRequest = getMatchesForJobRequest;
// Get matches for a worker
const getMatchesForWorker = async (req, res) => {
    const { workerProfileId } = req.params;
    try {
        const matches = await prisma_js_1.prisma.jobMatch.findMany({
            where: {
                workerProfileId: Number(workerProfileId),
            },
            include: {
                jobRequest: {
                    include: {
                        company: true,
                    },
                },
            },
            orderBy: {
                matchScore: "desc",
            },
        });
        return res.status(200).json(matches);
    }
    catch (error) {
        console.error("Error fetching matches for worker:", error);
        return res
            .status(500)
            .json({ error: "Failed to fetch matches for worker" });
    }
};
exports.getMatchesForWorker = getMatchesForWorker;
// Update worker interest in a match
const updateWorkerInterest = async (req, res) => {
    const { id } = req.params;
    const { interested } = req.body;
    try {
        const match = await prisma_js_1.prisma.jobMatch.update({
            where: { id: Number(id) },
            data: {
                workerInterested: interested,
            },
        });
        // If both parties are interested, create a work assignment
        if (match.workerInterested && match.companyInterested) {
            await createWorkAssignment(match.id.toString());
        }
        return res.status(200).json(match);
    }
    catch (error) {
        console.error("Error updating worker interest:", error);
        return res.status(500).json({ error: "Failed to update worker interest" });
    }
};
exports.updateWorkerInterest = updateWorkerInterest;
// Update company interest in a match
const updateCompanyInterest = async (req, res) => {
    const { id } = req.params;
    const { interested } = req.body;
    try {
        const match = await prisma_js_1.prisma.jobMatch.update({
            where: { id: Number(id) },
            data: {
                companyInterested: interested,
            },
        });
        // If both parties are interested, create a work assignment
        if (match.workerInterested && match.companyInterested) {
            await createWorkAssignment(match.id.toString());
        }
        return res.status(200).json(match);
    }
    catch (error) {
        console.error("Error updating company interest:", error);
        return res.status(500).json({ error: "Failed to update company interest" });
    }
};
exports.updateCompanyInterest = updateCompanyInterest;
// Helper function to create a work assignment from a match
async function createWorkAssignment(matchId) {
    try {
        // Get match details
        const match = await prisma_js_1.prisma.jobMatch.findUnique({
            where: { id: Number(matchId) },
            include: {
                worker: true,
                jobRequest: true,
            },
        });
        if (!match) {
            console.error("Match not found");
            return;
        }
        // Check if assignment already exists
        const existingAssignment = await prisma_js_1.prisma.intermediationAssignment.findUnique({
            where: { matchId: Number(matchId) },
        });
        if (existingAssignment) {
            console.log("Assignment already exists");
            return;
        }
        // Create intermediation assignment
        const assignment = await prisma_js_1.prisma.intermediationAssignment.create({
            data: {
                worker: {
                    connect: { id: match.workerProfileId },
                },
                jobRequest: {
                    connect: { id: match.jobRequestId },
                },
                match: {
                    connect: { id: Number(matchId) },
                },
                startDate: match.jobRequest.startDate,
                endDate: match.jobRequest.endDate,
                rate: match.worker.hourlyRate || 0,
                rateType: match.jobRequest.rateType,
                trialPeriod: true,
                trialEndDate: new Date(new Date().setDate(new Date().getDate() + 7)), // 1 week trial
            },
        });
        // Update match status
        await prisma_js_1.prisma.jobMatch.update({
            where: { id: Number(matchId) },
            data: {
                status: client_1.MatchStatus.accepted,
            },
        });
        // Update job request status if it was open
        if (match.jobRequest.status === "open") {
            await prisma_js_1.prisma.jobRequest.update({
                where: { id: match.jobRequestId },
                data: {
                    status: "inProgress",
                },
            });
        }
        console.log(`Created work assignment ${assignment.id} for match ${matchId}`);
    }
    catch (error) {
        console.error("Error creating work assignment:", error);
    }
}
//# sourceMappingURL=jobMatchController.js.map