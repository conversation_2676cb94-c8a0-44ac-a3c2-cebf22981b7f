"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.markBonusAsPaid = exports.getEmployeeBonuses = exports.deleteBonus = exports.updateBonus = exports.createBonus = exports.getBonusById = exports.getAllBonuses = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Using centralized prisma instance from lib/prisma.js
// Get all bonuses
const getAllBonuses = async (req, res, next) => {
    try {
        const { companyId } = req.query;
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        const bonuses = await prisma_js_1.prisma.bonus.findMany({
            where: {
                employee: {
                    companyId: Number(companyId),
                },
            },
            include: {
                employee: {
                    include: {
                        user: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(bonuses);
    }
    catch (error) {
        console.error("Error fetching bonuses:", error);
        next(createHttpError(500, "Failed to fetch bonuses"));
    }
};
exports.getAllBonuses = getAllBonuses;
// Get a specific bonus by ID
const getBonusById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const bonus = await prisma_js_1.prisma.bonus.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                employee: {
                    include: {
                        user: true,
                    },
                },
            },
        });
        if (!bonus) {
            return next(createHttpError(404, "Bonus not found"));
        }
        res.json(bonus);
    }
    catch (error) {
        console.error("Error fetching bonus:", error);
        next(createHttpError(500, "Failed to fetch bonus"));
    }
};
exports.getBonusById = getBonusById;
// Create a new bonus
const createBonus = async (req, res, next) => {
    try {
        const { employeeId, type, amount, currency, description, awardDate, isPaid, paymentDate, } = req.body;
        if (!employeeId || !type || !amount) {
            return next(createHttpError(400, "Employee ID, type, and amount are required"));
        }
        // Check if employee exists
        const employee = await prisma_js_1.prisma.employee.findUnique({
            where: {
                id: Number(employeeId),
            },
        });
        if (!employee) {
            return next(createHttpError(404, "Employee not found"));
        }
        const bonus = await prisma_js_1.prisma.bonus.create({
            data: {
                employeeId: Number(employeeId),
                type: type, // Cast to BonusType enum
                amount: parseFloat(amount),
                currency: currency || "USD",
                description,
                awardDate: awardDate ? new Date(awardDate) : new Date(),
                isPaid: isPaid || false,
                paymentDate: isPaid && paymentDate ? new Date(paymentDate) : null,
            },
        });
        res.status(201).json(bonus);
    }
    catch (error) {
        console.error("Error creating bonus:", error);
        next(createHttpError(500, "Failed to create bonus"));
    }
};
exports.createBonus = createBonus;
// Update a bonus
const updateBonus = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { type, amount, currency, description, awardDate, isPaid, paymentDate, } = req.body;
        const bonus = await prisma_js_1.prisma.bonus.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!bonus) {
            return next(createHttpError(404, "Bonus not found"));
        }
        const updatedBonus = await prisma_js_1.prisma.bonus.update({
            where: {
                id: Number(id),
            },
            data: {
                type: type !== undefined ? type : undefined,
                amount: amount !== undefined ? parseFloat(amount) : undefined,
                currency: currency !== undefined ? currency : undefined,
                description: description !== undefined ? description : undefined,
                awardDate: awardDate !== undefined ? new Date(awardDate) : undefined,
                isPaid: isPaid !== undefined ? isPaid : undefined,
                paymentDate: paymentDate !== undefined ? new Date(paymentDate) : undefined,
            },
        });
        res.json(updatedBonus);
    }
    catch (error) {
        console.error("Error updating bonus:", error);
        next(createHttpError(500, "Failed to update bonus"));
    }
};
exports.updateBonus = updateBonus;
// Delete a bonus
const deleteBonus = async (req, res, next) => {
    try {
        const { id } = req.params;
        const bonus = await prisma_js_1.prisma.bonus.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!bonus) {
            return next(createHttpError(404, "Bonus not found"));
        }
        await prisma_js_1.prisma.bonus.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Bonus deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting bonus:", error);
        next(createHttpError(500, "Failed to delete bonus"));
    }
};
exports.deleteBonus = deleteBonus;
// Get all bonuses for an employee
const getEmployeeBonuses = async (req, res, next) => {
    try {
        const { employeeId } = req.params;
        const bonuses = await prisma_js_1.prisma.bonus.findMany({
            where: {
                employeeId: Number(employeeId),
            },
            include: {
                employee: {
                    include: {
                        user: true,
                    },
                },
            },
            orderBy: {
                awardDate: "desc",
            },
        });
        res.json(bonuses);
    }
    catch (error) {
        console.error("Error fetching employee bonuses:", error);
        next(createHttpError(500, "Failed to fetch employee bonuses"));
    }
};
exports.getEmployeeBonuses = getEmployeeBonuses;
// Mark a bonus as paid
const markBonusAsPaid = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { paymentDate } = req.body;
        if (!paymentDate) {
            return next(createHttpError(400, "Payment date is required"));
        }
        const bonus = await prisma_js_1.prisma.bonus.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!bonus) {
            return next(createHttpError(404, "Bonus not found"));
        }
        if (bonus.isPaid) {
            return next(createHttpError(400, "Bonus is already marked as paid"));
        }
        const updatedBonus = await prisma_js_1.prisma.bonus.update({
            where: {
                id: Number(id),
            },
            data: {
                isPaid: true,
                paymentDate: new Date(paymentDate),
            },
        });
        res.json(updatedBonus);
    }
    catch (error) {
        console.error("Error marking bonus as paid:", error);
        next(createHttpError(500, "Failed to mark bonus as paid"));
    }
};
exports.markBonusAsPaid = markBonusAsPaid;
//# sourceMappingURL=bonuses.controller.js.map