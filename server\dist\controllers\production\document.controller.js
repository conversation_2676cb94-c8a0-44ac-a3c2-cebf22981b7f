"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteDocument = exports.uploadDocument = exports.getProjectDocuments = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
// Get project documents
const getProjectDocuments = async (req, res) => {
    try {
        const { id: projectId } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Get user's company
        const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
            where: { userId },
            select: { companyId: true },
        });
        if (!userCompany) {
            return res
                .status(404)
                .json({ error: "User not associated with any company" });
        }
        // Check if project exists and belongs to user's company
        const project = await prisma_js_1.prisma.project.findFirst({
            where: {
                id: Number(projectId),
                companyId: Number(userCompany.companyId),
            },
        });
        if (!project) {
            return res.status(404).json({ error: "Project not found" });
        }
        // Get documents
        const documents = await prisma_js_1.prisma.projectDocument.findMany({
            where: { projectId: Number(projectId) },
            include: {
                uploadedBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
            },
            orderBy: { createdAt: "desc" },
        });
        return res.status(200).json(documents);
    }
    catch (error) {
        console.error(`Error fetching project documents:`, error);
        return res.status(500).json({ error: "Failed to fetch project documents" });
    }
};
exports.getProjectDocuments = getProjectDocuments;
// Upload document
const uploadDocument = async (req, res) => {
    try {
        const { id: projectId } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Get user's company
        const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
            where: { userId },
            select: { companyId: true },
        });
        if (!userCompany) {
            return res
                .status(404)
                .json({ error: "User not associated with any company" });
        }
        // Check if project exists and belongs to user's company
        const project = await prisma_js_1.prisma.project.findFirst({
            where: {
                id: Number(projectId),
                companyId: Number(userCompany.companyId),
            },
        });
        if (!project) {
            return res.status(404).json({ error: "Project not found" });
        }
        // Handle file upload
        if (!req.file) {
            return res.status(400).json({ error: "No file uploaded" });
        }
        const { title, description, category, version } = req.body;
        if (!title) {
            return res.status(400).json({ error: "Title is required" });
        }
        // In a real implementation, you would upload the file to a storage service
        // and get a URL back. For this example, we'll just use a placeholder URL.
        const fileUrl = `/uploads/${req.file.filename}`;
        const fileType = req.file.mimetype;
        const fileSize = req.file.size;
        const document = await prisma_js_1.prisma.projectDocument.create({
            data: {
                title,
                description,
                fileUrl,
                fileType,
                fileSize,
                category: category || "GENERAL",
                version,
                projectId: Number(projectId),
                uploadedById: Number(userId),
            },
        });
        return res.status(201).json(document);
    }
    catch (error) {
        console.error("Error uploading document:", error);
        return res.status(500).json({ error: "Failed to upload document" });
    }
};
exports.uploadDocument = uploadDocument;
// Delete document
const deleteDocument = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Get document
        const document = await prisma_js_1.prisma.projectDocument.findUnique({
            where: { id: Number(id) },
            include: {
                project: {
                    select: {
                        companyId: true,
                    },
                },
            },
        });
        if (!document) {
            return res.status(404).json({ error: "Document not found" });
        }
        // Get user's company
        const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
            where: { userId },
            select: { companyId: true },
        });
        if (!userCompany ||
            Number(userCompany.companyId) !== document.project.companyId) {
            return res
                .status(403)
                .json({ error: "You do not have permission to delete this document" });
        }
        // In a real implementation, you would also delete the file from storage
        // For this example, we'll try to delete the file from the uploads directory
        if (document.fileUrl) {
            const filePath = path.join(__dirname, "../../", document.fileUrl);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        }
        await prisma_js_1.prisma.projectDocument.delete({
            where: { id: Number(id) },
        });
        return res.status(200).json({ message: "Document deleted successfully" });
    }
    catch (error) {
        console.error(`Error deleting document:`, error);
        return res.status(500).json({ error: "Failed to delete document" });
    }
};
exports.deleteDocument = deleteDocument;
//# sourceMappingURL=document.controller.js.map