"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedChatData = seedChatData;
const client_1 = require("@prisma/client");
const dotenv_1 = require("dotenv");
const path_1 = require("path");
// Load environment variables from .env.dev
(0, dotenv_1.config)({ path: (0, path_1.resolve)(__dirname, "../../.env.dev") });
const prisma = new client_1.PrismaClient();
async function seedChatData() {
    console.log("🌱 Seeding Estonian chat data...");
    try {
        // Get users from the same company for testing
        const users = await prisma.user.findMany({
            take: 10,
            orderBy: { id: "asc" },
        });
        if (users.length < 2) {
            console.log("❌ Need at least 2 users to create chat conversations. Please seed users first.");
            return;
        }
        console.log(`💬 Creating Estonian chat data for ${users.length} users`);
        // Clear existing chat data
        await prisma.chatMessage.deleteMany();
        await prisma.chatParticipant.deleteMany();
        await prisma.chatConversation.deleteMany();
        console.log("🗑️ Cleared existing chat data");
        // Estonian conversation topics and messages
        const estonianConversations = [
            {
                participants: [users[0].id, users[1].id],
                messages: [
                    { senderId: users[0].id, content: "Tere! Kuidas läheb?", time: 2 },
                    {
                        senderId: users[1].id,
                        content: "Tere! Kõik on hästi, aitäh küsimast. Kuidas sul läheb?",
                        time: 1.5,
                    },
                    {
                        senderId: users[0].id,
                        content: "Mul ka hästi! Kas sa jõuad täna koosolekule?",
                        time: 1,
                    },
                    {
                        senderId: users[1].id,
                        content: "Jah, kindlasti! Mis kell see algab?",
                        time: 0.5,
                    },
                    {
                        senderId: users[0].id,
                        content: "Kell 14:00 konverentsisaalis. Näeme seal!",
                        time: 0.25,
                    },
                ],
            },
            {
                participants: [users[0].id, users[2] ? users[2].id : users[1].id],
                messages: [
                    {
                        senderId: users[2] ? users[2].id : users[1].id,
                        content: "Kas sa saaksid mulle aidata ühe projektiga?",
                        time: 3,
                    },
                    {
                        senderId: users[0].id,
                        content: "Muidugi! Mis abi sa vajad?",
                        time: 2.8,
                    },
                    {
                        senderId: users[2] ? users[2].id : users[1].id,
                        content: "Ma ei saa aru, kuidas see uus süsteem töötab.",
                        time: 2.5,
                    },
                    {
                        senderId: users[0].id,
                        content: "Võime homme kohtuda ja ma näitan sulle. Sobib?",
                        time: 2,
                    },
                    {
                        senderId: users[2] ? users[2].id : users[1].id,
                        content: "Suurepärane! Aitäh sulle!",
                        time: 1.5,
                    },
                ],
            },
        ];
        // Create conversations with Estonian messages
        for (let i = 0; i < estonianConversations.length; i++) {
            const convData = estonianConversations[i];
            const conversation = await prisma.chatConversation.create({
                data: {
                    type: "DIRECT",
                    participants: {
                        create: convData.participants.map((userId) => ({ userId })),
                    },
                },
            });
            console.log(`💬 Created Estonian conversation ${i + 1}: ${conversation.id}`);
            // Create messages for this conversation
            for (const msgData of convData.messages) {
                await prisma.chatMessage.create({
                    data: {
                        conversationId: conversation.id,
                        senderId: msgData.senderId,
                        content: msgData.content,
                        status: client_1.ChatMessageStatus.READ,
                        createdAt: new Date(Date.now() - msgData.time * 60 * 60 * 1000), // hours ago
                    },
                });
            }
            console.log(`💬 Created ${convData.messages.length} Estonian messages for conversation ${i + 1}`);
        }
        // Add more Estonian conversations if we have more users
        if (users.length >= 4) {
            const moreConversations = [
                {
                    participants: [users[1].id, users[3].id],
                    messages: [
                        {
                            senderId: users[1].id,
                            content: "Kas sa oled juba lõunat söönud?",
                            time: 4,
                        },
                        {
                            senderId: users[3].id,
                            content: "Ei ole veel. Tahad koos minna?",
                            time: 3.5,
                        },
                        {
                            senderId: users[1].id,
                            content: "Jah, hea mõte! Kuhu läheme?",
                            time: 3,
                        },
                        {
                            senderId: users[3].id,
                            content: "Võiksime minna sellesse uude kohvikusse.",
                            time: 2.5,
                        },
                        {
                            senderId: users[1].id,
                            content: "Suurepärane valik! Kohtume 10 minuti pärast fuajees.",
                            time: 2,
                        },
                    ],
                },
                {
                    participants: [users[2].id, users[3].id],
                    messages: [
                        {
                            senderId: users[2].id,
                            content: "Kas sa näed homme meie esitlust?",
                            time: 5,
                        },
                        {
                            senderId: users[3].id,
                            content: "Jah, kindlasti! Olen väga huvitatud.",
                            time: 4.5,
                        },
                        {
                            senderId: users[2].id,
                            content: "Suurepärane! Ma arvan, et see läheb hästi.",
                            time: 4,
                        },
                        {
                            senderId: users[3].id,
                            content: "Ma olen kindel, et te teete suurepärast tööd!",
                            time: 3.5,
                        },
                    ],
                },
            ];
            for (let i = 0; i < moreConversations.length; i++) {
                const convData = moreConversations[i];
                const conversation = await prisma.chatConversation.create({
                    data: {
                        type: "DIRECT",
                        participants: {
                            create: convData.participants.map((userId) => ({ userId })),
                        },
                    },
                });
                console.log(`💬 Created additional Estonian conversation ${i + 3}: ${conversation.id}`);
                // Create messages for this conversation
                for (const msgData of convData.messages) {
                    await prisma.chatMessage.create({
                        data: {
                            conversationId: conversation.id,
                            senderId: msgData.senderId,
                            content: msgData.content,
                            status: client_1.ChatMessageStatus.READ,
                            createdAt: new Date(Date.now() - msgData.time * 60 * 60 * 1000), // hours ago
                        },
                    });
                }
                console.log(`💬 Created ${convData.messages.length} Estonian messages for additional conversation ${i + 3}`);
            }
        }
        // Create Estonian group conversation if we have 5 or more users
        if (users.length >= 5) {
            const groupConversation = await prisma.chatConversation.create({
                data: {
                    name: "Meeskonna Arutelu",
                    type: "GROUP",
                    participants: {
                        create: users.slice(0, 5).map((user) => ({ userId: user.id })),
                    },
                },
            });
            console.log(`💬 Created Estonian group conversation: ${groupConversation.name}`);
            // Create Estonian group messages
            const groupMessages = [
                {
                    conversationId: groupConversation.id,
                    senderId: users[0].id,
                    content: "Tere meeskond! Arutame homme toimuvat sprindiplaneerimist.",
                    status: client_1.ChatMessageStatus.READ,
                    createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
                },
                {
                    conversationId: groupConversation.id,
                    senderId: users[1].id,
                    content: "Suurepärane idee! Mul on mõned funktsioonid, mida tahaksin pakkuda.",
                    status: client_1.ChatMessageStatus.READ,
                    createdAt: new Date(Date.now() - 5.5 * 60 * 60 * 1000), // 5.5 hours ago
                },
                {
                    conversationId: groupConversation.id,
                    senderId: users[2].id,
                    content: "Olen kindlasti kaasas! Millal me koosoleku planeerime?",
                    status: client_1.ChatMessageStatus.READ,
                    createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
                },
                {
                    conversationId: groupConversation.id,
                    senderId: users[3].id,
                    content: "Kas homme kell 14:00 sobiks kõigile?",
                    status: client_1.ChatMessageStatus.READ,
                    createdAt: new Date(Date.now() - 4.5 * 60 * 60 * 1000), // 4.5 hours ago
                },
                {
                    conversationId: groupConversation.id,
                    senderId: users[4].id,
                    content: "Mulle sobib! Kas keegi valmistab päevakorra ette?",
                    status: client_1.ChatMessageStatus.READ,
                    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
                },
                {
                    conversationId: groupConversation.id,
                    senderId: users[1].id,
                    content: "Ma võin päevakorra koostada ja täna õhtul välja saata.",
                    status: client_1.ChatMessageStatus.DELIVERED,
                    createdAt: new Date(Date.now() - 3.5 * 60 * 60 * 1000), // 3.5 hours ago
                },
                {
                    conversationId: groupConversation.id,
                    senderId: users[0].id,
                    content: "Tänan! Ootan põnevusega homme kohtumist.",
                    status: client_1.ChatMessageStatus.SENT,
                    createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
                },
            ];
            for (const messageData of groupMessages) {
                await prisma.chatMessage.create({
                    data: messageData,
                });
            }
            console.log(`💬 Created ${groupMessages.length} Estonian messages in group conversation`);
        }
        // Update conversation timestamps
        await prisma.chatConversation.updateMany({
            data: {
                updatedAt: new Date(),
            },
        });
        console.log("✅ Chat data seeding completed successfully!");
    }
    catch (error) {
        console.error("❌ Error seeding chat data:", error);
        throw error;
    }
}
// Run if called directly
if (require.main === module) {
    seedChatData()
        .catch((e) => {
        console.error(e);
        process.exit(1);
    })
        .finally(async () => {
        await prisma.$disconnect();
    });
}
//# sourceMappingURL=chat-data.js.map