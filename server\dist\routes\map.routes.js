"use strict";
// server/routes/map.routes.ts
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const authenticateToken_1 = require("../middleware/authenticateToken");
const map_controller_1 = require("../controllers/map/map.controller");
const gps_controller_1 = require("../controllers/map/gps.controller");
const router = express_1.default.Router();
// Map data routes
router.get("/data", authenticateToken_1.authenticateToken, map_controller_1.getMapData);
// GPS device routes
router.post("/gps/devices", authenticateToken_1.authenticateToken, gps_controller_1.createGpsDevice);
router.get("/gps/devices", authenticateToken_1.authenticateToken, gps_controller_1.getGpsDevices);
router.post("/gps/location", gps_controller_1.receiveGpsLocation); // No auth for GPS devices
router.get("/gps/devices/:deviceId/history", authenticateToken_1.authenticateToken, gps_controller_1.getLocationHistory);
// Worker location routes
router.post("/worker/location", authenticateToken_1.authenticateToken, gps_controller_1.updateWorkerLocation);
exports.default = router;
//# sourceMappingURL=map.routes.js.map