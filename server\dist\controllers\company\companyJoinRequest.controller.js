"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.acceptInvitation = exports.verifyInvitation = exports.getCompanyInvitations = exports.createInvitation = exports.respondToJoinRequest = exports.getUserJoinRequests = exports.getCompanyJoinRequests = exports.createJoinRequest = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const crypto = __importStar(require("crypto"));
const sendEmail_js_1 = require("../../utils/sendEmail.js");
// Using centralized prisma instance from lib/prisma.js
const SERVER_URL = process.env.SERVER_URL || "http://localhost:4004";
const CLIENT_URL = process.env.CLIENT_URL || "http://localhost:3000";
// Create a join request
const createJoinRequest = async (req, res) => {
    try {
        const { userId, companyName, role, message } = req.body;
        if (!userId || !companyName || !role) {
            return res.status(400).json({ message: "Missing required fields" });
        }
        // Check if user exists
        const user = await prisma_js_1.prisma.user.findUnique({
            where: { id: parseInt(userId) },
        });
        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findFirst({
            where: { name: companyName },
        });
        if (!company) {
            return res.status(404).json({ message: "Company not found" });
        }
        // Check if user already has a pending request for this company
        const existingRequest = await prisma_js_1.prisma.companyJoinRequest.findFirst({
            where: {
                userId: parseInt(userId),
                companyId: company.id,
                status: "PENDING",
            },
        });
        if (existingRequest) {
            return res.status(400).json({
                message: "You already have a pending request for this company",
            });
        }
        // Create join request
        const joinRequest = await prisma_js_1.prisma.companyJoinRequest.create({
            data: {
                user: { connect: { id: parseInt(userId) } },
                company: { connect: { id: company.id } },
                companyName,
                role,
                message,
                status: "PENDING",
            },
        });
        // Find company owner to notify
        const companyOwner = await prisma_js_1.prisma.userCompany.findFirst({
            where: {
                companyId: company.id,
                role: "OWNER",
            },
            include: {
                user: true,
            },
        });
        if (companyOwner && companyOwner.user) {
            // Send email notification to company owner
            const emailSubject = "New Join Request";
            const emailBody = `
        <h1>New Join Request</h1>
        <p>${user.firstName} ${user.lastName} has requested to join your company as a ${role}.</p>
        <p>Message: ${message || "No message provided"}</p>
        <p>Please log in to your account to approve or reject this request.</p>
        <a href="${CLIENT_URL}/recruitment/company-requests">View Request</a>
      `;
            await (0, sendEmail_js_1.sendEmail)(companyOwner.user.email, emailSubject, null, emailBody);
        }
        return res.status(201).json({
            message: "Join request created successfully",
            joinRequest,
        });
    }
    catch (error) {
        console.error("Error creating join request:", error);
        return res.status(500).json({ message: "Server error", error });
    }
};
exports.createJoinRequest = createJoinRequest;
// Get all join requests for a company
const getCompanyJoinRequests = async (req, res) => {
    try {
        const { companyId } = req.params;
        if (!companyId) {
            return res.status(400).json({ message: "Company ID is required" });
        }
        const joinRequests = await prisma_js_1.prisma.companyJoinRequest.findMany({
            where: {
                companyId: Number(companyId),
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        phone: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json(joinRequests);
    }
    catch (error) {
        console.error("Error getting join requests:", error);
        return res.status(500).json({ message: "Server error", error });
    }
};
exports.getCompanyJoinRequests = getCompanyJoinRequests;
// Get all join requests for a user
const getUserJoinRequests = async (req, res) => {
    try {
        const { userId } = req.params;
        if (!userId) {
            return res.status(400).json({ message: "User ID is required" });
        }
        const joinRequests = await prisma_js_1.prisma.companyJoinRequest.findMany({
            where: {
                userId: parseInt(userId),
            },
            include: {
                company: {
                    select: {
                        id: true,
                        name: true,
                        type: true,
                        status: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json(joinRequests);
    }
    catch (error) {
        console.error("Error getting user join requests:", error);
        return res.status(500).json({ message: "Server error", error });
    }
};
exports.getUserJoinRequests = getUserJoinRequests;
// Approve or reject a join request
const respondToJoinRequest = async (req, res) => {
    try {
        const { requestId } = req.params;
        const { status, responseMessage } = req.body;
        if (!requestId || !status) {
            return res
                .status(400)
                .json({ message: "Request ID and status are required" });
        }
        if (status !== "APPROVED" && status !== "REJECTED") {
            return res
                .status(400)
                .json({ message: "Status must be APPROVED or REJECTED" });
        }
        // Get the join request
        const joinRequest = (await prisma_js_1.prisma.companyJoinRequest.findUnique({
            where: { id: Number(requestId) },
            include: {
                user: true,
                company: true,
            },
        }));
        if (!joinRequest) {
            return res.status(404).json({ message: "Join request not found" });
        }
        if (joinRequest.status !== "PENDING") {
            return res
                .status(400)
                .json({ message: "This request has already been processed" });
        }
        // Update the join request status
        const updatedRequest = await prisma_js_1.prisma.companyJoinRequest.update({
            where: { id: Number(requestId) },
            data: {
                status,
                responseMessage,
            },
        });
        // If approved, create a UserCompany relationship
        if (status === "APPROVED") {
            await prisma_js_1.prisma.userCompany.create({
                data: {
                    user: { connect: { id: joinRequest.userId } },
                    company: { connect: { id: joinRequest.companyId } },
                    role: joinRequest.role,
                    status: "ACTIVE",
                },
            });
        }
        // Send email notification to the user
        const emailSubject = `Join Request ${status === "APPROVED" ? "Approved" : "Rejected"}`;
        const emailBody = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Join Request ${status === "APPROVED" ? "Approved" : "Rejected"}</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1e293b; /* Dark background */
            color: #e2e8f0; /* Light text */
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #475569;
          }
          .content {
            padding: 30px 20px;
          }
          .footer {
            text-align: center;
            padding: 20px 0;
            font-size: 12px;
            color: #94a3b8;
            border-top: 1px solid #475569;
          }
          h1 {
            color: ${status === "APPROVED" ? "#10b981" : "#ef4444"};
            margin-top: 0;
          }
          .button {
            display: inline-block;
            background: ${status === "APPROVED"
            ? "linear-gradient(135deg, #10b981 0%, #059669 100%)"
            : "linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%)"};
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            margin: 20px 0;
            font-weight: 600;
            text-align: center;
          }
          .message-box {
            background-color: #2d3748;
            border-left: 4px solid ${status === "APPROVED" ? "#10b981" : "#ef4444"};
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
          }
          .status-icon {
            font-size: 48px;
            text-align: center;
            margin-bottom: 20px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <img src="https://devskills.ee/img/logo/logo-light.svg" alt="Company Logo" height="40">
          </div>
          <div class="content">
            <div class="status-icon">
              ${status === "APPROVED" ? "✅" : "❌"}
            </div>
            <h1>Join Request ${status === "APPROVED" ? "Approved" : "Rejected"}</h1>
            <p>Hello ${joinRequest.user.firstName || "there"},</p>
            <p>Your request to join <strong>${joinRequest.company?.name}</strong> as a <strong>${joinRequest.role}</strong> has been <strong>${status === "APPROVED" ? "approved" : "rejected"}</strong>.</p>
            ${responseMessage
            ? `<div class="message-box"><p><strong>Message:</strong> ${responseMessage}</p></div>`
            : ""}
            ${status === "APPROVED"
            ? `<p>You can now log in and access the company resources.</p>`
            : ""}
            <a href="${CLIENT_URL}/auth" class="button">${status === "APPROVED" ? "Log In Now" : "Return to Login"}</a>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} DevSkills Ultimation Studio. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
        await (0, sendEmail_js_1.sendEmail)(joinRequest.user.email, emailSubject, null, emailBody);
        return res.status(200).json({
            message: `Join request ${status === "APPROVED" ? "approved" : "rejected"} successfully`,
            joinRequest: updatedRequest,
        });
    }
    catch (error) {
        console.error("Error responding to join request:", error);
        return res.status(500).json({ message: "Server error", error });
    }
};
exports.respondToJoinRequest = respondToJoinRequest;
// Create a company invitation
const createInvitation = async (req, res) => {
    try {
        console.log("createInvitation: Request received", {
            body: req.body,
            user: req.user,
        });
        let { companyId, email, role, message } = req.body;
        // Handle role if it's an object (from frontend)
        if (typeof role === "object" && role !== null && role.value) {
            console.log("createInvitation: Role is an object, extracting value", role);
            role = role.value;
        }
        console.log("createInvitation: Extracted data", {
            companyId,
            email,
            role,
            message,
        });
        if (!companyId || !email || !role) {
            return res.status(400).json({ message: "Missing required fields" });
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return res.status(404).json({ message: "Company not found" });
        }
        // Check if invitation already exists
        const existingInvitation = await prisma_js_1.prisma.companyInvitation.findFirst({
            where: {
                companyId: Number(companyId),
                email,
                status: "PENDING",
            },
        });
        if (existingInvitation) {
            return res.status(400).json({
                message: "An invitation has already been sent to this email",
            });
        }
        // Generate invitation token
        const invitationToken = crypto.randomBytes(32).toString("hex");
        // Set expiration date (7 days from now)
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7);
        // Create invitation
        const invitation = await prisma_js_1.prisma.companyInvitation.create({
            data: {
                company: { connect: { id: Number(companyId) } },
                email,
                role,
                message,
                invitationToken,
                status: "PENDING",
                expiresAt,
            },
        });
        // Send invitation email
        const invitationLink = `${CLIENT_URL}/auth/signup?token=${invitationToken}&email=${email}&role=${role}&company=${company.name}`;
        const emailSubject = `Invitation to join ${company.name}`;
        const emailBody = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invitation to join ${company.name}</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1e293b; /* Dark background */
            color: #e2e8f0; /* Light text */
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #475569;
          }
          .content {
            padding: 30px 20px;
          }
          .footer {
            text-align: center;
            padding: 20px 0;
            font-size: 12px;
            color: #94a3b8;
            border-top: 1px solid #475569;
          }
          h1 {
            color: #8b5cf6; /* Purple accent */
            margin-top: 0;
          }
          .button {
            display: inline-block;
            background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            margin: 20px 0;
            font-weight: 600;
            text-align: center;
          }
          .message-box {
            background-color: #2d3748;
            border-left: 4px solid #8b5cf6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
          }
          .expiry {
            font-size: 14px;
            color: #94a3b8;
            margin-top: 30px;
            text-align: center;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <img src="https://devskills.ee/img/logo/logo-light.svg" alt="${company.name} Logo" height="40">
          </div>
          <div class="content">
            <h1>You've been invited to join ${company.name}</h1>
            <p>Hello,</p>
            <p>You have been invited to join <strong>${company.name}</strong> as a <strong>${role}</strong>.</p>
            ${message
            ? `<div class="message-box"><p><strong>Message:</strong> ${message}</p></div>`
            : ""}
            <p>Click the button below to accept the invitation and create your account:</p>
            <a href="${invitationLink}" class="button">Accept Invitation</a>
            <p class="expiry">This invitation will expire on ${expiresAt.toLocaleDateString()}.</p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} DevSkills Ultimation Studio. All rights reserved.</p>
            <p>If you didn't expect this invitation, you can safely ignore this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
        await (0, sendEmail_js_1.sendEmail)(email, emailSubject, null, emailBody);
        return res.status(201).json({
            message: "Invitation sent successfully",
            invitation,
        });
    }
    catch (error) {
        console.error("Error creating invitation:", error);
        return res.status(500).json({ message: "Server error", error });
    }
};
exports.createInvitation = createInvitation;
// Get all invitations for a company
const getCompanyInvitations = async (req, res) => {
    try {
        const { companyId } = req.params;
        if (!companyId) {
            return res.status(400).json({ message: "Company ID is required" });
        }
        const invitations = await prisma_js_1.prisma.companyInvitation.findMany({
            where: {
                companyId: Number(companyId),
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json(invitations);
    }
    catch (error) {
        console.error("Error getting invitations:", error);
        return res.status(500).json({ message: "Server error", error });
    }
};
exports.getCompanyInvitations = getCompanyInvitations;
// Verify invitation token
const verifyInvitation = async (req, res) => {
    try {
        const { token } = req.params;
        if (!token) {
            return res.status(400).json({ message: "Invitation token is required" });
        }
        const invitation = await prisma_js_1.prisma.companyInvitation.findUnique({
            where: { invitationToken: token },
            include: {
                company: true,
            },
        });
        if (!invitation) {
            return res.status(404).json({ message: "Invitation not found" });
        }
        if (invitation.status !== "PENDING") {
            return res
                .status(400)
                .json({ message: "This invitation has already been used" });
        }
        if (invitation.expiresAt < new Date()) {
            return res.status(400).json({ message: "This invitation has expired" });
        }
        return res.status(200).json({
            valid: true,
            invitation: {
                email: invitation.email,
                role: invitation.role,
                companyName: invitation.company.name,
                companyId: invitation.companyId,
            },
        });
    }
    catch (error) {
        console.error("Error verifying invitation:", error);
        return res.status(500).json({ message: "Server error", error });
    }
};
exports.verifyInvitation = verifyInvitation;
// Accept invitation and create user
const acceptInvitation = async (req, res) => {
    try {
        const { token } = req.params;
        const { firstName, lastName, password, phone } = req.body;
        if (!token || !firstName || !lastName || !password) {
            return res.status(400).json({ message: "Missing required fields" });
        }
        // Get the invitation
        const invitation = await prisma_js_1.prisma.companyInvitation.findUnique({
            where: { invitationToken: token },
            include: {
                company: true,
            },
        });
        if (!invitation) {
            return res.status(404).json({ message: "Invitation not found" });
        }
        if (invitation.status !== "PENDING") {
            return res
                .status(400)
                .json({ message: "This invitation has already been used" });
        }
        if (invitation.expiresAt < new Date()) {
            return res.status(400).json({ message: "This invitation has expired" });
        }
        // Check if user with this email already exists
        const existingUser = await prisma_js_1.prisma.user.findUnique({
            where: { email: invitation.email },
        });
        if (existingUser) {
            // If user exists, just create the company relationship
            await prisma_js_1.prisma.userCompany.create({
                data: {
                    user: { connect: { id: existingUser.id } },
                    company: { connect: { id: invitation.companyId } },
                    role: invitation.role,
                    status: "ACTIVE",
                },
            });
            // Update invitation status
            await prisma_js_1.prisma.companyInvitation.update({
                where: { id: invitation.id },
                data: { status: "APPROVED" },
            });
            return res.status(200).json({
                message: "You have been added to the company",
                userId: existingUser.id,
            });
        }
        // Create new user
        const user = await prisma_js_1.prisma.user.create({
            data: {
                firstName,
                lastName,
                email: invitation.email,
                password, // Note: This should be hashed in a real implementation
                phone,
                barcode: "", // Empty string instead of null
                qrCode: "", // Empty string instead of null
                userRoles: {
                    create: [{ role: invitation.role }],
                },
                companies: {
                    create: {
                        company: { connect: { id: invitation.companyId } },
                        role: invitation.role,
                        status: "ACTIVE",
                    },
                },
            },
        });
        // Update invitation status
        await prisma_js_1.prisma.companyInvitation.update({
            where: { id: invitation.id },
            data: { status: "APPROVED" },
        });
        return res.status(201).json({
            message: "Account created and joined company successfully",
            userId: user.id,
        });
    }
    catch (error) {
        console.error("Error accepting invitation:", error);
        return res.status(500).json({ message: "Server error", error });
    }
};
exports.acceptInvitation = acceptInvitation;
//# sourceMappingURL=companyJoinRequest.controller.js.map