"use strict";
// server/controllers/map.controller.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMapData = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// Get map data with role-based filtering
const getMapData = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { layers, bounds } = req.query;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Get user roles and company
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: {
                userRoles: true,
                companies: {
                    include: {
                        company: true,
                    },
                },
            },
        });
        if (!user) {
            res.status(404).json({ error: "User not found" });
            return;
        }
        const userRoles = user.userRoles.map((ur) => ur.role);
        const companyIds = user.companies.map((uc) => uc.companyId);
        // Parse requested layers
        const requestedLayers = layers ? layers.split(",") : ["all"];
        const mapData = {
            type: "FeatureCollection",
            features: [],
        };
        // Get projects if requested
        if (requestedLayers.includes("projects") ||
            requestedLayers.includes("all")) {
            const projects = await getProjectsForMap(userRoles, companyIds, userId);
            mapData.features.push(...projects);
        }
        // Get workers if requested
        if (requestedLayers.includes("workers") ||
            requestedLayers.includes("all")) {
            const workers = await getWorkersForMap(userRoles, companyIds, userId);
            mapData.features.push(...workers);
        }
        // Get assets if requested
        if (requestedLayers.includes("assets") || requestedLayers.includes("all")) {
            const assets = await getAssetsForMap(userRoles, companyIds, userId);
            mapData.features.push(...assets);
        }
        // Get work locations if requested
        if (requestedLayers.includes("work_locations") ||
            requestedLayers.includes("all")) {
            const workLocations = await getWorkLocationsForMap(userRoles, companyIds);
            mapData.features.push(...workLocations);
        }
        res.json(mapData);
    }
    catch (error) {
        console.error("Error fetching map data:", error);
        res.status(500).json({ error: "Failed to fetch map data" });
    }
};
exports.getMapData = getMapData;
// Get projects based on user role
async function getProjectsForMap(userRoles, companyIds, userId) {
    let projectFilter = {};
    if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
        // Admins can see all projects in their companies
        projectFilter = { companyId: { in: companyIds } };
    }
    else if (userRoles.includes("PROJECTLEADER")) {
        // Project leaders see their assigned projects
        projectFilter = {
            OR: [{ projectLeadId: userId }, { companyId: { in: companyIds } }],
        };
    }
    else if (userRoles.includes("CLIENT")) {
        // Clients see projects they're involved in
        projectFilter = { clientId: { in: companyIds } };
    }
    else {
        // Workers see projects they're assigned to
        projectFilter = {
            OR: [
                { assignedWorkers: { some: { id: userId } } },
                { timeEntries: { some: { userId } } },
            ],
        };
    }
    const projects = await prisma.project.findMany({
        where: projectFilter,
        include: {
            company: true,
            client: true,
        },
    });
    // Return projects with real coordinates from database
    return projects.map((project) => ({
        type: "Feature",
        id: `project-${project.id}`,
        properties: {
            id: project.id,
            name: project.name,
            description: project.description,
            status: project.status,
            type: "project",
            company: project.company.name,
            client: project.client.name,
            startDate: project.startDate,
            endDate: project.endDate,
            budget: project.budget,
            address: project.address,
        },
        geometry: {
            type: "Point",
            coordinates: [
                project.longitude || 24.7536, // Use stored coordinates or default to Tallinn
                project.latitude || 59.437,
            ],
        },
    }));
}
// Get worker locations based on user role
async function getWorkersForMap(userRoles, companyIds, userId) {
    let workerFilter = {};
    if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
        // Admins can see all workers in their companies
        workerFilter = {
            companies: { some: { companyId: { in: companyIds } } },
        };
    }
    else if (userRoles.includes("PROJECTLEADER")) {
        // Project leaders see workers on their projects
        workerFilter = {
            OR: [
                { assignedProjects: { some: { projectLeadId: userId } } },
                { companies: { some: { companyId: { in: companyIds } } } },
            ],
        };
    }
    else if (userRoles.includes("CLIENT")) {
        // Clients see workers on their projects
        workerFilter = {
            assignedProjects: { some: { clientId: { in: companyIds } } },
        };
    }
    else {
        // Workers see only themselves
        workerFilter = { id: userId };
    }
    // Get workers with their latest location data
    const workers = await prisma.user.findMany({
        where: workerFilter,
        include: {
            companies: {
                include: { company: true },
            },
            workerLocations: {
                orderBy: { timestamp: "desc" },
                take: 1,
                include: {
                    project: true,
                    task: true,
                    workLocation: true,
                },
            },
        },
    });
    return workers.map((worker) => {
        const latestLocation = worker.workerLocations[0];
        return {
            type: "Feature",
            id: `worker-${worker.id}`,
            properties: {
                id: worker.id,
                name: `${worker.firstName} ${worker.lastName}`,
                firstName: worker.firstName,
                lastName: worker.lastName,
                avatar: worker.avatar,
                type: "worker",
                status: latestLocation?.isCompliant ? "compliant" : "non-compliant",
                project: latestLocation?.project?.name || "Unassigned project",
                task: latestLocation?.task?.name || "Unassigned task",
                timestamp: latestLocation?.timestamp?.toISOString() || new Date().toISOString(),
                accuracy: latestLocation?.accuracy || 5,
                company: worker.companies[0]?.company?.name || "Unknown company",
            },
            geometry: {
                type: "Point",
                coordinates: [
                    latestLocation?.longitude || 24.7536,
                    latestLocation?.latitude || 59.437,
                ],
            },
        };
    });
}
// Get trackable assets based on user role
async function getAssetsForMap(userRoles, companyIds, userId) {
    let assetFilter = {};
    if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
        // Admins can see all assets in their companies
        assetFilter = { companyId: { in: companyIds } };
    }
    else if (userRoles.includes("PROJECTLEADER")) {
        // Project leaders see assets on their projects
        assetFilter = {
            OR: [
                { companyId: { in: companyIds } },
                {
                    projectAssignments: { some: { project: { projectLeadId: userId } } },
                },
            ],
        };
    }
    else if (userRoles.includes("CLIENT")) {
        // Clients see assets on their projects
        assetFilter = {
            projectAssignments: {
                some: { project: { clientId: { in: companyIds } } },
            },
        };
    }
    else {
        // Workers see assets they're assigned to work with
        assetFilter = {
            projectAssignments: {
                some: {
                    project: {
                        OR: [
                            { assignedWorkers: { some: { id: userId } } },
                            { timeEntries: { some: { userId } } },
                        ],
                    },
                },
            },
        };
    }
    // Get real trackable assets from database
    const assets = await prisma.trackableAsset.findMany({
        where: assetFilter,
        include: {
            company: true,
            currentLocation: true,
            projectAssignments: {
                include: {
                    project: true,
                },
                orderBy: { assignedAt: "desc" },
                take: 1,
            },
        },
    });
    return assets.map((asset) => {
        const currentProject = asset.projectAssignments[0]?.project;
        return {
            type: "Feature",
            id: `asset-${asset.id}`,
            properties: {
                id: asset.id,
                name: asset.name,
                description: asset.description,
                type: "asset",
                assetType: asset.type,
                status: asset.status,
                serialNumber: asset.serialNumber,
                model: asset.model,
                manufacturer: asset.manufacturer,
                company: asset.company.name,
                project: currentProject?.name || "Unassigned project",
                lastUpdate: asset.currentLocation?.lastUpdate || asset.updatedAt,
                isInGeofence: asset.currentLocation?.isInGeofence || true,
            },
            geometry: {
                type: "Point",
                coordinates: [
                    asset.currentLocation?.longitude || 24.7536,
                    asset.currentLocation?.latitude || 59.437,
                ],
            },
        };
    });
}
// Get work locations based on user role
async function getWorkLocationsForMap(userRoles, companyIds) {
    const workLocations = await prisma.workLocation.findMany({
        where: {
            companyId: { in: companyIds },
            isActive: true,
        },
        include: {
            company: true,
        },
    });
    return workLocations.map((location) => ({
        type: "Feature",
        id: `work-location-${location.id}`,
        properties: {
            id: location.id,
            name: location.name,
            description: location.description,
            type: "work_location",
            address: location.address,
            radius: location.radius,
            isDefault: location.isDefault,
            company: location.company.name,
        },
        geometry: {
            type: "Point",
            coordinates: [location.longitude, location.latitude],
        },
    }));
}
//# sourceMappingURL=map.controller.js.map