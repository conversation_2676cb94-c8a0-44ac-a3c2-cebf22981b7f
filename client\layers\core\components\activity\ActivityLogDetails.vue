<!-- client/layers/core/components/activity/ActivityLogDetails.vue -->

<template>
  <!-- Activity Details Dialog -->
  <div
    v-if="activityDialog.isOpen.value"
    class="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4"
    @click="closeDialog"
  >
    <div
      class="bg-white dark:bg-muted-900 rounded-lg shadow-lg max-w-4xl w-full max-h-[85vh] overflow-hidden flex flex-col border border-muted-200 dark:border-muted-700"
      @click.stop
    >
      <!-- Header -->
      <div
        class="flex w-full items-center justify-between p-4 md:p-6 border-b border-muted-200 dark:border-muted-700"
      >
        <div class="flex items-center gap-3">
          <div
            class="w-10 h-10 rounded-full flex items-center justify-center"
            :class="getActivityIconClass(activity?.type)"
          >
            <Icon :name="getActivityIcon(activity?.type)" class="w-5 h-5" />
          </div>
          <div>
            <h2
              class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white"
            >
              {{ activity?.title || "Activity Details" }}
            </h2>
            <p class="text-sm text-muted-500 dark:text-muted-400">
              {{ formatDate(activity?.timestamp) }}
            </p>
          </div>
        </div>
        <BaseButton
          class="icon-md !bg-muted-100 dark:!bg-muted-700"
          @click="closeDialog"
        >
          <Icon name="lucide:x" class="size-4" />
        </BaseButton>
      </div>

      <!-- Content -->
      <div class="p-4 md:p-6 overflow-y-auto flex-1 min-h-0">
        <div v-if="loading" class="flex justify-center py-8">
          <div class="flex items-center justify-center">
            <Icon
              name="solar:refresh-line-duotone"
              class="h-6 w-6 animate-spin text-primary-500"
            />
            <span class="ml-2 text-muted-600 dark:text-muted-400"
              >Loading...</span
            >
          </div>
        </div>

        <div v-else-if="!activity" class="text-center py-8">
          <Icon
            name="solar:danger-triangle-line-duotone"
            class="h-12 w-12 text-muted-400 mx-auto mb-3"
          />
          <BaseText class="text-muted-500 dark:text-muted-400">
            Activity not found or failed to load
          </BaseText>
        </div>

        <!-- Activity content -->
        <div v-else>
          <!-- Main Content Flex Layout -->
          <div class="flex flex-col lg:flex-row gap-6 h-full">
            <!-- Main Content (60% width) -->
            <div
              class="space-y-6"
              style="flex: 1 1 auto"
              :style="{ 'flex-basis': '65%', 'max-width': '65%' }"
            >
              <!-- Description -->
              <div class="mb-6">
                <BaseHeading
                  as="h4"
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-white mb-2"
                >
                  Description
                </BaseHeading>
                <BaseParagraph class="text-muted-600 dark:text-muted-300">
                  {{ activity.description }}
                </BaseParagraph>
              </div>

              <!-- Changes -->
              <div v-if="hasChanges" class="mb-6">
                <BaseHeading
                  as="h4"
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-white mb-2"
                >
                  Changes Made
                </BaseHeading>
                <div class="space-y-4">
                  <div
                    v-for="(change, field) in formattedChanges"
                    :key="field"
                    class="rounded-lg border border-muted-200 bg-muted-50 p-4 dark:border-muted-700 dark:bg-muted-800"
                  >
                    <div
                      class="mb-3 text-sm font-medium text-muted-700 dark:text-muted-300"
                    >
                      {{ formatFieldName(String(field)) }}
                    </div>
                    <div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
                      <div>
                        <div
                          class="mb-2 text-xs font-medium text-red-600 dark:text-red-400"
                        >
                          Previous Value
                        </div>
                        <div
                          class="rounded bg-red-50 p-3 text-sm text-red-800 dark:bg-red-900/20 dark:text-red-200"
                        >
                          {{ formatValue(change.old) }}
                        </div>
                      </div>
                      <div>
                        <div
                          class="mb-2 text-xs font-medium text-green-600 dark:text-green-400"
                        >
                          New Value
                        </div>
                        <div
                          class="rounded bg-green-50 p-3 text-sm text-green-800 dark:bg-green-900/20 dark:text-green-200"
                        >
                          {{ formatValue(change.new) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Related Data -->
              <div v-if="activity.metadata" class="mb-6">
                <BaseHeading
                  as="h4"
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-white mb-2"
                >
                  Related Data
                </BaseHeading>
                <BaseCard class="p-4">
                  <pre
                    class="text-xs text-muted-500 dark:text-muted-400 overflow-auto max-h-60"
                    >{{ formatJson(activity.metadata) }}</pre
                  >
                </BaseCard>
              </div>

              <!-- Tags -->
              <div
                v-if="activity.tags && activity.tags.length > 0"
                class="mb-6"
              >
                <BaseHeading
                  as="h4"
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-white mb-2"
                >
                  Tags
                </BaseHeading>
                <div class="flex flex-wrap gap-2">
                  <BaseTag
                    v-for="(tag, index) in activity.tags"
                    :key="`tag-${index}`"
                    variant="none"
                    size="sm"
                    :style="getTagBadgeStyle(index)"
                  >
                    {{ tag }}
                  </BaseTag>
                </div>
              </div>
            </div>

            <!-- Sidebar (40% width) -->
            <div
              class="space-y-4"
              style="flex: 1 1 auto"
              :style="{ 'flex-basis': '35%', 'max-width': '35%' }"
            >
              <!-- Activity Information -->
              <BaseCard class="p-4">
                <BaseHeading
                  as="h4"
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-white mb-4"
                >
                  Activity Information
                </BaseHeading>
                <div class="space-y-3">
                  <div class="flex justify-between items-center">
                    <BaseText size="sm" class="text-muted-500">Type</BaseText>
                    <BaseTag
                      variant="none"
                      size="sm"
                      :style="getActivityTypeBadgeStyle(activity.type)"
                    >
                      {{ formatActivityType(activity.type) }}
                    </BaseTag>
                  </div>
                  <div class="flex justify-between">
                    <BaseText size="sm" class="text-muted-500"
                      >Timestamp</BaseText
                    >
                    <BaseText
                      size="sm"
                      class="text-muted-800 dark:text-muted-100"
                    >
                      {{ formatDate(activity.timestamp) }}
                    </BaseText>
                  </div>
                  <div class="flex justify-between">
                    <BaseText size="sm" class="text-muted-500">Module</BaseText>
                    <BaseText
                      size="sm"
                      class="text-muted-800 dark:text-muted-100"
                    >
                      {{ activity.module || "-" }}
                    </BaseText>
                  </div>
                  <div class="flex justify-between">
                    <BaseText size="sm" class="text-muted-500"
                      >IP Address</BaseText
                    >
                    <BaseText
                      size="sm"
                      class="text-muted-800 dark:text-muted-100"
                    >
                      {{ activity.ipAddress || "-" }}
                    </BaseText>
                  </div>
                  <div class="flex justify-between">
                    <BaseText size="sm" class="text-muted-500"
                      >Browser</BaseText
                    >
                    <BaseText
                      size="sm"
                      class="text-muted-800 dark:text-muted-100"
                    >
                      {{ activity.browser || "-" }}
                    </BaseText>
                  </div>
                  <div class="flex justify-between">
                    <BaseText size="sm" class="text-muted-500">OS</BaseText>
                    <BaseText
                      size="sm"
                      class="text-muted-800 dark:text-muted-100"
                    >
                      {{ activity.os || "-" }}
                    </BaseText>
                  </div>
                </div>
              </BaseCard>

              <!-- User Information -->
              <BaseCard v-if="activity.user" class="p-4">
                <BaseHeading
                  as="h4"
                  size="sm"
                  weight="medium"
                  class="text-muted-800 dark:text-white mb-4"
                >
                  User Information
                </BaseHeading>
                <div class="flex items-center mb-4">
                  <BaseAvatar
                    :src="activity.user.avatar"
                    :alt="getUserName(activity.user)"
                    size="md"
                    class="mr-3"
                  />
                  <div>
                    <div class="mb-1">
                      <BaseText
                        size="sm"
                        weight="medium"
                        class="text-muted-800 dark:text-muted-100"
                      >
                        {{ getUserName(activity.user) }}
                      </BaseText>
                    </div>
                    <div>
                      <BaseText size="xs" class="text-muted-500">
                        {{ activity.user.role || "-" }}
                      </BaseText>
                    </div>
                  </div>
                </div>
                <BaseButton
                  variant="primary"
                  class="w-full"
                  @click="viewUserProfile"
                >
                  <Icon name="solar:user-line-duotone" class="h-4 w-4 mr-1" />
                  View User Profile
                </BaseButton>
              </BaseCard>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div
        class="flex w-full items-center justify-between p-4 md:px-6 border-t border-muted-200 dark:border-muted-700"
      >
        <BaseButton color="muted" @click="closeDialog">Close</BaseButton>

        <div class="flex items-center gap-2">
          <BaseButton
            v-if="activity && activity.type === 'error'"
            variant="danger"
            @click="$emit('resolve', activity)"
          >
            <Icon name="solar:check-circle-line-duotone" class="h-4 w-4 mr-1" />
            Mark as Resolved
          </BaseButton>

          <BaseButton variant="primary" @click="exportActivityLog">
            <Icon name="solar:download-line-duotone" class="h-4 w-4 mr-1" />
            Export
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { format as formatDateFns } from "date-fns";
import { useActivityDialog } from "../../composables/useActivityDialog";
import { useActivityStore } from "../../stores/useActivityStore";

// Remove props since we're using the composable directly
const emit = defineEmits(["resolve"]);

const toaster = useNuiToasts();
const activityDialog = useActivityDialog();
const activityStore = useActivityStore();
const loading = ref(false);
const activity = ref<any>(null);

// Computed property for the current activity ID
const activityId = computed(() => activityDialog.selectedActivityId.value);

// Close dialog function
const closeDialog = () => {
  activityDialog.closeDialog();
};

// Date formatting function
const formatDate = (timestamp: Date | string) => {
  if (!timestamp) return "N/A";

  try {
    const date =
      typeof timestamp === "string" ? new Date(timestamp) : timestamp;
    return formatDateFns(date, "MMM d, yyyy HH:mm:ss");
  } catch (error) {
    return "N/A";
  }
};

const getUserName = (user: any) => {
  if (!user) return "Unknown User";

  return (
    `${user.firstName} ${user.lastName}`.trim() ||
    user.username ||
    "Unknown User"
  );
};

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    login: "solar:login-3-line-duotone",
    logout: "solar:logout-3-line-duotone",
    create: "solar:add-circle-line-duotone",
    update: "solar:pen-line-duotone",
    delete: "solar:trash-bin-trash-line-duotone",
    share: "solar:share-line-duotone",
    comment: "solar:chat-round-line-duotone",
    upload: "solar:upload-line-duotone",
    download: "solar:download-line-duotone",
    approve: "solar:check-circle-line-duotone",
    reject: "solar:close-circle-line-duotone",
    assign: "solar:user-plus-line-duotone",
    complete: "solar:check-square-line-duotone",
    payment: "solar:dollar-line-duotone",
    error: "solar:danger-triangle-line-duotone",
    security: "solar:shield-check-line-duotone",
    settings: "solar:settings-line-duotone",
    notification: "solar:bell-line-duotone",
  };

  return iconMap[type] || "solar:activity-line-duotone";
};

const getActivityIconClass = (type: string) => {
  const classMap: Record<string, string> = {
    login:
      "bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400",
    logout: "bg-muted-100 text-muted-500 dark:bg-muted-700 dark:text-muted-400",
    create:
      "bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400",
    update: "bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400",
    delete:
      "bg-danger-100 text-danger-500 dark:bg-danger-500/20 dark:text-danger-400",
    share:
      "bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400",
    comment: "bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400",
    upload:
      "bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400",
    download:
      "bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400",
    approve:
      "bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400",
    reject:
      "bg-danger-100 text-danger-500 dark:bg-danger-500/20 dark:text-danger-400",
    assign:
      "bg-warning-100 text-warning-500 dark:bg-warning-500/20 dark:text-warning-400",
    complete:
      "bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400",
    payment:
      "bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400",
    error:
      "bg-danger-100 text-danger-500 dark:bg-danger-500/20 dark:text-danger-400",
    security:
      "bg-warning-100 text-warning-500 dark:bg-warning-500/20 dark:text-warning-400",
    settings:
      "bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400",
    notification:
      "bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400",
  };

  return (
    classMap[type] ||
    "bg-muted-100 text-muted-500 dark:bg-muted-700 dark:text-muted-400"
  );
};

const getActivityTypeBadgeStyle = (type: string) => {
  const styleMap: Record<string, any> = {
    login: {
      backgroundColor: "#dbeafe",
      color: "#1e40af",
      border: "1px solid #93c5fd",
    },
    logout: {
      backgroundColor: "#f3f4f6",
      color: "#374151",
      border: "1px solid #d1d5db",
    },
    create: {
      backgroundColor: "#dcfce7",
      color: "#166534",
      border: "1px solid #86efac",
    },
    update: {
      backgroundColor: "#dbeafe",
      color: "#1e40af",
      border: "1px solid #93c5fd",
    },
    delete: {
      backgroundColor: "#fee2e2",
      color: "#dc2626",
      border: "1px solid #fca5a5",
    },
    share: {
      backgroundColor: "#f3e8ff",
      color: "#7c3aed",
      border: "1px solid #c4b5fd",
    },
    comment: {
      backgroundColor: "#e0e7ff",
      color: "#4338ca",
      border: "1px solid #a5b4fc",
    },
    upload: {
      backgroundColor: "#dcfce7",
      color: "#166534",
      border: "1px solid #86efac",
    },
    download: {
      backgroundColor: "#dbeafe",
      color: "#1e40af",
      border: "1px solid #93c5fd",
    },
    approve: {
      backgroundColor: "#dcfce7",
      color: "#166534",
      border: "1px solid #86efac",
    },
    reject: {
      backgroundColor: "#fee2e2",
      color: "#dc2626",
      border: "1px solid #fca5a5",
    },
    assign: {
      backgroundColor: "#fef3c7",
      color: "#92400e",
      border: "1px solid #fcd34d",
    },
    complete: {
      backgroundColor: "#dcfce7",
      color: "#166534",
      border: "1px solid #86efac",
    },
    payment: {
      backgroundColor: "#fef3c7",
      color: "#92400e",
      border: "1px solid #fcd34d",
    },
    error: {
      backgroundColor: "#fee2e2",
      color: "#dc2626",
      border: "1px solid #fca5a5",
    },
    security: {
      backgroundColor: "#fed7aa",
      color: "#c2410c",
      border: "1px solid #fdba74",
    },
    settings: {
      backgroundColor: "#f3f4f6",
      color: "#374151",
      border: "1px solid #d1d5db",
    },
    notification: {
      backgroundColor: "#f3e8ff",
      color: "#7c3aed",
      border: "1px solid #c4b5fd",
    },
  };

  return (
    styleMap[type] || {
      backgroundColor: "#f3f4f6",
      color: "#374151",
      border: "1px solid #d1d5db",
    }
  );
};

const formatActivityType = (type: string) => {
  if (!type) return "Unknown";

  // Convert camelCase or snake_case to Title Case
  return type
    .replace(/([A-Z])/g, " $1") // Insert a space before all uppercase letters
    .replace(/_/g, " ") // Replace underscores with spaces
    .replace(/^\w/, (c) => c.toUpperCase()); // Capitalize the first letter
};

const getTagBadgeStyle = (index: number) => {
  const styleMap = [
    {
      backgroundColor: "#dbeafe",
      color: "#1e40af",
      border: "1px solid #93c5fd",
    }, // blue
    {
      backgroundColor: "#dcfce7",
      color: "#166534",
      border: "1px solid #86efac",
    }, // green
    {
      backgroundColor: "#f3e8ff",
      color: "#7c3aed",
      border: "1px solid #c4b5fd",
    }, // purple
    {
      backgroundColor: "#fef3c7",
      color: "#92400e",
      border: "1px solid #fcd34d",
    }, // yellow
    {
      backgroundColor: "#fee2e2",
      color: "#dc2626",
      border: "1px solid #fca5a5",
    }, // red
    {
      backgroundColor: "#e0e7ff",
      color: "#4338ca",
      border: "1px solid #a5b4fc",
    }, // indigo
    {
      backgroundColor: "#fce7f3",
      color: "#be185d",
      border: "1px solid #f9a8d4",
    }, // pink
    {
      backgroundColor: "#f3f4f6",
      color: "#374151",
      border: "1px solid #d1d5db",
    }, // gray
  ];
  return styleMap[index % styleMap.length];
};

const formatJson = (data: any) => {
  if (!data) return "";

  try {
    return JSON.stringify(data, null, 2);
  } catch (error) {
    return String(data);
  }
};

// Computed properties for enhanced activity details
const hasChanges = computed(() => {
  return (
    activity.value?.changes && Object.keys(activity.value.changes).length > 0
  );
});

const formattedChanges = computed(() => {
  if (!activity.value?.changes) return {};
  return activity.value.changes;
});

const formatFieldName = (field: string) => {
  // Convert camelCase or snake_case to Title Case
  return field
    .replace(/([A-Z])/g, " $1") // Insert a space before all uppercase letters
    .replace(/_/g, " ") // Replace underscores with spaces
    .replace(/^\w/, (c) => c.toUpperCase()); // Capitalize the first letter
};

const formatValue = (value: any) => {
  if (value === null || value === undefined) return "-";
  if (typeof value === "boolean") return value ? "Yes" : "No";
  if (typeof value === "object") return JSON.stringify(value, null, 2);
  if (typeof value === "string" && value.trim() === "") return "(Empty)";
  return String(value);
};

const viewUserProfile = () => {
  if (!activity.value || !activity.value.user) return;

  // In a real implementation, this would navigate to the user profile
  // For now, we'll just show a toast message
  toaster.add({
    title: "User Profile",
    description: `Viewing profile for ${getUserName(activity.value.user)}`,
    icon: "solar:user-line-duotone",
    progress: true,
  });
};

const exportActivityLog = () => {
  if (!activity.value) return;

  // In a real implementation, this would export the activity log
  // For now, we'll just show a toast message
  toaster.add({
    title: "Export Complete",
    description: "Activity log exported successfully",
    icon: "solar:download-line-duotone",
    progress: true,
  });
};

// Fetch activity data when activityId changes
const fetchActivity = async () => {
  if (!activityId.value) return;

  loading.value = true;

  try {
    const result = await activityStore.fetchActivityById(
      parseInt(activityId.value)
    );
    activity.value = result;
  } catch (error) {
    console.error("Error fetching activity:", error);
    activity.value = null;
    toaster.add({
      title: "Error",
      description: "Failed to load activity details",
      icon: "solar:danger-triangle-line-duotone",
      progress: true,
    });
  } finally {
    loading.value = false;
  }
};

watch(
  () => activityDialog.selectedActivityId.value,
  () => {
    if (
      activityDialog.isOpen.value &&
      activityDialog.selectedActivityId.value
    ) {
      fetchActivity();
    }
  }
);

watch(
  () => activityDialog.isOpen.value,
  (isOpen) => {
    if (isOpen && activityDialog.selectedActivityId.value) {
      fetchActivity();
    }
  }
);

onMounted(() => {
  if (activityDialog.isOpen.value && activityDialog.selectedActivityId.value) {
    fetchActivity();
  }
});
</script>
