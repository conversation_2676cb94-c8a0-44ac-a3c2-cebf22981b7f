"use strict";
/**
 * This script finds and updates direct PrismaClient instantiations in the codebase
 * to use the centralized prisma instance from lib/prisma.ts
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const url_1 = require("url");
const __dirname = path_1.default.dirname((0, url_1.fileURLToPath)(import.meta.url));
const rootDir = path_1.default.join(__dirname, "..");
// Files to exclude from processing
const excludeDirs = ["node_modules", "dist", ".git", "prisma/generated"];
// Pattern to match direct PrismaClient instantiations
const directInstantiationPattern = /import\s+{\s*PrismaClient\s*}\s+from\s+['"]@prisma\/client['"]/;
const newInstancePattern = /const\s+prisma\s*=\s*new\s+PrismaClient/;
const tempPrismaPattern = /const\s+tempPrisma\s*=\s*new\s+PrismaClient/;
// Function to process a file
function processFile(filePath) {
    // Only process TypeScript files
    if (!filePath.endsWith(".ts") && !filePath.endsWith(".js")) {
        return;
    }
    // Skip lib/prisma.ts itself
    if (filePath.includes("lib/prisma.ts") ||
        filePath.includes("lib/prisma.js")) {
        return;
    }
    try {
        const content = fs_1.default.readFileSync(filePath, "utf8");
        // Check if file contains direct PrismaClient instantiation
        if (directInstantiationPattern.test(content) &&
            newInstancePattern.test(content)) {
            // Skip files with temporary PrismaClient instances (like in database.controller.ts)
            if (tempPrismaPattern.test(content)) {
                console.log(`Skipping file with temporary PrismaClient instance: ${filePath}`);
                return;
            }
            console.log(`Found direct PrismaClient instantiation in: ${filePath}`);
            // Calculate relative path to lib/prisma.js
            const relativePath = path_1.default.relative(path_1.default.dirname(filePath), path_1.default.join(rootDir, "lib"));
            const normalizedPath = relativePath.replace(/\\/g, "/");
            // Replace import statement
            let updatedContent = content.replace(directInstantiationPattern, `import { prisma } from '${normalizedPath}/prisma.js'`);
            // Remove PrismaClient instantiation - handle both with and without parameters
            updatedContent = updatedContent.replace(/const\s+prisma\s*=\s*new\s+PrismaClient\([\s\S]*?\);/g, "// Using centralized prisma instance from lib/prisma.js");
            // Also handle the case without parameters
            updatedContent = updatedContent.replace(/const\s+prisma\s*=\s*new\s+PrismaClient\s*\(\s*\)\s*;/g, "// Using centralized prisma instance from lib/prisma.js");
            // Handle the simplest case
            updatedContent = updatedContent.replace(/const\s+prisma\s*=\s*new\s+PrismaClient\s*;/g, "// Using centralized prisma instance from lib/prisma.js");
            // Write updated content back to file
            fs_1.default.writeFileSync(filePath, updatedContent, "utf8");
            console.log(`Updated: ${filePath}`);
        }
    }
    catch (error) {
        console.error(`Error processing file ${filePath}:`, error);
    }
}
// Function to recursively walk directories
function walkDir(dir) {
    const files = fs_1.default.readdirSync(dir);
    files.forEach((file) => {
        const filePath = path_1.default.join(dir, file);
        const stat = fs_1.default.statSync(filePath);
        if (stat.isDirectory()) {
            // Skip excluded directories
            if (excludeDirs.some((excludeDir) => filePath.includes(excludeDir))) {
                return;
            }
            walkDir(filePath);
        }
        else {
            processFile(filePath);
        }
    });
}
// Start processing from the controllers directory
console.log("Starting to update PrismaClient imports...");
walkDir(path_1.default.join(rootDir, "controllers"));
console.log("Finished updating PrismaClient imports.");
//# sourceMappingURL=update-prisma-imports.js.map