"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../../utils/route-helpers.js");
const applicationController_js_1 = require("../../controllers/recruitment/applicationController.js");
const router = express_1.default.Router();
// All routes require authentication
router.use(route_helpers_js_1.auth);
// Application routes
router.get("/company/:companyId", (0, route_helpers_js_1.wrapController)(applicationController_js_1.getApplications));
router.get("/company/:companyId/status/:status", (0, route_helpers_js_1.wrapController)(applicationController_js_1.getApplicationsByStatus));
router.get("/:id", (0, route_helpers_js_1.wrapController)(applicationController_js_1.getApplication));
router.post("/", (0, route_helpers_js_1.wrapController)(applicationController_js_1.createApplication));
router.patch("/:id/status", (0, route_helpers_js_1.wrapController)(applicationController_js_1.updateApplicationStatus));
router.patch("/:id/score", (0, route_helpers_js_1.wrapController)(applicationController_js_1.scoreApplication));
router.patch("/:id/shortlist", (0, route_helpers_js_1.wrapController)(applicationController_js_1.shortlistApplication));
router.patch("/:id/reject", (0, route_helpers_js_1.wrapController)(applicationController_js_1.rejectApplication));
exports.default = router;
//# sourceMappingURL=applications.js.map