navigation:
  core: Core
  dashboard: Dashboard
  overview: Overview
  system_health: System Health
  activity_logs: Activity Logs

dashboard:
  welcome_message: Welcome to your comprehensive dashboard overview
  refresh: Refresh
  settings: Settings
  budget_remaining: Budget Remaining
  total_employees: Total Employees
  active_projects: Active Projects
  unread_messages: Unread Messages
  project_status: Project Status
  budget_allocation: Budget Allocation
  employee_activity: Employee Activity
  recent_activities: Recent Activities
  view_all: View All

waitlist:
  meta:
    title: Join the CoManager Beta Waitlist
    description: Sign up for early access to CoManager - the all-in-one company management platform.
  tagline: BETA PROGRAM
  title: Join Our Waitlist
  subtitle: Be among the first to experience CoManager when we launch our beta program.
  form:
    email_placeholder: Enter your email address
    submit: Join Waitlist
    privacy_notice: We'll never share your email with anyone else.
    success:
      title: Registration Successful
      description: Please check your inbox for a confirmation email.
    error:
      title: Registration Error
      general: There was an error processing your request. Please try again.
      invalid_email: Please enter a valid email address.
      already_registered: This email is already registered in our waitlist.
      server: We're experiencing technical difficulties. Please try again later.
      email_sending_failed: Your registration was processed, but we couldn't send a confirmation email. Please contact support.
  angel:
    tagline: ANGEL INVESTORS
    title: Interested in Investing?
    description: Join our angel investor program and be part of our growth journey from the beginning.
    cta: Contact Us

landing:
  crowdfunding:
    tagline: CROWDFUNDING CAMPAIGN
    title: Help Us Build the Future
    progress:
      title: Funding Progress
      subtitle: Join our supporters and help us reach our goal.
      funded: funded
    packages:
      silver:
        name: Silver
      gold:
        name: Gold
      platinum:
        name: Platinum
      popular: POPULAR

system_health:
  title: System Health
  overview: System Health Overview
  last_updated: "Last updated: {time}"
  refresh_all: Refresh All Data
  loading: Loading system health data...
  error: Failed to load system health data
  kpi:
    system_health: System Health
    system_health_description: Overall system performance and stability
    active_users: Active Users
    active_users_description: Users with active sessions in the last 24 hours
    api_requests: API Requests
    api_requests_description: Total API requests processed in the last hour
    system_alerts: System Alerts
    system_alerts_description: Active system alerts requiring attention
    trend_up: "+{value}%"
    trend_down: "{value}%"
    trend_neutral: No change
  services:
    title: Service Status
    description: Monitor the health and availability of all system services
    refresh: Refresh Services
    all_operational: All services are operational
    issues_detected: "{count} service(s) need attention"
    status:
      operational: Operational
      degraded: Degraded
      maintenance: Maintenance
      down: Down
      unknown: Unknown
      critical: Critical
    metrics:
      response_time: "Response: {time}ms"
      uptime: "Uptime: {percentage}%"
      checking: Checking...
  resources:
    title: Resource Usage
    description: Monitor system resource consumption and performance
    refresh: Refresh Resources
    cpu:
      title: CPU Usage
      cores: "{count} cores"
      load: "Load: {value}"
    memory:
      title: Memory Usage
      used: "Used: {used}"
      total: "Total: {total}"
    disk:
      title: Disk Usage
      used: "Used: {used}"
      total: "Total: {total}"
      free: "Free: {free}"
    network:
      title: Network Activity
      download: "Download: {speed}/s"
      upload: "Upload: {speed}/s"
  performance:
    title: Performance Metrics
    description: Track key performance indicators and response times
    refresh: Refresh Metrics
    api_response_time: API Response Time
    db_query_time: Database Query Time
    error_rate: Error Rate
    page_load_time: Page Load Time
    trend_better: "{value}% better"
    trend_worse: "{value}% worse"
    units:
      milliseconds: ms
      percentage: "%"
      seconds: s
  database:
    title: Database Health
    description: Monitor database performance and connectivity
    connection_pool: Connection Pool
    query_cache_hit_rate: Query Cache Hit Rate
    avg_query_time: Avg. Query Time
    slow_queries: Slow Queries
    failed_queries: Failed Queries
    database_size: Database Size
    last_backup: Last Backup
    backup_database: Backup Database
    database_management: Database Management
  alerts:
    title: System Alerts
    description: Active system alerts and notifications
    view_all: View All
    refresh: Refresh
    no_alerts: No active alerts at this time
    no_alerts_description: Your system is running smoothly with no active alerts
    severity:
      low: Low
      medium: Medium
      high: High
      critical: Critical
    status:
      active: Active
      acknowledged: Acknowledged
      resolved: Resolved
      closed: Closed
    actions:
      acknowledge: Acknowledge
      resolve: Resolve
      view_details: View Details
    table:
      severity: Severity
      alert: Alert
      source: Source
      time: Time
      status: Status
  messages:
    refresh_success: System health data refreshed successfully
    refresh_error: Failed to refresh system health data
    service_refresh_success: Service status refreshed successfully
    service_refresh_error: Failed to refresh service status
    resource_refresh_success: Resource usage refreshed successfully
    resource_refresh_error: Failed to refresh resource usage
    performance_refresh_success: Performance metrics refreshed successfully
    performance_refresh_error: Failed to refresh performance metrics
    alerts_refresh_success: System alerts refreshed successfully
    alerts_refresh_error: Failed to refresh system alerts
  empty_states:
    no_data: No data available
    no_services: No services configured
    no_metrics: No performance metrics available
    no_alerts: No active alerts
    loading: Loading...
    error: Error loading data
  time:
    just_now: Just now
    minutes_ago: "{count} minutes ago"
    hours_ago: "{count} hours ago"
    days_ago: "{count} days ago"
    never: Never
  common:
    na: N/A
    success: Success
    error: Error
    failed: Failed
    refresh: Refresh
    last_updated: "Last updated: {time}"
    vs_previous: vs previous
    used: Used
    total: Total
    cores: Cores
    load_avg: Load Avg
    download: Download
    upload: Upload
    bytes: Bytes
    kb: KB
    mb: MB
    gb: GB
    tb: TB
    pb: PB
    eb: EB
    zb: ZB
    yb: YB

  # Service names and descriptions
  service_names:
    api_server: API Server
    authentication: Authentication
    database: Database
    email_service: Email Service
    file_storage: File Storage

  service_descriptions:
    api_server: REST API endpoints
    authentication: User authentication service
    database: Primary database server
    email_service: Notification emails
    file_storage: Document storage service

  # Performance metric labels
  metrics:
    api_response_time: API Response Time
    database_query_time: Database Query Time
    error_rate: Error Rate
    page_load_time: Page Load Time

  # Chart labels
  chart_labels:
    api_response_time_ms: "API Response Time (ms)"
    db_query_time_ms: "DB Query Time (ms)"
    page_load_time_ms: "Page Load Time (ms)"

# Activity page translations
activity:
  title: Activity Logs
  description: Track and monitor system and user activities

  actions:
    export: Export Logs
    refresh: Refresh

  metrics:
    total_activities: Total Activities
    user_logins: User Logins
    data_changes: Data Changes
    system_errors: System Errors
    last_24h: Last 24 hours

  chart:
    title: Activity Trends
    last_updated: Last updated
    loading: Loading chart...
    labels:
      7_days_ago: 7 days ago
      6_days_ago: 6 days ago
      5_days_ago: 5 days ago
      4_days_ago: 4 days ago
      3_days_ago: 3 days ago
      2_days_ago: 2 days ago
      yesterday: Yesterday
      today: Today
    datasets:
      user_logins: User Logins
      data_changes: Data Changes
      system_errors: System Errors
      activity_count: Activity Count

  logs:
    title: Activity Logs
    system: System
    unknown: Unknown
    sort:
      title: Sort By
      newest_first: Newest First
      oldest_first: Oldest First
      type: Type
      user: User
      module: Module
    columns:
      activity: Activity
      user: User
      module: Module
      time: Time
      status: Status
      actions: Actions
    status:
      success: Success
      error: Error
      info: Info
    actions:
      view: View
      export: Export
      refresh: Refresh
      clear_filters: Clear Filters
    loading: Loading activities...
    refreshed: Activity logs refreshed successfully
    refresh_failed: Failed to refresh activity logs
    fetch_failed: Failed to load activity logs
    exported: Activity logs exported successfully
    resolved: "Activity '{title}' resolved"
    empty:
      title: No Activities Found
      description: There are no activity logs to display. Activities will appear here as users interact with the system.
    error:
      title: Failed to Load Activities
      retry: Try Again
    pagination:
      showing: Showing
      of: of
      previous: Previous
      next: Next

  filters:
    title: Filters
    search_placeholder: Search activities...
    filter_by_type: Filter by Type
    filter_by_user: Filter by User
    filter_by_date: Filter by Date
    activity_type: Activity Type
    all_types: All Types
    all_users: All Users
    all_dates: All Dates
    results_found: results found
    clear_all: Clear All
    types:
      login: User Logins
      data_change: Data Changes
      system_error: System Errors
    dates:
      today: Today
      yesterday: Yesterday
      last_7_days: Last 7 Days
      last_30_days: Last 30 Days
      this_month: This Month
      last_month: Last Month

  # Activity Details Dialog
  details:
    title: Activity Details
    loading: Loading...
    not_found: Activity not found or failed to load
    close: Close
    export: Export
    mark_resolved: Mark as Resolved
    view_user_profile: View User Profile

    sections:
      description: Description
      changes_made: Changes Made
      related_data: Related Data
      tags: Tags
      activity_information: Activity Information
      user_information: User Information

    fields:
      type: Type
      timestamp: Timestamp
      module: Module
      ip_address: IP Address
      browser: Browser
      os: OS
      role: Role

    changes:
      previous_value: Previous Value
      new_value: New Value

    values:
      empty: (Empty)
      yes: "Yes"
      no: "No"
      unknown_user: Unknown User

    notifications:
      export_success: Activity log exported successfully
      load_error: Failed to load activity details
      user_profile_view: "Viewing profile for {name}"

  # Activity Types
  types:
    login: Login
    logout: Logout
    password_change: Password Change
    account_locked: Account Locked
    user_create: User Created
    user_update: User Updated
    user_delete: User Deleted
    user_role_change: Role Changed
    create: Created
    update: Updated
    delete: Deleted
    export: Exported
    import: Imported
    system_start: System Started
    system_stop: System Stopped
    backup_create: Backup Created
    backup_restore: Backup Restored
    security_violation: Security Violation
    permission_denied: Permission Denied
    suspicious_activity: Suspicious Activity
    project_create: Project Created
    project_update: Project Updated
    deal_create: Deal Created
    deal_update: Deal Updated
    payment_processed: Payment Processed
    email_sent: Email Sent
    file_upload: File Uploaded
    file_download: File Downloaded
    file_delete: File Deleted
    settings_update: Settings Updated
    integration_enabled: Integration Enabled
    integration_disabled: Integration Disabled
    message_sent: Message Sent
    notification_sent: Notification Sent
    other: Other
