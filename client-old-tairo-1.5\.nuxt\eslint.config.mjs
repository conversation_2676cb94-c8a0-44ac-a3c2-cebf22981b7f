// ESLint config generated by Nuxt
/// <reference path="./eslint-typegen.d.ts" />
/* eslint-disable */
// @ts-nocheck

import typegen from '../node_modules/.pnpm/eslint-typegen@2.1.0_eslint@8.57.1/node_modules/eslint-typegen/dist/index.mjs';
import { createConfigForNuxt, defineFlatConfigs, resolveOptions } from '../node_modules/.pnpm/@nuxt+eslint-config@1.2.0_@vue+compiler-sfc@3.5.13_eslint@8.57.1_typescript@5.8.2/node_modules/@nuxt/eslint-config/dist/flat.mjs';
import { fileURLToPath } from 'url';

const r = (...args) => fileURLToPath(new URL(...args, import.meta.url))

export { defineFlatConfigs }

export const options = resolveOptions({
  features: {
  "standalone": true
},
  dirs: {
    pages: ["pages", "layers/tairo/pages", "node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/pages", "layers/accounting/pages", "layers/budget/pages", "layers/companies/pages", "layers/core/pages", "layers/hr/pages", "layers/production/pages", "layers/recruitment/pages", "layers/tairo-layout-sidebar/pages", "layers/landing/pages", "layers/tairo-layout-iconnav/pages"],
    composables: ["composables", "utils", "layers/tairo/composables", "layers/tairo/utils", "node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/composables", "node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/utils", "layers/accounting/composables", "layers/accounting/utils", "layers/budget/composables", "layers/budget/utils", "layers/companies/composables", "layers/companies/utils", "layers/core/composables", "layers/core/utils", "layers/hr/composables", "layers/hr/utils", "layers/production/composables", "layers/production/utils", "layers/recruitment/composables", "layers/recruitment/utils", "layers/tairo-layout-sidebar/composables", "layers/tairo-layout-sidebar/utils", "layers/landing/composables", "layers/landing/utils", "layers/tairo-layout-iconnav/composables", "layers/tairo-layout-iconnav/utils"],
    components: ["components", "layers/tairo/components", "node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base", "node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon", "node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form", "layers/accounting/components", "layers/budget/components", "layers/companies/components", "layers/core/components", "layers/hr/components", "layers/production/components", "layers/recruitment/components", "layers/tairo-layout-sidebar/components", "layers/landing/components", "layers/tairo-layout-iconnav/components"],
    componentsPrefixed: [],
    layouts: ["layouts", "layers/tairo/layouts", "node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/layouts", "layers/accounting/layouts", "layers/budget/layouts", "layers/companies/layouts", "layers/core/layouts", "layers/hr/layouts", "layers/production/layouts", "layers/recruitment/layouts", "layers/tairo-layout-sidebar/layouts", "layers/landing/layouts", "layers/tairo-layout-iconnav/layouts"],
    plugins: ["plugins", "layers/tairo/plugins", "node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/plugins", "layers/accounting/plugins", "layers/budget/plugins", "layers/companies/plugins", "layers/core/plugins", "layers/hr/plugins", "layers/production/plugins", "layers/recruitment/plugins", "layers/tairo-layout-sidebar/plugins", "layers/landing/plugins", "layers/tairo-layout-iconnav/plugins"],
    middleware: ["middleware", "layers/tairo/middleware", "node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/middleware", "layers/accounting/middleware", "layers/budget/middleware", "layers/companies/middleware", "layers/core/middleware", "layers/hr/middleware", "layers/production/middleware", "layers/recruitment/middleware", "layers/tairo-layout-sidebar/middleware", "layers/landing/middleware", "layers/tairo-layout-iconnav/middleware"],
    modules: ["modules", "layers/tairo/modules", "node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/modules", "layers/accounting/modules", "layers/budget/modules", "layers/companies/modules", "layers/core/modules", "layers/hr/modules", "layers/production/modules", "layers/recruitment/modules", "layers/tairo-layout-sidebar/modules", "layers/landing/modules", "layers/tairo-layout-iconnav/modules"],
    servers: [],
    root: [],
    src: ["", "layers/tairo", "node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt", "layers/accounting", "layers/budget", "layers/companies", "layers/core", "layers/hr", "layers/production", "layers/recruitment", "layers/tairo-layout-sidebar", "layers/landing", "layers/tairo-layout-iconnav"],
}
})

export const configs = createConfigForNuxt(options)

configs.append(
// Set globals from imports registry
{
  name: 'nuxt/import-globals',
  languageOptions: {
    globals: Object.fromEntries(["useScriptTriggerConsent","useScriptEventPage","useScriptTriggerElement","useScript","useScriptGoogleAnalytics","useScriptPlausibleAnalytics","useScriptCrisp","useScriptClarity","useScriptCloudflareWebAnalytics","useScriptFathomAnalytics","useScriptMatomoAnalytics","useScriptGoogleTagManager","useScriptGoogleAdsense","useScriptSegment","useScriptMetaPixel","useScriptXPixel","useScriptIntercom","useScriptHotjar","useScriptStripe","useScriptLemonSqueezy","useScriptVimeoPlayer","useScriptYouTubePlayer","useScriptGoogleMaps","useScriptNpm","useScriptUmamiAnalytics","useScriptSnapchatPixel","isVue2","isVue3","defineNuxtLink","useNuxtApp","tryUseNuxtApp","defineNuxtPlugin","definePayloadPlugin","useRuntimeConfig","defineAppConfig","useAppConfig","updateAppConfig","defineNuxtComponent","useAsyncData","useLazyAsyncData","useNuxtData","refreshNuxtData","clearNuxtData","useHydration","callOnce","useState","clearNuxtState","clearError","createError","isNuxtError","showError","useError","useFetch","useLazyFetch","useCookie","refreshCookie","onPrehydrate","prerenderRoutes","useRequestHeader","useRequestHeaders","useResponseHeader","useRequestEvent","useRequestFetch","setResponseStatus","onNuxtReady","preloadComponents","prefetchComponents","preloadRouteComponents","abortNavigation","addRouteMiddleware","defineNuxtRouteMiddleware","setPageLayout","navigateTo","useRoute","useRouter","isPrerendered","loadPayload","preloadPayload","definePayloadReducer","definePayloadReviver","useLoadingIndicator","getAppManifest","getRouteRules","reloadNuxtApp","useRequestURL","usePreviewMode","useRouteAnnouncer","useRuntimeHook","useHeadSafe","useServerHeadSafe","useServerHead","useSeoMeta","useServerSeoMeta","injectHead","onBeforeRouteLeave","onBeforeRouteUpdate","withCtx","withDirectives","withKeys","withMemo","withModifiers","withScopeId","onActivated","onBeforeMount","onBeforeUnmount","onBeforeUpdate","onDeactivated","onErrorCaptured","onMounted","onRenderTracked","onRenderTriggered","onServerPrefetch","onUnmounted","onUpdated","computed","customRef","isProxy","isReactive","isReadonly","isRef","markRaw","proxyRefs","reactive","readonly","ref","shallowReactive","shallowReadonly","shallowRef","toRaw","toRef","toRefs","triggerRef","unref","watch","watchEffect","watchPostEffect","watchSyncEffect","isShallow","effect","effectScope","getCurrentScope","onScopeDispose","defineComponent","defineAsyncComponent","resolveComponent","getCurrentInstance","h","inject","hasInjectionContext","nextTick","provide","mergeModels","toValue","useModel","useAttrs","useCssModule","useCssVars","useSlots","useTransitionState","useId","useTemplateRef","useShadowRoot","Component","ComponentPublicInstance","ComputedRef","DirectiveBinding","ExtractDefaultPropTypes","ExtractPropTypes","ExtractPublicPropTypes","InjectionKey","PropType","Ref","MaybeRef","MaybeRefOrGetter","VNode","WritableComputedRef","requestIdleCallback","cancelIdleCallback","setInterval","createAnimation","createGesture","getIonPageElement","getPlatforms","getTimeGivenProgression","iosTransitionAnimation","isPlatform","mdTransitionAnimation","menuController","modalController","popoverController","alertController","actionSheetController","loadingController","pickerController","toastController","onIonViewDidEnter","onIonViewDidLeave","onIonViewWillEnter","onIonViewWillLeave","openURL","useBackButton","useIonRouter","useKeyboard","useHead","ioniconsAccessibility","ioniconsAccessibilityOutline","ioniconsAccessibilitySharp","ioniconsAdd","ioniconsAddCircle","ioniconsAddCircleOutline","ioniconsAddCircleSharp","ioniconsAddOutline","ioniconsAddSharp","ioniconsAirplane","ioniconsAirplaneOutline","ioniconsAirplaneSharp","ioniconsAlarm","ioniconsAlarmOutline","ioniconsAlarmSharp","ioniconsAlbums","ioniconsAlbumsOutline","ioniconsAlbumsSharp","ioniconsAlert","ioniconsAlertCircle","ioniconsAlertCircleOutline","ioniconsAlertCircleSharp","ioniconsAlertOutline","ioniconsAlertSharp","ioniconsAmericanFootball","ioniconsAmericanFootballOutline","ioniconsAmericanFootballSharp","ioniconsAnalytics","ioniconsAnalyticsOutline","ioniconsAnalyticsSharp","ioniconsAperture","ioniconsApertureOutline","ioniconsApertureSharp","ioniconsApps","ioniconsAppsOutline","ioniconsAppsSharp","ioniconsArchive","ioniconsArchiveOutline","ioniconsArchiveSharp","ioniconsArrowBack","ioniconsArrowBackCircle","ioniconsArrowBackCircleOutline","ioniconsArrowBackCircleSharp","ioniconsArrowBackOutline","ioniconsArrowBackSharp","ioniconsArrowDown","ioniconsArrowDownCircle","ioniconsArrowDownCircleOutline","ioniconsArrowDownCircleSharp","ioniconsArrowDownLeftBox","ioniconsArrowDownLeftBoxOutline","ioniconsArrowDownLeftBoxSharp","ioniconsArrowDownOutline","ioniconsArrowDownRightBox","********************************","ioniconsArrowDownRightBoxSharp","ioniconsArrowDownSharp","ioniconsArrowForward","ioniconsArrowForwardCircle","ioniconsArrowForwardCircleOutline","ioniconsArrowForwardCircleSharp","ioniconsArrowForwardOutline","ioniconsArrowForwardSharp","ioniconsArrowRedo","ioniconsArrowRedoCircle","ioniconsArrowRedoCircleOutline","ioniconsArrowRedoCircleSharp","ioniconsArrowRedoOutline","ioniconsArrowRedoSharp","ioniconsArrowUndo","ioniconsArrowUndoCircle","ioniconsArrowUndoCircleOutline","ioniconsArrowUndoCircleSharp","ioniconsArrowUndoOutline","ioniconsArrowUndoSharp","ioniconsArrowUp","ioniconsArrowUpCircle","ioniconsArrowUpCircleOutline","ioniconsArrowUpCircleSharp","ioniconsArrowUpLeftBox","ioniconsArrowUpLeftBoxOutline","ioniconsArrowUpLeftBoxSharp","ioniconsArrowUpOutline","ioniconsArrowUpRightBox","ioniconsArrowUpRightBoxOutline","ioniconsArrowUpRightBoxSharp","ioniconsArrowUpSharp","ioniconsAt","ioniconsAtCircle","ioniconsAtCircleOutline","ioniconsAtCircleSharp","ioniconsAtOutline","ioniconsAtSharp","ioniconsAttach","ioniconsAttachOutline","ioniconsAttachSharp","ioniconsBackspace","ioniconsBackspaceOutline","ioniconsBackspaceSharp","ioniconsBag","ioniconsBagAdd","ioniconsBagAddOutline","ioniconsBagAddSharp","ioniconsBagCheck","ioniconsBagCheckOutline","ioniconsBagCheckSharp","ioniconsBagHandle","ioniconsBagHandleOutline","ioniconsBagHandleSharp","ioniconsBagOutline","ioniconsBagRemove","ioniconsBagRemoveOutline","ioniconsBagRemoveSharp","ioniconsBagSharp","ioniconsBalloon","ioniconsBalloonOutline","ioniconsBalloonSharp","ioniconsBan","ioniconsBanOutline","ioniconsBanSharp","ioniconsBandage","ioniconsBandageOutline","ioniconsBandageSharp","ioniconsBarChart","ioniconsBarChartOutline","ioniconsBarChartSharp","ioniconsBarbell","ioniconsBarbellOutline","ioniconsBarbellSharp","ioniconsBarcode","ioniconsBarcodeOutline","ioniconsBarcodeSharp","ioniconsBaseball","ioniconsBaseballOutline","ioniconsBaseballSharp","ioniconsBasket","ioniconsBasketOutline","ioniconsBasketSharp","ioniconsBasketball","ioniconsBasketballOutline","ioniconsBasketballSharp","ioniconsBatteryCharging","ioniconsBatteryChargingOutline","ioniconsBatteryChargingSharp","ioniconsBatteryDead","ioniconsBatteryDeadOutline","ioniconsBatteryDeadSharp","ioniconsBatteryFull","ioniconsBatteryFullOutline","ioniconsBatteryFullSharp","ioniconsBatteryHalf","ioniconsBatteryHalfOutline","ioniconsBatteryHalfSharp","ioniconsBeaker","ioniconsBeakerOutline","ioniconsBeakerSharp","ioniconsBed","ioniconsBedOutline","ioniconsBedSharp","ioniconsBeer","ioniconsBeerOutline","ioniconsBeerSharp","ioniconsBicycle","ioniconsBicycleOutline","ioniconsBicycleSharp","ioniconsBinoculars","ioniconsBinocularsOutline","ioniconsBinocularsSharp","ioniconsBluetooth","ioniconsBluetoothOutline","ioniconsBluetoothSharp","ioniconsBoat","ioniconsBoatOutline","ioniconsBoatSharp","ioniconsBody","ioniconsBodyOutline","ioniconsBodySharp","ioniconsBonfire","ioniconsBonfireOutline","ioniconsBonfireSharp","ioniconsBook","ioniconsBookOutline","ioniconsBookSharp","ioniconsBookmark","ioniconsBookmarkOutline","ioniconsBookmarkSharp","ioniconsBookmarks","ioniconsBookmarksOutline","ioniconsBookmarksSharp","ioniconsBowlingBall","ioniconsBowlingBallOutline","ioniconsBowlingBallSharp","ioniconsBriefcase","ioniconsBriefcaseOutline","ioniconsBriefcaseSharp","ioniconsBrowsers","ioniconsBrowsersOutline","ioniconsBrowsersSharp","ioniconsBrush","ioniconsBrushOutline","ioniconsBrushSharp","ioniconsBug","ioniconsBugOutline","ioniconsBugSharp","ioniconsBuild","ioniconsBuildOutline","ioniconsBuildSharp","ioniconsBulb","ioniconsBulbOutline","ioniconsBulbSharp","ioniconsBus","ioniconsBusOutline","ioniconsBusSharp","ioniconsBusiness","ioniconsBusinessOutline","ioniconsBusinessSharp","ioniconsCafe","ioniconsCafeOutline","ioniconsCafeSharp","ioniconsCalculator","ioniconsCalculatorOutline","ioniconsCalculatorSharp","ioniconsCalendar","ioniconsCalendarClear","ioniconsCalendarClearOutline","ioniconsCalendarClearSharp","ioniconsCalendarNumber","ioniconsCalendarNumberOutline","ioniconsCalendarNumberSharp","ioniconsCalendarOutline","ioniconsCalendarSharp","ioniconsCall","ioniconsCallOutline","ioniconsCallSharp","ioniconsCamera","ioniconsCameraOutline","ioniconsCameraReverse","ioniconsCameraReverseOutline","ioniconsCameraReverseSharp","ioniconsCameraSharp","ioniconsCar","ioniconsCarOutline","ioniconsCarSharp","ioniconsCarSport","ioniconsCarSportOutline","ioniconsCarSportSharp","ioniconsCard","ioniconsCardOutline","ioniconsCardSharp","ioniconsCaretBack","ioniconsCaretBackCircle","ioniconsCaretBackCircleOutline","ioniconsCaretBackCircleSharp","ioniconsCaretBackOutline","ioniconsCaretBackSharp","ioniconsCaretDown","ioniconsCaretDownCircle","ioniconsCaretDownCircleOutline","ioniconsCaretDownCircleSharp","ioniconsCaretDownOutline","ioniconsCaretDownSharp","ioniconsCaretForward","ioniconsCaretForwardCircle","ioniconsCaretForwardCircleOutline","ioniconsCaretForwardCircleSharp","ioniconsCaretForwardOutline","ioniconsCaretForwardSharp","ioniconsCaretUp","ioniconsCaretUpCircle","ioniconsCaretUpCircleOutline","ioniconsCaretUpCircleSharp","ioniconsCaretUpOutline","ioniconsCaretUpSharp","ioniconsCart","ioniconsCartOutline","ioniconsCartSharp","ioniconsCash","ioniconsCashOutline","ioniconsCashSharp","ioniconsCellular","ioniconsCellularOutline","ioniconsCellularSharp","ioniconsChatbox","ioniconsChatboxEllipses","ioniconsChatboxEllipsesOutline","ioniconsChatboxEllipsesSharp","ioniconsChatboxOutline","ioniconsChatboxSharp","ioniconsChatbubble","ioniconsChatbubbleEllipses","ioniconsChatbubbleEllipsesOutline","ioniconsChatbubbleEllipsesSharp","ioniconsChatbubbleOutline","ioniconsChatbubbleSharp","ioniconsChatbubbles","ioniconsChatbubblesOutline","ioniconsChatbubblesSharp","ioniconsCheckbox","ioniconsCheckboxOutline","ioniconsCheckboxSharp","ioniconsCheckmark","ioniconsCheckmarkCircle","ioniconsCheckmarkCircleOutline","ioniconsCheckmarkCircleSharp","ioniconsCheckmarkDone","ioniconsCheckmarkDoneCircle","ioniconsCheckmarkDoneCircleOutline","ioniconsCheckmarkDoneCircleSharp","ioniconsCheckmarkDoneOutline","ioniconsCheckmarkDoneSharp","ioniconsCheckmarkOutline","ioniconsCheckmarkSharp","ioniconsChevronBack","ioniconsChevronBackCircle","ioniconsChevronBackCircleOutline","ioniconsChevronBackCircleSharp","ioniconsChevronBackOutline","ioniconsChevronBackSharp","ioniconsChevronCollapse","ioniconsChevronCollapseOutline","ioniconsChevronCollapseSharp","ioniconsChevronDown","ioniconsChevronDownCircle","ioniconsChevronDownCircleOutline","ioniconsChevronDownCircleSharp","ioniconsChevronDownOutline","ioniconsChevronDownSharp","ioniconsChevronExpand","ioniconsChevronExpandOutline","ioniconsChevronExpandSharp","ioniconsChevronForward","ioniconsChevronForwardCircle","ioniconsChevronForwardCircleOutline","ioniconsChevronForwardCircleSharp","ioniconsChevronForwardOutline","ioniconsChevronForwardSharp","ioniconsChevronUp","ioniconsChevronUpCircle","ioniconsChevronUpCircleOutline","ioniconsChevronUpCircleSharp","ioniconsChevronUpOutline","ioniconsChevronUpSharp","ioniconsClipboard","ioniconsClipboardOutline","ioniconsClipboardSharp","ioniconsClose","ioniconsCloseCircle","ioniconsCloseCircleOutline","ioniconsCloseCircleSharp","ioniconsCloseOutline","ioniconsCloseSharp","ioniconsCloud","ioniconsCloudCircle","ioniconsCloudCircleOutline","ioniconsCloudCircleSharp","ioniconsCloudDone","ioniconsCloudDoneOutline","ioniconsCloudDoneSharp","ioniconsCloudDownload","ioniconsCloudDownloadOutline","ioniconsCloudDownloadSharp","ioniconsCloudOffline","ioniconsCloudOfflineOutline","ioniconsCloudOfflineSharp","ioniconsCloudOutline","ioniconsCloudSharp","ioniconsCloudUpload","ioniconsCloudUploadOutline","ioniconsCloudUploadSharp","ioniconsCloudy","ioniconsCloudyNight","ioniconsCloudyNightOutline","ioniconsCloudyNightSharp","ioniconsCloudyOutline","ioniconsCloudySharp","ioniconsCode","ioniconsCodeDownload","ioniconsCodeDownloadOutline","ioniconsCodeDownloadSharp","ioniconsCodeOutline","ioniconsCodeSharp","ioniconsCodeSlash","ioniconsCodeSlashOutline","ioniconsCodeSlashSharp","ioniconsCodeWorking","ioniconsCodeWorkingOutline","ioniconsCodeWorkingSharp","ioniconsCog","ioniconsCogOutline","ioniconsCogSharp","ioniconsColorFill","ioniconsColorFillOutline","ioniconsColorFillSharp","ioniconsColorFilter","ioniconsColorFilterOutline","ioniconsColorFilterSharp","ioniconsColorPalette","ioniconsColorPaletteOutline","ioniconsColorPaletteSharp","ioniconsColorWand","ioniconsColorWandOutline","ioniconsColorWandSharp","ioniconsCompass","ioniconsCompassOutline","ioniconsCompassSharp","ioniconsConstruct","ioniconsConstructOutline","ioniconsConstructSharp","ioniconsContract","ioniconsContractOutline","ioniconsContractSharp","ioniconsContrast","ioniconsContrastOutline","ioniconsContrastSharp","ioniconsCopy","ioniconsCopyOutline","ioniconsCopySharp","ioniconsCreate","ioniconsCreateOutline","ioniconsCreateSharp","ioniconsCrop","ioniconsCropOutline","ioniconsCropSharp","ioniconsCube","ioniconsCubeOutline","ioniconsCubeSharp","ioniconsCut","ioniconsCutOutline","ioniconsCutSharp","ioniconsDesktop","ioniconsDesktopOutline","ioniconsDesktopSharp","ioniconsDiamond","ioniconsDiamondOutline","ioniconsDiamondSharp","ioniconsDice","ioniconsDiceOutline","ioniconsDiceSharp","ioniconsDisc","ioniconsDiscOutline","ioniconsDiscSharp","ioniconsDocument","ioniconsDocumentAttach","ioniconsDocumentAttachOutline","ioniconsDocumentAttachSharp","ioniconsDocumentLock","ioniconsDocumentLockOutline","ioniconsDocumentLockSharp","ioniconsDocumentOutline","ioniconsDocumentSharp","ioniconsDocumentText","ioniconsDocumentTextOutline","ioniconsDocumentTextSharp","ioniconsDocuments","ioniconsDocumentsOutline","ioniconsDocumentsSharp","ioniconsDownload","ioniconsDownloadOutline","ioniconsDownloadSharp","ioniconsDuplicate","ioniconsDuplicateOutline","ioniconsDuplicateSharp","ioniconsEar","ioniconsEarOutline","ioniconsEarSharp","ioniconsEarth","ioniconsEarthOutline","ioniconsEarthSharp","ioniconsEasel","ioniconsEaselOutline","ioniconsEaselSharp","ioniconsEgg","ioniconsEggOutline","ioniconsEggSharp","ioniconsEllipse","ioniconsEllipseOutline","ioniconsEllipseSharp","ioniconsEllipsisHorizontal","ioniconsEllipsisHorizontalCircle","ioniconsEllipsisHorizontalCircleOutline","ioniconsEllipsisHorizontalCircleSharp","ioniconsEllipsisHorizontalOutline","ioniconsEllipsisHorizontalSharp","ioniconsEllipsisVertical","ioniconsEllipsisVerticalCircle","ioniconsEllipsisVerticalCircleOutline","ioniconsEllipsisVerticalCircleSharp","ioniconsEllipsisVerticalOutline","ioniconsEllipsisVerticalSharp","ioniconsEnter","ioniconsEnterOutline","ioniconsEnterSharp","ioniconsExit","ioniconsExitOutline","ioniconsExitSharp","ioniconsExpand","ioniconsExpandOutline","ioniconsExpandSharp","ioniconsExtensionPuzzle","ioniconsExtensionPuzzleOutline","ioniconsExtensionPuzzleSharp","ioniconsEye","ioniconsEyeOff","ioniconsEyeOffOutline","ioniconsEyeOffSharp","ioniconsEyeOutline","ioniconsEyeSharp","ioniconsEyedrop","ioniconsEyedropOutline","ioniconsEyedropSharp","ioniconsFastFood","ioniconsFastFoodOutline","ioniconsFastFoodSharp","ioniconsFemale","ioniconsFemaleOutline","ioniconsFemaleSharp","ioniconsFileTray","ioniconsFileTrayFull","ioniconsFileTrayFullOutline","ioniconsFileTrayFullSharp","ioniconsFileTrayOutline","ioniconsFileTraySharp","ioniconsFileTrayStacked","ioniconsFileTrayStackedOutline","ioniconsFileTrayStackedSharp","ioniconsFilm","ioniconsFilmOutline","ioniconsFilmSharp","ioniconsFilter","ioniconsFilterCircle","ioniconsFilterCircleOutline","ioniconsFilterCircleSharp","ioniconsFilterOutline","ioniconsFilterSharp","ioniconsFingerPrint","ioniconsFingerPrintOutline","ioniconsFingerPrintSharp","ioniconsFish","ioniconsFishOutline","ioniconsFishSharp","ioniconsFitness","ioniconsFitnessOutline","ioniconsFitnessSharp","ioniconsFlag","ioniconsFlagOutline","ioniconsFlagSharp","ioniconsFlame","ioniconsFlameOutline","ioniconsFlameSharp","ioniconsFlash","ioniconsFlashOff","ioniconsFlashOffOutline","ioniconsFlashOffSharp","ioniconsFlashOutline","ioniconsFlashSharp","ioniconsFlashlight","ioniconsFlashlightOutline","ioniconsFlashlightSharp","ioniconsFlask","ioniconsFlaskOutline","ioniconsFlaskSharp","ioniconsFlower","ioniconsFlowerOutline","ioniconsFlowerSharp","ioniconsFolder","ioniconsFolderOpen","ioniconsFolderOpenOutline","ioniconsFolderOpenSharp","ioniconsFolderOutline","ioniconsFolderSharp","ioniconsFootball","ioniconsFootballOutline","ioniconsFootballSharp","ioniconsFootsteps","ioniconsFootstepsOutline","ioniconsFootstepsSharp","ioniconsFunnel","ioniconsFunnelOutline","ioniconsFunnelSharp","ioniconsGameController","ioniconsGameControllerOutline","ioniconsGameControllerSharp","ioniconsGift","ioniconsGiftOutline","ioniconsGiftSharp","ioniconsGitBranch","ioniconsGitBranchOutline","ioniconsGitBranchSharp","ioniconsGitCommit","ioniconsGitCommitOutline","ioniconsGitCommitSharp","ioniconsGitCompare","ioniconsGitCompareOutline","ioniconsGitCompareSharp","ioniconsGitMerge","ioniconsGitMergeOutline","ioniconsGitMergeSharp","ioniconsGitNetwork","ioniconsGitNetworkOutline","ioniconsGitNetworkSharp","ioniconsGitPullRequest","ioniconsGitPullRequestOutline","ioniconsGitPullRequestSharp","ioniconsGlasses","ioniconsGlassesOutline","ioniconsGlassesSharp","ioniconsGlobe","ioniconsGlobeOutline","ioniconsGlobeSharp","ioniconsGolf","ioniconsGolfOutline","ioniconsGolfSharp","ioniconsGrid","ioniconsGridOutline","ioniconsGridSharp","ioniconsHammer","ioniconsHammerOutline","ioniconsHammerSharp","ioniconsHandLeft","ioniconsHandLeftOutline","ioniconsHandLeftSharp","ioniconsHandRight","ioniconsHandRightOutline","ioniconsHandRightSharp","ioniconsHappy","ioniconsHappyOutline","ioniconsHappySharp","ioniconsHardwareChip","ioniconsHardwareChipOutline","ioniconsHardwareChipSharp","ioniconsHeadset","ioniconsHeadsetOutline","ioniconsHeadsetSharp","ioniconsHeart","ioniconsHeartCircle","ioniconsHeartCircleOutline","ioniconsHeartCircleSharp","ioniconsHeartDislike","ioniconsHeartDislikeCircle","ioniconsHeartDislikeCircleOutline","ioniconsHeartDislikeCircleSharp","ioniconsHeartDislikeOutline","ioniconsHeartDislikeSharp","ioniconsHeartHalf","ioniconsHeartHalfOutline","ioniconsHeartHalfSharp","ioniconsHeartOutline","ioniconsHeartSharp","ioniconsHelp","ioniconsHelpBuoy","ioniconsHelpBuoyOutline","ioniconsHelpBuoySharp","ioniconsHelpCircle","ioniconsHelpCircleOutline","ioniconsHelpCircleSharp","ioniconsHelpOutline","ioniconsHelpSharp","ioniconsHome","ioniconsHomeOutline","ioniconsHomeSharp","ioniconsHourglass","ioniconsHourglassOutline","ioniconsHourglassSharp","ioniconsIceCream","ioniconsIceCreamOutline","ioniconsIceCreamSharp","ioniconsIdCard","ioniconsIdCardOutline","ioniconsIdCardSharp","ioniconsImage","ioniconsImageOutline","ioniconsImageSharp","ioniconsImages","ioniconsImagesOutline","ioniconsImagesSharp","ioniconsInfinite","ioniconsInfiniteOutline","ioniconsInfiniteSharp","ioniconsInformation","ioniconsInformationCircle","ioniconsInformationCircleOutline","ioniconsInformationCircleSharp","ioniconsInformationOutline","ioniconsInformationSharp","ioniconsInvertMode","ioniconsInvertModeOutline","ioniconsInvertModeSharp","ioniconsJournal","ioniconsJournalOutline","ioniconsJournalSharp","ioniconsKey","ioniconsKeyOutline","ioniconsKeySharp","ioniconsKeypad","ioniconsKeypadOutline","ioniconsKeypadSharp","ioniconsLanguage","ioniconsLanguageOutline","ioniconsLanguageSharp","ioniconsLaptop","ioniconsLaptopOutline","ioniconsLaptopSharp","ioniconsLayers","ioniconsLayersOutline","ioniconsLayersSharp","ioniconsLeaf","ioniconsLeafOutline","ioniconsLeafSharp","ioniconsLibrary","ioniconsLibraryOutline","ioniconsLibrarySharp","ioniconsLink","ioniconsLinkOutline","ioniconsLinkSharp","ioniconsList","ioniconsListCircle","ioniconsListCircleOutline","ioniconsListCircleSharp","ioniconsListOutline","ioniconsListSharp","ioniconsLocate","ioniconsLocateOutline","ioniconsLocateSharp","ioniconsLocation","ioniconsLocationOutline","ioniconsLocationSharp","ioniconsLockClosed","ioniconsLockClosedOutline","ioniconsLockClosedSharp","ioniconsLockOpen","ioniconsLockOpenOutline","ioniconsLockOpenSharp","ioniconsLogIn","ioniconsLogInOutline","ioniconsLogInSharp","ioniconsLogOut","ioniconsLogOutOutline","ioniconsLogOutSharp","ioniconsLogoAlipay","ioniconsLogoAmazon","ioniconsLogoAmplify","ioniconsLogoAndroid","ioniconsLogoAngular","ioniconsLogoAppflow","ioniconsLogoApple","ioniconsLogoAppleAppstore","ioniconsLogoAppleAr","ioniconsLogoBehance","ioniconsLogoBitbucket","ioniconsLogoBitcoin","ioniconsLogoBuffer","ioniconsLogoCapacitor","ioniconsLogoChrome","ioniconsLogoClosedCaptioning","ioniconsLogoCodepen","ioniconsLogoCss3","ioniconsLogoDesignernews","ioniconsLogoDeviantart","ioniconsLogoDiscord","ioniconsLogoDocker","ioniconsLogoDribbble","ioniconsLogoDropbox","ioniconsLogoEdge","ioniconsLogoElectron","ioniconsLogoEuro","ioniconsLogoFacebook","ioniconsLogoFigma","ioniconsLogoFirebase","ioniconsLogoFirefox","ioniconsLogoFlickr","ioniconsLogoFoursquare","ioniconsLogoGithub","ioniconsLogoGitlab","ioniconsLogoGoogle","ioniconsLogoGooglePlaystore","ioniconsLogoHackernews","ioniconsLogoHtml5","ioniconsLogoInstagram","ioniconsLogoIonic","ioniconsLogoIonitron","ioniconsLogoJavascript","ioniconsLogoLaravel","ioniconsLogoLinkedin","ioniconsLogoMarkdown","ioniconsLogoMastodon","ioniconsLogoMedium","ioniconsLogoMicrosoft","ioniconsLogoNoSmoking","ioniconsLogoNodejs","ioniconsLogoNpm","ioniconsLogoOctocat","ioniconsLogoPaypal","ioniconsLogoPinterest","ioniconsLogoPlaystation","ioniconsLogoPwa","ioniconsLogoPython","ioniconsLogoReact","ioniconsLogoReddit","ioniconsLogoRss","ioniconsLogoSass","ioniconsLogoSkype","ioniconsLogoSlack","ioniconsLogoSnapchat","ioniconsLogoSoundcloud","ioniconsLogoStackoverflow","ioniconsLogoSteam","ioniconsLogoStencil","ioniconsLogoTableau","ioniconsLogoTiktok","ioniconsLogoTrapeze","ioniconsLogoTumblr","ioniconsLogoTux","ioniconsLogoTwitch","ioniconsLogoTwitter","ioniconsLogoUsd","ioniconsLogoVenmo","ioniconsLogoVercel","ioniconsLogoVimeo","ioniconsLogoVk","ioniconsLogoVue","ioniconsLogoWebComponent","ioniconsLogoWechat","ioniconsLogoWhatsapp","ioniconsLogoWindows","ioniconsLogoWordpress","ioniconsLogoX","ioniconsLogoXbox","ioniconsLogoXing","ioniconsLogoYahoo","ioniconsLogoYen","ioniconsLogoYoutube","ioniconsMagnet","ioniconsMagnetOutline","ioniconsMagnetSharp","ioniconsMail","ioniconsMailOpen","ioniconsMailOpenOutline","ioniconsMailOpenSharp","ioniconsMailOutline","ioniconsMailSharp","ioniconsMailUnread","ioniconsMailUnreadOutline","ioniconsMailUnreadSharp","ioniconsMale","ioniconsMaleFemale","ioniconsMaleFemaleOutline","ioniconsMaleFemaleSharp","ioniconsMaleOutline","ioniconsMaleSharp","ioniconsMan","ioniconsManOutline","ioniconsManSharp","ioniconsMap","ioniconsMapOutline","ioniconsMapSharp","ioniconsMedal","ioniconsMedalOutline","ioniconsMedalSharp","ioniconsMedical","ioniconsMedicalOutline","ioniconsMedicalSharp","ioniconsMedkit","ioniconsMedkitOutline","ioniconsMedkitSharp","ioniconsMegaphone","ioniconsMegaphoneOutline","ioniconsMegaphoneSharp","ioniconsMenu","ioniconsMenuOutline","ioniconsMenuSharp","ioniconsMic","ioniconsMicCircle","ioniconsMicCircleOutline","ioniconsMicCircleSharp","ioniconsMicOff","ioniconsMicOffCircle","ioniconsMicOffCircleOutline","ioniconsMicOffCircleSharp","ioniconsMicOffOutline","ioniconsMicOffSharp","ioniconsMicOutline","ioniconsMicSharp","ioniconsMoon","ioniconsMoonOutline","ioniconsMoonSharp","ioniconsMove","ioniconsMoveOutline","ioniconsMoveSharp","ioniconsMusicalNote","ioniconsMusicalNoteOutline","ioniconsMusicalNoteSharp","ioniconsMusicalNotes","ioniconsMusicalNotesOutline","ioniconsMusicalNotesSharp","ioniconsNavigate","ioniconsNavigateCircle","ioniconsNavigateCircleOutline","ioniconsNavigateCircleSharp","ioniconsNavigateOutline","ioniconsNavigateSharp","ioniconsNewspaper","ioniconsNewspaperOutline","ioniconsNewspaperSharp","ioniconsNotifications","ioniconsNotificationsCircle","ioniconsNotificationsCircleOutline","ioniconsNotificationsCircleSharp","ioniconsNotificationsOff","ioniconsNotificationsOffCircle","ioniconsNotificationsOffCircleOutline","ioniconsNotificationsOffCircleSharp","ioniconsNotificationsOffOutline","ioniconsNotificationsOffSharp","ioniconsNotificationsOutline","ioniconsNotificationsSharp","ioniconsNuclear","ioniconsNuclearOutline","ioniconsNuclearSharp","ioniconsNutrition","ioniconsNutritionOutline","ioniconsNutritionSharp","ioniconsOpen","ioniconsOpenOutline","ioniconsOpenSharp","ioniconsOptions","ioniconsOptionsOutline","ioniconsOptionsSharp","ioniconsPaperPlane","ioniconsPaperPlaneOutline","ioniconsPaperPlaneSharp","ioniconsPartlySunny","ioniconsPartlySunnyOutline","ioniconsPartlySunnySharp","ioniconsPause","ioniconsPauseCircle","ioniconsPauseCircleOutline","ioniconsPauseCircleSharp","ioniconsPauseOutline","ioniconsPauseSharp","ioniconsPaw","ioniconsPawOutline","ioniconsPawSharp","ioniconsPencil","ioniconsPencilOutline","ioniconsPencilSharp","ioniconsPeople","ioniconsPeopleCircle","ioniconsPeopleCircleOutline","ioniconsPeopleCircleSharp","ioniconsPeopleOutline","ioniconsPeopleSharp","ioniconsPerson","ioniconsPersonAdd","ioniconsPersonAddOutline","ioniconsPersonAddSharp","ioniconsPersonCircle","ioniconsPersonCircleOutline","ioniconsPersonCircleSharp","ioniconsPersonOutline","ioniconsPersonRemove","ioniconsPersonRemoveOutline","ioniconsPersonRemoveSharp","ioniconsPersonSharp","ioniconsPhoneLandscape","ioniconsPhoneLandscapeOutline","ioniconsPhoneLandscapeSharp","ioniconsPhonePortrait","ioniconsPhonePortraitOutline","ioniconsPhonePortraitSharp","ioniconsPieChart","ioniconsPieChartOutline","ioniconsPieChartSharp","ioniconsPin","ioniconsPinOutline","ioniconsPinSharp","ioniconsPint","ioniconsPintOutline","ioniconsPintSharp","ioniconsPizza","ioniconsPizzaOutline","ioniconsPizzaSharp","ioniconsPlanet","ioniconsPlanetOutline","ioniconsPlanetSharp","ioniconsPlay","ioniconsPlayBack","ioniconsPlayBackCircle","ioniconsPlayBackCircleOutline","ioniconsPlayBackCircleSharp","ioniconsPlayBackOutline","ioniconsPlayBackSharp","ioniconsPlayCircle","ioniconsPlayCircleOutline","ioniconsPlayCircleSharp","ioniconsPlayForward","ioniconsPlayForwardCircle","ioniconsPlayForwardCircleOutline","ioniconsPlayForwardCircleSharp","ioniconsPlayForwardOutline","ioniconsPlayForwardSharp","ioniconsPlayOutline","ioniconsPlaySharp","ioniconsPlaySkipBack","ioniconsPlaySkipBackCircle","ioniconsPlaySkipBackCircleOutline","ioniconsPlaySkipBackCircleSharp","ioniconsPlaySkipBackOutline","ioniconsPlaySkipBackSharp","ioniconsPlaySkipForward","ioniconsPlaySkipForwardCircle","ioniconsPlaySkipForwardCircleOutline","ioniconsPlaySkipForwardCircleSharp","ioniconsPlaySkipForwardOutline","ioniconsPlaySkipForwardSharp","ioniconsPodium","ioniconsPodiumOutline","ioniconsPodiumSharp","ioniconsPower","ioniconsPowerOutline","ioniconsPowerSharp","ioniconsPricetag","ioniconsPricetagOutline","ioniconsPricetagSharp","ioniconsPricetags","ioniconsPricetagsOutline","ioniconsPricetagsSharp","ioniconsPrint","ioniconsPrintOutline","ioniconsPrintSharp","ioniconsPrism","ioniconsPrismOutline","ioniconsPrismSharp","ioniconsPulse","ioniconsPulseOutline","ioniconsPulseSharp","ioniconsPush","ioniconsPushOutline","ioniconsPushSharp","ioniconsQrCode","ioniconsQrCodeOutline","ioniconsQrCodeSharp","ioniconsRadio","ioniconsRadioButtonOff","ioniconsRadioButtonOffOutline","ioniconsRadioButtonOffSharp","ioniconsRadioButtonOn","ioniconsRadioButtonOnOutline","ioniconsRadioButtonOnSharp","ioniconsRadioOutline","ioniconsRadioSharp","ioniconsRainy","ioniconsRainyOutline","ioniconsRainySharp","ioniconsReader","ioniconsReaderOutline","ioniconsReaderSharp","ioniconsReceipt","ioniconsReceiptOutline","ioniconsReceiptSharp","ioniconsRecording","ioniconsRecordingOutline","ioniconsRecordingSharp","ioniconsRefresh","ioniconsRefreshCircle","ioniconsRefreshCircleOutline","ioniconsRefreshCircleSharp","ioniconsRefreshOutline","ioniconsRefreshSharp","ioniconsReload","ioniconsReloadCircle","ioniconsReloadCircleOutline","ioniconsReloadCircleSharp","ioniconsReloadOutline","ioniconsReloadSharp","ioniconsRemove","ioniconsRemoveCircle","ioniconsRemoveCircleOutline","ioniconsRemoveCircleSharp","ioniconsRemoveOutline","ioniconsRemoveSharp","ioniconsReorderFour","ioniconsReorderFourOutline","ioniconsReorderFourSharp","ioniconsReorderThree","ioniconsReorderThreeOutline","ioniconsReorderThreeSharp","ioniconsReorderTwo","ioniconsReorderTwoOutline","ioniconsReorderTwoSharp","ioniconsRepeat","ioniconsRepeatOutline","ioniconsRepeatSharp","ioniconsResize","ioniconsResizeOutline","ioniconsResizeSharp","ioniconsRestaurant","ioniconsRestaurantOutline","ioniconsRestaurantSharp","ioniconsReturnDownBack","ioniconsReturnDownBackOutline","ioniconsReturnDownBackSharp","ioniconsReturnDownForward","ioniconsReturnDownForwardOutline","ioniconsReturnDownForwardSharp","ioniconsReturnUpBack","ioniconsReturnUpBackOutline","ioniconsReturnUpBackSharp","ioniconsReturnUpForward","ioniconsReturnUpForwardOutline","ioniconsReturnUpForwardSharp","ioniconsRibbon","ioniconsRibbonOutline","ioniconsRibbonSharp","ioniconsRocket","ioniconsRocketOutline","ioniconsRocketSharp","ioniconsRose","ioniconsRoseOutline","ioniconsRoseSharp","ioniconsSad","ioniconsSadOutline","ioniconsSadSharp","ioniconsSave","ioniconsSaveOutline","ioniconsSaveSharp","ioniconsScale","ioniconsScaleOutline","ioniconsScaleSharp","ioniconsScan","ioniconsScanCircle","ioniconsScanCircleOutline","ioniconsScanCircleSharp","ioniconsScanOutline","ioniconsScanSharp","ioniconsSchool","ioniconsSchoolOutline","ioniconsSchoolSharp","ioniconsSearch","ioniconsSearchCircle","ioniconsSearchCircleOutline","ioniconsSearchCircleSharp","ioniconsSearchOutline","ioniconsSearchSharp","ioniconsSend","ioniconsSendOutline","ioniconsSendSharp","ioniconsServer","ioniconsServerOutline","ioniconsServerSharp","ioniconsSettings","ioniconsSettingsOutline","ioniconsSettingsSharp","ioniconsShapes","ioniconsShapesOutline","ioniconsShapesSharp","ioniconsShare","ioniconsShareOutline","ioniconsShareSharp","ioniconsShareSocial","ioniconsShareSocialOutline","ioniconsShareSocialSharp","ioniconsShield","ioniconsShieldCheckmark","ioniconsShieldCheckmarkOutline","ioniconsShieldCheckmarkSharp","ioniconsShieldHalf","ioniconsShieldHalfOutline","ioniconsShieldHalfSharp","ioniconsShieldOutline","ioniconsShieldSharp","ioniconsShirt","ioniconsShirtOutline","ioniconsShirtSharp","ioniconsShuffle","ioniconsShuffleOutline","ioniconsShuffleSharp","ioniconsSkull","ioniconsSkullOutline","ioniconsSkullSharp","ioniconsSnow","ioniconsSnowOutline","ioniconsSnowSharp","ioniconsSparkles","ioniconsSparklesOutline","ioniconsSparklesSharp","ioniconsSpeedometer","ioniconsSpeedometerOutline","ioniconsSpeedometerSharp","ioniconsSquare","ioniconsSquareOutline","ioniconsSquareSharp","ioniconsStar","ioniconsStarHalf","ioniconsStarHalfOutline","ioniconsStarHalfSharp","ioniconsStarOutline","ioniconsStarSharp","ioniconsStatsChart","ioniconsStatsChartOutline","ioniconsStatsChartSharp","ioniconsStop","ioniconsStopCircle","ioniconsStopCircleOutline","ioniconsStopCircleSharp","ioniconsStopOutline","ioniconsStopSharp","ioniconsStopwatch","ioniconsStopwatchOutline","ioniconsStopwatchSharp","ioniconsStorefront","ioniconsStorefrontOutline","ioniconsStorefrontSharp","ioniconsSubway","ioniconsSubwayOutline","ioniconsSubwaySharp","ioniconsSunny","ioniconsSunnyOutline","ioniconsSunnySharp","ioniconsSwapHorizontal","ioniconsSwapHorizontalOutline","ioniconsSwapHorizontalSharp","ioniconsSwapVertical","ioniconsSwapVerticalOutline","ioniconsSwapVerticalSharp","ioniconsSync","ioniconsSyncCircle","ioniconsSyncCircleOutline","ioniconsSyncCircleSharp","ioniconsSyncOutline","ioniconsSyncSharp","ioniconsTabletLandscape","ioniconsTabletLandscapeOutline","ioniconsTabletLandscapeSharp","ioniconsTabletPortrait","ioniconsTabletPortraitOutline","ioniconsTabletPortraitSharp","ioniconsTelescope","ioniconsTelescopeOutline","ioniconsTelescopeSharp","ioniconsTennisball","ioniconsTennisballOutline","ioniconsTennisballSharp","ioniconsTerminal","ioniconsTerminalOutline","ioniconsTerminalSharp","ioniconsText","ioniconsTextOutline","ioniconsTextSharp","ioniconsThermometer","ioniconsThermometerOutline","ioniconsThermometerSharp","ioniconsThumbsDown","ioniconsThumbsDownOutline","ioniconsThumbsDownSharp","ioniconsThumbsUp","ioniconsThumbsUpOutline","ioniconsThumbsUpSharp","ioniconsThunderstorm","ioniconsThunderstormOutline","ioniconsThunderstormSharp","ioniconsTicket","ioniconsTicketOutline","ioniconsTicketSharp","ioniconsTime","ioniconsTimeOutline","ioniconsTimeSharp","ioniconsTimer","ioniconsTimerOutline","ioniconsTimerSharp","ioniconsToday","ioniconsTodayOutline","ioniconsTodaySharp","ioniconsToggle","ioniconsToggleOutline","ioniconsToggleSharp","ioniconsTrailSign","ioniconsTrailSignOutline","ioniconsTrailSignSharp","ioniconsTrain","ioniconsTrainOutline","ioniconsTrainSharp","ioniconsTransgender","ioniconsTransgenderOutline","ioniconsTransgenderSharp","ioniconsTrash","ioniconsTrashBin","ioniconsTrashBinOutline","ioniconsTrashBinSharp","ioniconsTrashOutline","ioniconsTrashSharp","ioniconsTrendingDown","ioniconsTrendingDownOutline","ioniconsTrendingDownSharp","ioniconsTrendingUp","ioniconsTrendingUpOutline","ioniconsTrendingUpSharp","ioniconsTriangle","ioniconsTriangleOutline","ioniconsTriangleSharp","ioniconsTrophy","ioniconsTrophyOutline","ioniconsTrophySharp","ioniconsTv","ioniconsTvOutline","ioniconsTvSharp","ioniconsUmbrella","ioniconsUmbrellaOutline","ioniconsUmbrellaSharp","ioniconsUnlink","ioniconsUnlinkOutline","ioniconsUnlinkSharp","ioniconsVideocam","ioniconsVideocamOff","ioniconsVideocamOffOutline","ioniconsVideocamOffSharp","ioniconsVideocamOutline","ioniconsVideocamSharp","ioniconsVolumeHigh","ioniconsVolumeHighOutline","ioniconsVolumeHighSharp","ioniconsVolumeLow","ioniconsVolumeLowOutline","ioniconsVolumeLowSharp","ioniconsVolumeMedium","ioniconsVolumeMediumOutline","ioniconsVolumeMediumSharp","ioniconsVolumeMute","ioniconsVolumeMuteOutline","ioniconsVolumeMuteSharp","ioniconsVolumeOff","ioniconsVolumeOffOutline","ioniconsVolumeOffSharp","ioniconsWalk","ioniconsWalkOutline","ioniconsWalkSharp","ioniconsWallet","ioniconsWalletOutline","ioniconsWalletSharp","ioniconsWarning","ioniconsWarningOutline","ioniconsWarningSharp","ioniconsWatch","ioniconsWatchOutline","ioniconsWatchSharp","ioniconsWater","ioniconsWaterOutline","ioniconsWaterSharp","ioniconsWifi","ioniconsWifiOutline","ioniconsWifiSharp","ioniconsWine","ioniconsWineOutline","ioniconsWineSharp","ioniconsWoman","ioniconsWomanOutline","ioniconsWomanSharp","computedAsync","asyncComputed","computedEager","eagerComputed","computedInject","computedWithControl","controlledComputed","createEventHook","createGlobalState","createInjectionState","createReusableTemplate","createSharedComposable","createTemplatePromise","createUnrefFn","extendRef","injectLocal","isDefined","makeDestructurable","onClickOutside","onKeyStroke","onLongPress","onStartTyping","provideLocal","reactify","createReactiveFn","reactifyObject","reactiveComputed","reactiveOmit","reactivePick","refAutoReset","autoResetRef","refDebounced","useDebounce","debouncedRef","refDefault","refThrottled","useThrottle","throttledRef","refWithControl","controlledRef","syncRef","syncRefs","templateRef","toReactive","resolveRef","resolveUnref","tryOnBeforeMount","tryOnBeforeUnmount","tryOnMounted","tryOnScopeDispose","tryOnUnmounted","unrefElement","until","useActiveElement","useAnimate","useArrayDifference","useArrayEvery","useArrayFilter","useArrayFind","useArrayFindIndex","useArrayFindLast","useArrayIncludes","useArrayJoin","useArrayMap","useArrayReduce","useArraySome","useArrayUnique","useAsyncQueue","useAsyncState","useBase64","useBattery","useBluetooth","useBreakpoints","useBroadcastChannel","useBrowserLocation","useCached","useClipboard","useClipboardItems","useCloned","useConfirmDialog","useCounter","useCssVar","useCurrentElement","useCycleList","useDark","useDateFormat","useDebouncedRefHistory","useDebounceFn","useDeviceMotion","useDeviceOrientation","useDevicePixelRatio","useDevicesList","useDisplayMedia","useDocumentVisibility","useDraggable","useDropZone","useElementBounding","useElementByPoint","useElementHover","useElementSize","useElementVisibility","useEventBus","useEventListener","useEventSource","useEyeDropper","useFavicon","useFileDialog","useFileSystemAccess","useFocus","useFocusWithin","useFps","useFullscreen","useGamepad","useGeolocation","useIdle","useInfiniteScroll","useIntersectionObserver","useInterval","useIntervalFn","useKeyModifier","useLastChanged","useLocalStorage","useMagicKeys","useManualRefHistory","useMediaControls","useMediaQuery","useMemoize","useMemory","useMounted","useMouse","useMouseInElement","useMousePressed","useMutationObserver","useNavigatorLanguage","useNetwork","useNow","useObjectUrl","useOffsetPagination","useOnline","usePageLeave","useParallax","useParentElement","usePerformanceObserver","usePermission","usePointer","usePointerLock","usePointerSwipe","usePreferredColorScheme","usePreferredContrast","usePreferredDark","usePreferredLanguages","usePreferredReducedMotion","usePrevious","useRafFn","useRefHistory","useResizeObserver","useScreenOrientation","useScreenSafeArea","useScriptTag","useScroll","useScrollLock","useSessionStorage","useShare","useSorted","useSpeechRecognition","useSpeechSynthesis","useStepper","useStorageAsync","useStyleTag","useSupported","useSwipe","useTemplateRefsList","useTextareaAutosize","useTextDirection","useTextSelection","useThrottledRefHistory","useThrottleFn","useTimeAgo","useTimeout","useTimeoutFn","useTimeoutPoll","useTimestamp","useToggle","useToNumber","useToString","useTransition","useUrlSearchParams","useUserMedia","useVibrate","useVirtualList","useVModel","useVModels","useWakeLock","useWebNotification","useWebSocket","useWebWorker","useWebWorkerFn","useWindowFocus","useWindowScroll","useWindowSize","watchArray","watchAtMost","watchDebounced","debouncedWatch","watchDeep","watchIgnorable","ignorableWatch","watchImmediate","watchOnce","watchPausable","pausableWatch","watchThrottled","throttledWatch","watchTriggerable","watchWithFilter","whenever","useLazyApexCharts","useIconnav","TairoIconnavResolvedConfig","useLayoutSwitcher","useSidebar","useTailwindColors","useTailwindBreakpoints","useToaster","toasterThemes","useAiAssistant","useApi","useComponentAccess","accessDirective","ComponentAccessOptions","useWorkforceForm","resolveComponentOrNative","axiosSetup","colorToRgb","switchColorShades","switchColor","resetColor","getPhoneCountries","CountryInfo","CountriesInfo","formatDate","DateFormatsNames","extractAllRoutes","getAllModules","provideMultiStepForm","useMultiStepForm","StepForm","MultiStepFormConfig","MultiStepFormContext","usePanels","useIsMacLike","useMetaKey","toString","perSession","asMinutes","asDollar","asKDollar","asPercent","toDate","toFixed","getRandomColor","formatPrice","formatFileSize","capitalize","useNinjaButton","BaseButtonProperties","useNuiDefaultProperty","useNinjaFilePreview","useNinjaId","useNinjaMark","useNinjaScrollspy","useNinjaWindowScroll","useAccountingApi","useBudgetApi","useBudgetStore","useCompanyApi","useAdminSidebar","useCollapse","TairoCollapseResolvedConfig","useTopnav","TairoTopnavResolvedConfig","useAdminConfig","useAiStore","useAuthStore","useLanguageStore","useSubscriptionStore","useUserStore","UserCompany","UserRole","UserPreferences","UserSubscription","UserProfile","defineStore","acceptHMRUpdate","usePinia","storeToRefs","piniaPluginPersistedstate","useI18n","useRouteBaseName","useLocalePath","useLocaleRoute","useSwitchLocalePath","useLocaleHead","useBrowserLocale","useCookieLocale","useSetI18nParams","defineI18nRoute","defineI18nLocale","defineI18nConfig","useColorMode","useNinjaToasterState","useNinjaToasterProgress","createNinjaToaster","useNinjaToaster","definePageMeta","useLink","useNitroApp","useRuntimeConfig","useAppConfig","defineNitroPlugin","nitroPlugin","defineCachedFunction","defineCachedEventHandler","cachedFunction","cachedEventHandler","useStorage","defineRenderHandler","defineRouteMeta","getRouteRules","useEvent","defineTask","runTask","defineNitroErrorHandler","appendCorsHeaders","appendCorsPreflightHeaders","appendHeader","appendHeaders","appendResponseHeader","appendResponseHeaders","assertMethod","callNodeListener","clearResponseHeaders","clearSession","createApp","createAppEventHandler","createError","createEvent","createEventStream","createRouter","defaultContentType","defineEventHandler","defineLazyEventHandler","defineNodeListener","defineNodeMiddleware","defineRequestMiddleware","defineResponseMiddleware","defineWebSocket","defineWebSocketHandler","deleteCookie","dynamicEventHandler","eventHandler","fetchWithEvent","fromNodeMiddleware","fromPlainHandler","fromWebHandler","getCookie","getHeader","getHeaders","getMethod","getProxyRequestHeaders","getQuery","getRequestFingerprint","getRequestHeader","getRequestHeaders","getRequestHost","getRequestIP","getRequestPath","getRequestProtocol","getRequestURL","getRequestWebStream","getResponseHeader","getResponseHeaders","getResponseStatus","getResponseStatusText","getRouterParam","getRouterParams","getSession","getValidatedQuery","getValidatedRouterParams","handleCacheHeaders","handleCors","isCorsOriginAllowed","isError","isEvent","isEventHandler","isMethod","isPreflightRequest","isStream","isWebResponse","lazyEventHandler","parseCookies","promisifyNodeListener","proxyRequest","readBody","readFormData","readMultipartFormData","readRawBody","readValidatedBody","removeResponseHeader","sanitizeStatusCode","sanitizeStatusMessage","sealSession","send","sendError","sendIterable","sendNoContent","sendProxy","sendRedirect","sendStream","sendWebResponse","serveStatic","setCookie","setHeader","setHeaders","setResponseHeader","setResponseHeaders","setResponseStatus","splitCookiesString","toEventHandler","toNodeListener","toPlainHandler","toWebHandler","toWebRequest","unsealSession","updateSession","useBase","useSession","writeEarlyHints","__buildAssetsURL","__publicAssetsURL","defineAppConfig","defineI18nLocale","defineI18nConfig"].map(i => [i, 'readonly'])),
  },
}
)

export function withNuxt(...customs) {
  return configs
    .clone()
    .append(...customs)
    .onResolved(configs => typegen(configs, { dtsPath: r("./eslint-typegen.d.ts"), augmentFlatConfigUtils: true }))
}

export default withNuxt