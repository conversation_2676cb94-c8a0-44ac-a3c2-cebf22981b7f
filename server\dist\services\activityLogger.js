"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityLogger = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
class ActivityLogger {
    static async log(data) {
        try {
            await prisma.activityLog.create({
                data: {
                    type: data.type,
                    action: data.action,
                    module: data.module,
                    entity: data.entity,
                    entityId: data.entityId,
                    title: data.title,
                    description: data.description,
                    metadata: data.metadata,
                    userId: data.userId,
                    ipAddress: data.ipAddress,
                    userAgent: data.userAgent,
                    status: data.status || "SUCCESS",
                    severity: data.severity || "INFO",
                    changes: data.changes,
                },
            });
        }
        catch (error) {
            console.error("Failed to log activity:", error);
            // Don't throw error to avoid breaking the main flow
        }
    }
    static async logFromRequest(req, data) {
        const ipAddress = this.getClientIP(req);
        const userAgent = req.get('User-Agent');
        await this.log({
            ...data,
            ipAddress,
            userAgent,
        });
    }
    static async logUserLogin(userId, req, success = true) {
        await this.logFromRequest(req, {
            type: "LOGIN",
            action: "login",
            module: "authentication",
            entity: "user",
            entityId: userId.toString(),
            title: success ? "User Login" : "Failed Login Attempt",
            description: success
                ? "User successfully logged into the system"
                : "Failed login attempt detected",
            userId: success ? userId : undefined,
            status: success ? "SUCCESS" : "FAILED",
            severity: success ? "INFO" : "WARNING",
        });
    }
    static async logUserLogout(userId, req) {
        await this.logFromRequest(req, {
            type: "LOGOUT",
            action: "logout",
            module: "authentication",
            entity: "user",
            entityId: userId.toString(),
            title: "User Logout",
            description: "User logged out of the system",
            userId,
            status: "SUCCESS",
            severity: "INFO",
        });
    }
    static async logUserUpdate(userId, changes, req, updatedByUserId) {
        await this.logFromRequest(req, {
            type: "USER_UPDATE",
            action: "update",
            module: "user_management",
            entity: "user",
            entityId: userId.toString(),
            title: "User Profile Updated",
            description: `User profile information was updated`,
            userId: updatedByUserId || userId,
            status: "SUCCESS",
            severity: "INFO",
            changes,
            metadata: {
                targetUserId: userId,
                updatedFields: Object.keys(changes),
            },
        });
    }
    static async logUserCreate(userId, req, createdByUserId) {
        await this.logFromRequest(req, {
            type: "USER_CREATE",
            action: "create",
            module: "user_management",
            entity: "user",
            entityId: userId.toString(),
            title: "New User Created",
            description: "A new user account was created in the system",
            userId: createdByUserId,
            status: "SUCCESS",
            severity: "INFO",
            metadata: {
                targetUserId: userId,
            },
        });
    }
    static async logPasswordChange(userId, req) {
        await this.logFromRequest(req, {
            type: "PASSWORD_CHANGE",
            action: "update",
            module: "authentication",
            entity: "user",
            entityId: userId.toString(),
            title: "Password Changed",
            description: "User password was changed",
            userId,
            status: "SUCCESS",
            severity: "INFO",
        });
    }
    static async logSecurityEvent(title, description, req, userId, severity = "WARNING") {
        await this.logFromRequest(req, {
            type: "SECURITY_VIOLATION",
            action: "security_event",
            module: "security",
            title,
            description,
            userId,
            status: "FAILED",
            severity,
        });
    }
    static async logDataOperation(operation, entity, entityId, title, description, req, userId, changes) {
        await this.logFromRequest(req, {
            type: operation,
            action: operation.toLowerCase(),
            module: "data_management",
            entity,
            entityId,
            title,
            description,
            userId,
            status: "SUCCESS",
            severity: "INFO",
            changes,
        });
    }
    static async logSystemEvent(type, title, description, metadata, severity = "INFO") {
        await this.log({
            type,
            action: "system_event",
            module: "system",
            title,
            description,
            status: "SUCCESS",
            severity,
            metadata,
        });
    }
    static getClientIP(req) {
        return (req.headers['x-forwarded-for']?.split(',')[0] ||
            req.headers['x-real-ip'] ||
            req.connection?.remoteAddress ||
            req.socket?.remoteAddress ||
            'unknown');
    }
}
exports.ActivityLogger = ActivityLogger;
exports.default = ActivityLogger;
//# sourceMappingURL=activityLogger.js.map