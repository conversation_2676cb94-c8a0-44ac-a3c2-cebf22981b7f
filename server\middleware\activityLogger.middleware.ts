import { Request, Response, NextFunction } from "express";
import { ActivityLogger } from "../services/activityLogger.js";

// Middleware to automatically log activities based on route patterns
export const activityLoggerMiddleware = (options: {
  type?: string;
  action?: string;
  module: string;
  entity?: string;
  getTitle?: (req: Request) => string;
  getDescription?: (req: Request) => string;
  severity?: "LOW" | "INFO" | "WARNING" | "ERROR" | "CRITICAL";
}) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Store original res.json to intercept response
    const originalJson = res.json;
    
    res.json = function(body: any) {
      // Log activity after successful response
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const logData = {
          type: options.type || getTypeFromMethod(req.method),
          action: options.action || req.method.toLowerCase(),
          module: options.module,
          entity: options.entity,
          entityId: req.params.id || body?.id?.toString(),
          title: options.getTitle ? options.getTitle(req) : getDefaultTitle(req, options.module),
          description: options.getDescription ? options.getDescription(req) : getDefaultDescription(req, options.module),
          userId: req.user?.id,
          status: "SUCCESS" as const,
          severity: options.severity || "INFO" as const,
          changes: req.method === "PUT" || req.method === "PATCH" ? req.body : undefined,
        };

        // Log activity asynchronously without blocking response
        ActivityLogger.logFromRequest(req, logData).catch(error => {
          console.error("Failed to log activity:", error);
        });
      }

      return originalJson.call(this, body);
    };

    next();
  };
};

// Helper function to determine activity type from HTTP method
function getTypeFromMethod(method: string): string {
  switch (method.toUpperCase()) {
    case "POST":
      return "CREATE";
    case "PUT":
    case "PATCH":
      return "UPDATE";
    case "DELETE":
      return "DELETE";
    case "GET":
      return "OTHER"; // Usually we don't log GET requests
    default:
      return "OTHER";
  }
}

// Helper function to generate default title
function getDefaultTitle(req: Request, module: string): string {
  const method = req.method.toUpperCase();
  const entity = req.route?.path?.split('/')[1] || 'item';
  
  switch (method) {
    case "POST":
      return `${entity.charAt(0).toUpperCase() + entity.slice(1)} Created`;
    case "PUT":
    case "PATCH":
      return `${entity.charAt(0).toUpperCase() + entity.slice(1)} Updated`;
    case "DELETE":
      return `${entity.charAt(0).toUpperCase() + entity.slice(1)} Deleted`;
    default:
      return `${module} Activity`;
  }
}

// Helper function to generate default description
function getDefaultDescription(req: Request, module: string): string {
  const method = req.method.toUpperCase();
  const entity = req.route?.path?.split('/')[1] || 'item';
  
  switch (method) {
    case "POST":
      return `A new ${entity} was created in the ${module} module`;
    case "PUT":
    case "PATCH":
      return `An existing ${entity} was updated in the ${module} module`;
    case "DELETE":
      return `An ${entity} was deleted from the ${module} module`;
    default:
      return `Activity performed in the ${module} module`;
  }
}
