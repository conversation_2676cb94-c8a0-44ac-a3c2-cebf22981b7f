import { PrismaClient } from "@prisma/client";
import { Request } from "express";

const prisma = new PrismaClient();

export interface ActivityLogData {
  type: string;
  action: string;
  module: string;
  entity?: string;
  entityId?: string;
  title: string;
  description?: string;
  metadata?: any;
  userId?: number;
  ipAddress?: string;
  userAgent?: string;
  status?: "SUCCESS" | "FAILED" | "PENDING" | "CANCELLED";
  severity?: "LOW" | "INFO" | "WARNING" | "ERROR" | "CRITICAL";
  changes?: any;
}

export class ActivityLogger {
  static async log(data: ActivityLogData): Promise<void> {
    try {
      await prisma.activityLog.create({
        data: {
          type: data.type as any,
          action: data.action,
          module: data.module,
          entity: data.entity,
          entityId: data.entityId,
          title: data.title,
          description: data.description,
          metadata: data.metadata,
          userId: data.userId,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          status: data.status || "SUCCESS",
          severity: data.severity || "INFO",
          changes: data.changes,
        },
      });
    } catch (error) {
      console.error("Failed to log activity:", error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  static async logFromRequest(req: Request, data: Omit<ActivityLogData, 'ipAddress' | 'userAgent'>): Promise<void> {
    const ipAddress = this.getClientIP(req);
    const userAgent = req.get('User-Agent');

    await this.log({
      ...data,
      ipAddress,
      userAgent,
    });
  }

  static async logUserLogin(userId: number, req: Request, success: boolean = true): Promise<void> {
    await this.logFromRequest(req, {
      type: "LOGIN",
      action: "login",
      module: "authentication",
      entity: "user",
      entityId: userId.toString(),
      title: success ? "User Login" : "Failed Login Attempt",
      description: success 
        ? "User successfully logged into the system"
        : "Failed login attempt detected",
      userId: success ? userId : undefined,
      status: success ? "SUCCESS" : "FAILED",
      severity: success ? "INFO" : "WARNING",
    });
  }

  static async logUserLogout(userId: number, req: Request): Promise<void> {
    await this.logFromRequest(req, {
      type: "LOGOUT",
      action: "logout",
      module: "authentication",
      entity: "user",
      entityId: userId.toString(),
      title: "User Logout",
      description: "User logged out of the system",
      userId,
      status: "SUCCESS",
      severity: "INFO",
    });
  }

  static async logUserUpdate(userId: number, changes: any, req: Request, updatedByUserId?: number): Promise<void> {
    await this.logFromRequest(req, {
      type: "USER_UPDATE",
      action: "update",
      module: "user_management",
      entity: "user",
      entityId: userId.toString(),
      title: "User Profile Updated",
      description: `User profile information was updated`,
      userId: updatedByUserId || userId,
      status: "SUCCESS",
      severity: "INFO",
      changes,
      metadata: {
        targetUserId: userId,
        updatedFields: Object.keys(changes),
      },
    });
  }

  static async logUserCreate(userId: number, req: Request, createdByUserId: number): Promise<void> {
    await this.logFromRequest(req, {
      type: "USER_CREATE",
      action: "create",
      module: "user_management",
      entity: "user",
      entityId: userId.toString(),
      title: "New User Created",
      description: "A new user account was created in the system",
      userId: createdByUserId,
      status: "SUCCESS",
      severity: "INFO",
      metadata: {
        targetUserId: userId,
      },
    });
  }

  static async logPasswordChange(userId: number, req: Request): Promise<void> {
    await this.logFromRequest(req, {
      type: "PASSWORD_CHANGE",
      action: "update",
      module: "authentication",
      entity: "user",
      entityId: userId.toString(),
      title: "Password Changed",
      description: "User password was changed",
      userId,
      status: "SUCCESS",
      severity: "INFO",
    });
  }

  static async logSecurityEvent(
    title: string, 
    description: string, 
    req: Request, 
    userId?: number,
    severity: "LOW" | "INFO" | "WARNING" | "ERROR" | "CRITICAL" = "WARNING"
  ): Promise<void> {
    await this.logFromRequest(req, {
      type: "SECURITY_VIOLATION",
      action: "security_event",
      module: "security",
      title,
      description,
      userId,
      status: "FAILED",
      severity,
    });
  }

  static async logDataOperation(
    operation: "CREATE" | "UPDATE" | "DELETE",
    entity: string,
    entityId: string,
    title: string,
    description: string,
    req: Request,
    userId: number,
    changes?: any
  ): Promise<void> {
    await this.logFromRequest(req, {
      type: operation,
      action: operation.toLowerCase(),
      module: "data_management",
      entity,
      entityId,
      title,
      description,
      userId,
      status: "SUCCESS",
      severity: "INFO",
      changes,
    });
  }

  static async logSystemEvent(
    type: string,
    title: string,
    description: string,
    metadata?: any,
    severity: "LOW" | "INFO" | "WARNING" | "ERROR" | "CRITICAL" = "INFO"
  ): Promise<void> {
    await this.log({
      type,
      action: "system_event",
      module: "system",
      title,
      description,
      status: "SUCCESS",
      severity,
      metadata,
    });
  }

  private static getClientIP(req: Request): string {
    return (
      (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (req.headers['x-real-ip'] as string) ||
      req.connection?.remoteAddress ||
      req.socket?.remoteAddress ||
      'unknown'
    );
  }
}

export default ActivityLogger;
