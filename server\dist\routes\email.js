"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const route_helpers_js_1 = require("../utils/route-helpers.js");
const emailConfirmation_controller_js_1 = require("../controllers/auth/emailConfirmation.controller.js");
const email_controller_js_1 = require("../controllers/email/email.controller.js");
const router = (0, express_1.Router)();
// Email confirmation route
router.get("/confirm-email", (0, route_helpers_js_1.wrapController)(emailConfirmation_controller_js_1.confirmEmail));
// Email management routes
router.get("/folders", (0, route_helpers_js_1.wrapController)(email_controller_js_1.getEmailFolders));
router.get("/folder/:folderType", (0, route_helpers_js_1.wrapController)(email_controller_js_1.getEmailsByFolder));
router.post("/send", (0, route_helpers_js_1.wrapController)(email_controller_js_1.sendEmail));
router.patch("/:messageId/status", (0, route_helpers_js_1.wrapController)(email_controller_js_1.updateEmailStatus));
router.delete("/:messageId", (0, route_helpers_js_1.wrapController)(email_controller_js_1.deleteEmail));
exports.default = router;
//# sourceMappingURL=email.js.map