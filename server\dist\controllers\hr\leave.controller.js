"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLeaveBalance = exports.deleteLeaveRequest = exports.updateLeaveRequest = exports.createLeaveRequest = exports.getLeaveRequests = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
const getLeaveRequests = async (req, res, next) => {
    try {
        const requests = await prisma_js_1.prisma.leaveRequest.findMany({
            where: {
                employee: {
                    companyId: Number(req.user?.companyId),
                },
            },
            include: {
                employee: true,
            },
        });
        res.json(requests);
    }
    catch (error) {
        next(createHttpError(500, "Failed to fetch leave requests"));
    }
};
exports.getLeaveRequests = getLeaveRequests;
const createLeaveRequest = async (req, res, next) => {
    try {
        const payload = req.body;
        // Convert employeeId to number if it's a string
        if (typeof payload.employeeId === "string") {
            payload.employeeId = Number(payload.employeeId);
        }
        const request = await prisma_js_1.prisma.leaveRequest.create({
            data: {
                employeeId: payload.employeeId,
                startDate: new Date(payload.startDate),
                endDate: new Date(payload.endDate),
                leaveType: payload.leaveType,
                reason: payload.reason,
                status: payload.status || undefined,
                approvedBy: req.user?.id,
            },
            include: {
                employee: true,
            },
        });
        res.status(201).json(request);
    }
    catch (error) {
        next(createHttpError(500, "Failed to create leave request"));
    }
};
exports.createLeaveRequest = createLeaveRequest;
const updateLeaveRequest = async (req, res, next) => {
    try {
        const { id } = req.params;
        const payload = req.body;
        // Convert employeeId to number if it's a string
        if (typeof payload.employeeId === "string") {
            payload.employeeId = Number(payload.employeeId);
        }
        const request = await prisma_js_1.prisma.leaveRequest.update({
            where: { id: Number(id) },
            data: {
                employeeId: payload.employeeId,
                startDate: payload.startDate ? new Date(payload.startDate) : undefined,
                endDate: payload.endDate ? new Date(payload.endDate) : undefined,
                leaveType: payload.leaveType,
                reason: payload.reason,
                status: payload.status || undefined,
            },
            include: {
                employee: true,
            },
        });
        res.json(request);
    }
    catch (error) {
        if (error.code === "P2025") {
            return next(createHttpError(404, "Leave request not found"));
        }
        next(createHttpError(500, "Failed to update leave request"));
    }
};
exports.updateLeaveRequest = updateLeaveRequest;
const deleteLeaveRequest = async (req, res, next) => {
    try {
        const { id } = req.params;
        await prisma_js_1.prisma.leaveRequest.delete({
            where: { id: Number(id) },
        });
        res.status(204).send();
    }
    catch (error) {
        if (error.code === "P2025") {
            return next(createHttpError(404, "Leave request not found"));
        }
        next(createHttpError(500, "Failed to delete leave request"));
    }
};
exports.deleteLeaveRequest = deleteLeaveRequest;
const getLeaveBalance = async (req, res, next) => {
    try {
        const { employeeId } = req.params;
        // Use findFirst instead of findUnique since employeeId is not a unique field by itself
        const balance = await prisma_js_1.prisma.leaveBalance.findFirst({
            where: {
                employeeId: Number(employeeId),
            },
            include: {
                employee: true,
            },
        });
        if (!balance) {
            return next(createHttpError(404, "Leave balance not found"));
        }
        res.json(balance);
    }
    catch (error) {
        next(createHttpError(500, "Failed to fetch leave balance"));
    }
};
exports.getLeaveBalance = getLeaveBalance;
//# sourceMappingURL=leave.controller.js.map