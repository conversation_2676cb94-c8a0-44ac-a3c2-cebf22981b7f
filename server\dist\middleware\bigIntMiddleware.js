"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bigIntMiddleware = bigIntMiddleware;
const bigIntReplacer_js_1 = require("../utils/bigIntReplacer.js");
function bigIntMiddleware(req, res, next) {
    const oldJson = res.json;
    res.json = function (data) {
        const replacedData = (0, bigIntReplacer_js_1.bigIntReplacer)("", data);
        return oldJson.call(this, replacedData);
    };
    next();
}
//# sourceMappingURL=bigIntMiddleware.js.map