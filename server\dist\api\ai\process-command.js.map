{"version": 3, "file": "process-command.js", "sourceRoot": "", "sources": ["../../../api/ai/process-command.ts"], "names": [], "mappings": ";;AACA,mCAAgC;AAEhC,2BAA2B;AAC3B,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC;IACxB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;CACnC,CAAC,CAAC;AAEH,iCAAiC;AACjC,MAAM,WAAW,GAAG,CAAC,OAAgD,EAAE,EAAE;IACvE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACxC,KAAa,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IAC/C,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,kBAAe,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACnD,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAEtC,MAAM,cAAc,GAAG;QACrB,EAAE,EAAE;YACF,eAAe;YACf,cAAc;YACd,iBAAiB;YACjB,kBAAkB;YAClB,0BAA0B;SAC3B;QACD,SAAS,EAAE;YACT,gBAAgB;YAChB,cAAc;YACd,sBAAsB;YACtB,gBAAgB;YAChB,sBAAsB;SACvB;QACD,UAAU,EAAE;YACV,gBAAgB;YAChB,aAAa;YACb,kBAAkB;YAClB,iBAAiB;YACjB,oBAAoB;SACrB;QACD,MAAM,EAAE;YACN,eAAe;YACf,aAAa;YACb,YAAY;YACZ,cAAc;YACd,iBAAiB;SAClB;QACD,UAAU,EAAE;YACV,cAAc;YACd,aAAa;YACb,oBAAoB;YACpB,iBAAiB;YACjB,kBAAkB;SACnB;KACF,CAAC;IAEF,MAAM,YAAY,GAAG;;;;MAIjB,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC;SAC7B,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,MAAM,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;SAChE,IAAI,CAAC,IAAI,CAAC;;;cAGH,OAAO,CAAC,WAAW;gBACjB,OAAO,CAAC,aAAa;8BACP,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;GAQ9D,CAAC;IAEF,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD,KAAK,EAAE,aAAa;YACpB,QAAQ,EAAE;gBACR,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE;gBACzC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;aACnC;YACD,WAAW,EAAE,GAAG;YAChB,eAAe,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;SACzC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC;IAC7E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACrD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,KAAK,EAAE,8BAA8B;YACrC,OAAO,EAAG,KAAe,CAAC,OAAO;SAClC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC"}