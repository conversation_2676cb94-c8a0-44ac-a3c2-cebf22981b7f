import type { ComputedRef, MaybeRef } from 'vue'
export type LayoutKey = "admin-layout" | "default" | "landing" | "settings" | "user-layout" | "empty" | "sidebar" | "iconnav"
declare module "../../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/pages/runtime/composables" {
  interface PageMeta {
    layout?: MaybeRef<LayoutKey | false> | ComputedRef<LayoutKey | false>
  }
}