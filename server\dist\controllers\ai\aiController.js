"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.textToSpeech = exports.processCommand = void 0;
const openai_1 = require("openai");
const openai = new openai_1.OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});
const processCommand = async (req, res) => {
    const { command, context, language = "en" } = req.body;
    console.log(`AI Controller: Processing command in language: ${language}`);
    // Determine which language to use for the response
    const responseLanguage = language === "et" ? "Estonian" : "English";
    const systemPrompt = `
    You are an AI assistant helping users navigate and interact with an enterprise management application.
    Your responses must be in JSON format.

    IMPORTANT: You must respond in ${responseLanguage} language. The user is using the application in ${responseLanguage}.

    Navigation Structure:
    Main Menu:
    ${JSON.stringify(context.navigation.mainMenu, null, 2)}

    Module Menus:
    ${JSON.stringify(context.navigation.moduleMenus, null, 2)}

    Current Context:
    - Path: ${context.currentPath}
    - Module: ${context.currentModule}
    - Available UI Actions: ${context.availableIntents.join(", ")}
    - Current Location: ${JSON.stringify(context.currentLocation, null, 2)}

    Command types:
    1. Navigation commands - Return: { "action": "navigate:[path]", "message": "..." }
    2. UI interaction commands - Return: { "action": "ui:[intent]:[value]", "message": "..." }
    3. Questions about available actions - Return: { "action": "explain", "message": "detailed explanation" }
    4. General questions - Return: { "action": "explain", "message": "conversational answer" }

    When asked about navigation or menu structure, provide information from the Navigation Structure above.
    Keep responses concise and natural. For UI interactions, confirm the action in the message.

    Remember to respond in ${responseLanguage} language only.
  `;
    try {
        const completion = await openai.chat.completions.create({
            model: "gpt-4-turbo",
            messages: [
                { role: "system", content: systemPrompt },
                { role: "user", content: command },
            ],
            temperature: 0.2,
            response_format: { type: "json_object" },
        });
        return res.json(JSON.parse(completion.choices[0].message.content || "{}"));
    }
    catch (error) {
        console.error("OpenAI API Error:", error);
        return res.status(500).json({
            error: "Failed to process AI command",
            details: error.message,
        });
    }
};
exports.processCommand = processCommand;
const textToSpeech = async (req, res) => {
    const { text, language = "en" } = req.body;
    console.log(`AI Controller: Converting text to speech in language: ${language}`);
    // Map language code to OpenAI voice
    // OpenAI doesn't have Estonian voices, so we'll use a similar voice for Estonian
    const voiceMap = {
        en: "nova", // English - female voice
        et: "alloy", // Estonian - using alloy as it might sound better for Estonian
    };
    const voice = voiceMap[language] || "nova";
    try {
        const response = await openai.audio.speech.create({
            model: "tts-1",
            voice: voice,
            input: text,
        });
        console.log(`AI Controller: Generated speech using voice: ${voice}`);
        const buffer = Buffer.from(await response.arrayBuffer());
        res.setHeader("Content-Type", "audio/mpeg");
        res.send(buffer);
    }
    catch (error) {
        console.error("OpenAI API Error:", error);
        res.status(500).json({
            error: "Failed to convert text to speech",
            details: error.message,
        });
    }
};
exports.textToSpeech = textToSpeech;
//# sourceMappingURL=aiController.js.map