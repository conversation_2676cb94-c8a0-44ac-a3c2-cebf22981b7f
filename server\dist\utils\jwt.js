"use strict";
// /server/utils/jwt.js
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateAccessToken = generateAccessToken;
exports.generateRefreshToken = generateRefreshToken;
exports.verifyToken = verifyToken;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const JWT_SECRET = process.env.JWT_SECRET || "your_secret_key";
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || "your_refresh_secret_key";
/**
 * Generates an access token for the given user
 * @param user The user object containing id and roles
 * @returns The generated access token
 */
function generateAccessToken(user) {
    return jsonwebtoken_1.default.sign({ userId: user.id, roles: user.roles }, JWT_SECRET, { expiresIn: "365d" } // Set to 1 year or longer, effectively indefinite
    );
}
/**
 * Generates a refresh token for the given user
 * @param user The user object containing id
 * @returns The generated refresh token
 */
function generateRefreshToken(user) {
    return jsonwebtoken_1.default.sign({ userId: user.id }, JWT_REFRESH_SECRET, { expiresIn: "365d" } // Set refresh token to a long expiration time as well
    );
}
/**
 * Verifies a JWT token
 * @param token The token to verify
 * @param isRefreshToken Whether this is a refresh token
 * @returns The decoded token payload
 */
function verifyToken(token, isRefreshToken = false) {
    try {
        const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;
        return jsonwebtoken_1.default.verify(token, secret);
    }
    catch (error) {
        throw new Error("Invalid or expired token");
    }
}
//# sourceMappingURL=jwt.js.map