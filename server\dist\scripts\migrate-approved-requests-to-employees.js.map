{"version": 3, "file": "migrate-approved-requests-to-employees.js", "sourceRoot": "", "sources": ["../../scripts/migrate-approved-requests-to-employees.js"], "names": [], "mappings": ";;AAAA,0EAA0E;AAC1E,gDAA0C;AAE1C,KAAK,UAAU,kCAAkC;IAC/C,OAAO,CAAC,GAAG,CACT,mEAAmE,CACpE,CAAC;IAEF,IAAI,CAAC;QACH,iCAAiC;QACjC,MAAM,gBAAgB,GAAG,MAAM,kBAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAChE,KAAK,EAAE;gBACL,MAAM,EAAE,UAAU;aACnB;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,SAAS,gBAAgB,CAAC,MAAM,yBAAyB,CAAC,CAAC;QAEvE,gCAAgC;QAChC,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;YACvC,IAAI,CAAC;gBACH,6CAA6C;gBAC7C,MAAM,gBAAgB,GAAG,MAAM,kBAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;oBACvD,KAAK,EAAE;wBACL,MAAM,EAAE,OAAO,CAAC,MAAM;wBACtB,SAAS,EAAE,OAAO,CAAC,SAAS;qBAC7B;iBACF,CAAC,CAAC;gBAEH,IAAI,gBAAgB,EAAE,CAAC;oBACrB,OAAO,CAAC,GAAG,CACT,2CAA2C,OAAO,CAAC,MAAM,eAAe,OAAO,CAAC,SAAS,EAAE,CAC5F,CAAC;oBACF,SAAS;gBACX,CAAC;gBAED,+BAA+B;gBAC/B,MAAM,kBAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAC3B,IAAI,EAAE;wBACJ,MAAM,EAAE,OAAO,CAAC,MAAM;wBACtB,SAAS,EAAE,OAAO,CAAC,SAAS;wBAC5B,QAAQ,EACN,OAAO,CAAC,IAAI,KAAK,eAAe;4BAC9B,CAAC,CAAC,gBAAgB;4BAClB,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU;gCAC7B,CAAC,CAAC,sBAAsB;gCACxB,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;oCAC3B,CAAC,CAAC,QAAQ;oCACV,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;wCAC3B,CAAC,CAAC,QAAQ;wCACV,CAAC,CAAC,UAAU;wBAChB,UAAU,EACR,OAAO,CAAC,IAAI,KAAK,eAAe;4BAC9B,CAAC,CAAC,oBAAoB;4BACtB,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU;gCAC7B,CAAC,CAAC,OAAO;gCACT,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;oCAC3B,CAAC,CAAC,YAAY;oCACd,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;wCAC3B,CAAC,CAAC,kBAAkB;wCACpB,CAAC,CAAC,SAAS;wBACf,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE;wBAC1C,MAAM,EAAE,CAAC,EAAE,sCAAsC;wBACjD,MAAM,EAAE,QAAQ;wBAChB,cAAc,EAAE,WAAW,EAAE,0BAA0B;qBACxD;iBACF,CAAC,CAAC;gBAEH,OAAO,CAAC,GAAG,CACT,oCAAoC,OAAO,CAAC,MAAM,eAAe,OAAO,CAAC,SAAS,EAAE,CACrF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,MAAM,mBAAmB,GAAG,MAAM,kBAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAClE,KAAK,EAAE;gBACL,MAAM,EAAE,UAAU;aACnB;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,SAAS,mBAAmB,CAAC,MAAM,uBAAuB,CAAC,CAAC;QAExE,mCAAmC;QACnC,KAAK,MAAM,UAAU,IAAI,mBAAmB,EAAE,CAAC;YAC7C,IAAI,CAAC;gBACH,4CAA4C;gBAC5C,MAAM,WAAW,GAAG,MAAM,kBAAM,CAAC,WAAW,CAAC,SAAS,CAAC;oBACrD,KAAK,EAAE;wBACL,SAAS,EAAE,UAAU,CAAC,SAAS;qBAChC;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE,IAAI;qBACX;iBACF,CAAC,CAAC;gBAEH,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,OAAO,CAAC,GAAG,CAAC,gCAAgC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC7D,SAAS;gBACX,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,gBAAgB,GAAG,MAAM,kBAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;oBACvD,KAAK,EAAE;wBACL,MAAM,EAAE,WAAW,CAAC,MAAM;wBAC1B,SAAS,EAAE,UAAU,CAAC,SAAS;qBAChC;iBACF,CAAC,CAAC;gBAEH,IAAI,gBAAgB,EAAE,CAAC;oBACrB,OAAO,CAAC,GAAG,CACT,2CAA2C,WAAW,CAAC,MAAM,eAAe,UAAU,CAAC,SAAS,EAAE,CACnG,CAAC;oBACF,SAAS;gBACX,CAAC;gBAED,+BAA+B;gBAC/B,MAAM,kBAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAC3B,IAAI,EAAE;wBACJ,MAAM,EAAE,WAAW,CAAC,MAAM;wBAC1B,SAAS,EAAE,UAAU,CAAC,SAAS;wBAC/B,QAAQ,EACN,UAAU,CAAC,IAAI,KAAK,eAAe;4BACjC,CAAC,CAAC,gBAAgB;4BAClB,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK,UAAU;gCAChC,CAAC,CAAC,sBAAsB;gCACxB,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK,QAAQ;oCAC9B,CAAC,CAAC,QAAQ;oCACV,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK,QAAQ;wCAC9B,CAAC,CAAC,QAAQ;wCACV,CAAC,CAAC,UAAU;wBAChB,UAAU,EACR,UAAU,CAAC,IAAI,KAAK,eAAe;4BACjC,CAAC,CAAC,oBAAoB;4BACtB,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK,UAAU;gCAChC,CAAC,CAAC,OAAO;gCACT,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK,QAAQ;oCAC9B,CAAC,CAAC,YAAY;oCACd,CAAC,CAAC,UAAU,CAAC,IAAI,KAAK,QAAQ;wCAC9B,CAAC,CAAC,kBAAkB;wCACpB,CAAC,CAAC,SAAS;wBACf,SAAS,EAAE,UAAU,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE;wBAC7C,MAAM,EAAE,CAAC,EAAE,sCAAsC;wBACjD,MAAM,EAAE,QAAQ;wBAChB,cAAc,EAAE,WAAW,EAAE,0BAA0B;qBACxD;iBACF,CAAC,CAAC;gBAEH,OAAO,CAAC,GAAG,CACT,oCAAoC,WAAW,CAAC,MAAM,eAAe,UAAU,CAAC,SAAS,EAAE,CAC5F,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,UAAU,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAClD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;YAAS,CAAC;QACT,MAAM,kBAAM,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC;AACH,CAAC;AAED,oBAAoB;AACpB,kCAAkC,EAAE;KACjC,IAAI,CAAC,GAAG,EAAE;IACT,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC1C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;KACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;IACjD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}