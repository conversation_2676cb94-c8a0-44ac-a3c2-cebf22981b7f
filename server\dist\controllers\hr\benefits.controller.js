"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeEmployeeBenefit = exports.updateEmployeeBenefit = exports.getEmployeeBenefits = exports.assignBenefitToEmployee = exports.deleteBenefit = exports.updateBenefit = exports.createBenefit = exports.getBenefitById = exports.getAllBenefits = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Using centralized prisma instance from lib/prisma.js
// Get all benefits
const getAllBenefits = async (req, res, next) => {
    try {
        const { companyId } = req.query;
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        const benefits = await prisma_js_1.prisma.benefit.findMany({
            where: {
                companyId: Number(companyId),
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(benefits);
    }
    catch (error) {
        console.error("Error fetching benefits:", error);
        next(createHttpError(500, "Failed to fetch benefits"));
    }
};
exports.getAllBenefits = getAllBenefits;
// Get a specific benefit by ID
const getBenefitById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const benefit = await prisma_js_1.prisma.benefit.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!benefit) {
            return next(createHttpError(404, "Benefit not found"));
        }
        res.json(benefit);
    }
    catch (error) {
        console.error("Error fetching benefit:", error);
        next(createHttpError(500, "Failed to fetch benefit"));
    }
};
exports.getBenefitById = getBenefitById;
// Create a new benefit
const createBenefit = async (req, res, next) => {
    try {
        const { name, description, type, provider, policyNumber, coverage, premium, employerContribution, startDate, endDate, status, companyId, } = req.body;
        if (!name || !type || !companyId) {
            return next(createHttpError(400, "Name, type, and company ID are required"));
        }
        const benefit = await prisma_js_1.prisma.benefit.create({
            data: {
                name,
                description,
                type,
                provider,
                policyNumber,
                coverage: coverage ? parseFloat(coverage) : null,
                premium: premium ? parseFloat(premium) : null,
                employerContribution: employerContribution
                    ? parseFloat(employerContribution)
                    : null,
                startDate: startDate ? new Date(startDate) : new Date(),
                endDate: endDate ? new Date(endDate) : undefined,
                status: status || "ACTIVE",
                companyId: Number(companyId),
            },
        });
        res.status(201).json(benefit);
    }
    catch (error) {
        console.error("Error creating benefit:", error);
        next(createHttpError(500, "Failed to create benefit"));
    }
};
exports.createBenefit = createBenefit;
// Update a benefit
const updateBenefit = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { name, description, type, provider, policyNumber, coverage, premium, employerContribution, startDate, endDate, status, } = req.body;
        const benefit = await prisma_js_1.prisma.benefit.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!benefit) {
            return next(createHttpError(404, "Benefit not found"));
        }
        const updatedBenefit = await prisma_js_1.prisma.benefit.update({
            where: {
                id: Number(id),
            },
            data: {
                name: name !== undefined ? name : undefined,
                description: description !== undefined ? description : undefined,
                type: type !== undefined ? type : undefined,
                provider: provider !== undefined ? provider : undefined,
                policyNumber: policyNumber !== undefined ? policyNumber : undefined,
                coverage: coverage !== undefined ? parseFloat(coverage) : undefined,
                premium: premium !== undefined ? parseFloat(premium) : undefined,
                employerContribution: employerContribution !== undefined
                    ? parseFloat(employerContribution)
                    : undefined,
                startDate: startDate !== undefined ? new Date(startDate) : undefined,
                endDate: endDate !== undefined ? new Date(endDate) : undefined,
                status: status !== undefined ? status : undefined,
            },
        });
        res.json(updatedBenefit);
    }
    catch (error) {
        console.error("Error updating benefit:", error);
        next(createHttpError(500, "Failed to update benefit"));
    }
};
exports.updateBenefit = updateBenefit;
// Delete a benefit
const deleteBenefit = async (req, res, next) => {
    try {
        const { id } = req.params;
        const benefit = await prisma_js_1.prisma.benefit.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!benefit) {
            return next(createHttpError(404, "Benefit not found"));
        }
        // Check if there are any employee benefits associated with this benefit
        const employeeBenefits = await prisma_js_1.prisma.employeeBenefit.findMany({
            where: {
                benefitId: Number(id),
            },
        });
        if (employeeBenefits.length > 0) {
            // Delete all employee benefits associated with this benefit
            await prisma_js_1.prisma.employeeBenefit.deleteMany({
                where: {
                    benefitId: Number(id),
                },
            });
        }
        // Delete the benefit
        await prisma_js_1.prisma.benefit.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Benefit deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting benefit:", error);
        next(createHttpError(500, "Failed to delete benefit"));
    }
};
exports.deleteBenefit = deleteBenefit;
// Assign a benefit to an employee
const assignBenefitToEmployee = async (req, res, next) => {
    try {
        const { employeeId, benefitId, effectiveDate, coverageAmount, employeeContribution, notes, status, } = req.body;
        if (!employeeId || !benefitId) {
            return next(createHttpError(400, "Employee ID and benefit ID are required"));
        }
        // Check if employee exists
        const employee = await prisma_js_1.prisma.employee.findUnique({
            where: {
                id: Number(employeeId),
            },
        });
        if (!employee) {
            return next(createHttpError(404, "Employee not found"));
        }
        // Check if benefit exists
        const benefit = await prisma_js_1.prisma.benefit.findUnique({
            where: {
                id: Number(benefitId),
            },
        });
        if (!benefit) {
            return next(createHttpError(404, "Benefit not found"));
        }
        // Check if assignment already exists
        const existingAssignment = await prisma_js_1.prisma.employeeBenefit.findFirst({
            where: {
                employeeId: Number(employeeId),
                benefitId: Number(benefitId),
            },
        });
        if (existingAssignment) {
            return next(createHttpError(409, "Benefit is already assigned to this employee"));
        }
        const employeeBenefit = await prisma_js_1.prisma.employeeBenefit.create({
            data: {
                employeeId: Number(employeeId),
                benefitId: Number(benefitId),
                effectiveDate: effectiveDate ? new Date(effectiveDate) : new Date(),
                coverageAmount: coverageAmount ? parseFloat(coverageAmount) : null,
                employeeContribution: employeeContribution
                    ? parseFloat(employeeContribution)
                    : null,
                notes,
                status: status || "ACTIVE",
            },
        });
        res.status(201).json(employeeBenefit);
    }
    catch (error) {
        console.error("Error assigning benefit to employee:", error);
        next(createHttpError(500, "Failed to assign benefit to employee"));
    }
};
exports.assignBenefitToEmployee = assignBenefitToEmployee;
// Get all benefits for an employee
const getEmployeeBenefits = async (req, res, next) => {
    try {
        const { employeeId } = req.params;
        const employeeBenefits = await prisma_js_1.prisma.employeeBenefit.findMany({
            where: {
                employeeId: Number(employeeId),
            },
            include: {
                benefit: true,
                employee: {
                    include: {
                        user: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(employeeBenefits);
    }
    catch (error) {
        console.error("Error fetching employee benefits:", error);
        next(createHttpError(500, "Failed to fetch employee benefits"));
    }
};
exports.getEmployeeBenefits = getEmployeeBenefits;
// Update an employee benefit
const updateEmployeeBenefit = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { effectiveDate, coverageAmount, employeeContribution, notes, status, } = req.body;
        const employeeBenefit = await prisma_js_1.prisma.employeeBenefit.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!employeeBenefit) {
            return next(createHttpError(404, "Employee benefit not found"));
        }
        const updatedEmployeeBenefit = await prisma_js_1.prisma.employeeBenefit.update({
            where: {
                id: Number(id),
            },
            data: {
                effectiveDate: effectiveDate !== undefined ? new Date(effectiveDate) : undefined,
                coverageAmount: coverageAmount !== undefined ? parseFloat(coverageAmount) : undefined,
                employeeContribution: employeeContribution !== undefined
                    ? parseFloat(employeeContribution)
                    : undefined,
                notes: notes !== undefined ? notes : undefined,
                status: status !== undefined ? status : undefined,
            },
        });
        res.json(updatedEmployeeBenefit);
    }
    catch (error) {
        console.error("Error updating employee benefit:", error);
        next(createHttpError(500, "Failed to update employee benefit"));
    }
};
exports.updateEmployeeBenefit = updateEmployeeBenefit;
// Remove a benefit from an employee
const removeEmployeeBenefit = async (req, res, next) => {
    try {
        const { id } = req.params;
        const employeeBenefit = await prisma_js_1.prisma.employeeBenefit.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!employeeBenefit) {
            return next(createHttpError(404, "Employee benefit not found"));
        }
        await prisma_js_1.prisma.employeeBenefit.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Benefit removed from employee successfully" });
    }
    catch (error) {
        console.error("Error removing benefit from employee:", error);
        next(createHttpError(500, "Failed to remove employee benefit"));
    }
};
exports.removeEmployeeBenefit = removeEmployeeBenefit;
//# sourceMappingURL=benefits.controller.js.map