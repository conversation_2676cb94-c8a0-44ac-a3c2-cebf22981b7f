"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchJobRequests = exports.addPreferredWorker = exports.updateJobRequest = exports.createJobRequest = exports.getJobRequestById = exports.getAllJobRequests = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const client_1 = require("@prisma/client");
// Using centralized prisma instance from lib/prisma.js
// Get all job requests
const getAllJobRequests = async (req, res) => {
    try {
        const jobRequests = await prisma_js_1.prisma.jobRequest.findMany({
            include: {
                company: true,
                preferredWorkers: true,
                matches: {
                    include: {
                        worker: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json(jobRequests);
    }
    catch (error) {
        console.error("Error fetching job requests:", error);
        return res.status(500).json({ error: "Failed to fetch job requests" });
    }
};
exports.getAllJobRequests = getAllJobRequests;
// Get job request by ID
const getJobRequestById = async (req, res) => {
    const { id } = req.params;
    try {
        const jobRequest = await prisma_js_1.prisma.jobRequest.findUnique({
            where: { id: Number(id) },
            include: {
                company: true,
                preferredWorkers: true,
                matches: {
                    include: {
                        worker: {
                            include: {
                                worker: {
                                    include: {
                                        skills: true,
                                        certifications: true,
                                    },
                                },
                            },
                        },
                    },
                },
                assignments: {
                    include: {
                        worker: true,
                        timeEntries: true,
                    },
                },
            },
        });
        if (!jobRequest) {
            return res.status(404).json({ error: "Job request not found" });
        }
        // Increment view count
        await prisma_js_1.prisma.jobRequest.update({
            where: { id: Number(id) },
            data: {
                views: { increment: 1 },
            },
        });
        return res.status(200).json(jobRequest);
    }
    catch (error) {
        console.error("Error fetching job request:", error);
        return res.status(500).json({ error: "Failed to fetch job request" });
    }
};
exports.getJobRequestById = getJobRequestById;
// Create job request
const createJobRequest = async (req, res) => {
    const { companyId, title, specialization, description, requiredSkills, requiredCertifications, experienceLevel, location, startDate, endDate, duration, workingHours, budget, rateType, urgency, preferredWorkers, } = req.body;
    try {
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return res.status(404).json({ error: "Company not found" });
        }
        // Create job request
        const jobRequest = await prisma_js_1.prisma.jobRequest.create({
            data: {
                company: {
                    connect: { id: Number(companyId) },
                },
                title,
                specialization,
                description,
                requiredSkills,
                requiredCertifications,
                experienceLevel: experienceLevel,
                location,
                startDate: new Date(startDate),
                endDate: endDate ? new Date(endDate) : null,
                duration: duration ? Number(duration) : null,
                workingHours: workingHours
                    ? client_1.WorkingHoursType.fullTime
                    : undefined,
                budget: budget ? Number(budget) : null,
                rateType: rateType,
                urgency: urgency,
                status: client_1.JobRequestStatus.open,
                preferredWorkers: {
                    create: preferredWorkers?.map((worker) => ({
                        workerId: Number(worker.workerId),
                        priority: worker.priority ? Number(worker.priority) : 1,
                        note: worker.note,
                    })) || [],
                },
            },
            include: {
                preferredWorkers: true,
            },
        });
        // Generate AI matches for this job request
        await generateMatches(jobRequest.id.toString());
        return res.status(201).json(jobRequest);
    }
    catch (error) {
        console.error("Error creating job request:", error);
        return res.status(500).json({ error: "Failed to create job request" });
    }
};
exports.createJobRequest = createJobRequest;
// Update job request
const updateJobRequest = async (req, res) => {
    const { id } = req.params;
    const { title, specialization, description, requiredSkills, requiredCertifications, experienceLevel, location, startDate, endDate, duration, workingHours, budget, rateType, urgency, status, } = req.body;
    try {
        const jobRequest = await prisma_js_1.prisma.jobRequest.update({
            where: { id: Number(id) },
            data: {
                title,
                specialization,
                description,
                requiredSkills,
                requiredCertifications,
                experienceLevel: experienceLevel,
                location,
                startDate: startDate ? new Date(startDate) : undefined,
                endDate: endDate ? new Date(endDate) : null,
                duration: duration ? Number(duration) : undefined,
                workingHours: workingHours
                    ? client_1.WorkingHoursType.fullTime
                    : undefined,
                budget: budget ? Number(budget) : undefined,
                rateType: rateType,
                urgency: urgency,
                status: status,
            },
        });
        // If job request was updated significantly, regenerate matches
        if (specialization ||
            requiredSkills ||
            requiredCertifications ||
            experienceLevel) {
            await generateMatches(id);
        }
        return res.status(200).json(jobRequest);
    }
    catch (error) {
        console.error("Error updating job request:", error);
        return res.status(500).json({ error: "Failed to update job request" });
    }
};
exports.updateJobRequest = updateJobRequest;
// Add preferred worker to job request
const addPreferredWorker = async (req, res) => {
    const { id } = req.params;
    const { workerId, priority, note } = req.body;
    try {
        // Check if job request exists
        const jobRequest = await prisma_js_1.prisma.jobRequest.findUnique({
            where: { id: Number(id) },
        });
        if (!jobRequest) {
            return res.status(404).json({ error: "Job request not found" });
        }
        // Check if worker exists
        const worker = await prisma_js_1.prisma.worker.findUnique({
            where: { id: Number(workerId) },
        });
        if (!worker) {
            return res.status(404).json({ error: "Worker not found" });
        }
        // Check if preferred worker already exists
        const existingPreferredWorker = await prisma_js_1.prisma.preferredWorker.findFirst({
            where: {
                jobRequestId: Number(id),
                workerId: Number(workerId),
            },
        });
        if (existingPreferredWorker) {
            return res.status(400).json({ error: "Preferred worker already exists" });
        }
        // Add preferred worker
        const preferredWorker = await prisma_js_1.prisma.preferredWorker.create({
            data: {
                jobRequest: {
                    connect: { id: Number(id) },
                },
                workerId: Number(workerId),
                priority: priority ? Number(priority) : 1,
                note,
            },
        });
        return res.status(201).json(preferredWorker);
    }
    catch (error) {
        console.error("Error adding preferred worker:", error);
        return res.status(500).json({ error: "Failed to add preferred worker" });
    }
};
exports.addPreferredWorker = addPreferredWorker;
// Search job requests
const searchJobRequests = async (req, res) => {
    const { specialization, location, startDate, endDate, status, companyId, urgency, } = req.query;
    try {
        const jobRequests = await prisma_js_1.prisma.jobRequest.findMany({
            where: {
                specialization: specialization
                    ? { contains: specialization, mode: "insensitive" }
                    : undefined,
                location: location
                    ? { contains: location, mode: "insensitive" }
                    : undefined,
                startDate: startDate
                    ? { gte: new Date(startDate) }
                    : undefined,
                endDate: endDate ? { lte: new Date(endDate) } : undefined,
                status: status ? status : undefined,
                companyId: companyId ? Number(companyId) : undefined,
                urgency: urgency ? urgency : undefined,
            },
            include: {
                company: true,
                preferredWorkers: true,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json(jobRequests);
    }
    catch (error) {
        console.error("Error searching job requests:", error);
        return res.status(500).json({ error: "Failed to search job requests" });
    }
};
exports.searchJobRequests = searchJobRequests;
// Helper function to generate AI matches for a job request
async function generateMatches(jobRequestId) {
    try {
        // Get job request details
        const jobRequest = await prisma_js_1.prisma.jobRequest.findUnique({
            where: { id: Number(jobRequestId) },
            include: {
                preferredWorkers: true,
            },
        });
        if (!jobRequest) {
            console.error("Job request not found");
            return;
        }
        // Find matching workers based on specialization, skills, and availability
        const matchingWorkers = await prisma_js_1.prisma.workerProfile.findMany({
            where: {
                specialization: {
                    contains: jobRequest.specialization,
                    mode: "insensitive",
                },
                currentlyAvailable: true,
                worker: {
                    skills: {
                        some: {
                            name: { in: jobRequest.requiredSkills },
                        },
                    },
                },
            },
            include: {
                worker: {
                    include: {
                        skills: true,
                        certifications: true,
                    },
                },
            },
        });
        // Create matches for each matching worker
        for (const worker of matchingWorkers) {
            // Calculate match score
            let matchScore = 0;
            // Specialization match
            if (worker.specialization.toLowerCase() ===
                jobRequest.specialization.toLowerCase()) {
                matchScore += 30;
            }
            // Experience level match
            const experienceScore = Math.min(worker.experienceYears * 5, 25);
            matchScore += experienceScore;
            // Skills match
            const workerSkills = worker.worker.skills.map((skill) => skill.name.toLowerCase());
            const skillMatchCount = jobRequest.requiredSkills.filter((skill) => workerSkills.includes(skill.toLowerCase())).length;
            const skillMatchScore = (skillMatchCount / jobRequest.requiredSkills.length) * 25;
            matchScore += skillMatchScore;
            // Rating score
            const ratingScore = worker.overallRating * 4;
            matchScore += ratingScore;
            // Preferred worker bonus
            const isPreferred = jobRequest.preferredWorkers.some((pw) => pw.workerId === worker.workerId);
            if (isPreferred) {
                matchScore += 10;
            }
            // Generate match reason
            const matchReason = `Specialization match: ${worker.specialization === jobRequest.specialization ? "Yes" : "Partial"},
                          Experience: ${worker.experienceYears} years,
                          Skills match: ${skillMatchCount}/${jobRequest.requiredSkills.length},
                          Rating: ${worker.overallRating}/5${isPreferred ? ", Preferred worker" : ""}`;
            // Check if match already exists
            const existingMatch = await prisma_js_1.prisma.jobMatch.findUnique({
                where: {
                    workerProfileId_jobRequestId: {
                        workerProfileId: worker.id,
                        jobRequestId: Number(jobRequestId),
                    },
                },
            });
            if (existingMatch) {
                // Update existing match
                await prisma_js_1.prisma.jobMatch.update({
                    where: {
                        id: existingMatch.id,
                    },
                    data: {
                        matchScore,
                        matchReason,
                    },
                });
            }
            else {
                // Create new match
                await prisma_js_1.prisma.jobMatch.create({
                    data: {
                        worker: {
                            connect: { id: worker.id },
                        },
                        jobRequest: {
                            connect: { id: Number(jobRequestId) },
                        },
                        matchScore,
                        matchReason,
                    },
                });
            }
        }
        console.log(`Generated ${matchingWorkers.length} matches for job request ${jobRequestId}`);
    }
    catch (error) {
        console.error("Error generating matches:", error);
    }
}
//# sourceMappingURL=jobRequestController.js.map