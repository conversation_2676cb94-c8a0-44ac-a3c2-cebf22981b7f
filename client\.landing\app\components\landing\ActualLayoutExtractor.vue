<!-- client/.landing/app/components/landing/ActualLayoutExtractor.vue -->

<template>
  <div class="layout-extractor" :class="{ dark: isDarkMode }">
    <div class="mock-layout flex h-full">
      <!-- Custom Mock Sidebar -->
      <MockSidebar
        :items="
          extractedMenu.map((item) => ({
            label: translateKey(item.label),
            icon: item.icon || 'solar:widget-2-linear',
            badge:
              translateKey(item.label) === 'Communication' ? '3' : undefined,
            i18n_key: item.label.__i18n_key__,
          }))
        "
        :default-active="activeItem"
        @update:activeItem="handleSidebarClick"
      >
        <template #subsidebar>
          <!-- Dynamic subsidebar based on active item -->
          <MockSubsidebar :title="translateKey(getActiveMenuItemLabel())">
            <!-- Search bar -->
            <div
              role="button"
              class="cursor-pointer h-8 mb-4 w-[calc(100%-2rem)] flex items-center justify-between bg-white dark:bg-muted-950 text-muted-400 hover:text-muted-600 dark:hover:text-muted-200 hover:ring-muted-300 dark:hover:ring-muted-700 gap-2 ps-3 pe-1 py-1 rounded-md ring-1 ring-muted-200 dark:ring-muted-800 transition-colors duration-300 mx-4"
            >
              <div class="pointer-events-none">
                <span class="font-sans text-sm">
                  {{ t("components.toolbar.search") }}
                </span>
              </div>
              <div class="flex gap-1">
                <BaseKbd size="sm" variant="default" class="!font-semibold h-6!"
                  >Ctrl</BaseKbd
                >
                <BaseKbd
                  size="sm"
                  variant="default"
                  class="!px-2 !font-semibold h-6!"
                  >K</BaseKbd
                >
              </div>
            </div>

            <!-- Menu Items - Dynamically showing the active item's links -->
            <div class="px-4">
              <template
                v-for="link in getActiveMenuItemLinks()"
                :key="translateKey(link.label)"
              >
                <!-- Regular Link -->
                <MockSidebarLink
                  v-if="!link.children"
                  :label="translateKey(link.label)"
                  :icon="link.icon"
                />

                <!-- Collapsible Link -->
                <MockCollapsible
                  v-else
                  :label="translateKey(link.label)"
                  :icon="link.icon"
                  :default-open="true"
                  :children="
                    link.children?.map((child) => ({
                      label: translateKey(child.label),
                      to: (child as any).to || '#',
                    }))
                  "
                />
              </template>
            </div>
          </MockSubsidebar>
        </template>
      </MockSidebar>

      <!-- Content Area -->
      <div class="mock-content flex-1 flex flex-col">
        <div class="px-4 md:px-6 xl:px-8 bg-muted-100 dark:bg-black">
          <div class="h-16 flex items-center justify-between">
            <div class="flex items-center gap-2">
              <button
                class="md:hidden flex items-center justify-center h-10 w-10 rounded-full bg-white dark:bg-muted-900"
              >
                <Icon name="lucide:menu" class="size-5 text-muted-400" />
              </button>
              <div class="flex items-center gap-4">
                <h1
                  class="text-lg font-medium text-muted-800 dark:text-white mr-6"
                >
                  {{ currentPageTitle }}
                </h1>

                <!-- User and Company Information -->
                <div class="flex flex-row items-center gap-4">
                  <!-- User Info -->
                  <div class="flex flex-col items-center justify-center">
                    <BaseTag
                      rounded="md"
                      variant="primary"
                      color="primary"
                      class="!px-2 !py-0"
                    >
                      {{ locale === "et" ? "OMANIK" : "OWNER" }}
                    </BaseTag>
                    <BaseSnack
                      :label="locale === 'et' ? 'Mart Mets' : 'John Smith'"
                      color="default"
                      size="xs"
                      image="/img/avatars/person-1.jpg"
                      class="whitespace-nowrap"
                    />
                  </div>

                  <!-- Company Info -->
                  <div
                    class="flex-col items-center justify-center md:flex hidden"
                  >
                    <BaseTag
                      rounded="md"
                      variant="primary"
                      color="primary"
                      class="!px-2 !py-0"
                    >
                      {{ locale === "et" ? "ETTEVÕTE" : "COMPANY" }}
                    </BaseTag>
                    <BaseSnack
                      :label="locale === 'et' ? 'Digi OÜ' : 'Digital LLC'"
                      color="default"
                      size="xs"
                      icon="ph:building"
                      class="whitespace-nowrap"
                    />
                  </div>

                  <!-- AI Feedback -->
                  <MockAiFeedback class="2xl:block hidden" />
                </div>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <!-- Notification Bell -->
              <div class="relative">
                <button
                  type="button"
                  class="notification-button border-muted-200 hover:ring-muted-200 dark:hover:ring-muted-700 dark:border-muted-700 dark:bg-muted-800 dark:ring-offset-muted-900 flex size-8 items-center justify-center rounded-full border bg-white ring-1 ring-transparent transition-all duration-300 hover:ring-offset-4"
                  @click="showNotification = !showNotification"
                >
                  <Icon name="lucide:bell" class="size-5 text-muted-400" />
                </button>

                <!-- Simple notification dropdown -->
                <div
                  v-if="showNotification"
                  class="notification-dropdown absolute right-0 mt-2 w-64 bg-white dark:bg-muted-800 rounded-md shadow-lg p-4 z-50 border border-muted-200 dark:border-muted-700"
                >
                  <div class="flex items-center justify-between mb-2">
                    <span
                      class="text-xs font-medium text-muted-800 dark:text-white"
                      >Notifications</span
                    >
                    <span class="text-xs text-primary-500"
                      >Mark all as read</span
                    >
                  </div>
                  <div class="space-y-2">
                    <div class="p-2 bg-muted-100 dark:bg-muted-900 rounded-md">
                      <div
                        class="text-xs text-muted-800 dark:text-muted-100 font-medium"
                      >
                        New message
                      </div>
                      <div class="text-xs text-muted-500 dark:text-muted-400">
                        You have a new message from the team
                      </div>
                    </div>
                    <div class="p-2 bg-muted-100 dark:bg-muted-900 rounded-md">
                      <div
                        class="text-xs text-muted-800 dark:text-muted-100 font-medium"
                      >
                        Task completed
                      </div>
                      <div class="text-xs text-muted-500 dark:text-muted-400">
                        Project milestone reached
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Language Selector -->
              <LanguageSelector
                :current-locale="locale"
                :available-locales="locales"
                @update:locale="switchLanguage"
                class="language-selector"
              />

              <!-- Mock Theme Toggle -->
              <MockThemeToggle v-model="isDarkMode" />
            </div>
          </div>
        </div>

        <!-- Mock Dashboard Content -->
        <div class="p-4 bg-muted-100 dark:bg-black overflow-y-auto flex-1">
          <!-- Key Performance Indicators -->
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"
          >
            <!-- KPI: Total Projects -->
            <BaseCard rounded="md" class="p-4">
              <div class="mb-1 flex items-center justify-between">
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-500 dark:text-muted-400"
                >
                  <span>{{ t("dashboard.kpi.projects") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400 dark:border-primary-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon name="solar:widget-2-bold-duotone" class="size-5" />
                </BaseIconBox>
              </div>
              <div class="mb-2">
                <BaseHeading
                  as="h4"
                  size="3xl"
                  weight="bold"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>42</span>
                </BaseHeading>
              </div>
              <div
                class="text-success-500 flex items-center gap-1 font-sans text-sm"
              >
                <span>+12.8%</span>
                <Icon name="lucide:trending-up" class="size-5" />
                <span class="text-muted-400 text-xs">{{
                  t("dashboard.kpi.since_last_month")
                }}</span>
              </div>
            </BaseCard>

            <!-- KPI: Active Tasks -->
            <BaseCard rounded="md" class="p-4">
              <div class="mb-1 flex items-center justify-between">
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-500 dark:text-muted-400"
                >
                  <span>{{ t("dashboard.kpi.tasks") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400 dark:border-info-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon
                    name="solar:checklist-minimalistic-bold-duotone"
                    class="size-5"
                  />
                </BaseIconBox>
              </div>
              <div class="mb-2">
                <BaseHeading
                  as="h4"
                  size="3xl"
                  weight="bold"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>156</span>
                </BaseHeading>
              </div>
              <div
                class="text-success-500 flex items-center gap-1 font-sans text-sm"
              >
                <span>+5.2%</span>
                <Icon name="lucide:trending-up" class="size-5" />
                <span class="text-muted-400 text-xs">{{
                  t("dashboard.kpi.this_week")
                }}</span>
              </div>
            </BaseCard>

            <!-- KPI: Budget Utilization -->
            <BaseCard rounded="md" class="p-4">
              <div class="mb-1 flex items-center justify-between">
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-500 dark:text-muted-400"
                >
                  <span>{{ t("dashboard.kpi.budget") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400 dark:border-success-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon name="solar:banknote-bold-duotone" class="size-5" />
                </BaseIconBox>
              </div>
              <div class="mb-2">
                <BaseHeading
                  as="h4"
                  size="3xl"
                  weight="bold"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>87%</span>
                </BaseHeading>
              </div>
              <div
                class="text-destructive-500 flex items-center gap-1 font-sans text-sm"
              >
                <span>-2.3%</span>
                <Icon name="lucide:trending-down" class="size-5" />
                <span class="text-muted-400 text-xs">{{
                  t("dashboard.kpi.remaining")
                }}</span>
              </div>
            </BaseCard>

            <!-- KPI: Team Members -->
            <BaseCard rounded="md" class="p-4">
              <div class="mb-1 flex items-center justify-between">
                <BaseHeading
                  as="h5"
                  size="sm"
                  weight="medium"
                  lead="tight"
                  class="text-muted-500 dark:text-muted-400"
                >
                  <span>{{ t("dashboard.kpi.team") }}</span>
                </BaseHeading>
                <BaseIconBox
                  size="xs"
                  class="bg-warning-100 text-warning-500 dark:bg-warning-500/20 dark:text-warning-400 dark:border-warning-500 dark:border-2"
                  rounded="full"
                  variant="none"
                >
                  <Icon
                    name="solar:users-group-rounded-bold-duotone"
                    class="size-5"
                  />
                </BaseIconBox>
              </div>
              <div class="mb-2">
                <BaseHeading
                  as="h4"
                  size="3xl"
                  weight="bold"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>24</span>
                </BaseHeading>
              </div>
              <div
                class="text-success-500 flex items-center gap-1 font-sans text-sm"
              >
                <span>+2</span>
                <Icon name="lucide:user-plus" class="size-5" />
                <span class="text-muted-400 text-xs">{{
                  t("dashboard.kpi.new_hires")
                }}</span>
              </div>
            </BaseCard>
          </div>

          <!-- Main Dashboard Content -->
          <div class="grid grid-cols-12 gap-4 mb-6">
            <!-- Revenue Chart - 8 columns on large screens -->
            <div class="col-span-12 lg:col-span-8">
              <BaseCard rounded="md" class="p-6">
                <div class="mb-2 flex items-center justify-between">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white"
                  >
                    <span>{{ t("dashboard.charts.revenue") }}</span>
                  </BaseHeading>
                  <div class="flex gap-2">
                    <BaseSelect size="sm" rounded="md" class="w-32">
                      <option value="monthly">
                        {{ t("dashboard.period.monthly") }}
                      </option>
                      <option value="quarterly">
                        {{ t("dashboard.period.quarterly") }}
                      </option>
                      <option value="yearly">
                        {{ t("dashboard.period.yearly") }}
                      </option>
                    </BaseSelect>
                    <BaseButton size="sm" rounded="md" href="#">
                      {{ t("dashboard.actions.details") }}
                    </BaseButton>
                  </div>
                </div>
                <div class="flex gap-8 mb-4">
                  <div>
                    <span
                      class="text-muted-600 dark:text-muted-400 font-sans text-xs"
                      >{{ t("dashboard.period.this_month") }}</span
                    >
                    <p
                      class="text-muted-900 dark:text-muted-100 font-sans text-lg font-medium"
                    >
                      $75,689
                    </p>
                  </div>
                  <div>
                    <span
                      class="text-muted-600 dark:text-muted-400 font-sans text-xs"
                      >{{ t("dashboard.period.last_month") }}</span
                    >
                    <p
                      class="text-muted-900 dark:text-muted-100 font-sans text-lg font-medium"
                    >
                      $59,724
                    </p>
                  </div>
                </div>
                <!-- Revenue Chart -->
                <DashboardChartRevenue />
              </BaseCard>
            </div>

            <!-- Goal Overview - 4 columns on large screens -->
            <div class="col-span-12 lg:col-span-4">
              <BaseCard rounded="md" class="flex h-full flex-col p-6">
                <div class="mb-6 flex items-center justify-between">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white"
                  >
                    <span>{{ t("dashboard.charts.goal_overview") }}</span>
                  </BaseHeading>
                </div>
                <!-- Goal Overview Chart -->
                <div class="mb-6 flex items-center justify-center">
                  <DashboardChartGoal />
                </div>
                <div class="mt-auto">
                  <div
                    class="border-muted-200 dark:border-muted-700 flex w-full border-t pt-4 text-center"
                  >
                    <div
                      class="border-muted-200 dark:border-muted-700 flex-1 border-r px-2"
                    >
                      <span class="text-muted-400 font-sans text-xs">
                        {{ t("dashboard.status.completed") }}
                      </span>
                      <p
                        class="text-muted-900 dark:text-muted-100 font-sans text-lg font-medium"
                      >
                        32
                      </p>
                    </div>
                    <div class="flex-1 px-2">
                      <span class="text-muted-400 font-sans text-xs">
                        {{ t("dashboard.status.in_progress") }}
                      </span>
                      <p
                        class="text-muted-900 dark:text-muted-100 font-sans text-lg font-medium"
                      >
                        9
                      </p>
                    </div>
                  </div>
                </div>
              </BaseCard>
            </div>
          </div>

          <!-- Module-specific Widgets -->
          <div class="grid grid-cols-12 gap-4 mb-6">
            <!-- Human Resources Module - 4 columns -->
            <div class="col-span-12 lg:col-span-4">
              <BaseCard rounded="md" class="p-6">
                <div class="mb-4 flex items-center justify-between">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white"
                  >
                    <span>{{ t("navigation.human_resources") }}</span>
                  </BaseHeading>
                  <BaseIconBox
                    size="xs"
                    class="bg-warning-100 text-warning-500 dark:bg-warning-500/20 dark:text-warning-400 dark:border-warning-500 dark:border-2"
                    rounded="full"
                    variant="none"
                  >
                    <Icon
                      name="solar:users-group-rounded-bold-duotone"
                      class="size-5"
                    />
                  </BaseIconBox>
                </div>

                <!-- Team Attendance -->
                <div class="mb-4">
                  <div class="flex items-center justify-between mb-2">
                    <BaseHeading
                      as="h5"
                      size="sm"
                      weight="medium"
                      lead="tight"
                      class="text-muted-800 dark:text-muted-100"
                    >
                      <span>{{ t("dashboard.hr.attendance") }}</span>
                    </BaseHeading>
                    <span class="text-success-500 text-xs font-medium"
                      >92%</span
                    >
                  </div>
                  <div
                    class="h-2 w-full bg-muted-200 dark:bg-muted-700 rounded-full overflow-hidden"
                  >
                    <div
                      class="h-full bg-success-500 rounded-full"
                      style="width: 92%"
                    ></div>
                  </div>
                </div>

                <!-- Upcoming Interviews -->
                <div class="mb-4">
                  <BaseHeading
                    as="h5"
                    size="sm"
                    weight="medium"
                    lead="tight"
                    class="text-muted-800 dark:text-muted-100 mb-2"
                  >
                    <span>{{ t("dashboard.hr.upcoming_interviews") }}</span>
                  </BaseHeading>
                  <div class="space-y-2">
                    <div
                      class="flex items-center justify-between p-2 bg-muted-100 dark:bg-muted-800 rounded-md"
                    >
                      <div class="flex items-center gap-2">
                        <BaseAvatar size="xs" src="/img/avatars/john_doe.jpg" />
                        <span
                          class="text-muted-800 dark:text-muted-100 text-sm"
                        >
                          {{ locale === "et" ? "Jaan Tamm" : "John Doe" }}
                        </span>
                      </div>
                      <span class="text-muted-400 text-xs">{{
                        t("dashboard.hr.today")
                      }}</span>
                    </div>
                    <div
                      class="flex items-center justify-between p-2 bg-muted-100 dark:bg-muted-800 rounded-md"
                    >
                      <div class="flex items-center gap-2">
                        <BaseAvatar
                          size="xs"
                          src="/img/avatars/jane_smith.jpg"
                        />
                        <span
                          class="text-muted-800 dark:text-muted-100 text-sm"
                        >
                          {{ locale === "et" ? "Mari Mets" : "Jane Smith" }}
                        </span>
                      </div>
                      <span class="text-muted-400 text-xs">{{
                        t("dashboard.hr.tomorrow")
                      }}</span>
                    </div>
                    <div
                      class="flex items-center justify-between p-2 bg-muted-100 dark:bg-muted-800 rounded-md"
                    >
                      <div class="flex items-center gap-2">
                        <BaseAvatar
                          size="xs"
                          src="/img/avatars/andrew_hill.jpg"
                        />
                        <span
                          class="text-muted-800 dark:text-muted-100 text-sm"
                        >
                          {{ locale === "et" ? "Andres Kask" : "Andrew Hill" }}
                        </span>
                      </div>
                      <span class="text-muted-400 text-xs">{{
                        t("dashboard.hr.tomorrow")
                      }}</span>
                    </div>
                  </div>
                </div>

                <!-- Recent Applications -->
                <div>
                  <BaseHeading
                    as="h5"
                    size="sm"
                    weight="medium"
                    lead="tight"
                    class="text-muted-800 dark:text-muted-100 mb-2"
                  >
                    <span>{{ t("dashboard.hr.recent_applications") }}</span>
                  </BaseHeading>
                  <div class="text-muted-400 text-sm">
                    <span>+12 {{ t("dashboard.hr.new_applications") }}</span>
                  </div>
                </div>
              </BaseCard>
            </div>

            <!-- Budget Module - 4 columns -->
            <div class="col-span-12 lg:col-span-4">
              <BaseCard rounded="md" class="p-6">
                <div class="mb-4 flex items-center justify-between">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white"
                  >
                    <span>{{ t("dashboard.budget.distribution") }}</span>
                  </BaseHeading>
                  <BaseIconBox
                    size="xs"
                    class="bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400 dark:border-success-500 dark:border-2"
                    rounded="full"
                    variant="none"
                  >
                    <Icon name="solar:banknote-bold-duotone" class="size-5" />
                  </BaseIconBox>
                </div>

                <!-- Budget Distribution -->
                <div class="mb-4">
                  <!-- Budget Distribution Chart -->
                  <DashboardChartBudget class="mb-2" />
                </div>

                <!-- Recent Expenses -->
                <div>
                  <BaseHeading
                    as="h5"
                    size="sm"
                    weight="medium"
                    lead="tight"
                    class="text-muted-800 dark:text-muted-100 mb-2"
                  >
                    <span>{{ t("dashboard.budget.recent_expenses") }}</span>
                  </BaseHeading>
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <span
                        class="text-muted-800 dark:text-muted-100 text-sm"
                        >{{ t("dashboard.budget.equipment") }}</span
                      >
                      <span class="text-destructive-500 text-sm font-medium"
                        >-$2,450</span
                      >
                    </div>
                    <div class="flex items-center justify-between">
                      <span
                        class="text-muted-800 dark:text-muted-100 text-sm"
                        >{{ t("dashboard.budget.marketing") }}</span
                      >
                      <span class="text-destructive-500 text-sm font-medium"
                        >-$1,800</span
                      >
                    </div>
                    <div class="flex items-center justify-between">
                      <span
                        class="text-muted-800 dark:text-muted-100 text-sm"
                        >{{ t("dashboard.budget.software") }}</span
                      >
                      <span class="text-destructive-500 text-sm font-medium"
                        >-$950</span
                      >
                    </div>
                  </div>
                </div>
              </BaseCard>
            </div>

            <!-- Production Module - 4 columns -->
            <div class="col-span-12 lg:col-span-4">
              <BaseCard rounded="md" class="p-6">
                <div class="mb-4 flex items-center justify-between">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white"
                  >
                    <span>{{ t("navigation.production") }}</span>
                  </BaseHeading>
                  <BaseIconBox
                    size="xs"
                    class="bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400 dark:border-info-500 dark:border-2"
                    rounded="full"
                    variant="none"
                  >
                    <Icon name="solar:buildings-3-outline" class="size-5" />
                  </BaseIconBox>
                </div>

                <!-- Production Status -->
                <div class="mb-4">
                  <BaseHeading
                    as="h5"
                    size="sm"
                    weight="medium"
                    lead="tight"
                    class="text-muted-800 dark:text-muted-100 mb-2"
                  >
                    <span>{{ t("dashboard.production.status") }}</span>
                  </BaseHeading>
                  <div class="grid grid-cols-2 gap-2">
                    <div
                      class="bg-muted-100 dark:bg-muted-800 p-3 rounded-md text-center"
                    >
                      <div
                        class="text-muted-800 dark:text-muted-100 text-lg font-medium"
                      >
                        24
                      </div>
                      <div class="text-muted-400 text-xs">
                        {{ t("dashboard.production.active") }}
                      </div>
                    </div>
                    <div
                      class="bg-muted-100 dark:bg-muted-800 p-3 rounded-md text-center"
                    >
                      <div
                        class="text-muted-800 dark:text-muted-100 text-lg font-medium"
                      >
                        8
                      </div>
                      <div class="text-muted-400 text-xs">
                        {{ t("dashboard.production.pending") }}
                      </div>
                    </div>
                    <div
                      class="bg-muted-100 dark:bg-muted-800 p-3 rounded-md text-center"
                    >
                      <div
                        class="text-muted-800 dark:text-muted-100 text-lg font-medium"
                      >
                        95%
                      </div>
                      <div class="text-muted-400 text-xs">
                        {{ t("dashboard.production.efficiency") }}
                      </div>
                    </div>
                    <div
                      class="bg-muted-100 dark:bg-muted-800 p-3 rounded-md text-center"
                    >
                      <div
                        class="text-muted-800 dark:text-muted-100 text-lg font-medium"
                      >
                        3
                      </div>
                      <div class="text-muted-400 text-xs">
                        {{ t("dashboard.production.issues") }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Production Timeline -->
                <div>
                  <BaseHeading
                    as="h5"
                    size="sm"
                    weight="medium"
                    lead="tight"
                    class="text-muted-800 dark:text-muted-100 mb-2"
                  >
                    <span>{{ t("dashboard.production.timeline") }}</span>
                  </BaseHeading>
                  <!-- Production Timeline Chart -->
                  <DashboardChartProduction />
                </div>
              </BaseCard>
            </div>
          </div>

          <!-- Communication and Sales Widgets -->
          <div class="grid grid-cols-12 gap-4 mb-6">
            <!-- Communication Module - 6 columns -->
            <div class="col-span-12 lg:col-span-6">
              <BaseCard rounded="md" class="p-6">
                <div class="mb-4 flex items-center justify-between">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white"
                  >
                    <span>{{ t("navigation.communication") }}</span>
                  </BaseHeading>
                  <BaseButton size="sm" rounded="md" href="#">
                    {{ t("dashboard.actions.view_all") }}
                  </BaseButton>
                </div>

                <!-- Recent Messages -->
                <div class="space-y-3">
                  <div
                    class="flex items-start gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <BaseAvatar size="sm" src="/img/avatars/person-1.jpg" />
                    <div class="flex-1">
                      <div class="flex items-center justify-between mb-1">
                        <span
                          class="text-muted-800 dark:text-muted-100 text-sm font-medium"
                        >
                          {{
                            locale === "et"
                              ? "Mihkel Jõesaar"
                              : "Michael Johnson"
                          }}
                        </span>
                        <span class="text-muted-400 text-xs">10:32 AM</span>
                      </div>
                      <p class="text-muted-500 dark:text-muted-300 text-sm">
                        {{ t("dashboard.communication.message_1") }}
                      </p>
                    </div>
                  </div>
                  <div
                    class="flex items-start gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <BaseAvatar
                      size="sm"
                      src="/img/avatars/sarah_williams.jpg"
                    />
                    <div class="flex-1">
                      <div class="flex items-center justify-between mb-1">
                        <span
                          class="text-muted-800 dark:text-muted-100 text-sm font-medium"
                        >
                          {{
                            locale === "et" ? "Sandra Villem" : "Sarah Williams"
                          }}
                        </span>
                        <span class="text-muted-400 text-xs">Yesterday</span>
                      </div>
                      <p class="text-muted-500 dark:text-muted-300 text-sm">
                        {{ t("dashboard.communication.message_2") }}
                      </p>
                    </div>
                  </div>
                  <div
                    class="flex items-start gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <BaseAvatar size="sm" src="/img/avatars/david_brown.jpg" />
                    <div class="flex-1">
                      <div class="flex items-center justify-between mb-1">
                        <span
                          class="text-muted-800 dark:text-muted-100 text-sm font-medium"
                        >
                          {{ locale === "et" ? "Taavi Pruun" : "David Brown" }}
                        </span>
                        <span class="text-muted-400 text-xs">Yesterday</span>
                      </div>
                      <p class="text-muted-500 dark:text-muted-300 text-sm">
                        {{ t("dashboard.communication.message_3") }}
                      </p>
                    </div>
                  </div>
                  <div
                    class="flex items-start gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <BaseAvatar size="sm" src="/img/avatars/person-4.jpg" />
                    <div class="flex-1">
                      <div class="flex items-center justify-between mb-1">
                        <span
                          class="text-muted-800 dark:text-muted-100 text-sm font-medium"
                        >
                          {{ locale === "et" ? "Liisa Rebane" : "Lisa Fox" }}
                        </span>
                        <span class="text-muted-400 text-xs">2 days ago</span>
                      </div>
                      <p class="text-muted-500 dark:text-muted-300 text-sm">
                        {{ t("dashboard.communication.message_1") }}
                      </p>
                    </div>
                  </div>
                </div>
              </BaseCard>
            </div>

            <!-- Sales Module - 6 columns -->
            <div class="col-span-12 lg:col-span-6">
              <BaseCard rounded="md" class="p-6">
                <div class="mb-4 flex items-center justify-between">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white"
                  >
                    <span>{{ t("navigation.sales") }}</span>
                  </BaseHeading>
                  <BaseIconBox
                    size="xs"
                    class="bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400 dark:border-primary-500 dark:border-2"
                    rounded="full"
                    variant="none"
                  >
                    <Icon name="lucide:bar-chart-2" class="size-5" />
                  </BaseIconBox>
                </div>

                <!-- Sales Performance -->
                <div class="mb-4">
                  <BaseHeading
                    as="h5"
                    size="sm"
                    weight="medium"
                    lead="tight"
                    class="text-muted-800 dark:text-muted-100 mb-2"
                  >
                    <span>{{ t("dashboard.sales.performance") }}</span>
                  </BaseHeading>
                  <!-- Sales Performance Chart -->
                  <DashboardChartSales class="mb-2" />
                </div>

                <!-- Top Products -->
                <div>
                  <BaseHeading
                    as="h5"
                    size="sm"
                    weight="medium"
                    lead="tight"
                    class="text-muted-800 dark:text-muted-100 mb-2"
                  >
                    <span>{{ t("dashboard.sales.top_products") }}</span>
                  </BaseHeading>
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <span class="text-muted-800 dark:text-muted-100 text-sm"
                        >Product A</span
                      >
                      <div class="flex items-center gap-2">
                        <span class="text-success-500 text-sm font-medium"
                          >$12,450</span
                        >
                        <span class="text-success-500 text-xs">+8%</span>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-muted-800 dark:text-muted-100 text-sm"
                        >Product B</span
                      >
                      <div class="flex items-center gap-2">
                        <span class="text-success-500 text-sm font-medium"
                          >$9,820</span
                        >
                        <span class="text-success-500 text-xs">+5%</span>
                      </div>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-muted-800 dark:text-muted-100 text-sm"
                        >Product C</span
                      >
                      <div class="flex items-center gap-2">
                        <span class="text-destructive-500 text-sm font-medium"
                          >$6,380</span
                        >
                        <span class="text-destructive-500 text-xs">-2%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </BaseCard>
            </div>
          </div>

          <!-- Calendar and Tasks Widgets -->
          <div class="grid grid-cols-12 gap-4">
            <!-- Calendar Widget - 8 columns -->
            <div class="col-span-12 lg:col-span-8">
              <BaseCard rounded="md" class="p-6">
                <div class="mb-4 flex items-center justify-between">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white"
                  >
                    <span>{{ t("dashboard.calendar.title") }}</span>
                  </BaseHeading>
                  <div class="flex gap-2">
                    <BaseButton size="sm" rounded="md" variant="muted" href="#">
                      {{ t("dashboard.calendar.today") }}
                    </BaseButton>
                    <BaseButton size="sm" rounded="md" variant="muted" href="#">
                      <Icon name="lucide:chevron-left" class="size-4" />
                    </BaseButton>
                    <BaseButton size="sm" rounded="md" variant="muted" href="#">
                      <Icon name="lucide:chevron-right" class="size-4" />
                    </BaseButton>
                  </div>
                </div>

                <!-- Calendar Component -->
                <DashboardCalendar />
              </BaseCard>
            </div>

            <!-- Tasks Widget - 4 columns -->
            <div class="col-span-12 lg:col-span-4">
              <BaseCard rounded="md" class="p-6">
                <div class="mb-4 flex items-center justify-between">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white"
                  >
                    <span>{{ t("dashboard.tasks.title") }}</span>
                  </BaseHeading>
                  <BaseButton size="sm" rounded="md" variant="primary" href="#">
                    <Icon name="lucide:plus" class="size-4" />
                  </BaseButton>
                </div>

                <!-- Tasks List -->
                <div class="space-y-3">
                  <div
                    class="flex items-center gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <DashboardCheckbox checked />
                    <span class="text-muted-400 text-sm line-through">{{
                      t("dashboard.tasks.task_1")
                    }}</span>
                  </div>
                  <div
                    class="flex items-center gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <DashboardCheckbox checked />
                    <span class="text-muted-400 text-sm line-through">{{
                      t("dashboard.tasks.task_2")
                    }}</span>
                  </div>
                  <div
                    class="flex items-center gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <DashboardCheckbox />
                    <span class="text-muted-800 dark:text-muted-100 text-sm">{{
                      t("dashboard.tasks.task_3")
                    }}</span>
                  </div>
                  <div
                    class="flex items-center gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <DashboardCheckbox />
                    <span class="text-muted-800 dark:text-muted-100 text-sm">{{
                      t("dashboard.tasks.task_4")
                    }}</span>
                  </div>
                  <div
                    class="flex items-center gap-3 p-3 bg-muted-100 dark:bg-muted-800 rounded-md"
                  >
                    <DashboardCheckbox />
                    <span class="text-muted-800 dark:text-muted-100 text-sm">{{
                      t("dashboard.tasks.task_5")
                    }}</span>
                  </div>
                </div>
              </BaseCard>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from "vue";
import { useI18n } from "vue-i18n";
import extractedMenuData from "./extractedMenu.json";
import MockSidebar from "./MockSidebar.vue";
import MockSubsidebar from "./MockSubsidebar.vue";
import MockSidebarLink from "./MockSidebarLink.vue";
import MockCollapsible from "./MockCollapsible.vue";
import LanguageSelector from "../LanguageSelector.vue";
import MockThemeToggle from "./MockThemeToggle.vue";
import MockAiFeedback from "./MockAiFeedback.vue";

// Import dashboard chart components
import DashboardChartRevenue from "../dashboard-chart/DashboardChartRevenue.vue";
import DashboardChartGoal from "../dashboard-chart/DashboardChartGoal.vue";
import DashboardChartBudget from "../dashboard-chart/DashboardChartBudget.vue";
import DashboardChartProduction from "../dashboard-chart/DashboardChartProduction.vue";
import DashboardChartSales from "../dashboard-chart/DashboardChartSales.vue";
import DashboardCalendar from "../dashboard-chart/DashboardCalendar.vue";
import DashboardCheckbox from "../dashboard-chart/DashboardCheckbox.vue";

const { t, locale, locales } = useI18n();

// Dark mode toggle for the mock dashboard only
const isDarkMode = ref(true);

// Notification toggle for the bell icon
const showNotification = ref(false);

// Close notification dropdown when clicking outside
function handleClickOutside(event: MouseEvent) {
  if (
    showNotification.value &&
    !event.composedPath().some((el) => {
      const element = el as HTMLElement;
      return (
        element.classList?.contains("notification-dropdown") ||
        element.classList?.contains("notification-button")
      );
    })
  ) {
    showNotification.value = false;
  }
}

// Add event listener when component is mounted
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

// Remove event listener when component is unmounted
onBeforeUnmount(() => {
  document.removeEventListener("click", handleClickOutside);
});

// Function to switch language
function switchLanguage(code: string) {
  if (code === "en" || code === "et") {
    locale.value = code;
  }
}

// Store the initial active item in localStorage to persist it (only in browser)
const defaultActiveItem = extractedMenuData[0]?.label || "";
const activeItem = ref(defaultActiveItem);

// Initialize from localStorage if available (only in browser)
if (process.client) {
  const storedItem = localStorage.getItem("mockDashboardActiveItem");
  if (storedItem) {
    activeItem.value = storedItem;
  }
}

// Update localStorage when activeItem changes (only in browser)
watch(activeItem, (newValue) => {
  if (process.client) {
    localStorage.setItem("mockDashboardActiveItem", String(newValue));
  }
});

function handleSidebarClick(value: string) {
  activeItem.value = value;
}

// Helper function to get the active menu item label
function getActiveMenuItemLabel() {
  // Find the menu item that matches the active item
  const menuItem = extractedMenu.value.find(
    (item) => translateKey(item.label) === activeItem.value
  );

  // Return the label or a default
  return menuItem?.label || extractedMenu.value[0]?.label || "Dashboard";
}

// Helper function to get the links for the active menu item
function getActiveMenuItemLinks() {
  // Find the menu item that matches the active item
  const menuItem = extractedMenu.value.find(
    (item) => translateKey(item.label) === activeItem.value
  );

  // Return the links or an empty array
  return menuItem?.links || extractedMenu.value[0]?.links || [];
}

// Force activeItem to maintain its value during locale changes
watch(
  () => locale.value,
  () => {
    if (!activeItem.value) {
      activeItem.value = defaultActiveItem;
    }
  },
  { immediate: true }
);

const extractedMenu = computed(() => {
  return extractedMenuData;
});

// Define types for menu items
interface I18nKey {
  __i18n_key__: string;
}

// Function to translate i18n keys from the extracted menu
function translateKey(key: string | I18nKey | any): string {
  if (typeof key === "object" && key && key.__i18n_key__) {
    return t(key.__i18n_key__);
  }
  if (typeof key === "string" && key.startsWith("__i18n_key__:")) {
    return t(key.replace("__i18n_key__:", ""));
  }
  return key as string;
}

// Current page title
const currentPageTitle = computed(() => {
  // Always translate the title properly
  if (activeItem.value === "Core") {
    return t("navigation.core") + " " + t("navigation.dashboard");
  } else if (
    typeof activeItem.value === "object" &&
    activeItem.value &&
    activeItem.value.__i18n_key__
  ) {
    return t(activeItem.value.__i18n_key__);
  } else {
    // Try to translate the string directly if it's a valid i18n key
    try {
      const translated = t(String(activeItem.value));
      // If translation returns the key itself, it means there's no translation
      return translated !== String(activeItem.value)
        ? translated
        : String(activeItem.value);
    } catch (e) {
      return String(activeItem.value);
    }
  }
});
</script>

<style scoped>
.layout-extractor {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.mock-layout {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.mock-content {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
</style>
