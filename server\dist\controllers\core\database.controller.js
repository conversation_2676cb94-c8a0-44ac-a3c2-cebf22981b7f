"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.executeQuery = exports.getDatabaseSchema = exports.getConnectionLeaks = exports.getConnectionPool = exports.getDatabaseTables = exports.getDatabaseStats = exports.killConnection = exports.restoreDatabase = exports.backupDatabase = exports.generateSchema = exports.migrateDatabase = exports.updateConnectionString = exports.testDatabaseConnection = exports.activateDatabaseConnection = exports.deleteDatabaseConnection = exports.updateDatabaseConnection = exports.createDatabaseConnection = exports.getDatabaseConnections = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const child_process_1 = require("child_process");
const util = __importStar(require("util"));
const dotenv = __importStar(require("dotenv"));
const date_fns_1 = require("date-fns");
const client_1 = require("@prisma/client");
const execPromise = util.promisify(child_process_1.exec);
// Get all database connections
const getDatabaseConnections = async (req, res) => {
    try {
        const connections = await prisma_js_1.prisma.databaseConnection.findMany({
            orderBy: {
                createdAt: "desc",
            },
        });
        res.status(200).json(connections);
    }
    catch (error) {
        console.error("Error fetching database connections:", error);
        res.status(500).json({ error: "Failed to fetch database connections" });
    }
};
exports.getDatabaseConnections = getDatabaseConnections;
// Create a new database connection
const createDatabaseConnection = async (req, res) => {
    try {
        const { name, connectionString, type, host, port, database, username, password, ssl, isActive, } = req.body;
        // Validate required fields
        if (!name || !connectionString) {
            res
                .status(400)
                .json({ error: "Name and connection string are required" });
            return;
        }
        // If this is the active connection, deactivate all others
        if (isActive) {
            await prisma_js_1.prisma.databaseConnection.updateMany({
                where: {
                    isActive: true,
                },
                data: {
                    isActive: false,
                },
            });
        }
        // Create the connection
        const connection = await prisma_js_1.prisma.databaseConnection.create({
            data: {
                name,
                connectionString,
                type,
                host,
                port,
                database,
                username,
                password,
                ssl,
                isActive,
            },
        });
        // If this is the active connection, update the .env file
        if (isActive) {
            await updateEnvFile(connectionString);
        }
        res.status(201).json(connection);
    }
    catch (error) {
        console.error("Error creating database connection:", error);
        res.status(500).json({ error: "Failed to create database connection" });
    }
};
exports.createDatabaseConnection = createDatabaseConnection;
// Update a database connection
const updateDatabaseConnection = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, connectionString, type, host, port, database, username, password, ssl, isActive, } = req.body;
        // Validate required fields
        if (!name || !connectionString) {
            res
                .status(400)
                .json({ error: "Name and connection string are required" });
            return;
        }
        // If this is the active connection, deactivate all others
        if (isActive) {
            await prisma_js_1.prisma.databaseConnection.updateMany({
                where: {
                    isActive: true,
                    id: {
                        not: parseInt(id),
                    },
                },
                data: {
                    isActive: false,
                },
            });
        }
        // Update the connection
        const connection = await prisma_js_1.prisma.databaseConnection.update({
            where: {
                id: parseInt(id),
            },
            data: {
                name,
                connectionString,
                type,
                host,
                port,
                database,
                username,
                password,
                ssl,
                isActive,
            },
        });
        // If this is the active connection, update the .env file
        if (isActive) {
            await updateEnvFile(connectionString);
        }
        res.status(200).json(connection);
    }
    catch (error) {
        console.error("Error updating database connection:", error);
        res.status(500).json({ error: "Failed to update database connection" });
    }
};
exports.updateDatabaseConnection = updateDatabaseConnection;
// Delete a database connection
const deleteDatabaseConnection = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if this is the active connection
        const connection = await prisma_js_1.prisma.databaseConnection.findUnique({
            where: {
                id: parseInt(id),
            },
        });
        if (!connection) {
            res.status(404).json({ error: "Database connection not found" });
            return;
        }
        // Delete the connection
        await prisma_js_1.prisma.databaseConnection.delete({
            where: {
                id: parseInt(id),
            },
        });
        res.status(200).json({ success: true });
    }
    catch (error) {
        console.error("Error deleting database connection:", error);
        res.status(500).json({ error: "Failed to delete database connection" });
    }
};
exports.deleteDatabaseConnection = deleteDatabaseConnection;
// Set a connection as active
const activateDatabaseConnection = async (req, res) => {
    try {
        const { id } = req.params;
        // Find the connection
        const connection = await prisma_js_1.prisma.databaseConnection.findUnique({
            where: {
                id: parseInt(id),
            },
        });
        if (!connection) {
            res.status(404).json({ error: "Database connection not found" });
            return;
        }
        // Deactivate all connections
        await prisma_js_1.prisma.databaseConnection.updateMany({
            where: {
                isActive: true,
            },
            data: {
                isActive: false,
            },
        });
        // Activate the selected connection
        await prisma_js_1.prisma.databaseConnection.update({
            where: {
                id: parseInt(id),
            },
            data: {
                isActive: true,
            },
        });
        // Update the .env file
        await updateEnvFile(connection.connectionString);
        res.status(200).json({ success: true });
    }
    catch (error) {
        console.error("Error activating database connection:", error);
        res.status(500).json({ error: "Failed to activate database connection" });
    }
};
exports.activateDatabaseConnection = activateDatabaseConnection;
// Test a database connection
const testDatabaseConnection = async (req, res) => {
    try {
        const { connectionString } = req.body;
        if (!connectionString) {
            res.status(400).json({ error: "Connection string is required" });
            return;
        }
        // Create a temporary Prisma client with the new connection string
        const tempPrisma = new client_1.PrismaClient({
            datasources: {
                db: {
                    url: connectionString,
                },
            },
        });
        try {
            // Test the connection by executing a simple query
            await tempPrisma.$queryRaw `SELECT 1`;
            // Disconnect the temporary client
            await tempPrisma.$disconnect();
            res.status(200).json({ success: true });
        }
        catch (error) {
            console.error("Error testing database connection:", error);
            // Disconnect the temporary client
            await tempPrisma.$disconnect();
            res.status(200).json({
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
            });
        }
    }
    catch (error) {
        console.error("Error testing database connection:", error);
        res
            .status(500)
            .json({ success: false, error: "Failed to test database connection" });
    }
};
exports.testDatabaseConnection = testDatabaseConnection;
// Update the connection string in the .env file
const updateConnectionString = async (req, res) => {
    try {
        const { connectionString } = req.body;
        if (!connectionString) {
            res.status(400).json({ error: "Connection string is required" });
            return;
        }
        // Update the .env file
        await updateEnvFile(connectionString);
        res.status(200).json({ success: true });
    }
    catch (error) {
        console.error("Error updating connection string:", error);
        res.status(500).json({ error: "Failed to update connection string" });
    }
};
exports.updateConnectionString = updateConnectionString;
// Migrate the database
const migrateDatabase = async (req, res) => {
    try {
        // Run the Prisma migration command
        const { stdout, stderr } = await execPromise("npx prisma migrate deploy");
        console.log("Migration stdout:", stdout);
        if (stderr) {
            console.error("Migration stderr:", stderr);
        }
        res.status(200).json({ success: true, output: stdout });
    }
    catch (error) {
        console.error("Error migrating database:", error);
        res.status(500).json({
            error: "Failed to migrate database",
            details: error instanceof Error ? error.message : "Unknown error",
        });
    }
};
exports.migrateDatabase = migrateDatabase;
// Generate the database schema
const generateSchema = async (req, res) => {
    try {
        // Run the Prisma generate command
        const { stdout, stderr } = await execPromise("npx prisma generate");
        console.log("Schema generation stdout:", stdout);
        if (stderr) {
            console.error("Schema generation stderr:", stderr);
        }
        res.status(200).json({ success: true, output: stdout });
    }
    catch (error) {
        console.error("Error generating schema:", error);
        res.status(500).json({
            error: "Failed to generate schema",
            details: error instanceof Error ? error.message : "Unknown error",
        });
    }
};
exports.generateSchema = generateSchema;
// Backup database
const backupDatabase = async (req, res) => {
    try {
        const { interval } = req.body; // Get the backup interval from the request
        // Create backups directory if it doesn't exist
        const backupsDir = path.join(process.cwd(), "backups");
        if (!fs.existsSync(backupsDir)) {
            fs.mkdirSync(backupsDir, { recursive: true });
        }
        // Generate backup filename with timestamp
        const timestamp = (0, date_fns_1.format)(new Date(), "yyyy-MM-dd_HH-mm-ss");
        const backupFilename = `backup_${timestamp}.sql`;
        const backupPath = path.join(backupsDir, backupFilename);
        // Get database connection info from .env
        const envPath = path.join(process.cwd(), ".env");
        const envContent = fs.readFileSync(envPath, "utf8");
        const envConfig = dotenv.parse(envContent);
        const connectionString = envConfig.DATABASE_URL;
        // Parse connection string
        const connectionInfo = parseConnectionString(connectionString);
        try {
            // Check if pg_dump is available
            await execPromise("pg_dump --version");
            // Build pg_dump command
            const pgDumpCmd = `pg_dump -h ${connectionInfo.host} -p ${connectionInfo.port} -U ${connectionInfo.username} -F c -b -v -f "${backupPath}" ${connectionInfo.database}`;
            // Set PGPASSWORD environment variable
            const env = { ...process.env, PGPASSWORD: connectionInfo.password };
            // Execute pg_dump
            const { stdout, stderr } = await execPromise(pgDumpCmd, { env });
            console.log("Backup stdout:", stdout);
            if (stderr) {
                console.log("Backup stderr:", stderr);
            }
            // Update last backup time in database (commented out until databaseSetting model is created)
            // We'll store this information in a file instead for now
            const backupInfoPath = path.join(backupsDir, "backup-info.json");
            let backupInfo = {};
            // Read existing backup info if it exists
            if (fs.existsSync(backupInfoPath)) {
                try {
                    backupInfo = JSON.parse(fs.readFileSync(backupInfoPath, "utf8"));
                }
                catch (e) {
                    console.error("Error reading backup info:", e);
                    backupInfo = {};
                }
            }
            // Update backup info
            backupInfo.lastBackup = new Date().toISOString();
            if (interval) {
                backupInfo.backupInterval = interval;
            }
            // Write backup info to file
            fs.writeFileSync(backupInfoPath, JSON.stringify(backupInfo, null, 2));
            res.status(200).json({
                success: true,
                filePath: backupPath,
                message: `Database backup created successfully: ${backupFilename}`,
            });
        }
        catch (cmdError) {
            // If pg_dump is not available, create a manual backup by exporting data
            console.log("pg_dump not available, creating manual backup...");
            // Create a simple JSON backup of important tables
            // Get a list of tables
            const tables = await prisma_js_1.prisma.$queryRaw `
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
      `;
            // For each table, export the data
            const backupData = {};
            for (const tableObj of tables) {
                const tableName = tableObj.table_name;
                const tableData = await prisma_js_1.prisma.$queryRawUnsafe(`SELECT * FROM "${tableName}" LIMIT 1000`);
                backupData[tableName] = tableData;
            }
            // Write the backup data to a JSON file
            const jsonBackupPath = path.join(backupsDir, `backup_${timestamp}.json`);
            fs.writeFileSync(jsonBackupPath, JSON.stringify(backupData, null, 2));
            // Update last backup time in database (commented out until databaseSetting model is created)
            // We'll store this information in a file instead for now
            const backupInfoPath = path.join(backupsDir, "backup-info.json");
            let backupInfo = {};
            // Read existing backup info if it exists
            if (fs.existsSync(backupInfoPath)) {
                try {
                    backupInfo = JSON.parse(fs.readFileSync(backupInfoPath, "utf8"));
                }
                catch (e) {
                    console.error("Error reading backup info:", e);
                    backupInfo = {};
                }
            }
            // Update backup info
            backupInfo.lastBackup = new Date().toISOString();
            if (interval) {
                backupInfo.backupInterval = interval;
            }
            // Write backup info to file
            fs.writeFileSync(backupInfoPath, JSON.stringify(backupInfo, null, 2));
            res.status(200).json({
                success: true,
                filePath: jsonBackupPath,
                message: `Database backup created successfully (JSON format): backup_${timestamp}.json`,
                warning: "pg_dump not available, created JSON backup instead. For full backups, please install PostgreSQL client tools.",
            });
        }
    }
    catch (error) {
        console.error("Error backing up database:", error);
        res.status(500).json({
            error: "Failed to backup database",
            details: error instanceof Error ? error.message : "Unknown error",
        });
    }
};
exports.backupDatabase = backupDatabase;
// Restore database from backup
const restoreDatabase = async (req, res) => {
    try {
        const { filePath } = req.body;
        if (!filePath) {
            res.status(400).json({ error: "Backup file path is required" });
            return;
        }
        // Check if file exists
        if (!fs.existsSync(filePath)) {
            res.status(404).json({ error: "Backup file not found" });
            return;
        }
        // Get database connection info from .env
        const envPath = path.join(process.cwd(), ".env");
        const envContent = fs.readFileSync(envPath, "utf8");
        const envConfig = dotenv.parse(envContent);
        const connectionString = envConfig.DATABASE_URL;
        // Parse connection string
        const connectionInfo = parseConnectionString(connectionString);
        // Build pg_restore command
        const pgRestoreCmd = `pg_restore -h ${connectionInfo.host} -p ${connectionInfo.port} -U ${connectionInfo.username} -d ${connectionInfo.database} -c -v "${filePath}"`;
        // Set PGPASSWORD environment variable
        const env = { ...process.env, PGPASSWORD: connectionInfo.password };
        // Execute pg_restore
        const { stdout, stderr } = await execPromise(pgRestoreCmd, { env });
        console.log("Restore stdout:", stdout);
        if (stderr) {
            console.log("Restore stderr:", stderr);
        }
        res.status(200).json({
            success: true,
            message: "Database restored successfully",
        });
    }
    catch (error) {
        console.error("Error restoring database:", error);
        res.status(500).json({
            error: "Failed to restore database",
            details: error instanceof Error ? error.message : "Unknown error",
        });
    }
};
exports.restoreDatabase = restoreDatabase;
// Kill a database connection
const killConnection = async (req, res) => {
    try {
        const { pid } = req.body;
        if (!pid) {
            res.status(400).json({ error: "PID is required" });
            return;
        }
        // Execute the query to terminate the connection
        await prisma_js_1.prisma.$executeRaw `SELECT pg_terminate_backend(${pid}::int)`;
        res.status(200).json({
            success: true,
            message: `Connection with PID ${pid} terminated successfully`,
        });
    }
    catch (error) {
        console.error("Error killing connection:", error);
        res.status(500).json({
            error: "Failed to kill connection",
            details: error instanceof Error ? error.message : "Unknown error",
        });
    }
};
exports.killConnection = killConnection;
// Get database statistics
const getDatabaseStats = async (req, res) => {
    try {
        // Get connection count
        const connectionCountResult = await prisma_js_1.prisma.$queryRaw `
      SELECT
        count(*) as connection_count,
        count(CASE WHEN state = 'active' THEN 1 END) as active_connections,
        count(CASE WHEN state = 'idle' THEN 1 END) as idle_connections
      FROM pg_stat_activity
      WHERE datname = current_database()
    `;
        // Get max connections
        const maxConnectionsResult = await prisma_js_1.prisma.$queryRaw `SHOW max_connections`;
        // Get database size
        const sizeResult = await prisma_js_1.prisma.$queryRaw `
      SELECT pg_size_pretty(pg_database_size(current_database())) as size
    `;
        // Get table count
        const tableCountResult = await prisma_js_1.prisma.$queryRaw `
      SELECT count(*) as table_count
      FROM information_schema.tables
      WHERE table_schema = 'public'
    `;
        // Get uptime
        const uptimeResult = await prisma_js_1.prisma.$queryRaw `
      SELECT pg_postmaster_start_time() as start_time
    `;
        // Format results - ensure we handle BigInt values properly
        const connectionCountResultTyped = connectionCountResult;
        const maxConnectionsResultTyped = maxConnectionsResult;
        const sizeResultTyped = sizeResult;
        const tableCountResultTyped = tableCountResult;
        const connectionCount = Number(connectionCountResultTyped[0].connection_count);
        const activeConnections = Number(connectionCountResultTyped[0].active_connections);
        const idleConnections = Number(connectionCountResultTyped[0].idle_connections);
        const maxConnections = Number(maxConnectionsResultTyped[0].max_connections);
        const connectionPercentage = (connectionCount / maxConnections) * 100;
        const size = sizeResultTyped[0].size;
        const tables = Number(tableCountResultTyped[0].table_count);
        // Calculate uptime
        const uptimeResultTyped = uptimeResult;
        const startTime = new Date(uptimeResultTyped[0].start_time);
        const now = new Date();
        const uptimeMs = now.getTime() - startTime.getTime();
        const uptimeDays = Math.floor(uptimeMs / (1000 * 60 * 60 * 24));
        const uptimeHours = Math.floor((uptimeMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const uptimeMinutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));
        const uptime = `${uptimeDays}d ${uptimeHours}h ${uptimeMinutes}m`;
        // Get long idle connections
        const longIdleConnectionsResult = await prisma_js_1.prisma.$queryRaw `
      SELECT count(*) as count
      FROM pg_stat_activity
      WHERE state = 'idle'
      AND (now() - state_change) > interval '5 minutes'
      AND datname = current_database()
    `;
        const longIdleConnectionsResultTyped = longIdleConnectionsResult;
        const longIdleConnections = Number(longIdleConnectionsResultTyped[0].count);
        // Get last backup time from backup-info.json if it exists
        let lastBackup = "N/A";
        const backupsDir = path.join(process.cwd(), "backups");
        const backupInfoPath = path.join(backupsDir, "backup-info.json");
        if (fs.existsSync(backupInfoPath)) {
            try {
                const backupInfo = JSON.parse(fs.readFileSync(backupInfoPath, "utf8"));
                if (backupInfo.lastBackup) {
                    lastBackup = backupInfo.lastBackup;
                }
            }
            catch (e) {
                console.error("Error reading backup info:", e);
            }
        }
        res.status(200).json({
            connectionCount,
            activeConnections,
            idleConnections,
            maxConnections,
            connectionPercentage,
            longIdleConnections,
            tables,
            size,
            uptime,
            lastBackup,
        });
    }
    catch (error) {
        console.error("Error getting database stats:", error);
        res.status(500).json({ error: "Failed to get database stats" });
    }
};
exports.getDatabaseStats = getDatabaseStats;
// Get database tables
const getDatabaseTables = async (req, res) => {
    try {
        const { search } = req.query;
        let query = `
      SELECT
        t.table_name as name,
        (SELECT reltuples FROM pg_class WHERE oid = (quote_ident(t.table_name)::regclass)) as row_count,
        pg_size_pretty(pg_total_relation_size(quote_ident(t.table_name)::regclass)) as size,
        (SELECT max(last_vacuum) FROM pg_stat_user_tables WHERE relname = t.table_name) as last_updated
      FROM information_schema.tables t
      WHERE t.table_schema = 'public'
    `;
        if (search) {
            query += ` AND t.table_name ILIKE '%${search}%'`;
        }
        query += ` ORDER BY t.table_name`;
        const tables = await prisma_js_1.prisma.$queryRawUnsafe(query);
        // Convert BigInt values to strings to avoid serialization issues
        const serializedTables = tables.map((table) => ({
            ...table,
            row_count: table.row_count ? Number(table.row_count) : 0, // Convert BigInt to Number or use 0 if null
        }));
        res.status(200).json(serializedTables);
    }
    catch (error) {
        console.error("Error getting database tables:", error);
        res.status(500).json({ error: "Failed to get database tables" });
    }
};
exports.getDatabaseTables = getDatabaseTables;
// Get connection pool information
const getConnectionPool = async (req, res) => {
    try {
        // This is a placeholder since Prisma doesn't expose connection pool metrics directly
        // In a real implementation, you might need to use a different approach to get this information
        // Get connection count
        const connectionCountResult = await prisma_js_1.prisma.$queryRaw `
      SELECT
        count(*) as total_connections,
        count(CASE WHEN state = 'active' THEN 1 END) as active_connections,
        count(CASE WHEN state = 'idle' THEN 1 END) as idle_connections
      FROM pg_stat_activity
      WHERE datname = current_database()
    `;
        // Get max connections
        const maxConnectionsResult = await prisma_js_1.prisma.$queryRaw `SHOW max_connections`;
        // Format results - ensure we handle BigInt values properly
        const connectionCountResultTyped = connectionCountResult;
        const maxConnectionsResultTyped = maxConnectionsResult;
        const totalConnections = Number(connectionCountResultTyped[0].total_connections);
        const activeConnections = Number(connectionCountResultTyped[0].active_connections);
        const idleConnections = Number(connectionCountResultTyped[0].idle_connections);
        const maxConnections = Number(maxConnectionsResultTyped[0].max_connections);
        // These values are placeholders and would need to be replaced with actual values in a real implementation
        const waitingRequests = 0;
        const idleTimeout = 10000; // 10 seconds
        const connectionTimeoutMillis = 30000; // 30 seconds
        res.status(200).json({
            totalConnections,
            activeConnections,
            idleConnections,
            waitingRequests,
            maxConnections,
            idleTimeout,
            connectionTimeoutMillis,
        });
    }
    catch (error) {
        console.error("Error getting connection pool info:", error);
        res.status(500).json({ error: "Failed to get connection pool info" });
    }
};
exports.getConnectionPool = getConnectionPool;
// Get connection leaks
const getConnectionLeaks = async (req, res) => {
    try {
        const leaks = await prisma_js_1.prisma.$queryRaw `
      SELECT
        pid,
        usename as username,
        application_name,
        client_addr as client_address,
        state,
        query,
        EXTRACT(EPOCH FROM (now() - state_change)) as idle_time_seconds,
        backend_start,
        state_change,
        wait_event_type,
        wait_event,
        backend_type
      FROM pg_stat_activity
      WHERE state = 'idle'
      AND (now() - state_change) > interval '5 minutes'
      AND datname = current_database()
      ORDER BY state_change ASC
    `;
        // Add an ID to each leak for easier handling in the frontend and convert BigInt values
        const leaksWithIds = leaks.map((leak, index) => ({
            id: index + 1,
            pid: Number(leak.pid),
            username: leak.username || "N/A",
            application_name: leak.application_name || "N/A",
            client_address: leak.client_address || "N/A",
            state: leak.state || "N/A",
            query: leak.query || "N/A",
            idle_time_seconds: Number(leak.idle_time_seconds) || 0,
            backend_start: leak.backend_start
                ? new Date(leak.backend_start).toISOString()
                : "N/A",
            state_change: leak.state_change
                ? new Date(leak.state_change).toISOString()
                : "N/A",
            wait_event_type: leak.wait_event_type || "N/A",
            wait_event: leak.wait_event || "N/A",
            backend_type: leak.backend_type || "N/A",
        }));
        res.status(200).json(leaksWithIds);
    }
    catch (error) {
        console.error("Error getting connection leaks:", error);
        res.status(500).json({ error: "Failed to get connection leaks" });
    }
};
exports.getConnectionLeaks = getConnectionLeaks;
// Get database schema
const getDatabaseSchema = async (req, res) => {
    try {
        // Path to the Prisma schema file
        const schemaPath = path.join(process.cwd(), "prisma", "schema.prisma");
        // Read the schema file
        const schema = fs.readFileSync(schemaPath, "utf8");
        res.status(200).json({ schema });
    }
    catch (error) {
        console.error("Error getting database schema:", error);
        res.status(500).json({ error: "Failed to get database schema" });
    }
};
exports.getDatabaseSchema = getDatabaseSchema;
// Execute a custom SQL query
const executeQuery = async (req, res) => {
    try {
        const { query } = req.body;
        if (!query) {
            res.status(400).json({ error: "Query is required" });
            return;
        }
        // Check if the query is a SELECT query to prevent data modification
        if (!query.trim().toLowerCase().startsWith("select")) {
            res.status(403).json({ error: "Only SELECT queries are allowed" });
            return;
        }
        // Execute the query
        const result = await prisma_js_1.prisma.$queryRawUnsafe(query);
        // Helper function to convert BigInt values to numbers
        const convertBigInts = (obj) => {
            if (obj === null || obj === undefined)
                return obj;
            if (typeof obj === "bigint")
                return Number(obj);
            if (Array.isArray(obj))
                return obj.map(convertBigInts);
            if (typeof obj === "object") {
                const converted = {};
                for (const key in obj) {
                    converted[key] = convertBigInts(obj[key]);
                }
                return converted;
            }
            return obj;
        };
        // Convert any BigInt values in the result
        const convertedResult = convertBigInts(result);
        res.status(200).json({ rows: convertedResult });
    }
    catch (error) {
        console.error("Error executing query:", error);
        res.status(500).json({
            error: "Failed to execute query",
            details: error instanceof Error ? error.message : "Unknown error",
        });
    }
};
exports.executeQuery = executeQuery;
// Helper function to parse a PostgreSQL connection string
function parseConnectionString(connectionString) {
    // Example: postgresql://username:password@host:port/database
    const regex = /^postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)$/;
    const match = connectionString.match(regex);
    if (!match) {
        throw new Error("Invalid PostgreSQL connection string");
    }
    return {
        username: match[1],
        password: match[2],
        host: match[3],
        port: match[4],
        database: match[5],
    };
}
// Helper function to update the .env file
async function updateEnvFile(connectionString) {
    try {
        // Path to the .env file
        const envPath = path.join(process.cwd(), ".env");
        // Read the current .env file
        let envContent = fs.readFileSync(envPath, "utf8");
        // Parse the current .env file
        const envConfig = dotenv.parse(envContent);
        // Update the DATABASE_URL
        envConfig.DATABASE_URL = connectionString;
        // Convert the config back to a string
        envContent = Object.entries(envConfig)
            .map(([key, value]) => `${key}=${value}`)
            .join("\n");
        // Write the updated content back to the .env file
        fs.writeFileSync(envPath, envContent);
        console.log("Updated .env file with new connection string");
    }
    catch (error) {
        console.error("Error updating .env file:", error);
        throw error;
    }
}
//# sourceMappingURL=database.controller.js.map