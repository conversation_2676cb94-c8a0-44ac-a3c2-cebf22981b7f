"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBusinessRules = getBusinessRules;
exports.saveBusinessRule = saveBusinessRule;
exports.getModuleStatus = getModuleStatus;
exports.updateModuleStatus = updateModuleStatus;
exports.getRecentDecisions = getRecentDecisions;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
async function getBusinessRules(req, res) {
    try {
        const rules = await prisma_js_1.prisma.businessRule.findMany({
            orderBy: { priority: "desc" },
        });
        res.json(rules);
    }
    catch (error) {
        res.status(500).json({ error: "Failed to fetch business rules" });
    }
}
async function saveBusinessRule(req, res) {
    try {
        const ruleData = req.body;
        if (ruleData.id) {
            // Update existing rule
            const { id, ...updateData } = ruleData;
            // Convert JSON fields to proper format
            const data = {
                name: updateData.name,
                isActive: updateData.isActive,
                type: updateData.type,
                priority: updateData.priority,
                condition: updateData.condition,
                action: updateData.action,
                severity: updateData.severity,
                metadata: updateData.metadata,
            };
            const updated = await prisma_js_1.prisma.businessRule.update({
                where: { id: Number(id) },
                data,
            });
            res.json(updated);
        }
        else {
            // Create new rule
            // Convert JSON fields to proper format
            const data = {
                name: ruleData.name,
                isActive: ruleData.isActive,
                type: ruleData.type,
                priority: ruleData.priority,
                condition: ruleData.condition,
                action: ruleData.action,
                severity: ruleData.severity,
                metadata: ruleData.metadata,
            };
            const created = await prisma_js_1.prisma.businessRule.create({
                data,
            });
            res.json(created);
        }
    }
    catch (error) {
        res.status(500).json({ error: "Failed to save business rule" });
    }
}
async function getModuleStatus(req, res) {
    try {
        const modules = await prisma_js_1.prisma.systemIntegration.findMany();
        res.json(modules);
    }
    catch (error) {
        res.status(500).json({ error: "Failed to fetch module status" });
    }
}
async function updateModuleStatus(req, res) {
    try {
        const { id } = req.params;
        const moduleData = req.body;
        // Convert JSON fields to proper format
        const data = {
            moduleName: moduleData.moduleName,
            isEnabled: moduleData.isEnabled,
            config: moduleData.config,
            dependencies: moduleData.dependencies,
        };
        const updated = await prisma_js_1.prisma.systemIntegration.update({
            where: { id: Number(id) },
            data,
        });
        res.json(updated);
    }
    catch (error) {
        res.status(500).json({ error: "Failed to update module status" });
    }
}
async function getRecentDecisions(req, res) {
    try {
        const decisions = await prisma_js_1.prisma.aiDecisionLog.findMany({
            orderBy: { timestamp: "desc" },
            take: 50, // Limit to most recent 50 decisions
        });
        res.json(decisions);
    }
    catch (error) {
        res.status(500).json({ error: "Failed to fetch AI decisions" });
    }
}
//# sourceMappingURL=core.controller.js.map