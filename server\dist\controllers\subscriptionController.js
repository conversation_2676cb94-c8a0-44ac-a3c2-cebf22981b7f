"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.subscriptionController = void 0;
const client_1 = require("@prisma/client");
const http_errors_1 = __importDefault(require("http-errors"));
const stripe_1 = __importDefault(require("stripe"));
const prisma = new client_1.PrismaClient();
const stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY || "", {
    apiVersion: "2025-04-30.basil",
});
exports.subscriptionController = {
    // Legacy method - kept for backward compatibility
    purchaseSubscription: async (req, res, next) => {
        const userId = req.user?.id;
        const { packageId } = req.body;
        if (!userId) {
            next((0, http_errors_1.default)(401, "Unauthorized: User ID not found"));
            return;
        }
        try {
            const subscriptionPackage = await prisma.subscriptionPackage.findUnique({
                where: { id: packageId },
            });
            if (!subscriptionPackage) {
                next((0, http_errors_1.default)(404, "Subscription package not found"));
                return;
            }
            const startDate = new Date();
            const endDate = new Date(startDate.getTime() + subscriptionPackage.durationInDays * 86400000 // Convert days to milliseconds
            );
            // Create subscription with the correct schema properties
            const subscription = await prisma.subscription.create({
                data: {
                    userId,
                    // packageId is not in the schema, use planId instead
                    planId: Number(packageId),
                    startDate,
                    endDate,
                    // price and isActive are not in the schema
                    // Use status instead of isActive
                    status: client_1.SubscriptionStatus.ACTIVE,
                    // Add required billingCycle property
                    billingCycle: client_1.BillingCycle.MONTHLY,
                },
                include: {
                    // package is not in the schema, use plan instead
                    plan: true,
                },
            });
            res.status(201).json(subscription);
        }
        catch (error) {
            console.error("Error purchasing subscription:", error);
            next((0, http_errors_1.default)(500, "Internal Server Error"));
        }
    },
    // Legacy method - kept for backward compatibility
    getUserSubscriptions: async (req, res, next) => {
        const userId = req.user?.id;
        if (!userId) {
            next((0, http_errors_1.default)(401, "Unauthorized: User ID not found"));
            return;
        }
        try {
            const subscriptions = await prisma.subscription.findMany({
                where: { userId },
                include: {
                    // package is not in the schema, use plan instead
                    plan: true,
                },
                orderBy: {
                    createdAt: "desc",
                },
            });
            res.json(subscriptions);
        }
        catch (error) {
            console.error("Error fetching subscriptions:", error);
            next((0, http_errors_1.default)(500, "Internal Server Error"));
        }
    },
    // Legacy method - kept for backward compatibility
    cancelSubscription: async (req, res, next) => {
        const userId = req.user?.id;
        const { subscriptionId } = req.params;
        if (!userId) {
            next((0, http_errors_1.default)(401, "Unauthorized: User ID not found"));
            return;
        }
        try {
            const subscription = await prisma.subscription.findFirst({
                where: {
                    id: Number(subscriptionId),
                    userId,
                },
            });
            if (!subscription) {
                next((0, http_errors_1.default)(404, "Subscription not found"));
                return;
            }
            const updatedSubscription = await prisma.subscription.update({
                where: { id: Number(subscriptionId) },
                data: {
                    // isActive is not in the schema, use status instead
                    status: client_1.SubscriptionStatus.CANCELLED,
                    endDate: new Date(), // End subscription immediately
                },
                include: {
                    // package is not in the schema, use plan instead
                    plan: true,
                },
            });
            res.json(updatedSubscription);
        }
        catch (error) {
            console.error("Error canceling subscription:", error);
            next((0, http_errors_1.default)(500, "Internal Server Error"));
        }
    },
    // New methods for the multi-step registration process
    createSubscription: async (req, res, next) => {
        try {
            const { userId, plan, billingCycle, paymentMethodId, billingAddress } = req.body;
            // Get user
            const user = await prisma.user.findUnique({
                where: { id: Number(userId) },
                include: {
                    companies: {
                        include: {
                            company: true,
                        },
                    },
                },
            });
            if (!user) {
                next((0, http_errors_1.default)(404, "User not found"));
                return;
            }
            // Get company if user has one
            const userCompany = user.companies.find((uc) => uc.role === "OWNER");
            const companyId = userCompany?.companyId;
            // Get subscription plan
            console.log("Looking for subscription plan with type:", plan);
            // Debug: List all subscription plans in the database
            const allPlans = await prisma.subscriptionPlan.findMany();
            console.log("All subscription plans in database:", allPlans);
            const subscriptionPlan = await prisma.subscriptionPlan.findFirst({
                where: { type: plan },
            });
            console.log("Found subscription plan:", subscriptionPlan);
            if (!subscriptionPlan) {
                console.log("No subscription plan found for type:", plan);
                next((0, http_errors_1.default)(404, "Subscription plan not found"));
                return;
            }
            // Create Stripe customer
            const customer = await stripe.customers.create({
                email: user.email,
                name: `${user.firstName} ${user.lastName}`,
                payment_method: paymentMethodId,
                invoice_settings: {
                    default_payment_method: paymentMethodId,
                },
                address: {
                    line1: billingAddress.line1,
                    line2: billingAddress.line2 || "",
                    city: billingAddress.city,
                    state: billingAddress.state,
                    postal_code: billingAddress.postalCode,
                    country: billingAddress.country,
                },
            });
            // Calculate price based on billing cycle
            const price = billingCycle === "MONTHLY"
                ? subscriptionPlan.monthlyPrice
                : subscriptionPlan.yearlyPrice;
            // Create Stripe subscription
            const stripeSubscription = await stripe.subscriptions.create({
                customer: customer.id,
                items: [
                    {
                        price_data: {
                            currency: "eur",
                            // Use type assertion to handle the product_data property
                            product: subscriptionPlan.id.toString(),
                            unit_amount: Math.round(price * 100), // Convert to cents
                            recurring: {
                                interval: billingCycle === "MONTHLY" ? "month" : "year",
                            },
                        }, // Use type assertion to bypass TypeScript checking
                    },
                ],
                payment_settings: {
                    payment_method_types: ["card"],
                    save_default_payment_method: "on_subscription",
                },
                expand: ["latest_invoice.payment_intent"],
            });
            // Calculate next billing date
            const now = new Date();
            const nextBillingDate = new Date(now);
            if (billingCycle === "MONTHLY") {
                nextBillingDate.setMonth(now.getMonth() + 1);
            }
            else {
                nextBillingDate.setFullYear(now.getFullYear() + 1);
            }
            // Create subscription in database
            const subscription = await prisma.subscription.create({
                data: {
                    userId: Number(userId),
                    companyId,
                    planId: subscriptionPlan.id,
                    status: client_1.SubscriptionStatus.ACTIVE,
                    startDate: new Date(),
                    billingCycle: billingCycle,
                    lastBillingDate: new Date(),
                    nextBillingDate,
                    stripeCustomerId: customer.id,
                    stripeSubscriptionId: stripeSubscription.id,
                },
            });
            // Create payment record
            await prisma.payment.create({
                data: {
                    subscriptionId: subscription.id,
                    amount: price,
                    currency: "EUR",
                    status: "succeeded",
                    stripePaymentId: stripeSubscription.latest_invoice
                        ?.payment_intent?.id,
                    stripeInvoiceId: stripeSubscription.latest_invoice?.id,
                    paymentMethod: "card",
                },
            });
            res.status(201).json({
                message: "Subscription created successfully",
                subscription,
            });
        }
        catch (error) {
            console.error("Error creating subscription:", error);
            next((0, http_errors_1.default)(500, "Failed to create subscription"));
        }
    },
    createFreeSubscription: async (req, res, next) => {
        try {
            // Remove unused plan variable
            const { userId, billingCycle } = req.body;
            // Get user
            const user = await prisma.user.findUnique({
                where: { id: Number(userId) },
                include: {
                    companies: {
                        include: {
                            company: true,
                        },
                    },
                },
            });
            if (!user) {
                next((0, http_errors_1.default)(404, "User not found"));
                return;
            }
            // Get company if user has one
            const userCompany = user.companies.find((uc) => uc.role === "OWNER");
            const companyId = userCompany?.companyId;
            // Get subscription plan
            const subscriptionPlan = await prisma.subscriptionPlan.findFirst({
                where: { type: client_1.SubscriptionPlanType.FREE_TRIAL },
            });
            if (!subscriptionPlan) {
                next((0, http_errors_1.default)(404, "Free trial plan not found"));
                return;
            }
            // Calculate trial end date (14 days from now)
            const now = new Date();
            const trialEndDate = new Date(now);
            trialEndDate.setDate(now.getDate() + 14);
            // Create subscription in database
            const subscription = await prisma.subscription.create({
                data: {
                    userId: Number(userId),
                    companyId,
                    planId: subscriptionPlan.id,
                    status: client_1.SubscriptionStatus.TRIAL,
                    startDate: new Date(),
                    endDate: trialEndDate,
                    trialEndDate,
                    billingCycle: billingCycle,
                },
            });
            res.status(201).json({
                message: "Free trial subscription created successfully",
                subscription,
            });
        }
        catch (error) {
            console.error("Error creating free subscription:", error);
            next((0, http_errors_1.default)(500, "Failed to create free subscription"));
        }
    },
    checkSubscriptionStatus: async (req, res, next) => {
        try {
            const userId = req.params.userId || req.user?.id;
            if (!userId) {
                next((0, http_errors_1.default)(401, "Unauthorized: User ID not found"));
                return;
            }
            const subscription = await prisma.subscription.findFirst({
                where: {
                    userId: Number(userId),
                    OR: [
                        { status: client_1.SubscriptionStatus.ACTIVE },
                        { status: client_1.SubscriptionStatus.TRIAL },
                    ],
                },
                include: {
                    plan: true,
                },
                orderBy: {
                    createdAt: "desc",
                },
            });
            if (!subscription) {
                res.status(404).json({
                    active: false,
                    message: "No active subscription found",
                });
                return;
            }
            // Check if trial has expired
            if (subscription.status === client_1.SubscriptionStatus.TRIAL &&
                subscription.trialEndDate) {
                const now = new Date();
                if (now > subscription.trialEndDate) {
                    // Update subscription status to EXPIRED
                    await prisma.subscription.update({
                        where: { id: subscription.id },
                        data: { status: client_1.SubscriptionStatus.EXPIRED },
                    });
                    res.status(200).json({
                        active: false,
                        message: "Trial period has expired",
                        subscription: {
                            ...subscription,
                            status: client_1.SubscriptionStatus.EXPIRED,
                        },
                    });
                    return;
                }
            }
            res.status(200).json({
                active: true,
                subscription,
            });
        }
        catch (error) {
            console.error("Error checking subscription status:", error);
            next((0, http_errors_1.default)(500, "Failed to check subscription status"));
        }
    },
    // Webhook handler for Stripe events
    handleStripeWebhook: async (req, res, next) => {
        const sig = req.headers["stripe-signature"];
        let event;
        try {
            event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET || "");
        }
        catch (err) {
            console.error("Webhook signature verification failed:", err);
            res.status(400).send(`Webhook Error: ${err.message}`);
            return;
        }
        // Handle the event
        try {
            switch (event.type) {
                case "invoice.payment_succeeded":
                    await handleInvoicePaymentSucceeded(event.data.object);
                    break;
                case "invoice.payment_failed":
                    await handleInvoicePaymentFailed(event.data.object);
                    break;
                case "customer.subscription.deleted":
                    await handleSubscriptionDeleted(event.data.object);
                    break;
                default:
                    console.log(`Unhandled event type ${event.type}`);
            }
            // Return a 200 response to acknowledge receipt of the event
            res.status(200).json({ received: true });
        }
        catch (error) {
            console.error("Error handling webhook event:", error);
            next((0, http_errors_1.default)(500, "Failed to process webhook event"));
        }
    },
};
// Helper functions for webhook handlers
async function handleInvoicePaymentSucceeded(invoice) {
    try {
        // Find subscription by Stripe subscription ID
        const subscription = await prisma.subscription.findFirst({
            where: { stripeSubscriptionId: invoice.subscription },
        });
        if (!subscription) {
            console.error("Subscription not found for invoice:", invoice.id);
            return;
        }
        // Calculate next billing date
        const now = new Date();
        const nextBillingDate = new Date(now);
        if (subscription.billingCycle === client_1.BillingCycle.MONTHLY) {
            nextBillingDate.setMonth(now.getMonth() + 1);
        }
        else {
            nextBillingDate.setFullYear(now.getFullYear() + 1);
        }
        // Update subscription
        await prisma.subscription.update({
            where: { id: subscription.id },
            data: {
                status: client_1.SubscriptionStatus.ACTIVE,
                lastBillingDate: now,
                nextBillingDate,
            },
        });
        // Create payment record
        await prisma.payment.create({
            data: {
                subscriptionId: subscription.id,
                amount: invoice.amount_paid / 100, // Convert from cents
                currency: invoice.currency.toUpperCase(),
                status: "succeeded",
                stripePaymentId: invoice.payment_intent,
                stripeInvoiceId: invoice.id,
                paymentMethod: "card",
            },
        });
    }
    catch (error) {
        console.error("Error handling invoice.payment_succeeded:", error);
    }
}
async function handleInvoicePaymentFailed(invoice) {
    try {
        // Find subscription by Stripe subscription ID
        const subscription = await prisma.subscription.findFirst({
            where: { stripeSubscriptionId: invoice.subscription },
        });
        if (!subscription) {
            console.error("Subscription not found for invoice:", invoice.id);
            return;
        }
        // Update subscription status
        await prisma.subscription.update({
            where: { id: subscription.id },
            data: {
                status: client_1.SubscriptionStatus.PENDING_PAYMENT,
            },
        });
        // Create payment record
        await prisma.payment.create({
            data: {
                subscriptionId: subscription.id,
                amount: invoice.amount_due / 100, // Convert from cents
                currency: invoice.currency.toUpperCase(),
                status: "failed",
                stripePaymentId: invoice.payment_intent,
                stripeInvoiceId: invoice.id,
                paymentMethod: "card",
            },
        });
    }
    catch (error) {
        console.error("Error handling invoice.payment_failed:", error);
    }
}
async function handleSubscriptionDeleted(subscription) {
    try {
        // Find subscription by Stripe subscription ID
        const dbSubscription = await prisma.subscription.findFirst({
            where: { stripeSubscriptionId: subscription.id },
        });
        if (!dbSubscription) {
            console.error("Subscription not found for Stripe subscription:", subscription.id);
            return;
        }
        // Update subscription status
        await prisma.subscription.update({
            where: { id: dbSubscription.id },
            data: {
                status: client_1.SubscriptionStatus.CANCELLED,
                endDate: new Date(),
            },
        });
    }
    catch (error) {
        console.error("Error handling customer.subscription.deleted:", error);
    }
}
//# sourceMappingURL=subscriptionController.js.map