"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeAccessRights = exports.getCurrentUserAccessRights = exports.bulkUpdateAccessRights = exports.updateAccessRight = exports.getAccessRightsByRole = exports.getAllAccessRights = void 0;
const client_1 = require("@prisma/client");
const http_errors_1 = __importDefault(require("http-errors"));
const prisma = new client_1.PrismaClient();
/**
 * Get all page access rights
 */
const getAllAccessRights = async (_req, // Prefix with underscore to indicate it's intentionally unused
res, next) => {
    try {
        const accessRights = await prisma.pageAccessRight.findMany({
            orderBy: [{ module: "asc" }, { path: "asc" }, { role: "asc" }],
        });
        res.json(accessRights);
    }
    catch (error) {
        console.error("Error fetching access rights:", error);
        next((0, http_errors_1.default)(500, "Error fetching access rights"));
    }
};
exports.getAllAccessRights = getAllAccessRights;
/**
 * Get access rights for a specific role
 */
const getAccessRightsByRole = async (req, res, next) => {
    const { role } = req.params;
    try {
        // Validate role
        if (!Object.values(client_1.Role).includes(role)) {
            next((0, http_errors_1.default)(400, "Invalid role"));
            return;
        }
        const accessRights = await prisma.pageAccessRight.findMany({
            where: { role: role },
            orderBy: [{ module: "asc" }, { path: "asc" }],
        });
        res.json(accessRights);
    }
    catch (error) {
        console.error(`Error fetching access rights for role ${role}:`, error);
        next((0, http_errors_1.default)(500, "Error fetching access rights"));
    }
};
exports.getAccessRightsByRole = getAccessRightsByRole;
/**
 * Update access rights for a specific path and role
 */
const updateAccessRight = async (req, res, next) => {
    const { path, role, canAccess } = req.body;
    try {
        // Validate role
        if (!Object.values(client_1.Role).includes(role)) {
            next((0, http_errors_1.default)(400, "Invalid role"));
            return;
        }
        // Validate path
        if (!path || typeof path !== "string") {
            next((0, http_errors_1.default)(400, "Invalid path"));
            return;
        }
        // Validate canAccess
        if (typeof canAccess !== "boolean") {
            next((0, http_errors_1.default)(400, "canAccess must be a boolean"));
            return;
        }
        // Find the module from the path
        const module = path.split("/")[1] || "root";
        // Update or create the access right
        const accessRight = await prisma.pageAccessRight.upsert({
            where: {
                path_role: {
                    path,
                    role: role,
                },
            },
            update: {
                canAccess,
            },
            create: {
                path,
                module,
                role: role,
                canAccess,
            },
        });
        res.json(accessRight);
    }
    catch (error) {
        console.error("Error updating access right:", error);
        next((0, http_errors_1.default)(500, "Error updating access right"));
    }
};
exports.updateAccessRight = updateAccessRight;
/**
 * Bulk update access rights
 */
const bulkUpdateAccessRights = async (req, res, next) => {
    const { accessRights } = req.body;
    try {
        if (!Array.isArray(accessRights)) {
            next((0, http_errors_1.default)(400, "accessRights must be an array"));
            return;
        }
        const results = await Promise.all(accessRights.map(async ({ path, role, canAccess, module: moduleFromRequest }) => {
            // Validate role - ensure it's one of the valid enum values
            const validRole = Object.values(client_1.Role).find((r) => r === role.toUpperCase());
            if (!validRole) {
                throw new Error(`Invalid role: ${role}. Valid roles are: ${Object.values(client_1.Role).join(", ")}`);
            }
            // Validate path
            if (!path || typeof path !== "string") {
                throw new Error(`Invalid path: ${path}`);
            }
            // Validate canAccess
            if (typeof canAccess !== "boolean") {
                throw new Error("canAccess must be a boolean");
            }
            // Use the module from request or extract from path
            const module = moduleFromRequest || path.split("/")[1] || "root";
            // Update or create the access right
            return prisma.pageAccessRight.upsert({
                where: {
                    path_role: {
                        path,
                        role: validRole,
                    },
                },
                update: {
                    canAccess,
                },
                create: {
                    path,
                    module,
                    role: validRole,
                    canAccess,
                },
            });
        }));
        res.json({ success: true, count: results.length });
    }
    catch (error) {
        console.error("Error bulk updating access rights:", error);
        next((0, http_errors_1.default)(500, `Error bulk updating access rights: ${error instanceof Error ? error.message : String(error)}`));
    }
};
exports.bulkUpdateAccessRights = bulkUpdateAccessRights;
/**
 * Get access rights for the current user
 */
const getCurrentUserAccessRights = async (req, res, next) => {
    try {
        if (!req.user || !req.user.roles) {
            next((0, http_errors_1.default)(401, "Unauthorized"));
            return;
        }
        // If user is SUPERADMIN, they have access to everything
        if (req.user.roles.includes("SUPERADMIN")) {
            res.json({ isSuperAdmin: true, accessRights: [] });
            return;
        }
        // Get access rights for all roles the user has
        const accessRights = await prisma.pageAccessRight.findMany({
            where: {
                role: {
                    in: req.user.roles,
                },
            },
        });
        // Create a map of paths to access rights
        // If a user has multiple roles, they get access if ANY role allows it
        const accessMap = accessRights.reduce((map, right) => {
            if (!map[right.path] || right.canAccess) {
                map[right.path] = right.canAccess;
            }
            return map;
        }, {});
        res.json({ isSuperAdmin: false, accessRights: accessMap });
    }
    catch (error) {
        console.error("Error fetching current user access rights:", error);
        next((0, http_errors_1.default)(500, "Error fetching current user access rights"));
    }
};
exports.getCurrentUserAccessRights = getCurrentUserAccessRights;
/**
 * Initialize default access rights for all routes
 */
const initializeAccessRights = async (req, res, next) => {
    try {
        // Get all routes from the application
        const routes = req.body.routes;
        if (!Array.isArray(routes)) {
            next((0, http_errors_1.default)(400, "routes must be an array"));
            return;
        }
        // Get all roles except SUPERADMIN (who has access to everything)
        const roles = Object.values(client_1.Role).filter((role) => role !== "SUPERADMIN");
        // Create default access rights for each route and role
        const defaultAccessRights = [];
        for (const route of routes) {
            const { path, module } = route;
            for (const role of roles) {
                // Default access rules:
                // - ADMIN has access to everything
                // - Other roles have access based on their module
                const canAccess = role === "ADMIN" ||
                    (role === "PROJECTLEADER" &&
                        ["core", "production", "hr", "recruitment"].includes(module)) ||
                    (role === "SALESMAN" &&
                        ["core", "companies", "accounting", "recruitment"].includes(module)) ||
                    (role === "WORKER" && ["core", "recruitment"].includes(module)) ||
                    (role === "CLIENT" && ["core", "recruitment"].includes(module));
                defaultAccessRights.push({
                    path,
                    module,
                    role,
                    canAccess,
                });
            }
        }
        // Bulk create access rights
        await prisma.$transaction(defaultAccessRights.map((right) => prisma.pageAccessRight.upsert({
            where: {
                path_role: {
                    path: right.path,
                    role: right.role,
                },
            },
            update: {}, // Don't update existing records
            create: {
                path: right.path,
                module: right.module,
                role: right.role,
                canAccess: right.canAccess,
            },
        })));
        res.json({ success: true, count: defaultAccessRights.length });
    }
    catch (error) {
        console.error("Error initializing access rights:", error);
        next((0, http_errors_1.default)(500, "Error initializing access rights"));
    }
};
exports.initializeAccessRights = initializeAccessRights;
//# sourceMappingURL=accessRights.controller.js.map