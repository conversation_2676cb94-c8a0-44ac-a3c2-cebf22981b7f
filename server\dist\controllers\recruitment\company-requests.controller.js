"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCompanyRequests = getCompanyRequests;
exports.getCurrentUserCompanyRequests = getCurrentUserCompanyRequests;
exports.updateCompanyRequest = updateCompanyRequest;
const prisma_js_1 = require("../../lib/prisma.js");
// Define the enum for company join request status
var CompanyJoinRequestStatus;
(function (CompanyJoinRequestStatus) {
    CompanyJoinRequestStatus["PENDING"] = "PENDING";
    CompanyJoinRequestStatus["APPROVED"] = "APPROVED";
    CompanyJoinRequestStatus["REJECTED"] = "REJECTED";
})(CompanyJoinRequestStatus || (CompanyJoinRequestStatus = {}));
// Get all company join requests for a specific company
async function getCompanyRequests(req, res) {
    try {
        const { companyId } = req.params;
        const requests = await prisma_js_1.prisma.companyJoinRequest.findMany({
            where: {
                companyId: Number(companyId),
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
                company: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(requests);
    }
    catch (error) {
        console.error("Error fetching company join requests:", error);
        res.status(500).json({ error: "Failed to fetch company join requests" });
    }
}
// Get all company join requests for the current user's company
async function getCurrentUserCompanyRequests(req, res) {
    try {
        const userId = req.user.id;
        // Find the user's company
        const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
            where: {
                userId,
                role: "OWNER",
            },
            select: {
                companyId: true,
            },
        });
        if (!userCompany) {
            return res.status(404).json({ error: "No company found for this user" });
        }
        const requests = await prisma_js_1.prisma.companyJoinRequest.findMany({
            where: {
                companyId: userCompany.companyId,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
                company: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(requests);
    }
    catch (error) {
        console.error("Error fetching company join requests:", error);
        res.status(500).json({ error: "Failed to fetch company join requests" });
    }
}
// Update a company join request (mark as read, approve, reject)
async function updateCompanyRequest(req, res) {
    try {
        const { requestId } = req.params;
        const { status, responseMessage, isRead } = req.body;
        // Validate the request
        const request = await prisma_js_1.prisma.companyJoinRequest.findUnique({
            where: { id: Number(requestId) },
            include: {
                company: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        });
        if (!request) {
            return res.status(404).json({ error: "Join request not found" });
        }
        // Check if the user is the company owner
        const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
            where: {
                userId: req.user.id,
                companyId: request.companyId,
                role: "OWNER",
            },
        });
        if (!userCompany) {
            return res
                .status(403)
                .json({ error: "You are not authorized to update this request" });
        }
        // Update the request
        const updateData = {};
        if (status) {
            updateData.status = status;
        }
        if (responseMessage) {
            updateData.responseMessage = responseMessage;
        }
        if (isRead !== undefined) {
            updateData.isRead = isRead;
        }
        const updatedRequest = await prisma_js_1.prisma.companyJoinRequest.update({
            where: { id: Number(requestId) },
            data: updateData,
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
                company: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        });
        res.json(updatedRequest);
    }
    catch (error) {
        console.error("Error updating company join request:", error);
        res.status(500).json({ error: "Failed to update company join request" });
    }
}
//# sourceMappingURL=company-requests.controller.js.map