<!-- client/layers/core/components/activity/ActivityLogList.vue -->

<template>
  <div class="activity-log-list">
    <div v-if="loading" class="flex justify-center py-8">
      <BaseButtonIcon shape="rounded" color="primary" loading />
    </div>

    <div v-else-if="activities.length === 0" class="text-center py-12">
      <Icon
        name="ph:activity-duotone"
        class="h-16 w-16 text-muted-300 dark:text-muted-700 mx-auto mb-4"
      />
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        class="text-muted-800 dark:text-white mb-2"
      >
        No Activities Found
      </BaseHeading>
      <BaseParagraph
        class="text-muted-500 dark:text-muted-400 max-w-md mx-auto"
      >
        There are no activities matching your filters. Try adjusting your search
        criteria.
      </BaseParagraph>
    </div>

    <div v-else>
      <BaseCard>
        <ActivityLogItem
          v-for="(activity, index) in activities"
          :key="index"
          :activity="activity"
          :show-avatar="showAvatars"
          :show-details-button="showDetailsButton"
          :clickable="clickable"
          @click="$emit('click', activity)"
          @view-details="$emit('view-details', activity)"
          class="activity-log-item"
          :class="{ 'border-b-0': index === activities.length - 1 }"
        />
      </BaseCard>

      <!-- Pagination -->
      <div
        v-if="showPagination && totalPages > 1"
        class="flex justify-center mt-6"
      >
        <BasePagination
          :total-items="totalItems"
          :per-page="perPage"
          :current-page="currentPage"
          @update:current-page="$emit('update:page', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import ActivityLogItem from "./ActivityLogItem.vue";

const props = defineProps({
  activities: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showAvatars: {
    type: Boolean,
    default: true,
  },
  showDetailsButton: {
    type: Boolean,
    default: false,
  },
  clickable: {
    type: Boolean,
    default: false,
  },
  showPagination: {
    type: Boolean,
    default: true,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  perPage: {
    type: Number,
    default: 10,
  },
  totalItems: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(["click", "view-details", "update:page"]);

const totalPages = computed(() => {
  return Math.ceil(props.totalItems / props.perPage);
});
</script>
