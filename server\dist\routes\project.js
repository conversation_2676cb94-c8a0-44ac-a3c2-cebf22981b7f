"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const project_controller_js_1 = require("../controllers/production/project.controller.js");
const router = express_1.default.Router();
// Main project routes
router.get("/", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(project_controller_js_1.getAllProjects));
router.post("/", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(project_controller_js_1.createProject));
exports.default = router;
//# sourceMappingURL=project.js.map