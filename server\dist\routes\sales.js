"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
// Import lead controllers
const leads_controller_js_1 = require("../controllers/sales/leads.controller.js");
// Import deal controllers
const deals_controller_js_1 = require("../controllers/sales/deals.controller.js");
// Import activities controllers
const activities_controller_js_1 = require("../controllers/sales/activities.controller.js");
// Import campaigns controllers
const campaigns_controller_js_1 = require("../controllers/sales/campaigns.controller.js");
// Import products controllers
const products_controller_js_1 = require("../controllers/sales/products.controller.js");
// Import quotations controllers
const quotations_controller_js_1 = require("../controllers/sales/quotations.controller.js");
// Import analytics controllers
const analytics_controller_js_1 = require("../controllers/sales/analytics.controller.js");
// Import settings controllers
const settings_controller_js_1 = require("../controllers/sales/settings.controller.js");
const router = express_1.default.Router();
// Apply authentication middleware to all routes
router.use(route_helpers_js_1.auth);
// Leads
router.get("/leads", (0, route_helpers_js_1.wrapController)(leads_controller_js_1.getAllLeads));
router.get("/leads/:id", (0, route_helpers_js_1.wrapController)(leads_controller_js_1.getLeadById));
router.post("/leads", (0, route_helpers_js_1.wrapController)(leads_controller_js_1.createLead));
router.put("/leads/:id", (0, route_helpers_js_1.wrapController)(leads_controller_js_1.updateLead));
router.delete("/leads/:id", (0, route_helpers_js_1.wrapController)(leads_controller_js_1.deleteLead));
router.post("/leads/import", (0, route_helpers_js_1.wrapController)(leads_controller_js_1.importLeads));
router.post("/leads/:id/convert", (0, route_helpers_js_1.wrapController)(leads_controller_js_1.convertLead));
router.post("/leads/:id/notes", (0, route_helpers_js_1.wrapController)(leads_controller_js_1.addLeadNote));
router.post("/leads/:id/tags", (0, route_helpers_js_1.wrapController)(leads_controller_js_1.addLeadTag));
router.delete("/leads/:id/tags/:tagId", (0, route_helpers_js_1.wrapController)(leads_controller_js_1.removeLeadTag));
// Deals
router.get("/deals", (0, route_helpers_js_1.wrapController)(deals_controller_js_1.getAllDeals));
router.get("/deals/:id", (0, route_helpers_js_1.wrapController)(deals_controller_js_1.getDealById));
router.post("/deals", (0, route_helpers_js_1.wrapController)(deals_controller_js_1.createDeal));
router.put("/deals/:id", (0, route_helpers_js_1.wrapController)(deals_controller_js_1.updateDeal));
router.delete("/deals/:id", (0, route_helpers_js_1.wrapController)(deals_controller_js_1.deleteDeal));
router.get("/deals/pipeline", (0, route_helpers_js_1.wrapController)(deals_controller_js_1.getPipelineDeals));
router.post("/deals/:id/products", (0, route_helpers_js_1.wrapController)(deals_controller_js_1.addProductToDeal));
router.put("/deals/:id/products/:productId", (0, route_helpers_js_1.wrapController)(deals_controller_js_1.updateDealProduct));
router.delete("/deals/:id/products/:productId", (0, route_helpers_js_1.wrapController)(deals_controller_js_1.removeDealProduct));
router.post("/deals/:id/notes", (0, route_helpers_js_1.wrapController)(deals_controller_js_1.addDealNote));
// Activities
router.get("/activities", (0, route_helpers_js_1.wrapController)(activities_controller_js_1.getAllActivities));
router.get("/activities/:id", (0, route_helpers_js_1.wrapController)(activities_controller_js_1.getActivityById));
router.post("/activities", (0, route_helpers_js_1.wrapController)(activities_controller_js_1.createActivity));
router.put("/activities/:id", (0, route_helpers_js_1.wrapController)(activities_controller_js_1.updateActivity));
router.delete("/activities/:id", (0, route_helpers_js_1.wrapController)(activities_controller_js_1.deleteActivity));
router.put("/activities/:id/complete", (0, route_helpers_js_1.wrapController)(activities_controller_js_1.completeActivity));
router.get("/activities/calendar", (0, route_helpers_js_1.wrapController)(activities_controller_js_1.getCalendarActivities));
// Campaigns
router.get("/campaigns", (0, route_helpers_js_1.wrapController)(campaigns_controller_js_1.getAllCampaigns));
router.get("/campaigns/:id", (0, route_helpers_js_1.wrapController)(campaigns_controller_js_1.getCampaignById));
router.post("/campaigns", (0, route_helpers_js_1.wrapController)(campaigns_controller_js_1.createCampaign));
router.put("/campaigns/:id", (0, route_helpers_js_1.wrapController)(campaigns_controller_js_1.updateCampaign));
router.delete("/campaigns/:id", (0, route_helpers_js_1.wrapController)(campaigns_controller_js_1.deleteCampaign));
router.post("/campaigns/:id/leads", (0, route_helpers_js_1.wrapController)(campaigns_controller_js_1.addLeadToCampaign));
router.delete("/campaigns/:id/leads/:leadId", (0, route_helpers_js_1.wrapController)(campaigns_controller_js_1.removeLeadFromCampaign));
router.post("/campaigns/:id/metrics", (0, route_helpers_js_1.wrapController)(campaigns_controller_js_1.addCampaignMetric));
router.get("/campaigns/templates", (0, route_helpers_js_1.wrapController)(campaigns_controller_js_1.getCampaignTemplates));
// Products
router.get("/products", (0, route_helpers_js_1.wrapController)(products_controller_js_1.getAllProducts));
router.get("/products/:id", (0, route_helpers_js_1.wrapController)(products_controller_js_1.getProductById));
router.post("/products", (0, route_helpers_js_1.wrapController)(products_controller_js_1.createProduct));
router.put("/products/:id", (0, route_helpers_js_1.wrapController)(products_controller_js_1.updateProduct));
router.delete("/products/:id", (0, route_helpers_js_1.wrapController)(products_controller_js_1.deleteProduct));
// Quotations
router.get("/quotations", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.getAllQuotations));
router.get("/quotations/:id", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.getQuotationById));
router.post("/quotations", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.createQuotation));
router.put("/quotations/:id", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.updateQuotation));
router.delete("/quotations/:id", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.deleteQuotation));
router.post("/quotations/:id/items", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.addQuotationItem));
router.put("/quotations/:id/items/:itemId", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.updateQuotationItem));
router.delete("/quotations/:id/items/:itemId", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.deleteQuotationItem));
router.put("/quotations/:id/send", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.sendQuotation));
router.put("/quotations/:id/accept", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.acceptQuotation));
router.put("/quotations/:id/reject", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.rejectQuotation));
router.get("/quotations/templates", (0, route_helpers_js_1.wrapController)(quotations_controller_js_1.getQuotationTemplates));
// Analytics
router.get("/analytics/performance", (0, route_helpers_js_1.wrapController)(analytics_controller_js_1.getPerformanceMetrics));
router.get("/analytics/forecasts", (0, route_helpers_js_1.wrapController)(analytics_controller_js_1.getSalesForecasts));
router.get("/analytics/reports", (0, route_helpers_js_1.wrapController)(analytics_controller_js_1.getCustomReports));
router.post("/analytics/reports", (0, route_helpers_js_1.wrapController)(analytics_controller_js_1.createCustomReport));
// Settings
router.get("/settings/stages", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.getPipelineStages));
router.post("/settings/stages", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.createPipelineStage));
router.put("/settings/stages/:id", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.updatePipelineStage));
router.delete("/settings/stages/:id", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.deletePipelineStage));
router.get("/settings/goals", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.getSalesGoals));
router.post("/settings/goals", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.createSalesGoal));
router.put("/settings/goals/:id", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.updateSalesGoal));
router.delete("/settings/goals/:id", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.deleteSalesGoal));
router.get("/settings/automation", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.getAutomationRules));
router.post("/settings/automation", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.createAutomationRule));
router.put("/settings/automation/:id", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.updateAutomationRule));
router.delete("/settings/automation/:id", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.deleteAutomationRule));
router.get("/settings/integrations", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.getIntegrations));
router.put("/settings/integrations/:id", (0, route_helpers_js_1.wrapController)(settings_controller_js_1.updateIntegration));
exports.default = router;
//# sourceMappingURL=sales.js.map