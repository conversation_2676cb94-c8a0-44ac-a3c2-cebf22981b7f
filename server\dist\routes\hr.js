"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const employee_controller_js_1 = require("../controllers/hr/employee.controller.js");
const payroll_controller_js_1 = require("../controllers/hr/payroll.controller.js");
const leave_controller_js_1 = require("../controllers/hr/leave.controller.js");
const benefits_controller_js_1 = require("../controllers/hr/benefits.controller.js");
const bonuses_controller_js_1 = require("../controllers/hr/bonuses.controller.js");
const performance_controller_js_1 = require("../controllers/hr/performance.controller.js");
const training_controller_js_1 = require("../controllers/hr/training.controller.js");
const career_controller_js_1 = require("../controllers/hr/career.controller.js");
const documents_controller_js_1 = require("../controllers/hr/documents.controller.js");
const router = express_1.default.Router();
// Employee routes
router.get("/employees", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(employee_controller_js_1.getAllEmployees));
router.get("/employees/stats", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(employee_controller_js_1.getEmployeeStats));
router.get("/employees/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(employee_controller_js_1.getEmployeeById));
router.post("/employees", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(employee_controller_js_1.createEmployee));
router.put("/employees/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(employee_controller_js_1.updateEmployee));
router.delete("/employees/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(employee_controller_js_1.deleteEmployee));
router.get("/employees/:id/documents", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(employee_controller_js_1.getEmployeeDocuments));
router.post("/employees/:id/documents", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(employee_controller_js_1.uploadEmployeeDocument));
// Department routes
router.get("/departments", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(employee_controller_js_1.getDepartments));
// Payroll routes
router.get("/payroll", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(payroll_controller_js_1.getPayrollData));
router.get("/payroll/history", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(payroll_controller_js_1.getPayrollHistory));
router.get("/payroll/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(payroll_controller_js_1.getPayrollById));
router.post("/payroll/generate", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(payroll_controller_js_1.generatePayroll));
router.put("/payroll/:id/status", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(payroll_controller_js_1.updatePayrollStatus));
// Leave management routes
router.get("/leave", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(leave_controller_js_1.getLeaveRequests));
router.post("/leave", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(leave_controller_js_1.createLeaveRequest));
router.put("/leave/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(leave_controller_js_1.updateLeaveRequest));
router.delete("/leave/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(leave_controller_js_1.deleteLeaveRequest));
router.get("/leave/balance/:employeeId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(leave_controller_js_1.getLeaveBalance));
// Benefits routes
router.get("/benefits", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(benefits_controller_js_1.getAllBenefits));
router.get("/benefits/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(benefits_controller_js_1.getBenefitById));
router.post("/benefits", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(benefits_controller_js_1.createBenefit));
router.put("/benefits/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(benefits_controller_js_1.updateBenefit));
router.delete("/benefits/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(benefits_controller_js_1.deleteBenefit));
router.post("/benefits/assign", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(benefits_controller_js_1.assignBenefitToEmployee));
router.get("/employees/:employeeId/benefits", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(benefits_controller_js_1.getEmployeeBenefits));
router.put("/employee-benefits/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(benefits_controller_js_1.updateEmployeeBenefit));
router.delete("/employee-benefits/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(benefits_controller_js_1.removeEmployeeBenefit));
// Bonuses routes
router.get("/bonuses", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(bonuses_controller_js_1.getAllBonuses));
router.get("/bonuses/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(bonuses_controller_js_1.getBonusById));
router.post("/bonuses", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(bonuses_controller_js_1.createBonus));
router.put("/bonuses/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(bonuses_controller_js_1.updateBonus));
router.delete("/bonuses/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(bonuses_controller_js_1.deleteBonus));
router.get("/employees/:employeeId/bonuses", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(bonuses_controller_js_1.getEmployeeBonuses));
router.put("/bonuses/:id/pay", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(bonuses_controller_js_1.markBonusAsPaid));
// Performance review routes
router.get("/performance", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(performance_controller_js_1.getAllPerformanceReviews));
router.get("/performance/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(performance_controller_js_1.getPerformanceReviewById));
router.post("/performance", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(performance_controller_js_1.createPerformanceReview));
router.put("/performance/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(performance_controller_js_1.updatePerformanceReview));
router.delete("/performance/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(performance_controller_js_1.deletePerformanceReview));
router.get("/employees/:employeeId/performance", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(performance_controller_js_1.getEmployeePerformanceReviews));
router.put("/performance/:id/acknowledge", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(performance_controller_js_1.acknowledgePerformanceReview));
// Training routes
router.get("/training", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(training_controller_js_1.getAllTrainings));
router.get("/training/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(training_controller_js_1.getTrainingById));
router.post("/training", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(training_controller_js_1.createTraining));
router.put("/training/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(training_controller_js_1.updateTraining));
router.delete("/training/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(training_controller_js_1.deleteTraining));
router.post("/training/enroll", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(training_controller_js_1.enrollEmployeeInTraining));
router.put("/employee-training/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(training_controller_js_1.updateEmployeeTraining));
router.get("/employees/:employeeId/training", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(training_controller_js_1.getEmployeeTrainings));
// Career path routes
router.get("/career-paths", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(career_controller_js_1.getAllCareerPaths));
router.get("/career-paths/:employeeId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(career_controller_js_1.getCareerPathByEmployeeId));
router.post("/career-paths", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(career_controller_js_1.createCareerPath));
router.put("/career-paths/:employeeId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(career_controller_js_1.updateCareerPath));
router.delete("/career-paths/:employeeId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(career_controller_js_1.deleteCareerPath));
// Document routes
router.get("/documents", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.getAllDocuments));
router.get("/documents/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.getDocumentById));
router.post("/documents", route_helpers_js_1.auth, documents_controller_js_1.upload.single("file"), (0, route_helpers_js_1.wrapController)(documents_controller_js_1.uploadDocument));
router.put("/documents/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.updateDocument));
router.delete("/documents/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.deleteDocument));
router.post("/documents/assign", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.assignDocumentToEmployee));
router.get("/employees/:employeeId/document-assignments", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.getEmployeeDocuments));
router.put("/employee-documents/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.updateEmployeeDocument));
router.delete("/employee-documents/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.removeEmployeeDocument));
exports.default = router;
//# sourceMappingURL=hr.js.map