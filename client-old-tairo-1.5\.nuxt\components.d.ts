
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
type HydrationStrategies = {
  hydrateOnVisible?: IntersectionObserverInit | true
  hydrateOnIdle?: number | true
  hydrateOnInteraction?: keyof HTMLElementEventMap | Array<keyof HTMLElementEventMap> | true
  hydrateOnMediaQuery?: string
  hydrateAfter?: number
  hydrateWhen?: boolean
  hydrateNever?: true
}
type LazyComponent<T> = (T & DefineComponent<HydrationStrategies, {}, {}, {}, {}, {}, {}, { hydrated: () => void }>)
interface _GlobalComponents {
      'AccountMenu': typeof import("../components/global/AccountMenu.vue")['default']
    'AccountingSubsidebar': typeof import("../components/global/AccountingSubsidebar.vue")['default']
    'AddContactButton': typeof import("../components/global/AddContactButton.vue")['default']
    'AddContactForm': typeof import("../components/global/AddContactForm.vue")['default']
    'AddContactModal': typeof import("../components/global/AddContactModal.vue")['default']
    'AddMachineButton': typeof import("../components/global/AddMachineButton.vue")['default']
    'AddMachineForm': typeof import("../components/global/AddMachineForm.vue")['default']
    'AddMachineModal': typeof import("../components/global/AddMachineModal.vue")['default']
    'AddMeetingButton': typeof import("../components/global/AddMeetingButton.vue")['default']
    'AddMeetingForm': typeof import("../components/global/AddMeetingForm.vue")['default']
    'AddMeetingModal': typeof import("../components/global/AddMeetingModal.vue")['default']
    'AddUserButton': typeof import("../components/global/AddUserButton.vue")['default']
    'AddUserForm': typeof import("../components/global/AddUserForm.vue")['default']
    'AddUserModal': typeof import("../components/global/AddUserModal.vue")['default']
    'AppLayoutSwitcher': typeof import("../components/global/AppLayoutSwitcher.vue")['default']
    'AppSearch': typeof import("../components/global/AppSearch.vue")['default']
    'AppSearchResult': typeof import("../components/global/AppSearchResult.vue")['default']
    'BudgetSubsidebar': typeof import("../components/global/BudgetSubsidebar.vue")['default']
    'CircularMenuActivity': typeof import("../components/global/CircularMenuActivity.vue")['default']
    'CircularMenuLanguage': typeof import("../components/global/CircularMenuLanguage.vue")['default']
    'CircularMenuNotifications': typeof import("../components/global/CircularMenuNotifications.vue")['default']
    'CollapseNavigationFooter': typeof import("../components/global/CollapseNavigationFooter.vue")['default']
    'CollapseNavigationHeader': typeof import("../components/global/CollapseNavigationHeader.vue")['default']
    'CommunicationSubsidebar': typeof import("../components/global/CommunicationSubsidebar.vue")['default']
    'CompaniesSubsidebar': typeof import("../components/global/CompaniesSubsidebar.vue")['default']
    'CoreSubsidebar': typeof import("../components/global/CoreSubsidebar.vue")['default']
    'CustomerVehiclesTable': typeof import("../components/global/CustomerVehiclesTable.vue")['default']
    'FlexTableCell': typeof import("../components/global/FlexTableCell.vue")['default']
    'FlexTableRow': typeof import("../components/global/FlexTableRow.vue")['default']
    'FlexTableStart': typeof import("../components/global/FlexTableStart.vue")['default']
    'HrSubsidebar': typeof import("../components/global/HrSubsidebar.vue")['default']
    'Logo': typeof import("../components/global/Logo.vue")['default']
    'LogoText': typeof import("../components/global/LogoText.vue")['default']
    'ModuleAccessInfo': typeof import("../components/global/ModuleAccessInfo.vue")['default']
    'PanelLanguage': typeof import("../components/global/PanelLanguage.vue")['default']
    'PanelSearch': typeof import("../components/global/PanelSearch.vue")['default']
    'PanelTask': typeof import("../components/global/PanelTask.vue")['default']
    'PhoneInput': typeof import("../components/global/PhoneInput.vue")['default']
    'ProductionSidebar': typeof import("../components/global/ProductionSidebar.vue")['default']
    'ProductionSubsidebar': typeof import("../components/global/ProductionSubsidebar.vue")['default']
    'RecruitmentSidebar': typeof import("../components/global/RecruitmentSidebar.vue")['default']
    'SalesSubsidebar': typeof import("../components/global/SalesSubsidebar.vue")['default']
    'Subsidebar': typeof import("../components/global/Subsidebar.vue")['default']
    'SubsidebarClients': typeof import("../components/global/SubsidebarClients.vue")['default']
    'SubsidebarDashboards': typeof import("../components/global/SubsidebarDashboards.vue")['default']
    'SubsidebarHeader': typeof import("../components/global/SubsidebarHeader.vue")['default']
    'SubsidebarLayouts': typeof import("../components/global/SubsidebarLayouts.vue")['default']
    'SubsidebarMenu': typeof import("../components/global/SubsidebarMenu.vue")['default']
    'SubsidebarMenuCollapseLinks': typeof import("../components/global/SubsidebarMenuCollapseLinks.vue")['default']
    'SubsidebarMenuDivider': typeof import("../components/global/SubsidebarMenuDivider.vue")['default']
    'SubsidebarMenuLink': typeof import("../components/global/SubsidebarMenuLink.vue")['default']
    'TeamListCompact': typeof import("../components/global/TeamListCompact.vue")['default']
    'ThemeToggle': typeof import("../components/global/ThemeToggle.vue")['default']
    'TimeManagementSubsidebar': typeof import("../components/global/TimeManagementSubsidebar.vue")['default']
    'Toaster': typeof import("../components/global/Toaster.vue")['default']
    'TocAnchor': typeof import("../components/global/TocAnchor.vue")['default']
    'ToolbarAccountMenu': typeof import("../components/global/ToolbarAccountMenu.vue")['default']
    'ToolbarActivity': typeof import("../components/global/ToolbarActivity.vue")['default']
    'ToolbarCustomize': typeof import("../components/global/ToolbarCustomize.vue")['default']
    'ToolbarLanguage': typeof import("../components/global/ToolbarLanguage.vue")['default']
    'ToolbarNotifications': typeof import("../components/global/ToolbarNotifications.vue")['default']
    'ToolbarSearch': typeof import("../components/global/ToolbarSearch.vue")['default']
    'AccessControl': typeof import("../components/AccessControl.vue")['default']
    'AddonApexcharts': typeof import("../components/AddonApexcharts.vue")['default']
    'AddonInputPassword': typeof import("../components/AddonInputPassword.vue")['default']
    'AdminsPanelAccount': typeof import("../components/Admins/PanelAccount.vue")['default']
    'AdminsPanelActivity': typeof import("../components/Admins/PanelActivity.vue")['default']
    'AdminsPanelCard': typeof import("../components/Admins/PanelCard.vue")['default']
    'AdminsPanelInvest': typeof import("../components/Admins/PanelInvest.vue")['default']
    'AdminsPanels': typeof import("../components/Admins/Panels.vue")['default']
    'AdminsSidebarBurger': typeof import("../components/Admins/SidebarBurger.vue")['default']
    'AdminsSidebarCircularMenu': typeof import("../components/Admins/SidebarCircularMenu.vue")['default']
    'AdminsSidebarLayout': typeof import("../components/Admins/SidebarLayout.vue")['default']
    'AdminsSidebarNavigation': typeof import("../components/Admins/SidebarNavigation.vue")['default']
    'AdminsSidebarNavigationItem': typeof import("../components/Admins/SidebarNavigationItem.vue")['default']
    'AdminsSidebarNavigationPanel': typeof import("../components/Admins/SidebarNavigationPanel.vue")['default']
    'AdminsSidebarToolbar': typeof import("../components/Admins/SidebarToolbar.vue")['default']
    'AdminsSidebarTools': typeof import("../components/Admins/SidebarTools.vue")['default']
    'AdminsToolbarNotifications': typeof import("../components/Admins/ToolbarNotifications.vue")['default']
    'AdminsTopnavWorkspaceDropdown': typeof import("../components/Admins/TopnavWorkspaceDropdown.vue")['default']
    'AiAssistant': typeof import("../components/AiAssistant.vue")['default']
    'AiAssistantButton': typeof import("../components/AiAssistantButton.vue")['default']
    'CalendarWidget': typeof import("../components/CalendarWidget.vue")['default']
    'DatePicker': typeof import("../components/DatePicker.vue")['default']
    'DemoAccountMenu': typeof import("../components/DemoAccountMenu.vue")['default']
    'DemoCalendarEvent': typeof import("../components/DemoCalendarEvent.vue")['default']
    'DemoCalendarEventPending': typeof import("../components/DemoCalendarEventPending.vue")['default']
    'TairoSidebarNavigation': typeof import("../components/TairoSidebarNavigation.vue")['default']
    'UsersAccountMenu': typeof import("../components/Users/<USER>")['default']
    'UsersPanelAccount': typeof import("../components/Users/<USER>")['default']
    'UsersPanelActivity': typeof import("../components/Users/<USER>")['default']
    'UsersPanelCard': typeof import("../components/Users/<USER>")['default']
    'UsersPanelInvest': typeof import("../components/Users/<USER>")['default']
    'UsersPanels': typeof import("../components/Users/<USER>")['default']
    'UsersSidebarBurger': typeof import("../components/Users/<USER>")['default']
    'UsersSidebarCircularMenu': typeof import("../components/Users/<USER>")['default']
    'UsersSidebarLayout': typeof import("../components/Users/<USER>")['default']
    'UsersSidebarNavigation': typeof import("../components/Users/<USER>")['default']
    'UsersSidebarNavigationItem': typeof import("../components/Users/<USER>")['default']
    'UsersSidebarNavigationPanel': typeof import("../components/Users/<USER>")['default']
    'UsersSidebarToolbar': typeof import("../components/Users/<USER>")['default']
    'UsersSidebarTools': typeof import("../components/Users/<USER>")['default']
    'UsersToolbarAccountMenu': typeof import("../components/Users/<USER>")['default']
    'UsersTopnavWorkspaceDropdown': typeof import("../components/Users/<USER>")['default']
    'AuthEmailVerification': typeof import("../components/auth/EmailVerification.vue")['default']
    'AuthPaymentForm': typeof import("../components/auth/PaymentForm.vue")['default']
    'AuthRegistrationForm': typeof import("../components/auth/RegistrationForm.vue")['default']
    'AuthRegistrationSuccess': typeof import("../components/auth/RegistrationSuccess.vue")['default']
    'AuthRoleSelection': typeof import("../components/auth/RoleSelection.vue")['default']
    'AuthStripePaymentForm': typeof import("../components/auth/StripePaymentForm.vue")['default']
    'AuthSubscriptionPlanSelection': typeof import("../components/auth/SubscriptionPlanSelection.vue")['default']
    'BaseLoader': typeof import("../components/base/BaseLoader.vue")['default']
    'DemoChartArea': typeof import("../components/demo-chart/DemoChartArea.vue")['default']
    'DemoChartAreaBalance': typeof import("../components/demo-chart/DemoChartAreaBalance.vue")['default']
    'DemoChartAreaMulti': typeof import("../components/demo-chart/DemoChartAreaMulti.vue")['default']
    'DemoChartAreaStats': typeof import("../components/demo-chart/DemoChartAreaStats.vue")['default']
    'DemoChartBar': typeof import("../components/demo-chart/DemoChartBar.vue")['default']
    'DemoChartBarHorizontal': typeof import("../components/demo-chart/DemoChartBarHorizontal.vue")['default']
    'DemoChartBarHorizontalMulti': typeof import("../components/demo-chart/DemoChartBarHorizontalMulti.vue")['default']
    'DemoChartBarMulti': typeof import("../components/demo-chart/DemoChartBarMulti.vue")['default']
    'DemoChartBarMultiIncome': typeof import("../components/demo-chart/DemoChartBarMultiIncome.vue")['default']
    'DemoChartBarRange': typeof import("../components/demo-chart/DemoChartBarRange.vue")['default']
    'DemoChartBarSocialChannels': typeof import("../components/demo-chart/DemoChartBarSocialChannels.vue")['default']
    'DemoChartBarStacked': typeof import("../components/demo-chart/DemoChartBarStacked.vue")['default']
    'DemoChartBubble': typeof import("../components/demo-chart/DemoChartBubble.vue")['default']
    'DemoChartDonut': typeof import("../components/demo-chart/DemoChartDonut.vue")['default']
    'DemoChartDonutExpenses': typeof import("../components/demo-chart/DemoChartDonutExpenses.vue")['default']
    'DemoChartLine': typeof import("../components/demo-chart/DemoChartLine.vue")['default']
    'DemoChartLineMulti': typeof import("../components/demo-chart/DemoChartLineMulti.vue")['default']
    'DemoChartLineMultiAlt': typeof import("../components/demo-chart/DemoChartLineMultiAlt.vue")['default']
    'DemoChartLineStep': typeof import("../components/demo-chart/DemoChartLineStep.vue")['default']
    'DemoChartPie': typeof import("../components/demo-chart/DemoChartPie.vue")['default']
    'DemoChartRadar': typeof import("../components/demo-chart/DemoChartRadar.vue")['default']
    'DemoChartRadial': typeof import("../components/demo-chart/DemoChartRadial.vue")['default']
    'DemoChartRadialGauge': typeof import("../components/demo-chart/DemoChartRadialGauge.vue")['default']
    'DemoChartRadialGaugeAlt': typeof import("../components/demo-chart/DemoChartRadialGaugeAlt.vue")['default']
    'DemoChartRadialMulti': typeof import("../components/demo-chart/DemoChartRadialMulti.vue")['default']
    'DemoChartScatter': typeof import("../components/demo-chart/DemoChartScatter.vue")['default']
    'DemoChartTimeline': typeof import("../components/demo-chart/DemoChartTimeline.vue")['default']
    'ExamplesComponentAccessExample': typeof import("../components/examples/ComponentAccessExample.vue")['default']
    'TairoLogo': typeof import("../layers/tairo/components/global/TairoLogo.vue")['default']
    'TairoLogoText': typeof import("../layers/tairo/components/global/TairoLogoText.vue")['default']
    'TairoToaster': typeof import("../layers/tairo/components/global/TairoToaster.vue")['default']
    'TairoTocAnchor': typeof import("../layers/tairo/components/global/TairoTocAnchor.vue")['default']
    'TairoCheckAnimated': typeof import("../layers/tairo/components/TairoCheckAnimated.vue")['default']
    'TairoContentWrapper': typeof import("../layers/tairo/components/TairoContentWrapper.vue")['default']
    'TairoContentWrapperTabbed': typeof import("../layers/tairo/components/TairoContentWrapperTabbed.vue")['default']
    'TairoError': typeof import("../layers/tairo/components/TairoError.vue")['default']
    'TairoFlexTable': typeof import("../layers/tairo/components/TairoFlexTable.vue")['default']
    'TairoFlexTableCell': typeof import("../layers/tairo/components/TairoFlexTableCell.vue")['default']
    'TairoFlexTableHeading': typeof import("../layers/tairo/components/TairoFlexTableHeading.vue")['default']
    'TairoFlexTableRow': typeof import("../layers/tairo/components/TairoFlexTableRow.vue")['default']
    'TairoFormGroup': typeof import("../layers/tairo/components/TairoFormGroup.vue")['default']
    'TairoFormSave': typeof import("../layers/tairo/components/TairoFormSave.vue")['default']
    'TairoImageZoom': typeof import("../layers/tairo/components/TairoImageZoom.vue")['default']
    'TairoModal': typeof import("../layers/tairo/components/TairoModal.vue")['default']
    'TairoPanels': typeof import("../layers/tairo/components/TairoPanels.vue")['default']
    'TairoPasswordStrength': typeof import("../layers/tairo/components/TairoPasswordStrength.vue")['default']
    'TairoPopover': typeof import("../layers/tairo/components/TairoPopover.vue")['default']
    'TairoPopoverContentDual': typeof import("../layers/tairo/components/TairoPopoverContentDual.vue")['default']
    'TairoPopoverContentHelp': typeof import("../layers/tairo/components/TairoPopoverContentHelp.vue")['default']
    'TairoPopoverContentMedia': typeof import("../layers/tairo/components/TairoPopoverContentMedia.vue")['default']
    'TairoSidebarLayoutConfig': typeof import("../layers/tairo/components/TairoSidebarLayout.config")['default']
    'TairoTable': typeof import("../layers/tairo/components/TairoTable.vue")['default']
    'TairoTableCell': typeof import("../layers/tairo/components/TairoTableCell.vue")['default']
    'TairoTableHeading': typeof import("../layers/tairo/components/TairoTableHeading.vue")['default']
    'TairoTableRow': typeof import("../layers/tairo/components/TairoTableRow.vue")['default']
    'TairoToc': typeof import("../layers/tairo/components/TairoToc.vue")['default']
    'TairoWelcome': typeof import("../layers/tairo/components/TairoWelcome.vue")['default']
    'AccountingDashboard': typeof import("../layers/accounting/components/AccountingDashboard.vue")['default']
    'JournalEntryForm': typeof import("../layers/accounting/components/JournalEntryForm.vue")['default']
    'BudgetChartsSection': typeof import("../layers/budget/components/BudgetChartsSection.vue")['default']
    'BudgetDashboard': typeof import("../layers/budget/components/BudgetDashboard.vue")['default']
    'BudgetIncomeForm': typeof import("../layers/budget/components/BudgetIncomeForm.vue")['default']
    'ExpensesSection': typeof import("../layers/budget/components/ExpensesSection.vue")['default']
    'GlobalFooter': typeof import("../layers/budget/components/GlobalFooter.vue")['default']
    'GlobalHeader': typeof import("../layers/budget/components/GlobalHeader.vue")['default']
    'IncomeSection': typeof import("../layers/budget/components/IncomeSection.vue")['default']
    'ChartsBudgetChartsSection': typeof import("../layers/budget/components/charts/BudgetChartsSection.vue")['default']
    'DashboardBudgetGoalsCard': typeof import("../layers/budget/components/dashboard/BudgetGoalsCard.vue")['default']
    'DashboardBudgetSummaryCard': typeof import("../layers/budget/components/dashboard/BudgetSummaryCard.vue")['default']
    'DashboardBudgetTrendsChart': typeof import("../layers/budget/components/dashboard/BudgetTrendsChart.vue")['default']
    'DashboardBudgetTypeSelector': typeof import("../layers/budget/components/dashboard/BudgetTypeSelector.vue")['default']
    'DashboardCategoryBreakdownCard': typeof import("../layers/budget/components/dashboard/CategoryBreakdownCard.vue")['default']
    'DashboardRecentTransactionsCard': typeof import("../layers/budget/components/dashboard/RecentTransactionsCard.vue")['default']
    'ExpensesMultipleExpenses': typeof import("../layers/budget/components/expenses/MultipleExpenses.vue")['default']
    'ExpensesOneTimeExpenses': typeof import("../layers/budget/components/expenses/OneTimeExpenses.vue")['default']
    'ExpensesRepeatedExpenses': typeof import("../layers/budget/components/expenses/RepeatedExpenses.vue")['default']
    'ForecastingCategories': typeof import("../layers/budget/components/forecasting/ForecastingCategories.vue")['default']
    'ForecastingChart': typeof import("../layers/budget/components/forecasting/ForecastingChart.vue")['default']
    'ForecastingControls': typeof import("../layers/budget/components/forecasting/ForecastingControls.vue")['default']
    'ForecastingScenarios': typeof import("../layers/budget/components/forecasting/ForecastingScenarios.vue")['default']
    'ForecastingSummary': typeof import("../layers/budget/components/forecasting/ForecastingSummary.vue")['default']
    'IncomesMultipleIncomes': typeof import("../layers/budget/components/incomes/MultipleIncomes.vue")['default']
    'IncomesOneTimeIncomes': typeof import("../layers/budget/components/incomes/OneTimeIncomes.vue")['default']
    'IncomesRepeatedIncomes': typeof import("../layers/budget/components/incomes/RepeatedIncomes.vue")['default']
    'PlannerBudgetComparisonCard': typeof import("../layers/budget/components/planner/BudgetComparisonCard.vue")['default']
    'PlannerMonthlyBudgetCard': typeof import("../layers/budget/components/planner/MonthlyBudgetCard.vue")['default']
    'QuickBudgetCategoryBreakdown': typeof import("../layers/budget/components/quick-budget/QuickBudgetCategoryBreakdown.vue")['default']
    'QuickBudgetEntryForm': typeof import("../layers/budget/components/quick-budget/QuickBudgetEntryForm.vue")['default']
    'QuickBudgetExpensesList': typeof import("../layers/budget/components/quick-budget/QuickBudgetExpensesList.vue")['default']
    'QuickBudgetGoals': typeof import("../layers/budget/components/quick-budget/QuickBudgetGoals.vue")['default']
    'QuickBudgetIncomeList': typeof import("../layers/budget/components/quick-budget/QuickBudgetIncomeList.vue")['default']
    'QuickBudgetSummaryCard': typeof import("../layers/budget/components/quick-budget/QuickBudgetSummaryCard.vue")['default']
    'QuickBudgetTransactionsList': typeof import("../layers/budget/components/quick-budget/QuickBudgetTransactionsList.vue")['default']
    'StatsSection': typeof import("../layers/budget/components/stats/StatsSection.vue")['default']
    'TransactionsListCard': typeof import("../layers/budget/components/transactions/TransactionsListCard.vue")['default']
    'CompaniesDashboard': typeof import("../layers/companies/components/CompaniesDashboard.vue")['default']
    'CompanyDashboard': typeof import("../layers/companies/components/CompanyDashboard.vue")['default']
    'CompanyForm': typeof import("../layers/companies/components/CompanyForm.vue")['default']
    'ClientsClientCard': typeof import("../layers/companies/components/clients/ClientCard.vue")['default']
    'ClientsTable': typeof import("../layers/companies/components/clients/ClientsTable.vue")['default']
    'ComplianceAudits': typeof import("../layers/companies/components/compliance/ComplianceAudits.vue")['default']
    'ComplianceOverview': typeof import("../layers/companies/components/compliance/ComplianceOverview.vue")['default']
    'ComplianceRequirements': typeof import("../layers/companies/components/compliance/ComplianceRequirements.vue")['default']
    'DocumentsDocumentCard': typeof import("../layers/companies/components/documents/DocumentCard.vue")['default']
    'RiskMitigationPlans': typeof import("../layers/companies/components/risk/MitigationPlans.vue")['default']
    'RiskDashboard': typeof import("../layers/companies/components/risk/RiskDashboard.vue")['default']
    'RiskRegister': typeof import("../layers/companies/components/risk/RiskRegister.vue")['default']
    'SettingsGeneralSettings': typeof import("../layers/companies/components/settings/GeneralSettings.vue")['default']
    'SettingsIntegrationSettings': typeof import("../layers/companies/components/settings/IntegrationSettings.vue")['default']
    'SettingsNotificationSettings': typeof import("../layers/companies/components/settings/NotificationSettings.vue")['default']
    'SettingsPermissionSettings': typeof import("../layers/companies/components/settings/PermissionSettings.vue")['default']
    'SuppliersSupplierCard': typeof import("../layers/companies/components/suppliers/SupplierCard.vue")['default']
    'SuppliersSupplierDocuments': typeof import("../layers/companies/components/suppliers/SupplierDocuments.vue")['default']
    'SuppliersSupplierOrders': typeof import("../layers/companies/components/suppliers/SupplierOrders.vue")['default']
    'SuppliersSupplierOverview': typeof import("../layers/companies/components/suppliers/SupplierOverview.vue")['default']
    'SuppliersSupplierPerformance': typeof import("../layers/companies/components/suppliers/SupplierPerformance.vue")['default']
    'SuppliersSupplierPerformanceMetrics': typeof import("../layers/companies/components/suppliers/SupplierPerformanceMetrics.vue")['default']
    'SuppliersSupplierTransactions': typeof import("../layers/companies/components/suppliers/SupplierTransactions.vue")['default']
    'SuppliersTable': typeof import("../layers/companies/components/suppliers/SuppliersTable.vue")['default']
    'WorkforceCapacityPlanning': typeof import("../layers/companies/components/workforce/CapacityPlanning.vue")['default']
    'WorkforceSkillsGapAnalysis': typeof import("../layers/companies/components/workforce/SkillsGapAnalysis.vue")['default']
    'WorkforceOverview': typeof import("../layers/companies/components/workforce/WorkforceOverview.vue")['default']
    'BaseLayerPage': typeof import("../layers/core/components/BaseLayerPage.vue")['default']
    'BusinessRuleModal': typeof import("../layers/core/components/BusinessRuleModal.vue")['default']
    'CoreDashboard': typeof import("../layers/core/components/CoreDashboard.vue")['default']
    'ActivityFilters': typeof import("../layers/core/components/activity/ActivityFilters.vue")['default']
    'ActivityLogDetails': typeof import("../layers/core/components/activity/ActivityLogDetails.vue")['default']
    'ActivityLogItem': typeof import("../layers/core/components/activity/ActivityLogItem.vue")['default']
    'ActivityLogList': typeof import("../layers/core/components/activity/ActivityLogList.vue")['default']
    'AiDecisionCard': typeof import("../layers/core/components/ai/DecisionCard.vue")['default']
    'AiDecisionDetailModal': typeof import("../layers/core/components/ai/DecisionDetailModal.vue")['default']
    'ApiEndpointCard': typeof import("../layers/core/components/api/ApiEndpointCard.vue")['default']
    'ApiEndpointDetailModal': typeof import("../layers/core/components/api/ApiEndpointDetailModal.vue")['default']
    'AutomationRuleCard': typeof import("../layers/core/components/automation/RuleCard.vue")['default']
    'BrandAssetCard': typeof import("../layers/core/components/brand/BrandAssetCard.vue")['default']
    'ChartsBarChart': typeof import("../layers/core/components/charts/BarChart.vue")['default']
    'ChartsLineChart': typeof import("../layers/core/components/charts/LineChart.vue")['default']
    'ChartsPieChart': typeof import("../layers/core/components/charts/PieChart.vue")['default']
    'ChartsRadialChart': typeof import("../layers/core/components/charts/RadialChart.vue")['default']
    'DashboardActivityCard': typeof import("../layers/core/components/dashboard/ActivityCard.vue")['default']
    'DashboardMetricCard': typeof import("../layers/core/components/dashboard/MetricCard.vue")['default']
    'DashboardModuleUsageCard': typeof import("../layers/core/components/dashboard/ModuleUsageCard.vue")['default']
    'DashboardNotificationsCard': typeof import("../layers/core/components/dashboard/NotificationsCard.vue")['default']
    'DashboardSystemHealthCard': typeof import("../layers/core/components/dashboard/SystemHealthCard.vue")['default']
    'DashboardUserActivityCard': typeof import("../layers/core/components/dashboard/UserActivityCard.vue")['default']
    'DatabaseConnectionBuilderForm': typeof import("../layers/core/components/database/ConnectionBuilderForm.vue")['default']
    'DatabaseConnectionPoolMonitor': typeof import("../layers/core/components/database/ConnectionPoolMonitor.vue")['default']
    'DatabaseConnectionStringForm': typeof import("../layers/core/components/database/ConnectionStringForm.vue")['default']
    'DatabaseExplorer': typeof import("../layers/core/components/database/DatabaseExplorer.vue")['default']
    'DatabaseHealth': typeof import("../layers/core/components/database/DatabaseHealth.vue")['default']
    'DatabaseSchema': typeof import("../layers/core/components/database/DatabaseSchema.vue")['default']
    'DatabaseTerminalWindow': typeof import("../layers/core/components/database/TerminalWindow.vue")['default']
    'DocumentsDocumentFilters': typeof import("../layers/core/components/documents/DocumentFilters.vue")['default']
    'DocumentsDocumentUploadModal': typeof import("../layers/core/components/documents/DocumentUploadModal.vue")['default']
    'EventsEventCard': typeof import("../layers/core/components/events/EventCard.vue")['default']
    'EventsEventDetailModal': typeof import("../layers/core/components/events/EventDetailModal.vue")['default']
    'HealthPerformanceMetricsCard': typeof import("../layers/core/components/health/PerformanceMetricsCard.vue")['default']
    'HealthResourceUsageCard': typeof import("../layers/core/components/health/ResourceUsageCard.vue")['default']
    'HealthServiceStatusCard': typeof import("../layers/core/components/health/ServiceStatusCard.vue")['default']
    'LegalDocumentCard': typeof import("../layers/core/components/legal/LegalDocumentCard.vue")['default']
    'MlDatasetCard': typeof import("../layers/core/components/ml/DatasetCard.vue")['default']
    'MlTrainingDataUploader': typeof import("../layers/core/components/ml/TrainingDataUploader.vue")['default']
    'MlTrainingFormatGuide': typeof import("../layers/core/components/ml/TrainingFormatGuide.vue")['default']
    'ModulesModuleCard': typeof import("../layers/core/components/modules/ModuleCard.vue")['default']
    'ModulesModuleDetailModal': typeof import("../layers/core/components/modules/ModuleDetailModal.vue")['default']
    'MonitoringAlertDetailsModal': typeof import("../layers/core/components/monitoring/AlertDetailsModal.vue")['default']
    'MonitoringAlertsListCard': typeof import("../layers/core/components/monitoring/AlertsListCard.vue")['default']
    'MonitoringAlertsSummaryCard': typeof import("../layers/core/components/monitoring/AlertsSummaryCard.vue")['default']
    'MonitoringApiPerformanceCard': typeof import("../layers/core/components/monitoring/ApiPerformanceCard.vue")['default']
    'MonitoringDatabaseResourcesCard': typeof import("../layers/core/components/monitoring/DatabaseResourcesCard.vue")['default']
    'MonitoringErrorDetailsModal': typeof import("../layers/core/components/monitoring/ErrorDetailsModal.vue")['default']
    'MonitoringErrorLogsCard': typeof import("../layers/core/components/monitoring/ErrorLogsCard.vue")['default']
    'MonitoringErrorSummaryCard': typeof import("../layers/core/components/monitoring/ErrorSummaryCard.vue")['default']
    'MonitoringPageLoadTimesCard': typeof import("../layers/core/components/monitoring/PageLoadTimesCard.vue")['default']
    'MonitoringPerformanceMetricsCard': typeof import("../layers/core/components/monitoring/PerformanceMetricsCard.vue")['default']
    'MonitoringResourceOverviewCard': typeof import("../layers/core/components/monitoring/ResourceOverviewCard.vue")['default']
    'MonitoringServerResourcesCard': typeof import("../layers/core/components/monitoring/ServerResourcesCard.vue")['default']
    'PoliciesPolicyCard': typeof import("../layers/core/components/policies/PolicyCard.vue")['default']
    'PoliciesPolicyCategoryFilter': typeof import("../layers/core/components/policies/PolicyCategoryFilter.vue")['default']
    'PoliciesPolicyDetailModal': typeof import("../layers/core/components/policies/PolicyDetailModal.vue")['default']
    'RulesCategoryCard': typeof import("../layers/core/components/rules/CategoryCard.vue")['default']
    'RulesRuleCard': typeof import("../layers/core/components/rules/RuleCard.vue")['default']
    'RulesRuleDetailModal': typeof import("../layers/core/components/rules/RuleDetailModal.vue")['default']
    'RulesRuleFilters': typeof import("../layers/core/components/rules/RuleFilters.vue")['default']
    'RulesTemplateCard': typeof import("../layers/core/components/rules/TemplateCard.vue")['default']
    'RulesTemplateDetailModal': typeof import("../layers/core/components/rules/TemplateDetailModal.vue")['default']
    'RulesValidationTester': typeof import("../layers/core/components/rules/ValidationTester.vue")['default']
    'SecurityAuditLogEntry': typeof import("../layers/core/components/security/AuditLogEntry.vue")['default']
    'SecurityAuditLogFilters': typeof import("../layers/core/components/security/AuditLogFilters.vue")['default']
    'SecurityEditRoleModal': typeof import("../layers/core/components/security/EditRoleModal.vue")['default']
    'SecurityEditUserRolesModal': typeof import("../layers/core/components/security/EditUserRolesModal.vue")['default']
    'SecurityRolePermissionsCard': typeof import("../layers/core/components/security/RolePermissionsCard.vue")['default']
    'SecurityUserRolesTable': typeof import("../layers/core/components/security/UserRolesTable.vue")['default']
    'SettingsAppearanceSettingsCard': typeof import("../layers/core/components/settings/AppearanceSettingsCard.vue")['default']
    'SettingsBackupHistoryCard': typeof import("../layers/core/components/settings/BackupHistoryCard.vue")['default']
    'SettingsBackupSettingsCard': typeof import("../layers/core/components/settings/BackupSettingsCard.vue")['default']
    'SettingsBackupStatusCard': typeof import("../layers/core/components/settings/BackupStatusCard.vue")['default']
    'SettingsDeleteConfirmationModal': typeof import("../layers/core/components/settings/DeleteConfirmationModal.vue")['default']
    'SettingsEnvironmentInfoCard': typeof import("../layers/core/components/settings/EnvironmentInfoCard.vue")['default']
    'SettingsEnvironmentVariablesCard': typeof import("../layers/core/components/settings/EnvironmentVariablesCard.vue")['default']
    'SettingsFormatSettingsCard': typeof import("../layers/core/components/settings/FormatSettingsCard.vue")['default']
    'SettingsGeneralSettingsCard': typeof import("../layers/core/components/settings/GeneralSettingsCard.vue")['default']
    'SettingsLanguageSettingsCard': typeof import("../layers/core/components/settings/LanguageSettingsCard.vue")['default']
    'SettingsPerformanceSettingsCard': typeof import("../layers/core/components/settings/PerformanceSettingsCard.vue")['default']
    'SettingsQuickActionsCard': typeof import("../layers/core/components/settings/QuickActionsCard.vue")['default']
    'SettingsRestoreConfirmationModal': typeof import("../layers/core/components/settings/RestoreConfirmationModal.vue")['default']
    'SettingsRestoreFromFileModal': typeof import("../layers/core/components/settings/RestoreFromFileModal.vue")['default']
    'SettingsRestoreSettingsCard': typeof import("../layers/core/components/settings/RestoreSettingsCard.vue")['default']
    'WebhooksWebhookCard': typeof import("../layers/core/components/webhooks/WebhookCard.vue")['default']
    'WebhooksWebhookDetailModal': typeof import("../layers/core/components/webhooks/WebhookDetailModal.vue")['default']
    'EmployeeFilters': typeof import("../layers/hr/components/EmployeeFilters.vue")['default']
    'EmployeeForm': typeof import("../layers/hr/components/EmployeeForm.vue")['default']
    'EmployeeList': typeof import("../layers/hr/components/EmployeeList.vue")['default']
    'HrDashboard': typeof import("../layers/hr/components/HrDashboard.vue")['default']
    'WorkerForm': typeof import("../layers/hr/components/WorkerForm.vue")['default']
    'AttendanceRecordsTab': typeof import("../layers/hr/components/attendance/AttendanceRecordsTab.vue")['default']
    'AttendanceReportsTab': typeof import("../layers/hr/components/attendance/AttendanceReportsTab.vue")['default']
    'AttendanceDailyAttendanceTab': typeof import("../layers/hr/components/attendance/DailyAttendanceTab.vue")['default']
    'BenefitsBenefitPlansTab': typeof import("../layers/hr/components/benefits/BenefitPlansTab.vue")['default']
    'BenefitsBenefitSettingsTab': typeof import("../layers/hr/components/benefits/BenefitSettingsTab.vue")['default']
    'BenefitsEmployeeBenefitsTab': typeof import("../layers/hr/components/benefits/EmployeeBenefitsTab.vue")['default']
    'BonusesAllBonusesTab': typeof import("../layers/hr/components/bonuses/AllBonusesTab.vue")['default']
    'BonusesBonusReportsTab': typeof import("../layers/hr/components/bonuses/BonusReportsTab.vue")['default']
    'BonusesEmployeeBonusesTab': typeof import("../layers/hr/components/bonuses/EmployeeBonusesTab.vue")['default']
    'CareerPathDetails': typeof import("../layers/hr/components/career/CareerPathDetails.vue")['default']
    'CareerPathFormModal': typeof import("../layers/hr/components/career/CareerPathFormModal.vue")['default']
    'CareerPathOverview': typeof import("../layers/hr/components/career/CareerPathOverview.vue")['default']
    'DepartmentsDepartmentCard': typeof import("../layers/hr/components/departments/DepartmentCard.vue")['default']
    'DocumentsCompanyPoliciesTab': typeof import("../layers/hr/components/documents/CompanyPoliciesTab.vue")['default']
    'DocumentsTab': typeof import("../layers/hr/components/documents/DocumentsTab.vue")['default']
    'DocumentsEmployeeDocumentsTab': typeof import("../layers/hr/components/documents/EmployeeDocumentsTab.vue")['default']
    'EmployeesDocumentUploadForm': typeof import("../layers/hr/components/employees/DocumentUploadForm.vue")['default']
    'EmployeesEmployeeDocuments': typeof import("../layers/hr/components/employees/EmployeeDocuments.vue")['default']
    'EmployeesEmployeeLeave': typeof import("../layers/hr/components/employees/EmployeeLeave.vue")['default']
    'EmployeesEmployeeOverview': typeof import("../layers/hr/components/employees/EmployeeOverview.vue")['default']
    'EmployeesEmployeePayroll': typeof import("../layers/hr/components/employees/EmployeePayroll.vue")['default']
    'EmployeesEmployeePerformance': typeof import("../layers/hr/components/employees/EmployeePerformance.vue")['default']
    'EmployeesEmployeeProfileCard': typeof import("../layers/hr/components/employees/EmployeeProfileCard.vue")['default']
    'LeaveBalancesTab': typeof import("../layers/hr/components/leave/LeaveBalancesTab.vue")['default']
    'LeaveCalendarTab': typeof import("../layers/hr/components/leave/LeaveCalendarTab.vue")['default']
    'LeaveRequestsTab': typeof import("../layers/hr/components/leave/LeaveRequestsTab.vue")['default']
    'LeaveSettingsTab': typeof import("../layers/hr/components/leave/LeaveSettingsTab.vue")['default']
    'PayrollRunTab': typeof import("../layers/hr/components/payroll/PayrollRunTab.vue")['default']
    'PayrollPayslipsTab': typeof import("../layers/hr/components/payroll/PayslipsTab.vue")['default']
    'PayrollTaxSettingsTab': typeof import("../layers/hr/components/payroll/TaxSettingsTab.vue")['default']
    'PerformanceEmployeePerformanceTab': typeof import("../layers/hr/components/performance/EmployeePerformanceTab.vue")['default']
    'PerformanceAnalyticsTab': typeof import("../layers/hr/components/performance/PerformanceAnalyticsTab.vue")['default']
    'PerformanceReviewsTab': typeof import("../layers/hr/components/performance/PerformanceReviewsTab.vue")['default']
    'ShiftsShiftAssignmentsTab': typeof import("../layers/hr/components/shifts/ShiftAssignmentsTab.vue")['default']
    'ShiftsShiftScheduleTab': typeof import("../layers/hr/components/shifts/ShiftScheduleTab.vue")['default']
    'ShiftsShiftTypesTab': typeof import("../layers/hr/components/shifts/ShiftTypesTab.vue")['default']
    'TrainingCertificationsTab': typeof import("../layers/hr/components/training/CertificationsTab.vue")['default']
    'TrainingEmployeeTrainingTab': typeof import("../layers/hr/components/training/EmployeeTrainingTab.vue")['default']
    'TrainingEnrollEmployeesModal': typeof import("../layers/hr/components/training/EnrollEmployeesModal.vue")['default']
    'TrainingDetailsModal': typeof import("../layers/hr/components/training/TrainingDetailsModal.vue")['default']
    'TrainingFormModal': typeof import("../layers/hr/components/training/TrainingFormModal.vue")['default']
    'TrainingProgramCard': typeof import("../layers/hr/components/training/TrainingProgramCard.vue")['default']
    'TrainingProgramsTab': typeof import("../layers/hr/components/training/TrainingProgramsTab.vue")['default']
    'ProductionDashboard': typeof import("../layers/production/components/ProductionDashboard.vue")['default']
    'DashboardTaskStatusChart': typeof import("../layers/production/components/dashboard/TaskStatusChart.vue")['default']
    'DocumentsUploadDocumentModal': typeof import("../layers/production/components/documents/UploadDocumentModal.vue")['default']
    'DocumentsViewDocumentModal': typeof import("../layers/production/components/documents/ViewDocumentModal.vue")['default']
    'QualityChecklistCard': typeof import("../layers/production/components/quality/ChecklistCard.vue")['default']
    'QualityChecklistDetailModal': typeof import("../layers/production/components/quality/ChecklistDetailModal.vue")['default']
    'QualityChecklistItem': typeof import("../layers/production/components/quality/ChecklistItem.vue")['default']
    'QualityNewChecklistModal': typeof import("../layers/production/components/quality/NewChecklistModal.vue")['default']
    'QualityPhotoCard': typeof import("../layers/production/components/quality/PhotoCard.vue")['default']
    'QualityPhotoDetailModal': typeof import("../layers/production/components/quality/PhotoDetailModal.vue")['default']
    'QualityUploadPhotoModal': typeof import("../layers/production/components/quality/UploadPhotoModal.vue")['default']
    'ReportsBarChart': typeof import("../layers/production/components/reports/BarChart.vue")['default']
    'ReportsLineChart': typeof import("../layers/production/components/reports/LineChart.vue")['default']
    'ReportsMetricCard': typeof import("../layers/production/components/reports/MetricCard.vue")['default']
    'ReportsPieChart': typeof import("../layers/production/components/reports/PieChart.vue")['default']
    'ReportsReportCard': typeof import("../layers/production/components/reports/ReportCard.vue")['default']
    'ResourcesEquipmentCard': typeof import("../layers/production/components/resources/EquipmentCard.vue")['default']
    'ResourcesEquipmentDetailModal': typeof import("../layers/production/components/resources/EquipmentDetailModal.vue")['default']
    'ResourcesMaterialCard': typeof import("../layers/production/components/resources/MaterialCard.vue")['default']
    'ResourcesMaterialDetailModal': typeof import("../layers/production/components/resources/MaterialDetailModal.vue")['default']
    'ResourcesNewEquipmentModal': typeof import("../layers/production/components/resources/NewEquipmentModal.vue")['default']
    'ResourcesNewMaterialModal': typeof import("../layers/production/components/resources/NewMaterialModal.vue")['default']
    'ResourcesNewWorkerModal': typeof import("../layers/production/components/resources/NewWorkerModal.vue")['default']
    'ResourcesWorkforceCard': typeof import("../layers/production/components/resources/WorkforceCard.vue")['default']
    'ResourcesWorkforceDetailModal': typeof import("../layers/production/components/resources/WorkforceDetailModal.vue")['default']
    'StatisticsProjectMetricsChart': typeof import("../layers/production/components/statistics/ProjectMetricsChart.vue")['default']
    'StatisticsProjectStatusChart': typeof import("../layers/production/components/statistics/ProjectStatusChart.vue")['default']
    'StatisticsTaskCompletionChart': typeof import("../layers/production/components/statistics/TaskCompletionChart.vue")['default']
    'TasksNewTaskModal': typeof import("../layers/production/components/tasks/NewTaskModal.vue")['default']
    'TasksTaskBoard': typeof import("../layers/production/components/tasks/TaskBoard.vue")['default']
    'TasksTaskCard': typeof import("../layers/production/components/tasks/TaskCard.vue")['default']
    'TasksTaskColumn': typeof import("../layers/production/components/tasks/TaskColumn.vue")['default']
    'TasksTaskDetailModal': typeof import("../layers/production/components/tasks/TaskDetailModal.vue")['default']
    'TimelineGanttChart': typeof import("../layers/production/components/timeline/GanttChart.vue")['default']
    'RecruitmentSidebarItem': typeof import("../layers/recruitment/components/RecruitmentSidebarItem.vue")['default']
    'AdminLanguageManager': typeof import("../layers/recruitment/components/admin/LanguageManager.vue")['default']
    'AdminTranslationsManager': typeof import("../layers/recruitment/components/admin/TranslationsManager.vue")['default']
    'FormsFormBuilderFieldEditor': typeof import("../layers/recruitment/components/forms/FormBuilderFieldEditor.vue")['default']
    'FormsFormBuilderFieldList': typeof import("../layers/recruitment/components/forms/FormBuilderFieldList.vue")['default']
    'FormsFormBuilderPreview': typeof import("../layers/recruitment/components/forms/FormBuilderPreview.vue")['default']
    'FormsFormBuilderPreviewField': typeof import("../layers/recruitment/components/forms/FormBuilderPreviewField.vue")['default']
    'FormsFormBuilderSettings': typeof import("../layers/recruitment/components/forms/FormBuilderSettings.vue")['default']
    'FormsFormBuilderSidebar': typeof import("../layers/recruitment/components/forms/FormBuilderSidebar.vue")['default']
    'FormsFormBuilderStepEditor': typeof import("../layers/recruitment/components/forms/FormBuilderStepEditor.vue")['default']
    'FormsPublicFormField': typeof import("../layers/recruitment/components/forms/PublicFormField.vue")['default']
    'WorkforceFormAdditionalInfoStep': typeof import("../layers/recruitment/components/workforce-form/AdditionalInfoStep.vue")['default']
    'WorkforceFormConfirmationStep': typeof import("../layers/recruitment/components/workforce-form/ConfirmationStep.vue")['default']
    'WorkforceFormIntroductionStep': typeof import("../layers/recruitment/components/workforce-form/IntroductionStep.vue")['default']
    'WorkforceFormLanguageSelector': typeof import("../layers/recruitment/components/workforce-form/LanguageSelector.vue")['default']
    'WorkforceFormPersonalInfoStep': typeof import("../layers/recruitment/components/workforce-form/PersonalInfoStep.vue")['default']
    'WorkforceFormProfessionalExperienceStep': typeof import("../layers/recruitment/components/workforce-form/ProfessionalExperienceStep.vue")['default']
    'WorkforceFormQualificationsStep': typeof import("../layers/recruitment/components/workforce-form/QualificationsStep.vue")['default']
    'WorkforceFormReferencesStep': typeof import("../layers/recruitment/components/workforce-form/ReferencesStep.vue")['default']
    'WorkforceFormSpecialtiesStep': typeof import("../layers/recruitment/components/workforce-form/SpecialtiesStep.vue")['default']
    'WorkforceAssignmentDetailsModal': typeof import("../layers/recruitment/components/workforce/AssignmentDetailsModal.vue")['default']
    'WorkforceAssignmentFilters': typeof import("../layers/recruitment/components/workforce/AssignmentFilters.vue")['default']
    'WorkforceAssignmentsPagination': typeof import("../layers/recruitment/components/workforce/AssignmentsPagination.vue")['default']
    'WorkforceJobDetailsModal': typeof import("../layers/recruitment/components/workforce/JobDetailsModal.vue")['default']
    'WorkforceJobFilters': typeof import("../layers/recruitment/components/workforce/JobFilters.vue")['default']
    'WorkforceJobFormModal': typeof import("../layers/recruitment/components/workforce/JobFormModal.vue")['default']
    'WorkforceJobsPagination': typeof import("../layers/recruitment/components/workforce/JobsPagination.vue")['default']
    'WorkforceMatchDetailsModal': typeof import("../layers/recruitment/components/workforce/MatchDetailsModal.vue")['default']
    'WorkforceMatchFilters': typeof import("../layers/recruitment/components/workforce/MatchFilters.vue")['default']
    'WorkforceMatchesPagination': typeof import("../layers/recruitment/components/workforce/MatchesPagination.vue")['default']
    'WorkforceWorkerDetailsModal': typeof import("../layers/recruitment/components/workforce/WorkerDetailsModal.vue")['default']
    'WorkforceWorkerFormModal': typeof import("../layers/recruitment/components/workforce/WorkerFormModal.vue")['default']
    'WorkforceWorkersPagination': typeof import("../layers/recruitment/components/workforce/WorkersPagination.vue")['default']
    'TairoSidebarBurger': typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarBurger.vue")['default']
    'TairoSidebarCircularMenu': typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarCircularMenu.vue")['default']
    'TairoSidebarLayout': typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarLayout.vue")['default']
    'TairoSidebarNavigationItem': typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarNavigationItem.vue")['default']
    'TairoSidebarNavigationPanel': typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarNavigationPanel.vue")['default']
    'TairoSidebarToolbar': typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarToolbar.vue")['default']
    'TairoSidebarTools': typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarTools.vue")['default']
    'TairoSubsidebar': typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebar.vue")['default']
    'TairoSubsidebarHeader': typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarHeader.vue")['default']
    'TairoSubsidebarMenu': typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenu.vue")['default']
    'TairoSubsidebarMenuCollapseLinks': typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuCollapseLinks.vue")['default']
    'TairoSubsidebarMenuDivider': typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuDivider.vue")['default']
    'TairoSubsidebarMenuLink': typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuLink.vue")['default']
    'LandingBenefits': typeof import("../layers/landing/components/LandingBenefits.vue")['default']
    'LandingContent': typeof import("../layers/landing/components/LandingContent.vue")['default']
    'LandingCta': typeof import("../layers/landing/components/LandingCta.vue")['default']
    'LandingCustomizer': typeof import("../layers/landing/components/LandingCustomizer.vue")['default']
    'LandingDemoLink': typeof import("../layers/landing/components/LandingDemoLink.vue")['default']
    'LandingDemos': typeof import("../layers/landing/components/LandingDemos.vue")['default']
    'LandingFeatures': typeof import("../layers/landing/components/LandingFeatures.vue")['default']
    'LandingFeaturesTile': typeof import("../layers/landing/components/LandingFeaturesTile.vue")['default']
    'LandingFooter': typeof import("../layers/landing/components/LandingFooter.vue")['default']
    'LandingHero': typeof import("../layers/landing/components/LandingHero.vue")['default']
    'LandingHeroMockup': typeof import("../layers/landing/components/LandingHeroMockup.vue")['default']
    'LandingLayers': typeof import("../layers/landing/components/LandingLayers.vue")['default']
    'LandingLayersBox': typeof import("../layers/landing/components/LandingLayersBox.vue")['default']
    'LandingLayout': typeof import("../layers/landing/components/LandingLayout.vue")['default']
    'LandingLayouts': typeof import("../layers/landing/components/LandingLayouts.vue")['default']
    'LandingNavbar': typeof import("../layers/landing/components/LandingNavbar.vue")['default']
    'MockupFollowersCompact': typeof import("../layers/landing/components/MockupFollowersCompact.vue")['default']
    'MockupInboxMessage': typeof import("../layers/landing/components/MockupInboxMessage.vue")['default']
    'MockupInfoBadges': typeof import("../layers/landing/components/MockupInfoBadges.vue")['default']
    'MockupProgressCircle': typeof import("../layers/landing/components/MockupProgressCircle.vue")['default']
    'MockupTeamSearchCompact': typeof import("../layers/landing/components/MockupTeamSearchCompact.vue")['default']
    'MockupVideoCompact': typeof import("../layers/landing/components/MockupVideoCompact.vue")['default']
    'SVGMorph': typeof import("../layers/landing/components/SVGMorph.vue")['default']
    'TairoIconnavCircularMenu': typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavCircularMenu.vue")['default']
    'TairoIconnavFooter': typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavFooter.vue")['default']
    'TairoIconnavLayout': typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavLayout.vue")['default']
    'TairoIconnavNavigation': typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavNavigation.vue")['default']
    'BaseAccordion': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAccordion.vue")['default']
    'BaseAvatar': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAvatar.vue")['default']
    'BaseAvatarGroup': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAvatarGroup.vue")['default']
    'BaseBreadcrumb': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseBreadcrumb.vue")['default']
    'BaseButton': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButton.vue")['default']
    'BaseButtonAction': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonAction.vue")['default']
    'BaseButtonClose': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonClose.vue")['default']
    'BaseButtonGroup': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonGroup.vue")['default']
    'BaseButtonIcon': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonIcon.vue")['default']
    'BaseCard': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseCard.vue")['default']
    'BaseDropdown': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdown.vue")['default']
    'BaseDropdownDivider': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdownDivider.vue")['default']
    'BaseDropdownItem': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdownItem.vue")['default']
    'BaseFocusLoop': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseFocusLoop.vue")['default']
    'BaseHeading': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseHeading.vue")['default']
    'BaseIconBox': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseIconBox.vue")['default']
    'BaseKbd': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseKbd.vue")['default']
    'BaseLink': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseLink.vue")['default']
    'BaseList': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseList.vue")['default']
    'BaseListItem': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseListItem.vue")['default']
    'BaseMessage': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseMessage.vue")['default']
    'BasePagination': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePagination.vue")['default']
    'BaseParagraph': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseParagraph.vue")['default']
    'BasePlaceholderPage': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePlaceholderPage.vue")['default']
    'BasePlaceload': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePlaceload.vue")['default']
    'BaseProgress': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProgress.vue")['default']
    'BaseProgressCircle': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProgressCircle.vue")['default']
    'BaseProse': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProse.vue")['default']
    'BaseSnack': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseSnack.vue")['default']
    'BaseTabSlider': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTabSlider.vue")['default']
    'BaseTabs': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTabs.vue")['default']
    'BaseTag': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTag.vue")['default']
    'BaseText': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseText.vue")['default']
    'BaseThemeSwitch': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseThemeSwitch.vue")['default']
    'BaseThemeToggle': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseThemeToggle.vue")['default']
    'IconCheck': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconCheck.vue")['default']
    'IconCheckCircle': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconCheckCircle.vue")['default']
    'IconChevronDown': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconChevronDown.vue")['default']
    'IconClose': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconClose.vue")['default']
    'IconIndeterminate': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconIndeterminate.vue")['default']
    'IconMinus': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconMinus.vue")['default']
    'IconMoon': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconMoon.vue")['default']
    'IconPlus': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconPlus.vue")['default']
    'IconSun': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconSun.vue")['default']
    'BaseAutocomplete': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseAutocomplete.vue")['default']
    'BaseAutocompleteItem': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseAutocompleteItem.vue")['default']
    'BaseCheckbox': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckbox.vue")['default']
    'BaseCheckboxAnimated': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckboxAnimated.vue")['default']
    'BaseCheckboxHeadless': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckboxHeadless.vue")['default']
    'BaseFullscreenDropfile': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseFullscreenDropfile.vue")['default']
    'BaseInput': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInput.vue")['default']
    'BaseInputFile': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputFile.vue")['default']
    'BaseInputFileHeadless': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputFileHeadless.vue")['default']
    'BaseInputHelpText': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputHelpText.vue")['default']
    'BaseInputNumber': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputNumber.vue")['default']
    'BaseListbox': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseListbox.vue")['default']
    'BaseListboxItem': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseListboxItem.vue")['default']
    'BaseRadio': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseRadio.vue")['default']
    'BaseRadioHeadless': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseRadioHeadless.vue")['default']
    'BaseSelect': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSelect.vue")['default']
    'BaseSwitchBall': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSwitchBall.vue")['default']
    'BaseSwitchThin': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSwitchThin.vue")['default']
    'BaseTextarea': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTextarea.vue")['default']
    'BaseTreeSelect': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTreeSelect.vue")['default']
    'BaseTreeSelectItem': typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTreeSelectItem.vue")['default']
    'NuxtWelcome': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
    'ClientOnly': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtRouteAnnouncer': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
    'NuxtPicture': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
    'IonAnimation': typeof import("../node_modules/.pnpm/@nuxtjs+ionic@0.16.0_@stencil+core@4.28.2_magicast@0.3.5_vue-router@4.5.0_vue@3.5.13_typescri_kvogt2gdziafhlhhzxvd6o57ta/node_modules/@nuxtjs/ionic/dist/runtime/components/IonAnimation.vue")['default']
    'IonAccordion': typeof import("@ionic/vue")['IonAccordion']
    'IonAccordionGroup': typeof import("@ionic/vue")['IonAccordionGroup']
    'IonActionSheet': typeof import("@ionic/vue")['IonActionSheet']
    'IonAlert': typeof import("@ionic/vue")['IonAlert']
    'IonApp': typeof import("@ionic/vue")['IonApp']
    'IonAvatar': typeof import("@ionic/vue")['IonAvatar']
    'IonBackButton': typeof import("@ionic/vue")['IonBackButton']
    'IonBackdrop': typeof import("@ionic/vue")['IonBackdrop']
    'IonBadge': typeof import("@ionic/vue")['IonBadge']
    'IonBreadcrumb': typeof import("@ionic/vue")['IonBreadcrumb']
    'IonBreadcrumbs': typeof import("@ionic/vue")['IonBreadcrumbs']
    'IonButton': typeof import("@ionic/vue")['IonButton']
    'IonButtons': typeof import("@ionic/vue")['IonButtons']
    'IonCard': typeof import("@ionic/vue")['IonCard']
    'IonCardContent': typeof import("@ionic/vue")['IonCardContent']
    'IonCardHeader': typeof import("@ionic/vue")['IonCardHeader']
    'IonCardSubtitle': typeof import("@ionic/vue")['IonCardSubtitle']
    'IonCardTitle': typeof import("@ionic/vue")['IonCardTitle']
    'IonCheckbox': typeof import("@ionic/vue")['IonCheckbox']
    'IonChip': typeof import("@ionic/vue")['IonChip']
    'IonCol': typeof import("@ionic/vue")['IonCol']
    'IonContent': typeof import("@ionic/vue")['IonContent']
    'IonDatetime': typeof import("@ionic/vue")['IonDatetime']
    'IonDatetimeButton': typeof import("@ionic/vue")['IonDatetimeButton']
    'IonFab': typeof import("@ionic/vue")['IonFab']
    'IonFabButton': typeof import("@ionic/vue")['IonFabButton']
    'IonFabList': typeof import("@ionic/vue")['IonFabList']
    'IonFooter': typeof import("@ionic/vue")['IonFooter']
    'IonGrid': typeof import("@ionic/vue")['IonGrid']
    'IonHeader': typeof import("@ionic/vue")['IonHeader']
    'IonIcon': typeof import("@ionic/vue")['IonIcon']
    'IonImg': typeof import("@ionic/vue")['IonImg']
    'IonInfiniteScroll': typeof import("@ionic/vue")['IonInfiniteScroll']
    'IonInfiniteScrollContent': typeof import("@ionic/vue")['IonInfiniteScrollContent']
    'IonInput': typeof import("@ionic/vue")['IonInput']
    'IonInputPasswordToggle': typeof import("@ionic/vue")['IonInputPasswordToggle']
    'IonItem': typeof import("@ionic/vue")['IonItem']
    'IonItemDivider': typeof import("@ionic/vue")['IonItemDivider']
    'IonItemGroup': typeof import("@ionic/vue")['IonItemGroup']
    'IonItemOption': typeof import("@ionic/vue")['IonItemOption']
    'IonItemOptions': typeof import("@ionic/vue")['IonItemOptions']
    'IonItemSliding': typeof import("@ionic/vue")['IonItemSliding']
    'IonLabel': typeof import("@ionic/vue")['IonLabel']
    'IonList': typeof import("@ionic/vue")['IonList']
    'IonListHeader': typeof import("@ionic/vue")['IonListHeader']
    'IonLoading': typeof import("@ionic/vue")['IonLoading']
    'IonMenu': typeof import("@ionic/vue")['IonMenu']
    'IonMenuButton': typeof import("@ionic/vue")['IonMenuButton']
    'IonMenuToggle': typeof import("@ionic/vue")['IonMenuToggle']
    'IonModal': typeof import("@ionic/vue")['IonModal']
    'IonNav': typeof import("@ionic/vue")['IonNav']
    'IonNavLink': typeof import("@ionic/vue")['IonNavLink']
    'IonNote': typeof import("@ionic/vue")['IonNote']
    'IonPage': typeof import("@ionic/vue")['IonPage']
    'IonPicker': typeof import("@ionic/vue")['IonPicker']
    'IonPickerColumn': typeof import("@ionic/vue")['IonPickerColumn']
    'IonPickerColumnOption': typeof import("@ionic/vue")['IonPickerColumnOption']
    'IonPickerLegacy': typeof import("@ionic/vue")['IonPickerLegacy']
    'IonPopover': typeof import("@ionic/vue")['IonPopover']
    'IonProgressBar': typeof import("@ionic/vue")['IonProgressBar']
    'IonRadio': typeof import("@ionic/vue")['IonRadio']
    'IonRadioGroup': typeof import("@ionic/vue")['IonRadioGroup']
    'IonRange': typeof import("@ionic/vue")['IonRange']
    'IonRefresher': typeof import("@ionic/vue")['IonRefresher']
    'IonRefresherContent': typeof import("@ionic/vue")['IonRefresherContent']
    'IonReorder': typeof import("@ionic/vue")['IonReorder']
    'IonReorderGroup': typeof import("@ionic/vue")['IonReorderGroup']
    'IonRippleEffect': typeof import("@ionic/vue")['IonRippleEffect']
    'IonRouterOutlet': typeof import("@ionic/vue")['IonRouterOutlet']
    'IonRow': typeof import("@ionic/vue")['IonRow']
    'IonSearchbar': typeof import("@ionic/vue")['IonSearchbar']
    'IonSegment': typeof import("@ionic/vue")['IonSegment']
    'IonSegmentButton': typeof import("@ionic/vue")['IonSegmentButton']
    'IonSegmentContent': typeof import("@ionic/vue")['IonSegmentContent']
    'IonSegmentView': typeof import("@ionic/vue")['IonSegmentView']
    'IonSelect': typeof import("@ionic/vue")['IonSelect']
    'IonSelectModal': typeof import("@ionic/vue")['IonSelectModal']
    'IonSelectOption': typeof import("@ionic/vue")['IonSelectOption']
    'IonSkeletonText': typeof import("@ionic/vue")['IonSkeletonText']
    'IonSpinner': typeof import("@ionic/vue")['IonSpinner']
    'IonSplitPane': typeof import("@ionic/vue")['IonSplitPane']
    'IonTab': typeof import("@ionic/vue")['IonTab']
    'IonTabs': typeof import("@ionic/vue")['IonTabs']
    'IonTabBar': typeof import("@ionic/vue")['IonTabBar']
    'IonTabButton': typeof import("@ionic/vue")['IonTabButton']
    'IonText': typeof import("@ionic/vue")['IonText']
    'IonTextarea': typeof import("@ionic/vue")['IonTextarea']
    'IonThumbnail': typeof import("@ionic/vue")['IonThumbnail']
    'IonTitle': typeof import("@ionic/vue")['IonTitle']
    'IonToast': typeof import("@ionic/vue")['IonToast']
    'IonToggle': typeof import("@ionic/vue")['IonToggle']
    'IonToolbar': typeof import("@ionic/vue")['IonToolbar']
    'NuxtLinkLocale': typeof import("../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
    'SwitchLocalePathLink': typeof import("../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
    'ColorScheme': typeof import("../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
    'Icon': typeof import("../node_modules/.pnpm/@nuxt+icon@1.11.0_magicast@0.3.5_vite@6.2.2_@types+node@22.14.0_jiti@2.4.2_lightningcss@1.29._4n4hvoiqlixjyr6n4h4n6fje7e/node_modules/@nuxt/icon/dist/runtime/components/index")['default']
    'NuxtPage': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': IslandComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
      'LazyAccountMenu': LazyComponent<typeof import("../components/global/AccountMenu.vue")['default']>
    'LazyAccountingSubsidebar': LazyComponent<typeof import("../components/global/AccountingSubsidebar.vue")['default']>
    'LazyAddContactButton': LazyComponent<typeof import("../components/global/AddContactButton.vue")['default']>
    'LazyAddContactForm': LazyComponent<typeof import("../components/global/AddContactForm.vue")['default']>
    'LazyAddContactModal': LazyComponent<typeof import("../components/global/AddContactModal.vue")['default']>
    'LazyAddMachineButton': LazyComponent<typeof import("../components/global/AddMachineButton.vue")['default']>
    'LazyAddMachineForm': LazyComponent<typeof import("../components/global/AddMachineForm.vue")['default']>
    'LazyAddMachineModal': LazyComponent<typeof import("../components/global/AddMachineModal.vue")['default']>
    'LazyAddMeetingButton': LazyComponent<typeof import("../components/global/AddMeetingButton.vue")['default']>
    'LazyAddMeetingForm': LazyComponent<typeof import("../components/global/AddMeetingForm.vue")['default']>
    'LazyAddMeetingModal': LazyComponent<typeof import("../components/global/AddMeetingModal.vue")['default']>
    'LazyAddUserButton': LazyComponent<typeof import("../components/global/AddUserButton.vue")['default']>
    'LazyAddUserForm': LazyComponent<typeof import("../components/global/AddUserForm.vue")['default']>
    'LazyAddUserModal': LazyComponent<typeof import("../components/global/AddUserModal.vue")['default']>
    'LazyAppLayoutSwitcher': LazyComponent<typeof import("../components/global/AppLayoutSwitcher.vue")['default']>
    'LazyAppSearch': LazyComponent<typeof import("../components/global/AppSearch.vue")['default']>
    'LazyAppSearchResult': LazyComponent<typeof import("../components/global/AppSearchResult.vue")['default']>
    'LazyBudgetSubsidebar': LazyComponent<typeof import("../components/global/BudgetSubsidebar.vue")['default']>
    'LazyCircularMenuActivity': LazyComponent<typeof import("../components/global/CircularMenuActivity.vue")['default']>
    'LazyCircularMenuLanguage': LazyComponent<typeof import("../components/global/CircularMenuLanguage.vue")['default']>
    'LazyCircularMenuNotifications': LazyComponent<typeof import("../components/global/CircularMenuNotifications.vue")['default']>
    'LazyCollapseNavigationFooter': LazyComponent<typeof import("../components/global/CollapseNavigationFooter.vue")['default']>
    'LazyCollapseNavigationHeader': LazyComponent<typeof import("../components/global/CollapseNavigationHeader.vue")['default']>
    'LazyCommunicationSubsidebar': LazyComponent<typeof import("../components/global/CommunicationSubsidebar.vue")['default']>
    'LazyCompaniesSubsidebar': LazyComponent<typeof import("../components/global/CompaniesSubsidebar.vue")['default']>
    'LazyCoreSubsidebar': LazyComponent<typeof import("../components/global/CoreSubsidebar.vue")['default']>
    'LazyCustomerVehiclesTable': LazyComponent<typeof import("../components/global/CustomerVehiclesTable.vue")['default']>
    'LazyFlexTableCell': LazyComponent<typeof import("../components/global/FlexTableCell.vue")['default']>
    'LazyFlexTableRow': LazyComponent<typeof import("../components/global/FlexTableRow.vue")['default']>
    'LazyFlexTableStart': LazyComponent<typeof import("../components/global/FlexTableStart.vue")['default']>
    'LazyHrSubsidebar': LazyComponent<typeof import("../components/global/HrSubsidebar.vue")['default']>
    'LazyLogo': LazyComponent<typeof import("../components/global/Logo.vue")['default']>
    'LazyLogoText': LazyComponent<typeof import("../components/global/LogoText.vue")['default']>
    'LazyModuleAccessInfo': LazyComponent<typeof import("../components/global/ModuleAccessInfo.vue")['default']>
    'LazyPanelLanguage': LazyComponent<typeof import("../components/global/PanelLanguage.vue")['default']>
    'LazyPanelSearch': LazyComponent<typeof import("../components/global/PanelSearch.vue")['default']>
    'LazyPanelTask': LazyComponent<typeof import("../components/global/PanelTask.vue")['default']>
    'LazyPhoneInput': LazyComponent<typeof import("../components/global/PhoneInput.vue")['default']>
    'LazyProductionSidebar': LazyComponent<typeof import("../components/global/ProductionSidebar.vue")['default']>
    'LazyProductionSubsidebar': LazyComponent<typeof import("../components/global/ProductionSubsidebar.vue")['default']>
    'LazyRecruitmentSidebar': LazyComponent<typeof import("../components/global/RecruitmentSidebar.vue")['default']>
    'LazySalesSubsidebar': LazyComponent<typeof import("../components/global/SalesSubsidebar.vue")['default']>
    'LazySubsidebar': LazyComponent<typeof import("../components/global/Subsidebar.vue")['default']>
    'LazySubsidebarClients': LazyComponent<typeof import("../components/global/SubsidebarClients.vue")['default']>
    'LazySubsidebarDashboards': LazyComponent<typeof import("../components/global/SubsidebarDashboards.vue")['default']>
    'LazySubsidebarHeader': LazyComponent<typeof import("../components/global/SubsidebarHeader.vue")['default']>
    'LazySubsidebarLayouts': LazyComponent<typeof import("../components/global/SubsidebarLayouts.vue")['default']>
    'LazySubsidebarMenu': LazyComponent<typeof import("../components/global/SubsidebarMenu.vue")['default']>
    'LazySubsidebarMenuCollapseLinks': LazyComponent<typeof import("../components/global/SubsidebarMenuCollapseLinks.vue")['default']>
    'LazySubsidebarMenuDivider': LazyComponent<typeof import("../components/global/SubsidebarMenuDivider.vue")['default']>
    'LazySubsidebarMenuLink': LazyComponent<typeof import("../components/global/SubsidebarMenuLink.vue")['default']>
    'LazyTeamListCompact': LazyComponent<typeof import("../components/global/TeamListCompact.vue")['default']>
    'LazyThemeToggle': LazyComponent<typeof import("../components/global/ThemeToggle.vue")['default']>
    'LazyTimeManagementSubsidebar': LazyComponent<typeof import("../components/global/TimeManagementSubsidebar.vue")['default']>
    'LazyToaster': LazyComponent<typeof import("../components/global/Toaster.vue")['default']>
    'LazyTocAnchor': LazyComponent<typeof import("../components/global/TocAnchor.vue")['default']>
    'LazyToolbarAccountMenu': LazyComponent<typeof import("../components/global/ToolbarAccountMenu.vue")['default']>
    'LazyToolbarActivity': LazyComponent<typeof import("../components/global/ToolbarActivity.vue")['default']>
    'LazyToolbarCustomize': LazyComponent<typeof import("../components/global/ToolbarCustomize.vue")['default']>
    'LazyToolbarLanguage': LazyComponent<typeof import("../components/global/ToolbarLanguage.vue")['default']>
    'LazyToolbarNotifications': LazyComponent<typeof import("../components/global/ToolbarNotifications.vue")['default']>
    'LazyToolbarSearch': LazyComponent<typeof import("../components/global/ToolbarSearch.vue")['default']>
    'LazyAccessControl': LazyComponent<typeof import("../components/AccessControl.vue")['default']>
    'LazyAddonApexcharts': LazyComponent<typeof import("../components/AddonApexcharts.vue")['default']>
    'LazyAddonInputPassword': LazyComponent<typeof import("../components/AddonInputPassword.vue")['default']>
    'LazyAdminsPanelAccount': LazyComponent<typeof import("../components/Admins/PanelAccount.vue")['default']>
    'LazyAdminsPanelActivity': LazyComponent<typeof import("../components/Admins/PanelActivity.vue")['default']>
    'LazyAdminsPanelCard': LazyComponent<typeof import("../components/Admins/PanelCard.vue")['default']>
    'LazyAdminsPanelInvest': LazyComponent<typeof import("../components/Admins/PanelInvest.vue")['default']>
    'LazyAdminsPanels': LazyComponent<typeof import("../components/Admins/Panels.vue")['default']>
    'LazyAdminsSidebarBurger': LazyComponent<typeof import("../components/Admins/SidebarBurger.vue")['default']>
    'LazyAdminsSidebarCircularMenu': LazyComponent<typeof import("../components/Admins/SidebarCircularMenu.vue")['default']>
    'LazyAdminsSidebarLayout': LazyComponent<typeof import("../components/Admins/SidebarLayout.vue")['default']>
    'LazyAdminsSidebarNavigation': LazyComponent<typeof import("../components/Admins/SidebarNavigation.vue")['default']>
    'LazyAdminsSidebarNavigationItem': LazyComponent<typeof import("../components/Admins/SidebarNavigationItem.vue")['default']>
    'LazyAdminsSidebarNavigationPanel': LazyComponent<typeof import("../components/Admins/SidebarNavigationPanel.vue")['default']>
    'LazyAdminsSidebarToolbar': LazyComponent<typeof import("../components/Admins/SidebarToolbar.vue")['default']>
    'LazyAdminsSidebarTools': LazyComponent<typeof import("../components/Admins/SidebarTools.vue")['default']>
    'LazyAdminsToolbarNotifications': LazyComponent<typeof import("../components/Admins/ToolbarNotifications.vue")['default']>
    'LazyAdminsTopnavWorkspaceDropdown': LazyComponent<typeof import("../components/Admins/TopnavWorkspaceDropdown.vue")['default']>
    'LazyAiAssistant': LazyComponent<typeof import("../components/AiAssistant.vue")['default']>
    'LazyAiAssistantButton': LazyComponent<typeof import("../components/AiAssistantButton.vue")['default']>
    'LazyCalendarWidget': LazyComponent<typeof import("../components/CalendarWidget.vue")['default']>
    'LazyDatePicker': LazyComponent<typeof import("../components/DatePicker.vue")['default']>
    'LazyDemoAccountMenu': LazyComponent<typeof import("../components/DemoAccountMenu.vue")['default']>
    'LazyDemoCalendarEvent': LazyComponent<typeof import("../components/DemoCalendarEvent.vue")['default']>
    'LazyDemoCalendarEventPending': LazyComponent<typeof import("../components/DemoCalendarEventPending.vue")['default']>
    'LazyTairoSidebarNavigation': LazyComponent<typeof import("../components/TairoSidebarNavigation.vue")['default']>
    'LazyUsersAccountMenu': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersPanelAccount': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersPanelActivity': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersPanelCard': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersPanelInvest': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersPanels': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersSidebarBurger': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersSidebarCircularMenu': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersSidebarLayout': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersSidebarNavigation': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersSidebarNavigationItem': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersSidebarNavigationPanel': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersSidebarToolbar': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersSidebarTools': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersToolbarAccountMenu': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyUsersTopnavWorkspaceDropdown': LazyComponent<typeof import("../components/Users/<USER>")['default']>
    'LazyAuthEmailVerification': LazyComponent<typeof import("../components/auth/EmailVerification.vue")['default']>
    'LazyAuthPaymentForm': LazyComponent<typeof import("../components/auth/PaymentForm.vue")['default']>
    'LazyAuthRegistrationForm': LazyComponent<typeof import("../components/auth/RegistrationForm.vue")['default']>
    'LazyAuthRegistrationSuccess': LazyComponent<typeof import("../components/auth/RegistrationSuccess.vue")['default']>
    'LazyAuthRoleSelection': LazyComponent<typeof import("../components/auth/RoleSelection.vue")['default']>
    'LazyAuthStripePaymentForm': LazyComponent<typeof import("../components/auth/StripePaymentForm.vue")['default']>
    'LazyAuthSubscriptionPlanSelection': LazyComponent<typeof import("../components/auth/SubscriptionPlanSelection.vue")['default']>
    'LazyBaseLoader': LazyComponent<typeof import("../components/base/BaseLoader.vue")['default']>
    'LazyDemoChartArea': LazyComponent<typeof import("../components/demo-chart/DemoChartArea.vue")['default']>
    'LazyDemoChartAreaBalance': LazyComponent<typeof import("../components/demo-chart/DemoChartAreaBalance.vue")['default']>
    'LazyDemoChartAreaMulti': LazyComponent<typeof import("../components/demo-chart/DemoChartAreaMulti.vue")['default']>
    'LazyDemoChartAreaStats': LazyComponent<typeof import("../components/demo-chart/DemoChartAreaStats.vue")['default']>
    'LazyDemoChartBar': LazyComponent<typeof import("../components/demo-chart/DemoChartBar.vue")['default']>
    'LazyDemoChartBarHorizontal': LazyComponent<typeof import("../components/demo-chart/DemoChartBarHorizontal.vue")['default']>
    'LazyDemoChartBarHorizontalMulti': LazyComponent<typeof import("../components/demo-chart/DemoChartBarHorizontalMulti.vue")['default']>
    'LazyDemoChartBarMulti': LazyComponent<typeof import("../components/demo-chart/DemoChartBarMulti.vue")['default']>
    'LazyDemoChartBarMultiIncome': LazyComponent<typeof import("../components/demo-chart/DemoChartBarMultiIncome.vue")['default']>
    'LazyDemoChartBarRange': LazyComponent<typeof import("../components/demo-chart/DemoChartBarRange.vue")['default']>
    'LazyDemoChartBarSocialChannels': LazyComponent<typeof import("../components/demo-chart/DemoChartBarSocialChannels.vue")['default']>
    'LazyDemoChartBarStacked': LazyComponent<typeof import("../components/demo-chart/DemoChartBarStacked.vue")['default']>
    'LazyDemoChartBubble': LazyComponent<typeof import("../components/demo-chart/DemoChartBubble.vue")['default']>
    'LazyDemoChartDonut': LazyComponent<typeof import("../components/demo-chart/DemoChartDonut.vue")['default']>
    'LazyDemoChartDonutExpenses': LazyComponent<typeof import("../components/demo-chart/DemoChartDonutExpenses.vue")['default']>
    'LazyDemoChartLine': LazyComponent<typeof import("../components/demo-chart/DemoChartLine.vue")['default']>
    'LazyDemoChartLineMulti': LazyComponent<typeof import("../components/demo-chart/DemoChartLineMulti.vue")['default']>
    'LazyDemoChartLineMultiAlt': LazyComponent<typeof import("../components/demo-chart/DemoChartLineMultiAlt.vue")['default']>
    'LazyDemoChartLineStep': LazyComponent<typeof import("../components/demo-chart/DemoChartLineStep.vue")['default']>
    'LazyDemoChartPie': LazyComponent<typeof import("../components/demo-chart/DemoChartPie.vue")['default']>
    'LazyDemoChartRadar': LazyComponent<typeof import("../components/demo-chart/DemoChartRadar.vue")['default']>
    'LazyDemoChartRadial': LazyComponent<typeof import("../components/demo-chart/DemoChartRadial.vue")['default']>
    'LazyDemoChartRadialGauge': LazyComponent<typeof import("../components/demo-chart/DemoChartRadialGauge.vue")['default']>
    'LazyDemoChartRadialGaugeAlt': LazyComponent<typeof import("../components/demo-chart/DemoChartRadialGaugeAlt.vue")['default']>
    'LazyDemoChartRadialMulti': LazyComponent<typeof import("../components/demo-chart/DemoChartRadialMulti.vue")['default']>
    'LazyDemoChartScatter': LazyComponent<typeof import("../components/demo-chart/DemoChartScatter.vue")['default']>
    'LazyDemoChartTimeline': LazyComponent<typeof import("../components/demo-chart/DemoChartTimeline.vue")['default']>
    'LazyExamplesComponentAccessExample': LazyComponent<typeof import("../components/examples/ComponentAccessExample.vue")['default']>
    'LazyTairoLogo': LazyComponent<typeof import("../layers/tairo/components/global/TairoLogo.vue")['default']>
    'LazyTairoLogoText': LazyComponent<typeof import("../layers/tairo/components/global/TairoLogoText.vue")['default']>
    'LazyTairoToaster': LazyComponent<typeof import("../layers/tairo/components/global/TairoToaster.vue")['default']>
    'LazyTairoTocAnchor': LazyComponent<typeof import("../layers/tairo/components/global/TairoTocAnchor.vue")['default']>
    'LazyTairoCheckAnimated': LazyComponent<typeof import("../layers/tairo/components/TairoCheckAnimated.vue")['default']>
    'LazyTairoContentWrapper': LazyComponent<typeof import("../layers/tairo/components/TairoContentWrapper.vue")['default']>
    'LazyTairoContentWrapperTabbed': LazyComponent<typeof import("../layers/tairo/components/TairoContentWrapperTabbed.vue")['default']>
    'LazyTairoError': LazyComponent<typeof import("../layers/tairo/components/TairoError.vue")['default']>
    'LazyTairoFlexTable': LazyComponent<typeof import("../layers/tairo/components/TairoFlexTable.vue")['default']>
    'LazyTairoFlexTableCell': LazyComponent<typeof import("../layers/tairo/components/TairoFlexTableCell.vue")['default']>
    'LazyTairoFlexTableHeading': LazyComponent<typeof import("../layers/tairo/components/TairoFlexTableHeading.vue")['default']>
    'LazyTairoFlexTableRow': LazyComponent<typeof import("../layers/tairo/components/TairoFlexTableRow.vue")['default']>
    'LazyTairoFormGroup': LazyComponent<typeof import("../layers/tairo/components/TairoFormGroup.vue")['default']>
    'LazyTairoFormSave': LazyComponent<typeof import("../layers/tairo/components/TairoFormSave.vue")['default']>
    'LazyTairoImageZoom': LazyComponent<typeof import("../layers/tairo/components/TairoImageZoom.vue")['default']>
    'LazyTairoModal': LazyComponent<typeof import("../layers/tairo/components/TairoModal.vue")['default']>
    'LazyTairoPanels': LazyComponent<typeof import("../layers/tairo/components/TairoPanels.vue")['default']>
    'LazyTairoPasswordStrength': LazyComponent<typeof import("../layers/tairo/components/TairoPasswordStrength.vue")['default']>
    'LazyTairoPopover': LazyComponent<typeof import("../layers/tairo/components/TairoPopover.vue")['default']>
    'LazyTairoPopoverContentDual': LazyComponent<typeof import("../layers/tairo/components/TairoPopoverContentDual.vue")['default']>
    'LazyTairoPopoverContentHelp': LazyComponent<typeof import("../layers/tairo/components/TairoPopoverContentHelp.vue")['default']>
    'LazyTairoPopoverContentMedia': LazyComponent<typeof import("../layers/tairo/components/TairoPopoverContentMedia.vue")['default']>
    'LazyTairoSidebarLayoutConfig': LazyComponent<typeof import("../layers/tairo/components/TairoSidebarLayout.config")['default']>
    'LazyTairoTable': LazyComponent<typeof import("../layers/tairo/components/TairoTable.vue")['default']>
    'LazyTairoTableCell': LazyComponent<typeof import("../layers/tairo/components/TairoTableCell.vue")['default']>
    'LazyTairoTableHeading': LazyComponent<typeof import("../layers/tairo/components/TairoTableHeading.vue")['default']>
    'LazyTairoTableRow': LazyComponent<typeof import("../layers/tairo/components/TairoTableRow.vue")['default']>
    'LazyTairoToc': LazyComponent<typeof import("../layers/tairo/components/TairoToc.vue")['default']>
    'LazyTairoWelcome': LazyComponent<typeof import("../layers/tairo/components/TairoWelcome.vue")['default']>
    'LazyAccountingDashboard': LazyComponent<typeof import("../layers/accounting/components/AccountingDashboard.vue")['default']>
    'LazyJournalEntryForm': LazyComponent<typeof import("../layers/accounting/components/JournalEntryForm.vue")['default']>
    'LazyBudgetChartsSection': LazyComponent<typeof import("../layers/budget/components/BudgetChartsSection.vue")['default']>
    'LazyBudgetDashboard': LazyComponent<typeof import("../layers/budget/components/BudgetDashboard.vue")['default']>
    'LazyBudgetIncomeForm': LazyComponent<typeof import("../layers/budget/components/BudgetIncomeForm.vue")['default']>
    'LazyExpensesSection': LazyComponent<typeof import("../layers/budget/components/ExpensesSection.vue")['default']>
    'LazyGlobalFooter': LazyComponent<typeof import("../layers/budget/components/GlobalFooter.vue")['default']>
    'LazyGlobalHeader': LazyComponent<typeof import("../layers/budget/components/GlobalHeader.vue")['default']>
    'LazyIncomeSection': LazyComponent<typeof import("../layers/budget/components/IncomeSection.vue")['default']>
    'LazyChartsBudgetChartsSection': LazyComponent<typeof import("../layers/budget/components/charts/BudgetChartsSection.vue")['default']>
    'LazyDashboardBudgetGoalsCard': LazyComponent<typeof import("../layers/budget/components/dashboard/BudgetGoalsCard.vue")['default']>
    'LazyDashboardBudgetSummaryCard': LazyComponent<typeof import("../layers/budget/components/dashboard/BudgetSummaryCard.vue")['default']>
    'LazyDashboardBudgetTrendsChart': LazyComponent<typeof import("../layers/budget/components/dashboard/BudgetTrendsChart.vue")['default']>
    'LazyDashboardBudgetTypeSelector': LazyComponent<typeof import("../layers/budget/components/dashboard/BudgetTypeSelector.vue")['default']>
    'LazyDashboardCategoryBreakdownCard': LazyComponent<typeof import("../layers/budget/components/dashboard/CategoryBreakdownCard.vue")['default']>
    'LazyDashboardRecentTransactionsCard': LazyComponent<typeof import("../layers/budget/components/dashboard/RecentTransactionsCard.vue")['default']>
    'LazyExpensesMultipleExpenses': LazyComponent<typeof import("../layers/budget/components/expenses/MultipleExpenses.vue")['default']>
    'LazyExpensesOneTimeExpenses': LazyComponent<typeof import("../layers/budget/components/expenses/OneTimeExpenses.vue")['default']>
    'LazyExpensesRepeatedExpenses': LazyComponent<typeof import("../layers/budget/components/expenses/RepeatedExpenses.vue")['default']>
    'LazyForecastingCategories': LazyComponent<typeof import("../layers/budget/components/forecasting/ForecastingCategories.vue")['default']>
    'LazyForecastingChart': LazyComponent<typeof import("../layers/budget/components/forecasting/ForecastingChart.vue")['default']>
    'LazyForecastingControls': LazyComponent<typeof import("../layers/budget/components/forecasting/ForecastingControls.vue")['default']>
    'LazyForecastingScenarios': LazyComponent<typeof import("../layers/budget/components/forecasting/ForecastingScenarios.vue")['default']>
    'LazyForecastingSummary': LazyComponent<typeof import("../layers/budget/components/forecasting/ForecastingSummary.vue")['default']>
    'LazyIncomesMultipleIncomes': LazyComponent<typeof import("../layers/budget/components/incomes/MultipleIncomes.vue")['default']>
    'LazyIncomesOneTimeIncomes': LazyComponent<typeof import("../layers/budget/components/incomes/OneTimeIncomes.vue")['default']>
    'LazyIncomesRepeatedIncomes': LazyComponent<typeof import("../layers/budget/components/incomes/RepeatedIncomes.vue")['default']>
    'LazyPlannerBudgetComparisonCard': LazyComponent<typeof import("../layers/budget/components/planner/BudgetComparisonCard.vue")['default']>
    'LazyPlannerMonthlyBudgetCard': LazyComponent<typeof import("../layers/budget/components/planner/MonthlyBudgetCard.vue")['default']>
    'LazyQuickBudgetCategoryBreakdown': LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetCategoryBreakdown.vue")['default']>
    'LazyQuickBudgetEntryForm': LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetEntryForm.vue")['default']>
    'LazyQuickBudgetExpensesList': LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetExpensesList.vue")['default']>
    'LazyQuickBudgetGoals': LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetGoals.vue")['default']>
    'LazyQuickBudgetIncomeList': LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetIncomeList.vue")['default']>
    'LazyQuickBudgetSummaryCard': LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetSummaryCard.vue")['default']>
    'LazyQuickBudgetTransactionsList': LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetTransactionsList.vue")['default']>
    'LazyStatsSection': LazyComponent<typeof import("../layers/budget/components/stats/StatsSection.vue")['default']>
    'LazyTransactionsListCard': LazyComponent<typeof import("../layers/budget/components/transactions/TransactionsListCard.vue")['default']>
    'LazyCompaniesDashboard': LazyComponent<typeof import("../layers/companies/components/CompaniesDashboard.vue")['default']>
    'LazyCompanyDashboard': LazyComponent<typeof import("../layers/companies/components/CompanyDashboard.vue")['default']>
    'LazyCompanyForm': LazyComponent<typeof import("../layers/companies/components/CompanyForm.vue")['default']>
    'LazyClientsClientCard': LazyComponent<typeof import("../layers/companies/components/clients/ClientCard.vue")['default']>
    'LazyClientsTable': LazyComponent<typeof import("../layers/companies/components/clients/ClientsTable.vue")['default']>
    'LazyComplianceAudits': LazyComponent<typeof import("../layers/companies/components/compliance/ComplianceAudits.vue")['default']>
    'LazyComplianceOverview': LazyComponent<typeof import("../layers/companies/components/compliance/ComplianceOverview.vue")['default']>
    'LazyComplianceRequirements': LazyComponent<typeof import("../layers/companies/components/compliance/ComplianceRequirements.vue")['default']>
    'LazyDocumentsDocumentCard': LazyComponent<typeof import("../layers/companies/components/documents/DocumentCard.vue")['default']>
    'LazyRiskMitigationPlans': LazyComponent<typeof import("../layers/companies/components/risk/MitigationPlans.vue")['default']>
    'LazyRiskDashboard': LazyComponent<typeof import("../layers/companies/components/risk/RiskDashboard.vue")['default']>
    'LazyRiskRegister': LazyComponent<typeof import("../layers/companies/components/risk/RiskRegister.vue")['default']>
    'LazySettingsGeneralSettings': LazyComponent<typeof import("../layers/companies/components/settings/GeneralSettings.vue")['default']>
    'LazySettingsIntegrationSettings': LazyComponent<typeof import("../layers/companies/components/settings/IntegrationSettings.vue")['default']>
    'LazySettingsNotificationSettings': LazyComponent<typeof import("../layers/companies/components/settings/NotificationSettings.vue")['default']>
    'LazySettingsPermissionSettings': LazyComponent<typeof import("../layers/companies/components/settings/PermissionSettings.vue")['default']>
    'LazySuppliersSupplierCard': LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierCard.vue")['default']>
    'LazySuppliersSupplierDocuments': LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierDocuments.vue")['default']>
    'LazySuppliersSupplierOrders': LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierOrders.vue")['default']>
    'LazySuppliersSupplierOverview': LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierOverview.vue")['default']>
    'LazySuppliersSupplierPerformance': LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierPerformance.vue")['default']>
    'LazySuppliersSupplierPerformanceMetrics': LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierPerformanceMetrics.vue")['default']>
    'LazySuppliersSupplierTransactions': LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierTransactions.vue")['default']>
    'LazySuppliersTable': LazyComponent<typeof import("../layers/companies/components/suppliers/SuppliersTable.vue")['default']>
    'LazyWorkforceCapacityPlanning': LazyComponent<typeof import("../layers/companies/components/workforce/CapacityPlanning.vue")['default']>
    'LazyWorkforceSkillsGapAnalysis': LazyComponent<typeof import("../layers/companies/components/workforce/SkillsGapAnalysis.vue")['default']>
    'LazyWorkforceOverview': LazyComponent<typeof import("../layers/companies/components/workforce/WorkforceOverview.vue")['default']>
    'LazyBaseLayerPage': LazyComponent<typeof import("../layers/core/components/BaseLayerPage.vue")['default']>
    'LazyBusinessRuleModal': LazyComponent<typeof import("../layers/core/components/BusinessRuleModal.vue")['default']>
    'LazyCoreDashboard': LazyComponent<typeof import("../layers/core/components/CoreDashboard.vue")['default']>
    'LazyActivityFilters': LazyComponent<typeof import("../layers/core/components/activity/ActivityFilters.vue")['default']>
    'LazyActivityLogDetails': LazyComponent<typeof import("../layers/core/components/activity/ActivityLogDetails.vue")['default']>
    'LazyActivityLogItem': LazyComponent<typeof import("../layers/core/components/activity/ActivityLogItem.vue")['default']>
    'LazyActivityLogList': LazyComponent<typeof import("../layers/core/components/activity/ActivityLogList.vue")['default']>
    'LazyAiDecisionCard': LazyComponent<typeof import("../layers/core/components/ai/DecisionCard.vue")['default']>
    'LazyAiDecisionDetailModal': LazyComponent<typeof import("../layers/core/components/ai/DecisionDetailModal.vue")['default']>
    'LazyApiEndpointCard': LazyComponent<typeof import("../layers/core/components/api/ApiEndpointCard.vue")['default']>
    'LazyApiEndpointDetailModal': LazyComponent<typeof import("../layers/core/components/api/ApiEndpointDetailModal.vue")['default']>
    'LazyAutomationRuleCard': LazyComponent<typeof import("../layers/core/components/automation/RuleCard.vue")['default']>
    'LazyBrandAssetCard': LazyComponent<typeof import("../layers/core/components/brand/BrandAssetCard.vue")['default']>
    'LazyChartsBarChart': LazyComponent<typeof import("../layers/core/components/charts/BarChart.vue")['default']>
    'LazyChartsLineChart': LazyComponent<typeof import("../layers/core/components/charts/LineChart.vue")['default']>
    'LazyChartsPieChart': LazyComponent<typeof import("../layers/core/components/charts/PieChart.vue")['default']>
    'LazyChartsRadialChart': LazyComponent<typeof import("../layers/core/components/charts/RadialChart.vue")['default']>
    'LazyDashboardActivityCard': LazyComponent<typeof import("../layers/core/components/dashboard/ActivityCard.vue")['default']>
    'LazyDashboardMetricCard': LazyComponent<typeof import("../layers/core/components/dashboard/MetricCard.vue")['default']>
    'LazyDashboardModuleUsageCard': LazyComponent<typeof import("../layers/core/components/dashboard/ModuleUsageCard.vue")['default']>
    'LazyDashboardNotificationsCard': LazyComponent<typeof import("../layers/core/components/dashboard/NotificationsCard.vue")['default']>
    'LazyDashboardSystemHealthCard': LazyComponent<typeof import("../layers/core/components/dashboard/SystemHealthCard.vue")['default']>
    'LazyDashboardUserActivityCard': LazyComponent<typeof import("../layers/core/components/dashboard/UserActivityCard.vue")['default']>
    'LazyDatabaseConnectionBuilderForm': LazyComponent<typeof import("../layers/core/components/database/ConnectionBuilderForm.vue")['default']>
    'LazyDatabaseConnectionPoolMonitor': LazyComponent<typeof import("../layers/core/components/database/ConnectionPoolMonitor.vue")['default']>
    'LazyDatabaseConnectionStringForm': LazyComponent<typeof import("../layers/core/components/database/ConnectionStringForm.vue")['default']>
    'LazyDatabaseExplorer': LazyComponent<typeof import("../layers/core/components/database/DatabaseExplorer.vue")['default']>
    'LazyDatabaseHealth': LazyComponent<typeof import("../layers/core/components/database/DatabaseHealth.vue")['default']>
    'LazyDatabaseSchema': LazyComponent<typeof import("../layers/core/components/database/DatabaseSchema.vue")['default']>
    'LazyDatabaseTerminalWindow': LazyComponent<typeof import("../layers/core/components/database/TerminalWindow.vue")['default']>
    'LazyDocumentsDocumentFilters': LazyComponent<typeof import("../layers/core/components/documents/DocumentFilters.vue")['default']>
    'LazyDocumentsDocumentUploadModal': LazyComponent<typeof import("../layers/core/components/documents/DocumentUploadModal.vue")['default']>
    'LazyEventsEventCard': LazyComponent<typeof import("../layers/core/components/events/EventCard.vue")['default']>
    'LazyEventsEventDetailModal': LazyComponent<typeof import("../layers/core/components/events/EventDetailModal.vue")['default']>
    'LazyHealthPerformanceMetricsCard': LazyComponent<typeof import("../layers/core/components/health/PerformanceMetricsCard.vue")['default']>
    'LazyHealthResourceUsageCard': LazyComponent<typeof import("../layers/core/components/health/ResourceUsageCard.vue")['default']>
    'LazyHealthServiceStatusCard': LazyComponent<typeof import("../layers/core/components/health/ServiceStatusCard.vue")['default']>
    'LazyLegalDocumentCard': LazyComponent<typeof import("../layers/core/components/legal/LegalDocumentCard.vue")['default']>
    'LazyMlDatasetCard': LazyComponent<typeof import("../layers/core/components/ml/DatasetCard.vue")['default']>
    'LazyMlTrainingDataUploader': LazyComponent<typeof import("../layers/core/components/ml/TrainingDataUploader.vue")['default']>
    'LazyMlTrainingFormatGuide': LazyComponent<typeof import("../layers/core/components/ml/TrainingFormatGuide.vue")['default']>
    'LazyModulesModuleCard': LazyComponent<typeof import("../layers/core/components/modules/ModuleCard.vue")['default']>
    'LazyModulesModuleDetailModal': LazyComponent<typeof import("../layers/core/components/modules/ModuleDetailModal.vue")['default']>
    'LazyMonitoringAlertDetailsModal': LazyComponent<typeof import("../layers/core/components/monitoring/AlertDetailsModal.vue")['default']>
    'LazyMonitoringAlertsListCard': LazyComponent<typeof import("../layers/core/components/monitoring/AlertsListCard.vue")['default']>
    'LazyMonitoringAlertsSummaryCard': LazyComponent<typeof import("../layers/core/components/monitoring/AlertsSummaryCard.vue")['default']>
    'LazyMonitoringApiPerformanceCard': LazyComponent<typeof import("../layers/core/components/monitoring/ApiPerformanceCard.vue")['default']>
    'LazyMonitoringDatabaseResourcesCard': LazyComponent<typeof import("../layers/core/components/monitoring/DatabaseResourcesCard.vue")['default']>
    'LazyMonitoringErrorDetailsModal': LazyComponent<typeof import("../layers/core/components/monitoring/ErrorDetailsModal.vue")['default']>
    'LazyMonitoringErrorLogsCard': LazyComponent<typeof import("../layers/core/components/monitoring/ErrorLogsCard.vue")['default']>
    'LazyMonitoringErrorSummaryCard': LazyComponent<typeof import("../layers/core/components/monitoring/ErrorSummaryCard.vue")['default']>
    'LazyMonitoringPageLoadTimesCard': LazyComponent<typeof import("../layers/core/components/monitoring/PageLoadTimesCard.vue")['default']>
    'LazyMonitoringPerformanceMetricsCard': LazyComponent<typeof import("../layers/core/components/monitoring/PerformanceMetricsCard.vue")['default']>
    'LazyMonitoringResourceOverviewCard': LazyComponent<typeof import("../layers/core/components/monitoring/ResourceOverviewCard.vue")['default']>
    'LazyMonitoringServerResourcesCard': LazyComponent<typeof import("../layers/core/components/monitoring/ServerResourcesCard.vue")['default']>
    'LazyPoliciesPolicyCard': LazyComponent<typeof import("../layers/core/components/policies/PolicyCard.vue")['default']>
    'LazyPoliciesPolicyCategoryFilter': LazyComponent<typeof import("../layers/core/components/policies/PolicyCategoryFilter.vue")['default']>
    'LazyPoliciesPolicyDetailModal': LazyComponent<typeof import("../layers/core/components/policies/PolicyDetailModal.vue")['default']>
    'LazyRulesCategoryCard': LazyComponent<typeof import("../layers/core/components/rules/CategoryCard.vue")['default']>
    'LazyRulesRuleCard': LazyComponent<typeof import("../layers/core/components/rules/RuleCard.vue")['default']>
    'LazyRulesRuleDetailModal': LazyComponent<typeof import("../layers/core/components/rules/RuleDetailModal.vue")['default']>
    'LazyRulesRuleFilters': LazyComponent<typeof import("../layers/core/components/rules/RuleFilters.vue")['default']>
    'LazyRulesTemplateCard': LazyComponent<typeof import("../layers/core/components/rules/TemplateCard.vue")['default']>
    'LazyRulesTemplateDetailModal': LazyComponent<typeof import("../layers/core/components/rules/TemplateDetailModal.vue")['default']>
    'LazyRulesValidationTester': LazyComponent<typeof import("../layers/core/components/rules/ValidationTester.vue")['default']>
    'LazySecurityAuditLogEntry': LazyComponent<typeof import("../layers/core/components/security/AuditLogEntry.vue")['default']>
    'LazySecurityAuditLogFilters': LazyComponent<typeof import("../layers/core/components/security/AuditLogFilters.vue")['default']>
    'LazySecurityEditRoleModal': LazyComponent<typeof import("../layers/core/components/security/EditRoleModal.vue")['default']>
    'LazySecurityEditUserRolesModal': LazyComponent<typeof import("../layers/core/components/security/EditUserRolesModal.vue")['default']>
    'LazySecurityRolePermissionsCard': LazyComponent<typeof import("../layers/core/components/security/RolePermissionsCard.vue")['default']>
    'LazySecurityUserRolesTable': LazyComponent<typeof import("../layers/core/components/security/UserRolesTable.vue")['default']>
    'LazySettingsAppearanceSettingsCard': LazyComponent<typeof import("../layers/core/components/settings/AppearanceSettingsCard.vue")['default']>
    'LazySettingsBackupHistoryCard': LazyComponent<typeof import("../layers/core/components/settings/BackupHistoryCard.vue")['default']>
    'LazySettingsBackupSettingsCard': LazyComponent<typeof import("../layers/core/components/settings/BackupSettingsCard.vue")['default']>
    'LazySettingsBackupStatusCard': LazyComponent<typeof import("../layers/core/components/settings/BackupStatusCard.vue")['default']>
    'LazySettingsDeleteConfirmationModal': LazyComponent<typeof import("../layers/core/components/settings/DeleteConfirmationModal.vue")['default']>
    'LazySettingsEnvironmentInfoCard': LazyComponent<typeof import("../layers/core/components/settings/EnvironmentInfoCard.vue")['default']>
    'LazySettingsEnvironmentVariablesCard': LazyComponent<typeof import("../layers/core/components/settings/EnvironmentVariablesCard.vue")['default']>
    'LazySettingsFormatSettingsCard': LazyComponent<typeof import("../layers/core/components/settings/FormatSettingsCard.vue")['default']>
    'LazySettingsGeneralSettingsCard': LazyComponent<typeof import("../layers/core/components/settings/GeneralSettingsCard.vue")['default']>
    'LazySettingsLanguageSettingsCard': LazyComponent<typeof import("../layers/core/components/settings/LanguageSettingsCard.vue")['default']>
    'LazySettingsPerformanceSettingsCard': LazyComponent<typeof import("../layers/core/components/settings/PerformanceSettingsCard.vue")['default']>
    'LazySettingsQuickActionsCard': LazyComponent<typeof import("../layers/core/components/settings/QuickActionsCard.vue")['default']>
    'LazySettingsRestoreConfirmationModal': LazyComponent<typeof import("../layers/core/components/settings/RestoreConfirmationModal.vue")['default']>
    'LazySettingsRestoreFromFileModal': LazyComponent<typeof import("../layers/core/components/settings/RestoreFromFileModal.vue")['default']>
    'LazySettingsRestoreSettingsCard': LazyComponent<typeof import("../layers/core/components/settings/RestoreSettingsCard.vue")['default']>
    'LazyWebhooksWebhookCard': LazyComponent<typeof import("../layers/core/components/webhooks/WebhookCard.vue")['default']>
    'LazyWebhooksWebhookDetailModal': LazyComponent<typeof import("../layers/core/components/webhooks/WebhookDetailModal.vue")['default']>
    'LazyEmployeeFilters': LazyComponent<typeof import("../layers/hr/components/EmployeeFilters.vue")['default']>
    'LazyEmployeeForm': LazyComponent<typeof import("../layers/hr/components/EmployeeForm.vue")['default']>
    'LazyEmployeeList': LazyComponent<typeof import("../layers/hr/components/EmployeeList.vue")['default']>
    'LazyHrDashboard': LazyComponent<typeof import("../layers/hr/components/HrDashboard.vue")['default']>
    'LazyWorkerForm': LazyComponent<typeof import("../layers/hr/components/WorkerForm.vue")['default']>
    'LazyAttendanceRecordsTab': LazyComponent<typeof import("../layers/hr/components/attendance/AttendanceRecordsTab.vue")['default']>
    'LazyAttendanceReportsTab': LazyComponent<typeof import("../layers/hr/components/attendance/AttendanceReportsTab.vue")['default']>
    'LazyAttendanceDailyAttendanceTab': LazyComponent<typeof import("../layers/hr/components/attendance/DailyAttendanceTab.vue")['default']>
    'LazyBenefitsBenefitPlansTab': LazyComponent<typeof import("../layers/hr/components/benefits/BenefitPlansTab.vue")['default']>
    'LazyBenefitsBenefitSettingsTab': LazyComponent<typeof import("../layers/hr/components/benefits/BenefitSettingsTab.vue")['default']>
    'LazyBenefitsEmployeeBenefitsTab': LazyComponent<typeof import("../layers/hr/components/benefits/EmployeeBenefitsTab.vue")['default']>
    'LazyBonusesAllBonusesTab': LazyComponent<typeof import("../layers/hr/components/bonuses/AllBonusesTab.vue")['default']>
    'LazyBonusesBonusReportsTab': LazyComponent<typeof import("../layers/hr/components/bonuses/BonusReportsTab.vue")['default']>
    'LazyBonusesEmployeeBonusesTab': LazyComponent<typeof import("../layers/hr/components/bonuses/EmployeeBonusesTab.vue")['default']>
    'LazyCareerPathDetails': LazyComponent<typeof import("../layers/hr/components/career/CareerPathDetails.vue")['default']>
    'LazyCareerPathFormModal': LazyComponent<typeof import("../layers/hr/components/career/CareerPathFormModal.vue")['default']>
    'LazyCareerPathOverview': LazyComponent<typeof import("../layers/hr/components/career/CareerPathOverview.vue")['default']>
    'LazyDepartmentsDepartmentCard': LazyComponent<typeof import("../layers/hr/components/departments/DepartmentCard.vue")['default']>
    'LazyDocumentsCompanyPoliciesTab': LazyComponent<typeof import("../layers/hr/components/documents/CompanyPoliciesTab.vue")['default']>
    'LazyDocumentsTab': LazyComponent<typeof import("../layers/hr/components/documents/DocumentsTab.vue")['default']>
    'LazyDocumentsEmployeeDocumentsTab': LazyComponent<typeof import("../layers/hr/components/documents/EmployeeDocumentsTab.vue")['default']>
    'LazyEmployeesDocumentUploadForm': LazyComponent<typeof import("../layers/hr/components/employees/DocumentUploadForm.vue")['default']>
    'LazyEmployeesEmployeeDocuments': LazyComponent<typeof import("../layers/hr/components/employees/EmployeeDocuments.vue")['default']>
    'LazyEmployeesEmployeeLeave': LazyComponent<typeof import("../layers/hr/components/employees/EmployeeLeave.vue")['default']>
    'LazyEmployeesEmployeeOverview': LazyComponent<typeof import("../layers/hr/components/employees/EmployeeOverview.vue")['default']>
    'LazyEmployeesEmployeePayroll': LazyComponent<typeof import("../layers/hr/components/employees/EmployeePayroll.vue")['default']>
    'LazyEmployeesEmployeePerformance': LazyComponent<typeof import("../layers/hr/components/employees/EmployeePerformance.vue")['default']>
    'LazyEmployeesEmployeeProfileCard': LazyComponent<typeof import("../layers/hr/components/employees/EmployeeProfileCard.vue")['default']>
    'LazyLeaveBalancesTab': LazyComponent<typeof import("../layers/hr/components/leave/LeaveBalancesTab.vue")['default']>
    'LazyLeaveCalendarTab': LazyComponent<typeof import("../layers/hr/components/leave/LeaveCalendarTab.vue")['default']>
    'LazyLeaveRequestsTab': LazyComponent<typeof import("../layers/hr/components/leave/LeaveRequestsTab.vue")['default']>
    'LazyLeaveSettingsTab': LazyComponent<typeof import("../layers/hr/components/leave/LeaveSettingsTab.vue")['default']>
    'LazyPayrollRunTab': LazyComponent<typeof import("../layers/hr/components/payroll/PayrollRunTab.vue")['default']>
    'LazyPayrollPayslipsTab': LazyComponent<typeof import("../layers/hr/components/payroll/PayslipsTab.vue")['default']>
    'LazyPayrollTaxSettingsTab': LazyComponent<typeof import("../layers/hr/components/payroll/TaxSettingsTab.vue")['default']>
    'LazyPerformanceEmployeePerformanceTab': LazyComponent<typeof import("../layers/hr/components/performance/EmployeePerformanceTab.vue")['default']>
    'LazyPerformanceAnalyticsTab': LazyComponent<typeof import("../layers/hr/components/performance/PerformanceAnalyticsTab.vue")['default']>
    'LazyPerformanceReviewsTab': LazyComponent<typeof import("../layers/hr/components/performance/PerformanceReviewsTab.vue")['default']>
    'LazyShiftsShiftAssignmentsTab': LazyComponent<typeof import("../layers/hr/components/shifts/ShiftAssignmentsTab.vue")['default']>
    'LazyShiftsShiftScheduleTab': LazyComponent<typeof import("../layers/hr/components/shifts/ShiftScheduleTab.vue")['default']>
    'LazyShiftsShiftTypesTab': LazyComponent<typeof import("../layers/hr/components/shifts/ShiftTypesTab.vue")['default']>
    'LazyTrainingCertificationsTab': LazyComponent<typeof import("../layers/hr/components/training/CertificationsTab.vue")['default']>
    'LazyTrainingEmployeeTrainingTab': LazyComponent<typeof import("../layers/hr/components/training/EmployeeTrainingTab.vue")['default']>
    'LazyTrainingEnrollEmployeesModal': LazyComponent<typeof import("../layers/hr/components/training/EnrollEmployeesModal.vue")['default']>
    'LazyTrainingDetailsModal': LazyComponent<typeof import("../layers/hr/components/training/TrainingDetailsModal.vue")['default']>
    'LazyTrainingFormModal': LazyComponent<typeof import("../layers/hr/components/training/TrainingFormModal.vue")['default']>
    'LazyTrainingProgramCard': LazyComponent<typeof import("../layers/hr/components/training/TrainingProgramCard.vue")['default']>
    'LazyTrainingProgramsTab': LazyComponent<typeof import("../layers/hr/components/training/TrainingProgramsTab.vue")['default']>
    'LazyProductionDashboard': LazyComponent<typeof import("../layers/production/components/ProductionDashboard.vue")['default']>
    'LazyDashboardTaskStatusChart': LazyComponent<typeof import("../layers/production/components/dashboard/TaskStatusChart.vue")['default']>
    'LazyDocumentsUploadDocumentModal': LazyComponent<typeof import("../layers/production/components/documents/UploadDocumentModal.vue")['default']>
    'LazyDocumentsViewDocumentModal': LazyComponent<typeof import("../layers/production/components/documents/ViewDocumentModal.vue")['default']>
    'LazyQualityChecklistCard': LazyComponent<typeof import("../layers/production/components/quality/ChecklistCard.vue")['default']>
    'LazyQualityChecklistDetailModal': LazyComponent<typeof import("../layers/production/components/quality/ChecklistDetailModal.vue")['default']>
    'LazyQualityChecklistItem': LazyComponent<typeof import("../layers/production/components/quality/ChecklistItem.vue")['default']>
    'LazyQualityNewChecklistModal': LazyComponent<typeof import("../layers/production/components/quality/NewChecklistModal.vue")['default']>
    'LazyQualityPhotoCard': LazyComponent<typeof import("../layers/production/components/quality/PhotoCard.vue")['default']>
    'LazyQualityPhotoDetailModal': LazyComponent<typeof import("../layers/production/components/quality/PhotoDetailModal.vue")['default']>
    'LazyQualityUploadPhotoModal': LazyComponent<typeof import("../layers/production/components/quality/UploadPhotoModal.vue")['default']>
    'LazyReportsBarChart': LazyComponent<typeof import("../layers/production/components/reports/BarChart.vue")['default']>
    'LazyReportsLineChart': LazyComponent<typeof import("../layers/production/components/reports/LineChart.vue")['default']>
    'LazyReportsMetricCard': LazyComponent<typeof import("../layers/production/components/reports/MetricCard.vue")['default']>
    'LazyReportsPieChart': LazyComponent<typeof import("../layers/production/components/reports/PieChart.vue")['default']>
    'LazyReportsReportCard': LazyComponent<typeof import("../layers/production/components/reports/ReportCard.vue")['default']>
    'LazyResourcesEquipmentCard': LazyComponent<typeof import("../layers/production/components/resources/EquipmentCard.vue")['default']>
    'LazyResourcesEquipmentDetailModal': LazyComponent<typeof import("../layers/production/components/resources/EquipmentDetailModal.vue")['default']>
    'LazyResourcesMaterialCard': LazyComponent<typeof import("../layers/production/components/resources/MaterialCard.vue")['default']>
    'LazyResourcesMaterialDetailModal': LazyComponent<typeof import("../layers/production/components/resources/MaterialDetailModal.vue")['default']>
    'LazyResourcesNewEquipmentModal': LazyComponent<typeof import("../layers/production/components/resources/NewEquipmentModal.vue")['default']>
    'LazyResourcesNewMaterialModal': LazyComponent<typeof import("../layers/production/components/resources/NewMaterialModal.vue")['default']>
    'LazyResourcesNewWorkerModal': LazyComponent<typeof import("../layers/production/components/resources/NewWorkerModal.vue")['default']>
    'LazyResourcesWorkforceCard': LazyComponent<typeof import("../layers/production/components/resources/WorkforceCard.vue")['default']>
    'LazyResourcesWorkforceDetailModal': LazyComponent<typeof import("../layers/production/components/resources/WorkforceDetailModal.vue")['default']>
    'LazyStatisticsProjectMetricsChart': LazyComponent<typeof import("../layers/production/components/statistics/ProjectMetricsChart.vue")['default']>
    'LazyStatisticsProjectStatusChart': LazyComponent<typeof import("../layers/production/components/statistics/ProjectStatusChart.vue")['default']>
    'LazyStatisticsTaskCompletionChart': LazyComponent<typeof import("../layers/production/components/statistics/TaskCompletionChart.vue")['default']>
    'LazyTasksNewTaskModal': LazyComponent<typeof import("../layers/production/components/tasks/NewTaskModal.vue")['default']>
    'LazyTasksTaskBoard': LazyComponent<typeof import("../layers/production/components/tasks/TaskBoard.vue")['default']>
    'LazyTasksTaskCard': LazyComponent<typeof import("../layers/production/components/tasks/TaskCard.vue")['default']>
    'LazyTasksTaskColumn': LazyComponent<typeof import("../layers/production/components/tasks/TaskColumn.vue")['default']>
    'LazyTasksTaskDetailModal': LazyComponent<typeof import("../layers/production/components/tasks/TaskDetailModal.vue")['default']>
    'LazyTimelineGanttChart': LazyComponent<typeof import("../layers/production/components/timeline/GanttChart.vue")['default']>
    'LazyRecruitmentSidebarItem': LazyComponent<typeof import("../layers/recruitment/components/RecruitmentSidebarItem.vue")['default']>
    'LazyAdminLanguageManager': LazyComponent<typeof import("../layers/recruitment/components/admin/LanguageManager.vue")['default']>
    'LazyAdminTranslationsManager': LazyComponent<typeof import("../layers/recruitment/components/admin/TranslationsManager.vue")['default']>
    'LazyFormsFormBuilderFieldEditor': LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderFieldEditor.vue")['default']>
    'LazyFormsFormBuilderFieldList': LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderFieldList.vue")['default']>
    'LazyFormsFormBuilderPreview': LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderPreview.vue")['default']>
    'LazyFormsFormBuilderPreviewField': LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderPreviewField.vue")['default']>
    'LazyFormsFormBuilderSettings': LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderSettings.vue")['default']>
    'LazyFormsFormBuilderSidebar': LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderSidebar.vue")['default']>
    'LazyFormsFormBuilderStepEditor': LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderStepEditor.vue")['default']>
    'LazyFormsPublicFormField': LazyComponent<typeof import("../layers/recruitment/components/forms/PublicFormField.vue")['default']>
    'LazyWorkforceFormAdditionalInfoStep': LazyComponent<typeof import("../layers/recruitment/components/workforce-form/AdditionalInfoStep.vue")['default']>
    'LazyWorkforceFormConfirmationStep': LazyComponent<typeof import("../layers/recruitment/components/workforce-form/ConfirmationStep.vue")['default']>
    'LazyWorkforceFormIntroductionStep': LazyComponent<typeof import("../layers/recruitment/components/workforce-form/IntroductionStep.vue")['default']>
    'LazyWorkforceFormLanguageSelector': LazyComponent<typeof import("../layers/recruitment/components/workforce-form/LanguageSelector.vue")['default']>
    'LazyWorkforceFormPersonalInfoStep': LazyComponent<typeof import("../layers/recruitment/components/workforce-form/PersonalInfoStep.vue")['default']>
    'LazyWorkforceFormProfessionalExperienceStep': LazyComponent<typeof import("../layers/recruitment/components/workforce-form/ProfessionalExperienceStep.vue")['default']>
    'LazyWorkforceFormQualificationsStep': LazyComponent<typeof import("../layers/recruitment/components/workforce-form/QualificationsStep.vue")['default']>
    'LazyWorkforceFormReferencesStep': LazyComponent<typeof import("../layers/recruitment/components/workforce-form/ReferencesStep.vue")['default']>
    'LazyWorkforceFormSpecialtiesStep': LazyComponent<typeof import("../layers/recruitment/components/workforce-form/SpecialtiesStep.vue")['default']>
    'LazyWorkforceAssignmentDetailsModal': LazyComponent<typeof import("../layers/recruitment/components/workforce/AssignmentDetailsModal.vue")['default']>
    'LazyWorkforceAssignmentFilters': LazyComponent<typeof import("../layers/recruitment/components/workforce/AssignmentFilters.vue")['default']>
    'LazyWorkforceAssignmentsPagination': LazyComponent<typeof import("../layers/recruitment/components/workforce/AssignmentsPagination.vue")['default']>
    'LazyWorkforceJobDetailsModal': LazyComponent<typeof import("../layers/recruitment/components/workforce/JobDetailsModal.vue")['default']>
    'LazyWorkforceJobFilters': LazyComponent<typeof import("../layers/recruitment/components/workforce/JobFilters.vue")['default']>
    'LazyWorkforceJobFormModal': LazyComponent<typeof import("../layers/recruitment/components/workforce/JobFormModal.vue")['default']>
    'LazyWorkforceJobsPagination': LazyComponent<typeof import("../layers/recruitment/components/workforce/JobsPagination.vue")['default']>
    'LazyWorkforceMatchDetailsModal': LazyComponent<typeof import("../layers/recruitment/components/workforce/MatchDetailsModal.vue")['default']>
    'LazyWorkforceMatchFilters': LazyComponent<typeof import("../layers/recruitment/components/workforce/MatchFilters.vue")['default']>
    'LazyWorkforceMatchesPagination': LazyComponent<typeof import("../layers/recruitment/components/workforce/MatchesPagination.vue")['default']>
    'LazyWorkforceWorkerDetailsModal': LazyComponent<typeof import("../layers/recruitment/components/workforce/WorkerDetailsModal.vue")['default']>
    'LazyWorkforceWorkerFormModal': LazyComponent<typeof import("../layers/recruitment/components/workforce/WorkerFormModal.vue")['default']>
    'LazyWorkforceWorkersPagination': LazyComponent<typeof import("../layers/recruitment/components/workforce/WorkersPagination.vue")['default']>
    'LazyTairoSidebarBurger': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarBurger.vue")['default']>
    'LazyTairoSidebarCircularMenu': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarCircularMenu.vue")['default']>
    'LazyTairoSidebarLayout': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarLayout.vue")['default']>
    'LazyTairoSidebarNavigationItem': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarNavigationItem.vue")['default']>
    'LazyTairoSidebarNavigationPanel': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarNavigationPanel.vue")['default']>
    'LazyTairoSidebarToolbar': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarToolbar.vue")['default']>
    'LazyTairoSidebarTools': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarTools.vue")['default']>
    'LazyTairoSubsidebar': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebar.vue")['default']>
    'LazyTairoSubsidebarHeader': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarHeader.vue")['default']>
    'LazyTairoSubsidebarMenu': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenu.vue")['default']>
    'LazyTairoSubsidebarMenuCollapseLinks': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuCollapseLinks.vue")['default']>
    'LazyTairoSubsidebarMenuDivider': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuDivider.vue")['default']>
    'LazyTairoSubsidebarMenuLink': LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuLink.vue")['default']>
    'LazyLandingBenefits': LazyComponent<typeof import("../layers/landing/components/LandingBenefits.vue")['default']>
    'LazyLandingContent': LazyComponent<typeof import("../layers/landing/components/LandingContent.vue")['default']>
    'LazyLandingCta': LazyComponent<typeof import("../layers/landing/components/LandingCta.vue")['default']>
    'LazyLandingCustomizer': LazyComponent<typeof import("../layers/landing/components/LandingCustomizer.vue")['default']>
    'LazyLandingDemoLink': LazyComponent<typeof import("../layers/landing/components/LandingDemoLink.vue")['default']>
    'LazyLandingDemos': LazyComponent<typeof import("../layers/landing/components/LandingDemos.vue")['default']>
    'LazyLandingFeatures': LazyComponent<typeof import("../layers/landing/components/LandingFeatures.vue")['default']>
    'LazyLandingFeaturesTile': LazyComponent<typeof import("../layers/landing/components/LandingFeaturesTile.vue")['default']>
    'LazyLandingFooter': LazyComponent<typeof import("../layers/landing/components/LandingFooter.vue")['default']>
    'LazyLandingHero': LazyComponent<typeof import("../layers/landing/components/LandingHero.vue")['default']>
    'LazyLandingHeroMockup': LazyComponent<typeof import("../layers/landing/components/LandingHeroMockup.vue")['default']>
    'LazyLandingLayers': LazyComponent<typeof import("../layers/landing/components/LandingLayers.vue")['default']>
    'LazyLandingLayersBox': LazyComponent<typeof import("../layers/landing/components/LandingLayersBox.vue")['default']>
    'LazyLandingLayout': LazyComponent<typeof import("../layers/landing/components/LandingLayout.vue")['default']>
    'LazyLandingLayouts': LazyComponent<typeof import("../layers/landing/components/LandingLayouts.vue")['default']>
    'LazyLandingNavbar': LazyComponent<typeof import("../layers/landing/components/LandingNavbar.vue")['default']>
    'LazyMockupFollowersCompact': LazyComponent<typeof import("../layers/landing/components/MockupFollowersCompact.vue")['default']>
    'LazyMockupInboxMessage': LazyComponent<typeof import("../layers/landing/components/MockupInboxMessage.vue")['default']>
    'LazyMockupInfoBadges': LazyComponent<typeof import("../layers/landing/components/MockupInfoBadges.vue")['default']>
    'LazyMockupProgressCircle': LazyComponent<typeof import("../layers/landing/components/MockupProgressCircle.vue")['default']>
    'LazyMockupTeamSearchCompact': LazyComponent<typeof import("../layers/landing/components/MockupTeamSearchCompact.vue")['default']>
    'LazyMockupVideoCompact': LazyComponent<typeof import("../layers/landing/components/MockupVideoCompact.vue")['default']>
    'LazySVGMorph': LazyComponent<typeof import("../layers/landing/components/SVGMorph.vue")['default']>
    'LazyTairoIconnavCircularMenu': LazyComponent<typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavCircularMenu.vue")['default']>
    'LazyTairoIconnavFooter': LazyComponent<typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavFooter.vue")['default']>
    'LazyTairoIconnavLayout': LazyComponent<typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavLayout.vue")['default']>
    'LazyTairoIconnavNavigation': LazyComponent<typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavNavigation.vue")['default']>
    'LazyBaseAccordion': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAccordion.vue")['default']>
    'LazyBaseAvatar': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAvatar.vue")['default']>
    'LazyBaseAvatarGroup': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAvatarGroup.vue")['default']>
    'LazyBaseBreadcrumb': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseBreadcrumb.vue")['default']>
    'LazyBaseButton': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButton.vue")['default']>
    'LazyBaseButtonAction': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonAction.vue")['default']>
    'LazyBaseButtonClose': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonClose.vue")['default']>
    'LazyBaseButtonGroup': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonGroup.vue")['default']>
    'LazyBaseButtonIcon': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonIcon.vue")['default']>
    'LazyBaseCard': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseCard.vue")['default']>
    'LazyBaseDropdown': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdown.vue")['default']>
    'LazyBaseDropdownDivider': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdownDivider.vue")['default']>
    'LazyBaseDropdownItem': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdownItem.vue")['default']>
    'LazyBaseFocusLoop': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseFocusLoop.vue")['default']>
    'LazyBaseHeading': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseHeading.vue")['default']>
    'LazyBaseIconBox': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseIconBox.vue")['default']>
    'LazyBaseKbd': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseKbd.vue")['default']>
    'LazyBaseLink': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseLink.vue")['default']>
    'LazyBaseList': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseList.vue")['default']>
    'LazyBaseListItem': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseListItem.vue")['default']>
    'LazyBaseMessage': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseMessage.vue")['default']>
    'LazyBasePagination': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePagination.vue")['default']>
    'LazyBaseParagraph': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseParagraph.vue")['default']>
    'LazyBasePlaceholderPage': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePlaceholderPage.vue")['default']>
    'LazyBasePlaceload': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePlaceload.vue")['default']>
    'LazyBaseProgress': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProgress.vue")['default']>
    'LazyBaseProgressCircle': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProgressCircle.vue")['default']>
    'LazyBaseProse': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProse.vue")['default']>
    'LazyBaseSnack': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseSnack.vue")['default']>
    'LazyBaseTabSlider': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTabSlider.vue")['default']>
    'LazyBaseTabs': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTabs.vue")['default']>
    'LazyBaseTag': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTag.vue")['default']>
    'LazyBaseText': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseText.vue")['default']>
    'LazyBaseThemeSwitch': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseThemeSwitch.vue")['default']>
    'LazyBaseThemeToggle': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseThemeToggle.vue")['default']>
    'LazyIconCheck': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconCheck.vue")['default']>
    'LazyIconCheckCircle': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconCheckCircle.vue")['default']>
    'LazyIconChevronDown': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconChevronDown.vue")['default']>
    'LazyIconClose': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconClose.vue")['default']>
    'LazyIconIndeterminate': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconIndeterminate.vue")['default']>
    'LazyIconMinus': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconMinus.vue")['default']>
    'LazyIconMoon': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconMoon.vue")['default']>
    'LazyIconPlus': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconPlus.vue")['default']>
    'LazyIconSun': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconSun.vue")['default']>
    'LazyBaseAutocomplete': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseAutocomplete.vue")['default']>
    'LazyBaseAutocompleteItem': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseAutocompleteItem.vue")['default']>
    'LazyBaseCheckbox': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckbox.vue")['default']>
    'LazyBaseCheckboxAnimated': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckboxAnimated.vue")['default']>
    'LazyBaseCheckboxHeadless': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckboxHeadless.vue")['default']>
    'LazyBaseFullscreenDropfile': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseFullscreenDropfile.vue")['default']>
    'LazyBaseInput': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInput.vue")['default']>
    'LazyBaseInputFile': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputFile.vue")['default']>
    'LazyBaseInputFileHeadless': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputFileHeadless.vue")['default']>
    'LazyBaseInputHelpText': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputHelpText.vue")['default']>
    'LazyBaseInputNumber': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputNumber.vue")['default']>
    'LazyBaseListbox': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseListbox.vue")['default']>
    'LazyBaseListboxItem': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseListboxItem.vue")['default']>
    'LazyBaseRadio': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseRadio.vue")['default']>
    'LazyBaseRadioHeadless': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseRadioHeadless.vue")['default']>
    'LazyBaseSelect': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSelect.vue")['default']>
    'LazyBaseSwitchBall': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSwitchBall.vue")['default']>
    'LazyBaseSwitchThin': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSwitchThin.vue")['default']>
    'LazyBaseTextarea': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTextarea.vue")['default']>
    'LazyBaseTreeSelect': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTreeSelect.vue")['default']>
    'LazyBaseTreeSelectItem': LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTreeSelectItem.vue")['default']>
    'LazyNuxtWelcome': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/welcome.vue")['default']>
    'LazyNuxtLayout': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
    'LazyNuxtErrorBoundary': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']>
    'LazyClientOnly': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/client-only")['default']>
    'LazyDevOnly': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/dev-only")['default']>
    'LazyServerPlaceholder': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtLink': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-link")['default']>
    'LazyNuxtLoadingIndicator': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
    'LazyNuxtImg': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
    'LazyNuxtPicture': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
    'LazyIonAnimation': LazyComponent<typeof import("../node_modules/.pnpm/@nuxtjs+ionic@0.16.0_@stencil+core@4.28.2_magicast@0.3.5_vue-router@4.5.0_vue@3.5.13_typescri_kvogt2gdziafhlhhzxvd6o57ta/node_modules/@nuxtjs/ionic/dist/runtime/components/IonAnimation.vue")['default']>
    'LazyIonAccordion': LazyComponent<typeof import("@ionic/vue")['IonAccordion']>
    'LazyIonAccordionGroup': LazyComponent<typeof import("@ionic/vue")['IonAccordionGroup']>
    'LazyIonActionSheet': LazyComponent<typeof import("@ionic/vue")['IonActionSheet']>
    'LazyIonAlert': LazyComponent<typeof import("@ionic/vue")['IonAlert']>
    'LazyIonApp': LazyComponent<typeof import("@ionic/vue")['IonApp']>
    'LazyIonAvatar': LazyComponent<typeof import("@ionic/vue")['IonAvatar']>
    'LazyIonBackButton': LazyComponent<typeof import("@ionic/vue")['IonBackButton']>
    'LazyIonBackdrop': LazyComponent<typeof import("@ionic/vue")['IonBackdrop']>
    'LazyIonBadge': LazyComponent<typeof import("@ionic/vue")['IonBadge']>
    'LazyIonBreadcrumb': LazyComponent<typeof import("@ionic/vue")['IonBreadcrumb']>
    'LazyIonBreadcrumbs': LazyComponent<typeof import("@ionic/vue")['IonBreadcrumbs']>
    'LazyIonButton': LazyComponent<typeof import("@ionic/vue")['IonButton']>
    'LazyIonButtons': LazyComponent<typeof import("@ionic/vue")['IonButtons']>
    'LazyIonCard': LazyComponent<typeof import("@ionic/vue")['IonCard']>
    'LazyIonCardContent': LazyComponent<typeof import("@ionic/vue")['IonCardContent']>
    'LazyIonCardHeader': LazyComponent<typeof import("@ionic/vue")['IonCardHeader']>
    'LazyIonCardSubtitle': LazyComponent<typeof import("@ionic/vue")['IonCardSubtitle']>
    'LazyIonCardTitle': LazyComponent<typeof import("@ionic/vue")['IonCardTitle']>
    'LazyIonCheckbox': LazyComponent<typeof import("@ionic/vue")['IonCheckbox']>
    'LazyIonChip': LazyComponent<typeof import("@ionic/vue")['IonChip']>
    'LazyIonCol': LazyComponent<typeof import("@ionic/vue")['IonCol']>
    'LazyIonContent': LazyComponent<typeof import("@ionic/vue")['IonContent']>
    'LazyIonDatetime': LazyComponent<typeof import("@ionic/vue")['IonDatetime']>
    'LazyIonDatetimeButton': LazyComponent<typeof import("@ionic/vue")['IonDatetimeButton']>
    'LazyIonFab': LazyComponent<typeof import("@ionic/vue")['IonFab']>
    'LazyIonFabButton': LazyComponent<typeof import("@ionic/vue")['IonFabButton']>
    'LazyIonFabList': LazyComponent<typeof import("@ionic/vue")['IonFabList']>
    'LazyIonFooter': LazyComponent<typeof import("@ionic/vue")['IonFooter']>
    'LazyIonGrid': LazyComponent<typeof import("@ionic/vue")['IonGrid']>
    'LazyIonHeader': LazyComponent<typeof import("@ionic/vue")['IonHeader']>
    'LazyIonIcon': LazyComponent<typeof import("@ionic/vue")['IonIcon']>
    'LazyIonImg': LazyComponent<typeof import("@ionic/vue")['IonImg']>
    'LazyIonInfiniteScroll': LazyComponent<typeof import("@ionic/vue")['IonInfiniteScroll']>
    'LazyIonInfiniteScrollContent': LazyComponent<typeof import("@ionic/vue")['IonInfiniteScrollContent']>
    'LazyIonInput': LazyComponent<typeof import("@ionic/vue")['IonInput']>
    'LazyIonInputPasswordToggle': LazyComponent<typeof import("@ionic/vue")['IonInputPasswordToggle']>
    'LazyIonItem': LazyComponent<typeof import("@ionic/vue")['IonItem']>
    'LazyIonItemDivider': LazyComponent<typeof import("@ionic/vue")['IonItemDivider']>
    'LazyIonItemGroup': LazyComponent<typeof import("@ionic/vue")['IonItemGroup']>
    'LazyIonItemOption': LazyComponent<typeof import("@ionic/vue")['IonItemOption']>
    'LazyIonItemOptions': LazyComponent<typeof import("@ionic/vue")['IonItemOptions']>
    'LazyIonItemSliding': LazyComponent<typeof import("@ionic/vue")['IonItemSliding']>
    'LazyIonLabel': LazyComponent<typeof import("@ionic/vue")['IonLabel']>
    'LazyIonList': LazyComponent<typeof import("@ionic/vue")['IonList']>
    'LazyIonListHeader': LazyComponent<typeof import("@ionic/vue")['IonListHeader']>
    'LazyIonLoading': LazyComponent<typeof import("@ionic/vue")['IonLoading']>
    'LazyIonMenu': LazyComponent<typeof import("@ionic/vue")['IonMenu']>
    'LazyIonMenuButton': LazyComponent<typeof import("@ionic/vue")['IonMenuButton']>
    'LazyIonMenuToggle': LazyComponent<typeof import("@ionic/vue")['IonMenuToggle']>
    'LazyIonModal': LazyComponent<typeof import("@ionic/vue")['IonModal']>
    'LazyIonNav': LazyComponent<typeof import("@ionic/vue")['IonNav']>
    'LazyIonNavLink': LazyComponent<typeof import("@ionic/vue")['IonNavLink']>
    'LazyIonNote': LazyComponent<typeof import("@ionic/vue")['IonNote']>
    'LazyIonPage': LazyComponent<typeof import("@ionic/vue")['IonPage']>
    'LazyIonPicker': LazyComponent<typeof import("@ionic/vue")['IonPicker']>
    'LazyIonPickerColumn': LazyComponent<typeof import("@ionic/vue")['IonPickerColumn']>
    'LazyIonPickerColumnOption': LazyComponent<typeof import("@ionic/vue")['IonPickerColumnOption']>
    'LazyIonPickerLegacy': LazyComponent<typeof import("@ionic/vue")['IonPickerLegacy']>
    'LazyIonPopover': LazyComponent<typeof import("@ionic/vue")['IonPopover']>
    'LazyIonProgressBar': LazyComponent<typeof import("@ionic/vue")['IonProgressBar']>
    'LazyIonRadio': LazyComponent<typeof import("@ionic/vue")['IonRadio']>
    'LazyIonRadioGroup': LazyComponent<typeof import("@ionic/vue")['IonRadioGroup']>
    'LazyIonRange': LazyComponent<typeof import("@ionic/vue")['IonRange']>
    'LazyIonRefresher': LazyComponent<typeof import("@ionic/vue")['IonRefresher']>
    'LazyIonRefresherContent': LazyComponent<typeof import("@ionic/vue")['IonRefresherContent']>
    'LazyIonReorder': LazyComponent<typeof import("@ionic/vue")['IonReorder']>
    'LazyIonReorderGroup': LazyComponent<typeof import("@ionic/vue")['IonReorderGroup']>
    'LazyIonRippleEffect': LazyComponent<typeof import("@ionic/vue")['IonRippleEffect']>
    'LazyIonRouterOutlet': LazyComponent<typeof import("@ionic/vue")['IonRouterOutlet']>
    'LazyIonRow': LazyComponent<typeof import("@ionic/vue")['IonRow']>
    'LazyIonSearchbar': LazyComponent<typeof import("@ionic/vue")['IonSearchbar']>
    'LazyIonSegment': LazyComponent<typeof import("@ionic/vue")['IonSegment']>
    'LazyIonSegmentButton': LazyComponent<typeof import("@ionic/vue")['IonSegmentButton']>
    'LazyIonSegmentContent': LazyComponent<typeof import("@ionic/vue")['IonSegmentContent']>
    'LazyIonSegmentView': LazyComponent<typeof import("@ionic/vue")['IonSegmentView']>
    'LazyIonSelect': LazyComponent<typeof import("@ionic/vue")['IonSelect']>
    'LazyIonSelectModal': LazyComponent<typeof import("@ionic/vue")['IonSelectModal']>
    'LazyIonSelectOption': LazyComponent<typeof import("@ionic/vue")['IonSelectOption']>
    'LazyIonSkeletonText': LazyComponent<typeof import("@ionic/vue")['IonSkeletonText']>
    'LazyIonSpinner': LazyComponent<typeof import("@ionic/vue")['IonSpinner']>
    'LazyIonSplitPane': LazyComponent<typeof import("@ionic/vue")['IonSplitPane']>
    'LazyIonTab': LazyComponent<typeof import("@ionic/vue")['IonTab']>
    'LazyIonTabs': LazyComponent<typeof import("@ionic/vue")['IonTabs']>
    'LazyIonTabBar': LazyComponent<typeof import("@ionic/vue")['IonTabBar']>
    'LazyIonTabButton': LazyComponent<typeof import("@ionic/vue")['IonTabButton']>
    'LazyIonText': LazyComponent<typeof import("@ionic/vue")['IonText']>
    'LazyIonTextarea': LazyComponent<typeof import("@ionic/vue")['IonTextarea']>
    'LazyIonThumbnail': LazyComponent<typeof import("@ionic/vue")['IonThumbnail']>
    'LazyIonTitle': LazyComponent<typeof import("@ionic/vue")['IonTitle']>
    'LazyIonToast': LazyComponent<typeof import("@ionic/vue")['IonToast']>
    'LazyIonToggle': LazyComponent<typeof import("@ionic/vue")['IonToggle']>
    'LazyIonToolbar': LazyComponent<typeof import("@ionic/vue")['IonToolbar']>
    'LazyNuxtLinkLocale': LazyComponent<typeof import("../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']>
    'LazySwitchLocalePathLink': LazyComponent<typeof import("../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']>
    'LazyColorScheme': LazyComponent<typeof import("../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']>
    'LazyIcon': LazyComponent<typeof import("../node_modules/.pnpm/@nuxt+icon@1.11.0_magicast@0.3.5_vite@6.2.2_@types+node@22.14.0_jiti@2.4.2_lightningcss@1.29._4n4hvoiqlixjyr6n4h4n6fje7e/node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
    'LazyNuxtPage': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/pages/runtime/page")['default']>
    'LazyNoScript': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['NoScript']>
    'LazyLink': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Link']>
    'LazyBase': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Base']>
    'LazyTitle': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Title']>
    'LazyMeta': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Meta']>
    'LazyStyle': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Style']>
    'LazyHead': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Head']>
    'LazyHtml': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Html']>
    'LazyBody': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Body']>
    'LazyNuxtIsland': LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-island")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<IslandComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/server-placeholder")['default']>>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const AccountMenu: typeof import("../components/global/AccountMenu.vue")['default']
export const AccountingSubsidebar: typeof import("../components/global/AccountingSubsidebar.vue")['default']
export const AddContactButton: typeof import("../components/global/AddContactButton.vue")['default']
export const AddContactForm: typeof import("../components/global/AddContactForm.vue")['default']
export const AddContactModal: typeof import("../components/global/AddContactModal.vue")['default']
export const AddMachineButton: typeof import("../components/global/AddMachineButton.vue")['default']
export const AddMachineForm: typeof import("../components/global/AddMachineForm.vue")['default']
export const AddMachineModal: typeof import("../components/global/AddMachineModal.vue")['default']
export const AddMeetingButton: typeof import("../components/global/AddMeetingButton.vue")['default']
export const AddMeetingForm: typeof import("../components/global/AddMeetingForm.vue")['default']
export const AddMeetingModal: typeof import("../components/global/AddMeetingModal.vue")['default']
export const AddUserButton: typeof import("../components/global/AddUserButton.vue")['default']
export const AddUserForm: typeof import("../components/global/AddUserForm.vue")['default']
export const AddUserModal: typeof import("../components/global/AddUserModal.vue")['default']
export const AppLayoutSwitcher: typeof import("../components/global/AppLayoutSwitcher.vue")['default']
export const AppSearch: typeof import("../components/global/AppSearch.vue")['default']
export const AppSearchResult: typeof import("../components/global/AppSearchResult.vue")['default']
export const BudgetSubsidebar: typeof import("../components/global/BudgetSubsidebar.vue")['default']
export const CircularMenuActivity: typeof import("../components/global/CircularMenuActivity.vue")['default']
export const CircularMenuLanguage: typeof import("../components/global/CircularMenuLanguage.vue")['default']
export const CircularMenuNotifications: typeof import("../components/global/CircularMenuNotifications.vue")['default']
export const CollapseNavigationFooter: typeof import("../components/global/CollapseNavigationFooter.vue")['default']
export const CollapseNavigationHeader: typeof import("../components/global/CollapseNavigationHeader.vue")['default']
export const CommunicationSubsidebar: typeof import("../components/global/CommunicationSubsidebar.vue")['default']
export const CompaniesSubsidebar: typeof import("../components/global/CompaniesSubsidebar.vue")['default']
export const CoreSubsidebar: typeof import("../components/global/CoreSubsidebar.vue")['default']
export const CustomerVehiclesTable: typeof import("../components/global/CustomerVehiclesTable.vue")['default']
export const FlexTableCell: typeof import("../components/global/FlexTableCell.vue")['default']
export const FlexTableRow: typeof import("../components/global/FlexTableRow.vue")['default']
export const FlexTableStart: typeof import("../components/global/FlexTableStart.vue")['default']
export const HrSubsidebar: typeof import("../components/global/HrSubsidebar.vue")['default']
export const Logo: typeof import("../components/global/Logo.vue")['default']
export const LogoText: typeof import("../components/global/LogoText.vue")['default']
export const ModuleAccessInfo: typeof import("../components/global/ModuleAccessInfo.vue")['default']
export const PanelLanguage: typeof import("../components/global/PanelLanguage.vue")['default']
export const PanelSearch: typeof import("../components/global/PanelSearch.vue")['default']
export const PanelTask: typeof import("../components/global/PanelTask.vue")['default']
export const PhoneInput: typeof import("../components/global/PhoneInput.vue")['default']
export const ProductionSidebar: typeof import("../components/global/ProductionSidebar.vue")['default']
export const ProductionSubsidebar: typeof import("../components/global/ProductionSubsidebar.vue")['default']
export const RecruitmentSidebar: typeof import("../components/global/RecruitmentSidebar.vue")['default']
export const SalesSubsidebar: typeof import("../components/global/SalesSubsidebar.vue")['default']
export const Subsidebar: typeof import("../components/global/Subsidebar.vue")['default']
export const SubsidebarClients: typeof import("../components/global/SubsidebarClients.vue")['default']
export const SubsidebarDashboards: typeof import("../components/global/SubsidebarDashboards.vue")['default']
export const SubsidebarHeader: typeof import("../components/global/SubsidebarHeader.vue")['default']
export const SubsidebarLayouts: typeof import("../components/global/SubsidebarLayouts.vue")['default']
export const SubsidebarMenu: typeof import("../components/global/SubsidebarMenu.vue")['default']
export const SubsidebarMenuCollapseLinks: typeof import("../components/global/SubsidebarMenuCollapseLinks.vue")['default']
export const SubsidebarMenuDivider: typeof import("../components/global/SubsidebarMenuDivider.vue")['default']
export const SubsidebarMenuLink: typeof import("../components/global/SubsidebarMenuLink.vue")['default']
export const TeamListCompact: typeof import("../components/global/TeamListCompact.vue")['default']
export const ThemeToggle: typeof import("../components/global/ThemeToggle.vue")['default']
export const TimeManagementSubsidebar: typeof import("../components/global/TimeManagementSubsidebar.vue")['default']
export const Toaster: typeof import("../components/global/Toaster.vue")['default']
export const TocAnchor: typeof import("../components/global/TocAnchor.vue")['default']
export const ToolbarAccountMenu: typeof import("../components/global/ToolbarAccountMenu.vue")['default']
export const ToolbarActivity: typeof import("../components/global/ToolbarActivity.vue")['default']
export const ToolbarCustomize: typeof import("../components/global/ToolbarCustomize.vue")['default']
export const ToolbarLanguage: typeof import("../components/global/ToolbarLanguage.vue")['default']
export const ToolbarNotifications: typeof import("../components/global/ToolbarNotifications.vue")['default']
export const ToolbarSearch: typeof import("../components/global/ToolbarSearch.vue")['default']
export const AccessControl: typeof import("../components/AccessControl.vue")['default']
export const AddonApexcharts: typeof import("../components/AddonApexcharts.vue")['default']
export const AddonInputPassword: typeof import("../components/AddonInputPassword.vue")['default']
export const AdminsPanelAccount: typeof import("../components/Admins/PanelAccount.vue")['default']
export const AdminsPanelActivity: typeof import("../components/Admins/PanelActivity.vue")['default']
export const AdminsPanelCard: typeof import("../components/Admins/PanelCard.vue")['default']
export const AdminsPanelInvest: typeof import("../components/Admins/PanelInvest.vue")['default']
export const AdminsPanels: typeof import("../components/Admins/Panels.vue")['default']
export const AdminsSidebarBurger: typeof import("../components/Admins/SidebarBurger.vue")['default']
export const AdminsSidebarCircularMenu: typeof import("../components/Admins/SidebarCircularMenu.vue")['default']
export const AdminsSidebarLayout: typeof import("../components/Admins/SidebarLayout.vue")['default']
export const AdminsSidebarNavigation: typeof import("../components/Admins/SidebarNavigation.vue")['default']
export const AdminsSidebarNavigationItem: typeof import("../components/Admins/SidebarNavigationItem.vue")['default']
export const AdminsSidebarNavigationPanel: typeof import("../components/Admins/SidebarNavigationPanel.vue")['default']
export const AdminsSidebarToolbar: typeof import("../components/Admins/SidebarToolbar.vue")['default']
export const AdminsSidebarTools: typeof import("../components/Admins/SidebarTools.vue")['default']
export const AdminsToolbarNotifications: typeof import("../components/Admins/ToolbarNotifications.vue")['default']
export const AdminsTopnavWorkspaceDropdown: typeof import("../components/Admins/TopnavWorkspaceDropdown.vue")['default']
export const AiAssistant: typeof import("../components/AiAssistant.vue")['default']
export const AiAssistantButton: typeof import("../components/AiAssistantButton.vue")['default']
export const CalendarWidget: typeof import("../components/CalendarWidget.vue")['default']
export const DatePicker: typeof import("../components/DatePicker.vue")['default']
export const DemoAccountMenu: typeof import("../components/DemoAccountMenu.vue")['default']
export const DemoCalendarEvent: typeof import("../components/DemoCalendarEvent.vue")['default']
export const DemoCalendarEventPending: typeof import("../components/DemoCalendarEventPending.vue")['default']
export const TairoSidebarNavigation: typeof import("../components/TairoSidebarNavigation.vue")['default']
export const UsersAccountMenu: typeof import("../components/Users/<USER>")['default']
export const UsersPanelAccount: typeof import("../components/Users/<USER>")['default']
export const UsersPanelActivity: typeof import("../components/Users/<USER>")['default']
export const UsersPanelCard: typeof import("../components/Users/<USER>")['default']
export const UsersPanelInvest: typeof import("../components/Users/<USER>")['default']
export const UsersPanels: typeof import("../components/Users/<USER>")['default']
export const UsersSidebarBurger: typeof import("../components/Users/<USER>")['default']
export const UsersSidebarCircularMenu: typeof import("../components/Users/<USER>")['default']
export const UsersSidebarLayout: typeof import("../components/Users/<USER>")['default']
export const UsersSidebarNavigation: typeof import("../components/Users/<USER>")['default']
export const UsersSidebarNavigationItem: typeof import("../components/Users/<USER>")['default']
export const UsersSidebarNavigationPanel: typeof import("../components/Users/<USER>")['default']
export const UsersSidebarToolbar: typeof import("../components/Users/<USER>")['default']
export const UsersSidebarTools: typeof import("../components/Users/<USER>")['default']
export const UsersToolbarAccountMenu: typeof import("../components/Users/<USER>")['default']
export const UsersTopnavWorkspaceDropdown: typeof import("../components/Users/<USER>")['default']
export const AuthEmailVerification: typeof import("../components/auth/EmailVerification.vue")['default']
export const AuthPaymentForm: typeof import("../components/auth/PaymentForm.vue")['default']
export const AuthRegistrationForm: typeof import("../components/auth/RegistrationForm.vue")['default']
export const AuthRegistrationSuccess: typeof import("../components/auth/RegistrationSuccess.vue")['default']
export const AuthRoleSelection: typeof import("../components/auth/RoleSelection.vue")['default']
export const AuthStripePaymentForm: typeof import("../components/auth/StripePaymentForm.vue")['default']
export const AuthSubscriptionPlanSelection: typeof import("../components/auth/SubscriptionPlanSelection.vue")['default']
export const BaseLoader: typeof import("../components/base/BaseLoader.vue")['default']
export const DemoChartArea: typeof import("../components/demo-chart/DemoChartArea.vue")['default']
export const DemoChartAreaBalance: typeof import("../components/demo-chart/DemoChartAreaBalance.vue")['default']
export const DemoChartAreaMulti: typeof import("../components/demo-chart/DemoChartAreaMulti.vue")['default']
export const DemoChartAreaStats: typeof import("../components/demo-chart/DemoChartAreaStats.vue")['default']
export const DemoChartBar: typeof import("../components/demo-chart/DemoChartBar.vue")['default']
export const DemoChartBarHorizontal: typeof import("../components/demo-chart/DemoChartBarHorizontal.vue")['default']
export const DemoChartBarHorizontalMulti: typeof import("../components/demo-chart/DemoChartBarHorizontalMulti.vue")['default']
export const DemoChartBarMulti: typeof import("../components/demo-chart/DemoChartBarMulti.vue")['default']
export const DemoChartBarMultiIncome: typeof import("../components/demo-chart/DemoChartBarMultiIncome.vue")['default']
export const DemoChartBarRange: typeof import("../components/demo-chart/DemoChartBarRange.vue")['default']
export const DemoChartBarSocialChannels: typeof import("../components/demo-chart/DemoChartBarSocialChannels.vue")['default']
export const DemoChartBarStacked: typeof import("../components/demo-chart/DemoChartBarStacked.vue")['default']
export const DemoChartBubble: typeof import("../components/demo-chart/DemoChartBubble.vue")['default']
export const DemoChartDonut: typeof import("../components/demo-chart/DemoChartDonut.vue")['default']
export const DemoChartDonutExpenses: typeof import("../components/demo-chart/DemoChartDonutExpenses.vue")['default']
export const DemoChartLine: typeof import("../components/demo-chart/DemoChartLine.vue")['default']
export const DemoChartLineMulti: typeof import("../components/demo-chart/DemoChartLineMulti.vue")['default']
export const DemoChartLineMultiAlt: typeof import("../components/demo-chart/DemoChartLineMultiAlt.vue")['default']
export const DemoChartLineStep: typeof import("../components/demo-chart/DemoChartLineStep.vue")['default']
export const DemoChartPie: typeof import("../components/demo-chart/DemoChartPie.vue")['default']
export const DemoChartRadar: typeof import("../components/demo-chart/DemoChartRadar.vue")['default']
export const DemoChartRadial: typeof import("../components/demo-chart/DemoChartRadial.vue")['default']
export const DemoChartRadialGauge: typeof import("../components/demo-chart/DemoChartRadialGauge.vue")['default']
export const DemoChartRadialGaugeAlt: typeof import("../components/demo-chart/DemoChartRadialGaugeAlt.vue")['default']
export const DemoChartRadialMulti: typeof import("../components/demo-chart/DemoChartRadialMulti.vue")['default']
export const DemoChartScatter: typeof import("../components/demo-chart/DemoChartScatter.vue")['default']
export const DemoChartTimeline: typeof import("../components/demo-chart/DemoChartTimeline.vue")['default']
export const ExamplesComponentAccessExample: typeof import("../components/examples/ComponentAccessExample.vue")['default']
export const TairoLogo: typeof import("../layers/tairo/components/global/TairoLogo.vue")['default']
export const TairoLogoText: typeof import("../layers/tairo/components/global/TairoLogoText.vue")['default']
export const TairoToaster: typeof import("../layers/tairo/components/global/TairoToaster.vue")['default']
export const TairoTocAnchor: typeof import("../layers/tairo/components/global/TairoTocAnchor.vue")['default']
export const TairoCheckAnimated: typeof import("../layers/tairo/components/TairoCheckAnimated.vue")['default']
export const TairoContentWrapper: typeof import("../layers/tairo/components/TairoContentWrapper.vue")['default']
export const TairoContentWrapperTabbed: typeof import("../layers/tairo/components/TairoContentWrapperTabbed.vue")['default']
export const TairoError: typeof import("../layers/tairo/components/TairoError.vue")['default']
export const TairoFlexTable: typeof import("../layers/tairo/components/TairoFlexTable.vue")['default']
export const TairoFlexTableCell: typeof import("../layers/tairo/components/TairoFlexTableCell.vue")['default']
export const TairoFlexTableHeading: typeof import("../layers/tairo/components/TairoFlexTableHeading.vue")['default']
export const TairoFlexTableRow: typeof import("../layers/tairo/components/TairoFlexTableRow.vue")['default']
export const TairoFormGroup: typeof import("../layers/tairo/components/TairoFormGroup.vue")['default']
export const TairoFormSave: typeof import("../layers/tairo/components/TairoFormSave.vue")['default']
export const TairoImageZoom: typeof import("../layers/tairo/components/TairoImageZoom.vue")['default']
export const TairoModal: typeof import("../layers/tairo/components/TairoModal.vue")['default']
export const TairoPanels: typeof import("../layers/tairo/components/TairoPanels.vue")['default']
export const TairoPasswordStrength: typeof import("../layers/tairo/components/TairoPasswordStrength.vue")['default']
export const TairoPopover: typeof import("../layers/tairo/components/TairoPopover.vue")['default']
export const TairoPopoverContentDual: typeof import("../layers/tairo/components/TairoPopoverContentDual.vue")['default']
export const TairoPopoverContentHelp: typeof import("../layers/tairo/components/TairoPopoverContentHelp.vue")['default']
export const TairoPopoverContentMedia: typeof import("../layers/tairo/components/TairoPopoverContentMedia.vue")['default']
export const TairoSidebarLayoutConfig: typeof import("../layers/tairo/components/TairoSidebarLayout.config")['default']
export const TairoTable: typeof import("../layers/tairo/components/TairoTable.vue")['default']
export const TairoTableCell: typeof import("../layers/tairo/components/TairoTableCell.vue")['default']
export const TairoTableHeading: typeof import("../layers/tairo/components/TairoTableHeading.vue")['default']
export const TairoTableRow: typeof import("../layers/tairo/components/TairoTableRow.vue")['default']
export const TairoToc: typeof import("../layers/tairo/components/TairoToc.vue")['default']
export const TairoWelcome: typeof import("../layers/tairo/components/TairoWelcome.vue")['default']
export const AccountingDashboard: typeof import("../layers/accounting/components/AccountingDashboard.vue")['default']
export const JournalEntryForm: typeof import("../layers/accounting/components/JournalEntryForm.vue")['default']
export const BudgetChartsSection: typeof import("../layers/budget/components/BudgetChartsSection.vue")['default']
export const BudgetDashboard: typeof import("../layers/budget/components/BudgetDashboard.vue")['default']
export const BudgetIncomeForm: typeof import("../layers/budget/components/BudgetIncomeForm.vue")['default']
export const ExpensesSection: typeof import("../layers/budget/components/ExpensesSection.vue")['default']
export const GlobalFooter: typeof import("../layers/budget/components/GlobalFooter.vue")['default']
export const GlobalHeader: typeof import("../layers/budget/components/GlobalHeader.vue")['default']
export const IncomeSection: typeof import("../layers/budget/components/IncomeSection.vue")['default']
export const ChartsBudgetChartsSection: typeof import("../layers/budget/components/charts/BudgetChartsSection.vue")['default']
export const DashboardBudgetGoalsCard: typeof import("../layers/budget/components/dashboard/BudgetGoalsCard.vue")['default']
export const DashboardBudgetSummaryCard: typeof import("../layers/budget/components/dashboard/BudgetSummaryCard.vue")['default']
export const DashboardBudgetTrendsChart: typeof import("../layers/budget/components/dashboard/BudgetTrendsChart.vue")['default']
export const DashboardBudgetTypeSelector: typeof import("../layers/budget/components/dashboard/BudgetTypeSelector.vue")['default']
export const DashboardCategoryBreakdownCard: typeof import("../layers/budget/components/dashboard/CategoryBreakdownCard.vue")['default']
export const DashboardRecentTransactionsCard: typeof import("../layers/budget/components/dashboard/RecentTransactionsCard.vue")['default']
export const ExpensesMultipleExpenses: typeof import("../layers/budget/components/expenses/MultipleExpenses.vue")['default']
export const ExpensesOneTimeExpenses: typeof import("../layers/budget/components/expenses/OneTimeExpenses.vue")['default']
export const ExpensesRepeatedExpenses: typeof import("../layers/budget/components/expenses/RepeatedExpenses.vue")['default']
export const ForecastingCategories: typeof import("../layers/budget/components/forecasting/ForecastingCategories.vue")['default']
export const ForecastingChart: typeof import("../layers/budget/components/forecasting/ForecastingChart.vue")['default']
export const ForecastingControls: typeof import("../layers/budget/components/forecasting/ForecastingControls.vue")['default']
export const ForecastingScenarios: typeof import("../layers/budget/components/forecasting/ForecastingScenarios.vue")['default']
export const ForecastingSummary: typeof import("../layers/budget/components/forecasting/ForecastingSummary.vue")['default']
export const IncomesMultipleIncomes: typeof import("../layers/budget/components/incomes/MultipleIncomes.vue")['default']
export const IncomesOneTimeIncomes: typeof import("../layers/budget/components/incomes/OneTimeIncomes.vue")['default']
export const IncomesRepeatedIncomes: typeof import("../layers/budget/components/incomes/RepeatedIncomes.vue")['default']
export const PlannerBudgetComparisonCard: typeof import("../layers/budget/components/planner/BudgetComparisonCard.vue")['default']
export const PlannerMonthlyBudgetCard: typeof import("../layers/budget/components/planner/MonthlyBudgetCard.vue")['default']
export const QuickBudgetCategoryBreakdown: typeof import("../layers/budget/components/quick-budget/QuickBudgetCategoryBreakdown.vue")['default']
export const QuickBudgetEntryForm: typeof import("../layers/budget/components/quick-budget/QuickBudgetEntryForm.vue")['default']
export const QuickBudgetExpensesList: typeof import("../layers/budget/components/quick-budget/QuickBudgetExpensesList.vue")['default']
export const QuickBudgetGoals: typeof import("../layers/budget/components/quick-budget/QuickBudgetGoals.vue")['default']
export const QuickBudgetIncomeList: typeof import("../layers/budget/components/quick-budget/QuickBudgetIncomeList.vue")['default']
export const QuickBudgetSummaryCard: typeof import("../layers/budget/components/quick-budget/QuickBudgetSummaryCard.vue")['default']
export const QuickBudgetTransactionsList: typeof import("../layers/budget/components/quick-budget/QuickBudgetTransactionsList.vue")['default']
export const StatsSection: typeof import("../layers/budget/components/stats/StatsSection.vue")['default']
export const TransactionsListCard: typeof import("../layers/budget/components/transactions/TransactionsListCard.vue")['default']
export const CompaniesDashboard: typeof import("../layers/companies/components/CompaniesDashboard.vue")['default']
export const CompanyDashboard: typeof import("../layers/companies/components/CompanyDashboard.vue")['default']
export const CompanyForm: typeof import("../layers/companies/components/CompanyForm.vue")['default']
export const ClientsClientCard: typeof import("../layers/companies/components/clients/ClientCard.vue")['default']
export const ClientsTable: typeof import("../layers/companies/components/clients/ClientsTable.vue")['default']
export const ComplianceAudits: typeof import("../layers/companies/components/compliance/ComplianceAudits.vue")['default']
export const ComplianceOverview: typeof import("../layers/companies/components/compliance/ComplianceOverview.vue")['default']
export const ComplianceRequirements: typeof import("../layers/companies/components/compliance/ComplianceRequirements.vue")['default']
export const DocumentsDocumentCard: typeof import("../layers/companies/components/documents/DocumentCard.vue")['default']
export const RiskMitigationPlans: typeof import("../layers/companies/components/risk/MitigationPlans.vue")['default']
export const RiskDashboard: typeof import("../layers/companies/components/risk/RiskDashboard.vue")['default']
export const RiskRegister: typeof import("../layers/companies/components/risk/RiskRegister.vue")['default']
export const SettingsGeneralSettings: typeof import("../layers/companies/components/settings/GeneralSettings.vue")['default']
export const SettingsIntegrationSettings: typeof import("../layers/companies/components/settings/IntegrationSettings.vue")['default']
export const SettingsNotificationSettings: typeof import("../layers/companies/components/settings/NotificationSettings.vue")['default']
export const SettingsPermissionSettings: typeof import("../layers/companies/components/settings/PermissionSettings.vue")['default']
export const SuppliersSupplierCard: typeof import("../layers/companies/components/suppliers/SupplierCard.vue")['default']
export const SuppliersSupplierDocuments: typeof import("../layers/companies/components/suppliers/SupplierDocuments.vue")['default']
export const SuppliersSupplierOrders: typeof import("../layers/companies/components/suppliers/SupplierOrders.vue")['default']
export const SuppliersSupplierOverview: typeof import("../layers/companies/components/suppliers/SupplierOverview.vue")['default']
export const SuppliersSupplierPerformance: typeof import("../layers/companies/components/suppliers/SupplierPerformance.vue")['default']
export const SuppliersSupplierPerformanceMetrics: typeof import("../layers/companies/components/suppliers/SupplierPerformanceMetrics.vue")['default']
export const SuppliersSupplierTransactions: typeof import("../layers/companies/components/suppliers/SupplierTransactions.vue")['default']
export const SuppliersTable: typeof import("../layers/companies/components/suppliers/SuppliersTable.vue")['default']
export const WorkforceCapacityPlanning: typeof import("../layers/companies/components/workforce/CapacityPlanning.vue")['default']
export const WorkforceSkillsGapAnalysis: typeof import("../layers/companies/components/workforce/SkillsGapAnalysis.vue")['default']
export const WorkforceOverview: typeof import("../layers/companies/components/workforce/WorkforceOverview.vue")['default']
export const BaseLayerPage: typeof import("../layers/core/components/BaseLayerPage.vue")['default']
export const BusinessRuleModal: typeof import("../layers/core/components/BusinessRuleModal.vue")['default']
export const CoreDashboard: typeof import("../layers/core/components/CoreDashboard.vue")['default']
export const ActivityFilters: typeof import("../layers/core/components/activity/ActivityFilters.vue")['default']
export const ActivityLogDetails: typeof import("../layers/core/components/activity/ActivityLogDetails.vue")['default']
export const ActivityLogItem: typeof import("../layers/core/components/activity/ActivityLogItem.vue")['default']
export const ActivityLogList: typeof import("../layers/core/components/activity/ActivityLogList.vue")['default']
export const AiDecisionCard: typeof import("../layers/core/components/ai/DecisionCard.vue")['default']
export const AiDecisionDetailModal: typeof import("../layers/core/components/ai/DecisionDetailModal.vue")['default']
export const ApiEndpointCard: typeof import("../layers/core/components/api/ApiEndpointCard.vue")['default']
export const ApiEndpointDetailModal: typeof import("../layers/core/components/api/ApiEndpointDetailModal.vue")['default']
export const AutomationRuleCard: typeof import("../layers/core/components/automation/RuleCard.vue")['default']
export const BrandAssetCard: typeof import("../layers/core/components/brand/BrandAssetCard.vue")['default']
export const ChartsBarChart: typeof import("../layers/core/components/charts/BarChart.vue")['default']
export const ChartsLineChart: typeof import("../layers/core/components/charts/LineChart.vue")['default']
export const ChartsPieChart: typeof import("../layers/core/components/charts/PieChart.vue")['default']
export const ChartsRadialChart: typeof import("../layers/core/components/charts/RadialChart.vue")['default']
export const DashboardActivityCard: typeof import("../layers/core/components/dashboard/ActivityCard.vue")['default']
export const DashboardMetricCard: typeof import("../layers/core/components/dashboard/MetricCard.vue")['default']
export const DashboardModuleUsageCard: typeof import("../layers/core/components/dashboard/ModuleUsageCard.vue")['default']
export const DashboardNotificationsCard: typeof import("../layers/core/components/dashboard/NotificationsCard.vue")['default']
export const DashboardSystemHealthCard: typeof import("../layers/core/components/dashboard/SystemHealthCard.vue")['default']
export const DashboardUserActivityCard: typeof import("../layers/core/components/dashboard/UserActivityCard.vue")['default']
export const DatabaseConnectionBuilderForm: typeof import("../layers/core/components/database/ConnectionBuilderForm.vue")['default']
export const DatabaseConnectionPoolMonitor: typeof import("../layers/core/components/database/ConnectionPoolMonitor.vue")['default']
export const DatabaseConnectionStringForm: typeof import("../layers/core/components/database/ConnectionStringForm.vue")['default']
export const DatabaseExplorer: typeof import("../layers/core/components/database/DatabaseExplorer.vue")['default']
export const DatabaseHealth: typeof import("../layers/core/components/database/DatabaseHealth.vue")['default']
export const DatabaseSchema: typeof import("../layers/core/components/database/DatabaseSchema.vue")['default']
export const DatabaseTerminalWindow: typeof import("../layers/core/components/database/TerminalWindow.vue")['default']
export const DocumentsDocumentFilters: typeof import("../layers/core/components/documents/DocumentFilters.vue")['default']
export const DocumentsDocumentUploadModal: typeof import("../layers/core/components/documents/DocumentUploadModal.vue")['default']
export const EventsEventCard: typeof import("../layers/core/components/events/EventCard.vue")['default']
export const EventsEventDetailModal: typeof import("../layers/core/components/events/EventDetailModal.vue")['default']
export const HealthPerformanceMetricsCard: typeof import("../layers/core/components/health/PerformanceMetricsCard.vue")['default']
export const HealthResourceUsageCard: typeof import("../layers/core/components/health/ResourceUsageCard.vue")['default']
export const HealthServiceStatusCard: typeof import("../layers/core/components/health/ServiceStatusCard.vue")['default']
export const LegalDocumentCard: typeof import("../layers/core/components/legal/LegalDocumentCard.vue")['default']
export const MlDatasetCard: typeof import("../layers/core/components/ml/DatasetCard.vue")['default']
export const MlTrainingDataUploader: typeof import("../layers/core/components/ml/TrainingDataUploader.vue")['default']
export const MlTrainingFormatGuide: typeof import("../layers/core/components/ml/TrainingFormatGuide.vue")['default']
export const ModulesModuleCard: typeof import("../layers/core/components/modules/ModuleCard.vue")['default']
export const ModulesModuleDetailModal: typeof import("../layers/core/components/modules/ModuleDetailModal.vue")['default']
export const MonitoringAlertDetailsModal: typeof import("../layers/core/components/monitoring/AlertDetailsModal.vue")['default']
export const MonitoringAlertsListCard: typeof import("../layers/core/components/monitoring/AlertsListCard.vue")['default']
export const MonitoringAlertsSummaryCard: typeof import("../layers/core/components/monitoring/AlertsSummaryCard.vue")['default']
export const MonitoringApiPerformanceCard: typeof import("../layers/core/components/monitoring/ApiPerformanceCard.vue")['default']
export const MonitoringDatabaseResourcesCard: typeof import("../layers/core/components/monitoring/DatabaseResourcesCard.vue")['default']
export const MonitoringErrorDetailsModal: typeof import("../layers/core/components/monitoring/ErrorDetailsModal.vue")['default']
export const MonitoringErrorLogsCard: typeof import("../layers/core/components/monitoring/ErrorLogsCard.vue")['default']
export const MonitoringErrorSummaryCard: typeof import("../layers/core/components/monitoring/ErrorSummaryCard.vue")['default']
export const MonitoringPageLoadTimesCard: typeof import("../layers/core/components/monitoring/PageLoadTimesCard.vue")['default']
export const MonitoringPerformanceMetricsCard: typeof import("../layers/core/components/monitoring/PerformanceMetricsCard.vue")['default']
export const MonitoringResourceOverviewCard: typeof import("../layers/core/components/monitoring/ResourceOverviewCard.vue")['default']
export const MonitoringServerResourcesCard: typeof import("../layers/core/components/monitoring/ServerResourcesCard.vue")['default']
export const PoliciesPolicyCard: typeof import("../layers/core/components/policies/PolicyCard.vue")['default']
export const PoliciesPolicyCategoryFilter: typeof import("../layers/core/components/policies/PolicyCategoryFilter.vue")['default']
export const PoliciesPolicyDetailModal: typeof import("../layers/core/components/policies/PolicyDetailModal.vue")['default']
export const RulesCategoryCard: typeof import("../layers/core/components/rules/CategoryCard.vue")['default']
export const RulesRuleCard: typeof import("../layers/core/components/rules/RuleCard.vue")['default']
export const RulesRuleDetailModal: typeof import("../layers/core/components/rules/RuleDetailModal.vue")['default']
export const RulesRuleFilters: typeof import("../layers/core/components/rules/RuleFilters.vue")['default']
export const RulesTemplateCard: typeof import("../layers/core/components/rules/TemplateCard.vue")['default']
export const RulesTemplateDetailModal: typeof import("../layers/core/components/rules/TemplateDetailModal.vue")['default']
export const RulesValidationTester: typeof import("../layers/core/components/rules/ValidationTester.vue")['default']
export const SecurityAuditLogEntry: typeof import("../layers/core/components/security/AuditLogEntry.vue")['default']
export const SecurityAuditLogFilters: typeof import("../layers/core/components/security/AuditLogFilters.vue")['default']
export const SecurityEditRoleModal: typeof import("../layers/core/components/security/EditRoleModal.vue")['default']
export const SecurityEditUserRolesModal: typeof import("../layers/core/components/security/EditUserRolesModal.vue")['default']
export const SecurityRolePermissionsCard: typeof import("../layers/core/components/security/RolePermissionsCard.vue")['default']
export const SecurityUserRolesTable: typeof import("../layers/core/components/security/UserRolesTable.vue")['default']
export const SettingsAppearanceSettingsCard: typeof import("../layers/core/components/settings/AppearanceSettingsCard.vue")['default']
export const SettingsBackupHistoryCard: typeof import("../layers/core/components/settings/BackupHistoryCard.vue")['default']
export const SettingsBackupSettingsCard: typeof import("../layers/core/components/settings/BackupSettingsCard.vue")['default']
export const SettingsBackupStatusCard: typeof import("../layers/core/components/settings/BackupStatusCard.vue")['default']
export const SettingsDeleteConfirmationModal: typeof import("../layers/core/components/settings/DeleteConfirmationModal.vue")['default']
export const SettingsEnvironmentInfoCard: typeof import("../layers/core/components/settings/EnvironmentInfoCard.vue")['default']
export const SettingsEnvironmentVariablesCard: typeof import("../layers/core/components/settings/EnvironmentVariablesCard.vue")['default']
export const SettingsFormatSettingsCard: typeof import("../layers/core/components/settings/FormatSettingsCard.vue")['default']
export const SettingsGeneralSettingsCard: typeof import("../layers/core/components/settings/GeneralSettingsCard.vue")['default']
export const SettingsLanguageSettingsCard: typeof import("../layers/core/components/settings/LanguageSettingsCard.vue")['default']
export const SettingsPerformanceSettingsCard: typeof import("../layers/core/components/settings/PerformanceSettingsCard.vue")['default']
export const SettingsQuickActionsCard: typeof import("../layers/core/components/settings/QuickActionsCard.vue")['default']
export const SettingsRestoreConfirmationModal: typeof import("../layers/core/components/settings/RestoreConfirmationModal.vue")['default']
export const SettingsRestoreFromFileModal: typeof import("../layers/core/components/settings/RestoreFromFileModal.vue")['default']
export const SettingsRestoreSettingsCard: typeof import("../layers/core/components/settings/RestoreSettingsCard.vue")['default']
export const WebhooksWebhookCard: typeof import("../layers/core/components/webhooks/WebhookCard.vue")['default']
export const WebhooksWebhookDetailModal: typeof import("../layers/core/components/webhooks/WebhookDetailModal.vue")['default']
export const EmployeeFilters: typeof import("../layers/hr/components/EmployeeFilters.vue")['default']
export const EmployeeForm: typeof import("../layers/hr/components/EmployeeForm.vue")['default']
export const EmployeeList: typeof import("../layers/hr/components/EmployeeList.vue")['default']
export const HrDashboard: typeof import("../layers/hr/components/HrDashboard.vue")['default']
export const WorkerForm: typeof import("../layers/hr/components/WorkerForm.vue")['default']
export const AttendanceRecordsTab: typeof import("../layers/hr/components/attendance/AttendanceRecordsTab.vue")['default']
export const AttendanceReportsTab: typeof import("../layers/hr/components/attendance/AttendanceReportsTab.vue")['default']
export const AttendanceDailyAttendanceTab: typeof import("../layers/hr/components/attendance/DailyAttendanceTab.vue")['default']
export const BenefitsBenefitPlansTab: typeof import("../layers/hr/components/benefits/BenefitPlansTab.vue")['default']
export const BenefitsBenefitSettingsTab: typeof import("../layers/hr/components/benefits/BenefitSettingsTab.vue")['default']
export const BenefitsEmployeeBenefitsTab: typeof import("../layers/hr/components/benefits/EmployeeBenefitsTab.vue")['default']
export const BonusesAllBonusesTab: typeof import("../layers/hr/components/bonuses/AllBonusesTab.vue")['default']
export const BonusesBonusReportsTab: typeof import("../layers/hr/components/bonuses/BonusReportsTab.vue")['default']
export const BonusesEmployeeBonusesTab: typeof import("../layers/hr/components/bonuses/EmployeeBonusesTab.vue")['default']
export const CareerPathDetails: typeof import("../layers/hr/components/career/CareerPathDetails.vue")['default']
export const CareerPathFormModal: typeof import("../layers/hr/components/career/CareerPathFormModal.vue")['default']
export const CareerPathOverview: typeof import("../layers/hr/components/career/CareerPathOverview.vue")['default']
export const DepartmentsDepartmentCard: typeof import("../layers/hr/components/departments/DepartmentCard.vue")['default']
export const DocumentsCompanyPoliciesTab: typeof import("../layers/hr/components/documents/CompanyPoliciesTab.vue")['default']
export const DocumentsTab: typeof import("../layers/hr/components/documents/DocumentsTab.vue")['default']
export const DocumentsEmployeeDocumentsTab: typeof import("../layers/hr/components/documents/EmployeeDocumentsTab.vue")['default']
export const EmployeesDocumentUploadForm: typeof import("../layers/hr/components/employees/DocumentUploadForm.vue")['default']
export const EmployeesEmployeeDocuments: typeof import("../layers/hr/components/employees/EmployeeDocuments.vue")['default']
export const EmployeesEmployeeLeave: typeof import("../layers/hr/components/employees/EmployeeLeave.vue")['default']
export const EmployeesEmployeeOverview: typeof import("../layers/hr/components/employees/EmployeeOverview.vue")['default']
export const EmployeesEmployeePayroll: typeof import("../layers/hr/components/employees/EmployeePayroll.vue")['default']
export const EmployeesEmployeePerformance: typeof import("../layers/hr/components/employees/EmployeePerformance.vue")['default']
export const EmployeesEmployeeProfileCard: typeof import("../layers/hr/components/employees/EmployeeProfileCard.vue")['default']
export const LeaveBalancesTab: typeof import("../layers/hr/components/leave/LeaveBalancesTab.vue")['default']
export const LeaveCalendarTab: typeof import("../layers/hr/components/leave/LeaveCalendarTab.vue")['default']
export const LeaveRequestsTab: typeof import("../layers/hr/components/leave/LeaveRequestsTab.vue")['default']
export const LeaveSettingsTab: typeof import("../layers/hr/components/leave/LeaveSettingsTab.vue")['default']
export const PayrollRunTab: typeof import("../layers/hr/components/payroll/PayrollRunTab.vue")['default']
export const PayrollPayslipsTab: typeof import("../layers/hr/components/payroll/PayslipsTab.vue")['default']
export const PayrollTaxSettingsTab: typeof import("../layers/hr/components/payroll/TaxSettingsTab.vue")['default']
export const PerformanceEmployeePerformanceTab: typeof import("../layers/hr/components/performance/EmployeePerformanceTab.vue")['default']
export const PerformanceAnalyticsTab: typeof import("../layers/hr/components/performance/PerformanceAnalyticsTab.vue")['default']
export const PerformanceReviewsTab: typeof import("../layers/hr/components/performance/PerformanceReviewsTab.vue")['default']
export const ShiftsShiftAssignmentsTab: typeof import("../layers/hr/components/shifts/ShiftAssignmentsTab.vue")['default']
export const ShiftsShiftScheduleTab: typeof import("../layers/hr/components/shifts/ShiftScheduleTab.vue")['default']
export const ShiftsShiftTypesTab: typeof import("../layers/hr/components/shifts/ShiftTypesTab.vue")['default']
export const TrainingCertificationsTab: typeof import("../layers/hr/components/training/CertificationsTab.vue")['default']
export const TrainingEmployeeTrainingTab: typeof import("../layers/hr/components/training/EmployeeTrainingTab.vue")['default']
export const TrainingEnrollEmployeesModal: typeof import("../layers/hr/components/training/EnrollEmployeesModal.vue")['default']
export const TrainingDetailsModal: typeof import("../layers/hr/components/training/TrainingDetailsModal.vue")['default']
export const TrainingFormModal: typeof import("../layers/hr/components/training/TrainingFormModal.vue")['default']
export const TrainingProgramCard: typeof import("../layers/hr/components/training/TrainingProgramCard.vue")['default']
export const TrainingProgramsTab: typeof import("../layers/hr/components/training/TrainingProgramsTab.vue")['default']
export const ProductionDashboard: typeof import("../layers/production/components/ProductionDashboard.vue")['default']
export const DashboardTaskStatusChart: typeof import("../layers/production/components/dashboard/TaskStatusChart.vue")['default']
export const DocumentsUploadDocumentModal: typeof import("../layers/production/components/documents/UploadDocumentModal.vue")['default']
export const DocumentsViewDocumentModal: typeof import("../layers/production/components/documents/ViewDocumentModal.vue")['default']
export const QualityChecklistCard: typeof import("../layers/production/components/quality/ChecklistCard.vue")['default']
export const QualityChecklistDetailModal: typeof import("../layers/production/components/quality/ChecklistDetailModal.vue")['default']
export const QualityChecklistItem: typeof import("../layers/production/components/quality/ChecklistItem.vue")['default']
export const QualityNewChecklistModal: typeof import("../layers/production/components/quality/NewChecklistModal.vue")['default']
export const QualityPhotoCard: typeof import("../layers/production/components/quality/PhotoCard.vue")['default']
export const QualityPhotoDetailModal: typeof import("../layers/production/components/quality/PhotoDetailModal.vue")['default']
export const QualityUploadPhotoModal: typeof import("../layers/production/components/quality/UploadPhotoModal.vue")['default']
export const ReportsBarChart: typeof import("../layers/production/components/reports/BarChart.vue")['default']
export const ReportsLineChart: typeof import("../layers/production/components/reports/LineChart.vue")['default']
export const ReportsMetricCard: typeof import("../layers/production/components/reports/MetricCard.vue")['default']
export const ReportsPieChart: typeof import("../layers/production/components/reports/PieChart.vue")['default']
export const ReportsReportCard: typeof import("../layers/production/components/reports/ReportCard.vue")['default']
export const ResourcesEquipmentCard: typeof import("../layers/production/components/resources/EquipmentCard.vue")['default']
export const ResourcesEquipmentDetailModal: typeof import("../layers/production/components/resources/EquipmentDetailModal.vue")['default']
export const ResourcesMaterialCard: typeof import("../layers/production/components/resources/MaterialCard.vue")['default']
export const ResourcesMaterialDetailModal: typeof import("../layers/production/components/resources/MaterialDetailModal.vue")['default']
export const ResourcesNewEquipmentModal: typeof import("../layers/production/components/resources/NewEquipmentModal.vue")['default']
export const ResourcesNewMaterialModal: typeof import("../layers/production/components/resources/NewMaterialModal.vue")['default']
export const ResourcesNewWorkerModal: typeof import("../layers/production/components/resources/NewWorkerModal.vue")['default']
export const ResourcesWorkforceCard: typeof import("../layers/production/components/resources/WorkforceCard.vue")['default']
export const ResourcesWorkforceDetailModal: typeof import("../layers/production/components/resources/WorkforceDetailModal.vue")['default']
export const StatisticsProjectMetricsChart: typeof import("../layers/production/components/statistics/ProjectMetricsChart.vue")['default']
export const StatisticsProjectStatusChart: typeof import("../layers/production/components/statistics/ProjectStatusChart.vue")['default']
export const StatisticsTaskCompletionChart: typeof import("../layers/production/components/statistics/TaskCompletionChart.vue")['default']
export const TasksNewTaskModal: typeof import("../layers/production/components/tasks/NewTaskModal.vue")['default']
export const TasksTaskBoard: typeof import("../layers/production/components/tasks/TaskBoard.vue")['default']
export const TasksTaskCard: typeof import("../layers/production/components/tasks/TaskCard.vue")['default']
export const TasksTaskColumn: typeof import("../layers/production/components/tasks/TaskColumn.vue")['default']
export const TasksTaskDetailModal: typeof import("../layers/production/components/tasks/TaskDetailModal.vue")['default']
export const TimelineGanttChart: typeof import("../layers/production/components/timeline/GanttChart.vue")['default']
export const RecruitmentSidebarItem: typeof import("../layers/recruitment/components/RecruitmentSidebarItem.vue")['default']
export const AdminLanguageManager: typeof import("../layers/recruitment/components/admin/LanguageManager.vue")['default']
export const AdminTranslationsManager: typeof import("../layers/recruitment/components/admin/TranslationsManager.vue")['default']
export const FormsFormBuilderFieldEditor: typeof import("../layers/recruitment/components/forms/FormBuilderFieldEditor.vue")['default']
export const FormsFormBuilderFieldList: typeof import("../layers/recruitment/components/forms/FormBuilderFieldList.vue")['default']
export const FormsFormBuilderPreview: typeof import("../layers/recruitment/components/forms/FormBuilderPreview.vue")['default']
export const FormsFormBuilderPreviewField: typeof import("../layers/recruitment/components/forms/FormBuilderPreviewField.vue")['default']
export const FormsFormBuilderSettings: typeof import("../layers/recruitment/components/forms/FormBuilderSettings.vue")['default']
export const FormsFormBuilderSidebar: typeof import("../layers/recruitment/components/forms/FormBuilderSidebar.vue")['default']
export const FormsFormBuilderStepEditor: typeof import("../layers/recruitment/components/forms/FormBuilderStepEditor.vue")['default']
export const FormsPublicFormField: typeof import("../layers/recruitment/components/forms/PublicFormField.vue")['default']
export const WorkforceFormAdditionalInfoStep: typeof import("../layers/recruitment/components/workforce-form/AdditionalInfoStep.vue")['default']
export const WorkforceFormConfirmationStep: typeof import("../layers/recruitment/components/workforce-form/ConfirmationStep.vue")['default']
export const WorkforceFormIntroductionStep: typeof import("../layers/recruitment/components/workforce-form/IntroductionStep.vue")['default']
export const WorkforceFormLanguageSelector: typeof import("../layers/recruitment/components/workforce-form/LanguageSelector.vue")['default']
export const WorkforceFormPersonalInfoStep: typeof import("../layers/recruitment/components/workforce-form/PersonalInfoStep.vue")['default']
export const WorkforceFormProfessionalExperienceStep: typeof import("../layers/recruitment/components/workforce-form/ProfessionalExperienceStep.vue")['default']
export const WorkforceFormQualificationsStep: typeof import("../layers/recruitment/components/workforce-form/QualificationsStep.vue")['default']
export const WorkforceFormReferencesStep: typeof import("../layers/recruitment/components/workforce-form/ReferencesStep.vue")['default']
export const WorkforceFormSpecialtiesStep: typeof import("../layers/recruitment/components/workforce-form/SpecialtiesStep.vue")['default']
export const WorkforceAssignmentDetailsModal: typeof import("../layers/recruitment/components/workforce/AssignmentDetailsModal.vue")['default']
export const WorkforceAssignmentFilters: typeof import("../layers/recruitment/components/workforce/AssignmentFilters.vue")['default']
export const WorkforceAssignmentsPagination: typeof import("../layers/recruitment/components/workforce/AssignmentsPagination.vue")['default']
export const WorkforceJobDetailsModal: typeof import("../layers/recruitment/components/workforce/JobDetailsModal.vue")['default']
export const WorkforceJobFilters: typeof import("../layers/recruitment/components/workforce/JobFilters.vue")['default']
export const WorkforceJobFormModal: typeof import("../layers/recruitment/components/workforce/JobFormModal.vue")['default']
export const WorkforceJobsPagination: typeof import("../layers/recruitment/components/workforce/JobsPagination.vue")['default']
export const WorkforceMatchDetailsModal: typeof import("../layers/recruitment/components/workforce/MatchDetailsModal.vue")['default']
export const WorkforceMatchFilters: typeof import("../layers/recruitment/components/workforce/MatchFilters.vue")['default']
export const WorkforceMatchesPagination: typeof import("../layers/recruitment/components/workforce/MatchesPagination.vue")['default']
export const WorkforceWorkerDetailsModal: typeof import("../layers/recruitment/components/workforce/WorkerDetailsModal.vue")['default']
export const WorkforceWorkerFormModal: typeof import("../layers/recruitment/components/workforce/WorkerFormModal.vue")['default']
export const WorkforceWorkersPagination: typeof import("../layers/recruitment/components/workforce/WorkersPagination.vue")['default']
export const TairoSidebarBurger: typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarBurger.vue")['default']
export const TairoSidebarCircularMenu: typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarCircularMenu.vue")['default']
export const TairoSidebarLayout: typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarLayout.vue")['default']
export const TairoSidebarNavigationItem: typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarNavigationItem.vue")['default']
export const TairoSidebarNavigationPanel: typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarNavigationPanel.vue")['default']
export const TairoSidebarToolbar: typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarToolbar.vue")['default']
export const TairoSidebarTools: typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarTools.vue")['default']
export const TairoSubsidebar: typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebar.vue")['default']
export const TairoSubsidebarHeader: typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarHeader.vue")['default']
export const TairoSubsidebarMenu: typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenu.vue")['default']
export const TairoSubsidebarMenuCollapseLinks: typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuCollapseLinks.vue")['default']
export const TairoSubsidebarMenuDivider: typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuDivider.vue")['default']
export const TairoSubsidebarMenuLink: typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuLink.vue")['default']
export const LandingBenefits: typeof import("../layers/landing/components/LandingBenefits.vue")['default']
export const LandingContent: typeof import("../layers/landing/components/LandingContent.vue")['default']
export const LandingCta: typeof import("../layers/landing/components/LandingCta.vue")['default']
export const LandingCustomizer: typeof import("../layers/landing/components/LandingCustomizer.vue")['default']
export const LandingDemoLink: typeof import("../layers/landing/components/LandingDemoLink.vue")['default']
export const LandingDemos: typeof import("../layers/landing/components/LandingDemos.vue")['default']
export const LandingFeatures: typeof import("../layers/landing/components/LandingFeatures.vue")['default']
export const LandingFeaturesTile: typeof import("../layers/landing/components/LandingFeaturesTile.vue")['default']
export const LandingFooter: typeof import("../layers/landing/components/LandingFooter.vue")['default']
export const LandingHero: typeof import("../layers/landing/components/LandingHero.vue")['default']
export const LandingHeroMockup: typeof import("../layers/landing/components/LandingHeroMockup.vue")['default']
export const LandingLayers: typeof import("../layers/landing/components/LandingLayers.vue")['default']
export const LandingLayersBox: typeof import("../layers/landing/components/LandingLayersBox.vue")['default']
export const LandingLayout: typeof import("../layers/landing/components/LandingLayout.vue")['default']
export const LandingLayouts: typeof import("../layers/landing/components/LandingLayouts.vue")['default']
export const LandingNavbar: typeof import("../layers/landing/components/LandingNavbar.vue")['default']
export const MockupFollowersCompact: typeof import("../layers/landing/components/MockupFollowersCompact.vue")['default']
export const MockupInboxMessage: typeof import("../layers/landing/components/MockupInboxMessage.vue")['default']
export const MockupInfoBadges: typeof import("../layers/landing/components/MockupInfoBadges.vue")['default']
export const MockupProgressCircle: typeof import("../layers/landing/components/MockupProgressCircle.vue")['default']
export const MockupTeamSearchCompact: typeof import("../layers/landing/components/MockupTeamSearchCompact.vue")['default']
export const MockupVideoCompact: typeof import("../layers/landing/components/MockupVideoCompact.vue")['default']
export const SVGMorph: typeof import("../layers/landing/components/SVGMorph.vue")['default']
export const TairoIconnavCircularMenu: typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavCircularMenu.vue")['default']
export const TairoIconnavFooter: typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavFooter.vue")['default']
export const TairoIconnavLayout: typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavLayout.vue")['default']
export const TairoIconnavNavigation: typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavNavigation.vue")['default']
export const BaseAccordion: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAccordion.vue")['default']
export const BaseAvatar: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAvatar.vue")['default']
export const BaseAvatarGroup: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAvatarGroup.vue")['default']
export const BaseBreadcrumb: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseBreadcrumb.vue")['default']
export const BaseButton: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButton.vue")['default']
export const BaseButtonAction: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonAction.vue")['default']
export const BaseButtonClose: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonClose.vue")['default']
export const BaseButtonGroup: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonGroup.vue")['default']
export const BaseButtonIcon: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonIcon.vue")['default']
export const BaseCard: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseCard.vue")['default']
export const BaseDropdown: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdown.vue")['default']
export const BaseDropdownDivider: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdownDivider.vue")['default']
export const BaseDropdownItem: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdownItem.vue")['default']
export const BaseFocusLoop: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseFocusLoop.vue")['default']
export const BaseHeading: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseHeading.vue")['default']
export const BaseIconBox: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseIconBox.vue")['default']
export const BaseKbd: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseKbd.vue")['default']
export const BaseLink: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseLink.vue")['default']
export const BaseList: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseList.vue")['default']
export const BaseListItem: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseListItem.vue")['default']
export const BaseMessage: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseMessage.vue")['default']
export const BasePagination: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePagination.vue")['default']
export const BaseParagraph: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseParagraph.vue")['default']
export const BasePlaceholderPage: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePlaceholderPage.vue")['default']
export const BasePlaceload: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePlaceload.vue")['default']
export const BaseProgress: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProgress.vue")['default']
export const BaseProgressCircle: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProgressCircle.vue")['default']
export const BaseProse: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProse.vue")['default']
export const BaseSnack: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseSnack.vue")['default']
export const BaseTabSlider: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTabSlider.vue")['default']
export const BaseTabs: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTabs.vue")['default']
export const BaseTag: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTag.vue")['default']
export const BaseText: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseText.vue")['default']
export const BaseThemeSwitch: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseThemeSwitch.vue")['default']
export const BaseThemeToggle: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseThemeToggle.vue")['default']
export const IconCheck: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconCheck.vue")['default']
export const IconCheckCircle: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconCheckCircle.vue")['default']
export const IconChevronDown: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconChevronDown.vue")['default']
export const IconClose: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconClose.vue")['default']
export const IconIndeterminate: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconIndeterminate.vue")['default']
export const IconMinus: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconMinus.vue")['default']
export const IconMoon: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconMoon.vue")['default']
export const IconPlus: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconPlus.vue")['default']
export const IconSun: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconSun.vue")['default']
export const BaseAutocomplete: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseAutocomplete.vue")['default']
export const BaseAutocompleteItem: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseAutocompleteItem.vue")['default']
export const BaseCheckbox: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckbox.vue")['default']
export const BaseCheckboxAnimated: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckboxAnimated.vue")['default']
export const BaseCheckboxHeadless: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckboxHeadless.vue")['default']
export const BaseFullscreenDropfile: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseFullscreenDropfile.vue")['default']
export const BaseInput: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInput.vue")['default']
export const BaseInputFile: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputFile.vue")['default']
export const BaseInputFileHeadless: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputFileHeadless.vue")['default']
export const BaseInputHelpText: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputHelpText.vue")['default']
export const BaseInputNumber: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputNumber.vue")['default']
export const BaseListbox: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseListbox.vue")['default']
export const BaseListboxItem: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseListboxItem.vue")['default']
export const BaseRadio: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseRadio.vue")['default']
export const BaseRadioHeadless: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseRadioHeadless.vue")['default']
export const BaseSelect: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSelect.vue")['default']
export const BaseSwitchBall: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSwitchBall.vue")['default']
export const BaseSwitchThin: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSwitchThin.vue")['default']
export const BaseTextarea: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTextarea.vue")['default']
export const BaseTreeSelect: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTreeSelect.vue")['default']
export const BaseTreeSelectItem: typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTreeSelectItem.vue")['default']
export const NuxtWelcome: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
export const ClientOnly: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtRouteAnnouncer: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
export const NuxtPicture: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
export const IonAnimation: typeof import("../node_modules/.pnpm/@nuxtjs+ionic@0.16.0_@stencil+core@4.28.2_magicast@0.3.5_vue-router@4.5.0_vue@3.5.13_typescri_kvogt2gdziafhlhhzxvd6o57ta/node_modules/@nuxtjs/ionic/dist/runtime/components/IonAnimation.vue")['default']
export const IonAccordion: typeof import("@ionic/vue")['IonAccordion']
export const IonAccordionGroup: typeof import("@ionic/vue")['IonAccordionGroup']
export const IonActionSheet: typeof import("@ionic/vue")['IonActionSheet']
export const IonAlert: typeof import("@ionic/vue")['IonAlert']
export const IonApp: typeof import("@ionic/vue")['IonApp']
export const IonAvatar: typeof import("@ionic/vue")['IonAvatar']
export const IonBackButton: typeof import("@ionic/vue")['IonBackButton']
export const IonBackdrop: typeof import("@ionic/vue")['IonBackdrop']
export const IonBadge: typeof import("@ionic/vue")['IonBadge']
export const IonBreadcrumb: typeof import("@ionic/vue")['IonBreadcrumb']
export const IonBreadcrumbs: typeof import("@ionic/vue")['IonBreadcrumbs']
export const IonButton: typeof import("@ionic/vue")['IonButton']
export const IonButtons: typeof import("@ionic/vue")['IonButtons']
export const IonCard: typeof import("@ionic/vue")['IonCard']
export const IonCardContent: typeof import("@ionic/vue")['IonCardContent']
export const IonCardHeader: typeof import("@ionic/vue")['IonCardHeader']
export const IonCardSubtitle: typeof import("@ionic/vue")['IonCardSubtitle']
export const IonCardTitle: typeof import("@ionic/vue")['IonCardTitle']
export const IonCheckbox: typeof import("@ionic/vue")['IonCheckbox']
export const IonChip: typeof import("@ionic/vue")['IonChip']
export const IonCol: typeof import("@ionic/vue")['IonCol']
export const IonContent: typeof import("@ionic/vue")['IonContent']
export const IonDatetime: typeof import("@ionic/vue")['IonDatetime']
export const IonDatetimeButton: typeof import("@ionic/vue")['IonDatetimeButton']
export const IonFab: typeof import("@ionic/vue")['IonFab']
export const IonFabButton: typeof import("@ionic/vue")['IonFabButton']
export const IonFabList: typeof import("@ionic/vue")['IonFabList']
export const IonFooter: typeof import("@ionic/vue")['IonFooter']
export const IonGrid: typeof import("@ionic/vue")['IonGrid']
export const IonHeader: typeof import("@ionic/vue")['IonHeader']
export const IonIcon: typeof import("@ionic/vue")['IonIcon']
export const IonImg: typeof import("@ionic/vue")['IonImg']
export const IonInfiniteScroll: typeof import("@ionic/vue")['IonInfiniteScroll']
export const IonInfiniteScrollContent: typeof import("@ionic/vue")['IonInfiniteScrollContent']
export const IonInput: typeof import("@ionic/vue")['IonInput']
export const IonInputPasswordToggle: typeof import("@ionic/vue")['IonInputPasswordToggle']
export const IonItem: typeof import("@ionic/vue")['IonItem']
export const IonItemDivider: typeof import("@ionic/vue")['IonItemDivider']
export const IonItemGroup: typeof import("@ionic/vue")['IonItemGroup']
export const IonItemOption: typeof import("@ionic/vue")['IonItemOption']
export const IonItemOptions: typeof import("@ionic/vue")['IonItemOptions']
export const IonItemSliding: typeof import("@ionic/vue")['IonItemSliding']
export const IonLabel: typeof import("@ionic/vue")['IonLabel']
export const IonList: typeof import("@ionic/vue")['IonList']
export const IonListHeader: typeof import("@ionic/vue")['IonListHeader']
export const IonLoading: typeof import("@ionic/vue")['IonLoading']
export const IonMenu: typeof import("@ionic/vue")['IonMenu']
export const IonMenuButton: typeof import("@ionic/vue")['IonMenuButton']
export const IonMenuToggle: typeof import("@ionic/vue")['IonMenuToggle']
export const IonModal: typeof import("@ionic/vue")['IonModal']
export const IonNav: typeof import("@ionic/vue")['IonNav']
export const IonNavLink: typeof import("@ionic/vue")['IonNavLink']
export const IonNote: typeof import("@ionic/vue")['IonNote']
export const IonPage: typeof import("@ionic/vue")['IonPage']
export const IonPicker: typeof import("@ionic/vue")['IonPicker']
export const IonPickerColumn: typeof import("@ionic/vue")['IonPickerColumn']
export const IonPickerColumnOption: typeof import("@ionic/vue")['IonPickerColumnOption']
export const IonPickerLegacy: typeof import("@ionic/vue")['IonPickerLegacy']
export const IonPopover: typeof import("@ionic/vue")['IonPopover']
export const IonProgressBar: typeof import("@ionic/vue")['IonProgressBar']
export const IonRadio: typeof import("@ionic/vue")['IonRadio']
export const IonRadioGroup: typeof import("@ionic/vue")['IonRadioGroup']
export const IonRange: typeof import("@ionic/vue")['IonRange']
export const IonRefresher: typeof import("@ionic/vue")['IonRefresher']
export const IonRefresherContent: typeof import("@ionic/vue")['IonRefresherContent']
export const IonReorder: typeof import("@ionic/vue")['IonReorder']
export const IonReorderGroup: typeof import("@ionic/vue")['IonReorderGroup']
export const IonRippleEffect: typeof import("@ionic/vue")['IonRippleEffect']
export const IonRouterOutlet: typeof import("@ionic/vue")['IonRouterOutlet']
export const IonRow: typeof import("@ionic/vue")['IonRow']
export const IonSearchbar: typeof import("@ionic/vue")['IonSearchbar']
export const IonSegment: typeof import("@ionic/vue")['IonSegment']
export const IonSegmentButton: typeof import("@ionic/vue")['IonSegmentButton']
export const IonSegmentContent: typeof import("@ionic/vue")['IonSegmentContent']
export const IonSegmentView: typeof import("@ionic/vue")['IonSegmentView']
export const IonSelect: typeof import("@ionic/vue")['IonSelect']
export const IonSelectModal: typeof import("@ionic/vue")['IonSelectModal']
export const IonSelectOption: typeof import("@ionic/vue")['IonSelectOption']
export const IonSkeletonText: typeof import("@ionic/vue")['IonSkeletonText']
export const IonSpinner: typeof import("@ionic/vue")['IonSpinner']
export const IonSplitPane: typeof import("@ionic/vue")['IonSplitPane']
export const IonTab: typeof import("@ionic/vue")['IonTab']
export const IonTabs: typeof import("@ionic/vue")['IonTabs']
export const IonTabBar: typeof import("@ionic/vue")['IonTabBar']
export const IonTabButton: typeof import("@ionic/vue")['IonTabButton']
export const IonText: typeof import("@ionic/vue")['IonText']
export const IonTextarea: typeof import("@ionic/vue")['IonTextarea']
export const IonThumbnail: typeof import("@ionic/vue")['IonThumbnail']
export const IonTitle: typeof import("@ionic/vue")['IonTitle']
export const IonToast: typeof import("@ionic/vue")['IonToast']
export const IonToggle: typeof import("@ionic/vue")['IonToggle']
export const IonToolbar: typeof import("@ionic/vue")['IonToolbar']
export const NuxtLinkLocale: typeof import("../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']
export const SwitchLocalePathLink: typeof import("../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']
export const ColorScheme: typeof import("../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']
export const Icon: typeof import("../node_modules/.pnpm/@nuxt+icon@1.11.0_magicast@0.3.5_vite@6.2.2_@types+node@22.14.0_jiti@2.4.2_lightningcss@1.29._4n4hvoiqlixjyr6n4h4n6fje7e/node_modules/@nuxt/icon/dist/runtime/components/index")['default']
export const NuxtPage: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: IslandComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyAccountMenu: LazyComponent<typeof import("../components/global/AccountMenu.vue")['default']>
export const LazyAccountingSubsidebar: LazyComponent<typeof import("../components/global/AccountingSubsidebar.vue")['default']>
export const LazyAddContactButton: LazyComponent<typeof import("../components/global/AddContactButton.vue")['default']>
export const LazyAddContactForm: LazyComponent<typeof import("../components/global/AddContactForm.vue")['default']>
export const LazyAddContactModal: LazyComponent<typeof import("../components/global/AddContactModal.vue")['default']>
export const LazyAddMachineButton: LazyComponent<typeof import("../components/global/AddMachineButton.vue")['default']>
export const LazyAddMachineForm: LazyComponent<typeof import("../components/global/AddMachineForm.vue")['default']>
export const LazyAddMachineModal: LazyComponent<typeof import("../components/global/AddMachineModal.vue")['default']>
export const LazyAddMeetingButton: LazyComponent<typeof import("../components/global/AddMeetingButton.vue")['default']>
export const LazyAddMeetingForm: LazyComponent<typeof import("../components/global/AddMeetingForm.vue")['default']>
export const LazyAddMeetingModal: LazyComponent<typeof import("../components/global/AddMeetingModal.vue")['default']>
export const LazyAddUserButton: LazyComponent<typeof import("../components/global/AddUserButton.vue")['default']>
export const LazyAddUserForm: LazyComponent<typeof import("../components/global/AddUserForm.vue")['default']>
export const LazyAddUserModal: LazyComponent<typeof import("../components/global/AddUserModal.vue")['default']>
export const LazyAppLayoutSwitcher: LazyComponent<typeof import("../components/global/AppLayoutSwitcher.vue")['default']>
export const LazyAppSearch: LazyComponent<typeof import("../components/global/AppSearch.vue")['default']>
export const LazyAppSearchResult: LazyComponent<typeof import("../components/global/AppSearchResult.vue")['default']>
export const LazyBudgetSubsidebar: LazyComponent<typeof import("../components/global/BudgetSubsidebar.vue")['default']>
export const LazyCircularMenuActivity: LazyComponent<typeof import("../components/global/CircularMenuActivity.vue")['default']>
export const LazyCircularMenuLanguage: LazyComponent<typeof import("../components/global/CircularMenuLanguage.vue")['default']>
export const LazyCircularMenuNotifications: LazyComponent<typeof import("../components/global/CircularMenuNotifications.vue")['default']>
export const LazyCollapseNavigationFooter: LazyComponent<typeof import("../components/global/CollapseNavigationFooter.vue")['default']>
export const LazyCollapseNavigationHeader: LazyComponent<typeof import("../components/global/CollapseNavigationHeader.vue")['default']>
export const LazyCommunicationSubsidebar: LazyComponent<typeof import("../components/global/CommunicationSubsidebar.vue")['default']>
export const LazyCompaniesSubsidebar: LazyComponent<typeof import("../components/global/CompaniesSubsidebar.vue")['default']>
export const LazyCoreSubsidebar: LazyComponent<typeof import("../components/global/CoreSubsidebar.vue")['default']>
export const LazyCustomerVehiclesTable: LazyComponent<typeof import("../components/global/CustomerVehiclesTable.vue")['default']>
export const LazyFlexTableCell: LazyComponent<typeof import("../components/global/FlexTableCell.vue")['default']>
export const LazyFlexTableRow: LazyComponent<typeof import("../components/global/FlexTableRow.vue")['default']>
export const LazyFlexTableStart: LazyComponent<typeof import("../components/global/FlexTableStart.vue")['default']>
export const LazyHrSubsidebar: LazyComponent<typeof import("../components/global/HrSubsidebar.vue")['default']>
export const LazyLogo: LazyComponent<typeof import("../components/global/Logo.vue")['default']>
export const LazyLogoText: LazyComponent<typeof import("../components/global/LogoText.vue")['default']>
export const LazyModuleAccessInfo: LazyComponent<typeof import("../components/global/ModuleAccessInfo.vue")['default']>
export const LazyPanelLanguage: LazyComponent<typeof import("../components/global/PanelLanguage.vue")['default']>
export const LazyPanelSearch: LazyComponent<typeof import("../components/global/PanelSearch.vue")['default']>
export const LazyPanelTask: LazyComponent<typeof import("../components/global/PanelTask.vue")['default']>
export const LazyPhoneInput: LazyComponent<typeof import("../components/global/PhoneInput.vue")['default']>
export const LazyProductionSidebar: LazyComponent<typeof import("../components/global/ProductionSidebar.vue")['default']>
export const LazyProductionSubsidebar: LazyComponent<typeof import("../components/global/ProductionSubsidebar.vue")['default']>
export const LazyRecruitmentSidebar: LazyComponent<typeof import("../components/global/RecruitmentSidebar.vue")['default']>
export const LazySalesSubsidebar: LazyComponent<typeof import("../components/global/SalesSubsidebar.vue")['default']>
export const LazySubsidebar: LazyComponent<typeof import("../components/global/Subsidebar.vue")['default']>
export const LazySubsidebarClients: LazyComponent<typeof import("../components/global/SubsidebarClients.vue")['default']>
export const LazySubsidebarDashboards: LazyComponent<typeof import("../components/global/SubsidebarDashboards.vue")['default']>
export const LazySubsidebarHeader: LazyComponent<typeof import("../components/global/SubsidebarHeader.vue")['default']>
export const LazySubsidebarLayouts: LazyComponent<typeof import("../components/global/SubsidebarLayouts.vue")['default']>
export const LazySubsidebarMenu: LazyComponent<typeof import("../components/global/SubsidebarMenu.vue")['default']>
export const LazySubsidebarMenuCollapseLinks: LazyComponent<typeof import("../components/global/SubsidebarMenuCollapseLinks.vue")['default']>
export const LazySubsidebarMenuDivider: LazyComponent<typeof import("../components/global/SubsidebarMenuDivider.vue")['default']>
export const LazySubsidebarMenuLink: LazyComponent<typeof import("../components/global/SubsidebarMenuLink.vue")['default']>
export const LazyTeamListCompact: LazyComponent<typeof import("../components/global/TeamListCompact.vue")['default']>
export const LazyThemeToggle: LazyComponent<typeof import("../components/global/ThemeToggle.vue")['default']>
export const LazyTimeManagementSubsidebar: LazyComponent<typeof import("../components/global/TimeManagementSubsidebar.vue")['default']>
export const LazyToaster: LazyComponent<typeof import("../components/global/Toaster.vue")['default']>
export const LazyTocAnchor: LazyComponent<typeof import("../components/global/TocAnchor.vue")['default']>
export const LazyToolbarAccountMenu: LazyComponent<typeof import("../components/global/ToolbarAccountMenu.vue")['default']>
export const LazyToolbarActivity: LazyComponent<typeof import("../components/global/ToolbarActivity.vue")['default']>
export const LazyToolbarCustomize: LazyComponent<typeof import("../components/global/ToolbarCustomize.vue")['default']>
export const LazyToolbarLanguage: LazyComponent<typeof import("../components/global/ToolbarLanguage.vue")['default']>
export const LazyToolbarNotifications: LazyComponent<typeof import("../components/global/ToolbarNotifications.vue")['default']>
export const LazyToolbarSearch: LazyComponent<typeof import("../components/global/ToolbarSearch.vue")['default']>
export const LazyAccessControl: LazyComponent<typeof import("../components/AccessControl.vue")['default']>
export const LazyAddonApexcharts: LazyComponent<typeof import("../components/AddonApexcharts.vue")['default']>
export const LazyAddonInputPassword: LazyComponent<typeof import("../components/AddonInputPassword.vue")['default']>
export const LazyAdminsPanelAccount: LazyComponent<typeof import("../components/Admins/PanelAccount.vue")['default']>
export const LazyAdminsPanelActivity: LazyComponent<typeof import("../components/Admins/PanelActivity.vue")['default']>
export const LazyAdminsPanelCard: LazyComponent<typeof import("../components/Admins/PanelCard.vue")['default']>
export const LazyAdminsPanelInvest: LazyComponent<typeof import("../components/Admins/PanelInvest.vue")['default']>
export const LazyAdminsPanels: LazyComponent<typeof import("../components/Admins/Panels.vue")['default']>
export const LazyAdminsSidebarBurger: LazyComponent<typeof import("../components/Admins/SidebarBurger.vue")['default']>
export const LazyAdminsSidebarCircularMenu: LazyComponent<typeof import("../components/Admins/SidebarCircularMenu.vue")['default']>
export const LazyAdminsSidebarLayout: LazyComponent<typeof import("../components/Admins/SidebarLayout.vue")['default']>
export const LazyAdminsSidebarNavigation: LazyComponent<typeof import("../components/Admins/SidebarNavigation.vue")['default']>
export const LazyAdminsSidebarNavigationItem: LazyComponent<typeof import("../components/Admins/SidebarNavigationItem.vue")['default']>
export const LazyAdminsSidebarNavigationPanel: LazyComponent<typeof import("../components/Admins/SidebarNavigationPanel.vue")['default']>
export const LazyAdminsSidebarToolbar: LazyComponent<typeof import("../components/Admins/SidebarToolbar.vue")['default']>
export const LazyAdminsSidebarTools: LazyComponent<typeof import("../components/Admins/SidebarTools.vue")['default']>
export const LazyAdminsToolbarNotifications: LazyComponent<typeof import("../components/Admins/ToolbarNotifications.vue")['default']>
export const LazyAdminsTopnavWorkspaceDropdown: LazyComponent<typeof import("../components/Admins/TopnavWorkspaceDropdown.vue")['default']>
export const LazyAiAssistant: LazyComponent<typeof import("../components/AiAssistant.vue")['default']>
export const LazyAiAssistantButton: LazyComponent<typeof import("../components/AiAssistantButton.vue")['default']>
export const LazyCalendarWidget: LazyComponent<typeof import("../components/CalendarWidget.vue")['default']>
export const LazyDatePicker: LazyComponent<typeof import("../components/DatePicker.vue")['default']>
export const LazyDemoAccountMenu: LazyComponent<typeof import("../components/DemoAccountMenu.vue")['default']>
export const LazyDemoCalendarEvent: LazyComponent<typeof import("../components/DemoCalendarEvent.vue")['default']>
export const LazyDemoCalendarEventPending: LazyComponent<typeof import("../components/DemoCalendarEventPending.vue")['default']>
export const LazyTairoSidebarNavigation: LazyComponent<typeof import("../components/TairoSidebarNavigation.vue")['default']>
export const LazyUsersAccountMenu: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersPanelAccount: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersPanelActivity: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersPanelCard: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersPanelInvest: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersPanels: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersSidebarBurger: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersSidebarCircularMenu: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersSidebarLayout: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersSidebarNavigation: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersSidebarNavigationItem: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersSidebarNavigationPanel: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersSidebarToolbar: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersSidebarTools: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersToolbarAccountMenu: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyUsersTopnavWorkspaceDropdown: LazyComponent<typeof import("../components/Users/<USER>")['default']>
export const LazyAuthEmailVerification: LazyComponent<typeof import("../components/auth/EmailVerification.vue")['default']>
export const LazyAuthPaymentForm: LazyComponent<typeof import("../components/auth/PaymentForm.vue")['default']>
export const LazyAuthRegistrationForm: LazyComponent<typeof import("../components/auth/RegistrationForm.vue")['default']>
export const LazyAuthRegistrationSuccess: LazyComponent<typeof import("../components/auth/RegistrationSuccess.vue")['default']>
export const LazyAuthRoleSelection: LazyComponent<typeof import("../components/auth/RoleSelection.vue")['default']>
export const LazyAuthStripePaymentForm: LazyComponent<typeof import("../components/auth/StripePaymentForm.vue")['default']>
export const LazyAuthSubscriptionPlanSelection: LazyComponent<typeof import("../components/auth/SubscriptionPlanSelection.vue")['default']>
export const LazyBaseLoader: LazyComponent<typeof import("../components/base/BaseLoader.vue")['default']>
export const LazyDemoChartArea: LazyComponent<typeof import("../components/demo-chart/DemoChartArea.vue")['default']>
export const LazyDemoChartAreaBalance: LazyComponent<typeof import("../components/demo-chart/DemoChartAreaBalance.vue")['default']>
export const LazyDemoChartAreaMulti: LazyComponent<typeof import("../components/demo-chart/DemoChartAreaMulti.vue")['default']>
export const LazyDemoChartAreaStats: LazyComponent<typeof import("../components/demo-chart/DemoChartAreaStats.vue")['default']>
export const LazyDemoChartBar: LazyComponent<typeof import("../components/demo-chart/DemoChartBar.vue")['default']>
export const LazyDemoChartBarHorizontal: LazyComponent<typeof import("../components/demo-chart/DemoChartBarHorizontal.vue")['default']>
export const LazyDemoChartBarHorizontalMulti: LazyComponent<typeof import("../components/demo-chart/DemoChartBarHorizontalMulti.vue")['default']>
export const LazyDemoChartBarMulti: LazyComponent<typeof import("../components/demo-chart/DemoChartBarMulti.vue")['default']>
export const LazyDemoChartBarMultiIncome: LazyComponent<typeof import("../components/demo-chart/DemoChartBarMultiIncome.vue")['default']>
export const LazyDemoChartBarRange: LazyComponent<typeof import("../components/demo-chart/DemoChartBarRange.vue")['default']>
export const LazyDemoChartBarSocialChannels: LazyComponent<typeof import("../components/demo-chart/DemoChartBarSocialChannels.vue")['default']>
export const LazyDemoChartBarStacked: LazyComponent<typeof import("../components/demo-chart/DemoChartBarStacked.vue")['default']>
export const LazyDemoChartBubble: LazyComponent<typeof import("../components/demo-chart/DemoChartBubble.vue")['default']>
export const LazyDemoChartDonut: LazyComponent<typeof import("../components/demo-chart/DemoChartDonut.vue")['default']>
export const LazyDemoChartDonutExpenses: LazyComponent<typeof import("../components/demo-chart/DemoChartDonutExpenses.vue")['default']>
export const LazyDemoChartLine: LazyComponent<typeof import("../components/demo-chart/DemoChartLine.vue")['default']>
export const LazyDemoChartLineMulti: LazyComponent<typeof import("../components/demo-chart/DemoChartLineMulti.vue")['default']>
export const LazyDemoChartLineMultiAlt: LazyComponent<typeof import("../components/demo-chart/DemoChartLineMultiAlt.vue")['default']>
export const LazyDemoChartLineStep: LazyComponent<typeof import("../components/demo-chart/DemoChartLineStep.vue")['default']>
export const LazyDemoChartPie: LazyComponent<typeof import("../components/demo-chart/DemoChartPie.vue")['default']>
export const LazyDemoChartRadar: LazyComponent<typeof import("../components/demo-chart/DemoChartRadar.vue")['default']>
export const LazyDemoChartRadial: LazyComponent<typeof import("../components/demo-chart/DemoChartRadial.vue")['default']>
export const LazyDemoChartRadialGauge: LazyComponent<typeof import("../components/demo-chart/DemoChartRadialGauge.vue")['default']>
export const LazyDemoChartRadialGaugeAlt: LazyComponent<typeof import("../components/demo-chart/DemoChartRadialGaugeAlt.vue")['default']>
export const LazyDemoChartRadialMulti: LazyComponent<typeof import("../components/demo-chart/DemoChartRadialMulti.vue")['default']>
export const LazyDemoChartScatter: LazyComponent<typeof import("../components/demo-chart/DemoChartScatter.vue")['default']>
export const LazyDemoChartTimeline: LazyComponent<typeof import("../components/demo-chart/DemoChartTimeline.vue")['default']>
export const LazyExamplesComponentAccessExample: LazyComponent<typeof import("../components/examples/ComponentAccessExample.vue")['default']>
export const LazyTairoLogo: LazyComponent<typeof import("../layers/tairo/components/global/TairoLogo.vue")['default']>
export const LazyTairoLogoText: LazyComponent<typeof import("../layers/tairo/components/global/TairoLogoText.vue")['default']>
export const LazyTairoToaster: LazyComponent<typeof import("../layers/tairo/components/global/TairoToaster.vue")['default']>
export const LazyTairoTocAnchor: LazyComponent<typeof import("../layers/tairo/components/global/TairoTocAnchor.vue")['default']>
export const LazyTairoCheckAnimated: LazyComponent<typeof import("../layers/tairo/components/TairoCheckAnimated.vue")['default']>
export const LazyTairoContentWrapper: LazyComponent<typeof import("../layers/tairo/components/TairoContentWrapper.vue")['default']>
export const LazyTairoContentWrapperTabbed: LazyComponent<typeof import("../layers/tairo/components/TairoContentWrapperTabbed.vue")['default']>
export const LazyTairoError: LazyComponent<typeof import("../layers/tairo/components/TairoError.vue")['default']>
export const LazyTairoFlexTable: LazyComponent<typeof import("../layers/tairo/components/TairoFlexTable.vue")['default']>
export const LazyTairoFlexTableCell: LazyComponent<typeof import("../layers/tairo/components/TairoFlexTableCell.vue")['default']>
export const LazyTairoFlexTableHeading: LazyComponent<typeof import("../layers/tairo/components/TairoFlexTableHeading.vue")['default']>
export const LazyTairoFlexTableRow: LazyComponent<typeof import("../layers/tairo/components/TairoFlexTableRow.vue")['default']>
export const LazyTairoFormGroup: LazyComponent<typeof import("../layers/tairo/components/TairoFormGroup.vue")['default']>
export const LazyTairoFormSave: LazyComponent<typeof import("../layers/tairo/components/TairoFormSave.vue")['default']>
export const LazyTairoImageZoom: LazyComponent<typeof import("../layers/tairo/components/TairoImageZoom.vue")['default']>
export const LazyTairoModal: LazyComponent<typeof import("../layers/tairo/components/TairoModal.vue")['default']>
export const LazyTairoPanels: LazyComponent<typeof import("../layers/tairo/components/TairoPanels.vue")['default']>
export const LazyTairoPasswordStrength: LazyComponent<typeof import("../layers/tairo/components/TairoPasswordStrength.vue")['default']>
export const LazyTairoPopover: LazyComponent<typeof import("../layers/tairo/components/TairoPopover.vue")['default']>
export const LazyTairoPopoverContentDual: LazyComponent<typeof import("../layers/tairo/components/TairoPopoverContentDual.vue")['default']>
export const LazyTairoPopoverContentHelp: LazyComponent<typeof import("../layers/tairo/components/TairoPopoverContentHelp.vue")['default']>
export const LazyTairoPopoverContentMedia: LazyComponent<typeof import("../layers/tairo/components/TairoPopoverContentMedia.vue")['default']>
export const LazyTairoSidebarLayoutConfig: LazyComponent<typeof import("../layers/tairo/components/TairoSidebarLayout.config")['default']>
export const LazyTairoTable: LazyComponent<typeof import("../layers/tairo/components/TairoTable.vue")['default']>
export const LazyTairoTableCell: LazyComponent<typeof import("../layers/tairo/components/TairoTableCell.vue")['default']>
export const LazyTairoTableHeading: LazyComponent<typeof import("../layers/tairo/components/TairoTableHeading.vue")['default']>
export const LazyTairoTableRow: LazyComponent<typeof import("../layers/tairo/components/TairoTableRow.vue")['default']>
export const LazyTairoToc: LazyComponent<typeof import("../layers/tairo/components/TairoToc.vue")['default']>
export const LazyTairoWelcome: LazyComponent<typeof import("../layers/tairo/components/TairoWelcome.vue")['default']>
export const LazyAccountingDashboard: LazyComponent<typeof import("../layers/accounting/components/AccountingDashboard.vue")['default']>
export const LazyJournalEntryForm: LazyComponent<typeof import("../layers/accounting/components/JournalEntryForm.vue")['default']>
export const LazyBudgetChartsSection: LazyComponent<typeof import("../layers/budget/components/BudgetChartsSection.vue")['default']>
export const LazyBudgetDashboard: LazyComponent<typeof import("../layers/budget/components/BudgetDashboard.vue")['default']>
export const LazyBudgetIncomeForm: LazyComponent<typeof import("../layers/budget/components/BudgetIncomeForm.vue")['default']>
export const LazyExpensesSection: LazyComponent<typeof import("../layers/budget/components/ExpensesSection.vue")['default']>
export const LazyGlobalFooter: LazyComponent<typeof import("../layers/budget/components/GlobalFooter.vue")['default']>
export const LazyGlobalHeader: LazyComponent<typeof import("../layers/budget/components/GlobalHeader.vue")['default']>
export const LazyIncomeSection: LazyComponent<typeof import("../layers/budget/components/IncomeSection.vue")['default']>
export const LazyChartsBudgetChartsSection: LazyComponent<typeof import("../layers/budget/components/charts/BudgetChartsSection.vue")['default']>
export const LazyDashboardBudgetGoalsCard: LazyComponent<typeof import("../layers/budget/components/dashboard/BudgetGoalsCard.vue")['default']>
export const LazyDashboardBudgetSummaryCard: LazyComponent<typeof import("../layers/budget/components/dashboard/BudgetSummaryCard.vue")['default']>
export const LazyDashboardBudgetTrendsChart: LazyComponent<typeof import("../layers/budget/components/dashboard/BudgetTrendsChart.vue")['default']>
export const LazyDashboardBudgetTypeSelector: LazyComponent<typeof import("../layers/budget/components/dashboard/BudgetTypeSelector.vue")['default']>
export const LazyDashboardCategoryBreakdownCard: LazyComponent<typeof import("../layers/budget/components/dashboard/CategoryBreakdownCard.vue")['default']>
export const LazyDashboardRecentTransactionsCard: LazyComponent<typeof import("../layers/budget/components/dashboard/RecentTransactionsCard.vue")['default']>
export const LazyExpensesMultipleExpenses: LazyComponent<typeof import("../layers/budget/components/expenses/MultipleExpenses.vue")['default']>
export const LazyExpensesOneTimeExpenses: LazyComponent<typeof import("../layers/budget/components/expenses/OneTimeExpenses.vue")['default']>
export const LazyExpensesRepeatedExpenses: LazyComponent<typeof import("../layers/budget/components/expenses/RepeatedExpenses.vue")['default']>
export const LazyForecastingCategories: LazyComponent<typeof import("../layers/budget/components/forecasting/ForecastingCategories.vue")['default']>
export const LazyForecastingChart: LazyComponent<typeof import("../layers/budget/components/forecasting/ForecastingChart.vue")['default']>
export const LazyForecastingControls: LazyComponent<typeof import("../layers/budget/components/forecasting/ForecastingControls.vue")['default']>
export const LazyForecastingScenarios: LazyComponent<typeof import("../layers/budget/components/forecasting/ForecastingScenarios.vue")['default']>
export const LazyForecastingSummary: LazyComponent<typeof import("../layers/budget/components/forecasting/ForecastingSummary.vue")['default']>
export const LazyIncomesMultipleIncomes: LazyComponent<typeof import("../layers/budget/components/incomes/MultipleIncomes.vue")['default']>
export const LazyIncomesOneTimeIncomes: LazyComponent<typeof import("../layers/budget/components/incomes/OneTimeIncomes.vue")['default']>
export const LazyIncomesRepeatedIncomes: LazyComponent<typeof import("../layers/budget/components/incomes/RepeatedIncomes.vue")['default']>
export const LazyPlannerBudgetComparisonCard: LazyComponent<typeof import("../layers/budget/components/planner/BudgetComparisonCard.vue")['default']>
export const LazyPlannerMonthlyBudgetCard: LazyComponent<typeof import("../layers/budget/components/planner/MonthlyBudgetCard.vue")['default']>
export const LazyQuickBudgetCategoryBreakdown: LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetCategoryBreakdown.vue")['default']>
export const LazyQuickBudgetEntryForm: LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetEntryForm.vue")['default']>
export const LazyQuickBudgetExpensesList: LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetExpensesList.vue")['default']>
export const LazyQuickBudgetGoals: LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetGoals.vue")['default']>
export const LazyQuickBudgetIncomeList: LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetIncomeList.vue")['default']>
export const LazyQuickBudgetSummaryCard: LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetSummaryCard.vue")['default']>
export const LazyQuickBudgetTransactionsList: LazyComponent<typeof import("../layers/budget/components/quick-budget/QuickBudgetTransactionsList.vue")['default']>
export const LazyStatsSection: LazyComponent<typeof import("../layers/budget/components/stats/StatsSection.vue")['default']>
export const LazyTransactionsListCard: LazyComponent<typeof import("../layers/budget/components/transactions/TransactionsListCard.vue")['default']>
export const LazyCompaniesDashboard: LazyComponent<typeof import("../layers/companies/components/CompaniesDashboard.vue")['default']>
export const LazyCompanyDashboard: LazyComponent<typeof import("../layers/companies/components/CompanyDashboard.vue")['default']>
export const LazyCompanyForm: LazyComponent<typeof import("../layers/companies/components/CompanyForm.vue")['default']>
export const LazyClientsClientCard: LazyComponent<typeof import("../layers/companies/components/clients/ClientCard.vue")['default']>
export const LazyClientsTable: LazyComponent<typeof import("../layers/companies/components/clients/ClientsTable.vue")['default']>
export const LazyComplianceAudits: LazyComponent<typeof import("../layers/companies/components/compliance/ComplianceAudits.vue")['default']>
export const LazyComplianceOverview: LazyComponent<typeof import("../layers/companies/components/compliance/ComplianceOverview.vue")['default']>
export const LazyComplianceRequirements: LazyComponent<typeof import("../layers/companies/components/compliance/ComplianceRequirements.vue")['default']>
export const LazyDocumentsDocumentCard: LazyComponent<typeof import("../layers/companies/components/documents/DocumentCard.vue")['default']>
export const LazyRiskMitigationPlans: LazyComponent<typeof import("../layers/companies/components/risk/MitigationPlans.vue")['default']>
export const LazyRiskDashboard: LazyComponent<typeof import("../layers/companies/components/risk/RiskDashboard.vue")['default']>
export const LazyRiskRegister: LazyComponent<typeof import("../layers/companies/components/risk/RiskRegister.vue")['default']>
export const LazySettingsGeneralSettings: LazyComponent<typeof import("../layers/companies/components/settings/GeneralSettings.vue")['default']>
export const LazySettingsIntegrationSettings: LazyComponent<typeof import("../layers/companies/components/settings/IntegrationSettings.vue")['default']>
export const LazySettingsNotificationSettings: LazyComponent<typeof import("../layers/companies/components/settings/NotificationSettings.vue")['default']>
export const LazySettingsPermissionSettings: LazyComponent<typeof import("../layers/companies/components/settings/PermissionSettings.vue")['default']>
export const LazySuppliersSupplierCard: LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierCard.vue")['default']>
export const LazySuppliersSupplierDocuments: LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierDocuments.vue")['default']>
export const LazySuppliersSupplierOrders: LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierOrders.vue")['default']>
export const LazySuppliersSupplierOverview: LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierOverview.vue")['default']>
export const LazySuppliersSupplierPerformance: LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierPerformance.vue")['default']>
export const LazySuppliersSupplierPerformanceMetrics: LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierPerformanceMetrics.vue")['default']>
export const LazySuppliersSupplierTransactions: LazyComponent<typeof import("../layers/companies/components/suppliers/SupplierTransactions.vue")['default']>
export const LazySuppliersTable: LazyComponent<typeof import("../layers/companies/components/suppliers/SuppliersTable.vue")['default']>
export const LazyWorkforceCapacityPlanning: LazyComponent<typeof import("../layers/companies/components/workforce/CapacityPlanning.vue")['default']>
export const LazyWorkforceSkillsGapAnalysis: LazyComponent<typeof import("../layers/companies/components/workforce/SkillsGapAnalysis.vue")['default']>
export const LazyWorkforceOverview: LazyComponent<typeof import("../layers/companies/components/workforce/WorkforceOverview.vue")['default']>
export const LazyBaseLayerPage: LazyComponent<typeof import("../layers/core/components/BaseLayerPage.vue")['default']>
export const LazyBusinessRuleModal: LazyComponent<typeof import("../layers/core/components/BusinessRuleModal.vue")['default']>
export const LazyCoreDashboard: LazyComponent<typeof import("../layers/core/components/CoreDashboard.vue")['default']>
export const LazyActivityFilters: LazyComponent<typeof import("../layers/core/components/activity/ActivityFilters.vue")['default']>
export const LazyActivityLogDetails: LazyComponent<typeof import("../layers/core/components/activity/ActivityLogDetails.vue")['default']>
export const LazyActivityLogItem: LazyComponent<typeof import("../layers/core/components/activity/ActivityLogItem.vue")['default']>
export const LazyActivityLogList: LazyComponent<typeof import("../layers/core/components/activity/ActivityLogList.vue")['default']>
export const LazyAiDecisionCard: LazyComponent<typeof import("../layers/core/components/ai/DecisionCard.vue")['default']>
export const LazyAiDecisionDetailModal: LazyComponent<typeof import("../layers/core/components/ai/DecisionDetailModal.vue")['default']>
export const LazyApiEndpointCard: LazyComponent<typeof import("../layers/core/components/api/ApiEndpointCard.vue")['default']>
export const LazyApiEndpointDetailModal: LazyComponent<typeof import("../layers/core/components/api/ApiEndpointDetailModal.vue")['default']>
export const LazyAutomationRuleCard: LazyComponent<typeof import("../layers/core/components/automation/RuleCard.vue")['default']>
export const LazyBrandAssetCard: LazyComponent<typeof import("../layers/core/components/brand/BrandAssetCard.vue")['default']>
export const LazyChartsBarChart: LazyComponent<typeof import("../layers/core/components/charts/BarChart.vue")['default']>
export const LazyChartsLineChart: LazyComponent<typeof import("../layers/core/components/charts/LineChart.vue")['default']>
export const LazyChartsPieChart: LazyComponent<typeof import("../layers/core/components/charts/PieChart.vue")['default']>
export const LazyChartsRadialChart: LazyComponent<typeof import("../layers/core/components/charts/RadialChart.vue")['default']>
export const LazyDashboardActivityCard: LazyComponent<typeof import("../layers/core/components/dashboard/ActivityCard.vue")['default']>
export const LazyDashboardMetricCard: LazyComponent<typeof import("../layers/core/components/dashboard/MetricCard.vue")['default']>
export const LazyDashboardModuleUsageCard: LazyComponent<typeof import("../layers/core/components/dashboard/ModuleUsageCard.vue")['default']>
export const LazyDashboardNotificationsCard: LazyComponent<typeof import("../layers/core/components/dashboard/NotificationsCard.vue")['default']>
export const LazyDashboardSystemHealthCard: LazyComponent<typeof import("../layers/core/components/dashboard/SystemHealthCard.vue")['default']>
export const LazyDashboardUserActivityCard: LazyComponent<typeof import("../layers/core/components/dashboard/UserActivityCard.vue")['default']>
export const LazyDatabaseConnectionBuilderForm: LazyComponent<typeof import("../layers/core/components/database/ConnectionBuilderForm.vue")['default']>
export const LazyDatabaseConnectionPoolMonitor: LazyComponent<typeof import("../layers/core/components/database/ConnectionPoolMonitor.vue")['default']>
export const LazyDatabaseConnectionStringForm: LazyComponent<typeof import("../layers/core/components/database/ConnectionStringForm.vue")['default']>
export const LazyDatabaseExplorer: LazyComponent<typeof import("../layers/core/components/database/DatabaseExplorer.vue")['default']>
export const LazyDatabaseHealth: LazyComponent<typeof import("../layers/core/components/database/DatabaseHealth.vue")['default']>
export const LazyDatabaseSchema: LazyComponent<typeof import("../layers/core/components/database/DatabaseSchema.vue")['default']>
export const LazyDatabaseTerminalWindow: LazyComponent<typeof import("../layers/core/components/database/TerminalWindow.vue")['default']>
export const LazyDocumentsDocumentFilters: LazyComponent<typeof import("../layers/core/components/documents/DocumentFilters.vue")['default']>
export const LazyDocumentsDocumentUploadModal: LazyComponent<typeof import("../layers/core/components/documents/DocumentUploadModal.vue")['default']>
export const LazyEventsEventCard: LazyComponent<typeof import("../layers/core/components/events/EventCard.vue")['default']>
export const LazyEventsEventDetailModal: LazyComponent<typeof import("../layers/core/components/events/EventDetailModal.vue")['default']>
export const LazyHealthPerformanceMetricsCard: LazyComponent<typeof import("../layers/core/components/health/PerformanceMetricsCard.vue")['default']>
export const LazyHealthResourceUsageCard: LazyComponent<typeof import("../layers/core/components/health/ResourceUsageCard.vue")['default']>
export const LazyHealthServiceStatusCard: LazyComponent<typeof import("../layers/core/components/health/ServiceStatusCard.vue")['default']>
export const LazyLegalDocumentCard: LazyComponent<typeof import("../layers/core/components/legal/LegalDocumentCard.vue")['default']>
export const LazyMlDatasetCard: LazyComponent<typeof import("../layers/core/components/ml/DatasetCard.vue")['default']>
export const LazyMlTrainingDataUploader: LazyComponent<typeof import("../layers/core/components/ml/TrainingDataUploader.vue")['default']>
export const LazyMlTrainingFormatGuide: LazyComponent<typeof import("../layers/core/components/ml/TrainingFormatGuide.vue")['default']>
export const LazyModulesModuleCard: LazyComponent<typeof import("../layers/core/components/modules/ModuleCard.vue")['default']>
export const LazyModulesModuleDetailModal: LazyComponent<typeof import("../layers/core/components/modules/ModuleDetailModal.vue")['default']>
export const LazyMonitoringAlertDetailsModal: LazyComponent<typeof import("../layers/core/components/monitoring/AlertDetailsModal.vue")['default']>
export const LazyMonitoringAlertsListCard: LazyComponent<typeof import("../layers/core/components/monitoring/AlertsListCard.vue")['default']>
export const LazyMonitoringAlertsSummaryCard: LazyComponent<typeof import("../layers/core/components/monitoring/AlertsSummaryCard.vue")['default']>
export const LazyMonitoringApiPerformanceCard: LazyComponent<typeof import("../layers/core/components/monitoring/ApiPerformanceCard.vue")['default']>
export const LazyMonitoringDatabaseResourcesCard: LazyComponent<typeof import("../layers/core/components/monitoring/DatabaseResourcesCard.vue")['default']>
export const LazyMonitoringErrorDetailsModal: LazyComponent<typeof import("../layers/core/components/monitoring/ErrorDetailsModal.vue")['default']>
export const LazyMonitoringErrorLogsCard: LazyComponent<typeof import("../layers/core/components/monitoring/ErrorLogsCard.vue")['default']>
export const LazyMonitoringErrorSummaryCard: LazyComponent<typeof import("../layers/core/components/monitoring/ErrorSummaryCard.vue")['default']>
export const LazyMonitoringPageLoadTimesCard: LazyComponent<typeof import("../layers/core/components/monitoring/PageLoadTimesCard.vue")['default']>
export const LazyMonitoringPerformanceMetricsCard: LazyComponent<typeof import("../layers/core/components/monitoring/PerformanceMetricsCard.vue")['default']>
export const LazyMonitoringResourceOverviewCard: LazyComponent<typeof import("../layers/core/components/monitoring/ResourceOverviewCard.vue")['default']>
export const LazyMonitoringServerResourcesCard: LazyComponent<typeof import("../layers/core/components/monitoring/ServerResourcesCard.vue")['default']>
export const LazyPoliciesPolicyCard: LazyComponent<typeof import("../layers/core/components/policies/PolicyCard.vue")['default']>
export const LazyPoliciesPolicyCategoryFilter: LazyComponent<typeof import("../layers/core/components/policies/PolicyCategoryFilter.vue")['default']>
export const LazyPoliciesPolicyDetailModal: LazyComponent<typeof import("../layers/core/components/policies/PolicyDetailModal.vue")['default']>
export const LazyRulesCategoryCard: LazyComponent<typeof import("../layers/core/components/rules/CategoryCard.vue")['default']>
export const LazyRulesRuleCard: LazyComponent<typeof import("../layers/core/components/rules/RuleCard.vue")['default']>
export const LazyRulesRuleDetailModal: LazyComponent<typeof import("../layers/core/components/rules/RuleDetailModal.vue")['default']>
export const LazyRulesRuleFilters: LazyComponent<typeof import("../layers/core/components/rules/RuleFilters.vue")['default']>
export const LazyRulesTemplateCard: LazyComponent<typeof import("../layers/core/components/rules/TemplateCard.vue")['default']>
export const LazyRulesTemplateDetailModal: LazyComponent<typeof import("../layers/core/components/rules/TemplateDetailModal.vue")['default']>
export const LazyRulesValidationTester: LazyComponent<typeof import("../layers/core/components/rules/ValidationTester.vue")['default']>
export const LazySecurityAuditLogEntry: LazyComponent<typeof import("../layers/core/components/security/AuditLogEntry.vue")['default']>
export const LazySecurityAuditLogFilters: LazyComponent<typeof import("../layers/core/components/security/AuditLogFilters.vue")['default']>
export const LazySecurityEditRoleModal: LazyComponent<typeof import("../layers/core/components/security/EditRoleModal.vue")['default']>
export const LazySecurityEditUserRolesModal: LazyComponent<typeof import("../layers/core/components/security/EditUserRolesModal.vue")['default']>
export const LazySecurityRolePermissionsCard: LazyComponent<typeof import("../layers/core/components/security/RolePermissionsCard.vue")['default']>
export const LazySecurityUserRolesTable: LazyComponent<typeof import("../layers/core/components/security/UserRolesTable.vue")['default']>
export const LazySettingsAppearanceSettingsCard: LazyComponent<typeof import("../layers/core/components/settings/AppearanceSettingsCard.vue")['default']>
export const LazySettingsBackupHistoryCard: LazyComponent<typeof import("../layers/core/components/settings/BackupHistoryCard.vue")['default']>
export const LazySettingsBackupSettingsCard: LazyComponent<typeof import("../layers/core/components/settings/BackupSettingsCard.vue")['default']>
export const LazySettingsBackupStatusCard: LazyComponent<typeof import("../layers/core/components/settings/BackupStatusCard.vue")['default']>
export const LazySettingsDeleteConfirmationModal: LazyComponent<typeof import("../layers/core/components/settings/DeleteConfirmationModal.vue")['default']>
export const LazySettingsEnvironmentInfoCard: LazyComponent<typeof import("../layers/core/components/settings/EnvironmentInfoCard.vue")['default']>
export const LazySettingsEnvironmentVariablesCard: LazyComponent<typeof import("../layers/core/components/settings/EnvironmentVariablesCard.vue")['default']>
export const LazySettingsFormatSettingsCard: LazyComponent<typeof import("../layers/core/components/settings/FormatSettingsCard.vue")['default']>
export const LazySettingsGeneralSettingsCard: LazyComponent<typeof import("../layers/core/components/settings/GeneralSettingsCard.vue")['default']>
export const LazySettingsLanguageSettingsCard: LazyComponent<typeof import("../layers/core/components/settings/LanguageSettingsCard.vue")['default']>
export const LazySettingsPerformanceSettingsCard: LazyComponent<typeof import("../layers/core/components/settings/PerformanceSettingsCard.vue")['default']>
export const LazySettingsQuickActionsCard: LazyComponent<typeof import("../layers/core/components/settings/QuickActionsCard.vue")['default']>
export const LazySettingsRestoreConfirmationModal: LazyComponent<typeof import("../layers/core/components/settings/RestoreConfirmationModal.vue")['default']>
export const LazySettingsRestoreFromFileModal: LazyComponent<typeof import("../layers/core/components/settings/RestoreFromFileModal.vue")['default']>
export const LazySettingsRestoreSettingsCard: LazyComponent<typeof import("../layers/core/components/settings/RestoreSettingsCard.vue")['default']>
export const LazyWebhooksWebhookCard: LazyComponent<typeof import("../layers/core/components/webhooks/WebhookCard.vue")['default']>
export const LazyWebhooksWebhookDetailModal: LazyComponent<typeof import("../layers/core/components/webhooks/WebhookDetailModal.vue")['default']>
export const LazyEmployeeFilters: LazyComponent<typeof import("../layers/hr/components/EmployeeFilters.vue")['default']>
export const LazyEmployeeForm: LazyComponent<typeof import("../layers/hr/components/EmployeeForm.vue")['default']>
export const LazyEmployeeList: LazyComponent<typeof import("../layers/hr/components/EmployeeList.vue")['default']>
export const LazyHrDashboard: LazyComponent<typeof import("../layers/hr/components/HrDashboard.vue")['default']>
export const LazyWorkerForm: LazyComponent<typeof import("../layers/hr/components/WorkerForm.vue")['default']>
export const LazyAttendanceRecordsTab: LazyComponent<typeof import("../layers/hr/components/attendance/AttendanceRecordsTab.vue")['default']>
export const LazyAttendanceReportsTab: LazyComponent<typeof import("../layers/hr/components/attendance/AttendanceReportsTab.vue")['default']>
export const LazyAttendanceDailyAttendanceTab: LazyComponent<typeof import("../layers/hr/components/attendance/DailyAttendanceTab.vue")['default']>
export const LazyBenefitsBenefitPlansTab: LazyComponent<typeof import("../layers/hr/components/benefits/BenefitPlansTab.vue")['default']>
export const LazyBenefitsBenefitSettingsTab: LazyComponent<typeof import("../layers/hr/components/benefits/BenefitSettingsTab.vue")['default']>
export const LazyBenefitsEmployeeBenefitsTab: LazyComponent<typeof import("../layers/hr/components/benefits/EmployeeBenefitsTab.vue")['default']>
export const LazyBonusesAllBonusesTab: LazyComponent<typeof import("../layers/hr/components/bonuses/AllBonusesTab.vue")['default']>
export const LazyBonusesBonusReportsTab: LazyComponent<typeof import("../layers/hr/components/bonuses/BonusReportsTab.vue")['default']>
export const LazyBonusesEmployeeBonusesTab: LazyComponent<typeof import("../layers/hr/components/bonuses/EmployeeBonusesTab.vue")['default']>
export const LazyCareerPathDetails: LazyComponent<typeof import("../layers/hr/components/career/CareerPathDetails.vue")['default']>
export const LazyCareerPathFormModal: LazyComponent<typeof import("../layers/hr/components/career/CareerPathFormModal.vue")['default']>
export const LazyCareerPathOverview: LazyComponent<typeof import("../layers/hr/components/career/CareerPathOverview.vue")['default']>
export const LazyDepartmentsDepartmentCard: LazyComponent<typeof import("../layers/hr/components/departments/DepartmentCard.vue")['default']>
export const LazyDocumentsCompanyPoliciesTab: LazyComponent<typeof import("../layers/hr/components/documents/CompanyPoliciesTab.vue")['default']>
export const LazyDocumentsTab: LazyComponent<typeof import("../layers/hr/components/documents/DocumentsTab.vue")['default']>
export const LazyDocumentsEmployeeDocumentsTab: LazyComponent<typeof import("../layers/hr/components/documents/EmployeeDocumentsTab.vue")['default']>
export const LazyEmployeesDocumentUploadForm: LazyComponent<typeof import("../layers/hr/components/employees/DocumentUploadForm.vue")['default']>
export const LazyEmployeesEmployeeDocuments: LazyComponent<typeof import("../layers/hr/components/employees/EmployeeDocuments.vue")['default']>
export const LazyEmployeesEmployeeLeave: LazyComponent<typeof import("../layers/hr/components/employees/EmployeeLeave.vue")['default']>
export const LazyEmployeesEmployeeOverview: LazyComponent<typeof import("../layers/hr/components/employees/EmployeeOverview.vue")['default']>
export const LazyEmployeesEmployeePayroll: LazyComponent<typeof import("../layers/hr/components/employees/EmployeePayroll.vue")['default']>
export const LazyEmployeesEmployeePerformance: LazyComponent<typeof import("../layers/hr/components/employees/EmployeePerformance.vue")['default']>
export const LazyEmployeesEmployeeProfileCard: LazyComponent<typeof import("../layers/hr/components/employees/EmployeeProfileCard.vue")['default']>
export const LazyLeaveBalancesTab: LazyComponent<typeof import("../layers/hr/components/leave/LeaveBalancesTab.vue")['default']>
export const LazyLeaveCalendarTab: LazyComponent<typeof import("../layers/hr/components/leave/LeaveCalendarTab.vue")['default']>
export const LazyLeaveRequestsTab: LazyComponent<typeof import("../layers/hr/components/leave/LeaveRequestsTab.vue")['default']>
export const LazyLeaveSettingsTab: LazyComponent<typeof import("../layers/hr/components/leave/LeaveSettingsTab.vue")['default']>
export const LazyPayrollRunTab: LazyComponent<typeof import("../layers/hr/components/payroll/PayrollRunTab.vue")['default']>
export const LazyPayrollPayslipsTab: LazyComponent<typeof import("../layers/hr/components/payroll/PayslipsTab.vue")['default']>
export const LazyPayrollTaxSettingsTab: LazyComponent<typeof import("../layers/hr/components/payroll/TaxSettingsTab.vue")['default']>
export const LazyPerformanceEmployeePerformanceTab: LazyComponent<typeof import("../layers/hr/components/performance/EmployeePerformanceTab.vue")['default']>
export const LazyPerformanceAnalyticsTab: LazyComponent<typeof import("../layers/hr/components/performance/PerformanceAnalyticsTab.vue")['default']>
export const LazyPerformanceReviewsTab: LazyComponent<typeof import("../layers/hr/components/performance/PerformanceReviewsTab.vue")['default']>
export const LazyShiftsShiftAssignmentsTab: LazyComponent<typeof import("../layers/hr/components/shifts/ShiftAssignmentsTab.vue")['default']>
export const LazyShiftsShiftScheduleTab: LazyComponent<typeof import("../layers/hr/components/shifts/ShiftScheduleTab.vue")['default']>
export const LazyShiftsShiftTypesTab: LazyComponent<typeof import("../layers/hr/components/shifts/ShiftTypesTab.vue")['default']>
export const LazyTrainingCertificationsTab: LazyComponent<typeof import("../layers/hr/components/training/CertificationsTab.vue")['default']>
export const LazyTrainingEmployeeTrainingTab: LazyComponent<typeof import("../layers/hr/components/training/EmployeeTrainingTab.vue")['default']>
export const LazyTrainingEnrollEmployeesModal: LazyComponent<typeof import("../layers/hr/components/training/EnrollEmployeesModal.vue")['default']>
export const LazyTrainingDetailsModal: LazyComponent<typeof import("../layers/hr/components/training/TrainingDetailsModal.vue")['default']>
export const LazyTrainingFormModal: LazyComponent<typeof import("../layers/hr/components/training/TrainingFormModal.vue")['default']>
export const LazyTrainingProgramCard: LazyComponent<typeof import("../layers/hr/components/training/TrainingProgramCard.vue")['default']>
export const LazyTrainingProgramsTab: LazyComponent<typeof import("../layers/hr/components/training/TrainingProgramsTab.vue")['default']>
export const LazyProductionDashboard: LazyComponent<typeof import("../layers/production/components/ProductionDashboard.vue")['default']>
export const LazyDashboardTaskStatusChart: LazyComponent<typeof import("../layers/production/components/dashboard/TaskStatusChart.vue")['default']>
export const LazyDocumentsUploadDocumentModal: LazyComponent<typeof import("../layers/production/components/documents/UploadDocumentModal.vue")['default']>
export const LazyDocumentsViewDocumentModal: LazyComponent<typeof import("../layers/production/components/documents/ViewDocumentModal.vue")['default']>
export const LazyQualityChecklistCard: LazyComponent<typeof import("../layers/production/components/quality/ChecklistCard.vue")['default']>
export const LazyQualityChecklistDetailModal: LazyComponent<typeof import("../layers/production/components/quality/ChecklistDetailModal.vue")['default']>
export const LazyQualityChecklistItem: LazyComponent<typeof import("../layers/production/components/quality/ChecklistItem.vue")['default']>
export const LazyQualityNewChecklistModal: LazyComponent<typeof import("../layers/production/components/quality/NewChecklistModal.vue")['default']>
export const LazyQualityPhotoCard: LazyComponent<typeof import("../layers/production/components/quality/PhotoCard.vue")['default']>
export const LazyQualityPhotoDetailModal: LazyComponent<typeof import("../layers/production/components/quality/PhotoDetailModal.vue")['default']>
export const LazyQualityUploadPhotoModal: LazyComponent<typeof import("../layers/production/components/quality/UploadPhotoModal.vue")['default']>
export const LazyReportsBarChart: LazyComponent<typeof import("../layers/production/components/reports/BarChart.vue")['default']>
export const LazyReportsLineChart: LazyComponent<typeof import("../layers/production/components/reports/LineChart.vue")['default']>
export const LazyReportsMetricCard: LazyComponent<typeof import("../layers/production/components/reports/MetricCard.vue")['default']>
export const LazyReportsPieChart: LazyComponent<typeof import("../layers/production/components/reports/PieChart.vue")['default']>
export const LazyReportsReportCard: LazyComponent<typeof import("../layers/production/components/reports/ReportCard.vue")['default']>
export const LazyResourcesEquipmentCard: LazyComponent<typeof import("../layers/production/components/resources/EquipmentCard.vue")['default']>
export const LazyResourcesEquipmentDetailModal: LazyComponent<typeof import("../layers/production/components/resources/EquipmentDetailModal.vue")['default']>
export const LazyResourcesMaterialCard: LazyComponent<typeof import("../layers/production/components/resources/MaterialCard.vue")['default']>
export const LazyResourcesMaterialDetailModal: LazyComponent<typeof import("../layers/production/components/resources/MaterialDetailModal.vue")['default']>
export const LazyResourcesNewEquipmentModal: LazyComponent<typeof import("../layers/production/components/resources/NewEquipmentModal.vue")['default']>
export const LazyResourcesNewMaterialModal: LazyComponent<typeof import("../layers/production/components/resources/NewMaterialModal.vue")['default']>
export const LazyResourcesNewWorkerModal: LazyComponent<typeof import("../layers/production/components/resources/NewWorkerModal.vue")['default']>
export const LazyResourcesWorkforceCard: LazyComponent<typeof import("../layers/production/components/resources/WorkforceCard.vue")['default']>
export const LazyResourcesWorkforceDetailModal: LazyComponent<typeof import("../layers/production/components/resources/WorkforceDetailModal.vue")['default']>
export const LazyStatisticsProjectMetricsChart: LazyComponent<typeof import("../layers/production/components/statistics/ProjectMetricsChart.vue")['default']>
export const LazyStatisticsProjectStatusChart: LazyComponent<typeof import("../layers/production/components/statistics/ProjectStatusChart.vue")['default']>
export const LazyStatisticsTaskCompletionChart: LazyComponent<typeof import("../layers/production/components/statistics/TaskCompletionChart.vue")['default']>
export const LazyTasksNewTaskModal: LazyComponent<typeof import("../layers/production/components/tasks/NewTaskModal.vue")['default']>
export const LazyTasksTaskBoard: LazyComponent<typeof import("../layers/production/components/tasks/TaskBoard.vue")['default']>
export const LazyTasksTaskCard: LazyComponent<typeof import("../layers/production/components/tasks/TaskCard.vue")['default']>
export const LazyTasksTaskColumn: LazyComponent<typeof import("../layers/production/components/tasks/TaskColumn.vue")['default']>
export const LazyTasksTaskDetailModal: LazyComponent<typeof import("../layers/production/components/tasks/TaskDetailModal.vue")['default']>
export const LazyTimelineGanttChart: LazyComponent<typeof import("../layers/production/components/timeline/GanttChart.vue")['default']>
export const LazyRecruitmentSidebarItem: LazyComponent<typeof import("../layers/recruitment/components/RecruitmentSidebarItem.vue")['default']>
export const LazyAdminLanguageManager: LazyComponent<typeof import("../layers/recruitment/components/admin/LanguageManager.vue")['default']>
export const LazyAdminTranslationsManager: LazyComponent<typeof import("../layers/recruitment/components/admin/TranslationsManager.vue")['default']>
export const LazyFormsFormBuilderFieldEditor: LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderFieldEditor.vue")['default']>
export const LazyFormsFormBuilderFieldList: LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderFieldList.vue")['default']>
export const LazyFormsFormBuilderPreview: LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderPreview.vue")['default']>
export const LazyFormsFormBuilderPreviewField: LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderPreviewField.vue")['default']>
export const LazyFormsFormBuilderSettings: LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderSettings.vue")['default']>
export const LazyFormsFormBuilderSidebar: LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderSidebar.vue")['default']>
export const LazyFormsFormBuilderStepEditor: LazyComponent<typeof import("../layers/recruitment/components/forms/FormBuilderStepEditor.vue")['default']>
export const LazyFormsPublicFormField: LazyComponent<typeof import("../layers/recruitment/components/forms/PublicFormField.vue")['default']>
export const LazyWorkforceFormAdditionalInfoStep: LazyComponent<typeof import("../layers/recruitment/components/workforce-form/AdditionalInfoStep.vue")['default']>
export const LazyWorkforceFormConfirmationStep: LazyComponent<typeof import("../layers/recruitment/components/workforce-form/ConfirmationStep.vue")['default']>
export const LazyWorkforceFormIntroductionStep: LazyComponent<typeof import("../layers/recruitment/components/workforce-form/IntroductionStep.vue")['default']>
export const LazyWorkforceFormLanguageSelector: LazyComponent<typeof import("../layers/recruitment/components/workforce-form/LanguageSelector.vue")['default']>
export const LazyWorkforceFormPersonalInfoStep: LazyComponent<typeof import("../layers/recruitment/components/workforce-form/PersonalInfoStep.vue")['default']>
export const LazyWorkforceFormProfessionalExperienceStep: LazyComponent<typeof import("../layers/recruitment/components/workforce-form/ProfessionalExperienceStep.vue")['default']>
export const LazyWorkforceFormQualificationsStep: LazyComponent<typeof import("../layers/recruitment/components/workforce-form/QualificationsStep.vue")['default']>
export const LazyWorkforceFormReferencesStep: LazyComponent<typeof import("../layers/recruitment/components/workforce-form/ReferencesStep.vue")['default']>
export const LazyWorkforceFormSpecialtiesStep: LazyComponent<typeof import("../layers/recruitment/components/workforce-form/SpecialtiesStep.vue")['default']>
export const LazyWorkforceAssignmentDetailsModal: LazyComponent<typeof import("../layers/recruitment/components/workforce/AssignmentDetailsModal.vue")['default']>
export const LazyWorkforceAssignmentFilters: LazyComponent<typeof import("../layers/recruitment/components/workforce/AssignmentFilters.vue")['default']>
export const LazyWorkforceAssignmentsPagination: LazyComponent<typeof import("../layers/recruitment/components/workforce/AssignmentsPagination.vue")['default']>
export const LazyWorkforceJobDetailsModal: LazyComponent<typeof import("../layers/recruitment/components/workforce/JobDetailsModal.vue")['default']>
export const LazyWorkforceJobFilters: LazyComponent<typeof import("../layers/recruitment/components/workforce/JobFilters.vue")['default']>
export const LazyWorkforceJobFormModal: LazyComponent<typeof import("../layers/recruitment/components/workforce/JobFormModal.vue")['default']>
export const LazyWorkforceJobsPagination: LazyComponent<typeof import("../layers/recruitment/components/workforce/JobsPagination.vue")['default']>
export const LazyWorkforceMatchDetailsModal: LazyComponent<typeof import("../layers/recruitment/components/workforce/MatchDetailsModal.vue")['default']>
export const LazyWorkforceMatchFilters: LazyComponent<typeof import("../layers/recruitment/components/workforce/MatchFilters.vue")['default']>
export const LazyWorkforceMatchesPagination: LazyComponent<typeof import("../layers/recruitment/components/workforce/MatchesPagination.vue")['default']>
export const LazyWorkforceWorkerDetailsModal: LazyComponent<typeof import("../layers/recruitment/components/workforce/WorkerDetailsModal.vue")['default']>
export const LazyWorkforceWorkerFormModal: LazyComponent<typeof import("../layers/recruitment/components/workforce/WorkerFormModal.vue")['default']>
export const LazyWorkforceWorkersPagination: LazyComponent<typeof import("../layers/recruitment/components/workforce/WorkersPagination.vue")['default']>
export const LazyTairoSidebarBurger: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarBurger.vue")['default']>
export const LazyTairoSidebarCircularMenu: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarCircularMenu.vue")['default']>
export const LazyTairoSidebarLayout: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarLayout.vue")['default']>
export const LazyTairoSidebarNavigationItem: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarNavigationItem.vue")['default']>
export const LazyTairoSidebarNavigationPanel: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarNavigationPanel.vue")['default']>
export const LazyTairoSidebarToolbar: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarToolbar.vue")['default']>
export const LazyTairoSidebarTools: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSidebarTools.vue")['default']>
export const LazyTairoSubsidebar: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebar.vue")['default']>
export const LazyTairoSubsidebarHeader: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarHeader.vue")['default']>
export const LazyTairoSubsidebarMenu: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenu.vue")['default']>
export const LazyTairoSubsidebarMenuCollapseLinks: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuCollapseLinks.vue")['default']>
export const LazyTairoSubsidebarMenuDivider: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuDivider.vue")['default']>
export const LazyTairoSubsidebarMenuLink: LazyComponent<typeof import("../layers/tairo-layout-sidebar/components/TairoSubsidebarMenuLink.vue")['default']>
export const LazyLandingBenefits: LazyComponent<typeof import("../layers/landing/components/LandingBenefits.vue")['default']>
export const LazyLandingContent: LazyComponent<typeof import("../layers/landing/components/LandingContent.vue")['default']>
export const LazyLandingCta: LazyComponent<typeof import("../layers/landing/components/LandingCta.vue")['default']>
export const LazyLandingCustomizer: LazyComponent<typeof import("../layers/landing/components/LandingCustomizer.vue")['default']>
export const LazyLandingDemoLink: LazyComponent<typeof import("../layers/landing/components/LandingDemoLink.vue")['default']>
export const LazyLandingDemos: LazyComponent<typeof import("../layers/landing/components/LandingDemos.vue")['default']>
export const LazyLandingFeatures: LazyComponent<typeof import("../layers/landing/components/LandingFeatures.vue")['default']>
export const LazyLandingFeaturesTile: LazyComponent<typeof import("../layers/landing/components/LandingFeaturesTile.vue")['default']>
export const LazyLandingFooter: LazyComponent<typeof import("../layers/landing/components/LandingFooter.vue")['default']>
export const LazyLandingHero: LazyComponent<typeof import("../layers/landing/components/LandingHero.vue")['default']>
export const LazyLandingHeroMockup: LazyComponent<typeof import("../layers/landing/components/LandingHeroMockup.vue")['default']>
export const LazyLandingLayers: LazyComponent<typeof import("../layers/landing/components/LandingLayers.vue")['default']>
export const LazyLandingLayersBox: LazyComponent<typeof import("../layers/landing/components/LandingLayersBox.vue")['default']>
export const LazyLandingLayout: LazyComponent<typeof import("../layers/landing/components/LandingLayout.vue")['default']>
export const LazyLandingLayouts: LazyComponent<typeof import("../layers/landing/components/LandingLayouts.vue")['default']>
export const LazyLandingNavbar: LazyComponent<typeof import("../layers/landing/components/LandingNavbar.vue")['default']>
export const LazyMockupFollowersCompact: LazyComponent<typeof import("../layers/landing/components/MockupFollowersCompact.vue")['default']>
export const LazyMockupInboxMessage: LazyComponent<typeof import("../layers/landing/components/MockupInboxMessage.vue")['default']>
export const LazyMockupInfoBadges: LazyComponent<typeof import("../layers/landing/components/MockupInfoBadges.vue")['default']>
export const LazyMockupProgressCircle: LazyComponent<typeof import("../layers/landing/components/MockupProgressCircle.vue")['default']>
export const LazyMockupTeamSearchCompact: LazyComponent<typeof import("../layers/landing/components/MockupTeamSearchCompact.vue")['default']>
export const LazyMockupVideoCompact: LazyComponent<typeof import("../layers/landing/components/MockupVideoCompact.vue")['default']>
export const LazySVGMorph: LazyComponent<typeof import("../layers/landing/components/SVGMorph.vue")['default']>
export const LazyTairoIconnavCircularMenu: LazyComponent<typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavCircularMenu.vue")['default']>
export const LazyTairoIconnavFooter: LazyComponent<typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavFooter.vue")['default']>
export const LazyTairoIconnavLayout: LazyComponent<typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavLayout.vue")['default']>
export const LazyTairoIconnavNavigation: LazyComponent<typeof import("../layers/tairo-layout-iconnav/components/TairoIconnavNavigation.vue")['default']>
export const LazyBaseAccordion: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAccordion.vue")['default']>
export const LazyBaseAvatar: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAvatar.vue")['default']>
export const LazyBaseAvatarGroup: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseAvatarGroup.vue")['default']>
export const LazyBaseBreadcrumb: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseBreadcrumb.vue")['default']>
export const LazyBaseButton: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButton.vue")['default']>
export const LazyBaseButtonAction: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonAction.vue")['default']>
export const LazyBaseButtonClose: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonClose.vue")['default']>
export const LazyBaseButtonGroup: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonGroup.vue")['default']>
export const LazyBaseButtonIcon: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseButtonIcon.vue")['default']>
export const LazyBaseCard: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseCard.vue")['default']>
export const LazyBaseDropdown: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdown.vue")['default']>
export const LazyBaseDropdownDivider: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdownDivider.vue")['default']>
export const LazyBaseDropdownItem: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseDropdownItem.vue")['default']>
export const LazyBaseFocusLoop: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseFocusLoop.vue")['default']>
export const LazyBaseHeading: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseHeading.vue")['default']>
export const LazyBaseIconBox: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseIconBox.vue")['default']>
export const LazyBaseKbd: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseKbd.vue")['default']>
export const LazyBaseLink: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseLink.vue")['default']>
export const LazyBaseList: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseList.vue")['default']>
export const LazyBaseListItem: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseListItem.vue")['default']>
export const LazyBaseMessage: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseMessage.vue")['default']>
export const LazyBasePagination: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePagination.vue")['default']>
export const LazyBaseParagraph: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseParagraph.vue")['default']>
export const LazyBasePlaceholderPage: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePlaceholderPage.vue")['default']>
export const LazyBasePlaceload: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BasePlaceload.vue")['default']>
export const LazyBaseProgress: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProgress.vue")['default']>
export const LazyBaseProgressCircle: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProgressCircle.vue")['default']>
export const LazyBaseProse: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseProse.vue")['default']>
export const LazyBaseSnack: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseSnack.vue")['default']>
export const LazyBaseTabSlider: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTabSlider.vue")['default']>
export const LazyBaseTabs: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTabs.vue")['default']>
export const LazyBaseTag: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseTag.vue")['default']>
export const LazyBaseText: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseText.vue")['default']>
export const LazyBaseThemeSwitch: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseThemeSwitch.vue")['default']>
export const LazyBaseThemeToggle: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/base/BaseThemeToggle.vue")['default']>
export const LazyIconCheck: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconCheck.vue")['default']>
export const LazyIconCheckCircle: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconCheckCircle.vue")['default']>
export const LazyIconChevronDown: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconChevronDown.vue")['default']>
export const LazyIconClose: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconClose.vue")['default']>
export const LazyIconIndeterminate: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconIndeterminate.vue")['default']>
export const LazyIconMinus: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconMinus.vue")['default']>
export const LazyIconMoon: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconMoon.vue")['default']>
export const LazyIconPlus: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconPlus.vue")['default']>
export const LazyIconSun: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/icon/IconSun.vue")['default']>
export const LazyBaseAutocomplete: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseAutocomplete.vue")['default']>
export const LazyBaseAutocompleteItem: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseAutocompleteItem.vue")['default']>
export const LazyBaseCheckbox: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckbox.vue")['default']>
export const LazyBaseCheckboxAnimated: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckboxAnimated.vue")['default']>
export const LazyBaseCheckboxHeadless: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseCheckboxHeadless.vue")['default']>
export const LazyBaseFullscreenDropfile: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseFullscreenDropfile.vue")['default']>
export const LazyBaseInput: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInput.vue")['default']>
export const LazyBaseInputFile: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputFile.vue")['default']>
export const LazyBaseInputFileHeadless: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputFileHeadless.vue")['default']>
export const LazyBaseInputHelpText: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputHelpText.vue")['default']>
export const LazyBaseInputNumber: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseInputNumber.vue")['default']>
export const LazyBaseListbox: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseListbox.vue")['default']>
export const LazyBaseListboxItem: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseListboxItem.vue")['default']>
export const LazyBaseRadio: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseRadio.vue")['default']>
export const LazyBaseRadioHeadless: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseRadioHeadless.vue")['default']>
export const LazyBaseSelect: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSelect.vue")['default']>
export const LazyBaseSwitchBall: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSwitchBall.vue")['default']>
export const LazyBaseSwitchThin: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseSwitchThin.vue")['default']>
export const LazyBaseTextarea: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTextarea.vue")['default']>
export const LazyBaseTreeSelect: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTreeSelect.vue")['default']>
export const LazyBaseTreeSelectItem: LazyComponent<typeof import("../node_modules/.pnpm/@shuriken-ui+nuxt@3.6.1_magicast@0.3.5_nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0__4yqjyqergjj3jep7ndmzcykazu/node_modules/@shuriken-ui/nuxt/components/form/BaseTreeSelectItem.vue")['default']>
export const LazyNuxtWelcome: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/welcome.vue")['default']>
export const LazyNuxtLayout: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
export const LazyNuxtErrorBoundary: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']>
export const LazyClientOnly: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/client-only")['default']>
export const LazyDevOnly: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/dev-only")['default']>
export const LazyServerPlaceholder: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtLink: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-link")['default']>
export const LazyNuxtLoadingIndicator: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
export const LazyNuxtImg: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
export const LazyNuxtPicture: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
export const LazyIonAnimation: LazyComponent<typeof import("../node_modules/.pnpm/@nuxtjs+ionic@0.16.0_@stencil+core@4.28.2_magicast@0.3.5_vue-router@4.5.0_vue@3.5.13_typescri_kvogt2gdziafhlhhzxvd6o57ta/node_modules/@nuxtjs/ionic/dist/runtime/components/IonAnimation.vue")['default']>
export const LazyIonAccordion: LazyComponent<typeof import("@ionic/vue")['IonAccordion']>
export const LazyIonAccordionGroup: LazyComponent<typeof import("@ionic/vue")['IonAccordionGroup']>
export const LazyIonActionSheet: LazyComponent<typeof import("@ionic/vue")['IonActionSheet']>
export const LazyIonAlert: LazyComponent<typeof import("@ionic/vue")['IonAlert']>
export const LazyIonApp: LazyComponent<typeof import("@ionic/vue")['IonApp']>
export const LazyIonAvatar: LazyComponent<typeof import("@ionic/vue")['IonAvatar']>
export const LazyIonBackButton: LazyComponent<typeof import("@ionic/vue")['IonBackButton']>
export const LazyIonBackdrop: LazyComponent<typeof import("@ionic/vue")['IonBackdrop']>
export const LazyIonBadge: LazyComponent<typeof import("@ionic/vue")['IonBadge']>
export const LazyIonBreadcrumb: LazyComponent<typeof import("@ionic/vue")['IonBreadcrumb']>
export const LazyIonBreadcrumbs: LazyComponent<typeof import("@ionic/vue")['IonBreadcrumbs']>
export const LazyIonButton: LazyComponent<typeof import("@ionic/vue")['IonButton']>
export const LazyIonButtons: LazyComponent<typeof import("@ionic/vue")['IonButtons']>
export const LazyIonCard: LazyComponent<typeof import("@ionic/vue")['IonCard']>
export const LazyIonCardContent: LazyComponent<typeof import("@ionic/vue")['IonCardContent']>
export const LazyIonCardHeader: LazyComponent<typeof import("@ionic/vue")['IonCardHeader']>
export const LazyIonCardSubtitle: LazyComponent<typeof import("@ionic/vue")['IonCardSubtitle']>
export const LazyIonCardTitle: LazyComponent<typeof import("@ionic/vue")['IonCardTitle']>
export const LazyIonCheckbox: LazyComponent<typeof import("@ionic/vue")['IonCheckbox']>
export const LazyIonChip: LazyComponent<typeof import("@ionic/vue")['IonChip']>
export const LazyIonCol: LazyComponent<typeof import("@ionic/vue")['IonCol']>
export const LazyIonContent: LazyComponent<typeof import("@ionic/vue")['IonContent']>
export const LazyIonDatetime: LazyComponent<typeof import("@ionic/vue")['IonDatetime']>
export const LazyIonDatetimeButton: LazyComponent<typeof import("@ionic/vue")['IonDatetimeButton']>
export const LazyIonFab: LazyComponent<typeof import("@ionic/vue")['IonFab']>
export const LazyIonFabButton: LazyComponent<typeof import("@ionic/vue")['IonFabButton']>
export const LazyIonFabList: LazyComponent<typeof import("@ionic/vue")['IonFabList']>
export const LazyIonFooter: LazyComponent<typeof import("@ionic/vue")['IonFooter']>
export const LazyIonGrid: LazyComponent<typeof import("@ionic/vue")['IonGrid']>
export const LazyIonHeader: LazyComponent<typeof import("@ionic/vue")['IonHeader']>
export const LazyIonIcon: LazyComponent<typeof import("@ionic/vue")['IonIcon']>
export const LazyIonImg: LazyComponent<typeof import("@ionic/vue")['IonImg']>
export const LazyIonInfiniteScroll: LazyComponent<typeof import("@ionic/vue")['IonInfiniteScroll']>
export const LazyIonInfiniteScrollContent: LazyComponent<typeof import("@ionic/vue")['IonInfiniteScrollContent']>
export const LazyIonInput: LazyComponent<typeof import("@ionic/vue")['IonInput']>
export const LazyIonInputPasswordToggle: LazyComponent<typeof import("@ionic/vue")['IonInputPasswordToggle']>
export const LazyIonItem: LazyComponent<typeof import("@ionic/vue")['IonItem']>
export const LazyIonItemDivider: LazyComponent<typeof import("@ionic/vue")['IonItemDivider']>
export const LazyIonItemGroup: LazyComponent<typeof import("@ionic/vue")['IonItemGroup']>
export const LazyIonItemOption: LazyComponent<typeof import("@ionic/vue")['IonItemOption']>
export const LazyIonItemOptions: LazyComponent<typeof import("@ionic/vue")['IonItemOptions']>
export const LazyIonItemSliding: LazyComponent<typeof import("@ionic/vue")['IonItemSliding']>
export const LazyIonLabel: LazyComponent<typeof import("@ionic/vue")['IonLabel']>
export const LazyIonList: LazyComponent<typeof import("@ionic/vue")['IonList']>
export const LazyIonListHeader: LazyComponent<typeof import("@ionic/vue")['IonListHeader']>
export const LazyIonLoading: LazyComponent<typeof import("@ionic/vue")['IonLoading']>
export const LazyIonMenu: LazyComponent<typeof import("@ionic/vue")['IonMenu']>
export const LazyIonMenuButton: LazyComponent<typeof import("@ionic/vue")['IonMenuButton']>
export const LazyIonMenuToggle: LazyComponent<typeof import("@ionic/vue")['IonMenuToggle']>
export const LazyIonModal: LazyComponent<typeof import("@ionic/vue")['IonModal']>
export const LazyIonNav: LazyComponent<typeof import("@ionic/vue")['IonNav']>
export const LazyIonNavLink: LazyComponent<typeof import("@ionic/vue")['IonNavLink']>
export const LazyIonNote: LazyComponent<typeof import("@ionic/vue")['IonNote']>
export const LazyIonPage: LazyComponent<typeof import("@ionic/vue")['IonPage']>
export const LazyIonPicker: LazyComponent<typeof import("@ionic/vue")['IonPicker']>
export const LazyIonPickerColumn: LazyComponent<typeof import("@ionic/vue")['IonPickerColumn']>
export const LazyIonPickerColumnOption: LazyComponent<typeof import("@ionic/vue")['IonPickerColumnOption']>
export const LazyIonPickerLegacy: LazyComponent<typeof import("@ionic/vue")['IonPickerLegacy']>
export const LazyIonPopover: LazyComponent<typeof import("@ionic/vue")['IonPopover']>
export const LazyIonProgressBar: LazyComponent<typeof import("@ionic/vue")['IonProgressBar']>
export const LazyIonRadio: LazyComponent<typeof import("@ionic/vue")['IonRadio']>
export const LazyIonRadioGroup: LazyComponent<typeof import("@ionic/vue")['IonRadioGroup']>
export const LazyIonRange: LazyComponent<typeof import("@ionic/vue")['IonRange']>
export const LazyIonRefresher: LazyComponent<typeof import("@ionic/vue")['IonRefresher']>
export const LazyIonRefresherContent: LazyComponent<typeof import("@ionic/vue")['IonRefresherContent']>
export const LazyIonReorder: LazyComponent<typeof import("@ionic/vue")['IonReorder']>
export const LazyIonReorderGroup: LazyComponent<typeof import("@ionic/vue")['IonReorderGroup']>
export const LazyIonRippleEffect: LazyComponent<typeof import("@ionic/vue")['IonRippleEffect']>
export const LazyIonRouterOutlet: LazyComponent<typeof import("@ionic/vue")['IonRouterOutlet']>
export const LazyIonRow: LazyComponent<typeof import("@ionic/vue")['IonRow']>
export const LazyIonSearchbar: LazyComponent<typeof import("@ionic/vue")['IonSearchbar']>
export const LazyIonSegment: LazyComponent<typeof import("@ionic/vue")['IonSegment']>
export const LazyIonSegmentButton: LazyComponent<typeof import("@ionic/vue")['IonSegmentButton']>
export const LazyIonSegmentContent: LazyComponent<typeof import("@ionic/vue")['IonSegmentContent']>
export const LazyIonSegmentView: LazyComponent<typeof import("@ionic/vue")['IonSegmentView']>
export const LazyIonSelect: LazyComponent<typeof import("@ionic/vue")['IonSelect']>
export const LazyIonSelectModal: LazyComponent<typeof import("@ionic/vue")['IonSelectModal']>
export const LazyIonSelectOption: LazyComponent<typeof import("@ionic/vue")['IonSelectOption']>
export const LazyIonSkeletonText: LazyComponent<typeof import("@ionic/vue")['IonSkeletonText']>
export const LazyIonSpinner: LazyComponent<typeof import("@ionic/vue")['IonSpinner']>
export const LazyIonSplitPane: LazyComponent<typeof import("@ionic/vue")['IonSplitPane']>
export const LazyIonTab: LazyComponent<typeof import("@ionic/vue")['IonTab']>
export const LazyIonTabs: LazyComponent<typeof import("@ionic/vue")['IonTabs']>
export const LazyIonTabBar: LazyComponent<typeof import("@ionic/vue")['IonTabBar']>
export const LazyIonTabButton: LazyComponent<typeof import("@ionic/vue")['IonTabButton']>
export const LazyIonText: LazyComponent<typeof import("@ionic/vue")['IonText']>
export const LazyIonTextarea: LazyComponent<typeof import("@ionic/vue")['IonTextarea']>
export const LazyIonThumbnail: LazyComponent<typeof import("@ionic/vue")['IonThumbnail']>
export const LazyIonTitle: LazyComponent<typeof import("@ionic/vue")['IonTitle']>
export const LazyIonToast: LazyComponent<typeof import("@ionic/vue")['IonToast']>
export const LazyIonToggle: LazyComponent<typeof import("@ionic/vue")['IonToggle']>
export const LazyIonToolbar: LazyComponent<typeof import("@ionic/vue")['IonToolbar']>
export const LazyNuxtLinkLocale: LazyComponent<typeof import("../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale")['default']>
export const LazySwitchLocalePathLink: LazyComponent<typeof import("../node_modules/.pnpm/@nuxtjs+i18n@9.4.0_@vue+compiler-dom@3.5.13_eslint@8.57.1_magicast@0.3.5_rollup@4.36.0_vue@3.5.13_typescript@5.8.2_/node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink")['default']>
export const LazyColorScheme: LazyComponent<typeof import("../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue")['default']>
export const LazyIcon: LazyComponent<typeof import("../node_modules/.pnpm/@nuxt+icon@1.11.0_magicast@0.3.5_vite@6.2.2_@types+node@22.14.0_jiti@2.4.2_lightningcss@1.29._4n4hvoiqlixjyr6n4h4n6fje7e/node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
export const LazyNuxtPage: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/pages/runtime/page")['default']>
export const LazyNoScript: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['NoScript']>
export const LazyLink: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Link']>
export const LazyBase: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Base']>
export const LazyTitle: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Title']>
export const LazyMeta: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Meta']>
export const LazyStyle: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Style']>
export const LazyHead: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Head']>
export const LazyHtml: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Html']>
export const LazyBody: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/head/runtime/components")['Body']>
export const LazyNuxtIsland: LazyComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/nuxt-island")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<IslandComponent<typeof import("../node_modules/.pnpm/nuxt@3.16.1_@parcel+watcher@2.5.1_@types+node@22.14.0_db0@0.3.1_eslint@8.57.1_ioredis@5.6.0_l_67odzl4ijlnfxrfugezt5vg5yq/node_modules/nuxt/dist/app/components/server-placeholder")['default']>>

export const componentNames: string[]
