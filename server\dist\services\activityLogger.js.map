{"version": 3, "file": "activityLogger.js", "sourceRoot": "", "sources": ["../../services/activityLogger.ts"], "names": [], "mappings": ";;;AAAA,2CAA8C;AAG9C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAmBlC,MAAa,cAAc;IACzB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAqB;QACpC,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,IAAI,CAAC,IAAW;oBACtB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,SAAS;oBAChC,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,MAAM;oBACjC,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,oDAAoD;QACtD,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,GAAY,EAAE,IAAsD;QAC9F,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACxC,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAExC,MAAM,IAAI,CAAC,GAAG,CAAC;YACb,GAAG,IAAI;YACP,SAAS;YACT,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,GAAY,EAAE,UAAmB,IAAI;QAC7E,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE;YAC3B,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,sBAAsB;YACtD,WAAW,EAAE,OAAO;gBAClB,CAAC,CAAC,0CAA0C;gBAC5C,CAAC,CAAC,+BAA+B;YACnC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YACpC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ;YACtC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;SACvC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,GAAY;QACrD,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE;YAC3B,KAAK,EAAE,aAAa;YACpB,WAAW,EAAE,+BAA+B;YAC5C,MAAM;YACN,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,MAAM;SACjB,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,OAAY,EAAE,GAAY,EAAE,eAAwB;QAC7F,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,IAAI,EAAE,aAAa;YACnB,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE;YAC3B,KAAK,EAAE,sBAAsB;YAC7B,WAAW,EAAE,sCAAsC;YACnD,MAAM,EAAE,eAAe,IAAI,MAAM;YACjC,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,MAAM;YAChB,OAAO;YACP,QAAQ,EAAE;gBACR,YAAY,EAAE,MAAM;gBACpB,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;aACpC;SACF,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,GAAY,EAAE,eAAuB;QAC9E,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,IAAI,EAAE,aAAa;YACnB,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE;YAC3B,KAAK,EAAE,kBAAkB;YACzB,WAAW,EAAE,8CAA8C;YAC3D,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,MAAM;YAChB,QAAQ,EAAE;gBACR,YAAY,EAAE,MAAM;aACrB;SACF,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,GAAY;QACzD,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,IAAI,EAAE,iBAAiB;YACvB,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE;YAC3B,KAAK,EAAE,kBAAkB;YACzB,WAAW,EAAE,2BAA2B;YACxC,MAAM;YACN,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,MAAM;SACjB,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAC3B,KAAa,EACb,WAAmB,EACnB,GAAY,EACZ,MAAe,EACf,WAA8D,SAAS;QAEvE,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,IAAI,EAAE,oBAAoB;YAC1B,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE,UAAU;YAClB,KAAK;YACL,WAAW;YACX,MAAM;YACN,MAAM,EAAE,QAAQ;YAChB,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAC3B,SAAyC,EACzC,MAAc,EACd,QAAgB,EAChB,KAAa,EACb,WAAmB,EACnB,GAAY,EACZ,MAAc,EACd,OAAa;QAEb,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,SAAS,CAAC,WAAW,EAAE;YAC/B,MAAM,EAAE,iBAAiB;YACzB,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW;YACX,MAAM;YACN,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,MAAM;YAChB,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,cAAc,CACzB,IAAY,EACZ,KAAa,EACb,WAAmB,EACnB,QAAc,EACd,WAA8D,MAAM;QAEpE,MAAM,IAAI,CAAC,GAAG,CAAC;YACb,IAAI;YACJ,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE,QAAQ;YAChB,KAAK;YACL,WAAW;YACX,MAAM,EAAE,SAAS;YACjB,QAAQ;YACR,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,GAAY;QACrC,OAAO,CACJ,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAY,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACxD,GAAG,CAAC,OAAO,CAAC,WAAW,CAAY;YACpC,GAAG,CAAC,UAAU,EAAE,aAAa;YAC7B,GAAG,CAAC,MAAM,EAAE,aAAa;YACzB,SAAS,CACV,CAAC;IACJ,CAAC;CACF;AAnMD,wCAmMC;AAED,kBAAe,cAAc,CAAC"}