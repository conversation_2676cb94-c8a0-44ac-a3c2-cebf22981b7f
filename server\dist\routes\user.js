"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const user_controller_js_1 = require("../controllers/user.controller.js");
const profile_controller_js_1 = require("../controllers/user/profile.controller.js");
const router = express_1.default.Router();
// Get all users (protected route)
router.get("/", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(user_controller_js_1.getAllUsers));
// Create new user
router.post("/", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(user_controller_js_1.createUser));
// Update user roles
router.put("/roles", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(user_controller_js_1.updateUserRoles));
// Get current user
router.get("/me", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(profile_controller_js_1.getCurrentUser));
// Get user profile
router.get("/profile", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(profile_controller_js_1.getUserProfile));
// Update user profile
router.put("/profile", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(profile_controller_js_1.updateUserProfile));
// Update user preferences
router.put("/preferences", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(profile_controller_js_1.updateUserPreferences));
// Change user password
router.put("/change-password", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(profile_controller_js_1.changePassword));
exports.default = router;
//# sourceMappingURL=user.js.map