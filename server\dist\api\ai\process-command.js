"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const openai_1 = require("openai");
// Initialize OpenAI client
const openai = new openai_1.OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});
// Create a custom error function
const createError = (options) => {
    const error = new Error(options.message);
    error.statusCode = options.statusCode;
    return error;
};
exports.default = async (req, res) => {
    const { command, context } = req.body;
    const moduleCommands = {
        hr: [
            "create-worker",
            "create-match",
            "view-statistics",
            "request-time-off",
            "start-performance-review",
        ],
        companies: [
            "create-company",
            "edit-company",
            "view-workforce-needs",
            "create-request",
            "view-risk-assessment",
        ],
        production: [
            "create-project",
            "create-task",
            "create-checklist",
            "view-statistics",
            "update-task-status",
        ],
        budget: [
            "create-budget",
            "add-expense",
            "add-income",
            "view-reports",
            "create-forecast",
        ],
        accounting: [
            "create-entry",
            "view-ledger",
            "reconcile-accounts",
            "generate-report",
            "view-tax-summary",
        ],
    };
    const systemPrompt = `
    You are an AI assistant helping users navigate and interact with an enterprise management application.

    Available Modules and Commands:
    ${Object.entries(moduleCommands)
        .map(([module, commands]) => `${module}: ${commands.join(", ")}`)
        .join("\n")}

    Current Context:
    - Path: ${context.currentPath}
    - Module: ${context.currentModule}
    - Available UI Actions: ${context.availableIntents.join(", ")}

    Parse the command and return:
    {
      "action": "navigate:[path]" or "ui:[intent]",
      "parameters": { additional parameters },
      "message": "Confirmation message"
    }
  `;
    try {
        const completion = await openai.chat.completions.create({
            model: "gpt-4-turbo",
            messages: [
                { role: "system", content: systemPrompt },
                { role: "user", content: command },
            ],
            temperature: 0.2,
            response_format: { type: "json_object" },
        });
        return res.json(JSON.parse(completion.choices[0].message.content || "{}"));
    }
    catch (error) {
        console.error("Error processing AI command:", error);
        return res.status(500).json({
            error: "Failed to process AI command",
            details: error.message,
        });
    }
};
//# sourceMappingURL=process-command.js.map