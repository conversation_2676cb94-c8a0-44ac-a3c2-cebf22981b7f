"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCampaignTemplates = exports.addCampaignMetric = exports.removeLeadFromCampaign = exports.addLeadToCampaign = exports.deleteCampaign = exports.updateCampaign = exports.createCampaign = exports.getCampaignById = exports.getAllCampaigns = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Get all campaigns
const getAllCampaigns = async (req, res) => {
    try {
        const { type, status, search, sortBy = "createdAt", sortOrder = "desc", page = 1, limit = 10, } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        // Build filter conditions
        const where = {};
        if (type) {
            where.type = type;
        }
        if (status) {
            where.status = status;
        }
        if (search) {
            where.OR = [
                { name: { contains: search, mode: "insensitive" } },
                { description: { contains: search, mode: "insensitive" } },
                { targetAudience: { contains: search, mode: "insensitive" } },
            ];
        }
        // Get total count for pagination
        const totalCount = await prisma_js_1.prisma.campaign.count({ where });
        // Get campaigns with pagination, sorting and filtering
        const campaigns = await prisma_js_1.prisma.campaign.findMany({
            where,
            include: {
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                _count: {
                    select: {
                        leads: true,
                        metrics: true,
                    },
                },
            },
            orderBy: {
                [sortBy]: sortOrder,
            },
            skip,
            take: Number(limit),
        });
        res.status(200).json({
            campaigns,
            pagination: {
                total: totalCount,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(totalCount / Number(limit)),
            },
        });
    }
    catch (error) {
        console.error("Error fetching campaigns:", error);
        res.status(500).json({ error: "Failed to fetch campaigns" });
    }
};
exports.getAllCampaigns = getAllCampaigns;
// Get campaign by ID
const getCampaignById = async (req, res) => {
    try {
        const { id } = req.params;
        const campaign = await prisma_js_1.prisma.campaign.findUnique({
            where: { id: Number(id) },
            include: {
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
                leads: {
                    include: {
                        lead: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                email: true,
                                company: true,
                                status: true,
                            },
                        },
                    },
                },
                metrics: {
                    orderBy: {
                        date: "desc",
                    },
                },
            },
        });
        if (!campaign) {
            res.status(404).json({ error: "Campaign not found" });
            return;
        }
        res.status(200).json(campaign);
    }
    catch (error) {
        console.error("Error fetching campaign:", error);
        res.status(500).json({ error: "Failed to fetch campaign" });
    }
};
exports.getCampaignById = getCampaignById;
// Create a new campaign
const createCampaign = async (req, res) => {
    try {
        const { name, description, type, status, budget, startDate, endDate, targetAudience, expectedRevenue, } = req.body;
        const userId = req.user.id;
        // Create campaign
        const campaign = await prisma_js_1.prisma.campaign.create({
            data: {
                name,
                description,
                type,
                status,
                budget,
                actualCost: 0,
                startDate: new Date(startDate),
                endDate: endDate ? new Date(endDate) : null,
                targetAudience,
                expectedRevenue,
                actualRevenue: 0,
                createdById: userId,
            },
            include: {
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
            },
        });
        res.status(201).json(campaign);
    }
    catch (error) {
        console.error("Error creating campaign:", error);
        res.status(500).json({ error: "Failed to create campaign" });
    }
};
exports.createCampaign = createCampaign;
// Update a campaign
const updateCampaign = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description, type, status, budget, actualCost, startDate, endDate, targetAudience, expectedRevenue, actualRevenue, } = req.body;
        // Check if campaign exists
        const existingCampaign = await prisma_js_1.prisma.campaign.findUnique({
            where: { id: Number(id) },
        });
        if (!existingCampaign) {
            res.status(404).json({ error: "Campaign not found" });
            return;
        }
        // Update campaign
        const updatedCampaign = await prisma_js_1.prisma.campaign.update({
            where: { id: Number(id) },
            data: {
                name,
                description,
                type,
                status,
                budget,
                actualCost,
                startDate: startDate ? new Date(startDate) : undefined,
                endDate: endDate ? new Date(endDate) : null,
                targetAudience,
                expectedRevenue,
                actualRevenue,
                updatedAt: new Date(),
            },
            include: {
                createdBy: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                _count: {
                    select: {
                        leads: true,
                        metrics: true,
                    },
                },
            },
        });
        res.status(200).json(updatedCampaign);
    }
    catch (error) {
        console.error("Error updating campaign:", error);
        res.status(500).json({ error: "Failed to update campaign" });
    }
};
exports.updateCampaign = updateCampaign;
// Delete a campaign
const deleteCampaign = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if campaign exists
        const campaign = await prisma_js_1.prisma.campaign.findUnique({
            where: { id: Number(id) },
        });
        if (!campaign) {
            res.status(404).json({ error: "Campaign not found" });
            return;
        }
        // Delete campaign (cascade will handle related records)
        await prisma_js_1.prisma.campaign.delete({
            where: { id: Number(id) },
        });
        res.status(200).json({ message: "Campaign deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting campaign:", error);
        res.status(500).json({ error: "Failed to delete campaign" });
    }
};
exports.deleteCampaign = deleteCampaign;
// Add lead to campaign
const addLeadToCampaign = async (req, res) => {
    try {
        const { id } = req.params;
        const { leadId, status = "sent" } = req.body;
        // Check if campaign exists
        const campaign = await prisma_js_1.prisma.campaign.findUnique({
            where: { id: Number(id) },
        });
        if (!campaign) {
            res.status(404).json({ error: "Campaign not found" });
            return;
        }
        // Check if lead exists
        const lead = await prisma_js_1.prisma.lead.findUnique({
            where: { id: Number(leadId) },
        });
        if (!lead) {
            res.status(404).json({ error: "Lead not found" });
            return;
        }
        // Check if lead is already in campaign
        const existingCampaignLead = await prisma_js_1.prisma.campaignLead.findUnique({
            where: {
                campaignId_leadId: {
                    campaignId: Number(id),
                    leadId: Number(leadId),
                },
            },
        });
        if (existingCampaignLead) {
            res.status(400).json({ error: "Lead already added to this campaign" });
            return;
        }
        // Add lead to campaign
        const campaignLead = await prisma_js_1.prisma.campaignLead.create({
            data: {
                campaignId: Number(id),
                leadId: Number(leadId),
                status,
            },
            include: {
                lead: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        company: true,
                        status: true,
                    },
                },
            },
        });
        res.status(201).json(campaignLead);
    }
    catch (error) {
        console.error("Error adding lead to campaign:", error);
        res.status(500).json({ error: "Failed to add lead to campaign" });
    }
};
exports.addLeadToCampaign = addLeadToCampaign;
// Remove lead from campaign
const removeLeadFromCampaign = async (req, res) => {
    try {
        const { id, leadId } = req.params;
        // Check if campaign lead exists
        const campaignLead = await prisma_js_1.prisma.campaignLead.findUnique({
            where: {
                campaignId_leadId: {
                    campaignId: Number(id),
                    leadId: Number(leadId),
                },
            },
        });
        if (!campaignLead) {
            res.status(404).json({ error: "Lead not found in this campaign" });
            return;
        }
        // Remove lead from campaign
        await prisma_js_1.prisma.campaignLead.delete({
            where: {
                campaignId_leadId: {
                    campaignId: Number(id),
                    leadId: Number(leadId),
                },
            },
        });
        res
            .status(200)
            .json({ message: "Lead removed from campaign successfully" });
    }
    catch (error) {
        console.error("Error removing lead from campaign:", error);
        res.status(500).json({ error: "Failed to remove lead from campaign" });
    }
};
exports.removeLeadFromCampaign = removeLeadFromCampaign;
// Add campaign metric
const addCampaignMetric = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, value, date } = req.body;
        // Check if campaign exists
        const campaign = await prisma_js_1.prisma.campaign.findUnique({
            where: { id: Number(id) },
        });
        if (!campaign) {
            res.status(404).json({ error: "Campaign not found" });
            return;
        }
        // Check if metric already exists for this date
        const existingMetric = await prisma_js_1.prisma.campaignMetric.findUnique({
            where: {
                campaignId_name_date: {
                    campaignId: Number(id),
                    name,
                    date: new Date(date),
                },
            },
        });
        let metric;
        if (existingMetric) {
            // Update existing metric
            metric = await prisma_js_1.prisma.campaignMetric.update({
                where: {
                    id: existingMetric.id,
                },
                data: {
                    value,
                },
            });
        }
        else {
            // Create new metric
            metric = await prisma_js_1.prisma.campaignMetric.create({
                data: {
                    campaignId: Number(id),
                    name,
                    value,
                    date: new Date(date),
                },
            });
        }
        res.status(201).json(metric);
    }
    catch (error) {
        console.error("Error adding campaign metric:", error);
        res.status(500).json({ error: "Failed to add campaign metric" });
    }
};
exports.addCampaignMetric = addCampaignMetric;
// Get campaign templates
const getCampaignTemplates = async (req, res) => {
    try {
        // In a real implementation, this would fetch campaign templates from the database
        // For now, return some predefined templates
        const templates = [
            {
                id: "1",
                name: "Email Newsletter",
                description: "Standard email newsletter campaign",
                type: "EMAIL",
                targetAudience: "All customers",
                content: {
                    subject: "Monthly Newsletter",
                    body: "Hello {first_name},\n\nHere are our latest updates...",
                    callToAction: "Learn More",
                },
            },
            {
                id: "2",
                name: "Product Launch",
                description: "Campaign for new product launch",
                type: "EMAIL",
                targetAudience: "Existing customers",
                content: {
                    subject: "Introducing Our New Product",
                    body: "Hello {first_name},\n\nWe're excited to announce our new product...",
                    callToAction: "See the Product",
                },
            },
            {
                id: "3",
                name: "Webinar Invitation",
                description: "Invitation to upcoming webinar",
                type: "EMAIL",
                targetAudience: "Qualified leads",
                content: {
                    subject: "Join Our Exclusive Webinar",
                    body: "Hello {first_name},\n\nYou're invited to our upcoming webinar...",
                    callToAction: "Register Now",
                },
            },
            {
                id: "4",
                name: "Social Media Campaign",
                description: "Multi-platform social media campaign",
                type: "SOCIAL_MEDIA",
                targetAudience: "New prospects",
                content: {
                    posts: [
                        {
                            platform: "LinkedIn",
                            content: "We're excited to share...",
                            image: "campaign_image.jpg",
                        },
                        {
                            platform: "Twitter",
                            content: "Check out our latest...",
                            image: "campaign_image.jpg",
                        },
                    ],
                },
            },
            {
                id: "5",
                name: "Follow-up Sequence",
                description: "Multi-touch follow-up sequence",
                type: "EMAIL",
                targetAudience: "Recent leads",
                content: {
                    sequence: [
                        {
                            day: 1,
                            subject: "Thanks for your interest",
                            body: "Hello {first_name},\n\nThank you for your interest in our products...",
                        },
                        {
                            day: 3,
                            subject: "Did you know?",
                            body: "Hello {first_name},\n\nDid you know that our solution can help you...",
                        },
                        {
                            day: 7,
                            subject: "Last chance",
                            body: "Hello {first_name},\n\nThis is your last chance to take advantage of...",
                        },
                    ],
                },
            },
        ];
        res.status(200).json(templates);
    }
    catch (error) {
        console.error("Error fetching campaign templates:", error);
        res.status(500).json({ error: "Failed to fetch campaign templates" });
    }
};
exports.getCampaignTemplates = getCampaignTemplates;
//# sourceMappingURL=campaigns.controller.js.map