"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const accounting_controller_js_1 = require("../controllers/accounting/accounting.controller.js");
const router = express_1.default.Router();
// Dashboard routes
router.get("/", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(accounting_controller_js_1.getDashboard));
// Journal routes
router.get("/journal", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(accounting_controller_js_1.getJournalEntries));
// Chart of Accounts routes
router.get("/accounts", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(accounting_controller_js_1.getChartOfAccounts));
// Financial Reports routes
router.get("/reports", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(accounting_controller_js_1.getReports));
exports.default = router;
//# sourceMappingURL=accounting.js.map