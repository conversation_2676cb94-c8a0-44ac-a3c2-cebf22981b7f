{"version": 3, "file": "analyticsController.js", "sourceRoot": "", "sources": ["../../../controllers/recruitment/analyticsController.ts"], "names": [], "mappings": ";;;AACA,2CAA8C;AAE9C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAElC,qCAAqC;AAC9B,MAAM,oBAAoB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACxE,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QACjC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;QAEzC,MAAM,UAAU,GAAQ,EAAE,CAAC;QAC3B,IAAI,SAAS;YAAE,UAAU,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,SAAmB,CAAC,CAAC;QAC9D,IAAI,OAAO;YAAE,UAAU,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,OAAiB,CAAC,CAAC;QAE1D,MAAM,WAAW,GAAG;YAClB,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC;YAC9B,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;SACrE,CAAC;QAEF,oBAAoB;QACpB,MAAM,CACJ,SAAS,EACT,UAAU,EACV,iBAAiB,EACjB,mBAAmB,EACnB,uBAAuB,EACvB,mBAAmB,EACnB,eAAe,CAChB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACpB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YACtE,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC;YACxF,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC;gBAC1B,KAAK,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE;aAC5B,CAAC;YACF,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC;gBAC1B,KAAK,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE;aAC/C,CAAC;YACF,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC;gBAC1B,KAAK,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE;aACnD,CAAC;YACF,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;gBACrB,KAAK,EAAE;oBACL,WAAW,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE;oBACjC,MAAM,EAAE,WAAW;iBACpB;aACF,CAAC;YACF,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC;gBAC1B,KAAK,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE;aAC7C,CAAC;SACH,CAAC,CAAC;QAEH,iCAAiC;QACjC,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YAC7D,KAAK,EAAE;gBACL,GAAG,EAAE,WAAW;gBAChB,MAAM,EAAE,OAAO;aAChB;YACD,MAAM,EAAE;gBACN,SAAS,EAAE,IAAI;gBACf,UAAU,EAAE,IAAI;aACjB;SACF,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC;YAChD,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACpC,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;oBACnB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;oBACrG,OAAO,GAAG,GAAG,IAAI,CAAC;gBACpB,CAAC;gBACD,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM;YAClC,CAAC,CAAC,IAAI,CAAC;QAET,0BAA0B;QAC1B,MAAM,kBAAkB,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;YAC7D,EAAE,EAAE,CAAC,gBAAgB,CAAC;YACtB,KAAK,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE;YAC3B,MAAM,EAAE,IAAI;SACb,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG;YACjB,OAAO,EAAE,kBAAkB,CAAC,MAAM;YAClC,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,CAAC;YACX,KAAK,EAAE,CAAC;SACT,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC;YACP,SAAS;YACT,UAAU;YACV,iBAAiB;YACjB,mBAAmB;YACnB,uBAAuB;YACvB,mBAAmB;YACnB,eAAe;YACf,aAAa;YACb,kBAAkB,EAAE,UAAU;SAC/B,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC,CAAC;IAC/D,CAAC;AACH,CAAC,CAAC;AAjGW,QAAA,oBAAoB,wBAiG/B;AAEF,yCAAyC;AAClC,MAAM,kBAAkB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACtE,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QACjC,MAAM,EAAE,MAAM,GAAG,OAAO,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;QAEvC,IAAI,OAAe,CAAC;QACpB,IAAI,UAAkB,CAAC;QAEvB,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,MAAM;gBACT,OAAO,GAAG,MAAM,CAAC;gBACjB,UAAU,GAAG,OAAO,CAAC;gBACrB,MAAM;YACR,KAAK,OAAO;gBACV,OAAO,GAAG,OAAO,CAAC;gBAClB,UAAU,GAAG,OAAO,CAAC;gBACrB,MAAM;YACR,KAAK,MAAM;gBACT,OAAO,GAAG,MAAM,CAAC;gBACjB,UAAU,GAAG,IAAI,CAAC;gBAClB,MAAM;YACR;gBACE,OAAO,GAAG,OAAO,CAAC;gBAClB,UAAU,GAAG,OAAO,CAAC;QACzB,CAAC;QAED,6BAA6B;QAC7B,MAAM,oBAAoB,GAAG,MAAM,MAAM,CAAC,SAAS,CAAA;;qBAElC,OAAO;;;;;+BAKG,QAAQ,CAAC,SAAS,CAAC;4BACtB,OAAO;;;KAG9B,CAAC;QAEF,qBAAqB;QACrB,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,SAAS,CAAA;;qBAE1B,OAAO;;;;4BAIA,QAAQ,CAAC,SAAS,CAAC;4BACnB,OAAO;;;KAG9B,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC;YACP,YAAY,EAAE,oBAAoB;YAClC,IAAI,EAAE,YAAY;SACnB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC,CAAC;AA7DW,QAAA,kBAAkB,sBA6D7B;AAEF,8BAA8B;AACvB,MAAM,oBAAoB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACxE,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAEjC,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;YACrD,EAAE,EAAE,CAAC,OAAO,CAAC;YACb,KAAK,EAAE;gBACL,GAAG,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;aACxC;YACD,MAAM,EAAE,IAAI;SACb,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,CAAC;YACV,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,UAAU,EAAE,CAAC;YACb,eAAe,EAAE,CAAC;YAClB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;YACR,QAAQ,EAAE,CAAC;SACZ,CAAC;QAEF,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxB,MAAM,CAAC,IAAI,CAAC,KAA4B,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,CAAC,CAAC;IACjE,CAAC;AACH,CAAC,CAAC;AAhCW,QAAA,oBAAoB,wBAgC/B;AAEF,0BAA0B;AACnB,MAAM,oBAAoB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACxE,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QACjC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;QAEhC,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC/C,KAAK,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;YACzC,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,YAAY,EAAE,IAAI;qBACnB;iBACF;gBACD,YAAY,EAAE;oBACZ,KAAK,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE;oBAC1B,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;iBACrB;aACF;YACD,OAAO,EAAE;gBACP,YAAY,EAAE;oBACZ,MAAM,EAAE,MAAM;iBACf;aACF;YACD,IAAI,EAAE,QAAQ,CAAC,KAAe,CAAC;SAChC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACxC,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,KAAK,EAAE,GAAG,CAAC,KAAK;YAChB,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,iBAAiB,EAAE,GAAG,CAAC,MAAM,CAAC,YAAY;YAC1C,UAAU,EAAE,GAAG,CAAC,YAAY,CAAC,MAAM;YACnC,cAAc,EAAE,GAAG,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC;gBACzC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtE,CAAC,CAAC,KAAK;SACV,CAAC,CAAC,CAAC;QAEJ,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC1B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,CAAC;IAC9D,CAAC;AACH,CAAC,CAAC;AA1CW,QAAA,oBAAoB,wBA0C/B;AAEF,mCAAmC;AAC5B,MAAM,kBAAkB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACtE,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAEjC,yEAAyE;QACzE,4DAA4D;QAC5D,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC;YAC1D,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,EAAE;SACnD,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG;YACd,EAAE,IAAI,EAAE,iBAAiB,EAAE,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE;YAC9F,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE;YACvF,EAAE,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE;YACzF,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE;SACzF,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,8BAA8B,EAAE,CAAC,CAAC;IAClE,CAAC;AACH,CAAC,CAAC;AAtBW,QAAA,kBAAkB,sBAsB7B;AAEF,4BAA4B;AACrB,MAAM,qBAAqB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACzE,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAEjC,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YAC7D,KAAK,EAAE;gBACL,GAAG,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;gBACvC,MAAM,EAAE,OAAO;gBACf,UAAU,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE;aAC1B;YACD,MAAM,EAAE;gBACN,SAAS,EAAE,IAAI;gBACf,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAE;oBACH,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE;iBAC1C;aACF;SACF,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACjD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAW,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YACtG,OAAO;gBACL,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK;gBACvB,UAAU,EAAE,GAAG,CAAC,GAAG,CAAC,UAAU;gBAC9B,UAAU,EAAE,IAAI;aACjB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC;YAC7C,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM;YACxF,CAAC,CAAC,CAAC,CAAC;QAEN,GAAG,CAAC,IAAI,CAAC;YACP,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;YAC5C,cAAc,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,gBAAgB;SAC7D,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC9D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,CAAC,CAAC;IACvE,CAAC;AACH,CAAC,CAAC;AAxCW,QAAA,qBAAqB,yBAwChC"}