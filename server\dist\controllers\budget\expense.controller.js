"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.markExpenseUnpaid = exports.markExpensePaid = exports.deleteExpense = exports.updateExpense = exports.getExpenses = exports.createExpense = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
const createExpense = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        let { name, description, amount, paymentMethod, source, interval, startDate, endDate, multipleIntervals, } = req.body;
        amount = parseFloat(amount) || 0;
        if (interval === "MULTIPLE" &&
            Array.isArray(multipleIntervals) &&
            multipleIntervals.length > 0) {
            const dataArray = multipleIntervals.map((entry) => ({
                userId: userId,
                name,
                description: description || "",
                amount,
                paymentMethod,
                source,
                interval: "MULTIPLE",
                startDate: new Date(entry.startDate),
                endDate: new Date(entry.endDate),
            }));
            const result = await prisma_js_1.prisma.expense.createMany({ data: dataArray });
            return res.status(201).json({
                success: true,
                count: result.count,
                message: `${result.count} new expenses created (multiple).`,
            });
        }
        if (interval === "RECURRING") {
            const baseDate = startDate ? new Date(startDate) : new Date();
            const dataArray = [];
            for (let i = 0; i < 12; i++) {
                const monthlyDate = new Date(baseDate);
                monthlyDate.setMonth(baseDate.getMonth() + i);
                dataArray.push({
                    userId: userId,
                    name,
                    description: description || "",
                    amount,
                    paymentMethod,
                    source,
                    interval: "RECURRING",
                    startDate: monthlyDate,
                    endDate: monthlyDate,
                });
            }
            const result = await prisma_js_1.prisma.expense.createMany({ data: dataArray });
            return res.status(201).json({
                success: true,
                count: result.count,
                message: `${result.count} new expenses created (recurring).`,
            });
        }
        const singleStart = startDate ? new Date(startDate) : new Date();
        const singleEnd = endDate ? new Date(endDate) : singleStart;
        const newExpense = await prisma_js_1.prisma.expense.create({
            data: {
                userId: userId,
                name,
                description: description || "",
                amount,
                paymentMethod: paymentMethod, // Cast to enum type
                source: source, // Cast to enum type
                interval: interval, // Cast to enum type
                startDate: singleStart,
                endDate: singleEnd,
            },
        });
        return res.status(201).json(newExpense);
    }
    catch (error) {
        console.error("Error creating expense:", error);
        next(error);
    }
};
exports.createExpense = createExpense;
const getExpenses = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const expenses = await prisma_js_1.prisma.expense.findMany({
            where: { userId },
            orderBy: { createdAt: "desc" },
        });
        res.json(expenses);
    }
    catch (error) {
        next(error);
    }
};
exports.getExpenses = getExpenses;
const updateExpense = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const expenseId = Number(req.params.id);
        const { name, description, amount, paymentMethod, source } = req.body;
        const existing = await prisma_js_1.prisma.expense.findUnique({
            where: { id: expenseId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Expense not found or not yours" });
        }
        const updated = await prisma_js_1.prisma.expense.update({
            where: { id: expenseId },
            data: {
                name,
                description,
                amount: parseFloat(amount),
                paymentMethod: paymentMethod,
                source: source,
            },
        });
        res.json(updated);
    }
    catch (error) {
        next(error);
    }
};
exports.updateExpense = updateExpense;
const deleteExpense = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const expenseId = Number(req.params.id);
        const existing = await prisma_js_1.prisma.expense.findUnique({
            where: { id: expenseId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Expense not found or not yours" });
        }
        await prisma_js_1.prisma.expense.delete({
            where: { id: expenseId },
        });
        res.json({ success: true });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteExpense = deleteExpense;
const markExpensePaid = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const expenseId = Number(req.params.id);
        const existing = await prisma_js_1.prisma.expense.findUnique({
            where: { id: expenseId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Expense not found or not yours" });
        }
        const updated = await prisma_js_1.prisma.expense.update({
            where: { id: expenseId },
            data: { paid: true },
        });
        res.json({ success: true, data: updated });
    }
    catch (error) {
        next(error);
    }
};
exports.markExpensePaid = markExpensePaid;
const markExpenseUnpaid = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const expenseId = Number(req.params.id);
        const existing = await prisma_js_1.prisma.expense.findUnique({
            where: { id: expenseId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Expense not found or not yours" });
        }
        const updated = await prisma_js_1.prisma.expense.update({
            where: { id: expenseId },
            data: { paid: false },
        });
        res.json({ success: true, data: updated });
    }
    catch (error) {
        next(error);
    }
};
exports.markExpenseUnpaid = markExpenseUnpaid;
//# sourceMappingURL=expense.controller.js.map