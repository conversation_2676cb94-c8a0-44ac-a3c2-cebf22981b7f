"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateBudgetGoalProgress = exports.deleteBudgetGoal = exports.updateBudgetGoal = exports.getBudgetGoalById = exports.getBudgetGoals = exports.createBudgetGoal = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const createBudgetGoal = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const payload = req.body;
        // Validate required fields
        if (!payload.name ||
            !payload.targetAmount ||
            !payload.startDate ||
            !payload.endDate) {
            return res.status(400).json({
                error: "Missing required fields: name, targetAmount, startDate, and endDate are required",
            });
        }
        // Validate date range
        const startDate = new Date(payload.startDate);
        const endDate = new Date(payload.endDate);
        if (endDate < startDate) {
            return res.status(400).json({
                error: "End date must be after start date",
            });
        }
        // Create budget goal
        const newGoal = await prisma_js_1.prisma.budgetGoal.create({
            data: {
                userId: userId,
                name: payload.name,
                description: payload.description || "",
                targetAmount: payload.targetAmount,
                currentAmount: payload.currentAmount || 0,
                startDate,
                endDate,
                type: payload.type,
                status: payload.status || "IN_PROGRESS",
                companyId: payload.companyId,
            },
        });
        return res.status(201).json(newGoal);
    }
    catch (error) {
        next(error);
    }
};
exports.createBudgetGoal = createBudgetGoal;
const getBudgetGoals = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const type = req.query.type;
        const status = req.query.status;
        const companyId = req.query.companyId
            ? parseInt(req.query.companyId)
            : undefined;
        // Build filter
        const filter = { userId };
        if (type) {
            filter.type = type;
        }
        if (status) {
            filter.status = status;
        }
        if (companyId) {
            filter.companyId = companyId;
        }
        const goals = await prisma_js_1.prisma.budgetGoal.findMany({
            where: filter,
            orderBy: { endDate: "asc" },
        });
        res.json(goals);
    }
    catch (error) {
        next(error);
    }
};
exports.getBudgetGoals = getBudgetGoals;
const getBudgetGoalById = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const goalId = parseInt(req.params.id);
        const goal = await prisma_js_1.prisma.budgetGoal.findUnique({
            where: { id: goalId },
        });
        if (!goal || goal.userId !== userId) {
            return res
                .status(404)
                .json({ error: "Budget goal not found or not yours" });
        }
        res.json(goal);
    }
    catch (error) {
        next(error);
    }
};
exports.getBudgetGoalById = getBudgetGoalById;
const updateBudgetGoal = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const goalId = parseInt(req.params.id);
        const payload = req.body;
        // Check if goal exists and belongs to user
        const existing = await prisma_js_1.prisma.budgetGoal.findUnique({
            where: { id: goalId },
        });
        if (!existing || existing.userId !== userId) {
            return res
                .status(404)
                .json({ error: "Budget goal not found or not yours" });
        }
        // Validate date range if both dates are provided
        if (payload.startDate && payload.endDate) {
            const startDate = new Date(payload.startDate);
            const endDate = new Date(payload.endDate);
            if (endDate < startDate) {
                return res.status(400).json({
                    error: "End date must be after start date",
                });
            }
        }
        // Update goal
        const updated = await prisma_js_1.prisma.budgetGoal.update({
            where: { id: goalId },
            data: {
                name: payload.name,
                description: payload.description,
                targetAmount: payload.targetAmount,
                currentAmount: payload.currentAmount,
                startDate: payload.startDate ? new Date(payload.startDate) : undefined,
                endDate: payload.endDate ? new Date(payload.endDate) : undefined,
                type: payload.type,
                status: payload.status,
                companyId: payload.companyId,
            },
        });
        res.json(updated);
    }
    catch (error) {
        next(error);
    }
};
exports.updateBudgetGoal = updateBudgetGoal;
const deleteBudgetGoal = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const goalId = parseInt(req.params.id);
        // Check if goal exists and belongs to user
        const existing = await prisma_js_1.prisma.budgetGoal.findUnique({
            where: { id: goalId },
        });
        if (!existing || existing.userId !== userId) {
            return res
                .status(404)
                .json({ error: "Budget goal not found or not yours" });
        }
        // Delete goal
        await prisma_js_1.prisma.budgetGoal.delete({
            where: { id: goalId },
        });
        res.json({ success: true });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteBudgetGoal = deleteBudgetGoal;
const updateBudgetGoalProgress = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const goalId = parseInt(req.params.id);
        const { currentAmount } = req.body;
        if (currentAmount === undefined) {
            return res.status(400).json({ error: "currentAmount is required" });
        }
        // Check if goal exists and belongs to user
        const existing = await prisma_js_1.prisma.budgetGoal.findUnique({
            where: { id: goalId },
        });
        if (!existing || existing.userId !== userId) {
            return res
                .status(404)
                .json({ error: "Budget goal not found or not yours" });
        }
        // Update progress
        const updated = await prisma_js_1.prisma.budgetGoal.update({
            where: { id: goalId },
            data: {
                currentAmount,
                // Automatically update status based on progress
                status: determineGoalStatus(existing.type, currentAmount, existing.targetAmount),
            },
        });
        res.json(updated);
    }
    catch (error) {
        next(error);
    }
};
exports.updateBudgetGoalProgress = updateBudgetGoalProgress;
// Helper function to determine goal status based on progress
function determineGoalStatus(type, currentAmount, targetAmount) {
    if (type === "SAVING") {
        // For saving goals, we want to reach or exceed the target
        return currentAmount >= targetAmount ? "COMPLETED" : "IN_PROGRESS";
    }
    else {
        // For spending goals, we want to stay below the target
        return currentAmount <= targetAmount ? "IN_PROGRESS" : "FAILED";
    }
}
//# sourceMappingURL=goal.controller.js.map