{"version": 3, "file": "bonuses.controller.js", "sourceRoot": "", "sources": ["../../../controllers/hr/bonuses.controller.ts"], "names": [], "mappings": ";;;AACA,mDAA6C;AAE7C,8DAA8D;AAC9D,SAAS,eAAe,CAAC,UAAkB,EAAE,OAAe;IAC1D,MAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IACtC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;IAC9B,OAAO,KAAK,CAAC;AACf,CAAC;AAcD,uDAAuD;AAEvD,kBAAkB;AACX,MAAM,aAAa,GAAG,KAAK,EAChC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;QAEhC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YAC1C,KAAK,EAAE;gBACL,QAAQ,EAAE;oBACR,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;iBAC7B;aACF;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,IAAI,EAAE,IAAI;qBACX;iBACF;aACF;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,yBAAyB,CAAC,CAAC,CAAC;IACxD,CAAC;AACH,CAAC,CAAC;AAnCW,QAAA,aAAa,iBAmCxB;AAEF,6BAA6B;AACtB,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAE1B,MAAM,KAAK,GAAG,MAAM,kBAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,IAAI,EAAE,IAAI;qBACX;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC,CAAC;QACvD,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,uBAAuB,CAAC,CAAC,CAAC;IACtD,CAAC;AACH,CAAC,CAAC;AA9BW,QAAA,YAAY,gBA8BvB;AAEF,qBAAqB;AACd,MAAM,WAAW,GAAG,KAAK,EAC9B,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EACJ,UAAU,EACV,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,WAAW,EACX,SAAS,EACT,MAAM,EACN,WAAW,GACZ,GAAG,GAAG,CAAC,IAAI,CAAC;QAEb,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACpC,OAAO,IAAI,CACT,eAAe,CAAC,GAAG,EAAE,4CAA4C,CAAC,CACnE,CAAC;QACJ,CAAC;QAED,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,UAAU,CAAC;aACvB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,kBAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACtC,IAAI,EAAE;gBACJ,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;gBAC9B,IAAI,EAAE,IAAW,EAAE,yBAAyB;gBAC5C,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC;gBAC1B,QAAQ,EAAE,QAAQ,IAAI,KAAK;gBAC3B,WAAW;gBACX,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;gBACvD,MAAM,EAAE,MAAM,IAAI,KAAK;gBACvB,WAAW,EAAE,MAAM,IAAI,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI;aAClE;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC,CAAC;IACvD,CAAC;AACH,CAAC,CAAC;AApDW,QAAA,WAAW,eAoDtB;AAEF,iBAAiB;AACV,MAAM,WAAW,GAAG,KAAK,EAC9B,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,EACJ,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,WAAW,EACX,SAAS,EACT,MAAM,EACN,WAAW,GACZ,GAAG,GAAG,CAAC,IAAI,CAAC;QAEb,MAAM,KAAK,GAAG,MAAM,kBAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,kBAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YAC7C,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;gBAC3C,MAAM,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC7D,QAAQ,EAAE,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;gBACvD,WAAW,EAAE,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;gBAChE,SAAS,EAAE,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS;gBACpE,MAAM,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBACjD,WAAW,EACT,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;aAChE;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC,CAAC;IACvD,CAAC;AACH,CAAC,CAAC;AAhDW,QAAA,WAAW,eAgDtB;AAEF,iBAAiB;AACV,MAAM,WAAW,GAAG,KAAK,EAC9B,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAE1B,MAAM,KAAK,GAAG,MAAM,kBAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,kBAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACxB,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC,CAAC;IACtD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC,CAAC;IACvD,CAAC;AACH,CAAC,CAAC;AA7BW,QAAA,WAAW,eA6BtB;AAEF,kCAAkC;AAC3B,MAAM,kBAAkB,GAAG,KAAK,EACrC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAElC,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YAC1C,KAAK,EAAE;gBACL,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;aAC/B;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,IAAI,EAAE,IAAI;qBACX;iBACF;aACF;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,kCAAkC,CAAC,CAAC,CAAC;IACjE,CAAC;AACH,CAAC,CAAC;AA7BW,QAAA,kBAAkB,sBA6B7B;AAEF,uBAAuB;AAChB,MAAM,eAAe,GAAG,KAAK,EAClC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEjC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,kBAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iCAAiC,CAAC,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,kBAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YAC7C,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;YACD,IAAI,EAAE;gBACJ,MAAM,EAAE,IAAI;gBACZ,WAAW,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC;aACnC;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,8BAA8B,CAAC,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC,CAAC;AA1CW,QAAA,eAAe,mBA0C1B"}