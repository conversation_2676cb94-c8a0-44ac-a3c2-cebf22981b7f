"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCompanyTransition = exports.createCompanyTransition = exports.getCompanyTransition = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Get transition history for a company
const getCompanyTransition = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        const transition = await prisma_js_1.prisma.companyTransition.findUnique({
            where: { companyId: Number(companyId) },
            include: {
                company: true,
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
        if (!transition) {
            return next(createError(404, "Transition record not found"));
        }
        res.json(transition);
    }
    catch (error) {
        next(createError(500, "Failed to fetch company transition"));
    }
};
exports.getCompanyTransition = getCompanyTransition;
// Create a transition record when a company becomes a platform user
const createCompanyTransition = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        const { userId, previousType, transitionReason, previousData, notes } = req.body;
        // Check if transition already exists
        const existingTransition = await prisma_js_1.prisma.companyTransition.findUnique({
            where: { companyId: Number(companyId) },
        });
        if (existingTransition) {
            return next(createError(409, "Transition record already exists for this company"));
        }
        // Update the company record to mark it as a platform user
        await prisma_js_1.prisma.company.update({
            where: { id: Number(companyId) },
            data: {
                becameUserAt: new Date(),
            },
        });
        // Create the transition record
        const transition = await prisma_js_1.prisma.companyTransition.create({
            data: {
                companyId: Number(companyId),
                userId: userId ? Number(userId) : undefined,
                previousType,
                transitionReason,
                previousData,
                notes,
            },
            include: {
                company: true,
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
        res.status(201).json(transition);
    }
    catch (error) {
        next(createError(500, "Failed to create company transition"));
    }
};
exports.createCompanyTransition = createCompanyTransition;
// Update a transition record
const updateCompanyTransition = async (req, res, next) => {
    try {
        const { transitionId } = req.params;
        const { userId, transitionReason, previousData, notes } = req.body;
        const transition = await prisma_js_1.prisma.companyTransition.update({
            where: { id: Number(transitionId) },
            data: {
                userId: userId ? Number(userId) : undefined,
                transitionReason,
                previousData,
                notes,
                updatedAt: new Date(),
            },
            include: {
                company: true,
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
        res.json(transition);
    }
    catch (error) {
        if (error.code === "P2025") {
            return next(createError(404, "Transition record not found"));
        }
        next(createError(500, "Failed to update company transition"));
    }
};
exports.updateCompanyTransition = updateCompanyTransition;
//# sourceMappingURL=companyTransition.controller.js.map