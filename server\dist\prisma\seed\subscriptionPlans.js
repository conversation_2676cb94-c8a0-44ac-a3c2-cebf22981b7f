"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedSubscriptionPlans = seedSubscriptionPlans;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function seedSubscriptionPlans() {
    console.log('Seeding subscription plans...');
    // Check if plans already exist
    const existingPlans = await prisma.subscriptionPlan.findMany();
    if (existingPlans.length > 0) {
        console.log(`Found ${existingPlans.length} existing subscription plans. Skipping seed.`);
        return;
    }
    // Create subscription plans
    const plans = [
        {
            name: 'Free Trial',
            description: 'Try our platform for free for 14 days',
            type: client_1.SubscriptionPlanType.FREE_TRIAL,
            monthlyPrice: 0,
            yearlyPrice: 0,
            maxUsers: 2,
            additionalUserFee: 0,
            features: ['Core Module', 'Limited Access', '2 Users'],
            modules: ['core'],
        },
        {
            name: 'Starter',
            description: 'Perfect for small businesses and startups',
            type: client_1.SubscriptionPlanType.STARTER,
            monthlyPrice: 29.99,
            yearlyPrice: 299.99,
            maxUsers: 5,
            additionalUserFee: 9.99,
            features: ['Core Module', 'Budget Module', '5 Users', 'Email Support'],
            modules: ['core', 'budget'],
        },
        {
            name: 'Premium',
            description: 'Full access to all features and modules',
            type: client_1.SubscriptionPlanType.PREMIUM,
            monthlyPrice: 99.99,
            yearlyPrice: 999.99,
            maxUsers: 20,
            additionalUserFee: 4.99,
            features: ['All Modules', '20 Users', 'Priority Support', 'Custom Integrations'],
            modules: ['core', 'budget', 'hr', 'accounting', 'product', 'communication'],
        },
    ];
    // Insert plans into database
    for (const plan of plans) {
        await prisma.subscriptionPlan.create({
            data: plan,
        });
    }
    console.log('Subscription plans seeded successfully!');
}
// Run the seed function if this file is executed directly
if (require.main === module) {
    seedSubscriptionPlans()
        .then(async () => {
        await prisma.$disconnect();
    })
        .catch(async (e) => {
        console.error(e);
        await prisma.$disconnect();
        process.exit(1);
    });
}
//# sourceMappingURL=subscriptionPlans.js.map