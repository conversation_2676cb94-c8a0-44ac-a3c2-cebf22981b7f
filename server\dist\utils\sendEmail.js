"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendEmail = sendEmail;
const nodemailer_1 = __importDefault(require("nodemailer"));
const transporter = nodemailer_1.default.createTransport({
    host: "smtp.zoho.eu",
    port: 587,
    secure: false, // Use STARTTLS, which is the standard for port 587
    auth: {
        user: "<EMAIL>",
        pass: "Onamission#007",
    },
    tls: {
        ciphers: "SSLv3",
    },
});
async function sendEmail(to, subject, text, html, attachments) {
    // Create mail options with required fields
    const mailOptions = {
        from: "<EMAIL>", // Using the authenticated email address
        to,
        subject,
    };
    // Add text or html content based on what's provided
    if (text) {
        mailOptions.text = text;
    }
    if (html) {
        mailOptions.html = html;
        // If we have HTML but no text, create a simple text version as fallback
        if (!text) {
            // Create a very basic text version by stripping HTML tags
            mailOptions.text = html
                .replace(/<[^>]*>/g, "")
                .replace(/\s+/g, " ")
                .trim();
        }
    }
    // Add attachments if provided
    if (attachments && attachments.length > 0) {
        mailOptions.attachments = attachments;
    }
    try {
        console.log(`Attempting to send email to ${to} with subject "${subject}"...`);
        console.log(`Email content type: ${html ? "HTML" : "Text"}`);
        const info = await transporter.sendMail(mailOptions);
        console.log("Email sent successfully");
        console.log("Message ID:", info.messageId);
        // Log preview URL in development (if available)
        if (info.preview) {
            console.log("Preview URL:", info.preview);
        }
    }
    catch (error) {
        console.error("Error sending email:", error);
        throw new Error(`Error sending email: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
}
//# sourceMappingURL=sendEmail.js.map