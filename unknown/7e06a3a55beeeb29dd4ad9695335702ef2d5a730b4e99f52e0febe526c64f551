CREATE TABLE IF NOT EXISTS _content_info (id TEXT PRIMARY KEY, "ready" BOOLEAN, "structureVersion" VARCHAR, "version" VARCHAR, "__hash__" TEXT UNIQUE); -- structure
INSERT INTO _content_info VALUES ('checksum_docs', false, 'IknlcqeZqkgL4a-fzeVGklp_9ZLIselmb4dNVNTpOzM', 'v3.3.0--IknlcqeZqkgL4a-fzeVGklp_9ZLIselmb4dNVNTpOzM', '3StS5zCJAqlQ-xbnWiIEuvOu-40FQb9SHOByiughtZU'); -- meta
DROP TABLE IF EXISTS _content_docs; -- structure
CREATE TABLE IF NOT EXISTS _content_docs (id TEXT PRIMARY KEY, "title" VARCHAR, "body" TEXT, "components" TEXT, "description" VARCHAR, "extension" VARCHAR, "icon" TEXT, "meta" TEXT, "navigation" TEXT DEFAULT true, "path" VARCHAR, "seo" TEXT DEFAULT '{}', "stem" VARCHAR, "toc" BOOLEAN, "__hash__" TEXT UNIQUE); -- structure
UPDATE _content_info SET ready = true WHERE id = 'checksum_docs'; -- meta