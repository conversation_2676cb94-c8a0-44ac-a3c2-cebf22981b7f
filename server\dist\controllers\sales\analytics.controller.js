"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCustomReport = exports.getCustomReports = exports.getSalesForecasts = exports.getPerformanceMetrics = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Get performance metrics
const getPerformanceMetrics = async (req, res) => {
    try {
        const { timeframe = "this_month", userId, compareWith } = req.query;
        // Define date ranges
        const now = new Date();
        let startDate = new Date();
        let endDate = new Date();
        let compareStartDate = new Date();
        let compareEndDate = new Date();
        switch (timeframe) {
            case "this_week":
                // Start of current week (Sunday)
                startDate.setDate(now.getDate() - now.getDay());
                startDate.setHours(0, 0, 0, 0);
                // End of current week (Saturday)
                endDate.setDate(startDate.getDate() + 6);
                endDate.setHours(23, 59, 59, 999);
                // Previous week
                compareStartDate.setDate(startDate.getDate() - 7);
                compareEndDate.setDate(endDate.getDate() - 7);
                break;
            case "this_month":
                // Start of current month
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                // End of current month
                endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
                // Previous month
                compareStartDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                compareEndDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999);
                break;
            case "this_quarter":
                // Start of current quarter
                const quarter = Math.floor(now.getMonth() / 3);
                startDate = new Date(now.getFullYear(), quarter * 3, 1);
                // End of current quarter
                endDate = new Date(now.getFullYear(), quarter * 3 + 3, 0, 23, 59, 59, 999);
                // Previous quarter
                compareStartDate = new Date(now.getFullYear(), quarter * 3 - 3, 1);
                compareEndDate = new Date(now.getFullYear(), quarter * 3, 0, 23, 59, 59, 999);
                break;
            case "this_year":
                // Start of current year
                startDate = new Date(now.getFullYear(), 0, 1);
                // End of current year
                endDate = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999);
                // Previous year
                compareStartDate = new Date(now.getFullYear() - 1, 0, 1);
                compareEndDate = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59, 999);
                break;
            case "custom":
                if (req.query.startDate && req.query.endDate) {
                    startDate = new Date(req.query.startDate);
                    endDate = new Date(req.query.endDate);
                    endDate.setHours(23, 59, 59, 999);
                    // Calculate comparison period with same duration
                    const duration = endDate.getTime() - startDate.getTime();
                    compareEndDate = new Date(startDate.getTime() - 1);
                    compareStartDate = new Date(compareEndDate.getTime() - duration);
                }
                break;
            default:
                // Default to this month
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
                compareStartDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                compareEndDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999);
        }
        // Build filter conditions
        const where = {
            createdAt: {
                gte: startDate,
                lte: endDate,
            },
        };
        const compareWhere = {
            createdAt: {
                gte: compareStartDate,
                lte: compareEndDate,
            },
        };
        if (userId) {
            where.assignedToId = Number(userId);
            compareWhere.assignedToId = Number(userId);
        }
        // Get metrics for current period
        const [newLeads, qualifiedLeads, convertedLeads, newDeals, wonDeals, lostDeals, totalRevenue, activities,] = await Promise.all([
            // New leads
            prisma_js_1.prisma.lead.count({
                where: {
                    ...where,
                    status: "NEW",
                },
            }),
            // Qualified leads
            prisma_js_1.prisma.lead.count({
                where: {
                    ...where,
                    status: "QUALIFIED",
                },
            }),
            // Converted leads
            prisma_js_1.prisma.lead.count({
                where: {
                    ...where,
                    status: "CONVERTED",
                },
            }),
            // New deals
            prisma_js_1.prisma.deal.count({
                where,
            }),
            // Won deals
            prisma_js_1.prisma.deal.count({
                where: {
                    ...where,
                    stage: "CLOSED_WON",
                },
            }),
            // Lost deals
            prisma_js_1.prisma.deal.count({
                where: {
                    ...where,
                    stage: "CLOSED_LOST",
                },
            }),
            // Total revenue
            prisma_js_1.prisma.deal.aggregate({
                where: {
                    ...where,
                    stage: "CLOSED_WON",
                },
                _sum: {
                    value: true,
                },
            }),
            // Activities
            prisma_js_1.prisma.activity.count({
                where,
            }),
        ]);
        // Get metrics for comparison period if requested
        let comparisonMetrics = null;
        if (compareWith === "previous_period") {
            const [prevNewLeads, prevQualifiedLeads, prevConvertedLeads, prevNewDeals, prevWonDeals, prevLostDeals, prevTotalRevenue, prevActivities,] = await Promise.all([
                // New leads
                prisma_js_1.prisma.lead.count({
                    where: {
                        ...compareWhere,
                        status: "NEW",
                    },
                }),
                // Qualified leads
                prisma_js_1.prisma.lead.count({
                    where: {
                        ...compareWhere,
                        status: "QUALIFIED",
                    },
                }),
                // Converted leads
                prisma_js_1.prisma.lead.count({
                    where: {
                        ...compareWhere,
                        status: "CONVERTED",
                    },
                }),
                // New deals
                prisma_js_1.prisma.deal.count({
                    where: compareWhere,
                }),
                // Won deals
                prisma_js_1.prisma.deal.count({
                    where: {
                        ...compareWhere,
                        stage: "CLOSED_WON",
                    },
                }),
                // Lost deals
                prisma_js_1.prisma.deal.count({
                    where: {
                        ...compareWhere,
                        stage: "CLOSED_LOST",
                    },
                }),
                // Total revenue
                prisma_js_1.prisma.deal.aggregate({
                    where: {
                        ...compareWhere,
                        stage: "CLOSED_WON",
                    },
                    _sum: {
                        value: true,
                    },
                }),
                // Activities
                prisma_js_1.prisma.activity.count({
                    where: compareWhere,
                }),
            ]);
            comparisonMetrics = {
                newLeads: prevNewLeads,
                qualifiedLeads: prevQualifiedLeads,
                convertedLeads: prevConvertedLeads,
                newDeals: prevNewDeals,
                wonDeals: prevWonDeals,
                lostDeals: prevLostDeals,
                totalRevenue: prevTotalRevenue._sum.value || 0,
                activities: prevActivities,
                period: {
                    start: compareStartDate,
                    end: compareEndDate,
                },
            };
        }
        // Calculate conversion rates
        const leadConversionRate = newLeads > 0 ? (convertedLeads / newLeads) * 100 : 0;
        const dealWinRate = newDeals > 0 ? (wonDeals / newDeals) * 100 : 0;
        // Calculate average deal size
        const avgDealSize = wonDeals > 0 ? (totalRevenue._sum.value || 0) / wonDeals : 0;
        // Calculate growth rates if comparison metrics are available
        let growth = null;
        if (comparisonMetrics) {
            growth = {
                newLeads: comparisonMetrics.newLeads > 0
                    ? ((newLeads - comparisonMetrics.newLeads) /
                        comparisonMetrics.newLeads) *
                        100
                    : newLeads > 0
                        ? 100
                        : 0,
                qualifiedLeads: comparisonMetrics.qualifiedLeads > 0
                    ? ((qualifiedLeads - comparisonMetrics.qualifiedLeads) /
                        comparisonMetrics.qualifiedLeads) *
                        100
                    : qualifiedLeads > 0
                        ? 100
                        : 0,
                convertedLeads: comparisonMetrics.convertedLeads > 0
                    ? ((convertedLeads - comparisonMetrics.convertedLeads) /
                        comparisonMetrics.convertedLeads) *
                        100
                    : convertedLeads > 0
                        ? 100
                        : 0,
                newDeals: comparisonMetrics.newDeals > 0
                    ? ((newDeals - comparisonMetrics.newDeals) /
                        comparisonMetrics.newDeals) *
                        100
                    : newDeals > 0
                        ? 100
                        : 0,
                wonDeals: comparisonMetrics.wonDeals > 0
                    ? ((wonDeals - comparisonMetrics.wonDeals) /
                        comparisonMetrics.wonDeals) *
                        100
                    : wonDeals > 0
                        ? 100
                        : 0,
                totalRevenue: comparisonMetrics.totalRevenue > 0
                    ? (((totalRevenue._sum.value || 0) -
                        comparisonMetrics.totalRevenue) /
                        comparisonMetrics.totalRevenue) *
                        100
                    : (totalRevenue._sum.value || 0) > 0
                        ? 100
                        : 0,
                activities: comparisonMetrics.activities > 0
                    ? ((activities - comparisonMetrics.activities) /
                        comparisonMetrics.activities) *
                        100
                    : activities > 0
                        ? 100
                        : 0,
            };
        }
        // Get sales by stage
        const salesByStage = await prisma_js_1.prisma.deal.groupBy({
            by: ["stage"],
            where,
            _count: true,
            _sum: {
                value: true,
            },
        });
        // Get top performing sales reps
        const topSalesReps = await prisma_js_1.prisma.user.findMany({
            where: {
                assignedDeals: {
                    some: {
                        stage: "CLOSED_WON",
                        createdAt: {
                            gte: startDate,
                            lte: endDate,
                        },
                    },
                },
            },
            select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
                _count: {
                    select: {
                        assignedDeals: {
                            where: {
                                stage: "CLOSED_WON",
                                createdAt: {
                                    gte: startDate,
                                    lte: endDate,
                                },
                            },
                        },
                    },
                },
                assignedDeals: {
                    where: {
                        stage: "CLOSED_WON",
                        createdAt: {
                            gte: startDate,
                            lte: endDate,
                        },
                    },
                    select: {
                        value: true,
                    },
                },
            },
            orderBy: {
                assignedDeals: {
                    _count: "desc",
                },
            },
            take: 5,
        });
        // Calculate total revenue for each sales rep
        const salesRepPerformance = topSalesReps.map((rep) => {
            const totalRevenue = rep.assignedDeals.reduce((sum, deal) => sum + deal.value, 0);
            return {
                id: rep.id,
                name: `${rep.firstName} ${rep.lastName}`,
                avatar: rep.avatar,
                deals: rep._count.assignedDeals,
                revenue: totalRevenue,
            };
        });
        // Get lead sources distribution
        const leadSources = await prisma_js_1.prisma.lead.groupBy({
            by: ["source"],
            where,
            _count: true,
        });
        // Prepare response
        const metrics = {
            period: {
                start: startDate,
                end: endDate,
            },
            summary: {
                newLeads,
                qualifiedLeads,
                convertedLeads,
                newDeals,
                wonDeals,
                lostDeals,
                totalRevenue: totalRevenue._sum.value || 0,
                activities,
                leadConversionRate,
                dealWinRate,
                avgDealSize,
            },
            comparison: comparisonMetrics,
            growth,
            salesByStage,
            topSalesReps: salesRepPerformance,
            leadSources,
        };
        res.status(200).json(metrics);
    }
    catch (error) {
        console.error("Error fetching performance metrics:", error);
        res.status(500).json({ error: "Failed to fetch performance metrics" });
    }
};
exports.getPerformanceMetrics = getPerformanceMetrics;
// Get sales forecasts
const getSalesForecasts = async (req, res) => {
    try {
        const { months = 3, userId } = req.query;
        const numMonths = Number(months);
        const now = new Date();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();
        // Build filter conditions for pipeline deals
        const pipelineWhere = {
            stage: {
                notIn: ["CLOSED_WON", "CLOSED_LOST"],
            },
        };
        if (userId) {
            pipelineWhere.assignedToId = Number(userId);
        }
        // Get pipeline deals
        const pipelineDeals = await prisma_js_1.prisma.deal.findMany({
            where: pipelineWhere,
            select: {
                id: true,
                name: true,
                value: true,
                probability: true,
                stage: true,
                expectedCloseDate: true,
                assignedTo: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
        // Get historical data for the past 12 months
        const historicalData = [];
        for (let i = 11; i >= 0; i--) {
            const month = (currentMonth - i + 12) % 12;
            const year = currentYear - Math.floor((i - currentMonth) / 12);
            const startDate = new Date(year, month, 1);
            const endDate = new Date(year, month + 1, 0, 23, 59, 59, 999);
            const where = {
                stage: "CLOSED_WON",
                actualCloseDate: {
                    gte: startDate,
                    lte: endDate,
                },
            };
            if (userId) {
                where.assignedToId = Number(userId);
            }
            const monthlyRevenue = await prisma_js_1.prisma.deal.aggregate({
                where,
                _sum: {
                    value: true,
                },
            });
            historicalData.push({
                month: startDate.toLocaleString("default", { month: "short" }),
                year,
                revenue: monthlyRevenue._sum.value || 0,
            });
        }
        // Generate forecast for the next N months
        const forecast = [];
        for (let i = 0; i < numMonths; i++) {
            const month = (currentMonth + i + 1) % 12;
            const year = currentYear + Math.floor((currentMonth + i + 1) / 12);
            const startDate = new Date(year, month, 1);
            const endDate = new Date(year, month + 1, 0, 23, 59, 59, 999);
            // Filter deals expected to close in this month
            const monthlyDeals = pipelineDeals.filter((deal) => {
                if (!deal.expectedCloseDate)
                    return false;
                const closeDate = new Date(deal.expectedCloseDate);
                return closeDate >= startDate && closeDate <= endDate;
            });
            // Calculate weighted revenue
            const weightedRevenue = monthlyDeals.reduce((sum, deal) => {
                return sum + deal.value * (deal.probability / 100);
            }, 0);
            // Calculate best and worst case scenarios
            const bestCase = monthlyDeals.reduce((sum, deal) => {
                return (sum + (deal.value * Math.min(deal.probability * 1.25, 100)) / 100);
            }, 0);
            const worstCase = monthlyDeals.reduce((sum, deal) => {
                return sum + (deal.value * Math.max(deal.probability * 0.75, 0)) / 100;
            }, 0);
            forecast.push({
                month: startDate.toLocaleString("default", { month: "short" }),
                year,
                weightedRevenue,
                bestCase,
                worstCase,
                deals: monthlyDeals.length,
            });
        }
        // Calculate total forecast
        const totalForecast = {
            weightedRevenue: forecast.reduce((sum, month) => sum + month.weightedRevenue, 0),
            bestCase: forecast.reduce((sum, month) => sum + month.bestCase, 0),
            worstCase: forecast.reduce((sum, month) => sum + month.worstCase, 0),
            deals: forecast.reduce((sum, month) => sum + month.deals, 0),
        };
        // Get sales goals
        const salesGoals = await prisma_js_1.prisma.salesGoal.findMany({
            where: {
                endDate: {
                    gte: now,
                },
                ...(userId ? { assignedToId: Number(userId) } : {}),
            },
            orderBy: {
                endDate: "asc",
            },
        });
        // Prepare response
        const forecastData = {
            historical: historicalData,
            forecast,
            totalForecast,
            salesGoals,
            pipelineSummary: {
                totalDeals: pipelineDeals.length,
                totalValue: pipelineDeals.reduce((sum, deal) => sum + deal.value, 0),
                weightedValue: pipelineDeals.reduce((sum, deal) => sum + (deal.value * deal.probability) / 100, 0),
            },
        };
        res.status(200).json(forecastData);
    }
    catch (error) {
        console.error("Error generating sales forecast:", error);
        res.status(500).json({ error: "Failed to generate sales forecast" });
    }
};
exports.getSalesForecasts = getSalesForecasts;
// Get custom reports
const getCustomReports = async (req, res) => {
    try {
        // In a real implementation, this would fetch saved custom reports from the database
        // For now, return some predefined report templates
        const reports = [
            {
                id: "1",
                name: "Sales Pipeline Analysis",
                description: "Detailed analysis of the current sales pipeline by stage and owner",
                type: "pipeline",
                createdAt: "2023-05-15T10:30:00Z",
                lastRun: "2023-06-10T14:45:00Z",
                parameters: {
                    timeframe: "this_quarter",
                    groupBy: "stage",
                },
            },
            {
                id: "2",
                name: "Lead Source Effectiveness",
                description: "Analysis of lead sources and their conversion rates",
                type: "leads",
                createdAt: "2023-04-20T09:15:00Z",
                lastRun: "2023-06-12T11:30:00Z",
                parameters: {
                    timeframe: "last_6_months",
                    groupBy: "source",
                },
            },
            {
                id: "3",
                name: "Sales Team Performance",
                description: "Comparison of sales team members performance metrics",
                type: "performance",
                createdAt: "2023-03-10T16:20:00Z",
                lastRun: "2023-06-11T10:00:00Z",
                parameters: {
                    timeframe: "this_year",
                    metrics: ["deals_closed", "revenue", "avg_deal_size", "win_rate"],
                },
            },
            {
                id: "4",
                name: "Quarterly Revenue Forecast",
                description: "Revenue forecast for the next quarter based on pipeline",
                type: "forecast",
                createdAt: "2023-05-25T13:45:00Z",
                lastRun: "2023-06-13T09:30:00Z",
                parameters: {
                    months: 3,
                    scenarios: ["weighted", "best_case", "worst_case"],
                },
            },
            {
                id: "5",
                name: "Sales Cycle Analysis",
                description: "Analysis of sales cycle length by deal size and type",
                type: "cycle",
                createdAt: "2023-04-05T11:10:00Z",
                lastRun: "2023-06-09T15:20:00Z",
                parameters: {
                    timeframe: "last_12_months",
                    groupBy: ["deal_size", "deal_type"],
                },
            },
        ];
        res.status(200).json(reports);
    }
    catch (error) {
        console.error("Error fetching custom reports:", error);
        res.status(500).json({ error: "Failed to fetch custom reports" });
    }
};
exports.getCustomReports = getCustomReports;
// Create custom report
const createCustomReport = async (req, res) => {
    try {
        const { name, description, type, parameters } = req.body;
        // In a real implementation, this would save the report to the database
        // For now, just return a mock response
        const report = {
            id: Math.random().toString(36).substring(2, 15),
            name,
            description,
            type,
            parameters,
            createdAt: new Date().toISOString(),
            lastRun: null,
        };
        res.status(201).json(report);
    }
    catch (error) {
        console.error("Error creating custom report:", error);
        res.status(500).json({ error: "Failed to create custom report" });
    }
};
exports.createCustomReport = createCustomReport;
//# sourceMappingURL=analytics.controller.js.map