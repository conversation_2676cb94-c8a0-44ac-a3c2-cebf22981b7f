"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeEmployeeDocument = exports.updateEmployeeDocument = exports.getEmployeeDocuments = exports.assignDocumentToEmployee = exports.deleteDocument = exports.updateDocument = exports.uploadDocument = exports.getDocumentById = exports.getAllDocuments = exports.upload = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Using centralized prisma instance from lib/prisma.js
// Configure multer for file uploads
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path_1.default.join(__dirname, "../../uploads/documents");
        // Create directory if it doesn't exist
        if (!fs_1.default.existsSync(uploadDir)) {
            fs_1.default.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
        const ext = path_1.default.extname(file.originalname);
        cb(null, file.fieldname + "-" + uniqueSuffix + ext);
    },
});
exports.upload = (0, multer_1.default)({
    storage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
    fileFilter: (req, file, cb) => {
        // Accept common document types
        const allowedTypes = [
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
            "image/jpeg",
            "image/png",
        ];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        }
        else {
            cb(new Error("Invalid file type. Only PDF, Word, Excel, PowerPoint, text, and image files are allowed."));
        }
    },
});
// Get all documents for a company
const getAllDocuments = async (req, res, next) => {
    try {
        const { companyId } = req.query;
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        const documents = await prisma_js_1.prisma.hrDocument.findMany({
            where: {
                companyId: Number(companyId),
            },
            include: {
                uploader: true,
                employeeDocuments: {
                    include: {
                        employee: {
                            include: {
                                user: true,
                            },
                        },
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(documents);
    }
    catch (error) {
        console.error("Error fetching documents:", error);
        next(createHttpError(500, "An error occurred"));
    }
};
exports.getAllDocuments = getAllDocuments;
// Get a specific document by ID
const getDocumentById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const document = await prisma_js_1.prisma.hrDocument.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                uploader: true,
                employeeDocuments: {
                    include: {
                        employee: {
                            include: {
                                user: true,
                            },
                        },
                    },
                },
            },
        });
        if (!document) {
            return next(createHttpError(404, "Document not found"));
        }
        res.json(document);
    }
    catch (error) {
        console.error("Error fetching document:", error);
        next(createHttpError(500, "An error occurred"));
    }
};
exports.getDocumentById = getDocumentById;
// Upload a new document
const uploadDocument = async (req, res, next) => {
    try {
        if (!req.file) {
            return next(createHttpError(400, "No file uploaded"));
        }
        const { title, description, category, isConfidential, expiryDate, companyId, } = req.body;
        if (!title || !category || !companyId) {
            return next(createHttpError(400, "Title, category, and company ID are required"));
        }
        const fileUrl = `/uploads/documents/${req.file.filename}`;
        const fileType = path_1.default.extname(req.file.originalname).substring(1);
        const fileSize = req.file.size;
        const document = await prisma_js_1.prisma.hrDocument.create({
            data: {
                title,
                description,
                category: category, // Cast to DocumentCategory enum
                fileUrl,
                fileType,
                fileSize: fileSize,
                isConfidential: isConfidential === "true",
                expiryDate: expiryDate ? new Date(expiryDate) : null,
                uploadedBy: req.user?.id || 0,
                companyId: Number(companyId),
            },
        });
        res.status(201).json(document);
    }
    catch (error) {
        console.error("Error uploading document:", error);
        next(createHttpError(500, "An error occurred"));
    }
};
exports.uploadDocument = uploadDocument;
// Update a document
const updateDocument = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { title, description, category, isConfidential, expiryDate } = req.body;
        const document = await prisma_js_1.prisma.hrDocument.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!document) {
            return next(createHttpError(404, "Document not found"));
        }
        const updatedDocument = await prisma_js_1.prisma.hrDocument.update({
            where: {
                id: Number(id),
            },
            data: {
                title: title !== undefined ? title : undefined,
                description: description !== undefined ? description : undefined,
                category: category !== undefined ? category : undefined,
                isConfidential: isConfidential !== undefined ? isConfidential === "true" : undefined,
                expiryDate: expiryDate !== undefined ? new Date(expiryDate) : undefined,
            },
        });
        res.json(updatedDocument);
    }
    catch (error) {
        console.error("Error updating document:", error);
        next(createHttpError(500, "An error occurred"));
    }
};
exports.updateDocument = updateDocument;
// Delete a document
const deleteDocument = async (req, res, next) => {
    try {
        const { id } = req.params;
        const document = await prisma_js_1.prisma.hrDocument.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!document) {
            return next(createHttpError(404, "Document not found"));
        }
        // Delete the file from the filesystem
        const filePath = path_1.default.join(__dirname, "../../", document.fileUrl);
        if (fs_1.default.existsSync(filePath)) {
            fs_1.default.unlinkSync(filePath);
        }
        // Delete all employee document associations
        await prisma_js_1.prisma.employeeDocument.deleteMany({
            where: {
                documentId: Number(id),
            },
        });
        // Delete the document record
        await prisma_js_1.prisma.hrDocument.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Document deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting document:", error);
        next(createHttpError(500, "An error occurred"));
    }
};
exports.deleteDocument = deleteDocument;
// Assign a document to an employee
const assignDocumentToEmployee = async (req, res, next) => {
    try {
        const { employeeId, documentId, accessLevel, notes } = req.body;
        if (!employeeId || !documentId) {
            return next(createHttpError(400, "Employee ID and document ID are required"));
        }
        // Check if employee exists
        const employee = await prisma_js_1.prisma.employee.findUnique({
            where: {
                id: Number(employeeId),
            },
        });
        if (!employee) {
            return next(createHttpError(404, "Employee not found"));
        }
        // Check if document exists
        const document = await prisma_js_1.prisma.hrDocument.findUnique({
            where: {
                id: Number(documentId),
            },
        });
        if (!document) {
            return next(createHttpError(404, "Document not found"));
        }
        // Check if assignment already exists
        const existingAssignment = await prisma_js_1.prisma.employeeDocument.findFirst({
            where: {
                employeeId: Number(employeeId),
                documentId: Number(documentId),
            },
        });
        if (existingAssignment) {
            return next(createHttpError(409, "Document is already assigned to this employee"));
        }
        const employeeDocument = await prisma_js_1.prisma.employeeDocument.create({
            data: {
                employeeId: Number(employeeId),
                documentId: Number(documentId),
                accessLevel: accessLevel || "VIEW",
                notes,
            },
        });
        res.status(201).json(employeeDocument);
    }
    catch (error) {
        console.error("Error assigning document to employee:", error);
        next(createHttpError(500, "An error occurred"));
    }
};
exports.assignDocumentToEmployee = assignDocumentToEmployee;
// Get all documents for an employee
const getEmployeeDocuments = async (req, res, next) => {
    try {
        const { employeeId } = req.params;
        const employeeDocuments = await prisma_js_1.prisma.employeeDocument.findMany({
            where: {
                employeeId: Number(employeeId),
            },
            include: {
                document: {
                    include: {
                        uploader: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(employeeDocuments);
    }
    catch (error) {
        console.error("Error fetching employee documents:", error);
        next(createHttpError(500, "An error occurred"));
    }
};
exports.getEmployeeDocuments = getEmployeeDocuments;
// Update an employee document assignment
const updateEmployeeDocument = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { accessLevel, isAcknowledged, notes } = req.body;
        const employeeDocument = await prisma_js_1.prisma.employeeDocument.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!employeeDocument) {
            return next(createHttpError(404, "Employee document assignment not found"));
        }
        const updatedEmployeeDocument = await prisma_js_1.prisma.employeeDocument.update({
            where: {
                id: Number(id),
            },
            data: {
                accessLevel: accessLevel !== undefined ? accessLevel : undefined,
                isAcknowledged: isAcknowledged !== undefined ? isAcknowledged : undefined,
                acknowledgedAt: isAcknowledged ? new Date() : undefined,
                notes: notes !== undefined ? notes : undefined,
            },
        });
        res.json(updatedEmployeeDocument);
    }
    catch (error) {
        console.error("Error updating employee document assignment:", error);
        next(createHttpError(500, "An error occurred"));
    }
};
exports.updateEmployeeDocument = updateEmployeeDocument;
// Remove a document from an employee
const removeEmployeeDocument = async (req, res, next) => {
    try {
        const { id } = req.params;
        const employeeDocument = await prisma_js_1.prisma.employeeDocument.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!employeeDocument) {
            return next(createHttpError(404, "Employee document assignment not found"));
        }
        await prisma_js_1.prisma.employeeDocument.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Document removed from employee successfully" });
    }
    catch (error) {
        console.error("Error removing document from employee:", error);
        next(createHttpError(500, "An error occurred"));
    }
};
exports.removeEmployeeDocument = removeEmployeeDocument;
//# sourceMappingURL=documents.controller.js.map