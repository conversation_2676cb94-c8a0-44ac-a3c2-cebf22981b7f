"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const subscriptionController_js_1 = require("../controllers/subscriptionController.js");
const subscriptionPackageController_js_1 = require("../controllers/subscriptionPackageController.js");
const subscriptionPlanController_js_1 = require("../controllers/subscriptionPlanController.js");
const authorizeRoles_js_1 = require("../middleware/authorizeRoles.js");
const router = express_1.default.Router();
// Public routes
router.get("/plans", (0, route_helpers_js_1.wrapController)(subscriptionPlanController_js_1.subscriptionPlanController.getAllPlans));
router.get("/packages", (0, route_helpers_js_1.wrapController)(subscriptionPackageController_js_1.subscriptionPackageController.getAllPackages));
// Protected routes - require authentication
router.get("/current", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(subscriptionController_js_1.subscriptionController.getUserSubscriptions));
router.get("/plan/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(subscriptionPlanController_js_1.subscriptionPlanController.getPlanById));
router.get("/active", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(subscriptionController_js_1.subscriptionController.getUserSubscriptions));
router.get("/status/:userId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(subscriptionController_js_1.subscriptionController.checkSubscriptionStatus));
// Subscription creation routes
router.post("/create-free", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(subscriptionController_js_1.subscriptionController.createFreeSubscription));
router.post("/create", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(subscriptionController_js_1.subscriptionController.createSubscription));
router.post("/purchase", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(subscriptionController_js_1.subscriptionController.purchaseSubscription));
// Subscription management routes
router.post("/:id/cancel", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(subscriptionController_js_1.subscriptionController.cancelSubscription));
// Admin-only routes
router.post("/packages", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("ADMIN", "SUPERADMIN"), (0, route_helpers_js_1.wrapController)(subscriptionPackageController_js_1.subscriptionPackageController.createPackage));
// Stripe webhook - no authentication required
// Note: This needs to be handled differently because it uses raw body parsing
router.post("/webhook", express_1.default.raw({ type: "application/json" }), (0, route_helpers_js_1.wrapController)(subscriptionController_js_1.subscriptionController.handleStripeWebhook));
exports.default = router;
//# sourceMappingURL=consolidatedSubscriptionRoutes.js.map