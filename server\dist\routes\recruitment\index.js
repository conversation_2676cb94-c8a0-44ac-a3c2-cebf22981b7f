"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const jobs_1 = __importDefault(require("./jobs"));
const applications_1 = __importDefault(require("./applications"));
const analytics_1 = __importDefault(require("./analytics"));
const interviews_1 = __importDefault(require("./interviews"));
const router = express_1.default.Router();
// Mount recruitment routes
router.use('/jobs', jobs_1.default);
router.use('/applications', applications_1.default);
router.use('/analytics', analytics_1.default);
router.use('/interviews', interviews_1.default);
exports.default = router;
//# sourceMappingURL=index.js.map