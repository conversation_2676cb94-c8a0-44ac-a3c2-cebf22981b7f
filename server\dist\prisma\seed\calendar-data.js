"use strict";
// server/prisma/seed/calendar-data.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedCalendarData = seedCalendarData;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function seedCalendarData() {
    console.log("🗓️ Seeding calendar data...");
    try {
        // Get existing users and companies for relationships
        const users = await prisma.user.findMany({
            select: {
                id: true,
                userRoles: {
                    select: {
                        role: true,
                    },
                },
                companies: {
                    select: {
                        companyId: true,
                    },
                },
            },
        });
        const companies = await prisma.company.findMany({
            select: { id: true },
        });
        if (users.length === 0 || companies.length === 0) {
            console.log("⚠️ No users or companies found. Skipping calendar seed.");
            return;
        }
        // Helper function to check if user has a specific role
        const hasRole = (user, role) => {
            return user.userRoles.some((ur) => ur.role === role);
        };
        // Helper function to get users by roles
        const getUsersByRoles = (roles) => {
            return users.filter((user) => roles.some((role) => hasRole(user, role)));
        };
        // Helper function to get random date in the next 30 days
        const getRandomFutureDate = (daysFromNow = 30) => {
            const now = new Date();
            const randomDays = Math.floor(Math.random() * daysFromNow);
            const date = new Date(now.getTime() + randomDays * 24 * 60 * 60 * 1000);
            return date;
        };
        // Helper function to add hours to a date
        const addHours = (date, hours) => {
            return new Date(date.getTime() + hours * 60 * 60 * 1000);
        };
        // Estonian calendar events data
        const calendarEvents = [
            // Company Events
            {
                title: "Ettevõtte aastakoosolek",
                description: "Aastane üldkoosolek kõigi töötajatega. Arutame möödunud aasta tulemusi ja järgmise aasta plaane.",
                category: "COMPANY",
                duration: 120, // 2 hours
                participants: users.slice(0, 5).map((u) => u.id),
            },
            {
                title: "Meeskonna ehitamise üritus",
                description: "Lõbus päev kolleegidega. Mängud, söök ja head vestlused.",
                category: "COMPANY",
                duration: 480, // 8 hours
                participants: users.slice(0, 8).map((u) => u.id),
            },
            {
                title: "Kvartali tulemuste esitlus",
                description: "Kvartali müügitulemuste ja finantsnäitajate ülevaade.",
                category: "COMPANY",
                duration: 90,
                participants: getUsersByRoles([
                    "ADMIN",
                    "PROJECTLEADER",
                    "SALESMAN",
                ]).map((u) => u.id),
            },
            // Project Events
            {
                title: "Uue veebilehe arendusprojekti algus",
                description: "Projekti käivitamise koosolek. Arutame nõudeid, ajakavasid ja meeskonna jaotust.",
                category: "PROJECT",
                duration: 60,
                participants: getUsersByRoles(["PROJECTLEADER", "WORKER"]).map((u) => u.id),
            },
            {
                title: "Projekti vahekokkuvõte",
                description: "Pooleli oleva projekti edenemise ülevaade ja järgmiste sammude planeerimine.",
                category: "PROJECT",
                duration: 45,
                participants: getUsersByRoles(["PROJECTLEADER", "WORKER"])
                    .slice(0, 4)
                    .map((u) => u.id),
            },
            {
                title: "Projekti lõpetamise koosolek",
                description: "Projekti tulemuste hindamine ja õppetundide kogumine.",
                category: "PROJECT",
                duration: 75,
                participants: getUsersByRoles(["PROJECTLEADER", "WORKER", "ADMIN"]).map((u) => u.id),
            },
            // Sales Events
            {
                title: "Kliendikohtumine - ABC Ettevõte",
                description: "Esitlus uuele potentsiaalsele kliendile. Arutame nende vajadusi ja meie lahendusi.",
                category: "SALES",
                duration: 90,
                participants: getUsersByRoles(["SALESMAN", "ADMIN"])
                    .slice(0, 2)
                    .map((u) => u.id),
            },
            {
                title: "Müügimeeskonna iganädalane koosolek",
                description: "Nädala müügitulemuste ülevaade ja järgmise nädala eesmärkide seadmine.",
                category: "SALES",
                duration: 60,
                participants: getUsersByRoles(["SALESMAN"]).map((u) => u.id),
            },
            {
                title: "Lepingu allkirjastamine",
                description: "Olulise lepingu allkirjastamine kliendiga XYZ OÜ.",
                category: "SALES",
                duration: 30,
                participants: getUsersByRoles(["SALESMAN", "ADMIN"])
                    .slice(0, 2)
                    .map((u) => u.id),
            },
            // Meeting Events
            {
                title: "Tehnilise meeskonna nõupidamine",
                description: "Arutame tehnilisi väljakutseid ja lahendusi. Jagame teadmisi ja kogemusi.",
                category: "MEETING",
                duration: 60,
                participants: getUsersByRoles(["WORKER"]).map((u) => u.id),
            },
            {
                title: "Juhatuse koosolek",
                description: "Strateegiliste otsuste arutamine ja ettevõtte suuna määramine.",
                category: "MEETING",
                duration: 120,
                participants: getUsersByRoles(["ADMIN", "PROJECTLEADER"]).map((u) => u.id),
            },
            {
                title: "Klienditeeninduse koolitus",
                description: "Koolitus parema klienditeeninduse pakkumiseks. Praktilised harjutused ja näpunäited.",
                category: "MEETING",
                duration: 180,
                participants: users.slice(0, 6).map((u) => u.id),
            },
            // Task Events
            {
                title: "Kodulehe uuendamine",
                description: "Ettevõtte kodulehe sisu ja disaini uuendamine. Uute funktsioonide lisamine.",
                category: "TASK",
                duration: 240,
                participants: getUsersByRoles(["WORKER"])
                    .slice(0, 2)
                    .map((u) => u.id),
            },
            {
                title: "Andmebaasi optimeerimine",
                description: "Andmebaasi jõudluse parandamine ja aegunud andmete puhastamine.",
                category: "TASK",
                duration: 180,
                participants: getUsersByRoles(["WORKER"])
                    .slice(0, 1)
                    .map((u) => u.id),
            },
            {
                title: "Turundusmaterjali loomine",
                description: "Uute toodete jaoks turundusmaterjali ja brošüüride koostamine.",
                category: "TASK",
                duration: 300,
                participants: getUsersByRoles(["WORKER", "SALESMAN"])
                    .slice(0, 2)
                    .map((u) => u.id),
            },
            // Deadline Events
            {
                title: "Projekti esimese faasi tähtaeg",
                description: "Projekti esimese etapi lõpetamise tähtaeg. Kõik ülesanded peavad olema valmis.",
                category: "DEADLINE",
                duration: 30,
                participants: getUsersByRoles(["PROJECTLEADER", "WORKER"]).map((u) => u.id),
            },
            {
                title: "Aastaaruande esitamise tähtaeg",
                description: "Aastaaruande koostamise ja esitamise viimane päev.",
                category: "DEADLINE",
                duration: 60,
                participants: getUsersByRoles(["ADMIN", "PROJECTLEADER"]).map((u) => u.id),
            },
            // Personal Events
            {
                title: "Isiklik arengukõnelus",
                description: "Aastane arengukõnelus juhiga. Arutame eesmärke ja karjääriplaane.",
                category: "PERSONAL",
                duration: 60,
                participants: [users[0]?.id].filter(Boolean),
            },
            {
                title: "Tervisekontroll",
                description: "Aastane tervisekontroll ettevõtte arsti juures.",
                category: "PERSONAL",
                duration: 45,
                participants: [users[1]?.id].filter(Boolean),
            },
        ];
        // Create calendar events
        for (let i = 0; i < calendarEvents.length; i++) {
            const eventData = calendarEvents[i];
            const startDate = getRandomFutureDate(60); // Events in next 60 days
            const endDate = addHours(startDate, eventData.duration / 60);
            // Get a random user as creator
            const creator = users[Math.floor(Math.random() * users.length)];
            // Get company from creator's companies or use a random one
            const creatorCompanyId = creator.companies.length > 0
                ? creator.companies[0].companyId
                : companies[Math.floor(Math.random() * companies.length)].id;
            try {
                const event = await prisma.calendarEvent.create({
                    data: {
                        title: eventData.title,
                        description: eventData.description,
                        startDate,
                        endDate,
                        duration: eventData.duration,
                        category: eventData.category,
                        status: "SCHEDULED",
                        companyId: creatorCompanyId,
                        createdById: creator.id,
                        participants: {
                            create: [
                                // Add creator as accepted participant
                                {
                                    userId: creator.id,
                                    status: "ACCEPTED",
                                },
                                // Add other participants as pending
                                ...eventData.participants
                                    .filter((id) => id !== creator.id)
                                    .slice(0, 5) // Limit to 5 additional participants
                                    .map((userId) => ({
                                    userId,
                                    status: "PENDING",
                                })),
                            ],
                        },
                        reminders: {
                            create: [
                                {
                                    user: {
                                        connect: { id: creator.id },
                                    },
                                    reminderType: "EMAIL",
                                    minutesBefore: 15,
                                },
                            ],
                        },
                    },
                });
                console.log(`✅ Created event: ${event.title}`);
            }
            catch (error) {
                console.error(`❌ Failed to create event: ${eventData.title}`, error);
            }
        }
        console.log("🎉 Calendar data seeded successfully!");
    }
    catch (error) {
        console.error("❌ Error seeding calendar data:", error);
        throw error;
    }
}
//# sourceMappingURL=calendar-data.js.map