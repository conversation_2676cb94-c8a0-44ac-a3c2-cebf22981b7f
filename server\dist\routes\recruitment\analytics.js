"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../../utils/route-helpers.js");
const analyticsController_js_1 = require("../../controllers/recruitment/analyticsController.js");
const router = express_1.default.Router();
// All routes require authentication
router.use(route_helpers_js_1.auth);
// Analytics routes
router.get("/company/:companyId/overview", (0, route_helpers_js_1.wrapController)(analyticsController_js_1.getAnalyticsOverview));
router.get("/company/:companyId/metrics", (0, route_helpers_js_1.wrapController)(analyticsController_js_1.getMetricsByPeriod));
router.get("/company/:companyId/funnel", (0, route_helpers_js_1.wrapController)(analyticsController_js_1.getApplicationFunnel));
router.get("/company/:companyId/top-jobs", (0, route_helpers_js_1.wrapController)(analyticsController_js_1.getTopPerformingJobs));
router.get("/company/:companyId/sources", (0, route_helpers_js_1.wrapController)(analyticsController_js_1.getSourcesAnalysis));
router.get("/company/:companyId/time-to-hire", (0, route_helpers_js_1.wrapController)(analyticsController_js_1.getTimeToHireAnalysis));
exports.default = router;
//# sourceMappingURL=analytics.js.map