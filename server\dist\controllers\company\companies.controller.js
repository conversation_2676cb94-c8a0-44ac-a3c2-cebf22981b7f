"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.companiesController = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function for error handling
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Using centralized prisma instance from lib/prisma.js
exports.companiesController = {
    // Main company operations
    getAllCompanies: async (req, res) => {
        try {
            const companies = await prisma_js_1.prisma.company.findMany({
                include: {
                    contacts: true,
                    workforceNeeds: true,
                },
            });
            res.json(companies);
        }
        catch (error) {
            res.status(500).json({ message: "Failed to fetch companies", error });
        }
    },
    getCompanyById: async (req, res) => {
        try {
            const { id } = req.params;
            const company = await prisma_js_1.prisma.company.findUnique({
                where: { id: Number(id) },
                include: {
                    contacts: true,
                    workforceNeeds: true,
                },
            });
            if (!company) {
                return res.status(404).json({ message: "Company not found" });
            }
            res.json(company);
        }
        catch (error) {
            res.status(500).json({ message: "Failed to fetch company", error });
        }
    },
    createCompany: async (req, res) => {
        try {
            const company = await prisma_js_1.prisma.company.create({
                data: {
                    ...req.body,
                    contractTerms: req.body.contractTerms
                        ? JSON.stringify(req.body.contractTerms)
                        : null,
                },
                include: {
                    contacts: true,
                    workforceNeeds: true,
                },
            });
            res.status(201).json(company);
        }
        catch (error) {
            res.status(500).json({ message: "Failed to create company", error });
        }
    },
    updateCompany: async (req, res) => {
        try {
            const { id } = req.params;
            const company = await prisma_js_1.prisma.company.update({
                where: { id: Number(id) },
                data: {
                    ...req.body,
                    contractTerms: req.body.contractTerms
                        ? req.body.contractTerms
                        : undefined,
                },
                include: {
                    contacts: true,
                    workforceNeeds: true,
                },
            });
            res.json(company);
        }
        catch (error) {
            res.status(500).json({ message: "Failed to update company", error });
        }
    },
    deleteCompany: async (req, res) => {
        try {
            const { id } = req.params;
            await prisma_js_1.prisma.company.delete({
                where: { id: Number(id) },
            });
            res.status(204).send();
        }
        catch (error) {
            res.status(500).json({ message: "Failed to delete company", error });
        }
    },
    // Workforce needs operations
    getCompanyWorkforceNeeds: async (req, res) => {
        try {
            const { id } = req.params;
            const workforceNeeds = await prisma_js_1.prisma.workforceNeed.findMany({
                where: { companyId: Number(id) },
            });
            res.json(workforceNeeds);
        }
        catch (error) {
            res
                .status(500)
                .json({ message: "Failed to fetch workforce needs", error });
        }
    },
    createWorkforceNeed: async (req, res) => {
        try {
            const { id } = req.params;
            const workforceNeed = await prisma_js_1.prisma.workforceNeed.create({
                data: {
                    ...req.body,
                    companyId: Number(id),
                    aiMatches: req.body.aiMatches ? req.body.aiMatches : undefined,
                },
            });
            res.status(201).json(workforceNeed);
        }
        catch (error) {
            res
                .status(500)
                .json({ message: "Failed to create workforce need", error });
        }
    },
    updateWorkforceNeed: async (req, res) => {
        try {
            const { needId } = req.params;
            const workforceNeed = await prisma_js_1.prisma.workforceNeed.update({
                where: { id: Number(needId) },
                data: {
                    ...req.body,
                    aiMatches: req.body.aiMatches ? req.body.aiMatches : undefined,
                },
            });
            res.json(workforceNeed);
        }
        catch (error) {
            res
                .status(500)
                .json({ message: "Failed to update workforce need", error });
        }
    },
    deleteWorkforceNeed: async (req, res) => {
        try {
            const { needId } = req.params;
            await prisma_js_1.prisma.workforceNeed.delete({
                where: { id: Number(needId) },
            });
            res.status(204).send();
        }
        catch (error) {
            res
                .status(500)
                .json({ message: "Failed to delete workforce need", error });
        }
    },
    // Contacts operations
    getCompanyContacts: async (req, res) => {
        try {
            const { id } = req.params;
            const contacts = await prisma_js_1.prisma.contact.findMany({
                where: { companyId: Number(id) },
            });
            res.json(contacts);
        }
        catch (error) {
            res.status(500).json({ message: "Failed to fetch contacts", error });
        }
    },
    createContact: async (req, res) => {
        try {
            const { id } = req.params;
            const contact = await prisma_js_1.prisma.contact.create({
                data: {
                    ...req.body,
                    companyId: Number(id),
                },
            });
            res.status(201).json(contact);
        }
        catch (error) {
            res.status(500).json({ message: "Failed to create contact", error });
        }
    },
    updateContact: async (req, res) => {
        try {
            const { contactId } = req.params;
            const contact = await prisma_js_1.prisma.contact.update({
                where: { id: Number(contactId) },
                data: req.body,
            });
            res.json(contact);
        }
        catch (error) {
            res.status(500).json({ message: "Failed to update contact", error });
        }
    },
    deleteContact: async (req, res) => {
        try {
            const { contactId } = req.params;
            await prisma_js_1.prisma.contact.delete({
                where: { id: Number(contactId) },
            });
            res.status(204).send();
        }
        catch (error) {
            res.status(500).json({ message: "Failed to delete contact", error });
        }
    },
};
//# sourceMappingURL=companies.controller.js.map