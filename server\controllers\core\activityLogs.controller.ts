import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Get all activity logs with filtering and pagination
export const getActivityLogs = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      type,
      module,
      userId,
      status,
      severity,
      startDate,
      endDate,
      sortBy = "timestamp",
      sortOrder = "desc",
    } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { title: { contains: search as string, mode: "insensitive" } },
        { description: { contains: search as string, mode: "insensitive" } },
        { action: { contains: search as string, mode: "insensitive" } },
      ];
    }

    if (type && type !== "all_types") {
      where.type = type;
    }

    if (module && module !== "all_modules") {
      where.module = module;
    }

    if (userId && userId !== "all_users") {
      where.userId = parseInt(userId as string);
    }

    if (status && status !== "all_statuses") {
      where.status = status;
    }

    if (severity && severity !== "all_severities") {
      where.severity = severity;
    }

    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) {
        where.timestamp.gte = new Date(startDate as string);
      }
      if (endDate) {
        where.timestamp.lte = new Date(endDate as string);
      }
    }

    // Get total count
    const total = await prisma.activityLog.count({ where });

    // Get activity logs
    const activityLogs = await prisma.activityLog.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
      },
      orderBy: {
        [sortBy as string]: sortOrder,
      },
      skip,
      take: limitNum,
    });

    res.status(200).json({
      data: activityLogs,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum),
      },
    });
  } catch (error) {
    console.error("Error fetching activity logs:", error);
    res.status(500).json({ error: "Failed to fetch activity logs" });
  }
};

// Get activity log by ID
export const getActivityLogById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const activityLog = await prisma.activityLog.findUnique({
      where: { id: parseInt(id) },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
      },
    });

    if (!activityLog) {
      res.status(404).json({ error: "Activity log not found" });
      return;
    }

    res.status(200).json(activityLog);
  } catch (error) {
    console.error("Error fetching activity log:", error);
    res.status(500).json({ error: "Failed to fetch activity log" });
  }
};

// Create activity log (for system use)
export const createActivityLog = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      type,
      action,
      module,
      entity,
      entityId,
      title,
      description,
      metadata,
      userId,
      ipAddress,
      userAgent,
      status = "SUCCESS",
      severity = "INFO",
      changes,
    } = req.body;

    const activityLog = await prisma.activityLog.create({
      data: {
        type,
        action,
        module,
        entity,
        entityId,
        title,
        description,
        metadata,
        userId: userId ? parseInt(userId) : null,
        ipAddress,
        userAgent,
        status,
        severity,
        changes,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
      },
    });

    res.status(201).json(activityLog);
  } catch (error) {
    console.error("Error creating activity log:", error);
    res.status(500).json({ error: "Failed to create activity log" });
  }
};

// Get activity log statistics
export const getActivityLogStats = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { period = "7d" } = req.query;

    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case "24h":
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case "7d":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Get total logs
    const totalLogs = await prisma.activityLog.count({
      where: {
        timestamp: {
          gte: startDate,
        },
      },
    });

    // Get logs by type
    const logsByType = await prisma.activityLog.groupBy({
      by: ["type"],
      where: {
        timestamp: {
          gte: startDate,
        },
      },
      _count: {
        id: true,
      },
    });

    // Get logs by module
    const logsByModule = await prisma.activityLog.groupBy({
      by: ["module"],
      where: {
        timestamp: {
          gte: startDate,
        },
      },
      _count: {
        id: true,
      },
    });

    // Get error logs
    const errorLogs = await prisma.activityLog.count({
      where: {
        timestamp: {
          gte: startDate,
        },
        severity: {
          in: ["ERROR", "CRITICAL"],
        },
      },
    });

    // Get unique users
    const uniqueUsers = await prisma.activityLog.findMany({
      where: {
        timestamp: {
          gte: startDate,
        },
        userId: {
          not: null,
        },
      },
      select: {
        userId: true,
      },
      distinct: ["userId"],
    });

    // Get daily breakdown for chart data
    const dailyStats = [];
    const days = period === "24h" ? 1 : period === "7d" ? 7 : 30;

    for (let i = days - 1; i >= 0; i--) {
      const dayStart = new Date(now);
      dayStart.setDate(dayStart.getDate() - i);
      dayStart.setHours(0, 0, 0, 0);

      const dayEnd = new Date(dayStart);
      dayEnd.setHours(23, 59, 59, 999);

      // Get counts for this day
      const [dayTotal, dayLogins, dayDataChanges, dayErrors] =
        await Promise.all([
          // Total activities for the day
          prisma.activityLog.count({
            where: {
              timestamp: { gte: dayStart, lte: dayEnd },
            },
          }),
          // Login activities
          prisma.activityLog.count({
            where: {
              timestamp: { gte: dayStart, lte: dayEnd },
              type: "LOGIN",
            },
          }),
          // Data change activities
          prisma.activityLog.count({
            where: {
              timestamp: { gte: dayStart, lte: dayEnd },
              type: {
                in: [
                  "CREATE",
                  "UPDATE",
                  "DELETE",
                  "USER_CREATE",
                  "USER_UPDATE",
                  "PASSWORD_CHANGE",
                  "DEAL_CREATE",
                  "DEAL_UPDATE",
                  "PROJECT_CREATE",
                  "PROJECT_UPDATE",
                ],
              },
            },
          }),
          // Error activities
          prisma.activityLog.count({
            where: {
              timestamp: { gte: dayStart, lte: dayEnd },
              severity: { in: ["ERROR", "CRITICAL"] },
            },
          }),
        ]);

      dailyStats.push({
        date: dayStart.toISOString(),
        total: dayTotal,
        logins: dayLogins,
        dataChanges: dayDataChanges,
        errors: dayErrors,
      });
    }

    res.status(200).json({
      totalLogs,
      errorLogs,
      uniqueUsers: uniqueUsers.length,
      logsByType: logsByType.map((item) => ({
        type: item.type,
        count: item._count.id,
      })),
      logsByModule: logsByModule.map((item) => ({
        module: item.module,
        count: item._count.id,
      })),
      dailyStats, // New daily breakdown data
      period,
      startDate,
      endDate: now,
    });
  } catch (error) {
    console.error("Error fetching activity log stats:", error);
    res.status(500).json({ error: "Failed to fetch activity log statistics" });
  }
};
