"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const authorizeRoles_js_1 = require("../middleware/authorizeRoles.js");
// Import controllers
const database_controller_js_1 = require("../controllers/core/database.controller.js");
const router = express_1.default.Router();
// Database connection routes
router.get("/connections", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.getDatabaseConnections));
router.post("/connections", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.createDatabaseConnection));
router.put("/connections/:id", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.updateDatabaseConnection));
router.delete("/connections/:id", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.deleteDatabaseConnection));
router.post("/connections/:id/activate", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.activateDatabaseConnection));
router.post("/test-connection", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.testDatabaseConnection));
router.post("/update-connection-string", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.updateConnectionString));
// Database management routes
router.post("/migrate", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.migrateDatabase));
router.post("/generate-schema", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.generateSchema));
router.post("/backup", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.backupDatabase));
router.post("/restore", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.restoreDatabase));
router.post("/kill-connection", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.killConnection));
// Database information routes
router.get("/stats", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.getDatabaseStats));
router.get("/tables", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.getDatabaseTables));
router.get("/connection-pool", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.getConnectionPool));
router.get("/connection-leaks", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.getConnectionLeaks));
router.get("/schema", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.getDatabaseSchema));
// Query execution route
router.post("/execute-query", route_helpers_js_1.auth, (0, authorizeRoles_js_1.authorizeRoles)("SUPERADMIN"), (0, route_helpers_js_1.wrapController)(database_controller_js_1.executeQuery));
exports.default = router;
//# sourceMappingURL=database.js.map