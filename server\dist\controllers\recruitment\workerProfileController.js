"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRecommendedWorkers = exports.addSafetyTraining = exports.searchWorkerProfiles = exports.updateVerificationStatus = exports.updateWorkerProfile = exports.createWorkerProfile = exports.getWorkerProfileById = exports.getAllWorkerProfiles = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
// Get all worker profiles
const getAllWorkerProfiles = async (req, res) => {
    try {
        const workerProfiles = await prisma_js_1.prisma.workerProfile.findMany({
            include: {
                worker: {
                    include: {
                        skills: true,
                        certifications: true,
                    },
                },
                safetyTrainings: true,
            },
        });
        return res.status(200).json(workerProfiles);
    }
    catch (error) {
        console.error("Error fetching worker profiles:", error);
        return res.status(500).json({ error: "Failed to fetch worker profiles" });
    }
};
exports.getAllWorkerProfiles = getAllWorkerProfiles;
// Get worker profile by ID
const getWorkerProfileById = async (req, res) => {
    const { id } = req.params;
    try {
        const workerProfile = await prisma_js_1.prisma.workerProfile.findUnique({
            where: { id: Number(id) },
            include: {
                worker: {
                    include: {
                        skills: true,
                        certifications: true,
                        healthChecks: true,
                        documents: true,
                    },
                },
                safetyTrainings: true,
                workHistory: {
                    include: {
                        jobRequest: {
                            include: {
                                company: true,
                            },
                        },
                    },
                },
                reviews: true,
            },
        });
        if (!workerProfile) {
            return res.status(404).json({ error: "Worker profile not found" });
        }
        return res.status(200).json(workerProfile);
    }
    catch (error) {
        console.error("Error fetching worker profile:", error);
        return res.status(500).json({ error: "Failed to fetch worker profile" });
    }
};
exports.getWorkerProfileById = getWorkerProfileById;
// Create worker profile
const createWorkerProfile = async (req, res) => {
    const { workerId, specialization, experienceYears, hourlyRate, dailyRate, weeklyRate, monthlyRate, availableFrom, currentlyAvailable, searchTags, } = req.body;
    try {
        // Check if worker exists
        const worker = await prisma_js_1.prisma.worker.findUnique({
            where: { id: workerId },
        });
        if (!worker) {
            return res.status(404).json({ error: "Worker not found" });
        }
        // Check if worker profile already exists
        const existingProfile = await prisma_js_1.prisma.workerProfile.findUnique({
            where: { workerId },
        });
        if (existingProfile) {
            return res.status(400).json({ error: "Worker profile already exists" });
        }
        // Create worker profile
        const workerProfile = await prisma_js_1.prisma.workerProfile.create({
            data: {
                worker: {
                    connect: { id: workerId },
                },
                specialization,
                experienceYears,
                hourlyRate,
                dailyRate,
                weeklyRate,
                monthlyRate,
                availableFrom,
                currentlyAvailable,
                searchTags,
            },
        });
        return res.status(201).json(workerProfile);
    }
    catch (error) {
        console.error("Error creating worker profile:", error);
        return res.status(500).json({ error: "Failed to create worker profile" });
    }
};
exports.createWorkerProfile = createWorkerProfile;
// Update worker profile
const updateWorkerProfile = async (req, res) => {
    const { id } = req.params;
    const { specialization, experienceYears, hourlyRate, dailyRate, weeklyRate, monthlyRate, availableFrom, currentlyAvailable, searchTags, } = req.body;
    try {
        const workerProfile = await prisma_js_1.prisma.workerProfile.update({
            where: { id: Number(id) },
            data: {
                specialization,
                experienceYears,
                hourlyRate,
                dailyRate,
                weeklyRate,
                monthlyRate,
                availableFrom,
                currentlyAvailable,
                searchTags,
            },
        });
        return res.status(200).json(workerProfile);
    }
    catch (error) {
        console.error("Error updating worker profile:", error);
        return res.status(500).json({ error: "Failed to update worker profile" });
    }
};
exports.updateWorkerProfile = updateWorkerProfile;
// Update worker verification status
const updateVerificationStatus = async (req, res) => {
    const { id } = req.params;
    const { verificationStatus, verifiedBy } = req.body;
    try {
        const workerProfile = await prisma_js_1.prisma.workerProfile.update({
            where: { id: Number(id) },
            data: {
                verificationStatus,
                verifiedBy,
                verifiedAt: verificationStatus === "verified" ? new Date() : null,
            },
        });
        return res.status(200).json(workerProfile);
    }
    catch (error) {
        console.error("Error updating verification status:", error);
        return res
            .status(500)
            .json({ error: "Failed to update verification status" });
    }
};
exports.updateVerificationStatus = updateVerificationStatus;
// Search worker profiles
const searchWorkerProfiles = async (req, res) => {
    const { specialization, skills, minExperience, maxHourlyRate, availableNow, verificationStatus, minRating, } = req.query;
    try {
        const workerProfiles = await prisma_js_1.prisma.workerProfile.findMany({
            where: {
                specialization: specialization
                    ? { contains: specialization, mode: "insensitive" }
                    : undefined,
                experienceYears: minExperience
                    ? { gte: parseInt(minExperience) }
                    : undefined,
                hourlyRate: maxHourlyRate
                    ? { lte: parseFloat(maxHourlyRate) }
                    : undefined,
                currentlyAvailable: availableNow === "true" ? true : undefined,
                verificationStatus: verificationStatus,
                overallRating: minRating
                    ? { gte: parseFloat(minRating) }
                    : undefined,
                worker: skills
                    ? {
                        skills: {
                            some: {
                                name: { in: skills.split(",") },
                            },
                        },
                    }
                    : undefined,
            },
            include: {
                worker: {
                    include: {
                        skills: true,
                        certifications: true,
                    },
                },
            },
            orderBy: {
                overallRating: "desc",
            },
        });
        return res.status(200).json(workerProfiles);
    }
    catch (error) {
        console.error("Error searching worker profiles:", error);
        return res.status(500).json({ error: "Failed to search worker profiles" });
    }
};
exports.searchWorkerProfiles = searchWorkerProfiles;
// Add safety training
const addSafetyTraining = async (req, res) => {
    const { id } = req.params;
    const { trainingType, provider, completionDate, expiryDate, certificateUrl } = req.body;
    try {
        const safetyTraining = await prisma_js_1.prisma.safetyTraining.create({
            data: {
                worker: {
                    connect: { id: Number(id) },
                },
                trainingType,
                provider,
                completionDate: new Date(completionDate),
                expiryDate: expiryDate ? new Date(expiryDate) : null,
                certificateUrl,
            },
        });
        return res.status(201).json(safetyTraining);
    }
    catch (error) {
        console.error("Error adding safety training:", error);
        return res.status(500).json({ error: "Failed to add safety training" });
    }
};
exports.addSafetyTraining = addSafetyTraining;
// Get AI-recommended workers for a job
const getRecommendedWorkers = async (req, res) => {
    const { jobRequestId } = req.params;
    try {
        // Get job request details
        const jobRequest = await prisma_js_1.prisma.jobRequest.findUnique({
            where: { id: Number(jobRequestId) },
            include: {
                preferredWorkers: true,
            },
        });
        if (!jobRequest) {
            return res.status(404).json({ error: "Job request not found" });
        }
        // Find matching workers based on specialization, skills, and availability
        const matchingWorkers = await prisma_js_1.prisma.workerProfile.findMany({
            where: {
                specialization: {
                    contains: jobRequest.specialization,
                    mode: "insensitive",
                },
                currentlyAvailable: true,
                verificationStatus: "verified",
                worker: {
                    skills: {
                        some: {
                            name: { in: jobRequest.requiredSkills },
                        },
                    },
                    certifications: {
                        some: {
                            name: { in: jobRequest.requiredCertifications },
                        },
                    },
                },
            },
            include: {
                worker: {
                    include: {
                        skills: true,
                        certifications: true,
                    },
                },
            },
            orderBy: {
                overallRating: "desc",
            },
        });
        // Calculate match score for each worker
        const recommendedWorkers = matchingWorkers.map((worker) => {
            // Basic match score calculation (can be enhanced with AI in the future)
            let matchScore = 0;
            // Specialization match
            if (worker.specialization.toLowerCase() ===
                jobRequest.specialization.toLowerCase()) {
                matchScore += 30;
            }
            // Experience level match
            const experienceScore = Math.min(worker.experienceYears * 5, 25);
            matchScore += experienceScore;
            // Skills match
            const workerSkills = worker.worker.skills.map((skill) => skill.name.toLowerCase());
            const skillMatchCount = jobRequest.requiredSkills.filter((skill) => workerSkills.includes(skill.toLowerCase())).length;
            const skillMatchScore = (skillMatchCount / jobRequest.requiredSkills.length) * 25;
            matchScore += skillMatchScore;
            // Rating score
            const ratingScore = worker.overallRating * 4;
            matchScore += ratingScore;
            // Preferred worker bonus
            // Check if preferredWorkers exists before using it
            const isPreferred = jobRequest.preferredWorkers &&
                Array.isArray(jobRequest.preferredWorkers) &&
                jobRequest.preferredWorkers.some((pw) => pw.workerId === worker.workerId);
            if (isPreferred) {
                matchScore += 10;
            }
            return {
                ...worker,
                matchScore,
                matchReason: `Specialization match: ${worker.specialization === jobRequest.specialization
                    ? "Yes"
                    : "Partial"},
                      Experience: ${worker.experienceYears} years,
                      Skills match: ${skillMatchCount}/${jobRequest.requiredSkills.length},
                      Rating: ${worker.overallRating}/5${isPreferred ? ", Preferred worker" : ""}`,
            };
        });
        // Sort by match score
        recommendedWorkers.sort((a, b) => b.matchScore - a.matchScore);
        return res.status(200).json(recommendedWorkers);
    }
    catch (error) {
        console.error("Error getting recommended workers:", error);
        return res.status(500).json({ error: "Failed to get recommended workers" });
    }
};
exports.getRecommendedWorkers = getRecommendedWorkers;
//# sourceMappingURL=workerProfileController.js.map