"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startDbMonitor = startDbMonitor;
exports.stopDbMonitor = stopDbMonitor;
const prisma_js_1 = require("./prisma.js");
const nodemailer_1 = __importDefault(require("nodemailer"));
// Configuration
const CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes
const MAX_CONNECTIONS_THRESHOLD = 80; // 80% of max connections
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
// Create a transporter for sending emails
const transporter = nodemailer_1.default.createTransport({
    host: process.env.SMTP_HOST || 'smtp.example.com',
    port: parseInt(process.env.SMTP_PORT || '587', 10),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || '',
    },
});
/**
 * Check database connection status
 */
async function checkDatabaseConnections() {
    try {
        // Query to get current connection count
        const result = await prisma_js_1.prisma.$queryRaw `SELECT count(*) as connection_count FROM pg_stat_activity`;
        // @ts-ignore - result is an array with one object
        const connectionCount = parseInt(result[0].connection_count, 10);
        // Get max connections from PostgreSQL config
        const maxConnectionsResult = await prisma_js_1.prisma.$queryRaw `SHOW max_connections`;
        // @ts-ignore - result is an array with one object
        const maxConnections = parseInt(maxConnectionsResult[0].max_connections, 10);
        // Calculate percentage
        const connectionPercentage = (connectionCount / maxConnections) * 100;
        console.log(`Database connections: ${connectionCount}/${maxConnections} (${connectionPercentage.toFixed(2)}%)`);
        // If connections exceed threshold, send alert
        if (connectionPercentage > MAX_CONNECTIONS_THRESHOLD) {
            await sendAlert(connectionCount, maxConnections, connectionPercentage);
        }
    }
    catch (error) {
        console.error('Error checking database connections:', error);
    }
}
/**
 * Send alert email about high connection count
 */
async function sendAlert(connectionCount, maxConnections, percentage) {
    try {
        await transporter.sendMail({
            from: process.env.SMTP_FROM || '<EMAIL>',
            to: ADMIN_EMAIL,
            subject: '⚠️ Database Connection Alert - Ultimation Studio',
            html: `
        <h1>Database Connection Alert</h1>
        <p>The database connection count has reached a critical level:</p>
        <ul>
          <li><strong>Current connections:</strong> ${connectionCount}</li>
          <li><strong>Maximum connections:</strong> ${maxConnections}</li>
          <li><strong>Usage percentage:</strong> ${percentage.toFixed(2)}%</li>
        </ul>
        <p>This may indicate a connection leak in the application.</p>
        <p>Please check the application logs and consider restarting the server if necessary.</p>
      `,
        });
        console.log('Database connection alert email sent');
    }
    catch (error) {
        console.error('Error sending alert email:', error);
    }
}
/**
 * Start the database connection monitor
 */
function startDbMonitor() {
    // Run immediately on startup
    checkDatabaseConnections();
    // Then run at regular intervals
    setInterval(checkDatabaseConnections, CHECK_INTERVAL);
    console.log(`Database connection monitor started (checking every ${CHECK_INTERVAL / 60000} minutes)`);
}
/**
 * Stop the database connection monitor
 */
function stopDbMonitor() {
    // This is a placeholder - we don't actually need to stop anything
    // since the process will be terminated anyway
    console.log('Database connection monitor stopped');
}
//# sourceMappingURL=db-monitor.js.map