"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// server/scripts/clear-projects.ts
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function clearProjects() {
    console.log('🗑️ Clearing existing projects...');
    try {
        // Delete all projects (this will cascade to related data)
        const deletedCount = await prisma.project.deleteMany({});
        console.log(`✅ Deleted ${deletedCount.count} projects`);
        console.log('🎉 Projects cleared successfully!');
    }
    catch (error) {
        console.error('❌ Error clearing projects:', error);
        throw error;
    }
    finally {
        await prisma.$disconnect();
    }
}
// Run the clear function if this file is executed directly
if (require.main === module) {
    clearProjects()
        .then(() => {
        console.log('✅ Projects clearing completed');
        process.exit(0);
    })
        .catch((error) => {
        console.error('❌ Projects clearing failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=clear-projects.js.map