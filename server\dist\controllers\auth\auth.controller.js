"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.register = exports.refreshToken = exports.login = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const crypto_1 = __importDefault(require("crypto"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const codeGenerator_js_1 = require("../../services/codeGenerator.js");
const sendEmail_js_1 = require("../../utils/sendEmail.js");
const activityLogger_js_1 = require("../../services/activityLogger.js");
const prisma = new client_1.PrismaClient();
const SALT_ROUNDS = 10;
const JWT_SECRET = process.env.JWT_SECRET || "your_default_jwt_secret";
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || "your_default_jwt_refresh_secret";
const SERVER_URL = process.env.SERVER_URL || "http://localhost:4004";
const login = async (req, res) => {
    const { email, password, trustDevice } = req.body;
    if (!email || !password) {
        return res.status(400).json({ message: "Email and password are required" });
    }
    try {
        const user = await prisma.user.findUnique({ where: { email } });
        if (!user || !user.password) {
            console.log("Invalid email or password for:", email);
            // Log failed login attempt
            await activityLogger_js_1.ActivityLogger.logUserLogin(0, req, false);
            return res.status(401).json({ message: "Invalid email or password" });
        }
        const isMatch = await bcrypt_1.default.compare(password, user.password);
        if (!isMatch) {
            console.log("Invalid email or password for:", email);
            // Log failed login attempt
            await activityLogger_js_1.ActivityLogger.logUserLogin(user.id, req, false);
            return res.status(401).json({ message: "Invalid email or password" });
        }
        const tokenPayload = {
            id: user.id.toString(),
            roles: [], // Will be updated with userWithRoles below
            email: user.email,
        };
        const expiresIn = trustDevice ? "60d" : "1h";
        const accessToken = jsonwebtoken_1.default.sign(tokenPayload, JWT_SECRET, { expiresIn });
        // Log successful login
        await activityLogger_js_1.ActivityLogger.logUserLogin(user.id, req, true);
        res.json({
            accessToken,
        });
    }
    catch (error) {
        console.error("Error during login:", error);
        res.status(500).json({ message: "Server error", error });
    }
};
exports.login = login;
const refreshToken = async (req, res) => {
    const { refreshToken } = req.body;
    if (!refreshToken)
        return res.sendStatus(401);
    jsonwebtoken_1.default.verify(refreshToken, JWT_REFRESH_SECRET, (err, user) => {
        if (err) {
            console.error("Refresh token verification error:", err);
            return res.sendStatus(403);
        }
        const tokenPayload = {
            id: user.id,
            roles: user.roles,
            email: user.email,
        };
        const newAccessToken = jsonwebtoken_1.default.sign(tokenPayload, JWT_SECRET, {
            expiresIn: "1h",
        });
        console.log("Access token refreshed for user:", user.id);
        res.json({ accessToken: newAccessToken });
    });
};
exports.refreshToken = refreshToken;
const register = async (req, res) => {
    const { firstName, lastName, mobile, email, companyName, password, role } = req.body;
    try {
        console.log("Registering user with email:", email);
        // Validate role
        const validRoles = [
            "CLIENT",
            "PROJECTLEADER",
            "SALESMAN",
            "WORKER",
            "SUPPORTER",
        ];
        const userRole = role && validRoles.includes(role) ? role : "CLIENT";
        console.log(`Registering user with role: ${userRole}`);
        const existingUser = await prisma.user.findUnique({ where: { email } });
        if (existingUser) {
            return res.status(400).json({ message: "Email already in use" });
        }
        // Check if company exists and has an owner (for CLIENT role)
        if (userRole === "CLIENT" && companyName) {
            const company = await prisma.company.findFirst({
                where: { name: companyName },
            });
            if (company) {
                // Check if company already has an owner
                const existingOwner = await prisma.userCompany.findFirst({
                    where: {
                        companyId: company.id,
                        role: "OWNER",
                    },
                });
                if (existingOwner) {
                    return res.status(400).json({
                        message: "This company already has an owner. Please use a different company name.",
                    });
                }
            }
        }
        const hashedPassword = await bcrypt_1.default.hash(password, SALT_ROUNDS);
        const { barcode: userBarcode, qrCode: userQrCode } = await (0, codeGenerator_js_1.generateCodes)();
        const emailConfirmationToken = crypto_1.default.randomBytes(32).toString("hex");
        // Create user with the specified role
        const user = await prisma.user.create({
            data: {
                firstName,
                lastName,
                email,
                password: hashedPassword,
                phone: mobile,
                userRoles: {
                    create: [{ role: userRole }],
                },
                barcode: userBarcode,
                qrCode: userQrCode,
                emailConfirmed: false,
                emailConfirmationToken,
                phoneConfirmed: false,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
        });
        console.log("New user created with ID:", user.id);
        // Log user creation
        await activityLogger_js_1.ActivityLogger.logUserCreate(user.id, req, user.id);
        // Handle company relationship based on role
        if (userRole === "CLIENT" && companyName) {
            // For CLIENT role, create a new company if it doesn't exist
            let company = await prisma.company.findFirst({
                where: { name: companyName },
            });
            if (!company) {
                const { barcode: companyBarcode, qrCode: companyQrCode } = await (0, codeGenerator_js_1.generateCodes)();
                company = await prisma.company.create({
                    data: {
                        name: companyName,
                        type: "CLIENT", // Adding required CompanyType
                        status: "ACTIVE", // Adding required CompanyStatus
                        barcode: companyBarcode,
                        qrCode: companyQrCode,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                    },
                });
                console.log("New company created with ID:", company.id);
            }
            // Connect user to company as OWNER
            await prisma.user.update({
                where: {
                    id: user.id,
                },
                data: {
                    companies: {
                        create: {
                            company: {
                                connect: {
                                    id: company.id,
                                },
                            },
                            role: "OWNER",
                            status: "ACTIVE",
                        },
                    },
                },
            });
        }
        else if (userRole === "SUPPORTER" && companyName) {
            // For SUPPORTER role, we don't need to create a company relationship
            // but we might want to store the package information
            console.log(`User ${user.id} registered as a SUPPORTER`);
            // Additional SUPPORTER-specific logic can be added here
        }
        else if (["PROJECTLEADER", "SALESMAN", "WORKER"].includes(userRole) &&
            companyName) {
            // For other roles, create a join request to the specified company
            const company = await prisma.company.findFirst({
                where: { name: companyName },
            });
            if (company) {
                // Create a join request
                await prisma.companyJoinRequest.create({
                    data: {
                        user: { connect: { id: user.id } },
                        company: { connect: { id: company.id } },
                        companyName,
                        role: userRole,
                        status: "PENDING",
                    },
                });
                console.log(`Created join request for user ${user.id} to company ${company.id}`);
                // Find company owner to notify
                const companyOwner = await prisma.userCompany.findFirst({
                    where: {
                        companyId: company.id,
                        role: "OWNER",
                    },
                    include: {
                        user: true,
                    },
                });
                if (companyOwner && companyOwner.user) {
                    // Send email notification to company owner
                    const emailSubject = "New Join Request";
                    const emailBody = `
            <h1>New Join Request</h1>
            <p>${firstName} ${lastName} has requested to join your company as a ${userRole}.</p>
            <p>Please log in to your account to approve or reject this request.</p>
            <a href="${process.env.CLIENT_URL || "http://localhost:3000"}/recruitment/company-requests">View Request</a>
          `;
                    await (0, sendEmail_js_1.sendEmail)(companyOwner.user.email, emailSubject, emailBody);
                }
            }
            else {
                console.log(`Company ${companyName} not found for join request`);
            }
        }
        // Send email confirmation
        const confirmationLink = `${SERVER_URL}/api/v1/email/confirm-email?token=${emailConfirmationToken}`;
        const emailSubject = "Confirm Your CoManager Account";
        const emailBody = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="color-scheme" content="light">
        <meta name="supported-color-schemes" content="light">
        <title>Confirm Your CoManager Account</title>
        <!--[if mso]>
        <noscript>
          <xml>
            <o:OfficeDocumentSettings>
              <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
          </xml>
        </noscript>
        <![endif]-->
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            -moz-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
          }
          .logo-container {
            display: inline-block;
            margin-bottom: 20px;
            white-space: nowrap;
            line-height: 1;
          }
          .logo-text {
            font-size: 22px;
            font-weight: bold !important;
            font-family: Arial, sans-serif;
            color: #333 !important;
            letter-spacing: 0.05em;
            display: inline-block;
            vertical-align: middle;
            line-height: 1;
          }
          .logo-text.bold {
            font-weight: 900 !important;
          }
          .logo-text-firstletter {
            font-size: 24px;
            font-weight: bold !important;
            font-family: Arial, sans-serif;
            color: #333 !important;
            display: inline-block;
            vertical-align: middle;
            line-height: 1;
          }
          .logo-image {
            width: 20px;
            height: 22px;
            vertical-align: middle;
            margin-bottom: 4px;
            display: inline-block;
            position: relative;
          }
          .content {
            padding: 30px 20px;
            text-align: center;
          }
          .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #00a0c0 !important;
            color: #ffffff !important;
            text-decoration: none !important;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
            transition: background-color 0.3s;
            border: 1px solid #00a0c0;
            font-family: Arial, sans-serif;
            font-size: 16px;
            text-align: center;
            mso-line-height-rule: exactly;
            line-height: 1.5;
            -webkit-text-size-adjust: none;
          }
          .button:hover {
            background-color: #00b8db !important;
          }
          .footer {
            text-align: center;
            padding: 20px;
            color: #888;
            font-size: 0.9em;
            border-top: 1px solid #eee;
          }
          .expiry {
            font-size: 0.85em;
            color: #888;
            margin-top: 15px;
          }
          h1 {
            color: #333;
            margin-bottom: 20px;
          }
          p {
            margin-bottom: 15px;
          }
        </style>
      </head>
      <body style="margin:0;padding:0;background-color:#f9f9f9;font-family:Arial,sans-serif;">
        <!-- Preheader text (hidden) -->
        <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;">
          Please confirm your email address to complete your CoManager account registration.
        </div>
        <!-- Main table structure -->
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center" style="padding:40px 0;">
              <div class="container">
          <div class="header">
            <div class="logo-container">
              <span class="logo-text-firstletter bold">C</span>
              <img src="cid:<EMAIL>" alt="O" class="logo-image">
              <span class="logo-text bold">MANAGER</span>
            </div>
            <h1>Confirm Your Email Address</h1>
          </div>
          <div class="content">
            <p>Thank you for registering with CoManager!</p>
            <p>To complete your registration and activate your account, please confirm your email address by clicking the button below:</p>
            <!-- MSO conditional code for Outlook -->
            <!--[if mso]>
            <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${confirmationLink}" style="height:40px;v-text-anchor:middle;width:200px;" arcsize="10%" stroke="f" fillcolor="#00b8db">
              <w:anchorlock/>
              <center>
                <![endif]-->
                <a href="${confirmationLink}" class="button" style="background-color:#00b8db;border-radius:6px;color:#ffffff;display:inline-block;font-family:Arial, sans-serif;font-size:16px;font-weight:bold;line-height:40px;text-align:center;text-decoration:none;width:200px;-webkit-text-size-adjust:none;">Confirm Email</a>
            <!--[if mso]>
              </center>
            </v:roundrect>
            <![endif]-->
            <p class="expiry">This confirmation link will expire in 24 hours.</p>
            <p>If you didn't create an account with CoManager, you can safely ignore this email.</p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} CoManager. All rights reserved.</p>
          </div>
        </div>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `;
        try {
            // Use the logo from the server/public directory
            const logoPath = path.join(process.cwd(), "public/logo.png");
            console.log("Using logo path:", logoPath);
            // Check if the file exists
            if (fs.existsSync(logoPath)) {
                console.log("Logo file exists at path:", logoPath);
                // Send email with logo attachment
                await (0, sendEmail_js_1.sendEmail)(user.email, emailSubject, null, emailBody, [
                    {
                        filename: "logo.png",
                        path: logoPath,
                        cid: "<EMAIL>", // CID referenced in the HTML
                    },
                ]);
                console.log("Confirmation email sent successfully with logo attachment");
            }
            else {
                console.error("No logo file found in any of the checked paths");
                // Send email without logo attachment as fallback
                await (0, sendEmail_js_1.sendEmail)(user.email, emailSubject, null, emailBody);
                console.log("Confirmation email sent without logo attachment");
            }
        }
        catch (emailError) {
            console.error("Error sending confirmation email:", emailError);
            // Continue with registration even if email fails
        }
        console.log("Confirmation email sent to:", user.email);
        // Fix the roles mapping by getting the roles from the created user
        const userWithRoles = await prisma.user.findUnique({
            where: { id: user.id },
            include: { userRoles: true },
        });
        const tokenPayload = {
            id: user.id.toString(),
            roles: userWithRoles?.userRoles.map((role) => role.role) || [userRole],
            email: user.email,
        };
        const accessToken = jsonwebtoken_1.default.sign(tokenPayload, JWT_SECRET, {
            expiresIn: "1d",
        });
        res.status(201).json({
            message: "User registered successfully. You are now automatically logged in.",
            accessToken,
            userId: user.id,
            role: userRole,
        });
    }
    catch (error) {
        console.error("Error during registration:", error);
        res.status(500).json({ message: "Server error", error });
    }
};
exports.register = register;
//# sourceMappingURL=auth.controller.js.map