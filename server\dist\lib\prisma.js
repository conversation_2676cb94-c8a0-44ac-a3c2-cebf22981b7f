"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.disconnectPrisma = exports.prisma = void 0;
const client_1 = require("@prisma/client");
const extension_optimize_1 = require("@prisma/extension-optimize");
// PrismaClient is attached to the `global` object in development to prevent
// exhausting your database connection limit.
//
// Learn more:
// https://pris.ly/d/help/next-js-best-practices
// Define the global variable type
const globalForPrisma = global;
/**
 * Extends the PrismaClient with Optimize if API key is available
 */
function extendPrismaClient(client) {
    if (process.env.OPTIMIZE_API_KEY) {
        return client.$extends((0, extension_optimize_1.withOptimize)({
            apiKey: process.env.OPTIMIZE_API_KEY,
        }));
    }
    return client;
}
// This function is no longer needed as we create the client directly below
// Create the base PrismaClient first
const basePrismaClient = new client_1.PrismaClient({
    log: ["error"],
    datasources: {
        db: {
            url: process.env.DATABASE_URL,
        },
    },
});
// Note: We've removed the middleware due to deprecation warnings
// Connection errors will be handled at the application level
// Create or reuse the Prisma Client instance
exports.prisma = globalForPrisma.prisma || extendPrismaClient(basePrismaClient);
// Cleanup function to be called on application shutdown
const disconnectPrisma = async () => {
    try {
        // We need to cast here because the extended client might not have $disconnect
        // but the base client always does
        if ("$disconnect" in exports.prisma) {
            await exports.prisma.$disconnect();
            console.log("Disconnected from database");
        }
    }
    catch (error) {
        console.error("Error disconnecting from database:", error);
        process.exit(1);
    }
};
exports.disconnectPrisma = disconnectPrisma;
// Store the instance in the global object in development
if (process.env.NODE_ENV !== "production") {
    globalForPrisma.prisma = exports.prisma;
}
//# sourceMappingURL=prisma.js.map