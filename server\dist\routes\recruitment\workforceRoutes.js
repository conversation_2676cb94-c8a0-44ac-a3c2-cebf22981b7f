"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../../utils/route-helpers.js");
const workerProfileController = __importStar(require("../../controllers/recruitment/workerProfileController.js"));
const jobRequestController = __importStar(require("../../controllers/recruitment/jobRequestController.js"));
const jobMatchController = __importStar(require("../../controllers/recruitment/jobMatchController.js"));
const workAssignmentController = __importStar(require("../../controllers/recruitment/workAssignmentController.js"));
const rentalCompanyController = __importStar(require("../../controllers/recruitment/rentalCompanyController.js"));
const router = express_1.default.Router();
// Worker Profile Routes
router.get("/worker-profiles", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workerProfileController.getAllWorkerProfiles));
router.get("/worker-profiles/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workerProfileController.getWorkerProfileById));
router.post("/worker-profiles", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workerProfileController.createWorkerProfile));
router.put("/worker-profiles/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workerProfileController.updateWorkerProfile));
router.put("/worker-profiles/:id/verification", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workerProfileController.updateVerificationStatus));
router.get("/worker-profiles/search", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workerProfileController.searchWorkerProfiles));
router.post("/worker-profiles/:id/safety-trainings", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workerProfileController.addSafetyTraining));
router.get("/job-requests/:jobRequestId/recommended-workers", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workerProfileController.getRecommendedWorkers));
// Job Request Routes
router.get("/job-requests", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(jobRequestController.getAllJobRequests));
router.get("/job-requests/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(jobRequestController.getJobRequestById));
router.post("/job-requests", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(jobRequestController.createJobRequest));
router.put("/job-requests/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(jobRequestController.updateJobRequest));
router.post("/job-requests/:id/preferred-workers", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(jobRequestController.addPreferredWorker));
router.get("/job-requests/search", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(jobRequestController.searchJobRequests));
// Job Match Routes
router.get("/matches", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(jobMatchController.getAllMatches));
router.get("/job-requests/:jobRequestId/matches", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(jobMatchController.getMatchesForJobRequest));
router.get("/worker-profiles/:workerProfileId/matches", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(jobMatchController.getMatchesForWorker));
router.put("/matches/:id/worker-interest", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(jobMatchController.updateWorkerInterest));
router.put("/matches/:id/company-interest", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(jobMatchController.updateCompanyInterest));
// Work Assignment Routes
router.get("/assignments", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workAssignmentController.getAllWorkAssignments));
router.get("/assignments/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workAssignmentController.getWorkAssignmentById));
router.get("/companies/:companyId/assignments", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workAssignmentController.getWorkAssignmentsForCompany));
router.get("/worker-profiles/:workerProfileId/assignments", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workAssignmentController.getWorkAssignmentsForWorker));
router.put("/assignments/:id/status", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workAssignmentController.updateWorkAssignmentStatus));
router.put("/assignments/:id/trial", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workAssignmentController.updateTrialPeriodStatus));
router.post("/assignments/:id/time-entries", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workAssignmentController.addTimeEntry));
router.put("/time-entries/:id/approve", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workAssignmentController.approveTimeEntry));
router.post("/assignments/:id/worker-reviews", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workAssignmentController.addWorkerReview));
router.post("/assignments/:id/company-reviews", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workAssignmentController.addCompanyReview));
// Rental Company Routes
router.get("/rental-companies", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(rentalCompanyController.getAllRentalCompanies));
router.get("/rental-companies/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(rentalCompanyController.getRentalCompanyById));
router.post("/rental-companies", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(rentalCompanyController.createRentalCompany));
router.put("/rental-companies/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(rentalCompanyController.updateRentalCompany));
router.post("/rental-companies/:id/workers", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(rentalCompanyController.addWorkerToRentalCompany));
router.delete("/rental-companies/:id/workers/:workerId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(rentalCompanyController.removeWorkerFromRentalCompany));
exports.default = router;
//# sourceMappingURL=workforceRoutes.js.map