"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const waitlist_controller_js_1 = require("../controllers/waitlist/waitlist.controller.js");
const route_helpers_js_1 = require("../utils/route-helpers.js");
const router = express_1.default.Router();
// Public routes
router.post("/register", (0, route_helpers_js_1.wrapController)(waitlist_controller_js_1.registerWaitlistEmail));
router.get("/confirm/:token", (0, route_helpers_js_1.wrapController)(waitlist_controller_js_1.confirmWaitlistEmail));
// Admin routes (protected)
router.get("/emails", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(waitlist_controller_js_1.getWaitlistEmails));
exports.default = router;
//# sourceMappingURL=waitlist.js.map