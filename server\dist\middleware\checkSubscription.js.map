{"version": 3, "file": "checkSubscription.js", "sourceRoot": "", "sources": ["../../middleware/checkSubscription.ts"], "names": [], "mappings": ";;;;;;AACA,2CAAkE;AAClE,8DAAsC;AAYtC,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAElC;;;GAGG;AACI,MAAM,iBAAiB,GAAG,CAAC,gBAA0B,EAAE,EAAE;IAC9D,OAAO,KAAK,EACV,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;QACjB,IAAI,CAAC;YACH,kCAAkC;YAClC,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC3C,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;YAE5B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC,IAAA,qBAAW,EAAC,GAAG,EAAE,iCAAiC,CAAC,CAAC,CAAC;YACnE,CAAC;YAED,0CAA0C;YAC1C,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;gBAC7D,KAAK,EAAE;oBACL,MAAM;oBACN,+CAA+C;oBAC/C,MAAM,EAAE,2BAAkB,CAAC,MAAM;oBACjC,OAAO,EAAE;wBACP,EAAE,EAAE,IAAI,IAAI,EAAE;qBACf;iBACF;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,IAAI,EAAE,8BAA8B;iBAC3C;aACF,CAAC,CAAC;YAEH,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnC,MAAM,YAAY,GAAG,mBAAmB,CAAC,GAAG,CAC1C,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAC9B,CAAC;gBAEF,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CACvD,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAC3B,CAAC;gBAEF,IAAI,kBAAkB,EAAE,CAAC;oBACvB,OAAO,IAAI,EAAE,CAAC;gBAChB,CAAC;YACH,CAAC;YAED,+BAA+B;YAC/B,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;gBACvD,KAAK,EAAE;oBACL,MAAM;oBACN,EAAE,EAAE;wBACF,EAAE,MAAM,EAAE,2BAAkB,CAAC,MAAM,EAAE;wBACrC,EAAE,MAAM,EAAE,2BAAkB,CAAC,KAAK,EAAE;qBACrC;iBACF;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,IAAI;iBACX;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,MAAM;iBAClB;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO,IAAI,CACT,IAAA,qBAAW,EAAC,GAAG,EAAE,6CAA6C,CAAC,CAChE,CAAC;YACJ,CAAC;YAED,6BAA6B;YAC7B,IACE,YAAY,CAAC,MAAM,KAAK,2BAAkB,CAAC,KAAK;gBAChD,YAAY,CAAC,YAAY,EACzB,CAAC;gBACD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,GAAG,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;oBACpC,wCAAwC;oBACxC,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;wBAC/B,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE;wBAC9B,IAAI,EAAE,EAAE,MAAM,EAAE,2BAAkB,CAAC,OAAO,EAAE;qBAC7C,CAAC,CAAC;oBAEH,OAAO,IAAI,CACT,IAAA,qBAAW,EAAC,GAAG,EAAE,yCAAyC,CAAC,CAC5D,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,+DAA+D;YAC/D,IAAI,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnD,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,OAAmB,CAAC;gBAE1D,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CACtD,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAC1B,CAAC;gBAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACvB,OAAO,IAAI,CACT,IAAA,qBAAW,EACT,GAAG,EACH,oEAAoE,CACrE,CACF,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,iCAAiC;YACjC,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,IAAA,qBAAW,EAAC,GAAG,EAAE,uBAAuB,CAAC,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAlHW,QAAA,iBAAiB,qBAkH5B;AAEF;;;GAGG;AACI,MAAM,uBAAuB,GAAG,KAAK,EAC1C,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,kCAAkC;QAClC,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3C,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QAE5B,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC,IAAA,qBAAW,EAAC,GAAG,EAAE,iCAAiC,CAAC,CAAC,CAAC;QACnE,CAAC;QAED,iEAAiE;QACjE,MAAM,kBAAkB,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;YAC7D,KAAK,EAAE;gBACL,MAAM;gBACN,MAAM,EAAE,2BAAkB,CAAC,MAAM;gBACjC,OAAO,EAAE;oBACP,EAAE,EAAE,IAAI,IAAI,EAAE;iBACf;aACF;SACF,CAAC,CAAC;QAEH,IAAI,kBAAkB,EAAE,CAAC;YACvB,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,+BAA+B;QAC/B,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;YACvD,KAAK,EAAE;gBACL,MAAM;gBACN,EAAE,EAAE;oBACF,EAAE,MAAM,EAAE,2BAAkB,CAAC,MAAM,EAAE;oBACrC,EAAE,MAAM,EAAE,2BAAkB,CAAC,KAAK,EAAE;iBACrC;aACF;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,IAAI,CACT,IAAA,qBAAW,EAAC,GAAG,EAAE,qDAAqD,CAAC,CACxE,CAAC;QACJ,CAAC;QAED,6BAA6B;QAC7B,IACE,YAAY,CAAC,MAAM,KAAK,2BAAkB,CAAC,KAAK;YAChD,YAAY,CAAC,YAAY,EACzB,CAAC;YACD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,GAAG,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;gBACpC,wCAAwC;gBACxC,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC/B,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE;oBAC9B,IAAI,EAAE,EAAE,MAAM,EAAE,2BAAkB,CAAC,OAAO,EAAE;iBAC7C,CAAC,CAAC;gBAEH,OAAO,IAAI,CACT,IAAA,qBAAW,EAAC,GAAG,EAAE,iDAAiD,CAAC,CACpE,CAAC;YACJ,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,IAAA,qBAAW,EAAC,GAAG,EAAE,uBAAuB,CAAC,CAAC,CAAC;IAClD,CAAC;AACH,CAAC,CAAC;AA7EW,QAAA,uBAAuB,2BA6ElC"}