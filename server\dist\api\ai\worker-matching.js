"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
// Initialize Prisma client
const prisma = new client_1.PrismaClient();
// Helper functions
async function calculateWorkerMatches(workers, criteria) {
    // Implementation of worker matching algorithm
    return workers
        .map((worker) => ({
        workerId: worker.id,
        score: Math.random() * 100, // Placeholder for actual scoring logic
        matchedSkills: worker.skills.filter((skill) => criteria.skills.includes(skill)),
    }))
        .sort((a, b) => b.score - a.score);
}
function generateMatchExplanations(matches) {
    // Generate explanations for matches
    return matches.map((match) => ({
        workerId: match.workerId,
        explanation: `Worker matched with score ${match.score.toFixed(2)} based on ${match.matchedSkills.length} skills.`,
    }));
}
exports.default = async (req, res) => {
    const { laborRequest, availableWorkers } = req.body;
    const matchingCriteria = {
        skills: laborRequest.requirements.skills,
        experience: laborRequest.requirements.experience,
        location: laborRequest.requirements.location,
        availability: laborRequest.requirements.schedule,
        ratingThreshold: 4.0,
    };
    const matches = await calculateWorkerMatches(availableWorkers, matchingCriteria);
    // Log AI decision for transparency
    await prisma.aiDecisionLog.create({
        data: {
            module: "HR",
            action: "WORKER_MATCHING",
            context: {
                request: laborRequest,
                criteria: matchingCriteria,
            },
            rules: {
                matchingAlgorithm: "skill-based",
                priorityFactors: ["skills", "availability", "experience"],
            },
            suggestion: "Matched workers based on skills and availability",
            severity: "INFO",
        },
    });
    return res.json({
        matches,
        explanations: generateMatchExplanations(matches),
    });
};
//# sourceMappingURL=worker-matching.js.map