"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateEmailStatus = exports.sendEmail = exports.getEmailFolders = exports.getEmailMessages = exports.createEmailAccount = exports.getEmailAccounts = exports.createDemoEmailData = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const sendEmail_js_1 = require("../../utils/sendEmail.js");
// Using centralized prisma instance from lib/prisma.js
// Create demo email data for testing
const createDemoEmailData = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Check if user already has email accounts
        const existingAccounts = await prisma_js_1.prisma.emailAccount.findMany({
            where: { userId },
        });
        if (existingAccounts.length > 0) {
            res.status(400).json({ error: "Demo data already exists" });
            return;
        }
        // Create demo email account
        const account = await prisma_js_1.prisma.emailAccount.create({
            data: {
                userId,
                email: "<EMAIL>",
                name: "Demo Account",
                provider: "DEMO",
                isDefault: true,
            },
        });
        // Create default folders
        const defaultFolders = ["inbox", "sent", "drafts", "trash", "spam"];
        const folders = [];
        for (const folderType of defaultFolders) {
            const folder = await prisma_js_1.prisma.emailFolder.create({
                data: {
                    accountId: account.id,
                    name: folderType.charAt(0).toUpperCase() + folderType.slice(1),
                    type: folderType,
                },
            });
            folders.push(folder);
        }
        // Find inbox folder
        const inboxFolder = folders.find((f) => f.type === "inbox");
        if (!inboxFolder) {
            res.status(500).json({ error: "Failed to create inbox folder" });
            return;
        }
        // Create demo messages
        const demoMessages = [
            {
                from: "<EMAIL>",
                to: ["<EMAIL>"],
                subject: "Welcome to CoManager!",
                body: "Welcome to CoManager! We're excited to have you on board. This is your first email in the system.",
                status: "UNREAD",
                priority: "HIGH",
                receivedAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
            },
            {
                from: "<EMAIL>",
                to: ["<EMAIL>"],
                subject: "Project Update - Q1 2024",
                body: "Hi there! I wanted to give you a quick update on our Q1 projects. Everything is progressing well and we're on track to meet our deadlines.",
                status: "READ",
                priority: "NORMAL",
                receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
            },
            {
                from: "<EMAIL>",
                to: ["<EMAIL>"],
                subject: "Getting Started Guide",
                body: "Here's a comprehensive guide to help you get started with CoManager. You'll find tips and tricks to make the most of our platform.",
                status: "UNREAD",
                priority: "NORMAL",
                receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
            },
            {
                from: "<EMAIL>",
                to: ["<EMAIL>"],
                subject: "Weekly Team Meeting",
                body: "Don't forget about our weekly team meeting tomorrow at 10 AM. We'll be discussing the upcoming project milestones.",
                status: "READ",
                priority: "NORMAL",
                receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
            },
        ];
        for (const messageData of demoMessages) {
            await prisma_js_1.prisma.emailMessage.create({
                data: {
                    accountId: account.id,
                    folderId: inboxFolder.id,
                    ...messageData,
                    cc: [],
                    bcc: [],
                    isHtml: false,
                    hasAttachments: false,
                },
            });
        }
        res.status(201).json({
            message: "Demo email data created successfully",
            account,
            folders,
            messageCount: demoMessages.length,
        });
    }
    catch (error) {
        console.error("Error creating demo email data:", error);
        res.status(500).json({ error: "Failed to create demo email data" });
    }
};
exports.createDemoEmailData = createDemoEmailData;
// Get all email accounts for the authenticated user
const getEmailAccounts = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        const accounts = await prisma_js_1.prisma.emailAccount.findMany({
            where: {
                userId,
            },
            orderBy: {
                isDefault: "desc",
            },
        });
        res.status(200).json(accounts);
    }
    catch (error) {
        console.error("Error fetching email accounts:", error);
        res.status(500).json({ error: "Failed to fetch email accounts" });
    }
};
exports.getEmailAccounts = getEmailAccounts;
// Create a new email account
const createEmailAccount = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        const { email, name, provider, password, isDefault } = req.body;
        // Validate required fields
        if (!email || !name || !provider || !password) {
            res.status(400).json({ error: "Missing required fields" });
            return;
        }
        // If this is the default account, unset any existing default
        if (isDefault) {
            await prisma_js_1.prisma.emailAccount.updateMany({
                where: {
                    userId,
                    isDefault: true,
                },
                data: {
                    isDefault: false,
                },
            });
        }
        // Create the account
        const account = await prisma_js_1.prisma.emailAccount.create({
            data: {
                userId,
                email,
                name,
                provider,
                isDefault: isDefault || false,
            },
        });
        // In a real implementation, we would store the credentials securely
        // and set up the connection to the email provider
        // Create default folders
        const defaultFolders = ["inbox", "sent", "drafts", "trash", "spam"];
        for (const folderType of defaultFolders) {
            await prisma_js_1.prisma.emailFolder.create({
                data: {
                    accountId: account.id,
                    name: folderType.charAt(0).toUpperCase() + folderType.slice(1),
                    type: folderType,
                },
            });
        }
        res.status(201).json(account);
    }
    catch (error) {
        console.error("Error creating email account:", error);
        res.status(500).json({ error: "Failed to create email account" });
    }
};
exports.createEmailAccount = createEmailAccount;
// Get email messages for a specific account and folder
const getEmailMessages = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { accountId } = req.params;
        const { folder = "inbox" } = req.query;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Verify the account belongs to the user
        const account = await prisma_js_1.prisma.emailAccount.findFirst({
            where: {
                id: Number(accountId),
                userId,
            },
        });
        if (!account) {
            res.status(404).json({ error: "Email account not found" });
            return;
        }
        // Find the folder
        const emailFolder = await prisma_js_1.prisma.emailFolder.findFirst({
            where: {
                accountId: Number(accountId),
                type: folder.toString(),
            },
        });
        if (!emailFolder) {
            res.status(404).json({ error: "Folder not found" });
            return;
        }
        // Get messages
        const messages = await prisma_js_1.prisma.emailMessage.findMany({
            where: {
                accountId: Number(accountId),
                folderId: emailFolder.id,
            },
            include: {
                attachments: true,
            },
            orderBy: {
                receivedAt: "desc",
            },
        });
        res.status(200).json(messages);
    }
    catch (error) {
        console.error("Error fetching email messages:", error);
        res.status(500).json({ error: "Failed to fetch email messages" });
    }
};
exports.getEmailMessages = getEmailMessages;
// Get email folders for a specific account
const getEmailFolders = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { accountId } = req.params;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Verify the account belongs to the user
        const account = await prisma_js_1.prisma.emailAccount.findFirst({
            where: {
                id: Number(accountId),
                userId,
            },
        });
        if (!account) {
            res.status(404).json({ error: "Email account not found" });
            return;
        }
        // Get folders
        const folders = await prisma_js_1.prisma.emailFolder.findMany({
            where: {
                accountId: Number(accountId),
            },
        });
        // Count unread messages for each folder
        const foldersWithCounts = await Promise.all(folders.map(async (folder) => {
            const unreadCount = await prisma_js_1.prisma.emailMessage.count({
                where: {
                    folderId: folder.id,
                    status: "UNREAD",
                },
            });
            const totalCount = await prisma_js_1.prisma.emailMessage.count({
                where: {
                    folderId: folder.id,
                },
            });
            return {
                ...folder,
                unreadCount,
                totalCount,
            };
        }));
        res.status(200).json(foldersWithCounts);
    }
    catch (error) {
        console.error("Error fetching email folders:", error);
        res.status(500).json({ error: "Failed to fetch email folders" });
    }
};
exports.getEmailFolders = getEmailFolders;
// Send an email
const sendEmail = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { accountId, from, to, cc, bcc, subject, body, isHtml, priority } = req.body;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Validate required fields
        if (!accountId || !from || !to || !subject || !body) {
            res.status(400).json({ error: "Missing required fields" });
            return;
        }
        // Verify the account belongs to the user
        const account = await prisma_js_1.prisma.emailAccount.findFirst({
            where: {
                id: accountId,
                userId,
            },
        });
        if (!account) {
            res.status(404).json({ error: "Email account not found" });
            return;
        }
        // Find the sent folder
        const sentFolder = await prisma_js_1.prisma.emailFolder.findFirst({
            where: {
                accountId,
                type: "sent",
            },
        });
        if (!sentFolder) {
            res.status(404).json({ error: "Sent folder not found" });
            return;
        }
        // In a real implementation, we would use the email provider's API to send the email
        // For now, we'll just create a record in the database
        // Create the email message
        const message = await prisma_js_1.prisma.emailMessage.create({
            data: {
                accountId,
                folderId: sentFolder.id,
                from,
                to,
                cc: cc || [],
                bcc: bcc || [],
                subject,
                body,
                isHtml: isHtml || false,
                status: "READ", // Sent emails are always read
                priority: priority || "NORMAL",
                hasAttachments: false, // We're not handling attachments in this example
                receivedAt: new Date(),
            },
        });
        // In a real implementation, we would send the email here
        // For demonstration, we'll use our utility function
        try {
            await (0, sendEmail_js_1.sendEmail)(to.join(", "), subject, isHtml ? undefined : body, isHtml ? body : undefined);
        }
        catch (error) {
            console.error("Error sending email:", error);
            // We'll still return success since we created the record
        }
        res.status(201).json(message);
    }
    catch (error) {
        console.error("Error sending email:", error);
        res.status(500).json({ error: "Failed to send email" });
    }
};
exports.sendEmail = sendEmail;
// Update email status (read, unread, flagged, deleted)
const updateEmailStatus = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { messageId } = req.params;
        const { status } = req.body;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Validate status
        if (!["READ", "UNREAD", "FLAGGED", "DELETED"].includes(status.toUpperCase())) {
            res.status(400).json({ error: "Invalid status" });
            return;
        }
        // Find the message and verify it belongs to the user
        const message = await prisma_js_1.prisma.emailMessage.findFirst({
            where: {
                id: Number(messageId),
                account: {
                    userId,
                },
            },
            include: {
                account: true,
                folder: true,
            },
        });
        if (!message) {
            res.status(404).json({ error: "Message not found" });
            return;
        }
        // If status is DELETED, move to trash folder
        if (status.toUpperCase() === "DELETED") {
            const trashFolder = await prisma_js_1.prisma.emailFolder.findFirst({
                where: {
                    accountId: message.accountId,
                    type: "trash",
                },
            });
            if (!trashFolder) {
                res.status(404).json({ error: "Trash folder not found" });
                return;
            }
            // Update the message
            const updatedMessage = await prisma_js_1.prisma.emailMessage.update({
                where: {
                    id: Number(messageId),
                },
                data: {
                    folderId: trashFolder.id,
                    status: "READ", // Mark as read when moved to trash
                },
            });
            res.status(200).json(updatedMessage);
            return;
        }
        // Otherwise, just update the status
        const updatedMessage = await prisma_js_1.prisma.emailMessage.update({
            where: {
                id: Number(messageId),
            },
            data: {
                status: status.toUpperCase(),
            },
        });
        res.status(200).json(updatedMessage);
    }
    catch (error) {
        console.error("Error updating email status:", error);
        res.status(500).json({ error: "Failed to update email status" });
    }
};
exports.updateEmailStatus = updateEmailStatus;
//# sourceMappingURL=email.controller.js.map