{"version": 3, "sources": ["../../../../.pnpm/scule@1.3.0/node_modules/scule/dist/index.mjs"], "sourcesContent": ["const NUMBER_CHAR_RE = /\\d/;\nconst STR_SPLITTERS = [\"-\", \"_\", \"/\", \".\"];\nfunction isUppercase(char = \"\") {\n  if (NUMBER_CHAR_RE.test(char)) {\n    return void 0;\n  }\n  return char !== char.toLowerCase();\n}\nfunction splitByCase(str, separators) {\n  const splitters = separators ?? STR_SPLITTERS;\n  const parts = [];\n  if (!str || typeof str !== \"string\") {\n    return parts;\n  }\n  let buff = \"\";\n  let previousUpper;\n  let previousSplitter;\n  for (const char of str) {\n    const isSplitter = splitters.includes(char);\n    if (isSplitter === true) {\n      parts.push(buff);\n      buff = \"\";\n      previousUpper = void 0;\n      continue;\n    }\n    const isUpper = isUppercase(char);\n    if (previousSplitter === false) {\n      if (previousUpper === false && isUpper === true) {\n        parts.push(buff);\n        buff = char;\n        previousUpper = isUpper;\n        continue;\n      }\n      if (previousUpper === true && isUpper === false && buff.length > 1) {\n        const lastChar = buff.at(-1);\n        parts.push(buff.slice(0, Math.max(0, buff.length - 1)));\n        buff = lastChar + char;\n        previousUpper = isUpper;\n        continue;\n      }\n    }\n    buff += char;\n    previousUpper = isUpper;\n    previousSplitter = isSplitter;\n  }\n  parts.push(buff);\n  return parts;\n}\nfunction upperFirst(str) {\n  return str ? str[0].toUpperCase() + str.slice(1) : \"\";\n}\nfunction lowerFirst(str) {\n  return str ? str[0].toLowerCase() + str.slice(1) : \"\";\n}\nfunction pascalCase(str, opts) {\n  return str ? (Array.isArray(str) ? str : splitByCase(str)).map((p) => upperFirst(opts?.normalize ? p.toLowerCase() : p)).join(\"\") : \"\";\n}\nfunction camelCase(str, opts) {\n  return lowerFirst(pascalCase(str || \"\", opts));\n}\nfunction kebabCase(str, joiner) {\n  return str ? (Array.isArray(str) ? str : splitByCase(str)).map((p) => p.toLowerCase()).join(joiner ?? \"-\") : \"\";\n}\nfunction snakeCase(str) {\n  return kebabCase(str || \"\", \"_\");\n}\nfunction flatCase(str) {\n  return kebabCase(str || \"\", \"\");\n}\nfunction trainCase(str, opts) {\n  return (Array.isArray(str) ? str : splitByCase(str)).filter(Boolean).map((p) => upperFirst(opts?.normalize ? p.toLowerCase() : p)).join(\"-\");\n}\nconst titleCaseExceptions = /^(a|an|and|as|at|but|by|for|if|in|is|nor|of|on|or|the|to|with)$/i;\nfunction titleCase(str, opts) {\n  return (Array.isArray(str) ? str : splitByCase(str)).filter(Boolean).map(\n    (p) => titleCaseExceptions.test(p) ? p.toLowerCase() : upperFirst(opts?.normalize ? p.toLowerCase() : p)\n  ).join(\" \");\n}\n\nexport { camelCase, flatCase, isUppercase, kebabCase, lowerFirst, pascalCase, snakeCase, splitByCase, titleCase, trainCase, upperFirst };\n"], "mappings": ";;;AAAA,IAAM,iBAAiB;AACvB,IAAM,gBAAgB,CAAC,KAAK,KAAK,KAAK,GAAG;AACzC,SAAS,YAAY,OAAO,IAAI;AAC9B,MAAI,eAAe,KAAK,IAAI,GAAG;AAC7B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,KAAK,YAAY;AACnC;AACA,SAAS,YAAY,KAAK,YAAY;AACpC,QAAM,YAAY,cAAc;AAChC,QAAM,QAAQ,CAAC;AACf,MAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACnC,WAAO;AAAA,EACT;AACA,MAAI,OAAO;AACX,MAAI;AACJ,MAAI;AACJ,aAAW,QAAQ,KAAK;AACtB,UAAM,aAAa,UAAU,SAAS,IAAI;AAC1C,QAAI,eAAe,MAAM;AACvB,YAAM,KAAK,IAAI;AACf,aAAO;AACP,sBAAgB;AAChB;AAAA,IACF;AACA,UAAM,UAAU,YAAY,IAAI;AAChC,QAAI,qBAAqB,OAAO;AAC9B,UAAI,kBAAkB,SAAS,YAAY,MAAM;AAC/C,cAAM,KAAK,IAAI;AACf,eAAO;AACP,wBAAgB;AAChB;AAAA,MACF;AACA,UAAI,kBAAkB,QAAQ,YAAY,SAAS,KAAK,SAAS,GAAG;AAClE,cAAM,WAAW,KAAK,GAAG,EAAE;AAC3B,cAAM,KAAK,KAAK,MAAM,GAAG,KAAK,IAAI,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC;AACtD,eAAO,WAAW;AAClB,wBAAgB;AAChB;AAAA,MACF;AAAA,IACF;AACA,YAAQ;AACR,oBAAgB;AAChB,uBAAmB;AAAA,EACrB;AACA,QAAM,KAAK,IAAI;AACf,SAAO;AACT;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,MAAM,IAAI,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC,IAAI;AACrD;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,MAAM,IAAI,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC,IAAI;AACrD;AACA,SAAS,WAAW,KAAK,MAAM;AAC7B,SAAO,OAAO,MAAM,QAAQ,GAAG,IAAI,MAAM,YAAY,GAAG,GAAG,IAAI,CAAC,MAAM,WAAW,MAAM,YAAY,EAAE,YAAY,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI;AACtI;AACA,SAAS,UAAU,KAAK,MAAM;AAC5B,SAAO,WAAW,WAAW,OAAO,IAAI,IAAI,CAAC;AAC/C;AACA,SAAS,UAAU,KAAK,QAAQ;AAC9B,SAAO,OAAO,MAAM,QAAQ,GAAG,IAAI,MAAM,YAAY,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,EAAE,KAAK,UAAU,GAAG,IAAI;AAC/G;AACA,SAAS,UAAU,KAAK;AACtB,SAAO,UAAU,OAAO,IAAI,GAAG;AACjC;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,UAAU,OAAO,IAAI,EAAE;AAChC;AACA,SAAS,UAAU,KAAK,MAAM;AAC5B,UAAQ,MAAM,QAAQ,GAAG,IAAI,MAAM,YAAY,GAAG,GAAG,OAAO,OAAO,EAAE,IAAI,CAAC,MAAM,WAAW,MAAM,YAAY,EAAE,YAAY,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG;AAC7I;AACA,IAAM,sBAAsB;AAC5B,SAAS,UAAU,KAAK,MAAM;AAC5B,UAAQ,MAAM,QAAQ,GAAG,IAAI,MAAM,YAAY,GAAG,GAAG,OAAO,OAAO,EAAE;AAAA,IACnE,CAAC,MAAM,oBAAoB,KAAK,CAAC,IAAI,EAAE,YAAY,IAAI,WAAW,MAAM,YAAY,EAAE,YAAY,IAAI,CAAC;AAAA,EACzG,EAAE,KAAK,GAAG;AACZ;", "names": []}