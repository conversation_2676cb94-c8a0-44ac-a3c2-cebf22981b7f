"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.confirmEmail = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
/**
 * Confirm a user's email address
 */
const confirmEmail = async (req, res) => {
    try {
        const { token } = req.query;
        if (!token || typeof token !== "string") {
            const clientUrl = process.env.CLIENT_URL || "http://localhost:3000";
            res.redirect(`${clientUrl}/auth/confirm-email?success=false&error=${encodeURIComponent("Token is required")}`);
            return;
        }
        console.log(`Processing email confirmation with token: ${token}`);
        // Find the user by confirmation token
        const user = await prisma_js_1.prisma.user.findFirst({
            where: { emailConfirmationToken: token },
        });
        if (!user) {
            console.log(`No user found with token: ${token}`);
            const clientUrl = process.env.CLIENT_URL || "http://localhost:3000";
            res.redirect(`${clientUrl}/auth/confirm-email?success=false&error=${encodeURIComponent("Invalid confirmation token")}`);
            return;
        }
        // Check if already confirmed
        if (user.emailConfirmed) {
            console.log(`Email already confirmed for user: ${user.email}`);
            const clientUrl = process.env.CLIENT_URL || "http://localhost:3000";
            res.redirect(`${clientUrl}/auth/confirm-email?success=true&email=${encodeURIComponent(user.email)}&alreadyConfirmed=true`);
            return;
        }
        // Update the user to confirmed
        await prisma_js_1.prisma.user.update({
            where: { id: user.id },
            data: {
                emailConfirmed: true,
                emailConfirmationToken: null, // Clear the token after use
            },
        });
        console.log(`Email confirmed successfully for user: ${user.email}`);
        // Redirect to the frontend confirmation success page
        const clientUrl = process.env.CLIENT_URL || "http://localhost:3000";
        res.redirect(`${clientUrl}/auth/confirm-email?success=true&email=${encodeURIComponent(user.email)}`);
    }
    catch (error) {
        console.error("Error confirming email:", error);
        const clientUrl = process.env.CLIENT_URL || "http://localhost:3000";
        res.redirect(`${clientUrl}/auth/confirm-email?success=false&error=${encodeURIComponent(error.message)}`);
    }
};
exports.confirmEmail = confirmEmail;
//# sourceMappingURL=emailConfirmation.controller.js.map