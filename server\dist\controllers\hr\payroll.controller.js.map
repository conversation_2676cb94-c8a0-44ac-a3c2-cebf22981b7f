{"version": 3, "file": "payroll.controller.js", "sourceRoot": "", "sources": ["../../../controllers/hr/payroll.controller.ts"], "names": [], "mappings": ";;;AACA,mDAA6C;AAE7C,8DAA8D;AAC9D,SAAS,eAAe,CAAC,UAAkB,EAAE,OAAe;IAC1D,MAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IACtC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;IAC9B,OAAO,KAAK,CAAC;AACf,CAAC;AA4BM,MAAM,cAAc,GAAG,KAAK,EACjC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC5C,KAAK,EAAE;gBACL,QAAQ,EAAE;oBACR,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC;iBACvC;aACF;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QACH,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,8BAA8B,CAAC,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC,CAAC;AApBW,QAAA,cAAc,kBAoBzB;AAEK,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC5C,KAAK,EAAE;gBACL,QAAQ,EAAE;oBACR,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC;iBACvC;gBACD,MAAM,EAAE,WAAW,EAAE,yCAAyC;aAC/D;YACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC9B,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QACH,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iCAAiC,CAAC,CAAC,CAAC;IAChE,CAAC;AACH,CAAC,CAAC;AAtBW,QAAA,iBAAiB,qBAsB5B;AAEK,MAAM,cAAc,GAAG,KAAK,EACjC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE;YACzB,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC,CAAC;QAChE,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,gCAAgC,CAAC,CAAC,CAAC;IAC/D,CAAC;AACH,CAAC,CAAC;AAtBW,QAAA,cAAc,kBAsBzB;AAEK,MAAM,eAAe,GAAG,KAAK,EAClC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,GAAG,CAAC,IAAsB,CAAC;QAE3C,2BAA2B;QAC3B,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACtE,OAAO,IAAI,CACT,eAAe,CACb,GAAG,EACH,wDAAwD,CACzD,CACF,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,IAAI,EAAE;gBACJ,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBACtC,WAAW,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC1C,SAAS,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;gBACtC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,oDAAoD;gBACpF,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAG,OAAO,CAAC,MAAc,IAAI,OAAO,EAAE,mCAAmC;gBAC/E,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;gBACzB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE;aAC/C;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,4BAA4B,CAAC,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC;AAtCW,QAAA,eAAe,mBAsC1B;AAEK,MAAM,mBAAmB,GAAG,KAAK,EACtC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAE5B,sDAAsD;QACtD,IAAI,CAAC,CAAC,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACxE,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE;YACzB,IAAI,EAAE;gBACJ,MAAM,EAAE,MAAa,EAAE,6BAA6B;gBACpD,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;gBACzB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC,CAAC;QAChE,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iCAAiC,CAAC,CAAC,CAAC;IAChE,CAAC;AACH,CAAC,CAAC;AA9BW,QAAA,mBAAmB,uBA8B9B"}