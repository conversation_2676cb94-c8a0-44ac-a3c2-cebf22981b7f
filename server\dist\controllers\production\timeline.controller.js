"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteTimelineEvent = exports.updateTimelineEvent = exports.createTimelineEvent = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a timeline event
const createTimelineEvent = async (req, res) => {
    try {
        const { id: projectId } = req.params;
        const userId = Number(req.user?.id);
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Get user's company
        const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
            where: { userId },
            select: { companyId: true },
        });
        if (!userCompany) {
            return res
                .status(404)
                .json({ error: "User not associated with any company" });
        }
        // Check if project exists and belongs to user's company
        const project = await prisma_js_1.prisma.project.findFirst({
            where: {
                id: Number(projectId),
                companyId: Number(userCompany.companyId),
            },
        });
        if (!project) {
            return res.status(404).json({
                error: "Project not found or you do not have permission to add events to it",
            });
        }
        const { title, description, eventDate, endDate, type, status, color } = req.body;
        if (!title || !eventDate) {
            return res
                .status(400)
                .json({ error: "Title and event date are required" });
        }
        const timelineEvent = await prisma_js_1.prisma.projectTimelineEvent.create({
            data: {
                title,
                description,
                eventDate: new Date(eventDate),
                endDate: endDate ? new Date(endDate) : null,
                type: type || "MILESTONE",
                status: status || "UPCOMING",
                color,
                projectId: Number(projectId),
            },
        });
        return res.status(201).json(timelineEvent);
    }
    catch (error) {
        console.error("Error creating timeline event:", error);
        return res.status(500).json({ error: "Failed to create timeline event" });
    }
};
exports.createTimelineEvent = createTimelineEvent;
// Update a timeline event
const updateTimelineEvent = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = Number(req.user?.id);
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Get timeline event with project info
        const timelineEvent = await prisma_js_1.prisma.projectTimelineEvent.findUnique({
            where: { id: Number(id) },
            include: {
                project: {
                    select: {
                        companyId: true,
                    },
                },
            },
        });
        if (!timelineEvent) {
            return res.status(404).json({ error: "Timeline event not found" });
        }
        // Get user's company
        const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
            where: { userId },
            select: { companyId: true },
        });
        if (!userCompany ||
            Number(userCompany.companyId) !== timelineEvent.project.companyId) {
            return res.status(403).json({
                error: "You do not have permission to update this timeline event",
            });
        }
        const { title, description, eventDate, endDate, type, status, color } = req.body;
        const updatedEvent = await prisma_js_1.prisma.projectTimelineEvent.update({
            where: { id: Number(id) },
            data: {
                title: title !== undefined ? title : undefined,
                description: description !== undefined ? description : undefined,
                eventDate: eventDate !== undefined ? new Date(eventDate) : undefined,
                endDate: endDate !== undefined
                    ? endDate
                        ? new Date(endDate)
                        : null
                    : undefined,
                type: type !== undefined ? type : undefined,
                status: status !== undefined ? status : undefined,
                color: color !== undefined ? color : undefined,
            },
        });
        return res.status(200).json(updatedEvent);
    }
    catch (error) {
        console.error(`Error updating timeline event:`, error);
        return res.status(500).json({ error: "Failed to update timeline event" });
    }
};
exports.updateTimelineEvent = updateTimelineEvent;
// Delete a timeline event
const deleteTimelineEvent = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = Number(req.user?.id);
        if (!userId) {
            return res.status(401).json({ error: "Unauthorized" });
        }
        // Get timeline event with project info
        const timelineEvent = await prisma_js_1.prisma.projectTimelineEvent.findUnique({
            where: { id: Number(id) },
            include: {
                project: {
                    select: {
                        companyId: true,
                    },
                },
            },
        });
        if (!timelineEvent) {
            return res.status(404).json({ error: "Timeline event not found" });
        }
        // Get user's company
        const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
            where: { userId },
            select: { companyId: true },
        });
        if (!userCompany ||
            Number(userCompany.companyId) !== timelineEvent.project.companyId) {
            return res.status(403).json({
                error: "You do not have permission to delete this timeline event",
            });
        }
        await prisma_js_1.prisma.projectTimelineEvent.delete({
            where: { id: Number(id) },
        });
        return res
            .status(200)
            .json({ message: "Timeline event deleted successfully" });
    }
    catch (error) {
        console.error(`Error deleting timeline event:`, error);
        return res.status(500).json({ error: "Failed to delete timeline event" });
    }
};
exports.deleteTimelineEvent = deleteTimelineEvent;
//# sourceMappingURL=timeline.controller.js.map