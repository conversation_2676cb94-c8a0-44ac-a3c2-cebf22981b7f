"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteDocument = exports.getDocumentById = exports.uploadCompanyDocument = exports.getCompanyDocuments = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Use require instead of import to avoid TypeScript errors
const multer = require("multer");
const path = require("path");
const fs = require("fs");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(process.cwd(), "uploads/documents");
        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (_req, file, cb) => {
        const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
        const ext = path.extname(file.originalname);
        cb(null, uniqueSuffix + ext);
    },
});
const upload = multer({
    storage,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (_req, _file, cb) => {
        // Accept all file types for now
        // You can add file type restrictions here if needed
        cb(null, true);
    },
});
// Get all documents for a company
const getCompanyDocuments = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        // Validate required fields
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return next(createHttpError(404, "Company not found"));
        }
        // Get all documents for the company
        const documents = await prisma_js_1.prisma.document.findMany({
            where: {
                companyId: Number(companyId),
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(documents);
    }
    catch (error) {
        console.error("Error getting company documents:", error);
        next(createHttpError(500, "Failed to fetch company documents"));
    }
};
exports.getCompanyDocuments = getCompanyDocuments;
// Upload a document for a company
const uploadCompanyDocument = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        // Validate required fields
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return next(createHttpError(404, "Company not found"));
        }
        // Use multer to handle the file upload
        upload.single("file")(req, res, async (err) => {
            if (err) {
                console.error("Error uploading document:", err);
                return res.status(400).json({ error: err.message });
            }
            if (!req.file) {
                return res.status(400).json({ error: "No file uploaded" });
            }
            const { name, category, description } = req.body;
            if (!name) {
                return res.status(400).json({ error: "Document name is required" });
            }
            // Generate the URL for the uploaded file
            const baseUrl = process.env.SERVER_URL || "http://localhost:4004";
            const documentUrl = `${baseUrl}/uploads/documents/${req.file.filename}`;
            // Create a document record in the database
            const document = await prisma_js_1.prisma.document.create({
                data: {
                    name,
                    url: documentUrl,
                    type: "OTHER",
                    companyId: Number(companyId),
                    size: req.file.size,
                    mimeType: req.file.mimetype,
                    uploadedBy: req.user?.id || 0,
                    description,
                    module: req.body.module || "company",
                    category: category || "general",
                },
            });
            res.status(201).json(document);
        });
    }
    catch (error) {
        console.error("Error uploading company document:", error);
        next(createHttpError(500, "Failed to upload company document"));
    }
};
exports.uploadCompanyDocument = uploadCompanyDocument;
// Get a specific document
const getDocumentById = async (req, res, next) => {
    try {
        const { companyId, documentId } = req.params;
        // Validate required fields
        if (!companyId || !documentId) {
            return next(createHttpError(400, "Company ID and Document ID are required"));
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return next(createHttpError(404, "Company not found"));
        }
        // Get the document
        const document = await prisma_js_1.prisma.document.findUnique({
            where: {
                id: Number(documentId),
            },
        });
        if (!document || document.companyId !== Number(companyId)) {
            return next(createHttpError(404, "Document not found"));
        }
        res.json(document);
    }
    catch (error) {
        console.error("Error getting document:", error);
        next(createHttpError(500, "Failed to fetch document"));
    }
};
exports.getDocumentById = getDocumentById;
// Delete a document
const deleteDocument = async (req, res, next) => {
    try {
        const { companyId, documentId } = req.params;
        // Validate required fields
        if (!companyId || !documentId) {
            return next(createHttpError(400, "Company ID and Document ID are required"));
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return next(createHttpError(404, "Company not found"));
        }
        // Get the document
        const document = await prisma_js_1.prisma.document.findUnique({
            where: {
                id: Number(documentId),
            },
        });
        if (!document || document.companyId !== Number(companyId)) {
            return next(createHttpError(404, "Document not found"));
        }
        // Delete the file from the server
        if (document.url) {
            const baseUrl = process.env.SERVER_URL || "http://localhost:4004";
            const filePath = document.url
                .replace(baseUrl, "")
                .replace("/uploads", "uploads");
            try {
                if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath);
                }
            }
            catch (err) {
                console.error("Error deleting file:", err);
                // Continue with deletion even if file removal fails
            }
        }
        // Delete the document from the database
        await prisma_js_1.prisma.document.delete({
            where: {
                id: Number(documentId),
            },
        });
        res.status(204).send();
    }
    catch (error) {
        console.error("Error deleting document:", error);
        next(createHttpError(500, "Failed to delete document"));
    }
};
exports.deleteDocument = deleteDocument;
//# sourceMappingURL=documents.controller.js.map