"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.settings = exports.analytics = exports.quotations = exports.products = exports.campaigns = exports.activities = exports.deals = exports.leads = void 0;
const leads = __importStar(require("./leads.controller.js"));
exports.leads = leads;
const deals = __importStar(require("./deals.controller.js"));
exports.deals = deals;
const activities = __importStar(require("./activities.controller.js"));
exports.activities = activities;
const campaigns = __importStar(require("./campaigns.controller.js"));
exports.campaigns = campaigns;
const products = __importStar(require("./products.controller.js"));
exports.products = products;
const quotations = __importStar(require("./quotations.controller.js"));
exports.quotations = quotations;
const analytics = __importStar(require("./analytics.controller.js"));
exports.analytics = analytics;
const settings = __importStar(require("./settings.controller.js"));
exports.settings = settings;
//# sourceMappingURL=index.js.map