"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteProject = exports.updateProject = exports.getProjectById = exports.deleteTimelineEvent = exports.updateTimelineEvent = exports.createTimelineEvent = exports.getProjectTimeline = exports.deleteDocument = exports.uploadDocument = exports.getProjectDocuments = exports.getActiveProjects = exports.getAllProjects = exports.createProject = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
const createProject = async (req, res, next) => {
    const { name, description, companyId, clientId, products, stages, workers } = req.body;
    try {
        // Generate unique barcode and QR code
        // Create a simple function to generate random codes
        const generateCodes = () => {
            const randomString = Math.random().toString(36).substring(2, 10);
            return {
                barcode: `BAR-${randomString}`,
                qrCode: `QR-${randomString}`,
            };
        };
        const { barcode, qrCode } = generateCodes();
        // Create project
        const project = await prisma_js_1.prisma.project.create({
            data: {
                name,
                description,
                clientId: Number(clientId),
                companyId: Number(companyId),
                barcode,
                qrCode,
                status: "PLANNING", // Default status for new projects
                startDate: new Date(), // Add default start date
                projectLeadId: Number(req.user?.id) || 1, // Project leader
                budget: 0, // Add default budget
                assignedWorkers: {
                    connect: workers?.map((workerId) => ({ id: Number(workerId) })) || [], // Connect workers if provided
                },
                products: {
                    create: products || [],
                },
                stages: {
                    create: stages || [],
                },
            },
        });
        res.status(201).json(project);
    }
    catch (error) {
        if (error.code === "P2002" &&
            error.meta?.target &&
            (Array.isArray(error.meta.target)
                ? error.meta.target.includes("barcode")
                : error.meta.target === "barcode")) {
            console.warn("Barcode collision detected, retrying...");
            // Implement retry logic if necessary
        }
        console.error("Error creating project:", error);
        next(createHttpError(500, "Internal Server Error"));
    }
};
exports.createProject = createProject;
const getAllProjects = async (req, res, next) => {
    try {
        const projects = await prisma_js_1.prisma.project.findMany({
            where: {
                companyId: req.user?.companyId || undefined,
            },
            include: {
                products: true,
                stages: true,
                client: true,
                projectLead: true,
                documents: true,
                qcChecklists: true,
            },
        });
        res.json(projects);
    }
    catch (error) {
        console.error("Error fetching projects:", error);
        next(createHttpError(500, "Internal Server Error"));
    }
};
exports.getAllProjects = getAllProjects;
const getActiveProjects = async (req, res, next) => {
    try {
        const projects = await prisma_js_1.prisma.project.findMany({
            where: {
                companyId: req.user?.companyId || undefined,
                status: "IN_PROGRESS", // Only get active projects with correct enum value
            },
            include: {
                products: true,
                stages: true,
                client: true,
                projectLead: true,
                documents: true,
                qcChecklists: true,
            },
        });
        res.json(projects);
    }
    catch (error) {
        console.error("Error fetching active projects:", error);
        next(createHttpError(500, "Internal Server Error"));
    }
};
exports.getActiveProjects = getActiveProjects;
const getProjectDocuments = async (req, res, next) => {
    const { projectId } = req.params;
    try {
        const documents = await prisma_js_1.prisma.document.findMany({
            where: {
                projectId: Number(projectId),
                companyId: req.user?.companyId || undefined,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(documents);
    }
    catch (error) {
        console.error("Error fetching project documents:", error);
        next(createHttpError(500, "Internal Server Error"));
    }
};
exports.getProjectDocuments = getProjectDocuments;
const uploadDocument = async (req, res, next) => {
    const { projectId } = req.params;
    try {
        // TODO: Implement file upload logic here
        // This is a placeholder implementation
        const document = await prisma_js_1.prisma.document.create({
            data: {
                projectId: Number(projectId),
                companyId: req.user?.companyId || 1,
                name: req.body.name,
                url: req.body.url,
                type: req.body.type,
                uploadedBy: req.user?.id || 1, // Required field
            },
        });
        res.status(201).json(document);
    }
    catch (error) {
        console.error("Error uploading document:", error);
        next(createHttpError(500, "Failed to upload document"));
    }
};
exports.uploadDocument = uploadDocument;
// Delete document
const deleteDocument = async (req, res, next) => {
    const { documentId } = req.params;
    try {
        // Check if document exists
        const document = await prisma_js_1.prisma.document.findUnique({
            where: {
                id: Number(documentId),
            },
        });
        if (!document) {
            return next(createHttpError(404, "Document not found"));
        }
        // Check if user has permission to delete the document
        if (document.companyId !== req.user?.companyId) {
            return next(createHttpError(403, "You don't have permission to delete this document"));
        }
        // Delete document
        await prisma_js_1.prisma.document.delete({
            where: {
                id: Number(documentId),
            },
        });
        res.json({ message: "Document deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting document:", error);
        next(error);
    }
};
exports.deleteDocument = deleteDocument;
// Get project timeline
const getProjectTimeline = async (req, res, next) => {
    const { projectId } = req.params;
    try {
        // Get project details
        const project = await prisma_js_1.prisma.project.findUnique({
            where: {
                id: Number(projectId),
            },
            select: {
                id: true,
                name: true,
                status: true,
                startDate: true,
                endDate: true,
            },
        });
        if (!project) {
            return next(createHttpError(404, "Project not found"));
        }
        // Get timeline events
        const timelineEvents = await prisma_js_1.prisma.projectTimelineEvent.findMany({
            where: {
                projectId: Number(projectId),
            },
            orderBy: {
                eventDate: "asc",
            },
        });
        // Get tasks for timeline
        const tasks = await prisma_js_1.prisma.task.findMany({
            where: {
                projectId: Number(projectId),
            },
            select: {
                id: true,
                name: true,
                status: true,
                startDate: true,
                endDate: true,
                // dueDate field doesn't exist in the schema
                // dueDate: true,
            },
            orderBy: {
                startDate: "asc",
            },
        });
        res.json({
            project,
            timelineEvents,
            tasks,
        });
    }
    catch (error) {
        console.error("Error fetching project timeline:", error);
        next(error);
    }
};
exports.getProjectTimeline = getProjectTimeline;
// Create timeline event
const createTimelineEvent = async (req, res, next) => {
    const { projectId } = req.params;
    const { title, description, eventDate, endDate, type, status, color } = req.body;
    try {
        const timelineEvent = await prisma_js_1.prisma.projectTimelineEvent.create({
            data: {
                projectId: Number(projectId),
                title,
                description,
                eventDate: new Date(eventDate),
                endDate: endDate ? new Date(endDate) : undefined,
                type,
                status,
                color,
            },
        });
        res.status(201).json(timelineEvent);
    }
    catch (error) {
        console.error("Error creating timeline event:", error);
        next(error);
    }
};
exports.createTimelineEvent = createTimelineEvent;
// Update timeline event
const updateTimelineEvent = async (req, res, next) => {
    const { eventId } = req.params;
    const { title, description, eventDate, endDate, type, status, color } = req.body;
    try {
        const timelineEvent = await prisma_js_1.prisma.projectTimelineEvent.update({
            where: {
                id: Number(eventId),
            },
            data: {
                title,
                description,
                eventDate: eventDate ? new Date(eventDate) : undefined,
                endDate: endDate ? new Date(endDate) : undefined,
                type,
                status,
                color,
            },
        });
        res.json(timelineEvent);
    }
    catch (error) {
        console.error("Error updating timeline event:", error);
        next(error);
    }
};
exports.updateTimelineEvent = updateTimelineEvent;
// Delete timeline event
const deleteTimelineEvent = async (req, res, next) => {
    const { eventId } = req.params;
    try {
        await prisma_js_1.prisma.projectTimelineEvent.delete({
            where: {
                id: Number(eventId),
            },
        });
        res.json({ message: "Timeline event deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting timeline event:", error);
        next(error);
    }
};
exports.deleteTimelineEvent = deleteTimelineEvent;
// Get project by ID
const getProjectById = async (req, res, next) => {
    const { id } = req.params;
    try {
        // Check if project exists
        const project = await prisma_js_1.prisma.project.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                client: true,
                products: true,
                stages: true,
                // workers field doesn't exist in the schema, use Worker instead
                Worker: true,
            },
        });
        if (!project) {
            return next(createHttpError(404, "Project not found"));
        }
        // Check if user has permission to view the project
        if (project.companyId !== req.user?.companyId) {
            return next(createHttpError(403, "You don't have permission to view this project"));
        }
        res.json(project);
    }
    catch (error) {
        console.error("Error fetching project:", error);
        next(error);
    }
};
exports.getProjectById = getProjectById;
// Update project
const updateProject = async (req, res, next) => {
    const { id } = req.params;
    const { name, description, clientId } = req.body; // Only using these fields for update
    try {
        // Check if project exists
        const project = await prisma_js_1.prisma.project.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!project) {
            return next(createHttpError(404, "Project not found"));
        }
        // Check if user has permission to update the project
        if (project.companyId !== req.user?.companyId) {
            return next(createHttpError(403, "You don't have permission to update this project"));
        }
        // Update project
        const updatedProject = await prisma_js_1.prisma.project.update({
            where: {
                id: Number(id),
            },
            data: {
                name,
                description,
                clientId: clientId ? Number(clientId) : undefined,
            },
            include: {
                client: true,
                products: true,
                stages: true,
                // workers field doesn't exist in the schema, use Worker instead
                Worker: true,
            },
        });
        res.json(updatedProject);
    }
    catch (error) {
        console.error("Error updating project:", error);
        next(error);
    }
};
exports.updateProject = updateProject;
// Delete project
const deleteProject = async (req, res, next) => {
    const { id } = req.params;
    try {
        // Check if project exists
        const project = await prisma_js_1.prisma.project.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!project) {
            return next(createHttpError(404, "Project not found"));
        }
        // Check if user has permission to delete the project
        if (project.companyId !== Number(req.user?.companyId)) {
            return next(createHttpError(403, "You don't have permission to delete this project"));
        }
        // Delete project
        await prisma_js_1.prisma.project.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Project deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting project:", error);
        next(error);
    }
};
exports.deleteProject = deleteProject;
//# sourceMappingURL=project.controller.js.map