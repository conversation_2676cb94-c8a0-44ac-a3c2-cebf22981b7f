"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkVerificationStatus = checkVerificationStatus;
exports.resendVerificationEmail = resendVerificationEmail;
const client_1 = require("@prisma/client");
const crypto = __importStar(require("crypto"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const sendEmail_1 = require("../../utils/sendEmail");
// In CommonJS, __dirname is already defined, so we don't need to redefine it
// We'll use path.resolve() directly where needed
/**
 * Check if a user's email is verified
 */
async function checkVerificationStatus(req, res) {
    try {
        const { userId } = req.params;
        if (!userId) {
            return res.status(400).json({ message: "User ID is required" });
        }
        const db = new client_1.PrismaClient();
        const user = await db.user.findUnique({
            where: { id: parseInt(userId, 10) },
        });
        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }
        return res.status(200).json({ verified: user.emailConfirmed });
    }
    catch (error) {
        console.error("Error checking verification status:", error);
        return res
            .status(500)
            .json({ message: "Server error", error: error.message });
    }
}
/**
 * Resend verification email to user
 */
async function resendVerificationEmail(req, res) {
    try {
        const { email, userId } = req.body;
        if (!email || !userId) {
            return res
                .status(400)
                .json({ message: "Email and user ID are required" });
        }
        const db = new client_1.PrismaClient();
        const user = await db.user.findUnique({
            where: { id: parseInt(userId, 10) },
        });
        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }
        if (user.emailConfirmed) {
            return res.status(400).json({ message: "Email is already verified" });
        }
        // Generate new confirmation token
        const emailConfirmationToken = crypto.randomBytes(32).toString("hex");
        // Update user with new token
        await db.user.update({
            where: { id: user.id },
            data: { emailConfirmationToken },
        });
        // Send confirmation email
        const SERVER_URL = process.env.SERVER_URL || "http://localhost:4004";
        const confirmationLink = `${SERVER_URL}/api/v1/email/confirm-email?token=${emailConfirmationToken}`;
        const emailSubject = "Confirm Your CoManager Account";
        // Find logo path
        const logoPath = path.join(__dirname, "../../public/logo.png");
        // Email template
        const emailBody = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Confirm Your Email</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 0; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { text-align: center; padding: 20px 0; }
          .logo { max-width: 150px; }
          .content { padding: 20px 0; }
          .button { background-color: #00b8db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 20px 0; }
          .footer { text-align: center; padding: 20px 0; font-size: 12px; color: #666; }
          .expiry { font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="center">
        <div class="container">
          <div class="header">
            <div style="text-align: center;">
              <img src="cid:<EMAIL>" alt="CoManager Logo" class="logo" style="max-width: 150px;">
            </div>
            <h1>Confirm Your Email Address</h1>
          </div>
          <div class="content">
            <p>Thank you for registering with CoManager!</p>
            <p>To complete your registration and activate your account, please confirm your email address by clicking the button below:</p>
            <a href="${confirmationLink}" class="button" style="background-color:#00b8db;border-radius:6px;color:#ffffff;display:inline-block;font-family:Arial, sans-serif;font-size:16px;font-weight:bold;line-height:40px;text-align:center;text-decoration:none;width:200px;-webkit-text-size-adjust:none;">Confirm Email</a>
            <p class="expiry">This confirmation link will expire in 24 hours.</p>
            <p>If you didn't create an account with CoManager, you can safely ignore this email.</p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} CoManager. All rights reserved.</p>
          </div>
        </div>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `;
        // Send email
        if (fs.existsSync(logoPath)) {
            await (0, sendEmail_1.sendEmail)(user.email, emailSubject, null, emailBody, [
                {
                    filename: "logo.png",
                    path: logoPath,
                    cid: "<EMAIL>",
                },
            ]);
        }
        else {
            await (0, sendEmail_1.sendEmail)(user.email, emailSubject, null, emailBody);
        }
        return res
            .status(200)
            .json({ message: "Verification email resent successfully" });
    }
    catch (error) {
        console.error("Error resending verification email:", error);
        return res
            .status(500)
            .json({ message: "Server error", error: error.message });
    }
}
//# sourceMappingURL=email-verification.controller.js.map