"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bigIntReplacer = bigIntReplacer;
function bigIntReplacer(key, value) {
    if (typeof value === 'bigint') {
        return value.toString();
    }
    else if (Array.isArray(value)) {
        return value.map((item) => bigIntReplacer(key, item));
    }
    else if (typeof value === 'object' && value !== null) {
        const replacedObject = {};
        for (const k in value) {
            replacedObject[k] = bigIntReplacer(k, value[k]);
        }
        return replacedObject;
    }
    return value;
}
//# sourceMappingURL=bigIntReplacer.js.map