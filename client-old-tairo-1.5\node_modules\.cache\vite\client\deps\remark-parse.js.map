{"version": 3, "sources": ["../../../../.pnpm/ms@2.1.3/node_modules/ms/index.js", "../../../../.pnpm/debug@4.4.0_supports-color@9.4.0/node_modules/debug/src/common.js", "../../../../.pnpm/debug@4.4.0_supports-color@9.4.0/node_modules/debug/src/browser.js", "../../../../.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js", "../../../../.pnpm/mdast-util-to-string@4.0.0/node_modules/mdast-util-to-string/lib/index.js", "../../../../.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js", "../../../../.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/constants.js", "../../../../.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js", "../../../../.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/values.js", "../../../../.pnpm/micromark-util-chunked@2.0.1/node_modules/micromark-util-chunked/dev/index.js", "../../../../.pnpm/micromark-util-combine-extensions@2.0.1/node_modules/micromark-util-combine-extensions/index.js", "../../../../.pnpm/micromark-util-decode-numeric-character-reference@2.0.2/node_modules/micromark-util-decode-numeric-character-reference/dev/index.js", "../../../../.pnpm/micromark-util-normalize-identifier@2.0.1/node_modules/micromark-util-normalize-identifier/dev/index.js", "../../../../.pnpm/micromark-util-character@2.1.1/node_modules/micromark-util-character/dev/index.js", "../../../../.pnpm/micromark@4.0.2/node_modules/micromark/dev/lib/compile.js", "../../../../.pnpm/micromark-factory-space@2.0.1/node_modules/micromark-factory-space/dev/index.js", "../../../../.pnpm/micromark@4.0.2/node_modules/micromark/dev/lib/initialize/content.js", "../../../../.pnpm/micromark@4.0.2/node_modules/micromark/dev/lib/initialize/document.js", "../../../../.pnpm/micromark-util-classify-character@2.0.1/node_modules/micromark-util-classify-character/dev/index.js", "../../../../.pnpm/micromark-util-resolve-all@2.0.1/node_modules/micromark-util-resolve-all/index.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/attention.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/autolink.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/blank-line.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/block-quote.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/character-escape.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/character-reference.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/code-fenced.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/code-indented.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/code-text.js", "../../../../.pnpm/micromark-util-subtokenize@2.1.0/node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js", "../../../../.pnpm/micromark-util-subtokenize@2.1.0/node_modules/micromark-util-subtokenize/dev/index.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/content.js", "../../../../.pnpm/micromark-factory-destination@2.0.1/node_modules/micromark-factory-destination/dev/index.js", "../../../../.pnpm/micromark-factory-label@2.0.1/node_modules/micromark-factory-label/dev/index.js", "../../../../.pnpm/micromark-factory-title@2.0.1/node_modules/micromark-factory-title/dev/index.js", "../../../../.pnpm/micromark-factory-whitespace@2.0.1/node_modules/micromark-factory-whitespace/dev/index.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/definition.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/heading-atx.js", "../../../../.pnpm/micromark-util-html-tag-name@2.0.1/node_modules/micromark-util-html-tag-name/index.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/html-flow.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/html-text.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/label-end.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/label-start-image.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/label-start-link.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/line-ending.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/thematic-break.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/list.js", "../../../../.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/setext-underline.js", "../../../../.pnpm/micromark@4.0.2/node_modules/micromark/dev/lib/initialize/flow.js", "../../../../.pnpm/micromark@4.0.2/node_modules/micromark/dev/lib/initialize/text.js", "../../../../.pnpm/micromark@4.0.2/node_modules/micromark/dev/lib/constructs.js", "../../../../.pnpm/micromark@4.0.2/node_modules/micromark/dev/lib/create-tokenizer.js", "../../../../.pnpm/micromark@4.0.2/node_modules/micromark/dev/lib/parse.js", "../../../../.pnpm/micromark@4.0.2/node_modules/micromark/dev/lib/postprocess.js", "../../../../.pnpm/micromark@4.0.2/node_modules/micromark/dev/lib/preprocess.js", "../../../../.pnpm/micromark-util-decode-string@2.0.1/node_modules/micromark-util-decode-string/dev/index.js", "../../../../.pnpm/unist-util-stringify-position@4.0.0/node_modules/unist-util-stringify-position/lib/index.js", "../../../../.pnpm/mdast-util-from-markdown@2.0.2/node_modules/mdast-util-from-markdown/dev/lib/index.js", "../../../../.pnpm/remark-parse@11.0.0/node_modules/remark-parse/lib/index.js"], "sourcesContent": ["/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '')\n\t\t\t.trim()\n\t\t\t.replace(' ', ',')\n\t\t\t.split(',')\n\t\t\t.filter(Boolean);\n\n\t\tfor (const ns of split) {\n\t\t\tif (ns[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(ns.slice(1));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(ns);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Checks if the given string matches a namespace template, honoring\n\t * asterisks as wildcards.\n\t *\n\t * @param {String} search\n\t * @param {String} template\n\t * @return {Boolean}\n\t */\n\tfunction matchesTemplate(search, template) {\n\t\tlet searchIndex = 0;\n\t\tlet templateIndex = 0;\n\t\tlet starIndex = -1;\n\t\tlet matchIndex = 0;\n\n\t\twhile (searchIndex < search.length) {\n\t\t\tif (templateIndex < template.length && (template[templateIndex] === search[searchIndex] || template[templateIndex] === '*')) {\n\t\t\t\t// Match character or proceed with wildcard\n\t\t\t\tif (template[templateIndex] === '*') {\n\t\t\t\t\tstarIndex = templateIndex;\n\t\t\t\t\tmatchIndex = searchIndex;\n\t\t\t\t\ttemplateIndex++; // Skip the '*'\n\t\t\t\t} else {\n\t\t\t\t\tsearchIndex++;\n\t\t\t\t\ttemplateIndex++;\n\t\t\t\t}\n\t\t\t} else if (starIndex !== -1) { // eslint-disable-line no-negated-condition\n\t\t\t\t// Backtrack to the last '*' and try to match more characters\n\t\t\t\ttemplateIndex = starIndex + 1;\n\t\t\t\tmatchIndex++;\n\t\t\t\tsearchIndex = matchIndex;\n\t\t\t} else {\n\t\t\t\treturn false; // No match\n\t\t\t}\n\t\t}\n\n\t\t// Handle trailing '*' in template\n\t\twhile (templateIndex < template.length && template[templateIndex] === '*') {\n\t\t\ttemplateIndex++;\n\t\t}\n\n\t\treturn templateIndex === template.length;\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names,\n\t\t\t...createDebug.skips.map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tfor (const skip of createDebug.skips) {\n\t\t\tif (matchesTemplate(name, skip)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (const ns of createDebug.names) {\n\t\t\tif (matchesTemplate(name, ns)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\tlet m;\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t// eslint-disable-next-line no-return-assign\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n", "import {dequal} from 'dequal'\n\n/**\n * @type {Set<string>}\n */\nconst codesWarned = new Set()\n\nclass AssertionError extends Error {\n  name = /** @type {const} */ ('Assertion')\n  code = /** @type {const} */ ('ERR_ASSERTION')\n\n  /**\n   * Create an assertion error.\n   *\n   * @param {string} message\n   *   Message explaining error.\n   * @param {unknown} actual\n   *   Value.\n   * @param {unknown} expected\n   *   Baseline.\n   * @param {string} operator\n   *   Name of equality operation.\n   * @param {boolean} generated\n   *   Whether `message` is a custom message or not\n   * @returns\n   *   Instance.\n   */\n  // eslint-disable-next-line max-params\n  constructor(message, actual, expected, operator, generated) {\n    super(message)\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor)\n    }\n\n    /**\n     * @type {unknown}\n     */\n    this.actual = actual\n\n    /**\n     * @type {unknown}\n     */\n    this.expected = expected\n\n    /**\n     * @type {boolean}\n     */\n    this.generated = generated\n\n    /**\n     * @type {string}\n     */\n    this.operator = operator\n  }\n}\n\nclass DeprecationE<PERSON>r extends Error {\n  name = /** @type {const} */ ('DeprecationWarning')\n\n  /**\n   * Create a deprecation message.\n   *\n   * @param {string} message\n   *   Message explaining deprecation.\n   * @param {string | undefined} code\n   *   Deprecation identifier; deprecation messages will be generated only once per code.\n   * @returns\n   *   Instance.\n   */\n  constructor(message, code) {\n    super(message)\n\n    /**\n     * @type {string | undefined}\n     */\n    this.code = code\n  }\n}\n\n/**\n * Wrap a function or class to show a deprecation message when first called.\n *\n * > 👉 **Important**: only shows a message when the `development` condition is\n * > used, does nothing in production.\n *\n * When the resulting wrapped `fn` is called, emits a warning once to\n * `console.error` (`stderr`).\n * If a code is given, one warning message will be emitted in total per code.\n *\n * @template {Function} T\n *   Function or class kind.\n * @param {T} fn\n *   Function or class.\n * @param {string} message\n *   Message explaining deprecation.\n * @param {string | null | undefined} [code]\n *   Deprecation identifier (optional); deprecation messages will be generated\n *   only once per code.\n * @returns {T}\n *   Wrapped `fn`.\n */\nexport function deprecate(fn, message, code) {\n  let warned = false\n\n  // The wrapper will keep the same prototype as fn to maintain prototype chain\n  Object.setPrototypeOf(deprecated, fn)\n\n  // @ts-expect-error: it’s perfect, typescript…\n  return deprecated\n\n  /**\n   * @this {unknown}\n   * @param  {...Array<unknown>} args\n   * @returns {unknown}\n   */\n  function deprecated(...args) {\n    if (!warned) {\n      warned = true\n\n      if (typeof code === 'string' && codesWarned.has(code)) {\n        // Empty.\n      } else {\n        console.error(new DeprecationError(message, code || undefined))\n\n        if (typeof code === 'string') codesWarned.add(code)\n      }\n    }\n\n    return new.target\n      ? Reflect.construct(fn, args, new.target)\n      : Reflect.apply(fn, this, args)\n  }\n}\n\n/**\n * Assert deep strict equivalence.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @template {unknown} T\n *   Expected kind.\n * @param {unknown} actual\n *   Value.\n * @param {T} expected\n *   Baseline.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected values to be deeply equal'`).\n * @returns {asserts actual is T}\n *   Nothing; throws when `actual` is not deep strict equal to `expected`.\n * @throws {AssertionError}\n *   Throws when `actual` is not deep strict equal to `expected`.\n */\nexport function equal(actual, expected, message) {\n  assert(\n    dequal(actual, expected),\n    actual,\n    expected,\n    'equal',\n    'Expected values to be deeply equal',\n    message\n  )\n}\n\n/**\n * Assert if `value` is truthy.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {unknown} value\n *   Value to assert.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected value to be truthy'`).\n * @returns {asserts value}\n *   Nothing; throws when `value` is falsey.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function ok(value, message) {\n  assert(\n    Boolean(value),\n    false,\n    true,\n    'ok',\n    'Expected value to be truthy',\n    message\n  )\n}\n\n/**\n * Assert that a code path never happens.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Unreachable'`).\n * @returns {never}\n *   Nothing; always throws.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function unreachable(message) {\n  assert(false, false, true, 'ok', 'Unreachable', message)\n}\n\n/**\n * @param {boolean} bool\n *   Whether to skip this operation.\n * @param {unknown} actual\n *   Actual value.\n * @param {unknown} expected\n *   Expected value.\n * @param {string} operator\n *   Operator.\n * @param {string} defaultMessage\n *   Default message for operation.\n * @param {Error | string | null | undefined} userMessage\n *   User-provided message.\n * @returns {asserts bool}\n *   Nothing; throws when falsey.\n */\n// eslint-disable-next-line max-params\nfunction assert(bool, actual, expected, operator, defaultMessage, userMessage) {\n  if (!bool) {\n    throw userMessage instanceof Error\n      ? userMessage\n      : new AssertionError(\n          userMessage || defaultMessage,\n          actual,\n          expected,\n          operator,\n          !userMessage\n        )\n  }\n}\n", "/**\n * @typedef {import('mdast').Nodes} Nodes\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s (default: `true`).\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML (default: `true`).\n */\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} [value]\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */\nexport function toString(value, options) {\n  const settings = options || emptyOptions\n  const includeImageAlt =\n    typeof settings.includeImageAlt === 'boolean'\n      ? settings.includeImageAlt\n      : true\n  const includeHtml =\n    typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true\n\n  return one(value, includeImageAlt, includeHtml)\n}\n\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(value, includeImageAlt, includeHtml) {\n  if (node(value)) {\n    if ('value' in value) {\n      return value.type === 'html' && !includeHtml ? '' : value.value\n    }\n\n    if (includeImageAlt && 'alt' in value && value.alt) {\n      return value.alt\n    }\n\n    if ('children' in value) {\n      return all(value.children, includeImageAlt, includeHtml)\n    }\n  }\n\n  if (Array.isArray(value)) {\n    return all(value, includeImageAlt, includeHtml)\n  }\n\n  return ''\n}\n\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */\nfunction all(values, includeImageAlt, includeHtml) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n\n  while (++index < values.length) {\n    result[index] = one(values[index], includeImageAlt, includeHtml)\n  }\n\n  return result.join('')\n}\n\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Nodes}\n *   Whether `value` is a node.\n */\nfunction node(value) {\n  return Boolean(value && typeof value === 'object')\n}\n", "/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nexport const codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65_279,\n  // Unicode Specials block.\n  replacementCharacter: 65_533 // `�`\n})\n", "/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nexport const constants = /** @type {const} */ ({\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeContent: 'content',\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlBasic: 6, // Symbol for `<div`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  htmlRaw: 1, // Symbol for `<script>`\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10_000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n", "/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nexport const types = /** @type {const} */ ({\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n\n  codeTextData: 'codeTextData',\n\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n\n  htmlFlowData: 'htmlFlowData',\n\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n\n  htmlTextData: 'htmlTextData',\n\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n})\n", "/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nexport const values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n", "import {constants} from 'micromark-util-symbol'\n\n/**\n * Like `Array#splice`, but smarter for giant arrays.\n *\n * `Array#splice` takes all items to be inserted as individual argument which\n * causes a stack overflow in V8 when trying to insert 100k items for instance.\n *\n * Otherwise, this does not return the removed items, and takes `items` as an\n * array instead of rest parameters.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {number} start\n *   Index to remove/insert at (can be negative).\n * @param {number} remove\n *   Number of items to remove.\n * @param {Array<T>} items\n *   Items to inject into `list`.\n * @returns {undefined}\n *   Nothing.\n */\nexport function splice(list, start, remove, items) {\n  const end = list.length\n  let chunkStart = 0\n  /** @type {Array<unknown>} */\n  let parameters\n\n  // Make start between zero and `end` (included).\n  if (start < 0) {\n    start = -start > end ? 0 : end + start\n  } else {\n    start = start > end ? end : start\n  }\n\n  remove = remove > 0 ? remove : 0\n\n  // No need to chunk the items if there’s only a couple (10k) items.\n  if (items.length < constants.v8MaxSafeChunkSize) {\n    parameters = Array.from(items)\n    parameters.unshift(start, remove)\n    // @ts-expect-error Hush, it’s fine.\n    list.splice(...parameters)\n  } else {\n    // Delete `remove` items starting from `start`\n    if (remove) list.splice(start, remove)\n\n    // Insert the items in chunks to not cause stack overflows.\n    while (chunkStart < items.length) {\n      parameters = items.slice(\n        chunkStart,\n        chunkStart + constants.v8MaxSafeChunkSize\n      )\n      parameters.unshift(start, 0)\n      // @ts-expect-error Hush, it’s fine.\n      list.splice(...parameters)\n\n      chunkStart += constants.v8MaxSafeChunkSize\n      start += constants.v8MaxSafeChunkSize\n    }\n  }\n}\n\n/**\n * Append `items` (an array) at the end of `list` (another array).\n * When `list` was empty, returns `items` instead.\n *\n * This prevents a potentially expensive operation when `list` is empty,\n * and adds items in batches to prevent V8 from hanging.\n *\n * @template {unknown} T\n *   Item type.\n * @param {Array<T>} list\n *   List to operate on.\n * @param {Array<T>} items\n *   Items to add to `list`.\n * @returns {Array<T>}\n *   Either `list` or `items`.\n */\nexport function push(list, items) {\n  if (list.length > 0) {\n    splice(list, list.length, 0, items)\n    return list\n  }\n\n  return items\n}\n", "/**\n * @import {\n *   Extension,\n *   Handles,\n *   HtmlExtension,\n *   NormalizedExtension\n * } from 'micromark-util-types'\n */\n\nimport {splice} from 'micromark-util-chunked'\n\nconst hasOwnProperty = {}.hasOwnProperty\n\n/**\n * Combine multiple syntax extensions into one.\n *\n * @param {ReadonlyArray<Extension>} extensions\n *   List of syntax extensions.\n * @returns {NormalizedExtension}\n *   A single combined extension.\n */\nexport function combineExtensions(extensions) {\n  /** @type {NormalizedExtension} */\n  const all = {}\n  let index = -1\n\n  while (++index < extensions.length) {\n    syntaxExtension(all, extensions[index])\n  }\n\n  return all\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {NormalizedExtension} all\n *   Extension to merge into.\n * @param {Extension} extension\n *   Extension to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction syntaxExtension(all, extension) {\n  /** @type {keyof Extension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    /** @type {Record<string, unknown>} */\n    const left = maybe || (all[hook] = {})\n    /** @type {Record<string, unknown> | undefined} */\n    const right = extension[hook]\n    /** @type {string} */\n    let code\n\n    if (right) {\n      for (code in right) {\n        if (!hasOwnProperty.call(left, code)) left[code] = []\n        const value = right[code]\n        constructs(\n          // @ts-expect-error Looks like a list.\n          left[code],\n          Array.isArray(value) ? value : value ? [value] : []\n        )\n      }\n    }\n  }\n}\n\n/**\n * Merge `list` into `existing` (both lists of constructs).\n * Mutates `existing`.\n *\n * @param {Array<unknown>} existing\n *   List of constructs to merge into.\n * @param {Array<unknown>} list\n *   List of constructs to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction constructs(existing, list) {\n  let index = -1\n  /** @type {Array<unknown>} */\n  const before = []\n\n  while (++index < list.length) {\n    // @ts-expect-error Looks like an object.\n    ;(list[index].add === 'after' ? existing : before).push(list[index])\n  }\n\n  splice(existing, 0, 0, before)\n}\n\n/**\n * Combine multiple HTML extensions into one.\n *\n * @param {ReadonlyArray<HtmlExtension>} htmlExtensions\n *   List of HTML extensions.\n * @returns {HtmlExtension}\n *   Single combined HTML extension.\n */\nexport function combineHtmlExtensions(htmlExtensions) {\n  /** @type {HtmlExtension} */\n  const handlers = {}\n  let index = -1\n\n  while (++index < htmlExtensions.length) {\n    htmlExtension(handlers, htmlExtensions[index])\n  }\n\n  return handlers\n}\n\n/**\n * Merge `extension` into `all`.\n *\n * @param {HtmlExtension} all\n *   Extension to merge into.\n * @param {HtmlExtension} extension\n *   Extension to merge.\n * @returns {undefined}\n *   Nothing.\n */\nfunction htmlExtension(all, extension) {\n  /** @type {keyof HtmlExtension} */\n  let hook\n\n  for (hook in extension) {\n    const maybe = hasOwnProperty.call(all, hook) ? all[hook] : undefined\n    const left = maybe || (all[hook] = {})\n    const right = extension[hook]\n    /** @type {keyof Handles} */\n    let type\n\n    if (right) {\n      for (type in right) {\n        // @ts-expect-error assume document vs regular handler are managed correctly.\n        left[type] = right[type]\n      }\n    }\n  }\n}\n", "import {codes, values} from 'micromark-util-symbol'\n\n/**\n * Turn the number (in string form as either hexa- or plain decimal) coming from\n * a numeric character reference into a character.\n *\n * Sort of like `String.fromCodePoint(Number.parseInt(value, base))`, but makes\n * non-characters and control characters safe.\n *\n * @param {string} value\n *   Value to decode.\n * @param {number} base\n *   Numeric base.\n * @returns {string}\n *   Character.\n */\nexport function decodeNumericCharacterReference(value, base) {\n  const code = Number.parseInt(value, base)\n\n  if (\n    // C0 except for HT, LF, FF, CR, space.\n    code < codes.ht ||\n    code === codes.vt ||\n    (code > codes.cr && code < codes.space) ||\n    // Control character (DEL) of C0, and C1 controls.\n    (code > codes.tilde && code < 160) ||\n    // Lone high surrogates and low surrogates.\n    (code > 55_295 && code < 57_344) ||\n    // Noncharacters.\n    (code > 64_975 && code < 65_008) ||\n    /* eslint-disable no-bitwise */\n    (code & 65_535) === 65_535 ||\n    (code & 65_535) === 65_534 ||\n    /* eslint-enable no-bitwise */\n    // Out of range\n    code > 1_114_111\n  ) {\n    return values.replacementCharacter\n  }\n\n  return String.fromCodePoint(code)\n}\n", "import {values} from 'micromark-util-symbol'\n\n/**\n * Normalize an identifier (as found in references, definitions).\n *\n * Collapses markdown whitespace, trim, and then lower- and uppercase.\n *\n * Some characters are considered “uppercase”, such as U+03F4 (`ϴ`), but if their\n * lowercase counterpart (U+03B8 (`θ`)) is uppercased will result in a different\n * uppercase character (U+0398 (`Θ`)).\n * So, to get a canonical form, we perform both lower- and uppercase.\n *\n * Using uppercase last makes sure keys will never interact with default\n * prototypal values (such as `constructor`): nothing in the prototype of\n * `Object` is uppercase.\n *\n * @param {string} value\n *   Identifier to normalize.\n * @returns {string}\n *   Normalized identifier.\n */\nexport function normalizeIdentifier(value) {\n  return (\n    value\n      // Collapse markdown whitespace.\n      .replace(/[\\t\\n\\r ]+/g, values.space)\n      // Trim.\n      .replace(/^ | $/g, '')\n      // Some characters are considered “uppercase”, but if their lowercase\n      // counterpart is uppercased will result in a different uppercase\n      // character.\n      // Hence, to get that form, we perform both lower- and uppercase.\n      // Upper case makes sure keys will not interact with default prototypal\n      // methods: no method is uppercase.\n      .toLowerCase()\n      .toUpperCase()\n  )\n}\n", "/**\n * @import {Code} from 'micromark-util-types'\n */\n\nimport {codes} from 'micromark-util-symbol'\n\n/**\n * Check whether the character code represents an ASCII alpha (`a` through `z`,\n * case insensitive).\n *\n * An **ASCII alpha** is an ASCII upper alpha or ASCII lower alpha.\n *\n * An **ASCII upper alpha** is a character in the inclusive range U+0041 (`A`)\n * to U+005A (`Z`).\n *\n * An **ASCII lower alpha** is a character in the inclusive range U+0061 (`a`)\n * to U+007A (`z`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAlpha = regexCheck(/[A-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII alphanumeric (`a`\n * through `z`, case insensitive, or `0` through `9`).\n *\n * An **ASCII alphanumeric** is an ASCII digit (see `asciiDigit`) or ASCII alpha\n * (see `asciiAlpha`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAlphanumeric = regexCheck(/[\\dA-Za-z]/)\n\n/**\n * Check whether the character code represents an ASCII atext.\n *\n * atext is an ASCII alphanumeric (see `asciiAlphanumeric`), or a character in\n * the inclusive ranges U+0023 NUMBER SIGN (`#`) to U+0027 APOSTROPHE (`'`),\n * U+002A ASTERISK (`*`), U+002B PLUS SIGN (`+`), U+002D DASH (`-`), U+002F\n * SLASH (`/`), U+003D EQUALS TO (`=`), U+003F QUESTION MARK (`?`), U+005E\n * CARET (`^`) to U+0060 GRAVE ACCENT (`` ` ``), or U+007B LEFT CURLY BRACE\n * (`{`) to U+007E TILDE (`~`).\n *\n * See:\n * **\\[RFC5322]**:\n * [Internet Message Format](https://tools.ietf.org/html/rfc5322).\n * P. Resnick.\n * IETF.\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiAtext = regexCheck(/[#-'*+\\--9=?A-Z^-~]/)\n\n/**\n * Check whether a character code is an ASCII control character.\n *\n * An **ASCII control** is a character in the inclusive range U+0000 NULL (NUL)\n * to U+001F (US), or U+007F (DEL).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function asciiControl(code) {\n  return (\n    // Special whitespace codes (which have negative values), C0 and Control\n    // character DEL\n    code !== null && (code < codes.space || code === codes.del)\n  )\n}\n\n/**\n * Check whether the character code represents an ASCII digit (`0` through `9`).\n *\n * An **ASCII digit** is a character in the inclusive range U+0030 (`0`) to\n * U+0039 (`9`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiDigit = regexCheck(/\\d/)\n\n/**\n * Check whether the character code represents an ASCII hex digit (`a` through\n * `f`, case insensitive, or `0` through `9`).\n *\n * An **ASCII hex digit** is an ASCII digit (see `asciiDigit`), ASCII upper hex\n * digit, or an ASCII lower hex digit.\n *\n * An **ASCII upper hex digit** is a character in the inclusive range U+0041\n * (`A`) to U+0046 (`F`).\n *\n * An **ASCII lower hex digit** is a character in the inclusive range U+0061\n * (`a`) to U+0066 (`f`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiHexDigit = regexCheck(/[\\dA-Fa-f]/)\n\n/**\n * Check whether the character code represents ASCII punctuation.\n *\n * An **ASCII punctuation** is a character in the inclusive ranges U+0021\n * EXCLAMATION MARK (`!`) to U+002F SLASH (`/`), U+003A COLON (`:`) to U+0040 AT\n * SIGN (`@`), U+005B LEFT SQUARE BRACKET (`[`) to U+0060 GRAVE ACCENT\n * (`` ` ``), or U+007B LEFT CURLY BRACE (`{`) to U+007E TILDE (`~`).\n *\n * @param code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport const asciiPunctuation = regexCheck(/[!-/:-@[-`{-~]/)\n\n/**\n * Check whether a character code is a markdown line ending.\n *\n * A **markdown line ending** is the virtual characters M-0003 CARRIAGE RETURN\n * LINE FEED (CRLF), M-0004 LINE FEED (LF) and M-0005 CARRIAGE RETURN (CR).\n *\n * In micromark, the actual character U+000A LINE FEED (LF) and U+000D CARRIAGE\n * RETURN (CR) are replaced by these virtual characters depending on whether\n * they occurred together.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEnding(code) {\n  return code !== null && code < codes.horizontalTab\n}\n\n/**\n * Check whether a character code is a markdown line ending (see\n * `markdownLineEnding`) or markdown space (see `markdownSpace`).\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownLineEndingOrSpace(code) {\n  return code !== null && (code < codes.nul || code === codes.space)\n}\n\n/**\n * Check whether a character code is a markdown space.\n *\n * A **markdown space** is the concrete character U+0020 SPACE (SP) and the\n * virtual characters M-0001 VIRTUAL SPACE (VS) and M-0002 HORIZONTAL TAB (HT).\n *\n * In micromark, the actual character U+0009 CHARACTER TABULATION (HT) is\n * replaced by one M-0002 HORIZONTAL TAB (HT) and between 0 and 3 M-0001 VIRTUAL\n * SPACE (VS) characters, depending on the column at which the tab occurred.\n *\n * @param {Code} code\n *   Code.\n * @returns {boolean}\n *   Whether it matches.\n */\nexport function markdownSpace(code) {\n  return (\n    code === codes.horizontalTab ||\n    code === codes.virtualSpace ||\n    code === codes.space\n  )\n}\n\n// Size note: removing ASCII from the regex and using `asciiPunctuation` here\n// In fact adds to the bundle size.\n/**\n * Check whether the character code represents Unicode punctuation.\n *\n * A **Unicode punctuation** is a character in the Unicode `Pc` (Punctuation,\n * Connector), `Pd` (Punctuation, Dash), `Pe` (Punctuation, Close), `Pf`\n * (Punctuation, Final quote), `Pi` (Punctuation, Initial quote), `Po`\n * (Punctuation, Other), or `Ps` (Punctuation, Open) categories, or an ASCII\n * punctuation (see `asciiPunctuation`).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodePunctuation = regexCheck(/\\p{P}|\\p{S}/u)\n\n/**\n * Check whether the character code represents Unicode whitespace.\n *\n * Note that this does handle micromark specific markdown whitespace characters.\n * See `markdownLineEndingOrSpace` to check that.\n *\n * A **Unicode whitespace** is a character in the Unicode `Zs` (Separator,\n * Space) category, or U+0009 CHARACTER TABULATION (HT), U+000A LINE FEED (LF),\n * U+000C (FF), or U+000D CARRIAGE RETURN (CR) (**\\[UNICODE]**).\n *\n * See:\n * **\\[UNICODE]**:\n * [The Unicode Standard](https://www.unicode.org/versions/).\n * Unicode Consortium.\n *\n * @param code\n *   Code.\n * @returns\n *   Whether it matches.\n */\nexport const unicodeWhitespace = regexCheck(/\\s/)\n\n/**\n * Create a code check from a regex.\n *\n * @param {RegExp} regex\n *   Expression.\n * @returns {(code: Code) => boolean}\n *   Check.\n */\nfunction regexCheck(regex) {\n  return check\n\n  /**\n   * Check whether a code matches the bound regex.\n   *\n   * @param {Code} code\n   *   Character code.\n   * @returns {boolean}\n   *   Whether the character code matches the bound regex.\n   */\n  function check(code) {\n    return code !== null && code > -1 && regex.test(String.fromCharCode(code))\n  }\n}\n", "/**\n * While micromark is a lexer/tokenizer, the common case of going from markdown\n * to html is currently built in as this module, even though the parts can be\n * used separately to build ASTs, CSTs, or many other output formats.\n *\n * Having an HTML compiler built in is useful because it allows us to check for\n * compliancy to CommonMark, the de facto norm of markdown, specified in roughly\n * 600 input/output cases.\n *\n * This module has an interface that accepts lists of events instead of the\n * whole at once, however, because markdown can’t be truly streaming, we buffer\n * events before processing and outputting the final result.\n */\n\n/**\n * @import {\n *   CompileContext,\n *   CompileData,\n *   CompileOptions,\n *   Compile,\n *   Definition,\n *   Event,\n *   Handle,\n *   HtmlExtension,\n *   LineEnding,\n *   NormalizedHtmlExtension,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @typedef Media\n * @property {boolean | undefined} [image]\n * @property {string | undefined} [labelId]\n * @property {string | undefined} [label]\n * @property {string | undefined} [referenceId]\n * @property {string | undefined} [destination]\n * @property {string | undefined} [title]\n */\n\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {ok as assert} from 'devlop'\nimport {push} from 'micromark-util-chunked'\nimport {combineHtmlExtensions} from 'micromark-util-combine-extensions'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {encode as _encode} from 'micromark-util-encode'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {sanitizeUri} from 'micromark-util-sanitize-uri'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\nconst hasOwnProperty = {}.hasOwnProperty\n\n/**\n * These two are allowlists of safe protocols for full URLs in respectively the\n * `href` (on `<a>`) and `src` (on `<img>`) attributes.\n * They are based on what is allowed on GitHub,\n * <https://github.com/syntax-tree/hast-util-sanitize/blob/9275b21/lib/github.json#L31>\n */\nconst protocolHref = /^(https?|ircs?|mailto|xmpp)$/i\nconst protocolSource = /^https?$/i\n\n/**\n * @param {CompileOptions | null | undefined} [options]\n * @returns {Compile}\n */\nexport function compile(options) {\n  const settings = options || {}\n\n  /**\n   * Tags is needed because according to markdown, links and emphasis and\n   * whatnot can exist in images, however, as HTML doesn’t allow content in\n   * images, the tags are ignored in the `alt` attribute, but the content\n   * remains.\n   *\n   * @type {boolean | undefined}\n   */\n  let tags = true\n\n  /**\n   * An object to track identifiers to media (URLs and titles) defined with\n   * definitions.\n   *\n   * @type {Record<string, Definition>}\n   */\n  const definitions = {}\n\n  /**\n   * A lot of the handlers need to capture some of the output data, modify it\n   * somehow, and then deal with it.\n   * We do that by tracking a stack of buffers, that can be opened (with\n   * `buffer`) and closed (with `resume`) to access them.\n   *\n   * @type {Array<Array<string>>}\n   */\n  const buffers = [[]]\n\n  /**\n   * As we can have links in images and the other way around, where the deepest\n   * ones are closed first, we need to track which one we’re in.\n   *\n   * @type {Array<Media>}\n   */\n  const mediaStack = []\n\n  /**\n   * Same as `mediaStack` for tightness, which is specific to lists.\n   * We need to track if we’re currently in a tight or loose container.\n   *\n   * @type {Array<boolean>}\n   */\n  const tightStack = []\n\n  /** @type {HtmlExtension} */\n  const defaultHandlers = {\n    enter: {\n      blockQuote: onenterblockquote,\n      codeFenced: onentercodefenced,\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: onentercodeindented,\n      codeText: onentercodetext,\n      content: onentercontent,\n      definition: onenterdefinition,\n      definitionDestinationString: onenterdefinitiondestinationstring,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: onenteremphasis,\n      htmlFlow: onenterhtmlflow,\n      htmlText: onenterhtml,\n      image: onenterimage,\n      label: buffer,\n      link: onenterlink,\n      listItemMarker: onenterlistitemmarker,\n      listItemValue: onenterlistitemvalue,\n      listOrdered: onenterlistordered,\n      listUnordered: onenterlistunordered,\n      paragraph: onenterparagraph,\n      reference: buffer,\n      resource: onenterresource,\n      resourceDestinationString: onenterresourcedestinationstring,\n      resourceTitleString: buffer,\n      setextHeading: onentersetextheading,\n      strong: onenterstrong\n    },\n    exit: {\n      atxHeading: onexitatxheading,\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: onexitblockquote,\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      codeFenced: onexitflowcode,\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onresumedrop,\n      codeFlowValue: onexitcodeflowvalue,\n      codeIndented: onexitflowcode,\n      codeText: onexitcodetext,\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: onexitdefinition,\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: onexitemphasis,\n      hardBreakEscape: onexithardbreak,\n      hardBreakTrailing: onexithardbreak,\n      htmlFlow: onexithtml,\n      htmlFlowData: onexitdata,\n      htmlText: onexithtml,\n      htmlTextData: onexitdata,\n      image: onexitmedia,\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: onexitmedia,\n      listOrdered: onexitlistordered,\n      listUnordered: onexitlistunordered,\n      paragraph: onexitparagraph,\n      reference: onresumedrop,\n      referenceString: onexitreferencestring,\n      resource: onresumedrop,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      setextHeading: onexitsetextheading,\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: onexitstrong,\n      thematicBreak: onexitthematicbreak\n    }\n  }\n\n  /**\n   * Combine the HTML extensions with the default handlers.\n   * An HTML extension is an object whose fields are either `enter` or `exit`\n   * (reflecting whether a token is entered or exited).\n   * The values at such objects are names of tokens mapping to handlers.\n   * Handlers are called, respectively when a token is opener or closed, with\n   * that token, and a context as `this`.\n   */\n  const handlers = /** @type {NormalizedHtmlExtension} */ (\n    combineHtmlExtensions([defaultHandlers, ...(settings.htmlExtensions || [])])\n  )\n\n  /**\n   * Handlers do often need to keep track of some state.\n   * That state is provided here as a key-value store (an object).\n   *\n   * @type {CompileData}\n   */\n  const data = {\n    definitions,\n    tightStack\n  }\n\n  /**\n   * The context for handlers references a couple of useful functions.\n   * In handlers from extensions, those can be accessed at `this`.\n   * For the handlers here, they can be accessed directly.\n   *\n   * @type {Omit<CompileContext, 'sliceSerialize'>}\n   */\n  const context = {\n    buffer,\n    encode,\n    getData,\n    lineEndingIfNeeded,\n    options: settings,\n    raw,\n    resume,\n    setData,\n    tag\n  }\n\n  /**\n   * Generally, micromark copies line endings (`'\\r'`, `'\\n'`, `'\\r\\n'`) in the\n   * markdown document over to the compiled HTML.\n   * In some cases, such as `> a`, CommonMark requires that extra line endings\n   * are added: `<blockquote>\\n<p>a</p>\\n</blockquote>`.\n   * This variable hold the default line ending when given (or `undefined`),\n   * and in the latter case will be updated to the first found line ending if\n   * there is one.\n   */\n  let lineEndingStyle = settings.defaultLineEnding\n\n  // Return the function that handles a slice of events.\n  return compile\n\n  /**\n   * Deal w/ a slice of events.\n   * Return either the empty string if there’s nothing of note to return, or the\n   * result when done.\n   *\n   * @param {ReadonlyArray<Event>} events\n   * @returns {string}\n   */\n  function compile(events) {\n    let index = -1\n    let start = 0\n    /** @type {Array<number>} */\n    const listStack = []\n    // As definitions can come after references, we need to figure out the media\n    // (urls and titles) defined by them before handling the references.\n    // So, we do sort of what HTML does: put metadata at the start (in head), and\n    // then put content after (`body`).\n    /** @type {Array<Event>} */\n    let head = []\n    /** @type {Array<Event>} */\n    let body = []\n\n    while (++index < events.length) {\n      // Figure out the line ending style used in the document.\n      if (\n        !lineEndingStyle &&\n        (events[index][1].type === types.lineEnding ||\n          events[index][1].type === types.lineEndingBlank)\n      ) {\n        lineEndingStyle = /** @type {LineEnding} */ (\n          events[index][2].sliceSerialize(events[index][1])\n        )\n      }\n\n      // Preprocess lists to infer whether the list is loose or not.\n      if (\n        events[index][1].type === types.listOrdered ||\n        events[index][1].type === types.listUnordered\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          prepareList(events.slice(listStack.pop(), index))\n        }\n      }\n\n      // Move definitions to the front.\n      if (events[index][1].type === types.definition) {\n        if (events[index][0] === 'enter') {\n          body = push(body, events.slice(start, index))\n          start = index\n        } else {\n          head = push(head, events.slice(start, index + 1))\n          start = index + 1\n        }\n      }\n    }\n\n    head = push(head, body)\n    head = push(head, events.slice(start))\n    index = -1\n    const result = head\n\n    // Handle the start of the document, if defined.\n    if (handlers.enter.null) {\n      handlers.enter.null.call(context)\n    }\n\n    // Handle all events.\n    while (++index < events.length) {\n      const handles = handlers[result[index][0]]\n      const kind = result[index][1].type\n      const handle = handles[kind]\n\n      if (hasOwnProperty.call(handles, kind) && handle) {\n        handle.call(\n          {sliceSerialize: result[index][2].sliceSerialize, ...context},\n          result[index][1]\n        )\n      }\n    }\n\n    // Handle the end of the document, if defined.\n    if (handlers.exit.null) {\n      handlers.exit.null.call(context)\n    }\n\n    return buffers[0].join('')\n  }\n\n  /**\n   * Figure out whether lists are loose or not.\n   *\n   * @param {ReadonlyArray<Event>} slice\n   * @returns {undefined}\n   */\n  function prepareList(slice) {\n    const length = slice.length\n    let index = 0 // Skip open.\n    let containerBalance = 0\n    let loose = false\n    /** @type {boolean | undefined} */\n    let atMarker\n\n    while (++index < length) {\n      const event = slice[index]\n\n      if (event[1]._container) {\n        atMarker = undefined\n\n        if (event[0] === 'enter') {\n          containerBalance++\n        } else {\n          containerBalance--\n        }\n      } else\n        switch (event[1].type) {\n          case types.listItemPrefix: {\n            if (event[0] === 'exit') {\n              atMarker = true\n            }\n\n            break\n          }\n\n          case types.linePrefix: {\n            // Ignore\n\n            break\n          }\n\n          case types.lineEndingBlank: {\n            if (event[0] === 'enter' && !containerBalance) {\n              if (atMarker) {\n                atMarker = undefined\n              } else {\n                loose = true\n              }\n            }\n\n            break\n          }\n\n          default: {\n            atMarker = undefined\n          }\n        }\n    }\n\n    slice[0][1]._loose = loose\n  }\n\n  /**\n   * @type {CompileContext['setData']}\n   */\n  function setData(key, value) {\n    // @ts-expect-error: assume `value` is omitted (`undefined` is passed) only\n    // if allowed.\n    data[key] = value\n  }\n\n  /**\n   * @type {CompileContext['getData']}\n   */\n  function getData(key) {\n    return data[key]\n  }\n\n  /** @type {CompileContext['buffer']} */\n  function buffer() {\n    buffers.push([])\n  }\n\n  /** @type {CompileContext['resume']} */\n  function resume() {\n    const buf = buffers.pop()\n    assert(buf !== undefined, 'Cannot resume w/o buffer')\n    return buf.join('')\n  }\n\n  /** @type {CompileContext['tag']} */\n  function tag(value) {\n    if (!tags) return\n    setData('lastWasTag', true)\n    buffers[buffers.length - 1].push(value)\n  }\n\n  /** @type {CompileContext['raw']} */\n  function raw(value) {\n    setData('lastWasTag')\n    buffers[buffers.length - 1].push(value)\n  }\n\n  /**\n   * Output an extra line ending.\n   *\n   * @returns {undefined}\n   */\n  function lineEnding() {\n    raw(lineEndingStyle || '\\n')\n  }\n\n  /** @type {CompileContext['lineEndingIfNeeded']} */\n  function lineEndingIfNeeded() {\n    const buffer = buffers[buffers.length - 1]\n    const slice = buffer[buffer.length - 1]\n    const previous = slice ? slice.charCodeAt(slice.length - 1) : codes.eof\n\n    if (\n      previous === codes.lf ||\n      previous === codes.cr ||\n      previous === codes.eof\n    ) {\n      return\n    }\n\n    lineEnding()\n  }\n\n  /** @type {CompileContext['encode']} */\n  function encode(value) {\n    return getData('ignoreEncode') ? value : _encode(value)\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @returns {undefined}\n   */\n  function onresumedrop() {\n    resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered(token) {\n    tightStack.push(!token._loose)\n    lineEndingIfNeeded()\n    tag('<ol')\n    setData('expectFirstItem', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistunordered(token) {\n    tightStack.push(!token._loose)\n    lineEndingIfNeeded()\n    tag('<ul')\n    setData('expectFirstItem', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (getData('expectFirstItem')) {\n      const value = Number.parseInt(\n        this.sliceSerialize(token),\n        constants.numericBaseDecimal\n      )\n\n      if (value !== 1) {\n        tag(' start=\"' + encode(String(value)) + '\"')\n      }\n    }\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterlistitemmarker() {\n    if (getData('expectFirstItem')) {\n      tag('>')\n    } else {\n      onexitlistitem()\n    }\n\n    lineEndingIfNeeded()\n    tag('<li>')\n    setData('expectFirstItem')\n    // “Hack” to prevent a line ending from showing up if the item is empty.\n    setData('lastWasTag')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistordered() {\n    onexitlistitem()\n    tightStack.pop()\n    lineEnding()\n    tag('</ol>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistunordered() {\n    onexitlistitem()\n    tightStack.pop()\n    lineEnding()\n    tag('</ul>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistitem() {\n    if (getData('lastWasTag') && !getData('slurpAllLineEndings')) {\n      lineEndingIfNeeded()\n    }\n\n    tag('</li>')\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterblockquote() {\n    tightStack.push(false)\n    lineEndingIfNeeded()\n    tag('<blockquote>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitblockquote() {\n    tightStack.pop()\n    lineEndingIfNeeded()\n    tag('</blockquote>')\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterparagraph() {\n    if (!tightStack[tightStack.length - 1]) {\n      lineEndingIfNeeded()\n      tag('<p>')\n    }\n\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitparagraph() {\n    if (tightStack[tightStack.length - 1]) {\n      setData('slurpAllLineEndings', true)\n    } else {\n      tag('</p>')\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercodefenced() {\n    lineEndingIfNeeded()\n    tag('<pre><code')\n    setData('fencesCount', 0)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const value = resume()\n    tag(' class=\"language-' + value + '\"')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    const count = getData('fencesCount') || 0\n\n    if (!count) {\n      tag('>')\n      setData('slurpOneLineEnding', true)\n    }\n\n    setData('fencesCount', count + 1)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercodeindented() {\n    lineEndingIfNeeded()\n    tag('<pre><code>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitflowcode() {\n    const count = getData('fencesCount')\n\n    // One special case is if we are inside a container, and the fenced code was\n    // not closed (meaning it runs to the end).\n    // In that case, the following line ending, is considered *outside* the\n    // fenced code and block quote by micromark, but CM wants to treat that\n    // ending as part of the code.\n    if (\n      count !== undefined &&\n      count < 2 &&\n      data.tightStack.length > 0 &&\n      !getData('lastWasTag')\n    ) {\n      lineEnding()\n    }\n\n    // But in most cases, it’s simpler: when we’ve seen some data, emit an extra\n    // line ending when needed.\n    if (getData('flowCodeSeenData')) {\n      lineEndingIfNeeded()\n    }\n\n    tag('</code></pre>')\n    if (count !== undefined && count < 2) lineEndingIfNeeded()\n    setData('flowCodeSeenData')\n    setData('fencesCount')\n    setData('slurpOneLineEnding')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterimage() {\n    mediaStack.push({image: true})\n    tags = undefined // Disallow tags.\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlink() {\n    mediaStack.push({})\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlabeltext(token) {\n    mediaStack[mediaStack.length - 1].labelId = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlabel() {\n    mediaStack[mediaStack.length - 1].label = resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitreferencestring(token) {\n    mediaStack[mediaStack.length - 1].referenceId = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterresource() {\n    buffer() // We can have line endings in the resource, ignore them.\n    mediaStack[mediaStack.length - 1].destination = ''\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterresourcedestinationstring() {\n    buffer()\n    // Ignore encoding the result, as we’ll first percent encode the url and\n    // encode manually after.\n    setData('ignoreEncode', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitresourcedestinationstring() {\n    mediaStack[mediaStack.length - 1].destination = resume()\n    setData('ignoreEncode')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitresourcetitlestring() {\n    mediaStack[mediaStack.length - 1].title = resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitmedia() {\n    let index = mediaStack.length - 1 // Skip current.\n    const media = mediaStack[index]\n    const id = media.referenceId || media.labelId\n    assert(id !== undefined, 'media should have `referenceId` or `labelId`')\n    assert(media.label !== undefined, 'media should have `label`')\n    const context =\n      media.destination === undefined\n        ? definitions[normalizeIdentifier(id)]\n        : media\n\n    tags = true\n\n    while (index--) {\n      if (mediaStack[index].image) {\n        tags = undefined\n        break\n      }\n    }\n\n    if (media.image) {\n      tag(\n        '<img src=\"' +\n          sanitizeUri(\n            context.destination,\n            settings.allowDangerousProtocol ? undefined : protocolSource\n          ) +\n          '\" alt=\"'\n      )\n      raw(media.label)\n      tag('\"')\n    } else {\n      tag(\n        '<a href=\"' +\n          sanitizeUri(\n            context.destination,\n            settings.allowDangerousProtocol ? undefined : protocolHref\n          ) +\n          '\"'\n      )\n    }\n\n    tag(context.title ? ' title=\"' + context.title + '\"' : '')\n\n    if (media.image) {\n      tag(' />')\n    } else {\n      tag('>')\n      raw(media.label)\n      tag('</a>')\n    }\n\n    mediaStack.pop()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterdefinition() {\n    buffer()\n    mediaStack.push({})\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    // Discard label, use the source content instead.\n    resume()\n    mediaStack[mediaStack.length - 1].labelId = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterdefinitiondestinationstring() {\n    buffer()\n    setData('ignoreEncode', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    mediaStack[mediaStack.length - 1].destination = resume()\n    setData('ignoreEncode')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    mediaStack[mediaStack.length - 1].title = resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinition() {\n    const media = mediaStack[mediaStack.length - 1]\n    assert(media.labelId !== undefined, 'media should have `labelId`')\n    const id = normalizeIdentifier(media.labelId)\n\n    resume()\n\n    if (!hasOwnProperty.call(definitions, id)) {\n      definitions[id] = mediaStack[mediaStack.length - 1]\n    }\n\n    mediaStack.pop()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercontent() {\n    setData('slurpAllLineEndings', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    // Exit for further sequences.\n    if (getData('headingRank')) return\n    setData('headingRank', this.sliceSerialize(token).length)\n    lineEndingIfNeeded()\n    tag('<h' + getData('headingRank') + '>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentersetextheading() {\n    buffer()\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    setData('slurpAllLineEndings', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheading() {\n    tag('</h' + getData('headingRank') + '>')\n    setData('headingRank')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    setData(\n      'headingRank',\n      this.sliceSerialize(token).charCodeAt(0) === codes.equalsTo ? 1 : 2\n    )\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    const value = resume()\n    lineEndingIfNeeded()\n    tag('<h' + getData('headingRank') + '>')\n    raw(value)\n    tag('</h' + getData('headingRank') + '>')\n    setData('slurpAllLineEndings')\n    setData('headingRank')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdata(token) {\n    raw(encode(this.sliceSerialize(token)))\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlineending(token) {\n    if (getData('slurpAllLineEndings')) {\n      return\n    }\n\n    if (getData('slurpOneLineEnding')) {\n      setData('slurpOneLineEnding')\n      return\n    }\n\n    if (getData('inCodeText')) {\n      raw(' ')\n      return\n    }\n\n    raw(encode(this.sliceSerialize(token)))\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeflowvalue(token) {\n    raw(encode(this.sliceSerialize(token)))\n    setData('flowCodeSeenData', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexithardbreak() {\n    tag('<br />')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterhtmlflow() {\n    lineEndingIfNeeded()\n    onenterhtml()\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexithtml() {\n    setData('ignoreEncode')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterhtml() {\n    if (settings.allowDangerousHtml) {\n      setData('ignoreEncode', true)\n    }\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenteremphasis() {\n    tag('<em>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterstrong() {\n    tag('<strong>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onentercodetext() {\n    setData('inCodeText', true)\n    tag('<code>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitcodetext() {\n    setData('inCodeText')\n    tag('</code>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitemphasis() {\n    tag('</em>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitstrong() {\n    tag('</strong>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitthematicbreak() {\n    lineEndingIfNeeded()\n    tag('<hr />')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @param {Token} token\n   * @returns {undefined}\n   */\n  function onexitcharacterreferencemarker(token) {\n    setData('characterReferenceType', token.type)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const value = this.sliceSerialize(token)\n    const decoded = getData('characterReferenceType')\n      ? decodeNumericCharacterReference(\n          value,\n          getData('characterReferenceType') ===\n            types.characterReferenceMarkerNumeric\n            ? constants.numericBaseDecimal\n            : constants.numericBaseHexadecimal\n        )\n      : decodeNamedCharacterReference(value)\n\n    // `decodeNamedCharacterReference` can return `false` for invalid named\n    // character references,\n    // but everything we’ve tokenized is valid.\n    raw(encode(/** @type {string} */ (decoded)))\n    setData('characterReferenceType')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    const uri = this.sliceSerialize(token)\n    tag(\n      '<a href=\"' +\n        sanitizeUri(\n          uri,\n          settings.allowDangerousProtocol ? undefined : protocolHref\n        ) +\n        '\">'\n    )\n    raw(encode(uri))\n    tag('</a>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    const uri = this.sliceSerialize(token)\n    tag('<a href=\"' + sanitizeUri('mailto:' + uri) + '\">')\n    raw(encode(uri))\n    tag('</a>')\n  }\n}\n", "/**\n * @import {Effects, State, TokenType} from 'micromark-util-types'\n */\n\nimport {markdownSpace} from 'micromark-util-character'\n\n// To do: implement `spaceOrTab`, `spaceOrTabMinMax`, `spaceOrTabWithOptions`.\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   spaces in markdown are often optional, in which case this factory can be\n *     used and `ok` will be switched to whether spaces were found or not\n * *   one line ending or space can be detected with `markdownSpace(code)` right\n *     before using `factorySpace`\n *\n * ###### Examples\n *\n * Where `␉` represents a tab (plus how much it expands) and `␠` represents a\n * single space.\n *\n * ```markdown\n * ␉\n * ␠␠␠␠\n * ␉␠\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {TokenType} type\n *   Type (`' \\t'`).\n * @param {number | undefined} [max=Infinity]\n *   Max (exclusive).\n * @returns {State}\n *   Start state.\n */\nexport function factorySpace(effects, ok, type, max) {\n  const limit = max ? max - 1 : Number.POSITIVE_INFINITY\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (markdownSpace(code)) {\n      effects.enter(type)\n      return prefix(code)\n    }\n\n    return ok(code)\n  }\n\n  /** @type {State} */\n  function prefix(code) {\n    if (markdownSpace(code) && size++ < limit) {\n      effects.consume(code)\n      return prefix\n    }\n\n    effects.exit(type)\n    return ok(code)\n  }\n}\n", "/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {InitialConstruct} */\nexport const content = {tokenize: initializeContent}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */\nfunction initializeContent(effects) {\n  const contentStart = effects.attempt(\n    this.parser.constructs.contentInitial,\n    afterContentStartConstruct,\n    paragraphInitial\n  )\n  /** @type {Token} */\n  let previous\n\n  return contentStart\n\n  /** @type {State} */\n  function afterContentStartConstruct(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, contentStart, types.linePrefix)\n  }\n\n  /** @type {State} */\n  function paragraphInitial(code) {\n    assert(\n      code !== codes.eof && !markdownLineEnding(code),\n      'expected anything other than a line ending or EOF'\n    )\n    effects.enter(types.paragraph)\n    return lineStart(code)\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    const token = effects.enter(types.chunkText, {\n      contentType: constants.contentTypeText,\n      previous\n    })\n\n    if (previous) {\n      previous.next = token\n    }\n\n    previous = token\n\n    return data(code)\n  }\n\n  /** @type {State} */\n  function data(code) {\n    if (code === codes.eof) {\n      effects.exit(types.chunkText)\n      effects.exit(types.paragraph)\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      effects.exit(types.chunkText)\n      return lineStart\n    }\n\n    // Data.\n    effects.consume(code)\n    return data\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   ContainerState,\n *   InitialConstruct,\n *   Initializer,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @typedef {[Construct, ContainerState]} StackItem\n *   Construct and its state.\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {splice} from 'micromark-util-chunked'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {InitialConstruct} */\nexport const document = {tokenize: initializeDocument}\n\n/** @type {Construct} */\nconst containerConstruct = {tokenize: tokenizeContainer}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeDocument(effects) {\n  const self = this\n  /** @type {Array<StackItem>} */\n  const stack = []\n  let continued = 0\n  /** @type {TokenizeContext | undefined} */\n  let childFlow\n  /** @type {Token | undefined} */\n  let childToken\n  /** @type {number} */\n  let lineStartOffset\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    // First we iterate through the open blocks, starting with the root\n    // document, and descending through last children down to the last open\n    // block.\n    // Each block imposes a condition that the line must satisfy if the block is\n    // to remain open.\n    // For example, a block quote requires a `>` character.\n    // A paragraph requires a non-blank line.\n    // In this phase we may match all or just some of the open blocks.\n    // But we cannot close unmatched blocks yet, because we may have a lazy\n    // continuation line.\n    if (continued < stack.length) {\n      const item = stack[continued]\n      self.containerState = item[1]\n      assert(\n        item[0].continuation,\n        'expected `continuation` to be defined on container construct'\n      )\n      return effects.attempt(\n        item[0].continuation,\n        documentContinue,\n        checkNewContainers\n      )(code)\n    }\n\n    // Done.\n    return checkNewContainers(code)\n  }\n\n  /** @type {State} */\n  function documentContinue(code) {\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined after continuation'\n    )\n\n    continued++\n\n    // Note: this field is called `_closeFlow` but it also closes containers.\n    // Perhaps a good idea to rename it but it’s already used in the wild by\n    // extensions.\n    if (self.containerState._closeFlow) {\n      self.containerState._closeFlow = undefined\n\n      if (childFlow) {\n        closeFlow()\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when dealing with lazy lines in `writeToChild`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the flow chunk.\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === types.chunkFlow\n        ) {\n          point = self.events[indexBeforeFlow][1].end\n          break\n        }\n      }\n\n      assert(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      let index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n\n      return checkNewContainers(code)\n    }\n\n    return start(code)\n  }\n\n  /** @type {State} */\n  function checkNewContainers(code) {\n    // Next, after consuming the continuation markers for existing blocks, we\n    // look for new block starts (e.g. `>` for a block quote).\n    // If we encounter a new block start, we close any blocks unmatched in\n    // step 1 before creating the new block as a child of the last matched\n    // block.\n    if (continued === stack.length) {\n      // No need to `check` whether there’s a container, of `exitContainers`\n      // would be moot.\n      // We can instead immediately `attempt` to parse one.\n      if (!childFlow) {\n        return documentContinued(code)\n      }\n\n      // If we have concrete content, such as block HTML or fenced code,\n      // we can’t have containers “pierce” into them, so we can immediately\n      // start.\n      if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n        return flowStart(code)\n      }\n\n      // If we do have flow, it could still be a blank line,\n      // but we’d be interrupting it w/ a new container if there’s a current\n      // construct.\n      // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n      // needed in micromark-extension-gfm-table@1.0.6).\n      self.interrupt = Boolean(\n        childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack\n      )\n    }\n\n    // Check if there is a new container.\n    self.containerState = {}\n    return effects.check(\n      containerConstruct,\n      thereIsANewContainer,\n      thereIsNoNewContainer\n    )(code)\n  }\n\n  /** @type {State} */\n  function thereIsANewContainer(code) {\n    if (childFlow) closeFlow()\n    exitContainers(continued)\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function thereIsNoNewContainer(code) {\n    self.parser.lazy[self.now().line] = continued !== stack.length\n    lineStartOffset = self.now().offset\n    return flowStart(code)\n  }\n\n  /** @type {State} */\n  function documentContinued(code) {\n    // Try new containers.\n    self.containerState = {}\n    return effects.attempt(\n      containerConstruct,\n      containerContinue,\n      flowStart\n    )(code)\n  }\n\n  /** @type {State} */\n  function containerContinue(code) {\n    assert(\n      self.currentConstruct,\n      'expected `currentConstruct` to be defined on tokenizer'\n    )\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined on tokenizer'\n    )\n    continued++\n    stack.push([self.currentConstruct, self.containerState])\n    // Try another.\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function flowStart(code) {\n    if (code === codes.eof) {\n      if (childFlow) closeFlow()\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    childFlow = childFlow || self.parser.flow(self.now())\n    effects.enter(types.chunkFlow, {\n      _tokenizer: childFlow,\n      contentType: constants.contentTypeFlow,\n      previous: childToken\n    })\n\n    return flowContinue(code)\n  }\n\n  /** @type {State} */\n  function flowContinue(code) {\n    if (code === codes.eof) {\n      writeToChild(effects.exit(types.chunkFlow), true)\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      writeToChild(effects.exit(types.chunkFlow))\n      // Get ready for the next line.\n      continued = 0\n      self.interrupt = undefined\n      return start\n    }\n\n    effects.consume(code)\n    return flowContinue\n  }\n\n  /**\n   * @param {Token} token\n   *   Token.\n   * @param {boolean | undefined} [endOfFile]\n   *   Whether the token is at the end of the file (default: `false`).\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function writeToChild(token, endOfFile) {\n    assert(childFlow, 'expected `childFlow` to be defined when continuing')\n    const stream = self.sliceStream(token)\n    if (endOfFile) stream.push(null)\n    token.previous = childToken\n    if (childToken) childToken.next = token\n    childToken = token\n    childFlow.defineSkip(token.start)\n    childFlow.write(stream)\n\n    // Alright, so we just added a lazy line:\n    //\n    // ```markdown\n    // > a\n    // b.\n    //\n    // Or:\n    //\n    // > ~~~c\n    // d\n    //\n    // Or:\n    //\n    // > | e |\n    // f\n    // ```\n    //\n    // The construct in the second example (fenced code) does not accept lazy\n    // lines, so it marked itself as done at the end of its first line, and\n    // then the content construct parses `d`.\n    // Most constructs in markdown match on the first line: if the first line\n    // forms a construct, a non-lazy line can’t “unmake” it.\n    //\n    // The construct in the third example is potentially a GFM table, and\n    // those are *weird*.\n    // It *could* be a table, from the first line, if the following line\n    // matches a condition.\n    // In this case, that second line is lazy, which “unmakes” the first line\n    // and turns the whole into one content block.\n    //\n    // We’ve now parsed the non-lazy and the lazy line, and can figure out\n    // whether the lazy line started a new flow block.\n    // If it did, we exit the current containers between the two flow blocks.\n    if (self.parser.lazy[token.start.line]) {\n      let index = childFlow.events.length\n\n      while (index--) {\n        if (\n          // The token starts before the line ending…\n          childFlow.events[index][1].start.offset < lineStartOffset &&\n          // …and either is not ended yet…\n          (!childFlow.events[index][1].end ||\n            // …or ends after it.\n            childFlow.events[index][1].end.offset > lineStartOffset)\n        ) {\n          // Exit: there’s still something open, which means it’s a lazy line\n          // part of something.\n          return\n        }\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when closing flow in `documentContinue`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {boolean | undefined} */\n      let seen\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the previous chunk (the one before the lazy line).\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === types.chunkFlow\n        ) {\n          if (seen) {\n            point = self.events[indexBeforeFlow][1].end\n            break\n          }\n\n          seen = true\n        }\n      }\n\n      assert(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n    }\n  }\n\n  /**\n   * @param {number} size\n   *   Size.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function exitContainers(size) {\n    let index = stack.length\n\n    // Exit open containers.\n    while (index-- > size) {\n      const entry = stack[index]\n      self.containerState = entry[1]\n      assert(\n        entry[0].exit,\n        'expected `exit` to be defined on container construct'\n      )\n      entry[0].exit.call(self, effects)\n    }\n\n    stack.length = size\n  }\n\n  function closeFlow() {\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined when closing flow'\n    )\n    assert(childFlow, 'expected `childFlow` to be defined when closing it')\n    childFlow.write([codes.eof])\n    childToken = undefined\n    childFlow = undefined\n    self.containerState._closeFlow = undefined\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n *   Tokenizer.\n */\nfunction tokenizeContainer(effects, ok, nok) {\n  // Always populated by defaults.\n  assert(\n    this.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n  return factorySpace(\n    effects,\n    effects.attempt(this.parser.constructs.document, ok, nok),\n    types.linePrefix,\n    this.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : constants.tabSize\n  )\n}\n", "/**\n * @import {Code} from 'micromark-util-types'\n */\n\nimport {\n  markdownLineEndingOrSpace,\n  unicodePunctuation,\n  unicodeWhitespace\n} from 'micromark-util-character'\nimport {codes, constants} from 'micromark-util-symbol'\n\n/**\n * Classify whether a code represents whitespace, punctuation, or something\n * else.\n *\n * Used for attention (emphasis, strong), whose sequences can open or close\n * based on the class of surrounding characters.\n *\n * > 👉 **Note**: eof (`null`) is seen as whitespace.\n *\n * @param {Code} code\n *   Code.\n * @returns {typeof constants.characterGroupWhitespace | typeof constants.characterGroupPunctuation | undefined}\n *   Group.\n */\nexport function classifyCharacter(code) {\n  if (\n    code === codes.eof ||\n    markdownLineEndingOrSpace(code) ||\n    unicodeWhitespace(code)\n  ) {\n    return constants.characterGroupWhitespace\n  }\n\n  if (unicodePunctuation(code)) {\n    return constants.characterGroupPunctuation\n  }\n}\n", "/**\n * @import {Event, Resolver, TokenizeContext} from 'micromark-util-types'\n */\n\n/**\n * Call all `resolveAll`s.\n *\n * @param {ReadonlyArray<{resolveAll?: Resolver | undefined}>} constructs\n *   List of constructs, optionally with `resolveAll`s.\n * @param {Array<Event>} events\n *   List of events.\n * @param {TokenizeContext} context\n *   Context used by `tokenize`.\n * @returns {Array<Event>}\n *   Changed events.\n */\nexport function resolveAll(constructs, events, context) {\n  /** @type {Array<Resolver>} */\n  const called = []\n  let index = -1\n\n  while (++index < constructs.length) {\n    const resolve = constructs[index].resolveAll\n\n    if (resolve && !called.includes(resolve)) {\n      events = resolve(events, context)\n      called.push(resolve)\n    }\n  }\n\n  return events\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   Event,\n *   Point,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {push, splice} from 'micromark-util-chunked'\nimport {classify<PERSON><PERSON>cter} from 'micromark-util-classify-character'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const attention = {\n  name: 'attention',\n  resolveAll: resolveAllAttention,\n  tokenize: tokenizeAttention\n}\n\n/**\n * Take all events and resolve attention to emphasis or strong.\n *\n * @type {Resolver}\n */\n// eslint-disable-next-line complexity\nfunction resolveAllAttention(events, context) {\n  let index = -1\n  /** @type {number} */\n  let open\n  /** @type {Token} */\n  let group\n  /** @type {Token} */\n  let text\n  /** @type {Token} */\n  let openingSequence\n  /** @type {Token} */\n  let closingSequence\n  /** @type {number} */\n  let use\n  /** @type {Array<Event>} */\n  let nextEvents\n  /** @type {number} */\n  let offset\n\n  // Walk through all events.\n  //\n  // Note: performance of this is fine on an mb of normal markdown, but it’s\n  // a bottleneck for malicious stuff.\n  while (++index < events.length) {\n    // Find a token that can close.\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === 'attentionSequence' &&\n      events[index][1]._close\n    ) {\n      open = index\n\n      // Now walk back to find an opener.\n      while (open--) {\n        // Find a token that can open the closer.\n        if (\n          events[open][0] === 'exit' &&\n          events[open][1].type === 'attentionSequence' &&\n          events[open][1]._open &&\n          // If the markers are the same:\n          context.sliceSerialize(events[open][1]).charCodeAt(0) ===\n            context.sliceSerialize(events[index][1]).charCodeAt(0)\n        ) {\n          // If the opening can close or the closing can open,\n          // and the close size *is not* a multiple of three,\n          // but the sum of the opening and closing size *is* multiple of three,\n          // then don’t match.\n          if (\n            (events[open][1]._close || events[index][1]._open) &&\n            (events[index][1].end.offset - events[index][1].start.offset) % 3 &&\n            !(\n              (events[open][1].end.offset -\n                events[open][1].start.offset +\n                events[index][1].end.offset -\n                events[index][1].start.offset) %\n              3\n            )\n          ) {\n            continue\n          }\n\n          // Number of markers to use from the sequence.\n          use =\n            events[open][1].end.offset - events[open][1].start.offset > 1 &&\n            events[index][1].end.offset - events[index][1].start.offset > 1\n              ? 2\n              : 1\n\n          const start = {...events[open][1].end}\n          const end = {...events[index][1].start}\n          movePoint(start, -use)\n          movePoint(end, use)\n\n          openingSequence = {\n            type: use > 1 ? types.strongSequence : types.emphasisSequence,\n            start,\n            end: {...events[open][1].end}\n          }\n          closingSequence = {\n            type: use > 1 ? types.strongSequence : types.emphasisSequence,\n            start: {...events[index][1].start},\n            end\n          }\n          text = {\n            type: use > 1 ? types.strongText : types.emphasisText,\n            start: {...events[open][1].end},\n            end: {...events[index][1].start}\n          }\n          group = {\n            type: use > 1 ? types.strong : types.emphasis,\n            start: {...openingSequence.start},\n            end: {...closingSequence.end}\n          }\n\n          events[open][1].end = {...openingSequence.start}\n          events[index][1].start = {...closingSequence.end}\n\n          nextEvents = []\n\n          // If there are more markers in the opening, add them before.\n          if (events[open][1].end.offset - events[open][1].start.offset) {\n            nextEvents = push(nextEvents, [\n              ['enter', events[open][1], context],\n              ['exit', events[open][1], context]\n            ])\n          }\n\n          // Opening.\n          nextEvents = push(nextEvents, [\n            ['enter', group, context],\n            ['enter', openingSequence, context],\n            ['exit', openingSequence, context],\n            ['enter', text, context]\n          ])\n\n          // Always populated by defaults.\n          assert(\n            context.parser.constructs.insideSpan.null,\n            'expected `insideSpan` to be populated'\n          )\n\n          // Between.\n          nextEvents = push(\n            nextEvents,\n            resolveAll(\n              context.parser.constructs.insideSpan.null,\n              events.slice(open + 1, index),\n              context\n            )\n          )\n\n          // Closing.\n          nextEvents = push(nextEvents, [\n            ['exit', text, context],\n            ['enter', closingSequence, context],\n            ['exit', closingSequence, context],\n            ['exit', group, context]\n          ])\n\n          // If there are more markers in the closing, add them after.\n          if (events[index][1].end.offset - events[index][1].start.offset) {\n            offset = 2\n            nextEvents = push(nextEvents, [\n              ['enter', events[index][1], context],\n              ['exit', events[index][1], context]\n            ])\n          } else {\n            offset = 0\n          }\n\n          splice(events, open - 1, index - open + 3, nextEvents)\n\n          index = open + nextEvents.length - offset - 2\n          break\n        }\n      }\n    }\n  }\n\n  // Remove remaining sequences.\n  index = -1\n\n  while (++index < events.length) {\n    if (events[index][1].type === 'attentionSequence') {\n      events[index][1].type = 'data'\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAttention(effects, ok) {\n  const attentionMarkers = this.parser.constructs.attentionMarkers.null\n  const previous = this.previous\n  const before = classifyCharacter(previous)\n\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Before a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(\n      code === codes.asterisk || code === codes.underscore,\n      'expected asterisk or underscore'\n    )\n    marker = code\n    effects.enter('attentionSequence')\n    return inside(code)\n  }\n\n  /**\n   * In a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    const token = effects.exit('attentionSequence')\n\n    // To do: next major: move this to resolver, just like `markdown-rs`.\n    const after = classifyCharacter(code)\n\n    // Always populated by defaults.\n    assert(attentionMarkers, 'expected `attentionMarkers` to be populated')\n\n    const open =\n      !after ||\n      (after === constants.characterGroupPunctuation && before) ||\n      attentionMarkers.includes(code)\n    const close =\n      !before ||\n      (before === constants.characterGroupPunctuation && after) ||\n      attentionMarkers.includes(previous)\n\n    token._open = Boolean(\n      marker === codes.asterisk ? open : open && (before || !close)\n    )\n    token._close = Boolean(\n      marker === codes.asterisk ? close : close && (after || !open)\n    )\n    return ok(code)\n  }\n}\n\n/**\n * Move a point a bit.\n *\n * Note: `move` only works inside lines! It’s not possible to move past other\n * chunks (replacement characters, tabs, or line endings).\n *\n * @param {Point} point\n *   Point.\n * @param {number} offset\n *   Amount to move.\n * @returns {undefined}\n *   Nothing.\n */\nfunction movePoint(point, offset) {\n  point.column += offset\n  point.offset += offset\n  point._bufferIndex += offset\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  asciiAtext,\n  asciiControl\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const autolink = {name: 'autolink', tokenize: tokenizeAutolink}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAutolink(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of an autolink.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *      ^\n   * > | a<<EMAIL>>b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.autolink)\n    effects.enter(types.autolinkMarker)\n    effects.consume(code)\n    effects.exit(types.autolinkMarker)\n    effects.enter(types.autolinkProtocol)\n    return open\n  }\n\n  /**\n   * After `<`, at protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *       ^\n   * > | a<<EMAIL>>b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return schemeOrEmailAtext\n    }\n\n    if (code === codes.atSign) {\n      return nok(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * At second byte of protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeOrEmailAtext(code) {\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      code === codes.plusSign ||\n      code === codes.dash ||\n      code === codes.dot ||\n      asciiAlphanumeric(code)\n    ) {\n      // Count the previous alphabetical from `open` too.\n      size = 1\n      return schemeInsideOrEmailAtext(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * In ambiguous protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeInsideOrEmailAtext(code) {\n    if (code === codes.colon) {\n      effects.consume(code)\n      size = 0\n      return urlInside\n    }\n\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      (code === codes.plusSign ||\n        code === codes.dash ||\n        code === codes.dot ||\n        asciiAlphanumeric(code)) &&\n      size++ < constants.autolinkSchemeSizeMax\n    ) {\n      effects.consume(code)\n      return schemeInsideOrEmailAtext\n    }\n\n    size = 0\n    return emailAtext(code)\n  }\n\n  /**\n   * After protocol, in URL.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function urlInside(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.autolinkProtocol)\n      effects.enter(types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(types.autolinkMarker)\n      effects.exit(types.autolink)\n      return ok\n    }\n\n    // ASCII control, space, or `<`.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.lessThan ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return urlInside\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtext(code) {\n    if (code === codes.atSign) {\n      effects.consume(code)\n      return emailAtSignOrDot\n    }\n\n    if (asciiAtext(code)) {\n      effects.consume(code)\n      return emailAtext\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label, after at-sign or dot.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                 ^       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtSignOrDot(code) {\n    return asciiAlphanumeric(code) ? emailLabel(code) : nok(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are allowed.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailLabel(code) {\n    if (code === codes.dot) {\n      effects.consume(code)\n      size = 0\n      return emailAtSignOrDot\n    }\n\n    if (code === codes.greaterThan) {\n      // Exit, then change the token type.\n      effects.exit(types.autolinkProtocol).type = types.autolinkEmail\n      effects.enter(types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(types.autolinkMarker)\n      effects.exit(types.autolink)\n      return ok\n    }\n\n    return emailValue(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are *not* allowed.\n   *\n   * Though, this is also used in `emailLabel` to parse other values.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailValue(code) {\n    // ASCII alphanumeric or `-`.\n    if (\n      (code === codes.dash || asciiAlphanumeric(code)) &&\n      size++ < constants.autolinkDomainSizeMax\n    ) {\n      const next = code === codes.dash ? emailValue : emailLabel\n      effects.consume(code)\n      return next\n    }\n\n    return nok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const blankLine = {partial: true, tokenize: tokenizeBlankLine}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLine(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of blank line.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *     ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    return markdownSpace(code)\n      ? factorySpace(effects, after, types.linePrefix)(code)\n      : after(code)\n  }\n\n  /**\n   * At eof/eol, after optional whitespace.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *       ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   Exiter,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const blockQuote = {\n  continuation: {tokenize: tokenizeBlockQuoteContinuation},\n  exit,\n  name: 'blockQuote',\n  tokenize: tokenizeBlockQuoteStart\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of block quote.\n   *\n   * ```markdown\n   * > | > a\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.greaterThan) {\n      const state = self.containerState\n\n      assert(state, 'expected `containerState` to be defined in container')\n\n      if (!state.open) {\n        effects.enter(types.blockQuote, {_container: true})\n        state.open = true\n      }\n\n      effects.enter(types.blockQuotePrefix)\n      effects.enter(types.blockQuoteMarker)\n      effects.consume(code)\n      effects.exit(types.blockQuoteMarker)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>`, before optional whitespace.\n   *\n   * ```markdown\n   * > | > a\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.blockQuotePrefixWhitespace)\n      effects.consume(code)\n      effects.exit(types.blockQuotePrefixWhitespace)\n      effects.exit(types.blockQuotePrefix)\n      return ok\n    }\n\n    effects.exit(types.blockQuotePrefix)\n    return ok(code)\n  }\n}\n\n/**\n * Start of block quote continuation.\n *\n * ```markdown\n *   | > a\n * > | > b\n *     ^\n * ```\n *\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteContinuation(effects, ok, nok) {\n  const self = this\n\n  return contStart\n\n  /**\n   * Start of block quote continuation.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contStart(code) {\n    if (markdownSpace(code)) {\n      // Always populated by defaults.\n      assert(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      return factorySpace(\n        effects,\n        contBefore,\n        types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : constants.tabSize\n      )(code)\n    }\n\n    return contBefore(code)\n  }\n\n  /**\n   * At `>`, after optional whitespace.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contBefore(code) {\n    return effects.attempt(blockQuote, ok, nok)(code)\n  }\n}\n\n/** @type {Exiter} */\nfunction exit(effects) {\n  effects.exit(types.blockQuote)\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {asciiPunctuation} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const characterEscape = {\n  name: 'characterEscape',\n  tokenize: tokenizeCharacterEscape\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of character escape.\n   *\n   * ```markdown\n   * > | a\\*b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`')\n    effects.enter(types.characterEscape)\n    effects.enter(types.escapeMarker)\n    effects.consume(code)\n    effects.exit(types.escapeMarker)\n    return inside\n  }\n\n  /**\n   * After `\\`, at punctuation.\n   *\n   * ```markdown\n   * > | a\\*b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // ASCII punctuation.\n    if (asciiPunctuation(code)) {\n      effects.enter(types.characterEscapeValue)\n      effects.consume(code)\n      effects.exit(types.characterEscapeValue)\n      effects.exit(types.characterEscape)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {\n  asciiAlphanumeric,\n  asciiDigit,\n  asciiHexDigit\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const characterReference = {\n  name: 'characterReference',\n  tokenize: tokenizeCharacterReference\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterReference(effects, ok, nok) {\n  const self = this\n  let size = 0\n  /** @type {number} */\n  let max\n  /** @type {(code: Code) => boolean} */\n  let test\n\n  return start\n\n  /**\n   * Start of character reference.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *      ^\n   * > | a&#123;b\n   *      ^\n   * > | a&#x9;b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.ampersand, 'expected `&`')\n    effects.enter(types.characterReference)\n    effects.enter(types.characterReferenceMarker)\n    effects.consume(code)\n    effects.exit(types.characterReferenceMarker)\n    return open\n  }\n\n  /**\n   * After `&`, at `#` for numeric references or alphanumeric for named\n   * references.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^\n   * > | a&#123;b\n   *       ^\n   * > | a&#x9;b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.characterReferenceMarkerNumeric)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarkerNumeric)\n      return numeric\n    }\n\n    effects.enter(types.characterReferenceValue)\n    max = constants.characterReferenceNamedSizeMax\n    test = asciiAlphanumeric\n    return value(code)\n  }\n\n  /**\n   * After `#`, at `x` for hexadecimals or digit for decimals.\n   *\n   * ```markdown\n   * > | a&#123;b\n   *        ^\n   * > | a&#x9;b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function numeric(code) {\n    if (code === codes.uppercaseX || code === codes.lowercaseX) {\n      effects.enter(types.characterReferenceMarkerHexadecimal)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarkerHexadecimal)\n      effects.enter(types.characterReferenceValue)\n      max = constants.characterReferenceHexadecimalSizeMax\n      test = asciiHexDigit\n      return value\n    }\n\n    effects.enter(types.characterReferenceValue)\n    max = constants.characterReferenceDecimalSizeMax\n    test = asciiDigit\n    return value(code)\n  }\n\n  /**\n   * After markers (`&#x`, `&#`, or `&`), in value, before `;`.\n   *\n   * The character reference kind defines what and how many characters are\n   * allowed.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^^^\n   * > | a&#123;b\n   *        ^^^\n   * > | a&#x9;b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function value(code) {\n    if (code === codes.semicolon && size) {\n      const token = effects.exit(types.characterReferenceValue)\n\n      if (\n        test === asciiAlphanumeric &&\n        !decodeNamedCharacterReference(self.sliceSerialize(token))\n      ) {\n        return nok(code)\n      }\n\n      // To do: `markdown-rs` uses a different name:\n      // `CharacterReferenceMarkerSemi`.\n      effects.enter(types.characterReferenceMarker)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarker)\n      effects.exit(types.characterReference)\n      return ok\n    }\n\n    if (test(code) && size++ < max) {\n      effects.consume(code)\n      return value\n    }\n\n    return nok(code)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuation\n}\n\n/** @type {Construct} */\nexport const codeFenced = {\n  concrete: true,\n  name: 'codeFenced',\n  tokenize: tokenizeCodeFenced\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeFenced(effects, ok, nok) {\n  const self = this\n  /** @type {Construct} */\n  const closeStart = {partial: true, tokenize: tokenizeCloseStart}\n  let initialPrefix = 0\n  let sizeOpen = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of code.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse whitespace like `markdown-rs`.\n    return beforeSequenceOpen(code)\n  }\n\n  /**\n   * In opening fence, after prefix, at sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeSequenceOpen(code) {\n    assert(\n      code === codes.graveAccent || code === codes.tilde,\n      'expected `` ` `` or `~`'\n    )\n\n    const tail = self.events[self.events.length - 1]\n    initialPrefix =\n      tail && tail[1].type === types.linePrefix\n        ? tail[2].sliceSerialize(tail[1], true).length\n        : 0\n\n    marker = code\n    effects.enter(types.codeFenced)\n    effects.enter(types.codeFencedFence)\n    effects.enter(types.codeFencedFenceSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *      ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === marker) {\n      sizeOpen++\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    if (sizeOpen < constants.codeFencedSequenceSizeMin) {\n      return nok(code)\n    }\n\n    effects.exit(types.codeFencedFenceSequence)\n    return markdownSpace(code)\n      ? factorySpace(effects, infoBefore, types.whitespace)(code)\n      : infoBefore(code)\n  }\n\n  /**\n   * In opening fence, after the sequence (and optional whitespace), before info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function infoBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFencedFence)\n      return self.interrupt\n        ? ok(code)\n        : effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(types.codeFencedFenceInfo)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return info(code)\n  }\n\n  /**\n   * In info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function info(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceInfo)\n      return infoBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceInfo)\n      return factorySpace(effects, metaBefore, types.whitespace)(code)\n    }\n\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return info\n  }\n\n  /**\n   * In opening fence, after info and whitespace, before meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function metaBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return infoBefore(code)\n    }\n\n    effects.enter(types.codeFencedFenceMeta)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return meta(code)\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceMeta)\n      return infoBefore(code)\n    }\n\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return meta\n  }\n\n  /**\n   * At eol/eof in code, before a non-lazy closing fence or content.\n   *\n   * ```markdown\n   * > | ~~~js\n   *          ^\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function atNonLazyBreak(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    return effects.attempt(closeStart, after, contentBefore)(code)\n  }\n\n  /**\n   * Before code content, not a closing fence, at eol.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentBefore(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return contentStart\n  }\n\n  /**\n   * Before code content, not a closing fence.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return initialPrefix > 0 && markdownSpace(code)\n      ? factorySpace(\n          effects,\n          beforeContentChunk,\n          types.linePrefix,\n          initialPrefix + 1\n        )(code)\n      : beforeContentChunk(code)\n  }\n\n  /**\n   * Before code content, after optional prefix.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(types.codeFlowValue)\n    return contentChunk(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^^^^^^^^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue)\n      return beforeContentChunk(code)\n    }\n\n    effects.consume(code)\n    return contentChunk\n  }\n\n  /**\n   * After code.\n   *\n   * ```markdown\n   *   | ~~~js\n   *   | alert(1)\n   * > | ~~~\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit(types.codeFenced)\n    return ok(code)\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Tokenizer}\n   */\n  function tokenizeCloseStart(effects, ok, nok) {\n    let size = 0\n\n    return startBefore\n\n    /**\n     *\n     *\n     * @type {State}\n     */\n    function startBefore(code) {\n      assert(markdownLineEnding(code), 'expected eol')\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return start\n    }\n\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      // Always populated by defaults.\n      assert(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      // To do: `enter` here or in next state?\n      effects.enter(types.codeFencedFence)\n      return markdownSpace(code)\n        ? factorySpace(\n            effects,\n            beforeSequenceClose,\n            types.linePrefix,\n            self.parser.constructs.disable.null.includes('codeIndented')\n              ? undefined\n              : constants.tabSize\n          )(code)\n        : beforeSequenceClose(code)\n    }\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      if (code === marker) {\n        effects.enter(types.codeFencedFenceSequence)\n        return sequenceClose(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === marker) {\n        size++\n        effects.consume(code)\n        return sequenceClose\n      }\n\n      if (size >= sizeOpen) {\n        effects.exit(types.codeFencedFenceSequence)\n        return markdownSpace(code)\n          ? factorySpace(effects, sequenceCloseAfter, types.whitespace)(code)\n          : sequenceCloseAfter(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceCloseAfter(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        effects.exit(types.codeFencedFence)\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return lineStart\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const codeIndented = {\n  name: 'codeIndented',\n  tokenize: tokenizeCodeIndented\n}\n\n/** @type {Construct} */\nconst furtherStart = {partial: true, tokenize: tokenizeFurtherStart}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeIndented(effects, ok, nok) {\n  const self = this\n  return start\n\n  /**\n   * Start of code (indented).\n   *\n   * > **Parsing note**: it is not needed to check if this first line is a\n   * > filled line (that it has a non-whitespace character), because blank lines\n   * > are parsed already, so we never run into that.\n   *\n   * ```markdown\n   * > |     aaa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: manually check if interrupting like `markdown-rs`.\n    assert(markdownSpace(code))\n    effects.enter(types.codeIndented)\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return factorySpace(\n      effects,\n      afterPrefix,\n      types.linePrefix,\n      constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n      ? atBreak(code)\n      : nok(code)\n  }\n\n  /**\n   * At a break.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === codes.eof) {\n      return after(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      return effects.attempt(furtherStart, atBreak, after)(code)\n    }\n\n    effects.enter(types.codeFlowValue)\n    return inside(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return inside\n  }\n\n  /** @type {State} */\n  function after(code) {\n    effects.exit(types.codeIndented)\n    // To do: allow interrupting like `markdown-rs`.\n    // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeFurtherStart(effects, ok, nok) {\n  const self = this\n\n  return furtherStart\n\n  /**\n   * At eol, trying to parse another indent.\n   *\n   * ```markdown\n   * > |     aaa\n   *            ^\n   *   |     bbb\n   * ```\n   *\n   * @type {State}\n   */\n  function furtherStart(code) {\n    // To do: improve `lazy` / `pierce` handling.\n    // If this is a lazy line, it can’t be code.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return furtherStart\n    }\n\n    // To do: the code here in `micromark-js` is a bit different from\n    // `markdown-rs` because there it can attempt spaces.\n    // We can’t yet.\n    //\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return factorySpace(\n      effects,\n      afterPrefix,\n      types.linePrefix,\n      constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n      ? ok(code)\n      : markdownLineEnding(code)\n        ? furtherStart(code)\n        : nok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   Previous,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const codeText = {\n  name: 'codeText',\n  previous,\n  resolve: resolveCodeText,\n  tokenize: tokenizeCodeText\n}\n\n// To do: next major: don’t resolve, like `markdown-rs`.\n/** @type {Resolver} */\nfunction resolveCodeText(events) {\n  let tailExitIndex = events.length - 4\n  let headEnterIndex = 3\n  /** @type {number} */\n  let index\n  /** @type {number | undefined} */\n  let enter\n\n  // If we start and end with an EOL or a space.\n  if (\n    (events[headEnterIndex][1].type === types.lineEnding ||\n      events[headEnterIndex][1].type === 'space') &&\n    (events[tailExitIndex][1].type === types.lineEnding ||\n      events[tailExitIndex][1].type === 'space')\n  ) {\n    index = headEnterIndex\n\n    // And we have data.\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === types.codeTextData) {\n        // Then we have padding.\n        events[headEnterIndex][1].type = types.codeTextPadding\n        events[tailExitIndex][1].type = types.codeTextPadding\n        headEnterIndex += 2\n        tailExitIndex -= 2\n        break\n      }\n    }\n  }\n\n  // Merge adjacent spaces and data.\n  index = headEnterIndex - 1\n  tailExitIndex++\n\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (\n        index !== tailExitIndex &&\n        events[index][1].type !== types.lineEnding\n      ) {\n        enter = index\n      }\n    } else if (\n      index === tailExitIndex ||\n      events[index][1].type === types.lineEnding\n    ) {\n      events[enter][1].type = types.codeTextData\n\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end\n        events.splice(enter + 2, index - enter - 2)\n        tailExitIndex -= index - enter - 2\n        index = enter + 2\n      }\n\n      enter = undefined\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Previous}\n */\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return (\n    code !== codes.graveAccent ||\n    this.events[this.events.length - 1][1].type === types.characterEscape\n  )\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeText(effects, ok, nok) {\n  const self = this\n  let sizeOpen = 0\n  /** @type {number} */\n  let size\n  /** @type {Token} */\n  let token\n\n  return start\n\n  /**\n   * Start of code (text).\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * > | \\`a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.graveAccent, 'expected `` ` ``')\n    assert(previous.call(self, self.previous), 'expected correct previous')\n    effects.enter(types.codeText)\n    effects.enter(types.codeTextSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === codes.graveAccent) {\n      effects.consume(code)\n      sizeOpen++\n      return sequenceOpen\n    }\n\n    effects.exit(types.codeTextSequence)\n    return between(code)\n  }\n\n  /**\n   * Between something and something else.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function between(code) {\n    // EOF.\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    // To do: next major: don’t do spaces in resolve, but when compiling,\n    // like `markdown-rs`.\n    // Tabs don’t work, and virtual spaces don’t make sense.\n    if (code === codes.space) {\n      effects.enter('space')\n      effects.consume(code)\n      effects.exit('space')\n      return between\n    }\n\n    // Closing fence? Could also be data.\n    if (code === codes.graveAccent) {\n      token = effects.enter(types.codeTextSequence)\n      size = 0\n      return sequenceClose(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return between\n    }\n\n    // Data.\n    effects.enter(types.codeTextData)\n    return data(code)\n  }\n\n  /**\n   * In data.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.graveAccent ||\n      markdownLineEnding(code)\n    ) {\n      effects.exit(types.codeTextData)\n      return between(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n\n  /**\n   * In closing sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceClose(code) {\n    // More.\n    if (code === codes.graveAccent) {\n      effects.consume(code)\n      size++\n      return sequenceClose\n    }\n\n    // Done!\n    if (size === sizeOpen) {\n      effects.exit(types.codeTextSequence)\n      effects.exit(types.codeText)\n      return ok(code)\n    }\n\n    // More or less accents: mark as data.\n    token.type = types.codeTextData\n    return data(code)\n  }\n}\n", "import {constants} from 'micromark-util-symbol'\n\n/**\n * Some of the internal operations of micromark do lots of editing\n * operations on very large arrays. This runs into problems with two\n * properties of most circa-2020 JavaScript interpreters:\n *\n *  - Array-length modifications at the high end of an array (push/pop) are\n *    expected to be common and are implemented in (amortized) time\n *    proportional to the number of elements added or removed, whereas\n *    other operations (shift/unshift and splice) are much less efficient.\n *  - Function arguments are passed on the stack, so adding tens of thousands\n *    of elements to an array with `arr.push(...newElements)` will frequently\n *    cause stack overflows. (see <https://stackoverflow.com/questions/22123769/rangeerror-maximum-call-stack-size-exceeded-why>)\n *\n * SpliceBuffers are an implementation of gap buffers, which are a\n * generalization of the \"queue made of two stacks\" idea. The splice buffer\n * maintains a cursor, and moving the cursor has cost proportional to the\n * distance the cursor moves, but inserting, deleting, or splicing in\n * new information at the cursor is as efficient as the push/pop operation.\n * This allows for an efficient sequence of splices (or pushes, pops, shifts,\n * or unshifts) as long such edits happen at the same part of the array or\n * generally sweep through the array from the beginning to the end.\n *\n * The interface for splice buffers also supports large numbers of inputs by\n * passing a single array argument rather passing multiple arguments on the\n * function call stack.\n *\n * @template T\n *   Item type.\n */\nexport class SpliceBuffer {\n  /**\n   * @param {ReadonlyArray<T> | null | undefined} [initial]\n   *   Initial items (optional).\n   * @returns\n   *   Splice buffer.\n   */\n  constructor(initial) {\n    /** @type {Array<T>} */\n    this.left = initial ? [...initial] : []\n    /** @type {Array<T>} */\n    this.right = []\n  }\n\n  /**\n   * Array access;\n   * does not move the cursor.\n   *\n   * @param {number} index\n   *   Index.\n   * @return {T}\n   *   Item.\n   */\n  get(index) {\n    if (index < 0 || index >= this.left.length + this.right.length) {\n      throw new RangeError(\n        'Cannot access index `' +\n          index +\n          '` in a splice buffer of size `' +\n          (this.left.length + this.right.length) +\n          '`'\n      )\n    }\n\n    if (index < this.left.length) return this.left[index]\n    return this.right[this.right.length - index + this.left.length - 1]\n  }\n\n  /**\n   * The length of the splice buffer, one greater than the largest index in the\n   * array.\n   */\n  get length() {\n    return this.left.length + this.right.length\n  }\n\n  /**\n   * Remove and return `list[0]`;\n   * moves the cursor to `0`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  shift() {\n    this.setCursor(0)\n    return this.right.pop()\n  }\n\n  /**\n   * Slice the buffer to get an array;\n   * does not move the cursor.\n   *\n   * @param {number} start\n   *   Start.\n   * @param {number | null | undefined} [end]\n   *   End (optional).\n   * @returns {Array<T>}\n   *   Array of items.\n   */\n  slice(start, end) {\n    /** @type {number} */\n    const stop =\n      end === null || end === undefined ? Number.POSITIVE_INFINITY : end\n\n    if (stop < this.left.length) {\n      return this.left.slice(start, stop)\n    }\n\n    if (start > this.left.length) {\n      return this.right\n        .slice(\n          this.right.length - stop + this.left.length,\n          this.right.length - start + this.left.length\n        )\n        .reverse()\n    }\n\n    return this.left\n      .slice(start)\n      .concat(\n        this.right.slice(this.right.length - stop + this.left.length).reverse()\n      )\n  }\n\n  /**\n   * Mimics the behavior of Array.prototype.splice() except for the change of\n   * interface necessary to avoid segfaults when patching in very large arrays.\n   *\n   * This operation moves cursor is moved to `start` and results in the cursor\n   * placed after any inserted items.\n   *\n   * @param {number} start\n   *   Start;\n   *   zero-based index at which to start changing the array;\n   *   negative numbers count backwards from the end of the array and values\n   *   that are out-of bounds are clamped to the appropriate end of the array.\n   * @param {number | null | undefined} [deleteCount=0]\n   *   Delete count (default: `0`);\n   *   maximum number of elements to delete, starting from start.\n   * @param {Array<T> | null | undefined} [items=[]]\n   *   Items to include in place of the deleted items (default: `[]`).\n   * @return {Array<T>}\n   *   Any removed items.\n   */\n  splice(start, deleteCount, items) {\n    /** @type {number} */\n    const count = deleteCount || 0\n\n    this.setCursor(Math.trunc(start))\n    const removed = this.right.splice(\n      this.right.length - count,\n      Number.POSITIVE_INFINITY\n    )\n    if (items) chunkedPush(this.left, items)\n    return removed.reverse()\n  }\n\n  /**\n   * Remove and return the highest-numbered item in the array, so\n   * `list[list.length - 1]`;\n   * Moves the cursor to `length`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  pop() {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    return this.left.pop()\n  }\n\n  /**\n   * Inserts a single item to the high-numbered side of the array;\n   * moves the cursor to `length`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  push(item) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    this.left.push(item)\n  }\n\n  /**\n   * Inserts many items to the high-numbered side of the array.\n   * Moves the cursor to `length`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  pushMany(items) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    chunkedPush(this.left, items)\n  }\n\n  /**\n   * Inserts a single item to the low-numbered side of the array;\n   * Moves the cursor to `0`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshift(item) {\n    this.setCursor(0)\n    this.right.push(item)\n  }\n\n  /**\n   * Inserts many items to the low-numbered side of the array;\n   * moves the cursor to `0`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshiftMany(items) {\n    this.setCursor(0)\n    chunkedPush(this.right, items.reverse())\n  }\n\n  /**\n   * Move the cursor to a specific position in the array. Requires\n   * time proportional to the distance moved.\n   *\n   * If `n < 0`, the cursor will end up at the beginning.\n   * If `n > length`, the cursor will end up at the end.\n   *\n   * @param {number} n\n   *   Position.\n   * @return {undefined}\n   *   Nothing.\n   */\n  setCursor(n) {\n    if (\n      n === this.left.length ||\n      (n > this.left.length && this.right.length === 0) ||\n      (n < 0 && this.left.length === 0)\n    )\n      return\n    if (n < this.left.length) {\n      // Move cursor to the this.left\n      const removed = this.left.splice(n, Number.POSITIVE_INFINITY)\n      chunkedPush(this.right, removed.reverse())\n    } else {\n      // Move cursor to the this.right\n      const removed = this.right.splice(\n        this.left.length + this.right.length - n,\n        Number.POSITIVE_INFINITY\n      )\n      chunkedPush(this.left, removed.reverse())\n    }\n  }\n}\n\n/**\n * Avoid stack overflow by pushing items onto the stack in segments\n *\n * @template T\n *   Item type.\n * @param {Array<T>} list\n *   List to inject into.\n * @param {ReadonlyArray<T>} right\n *   Items to inject.\n * @return {undefined}\n *   Nothing.\n */\nfunction chunkedPush(list, right) {\n  /** @type {number} */\n  let chunkStart = 0\n\n  if (right.length < constants.v8MaxSafeChunkSize) {\n    list.push(...right)\n  } else {\n    while (chunkStart < right.length) {\n      list.push(\n        ...right.slice(chunkStart, chunkStart + constants.v8MaxSafeChunkSize)\n      )\n      chunkStart += constants.v8MaxSafeChunkSize\n    }\n  }\n}\n", "/**\n * @import {Chunk, Event, Token} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {splice} from 'micromark-util-chunked'\nimport {codes, types} from 'micromark-util-symbol'\nimport {<PERSON>plice<PERSON>uffer} from './lib/splice-buffer.js'\n\n// Hidden API exposed for testing.\nexport {SpliceBuffer} from './lib/splice-buffer.js'\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} eventsArray\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */\n// eslint-disable-next-line complexity\nexport function subtokenize(eventsArray) {\n  /** @type {Record<string, number>} */\n  const jumps = {}\n  let index = -1\n  /** @type {Event} */\n  let event\n  /** @type {number | undefined} */\n  let lineIndex\n  /** @type {number} */\n  let otherIndex\n  /** @type {Event} */\n  let otherEvent\n  /** @type {Array<Event>} */\n  let parameters\n  /** @type {Array<Event>} */\n  let subevents\n  /** @type {boolean | undefined} */\n  let more\n  const events = new SpliceBuffer(eventsArray)\n\n  while (++index < events.length) {\n    while (index in jumps) {\n      index = jumps[index]\n    }\n\n    event = events.get(index)\n\n    // Add a hook for the GFM tasklist extension, which needs to know if text\n    // is in the first content of a list item.\n    if (\n      index &&\n      event[1].type === types.chunkFlow &&\n      events.get(index - 1)[1].type === types.listItemPrefix\n    ) {\n      assert(event[1]._tokenizer, 'expected `_tokenizer` on subtokens')\n      subevents = event[1]._tokenizer.events\n      otherIndex = 0\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === types.lineEndingBlank\n      ) {\n        otherIndex += 2\n      }\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === types.content\n      ) {\n        while (++otherIndex < subevents.length) {\n          if (subevents[otherIndex][1].type === types.content) {\n            break\n          }\n\n          if (subevents[otherIndex][1].type === types.chunkText) {\n            subevents[otherIndex][1]._isInFirstContentOfListItem = true\n            otherIndex++\n          }\n        }\n      }\n    }\n\n    // Enter.\n    if (event[0] === 'enter') {\n      if (event[1].contentType) {\n        Object.assign(jumps, subcontent(events, index))\n        index = jumps[index]\n        more = true\n      }\n    }\n    // Exit.\n    else if (event[1]._container) {\n      otherIndex = index\n      lineIndex = undefined\n\n      while (otherIndex--) {\n        otherEvent = events.get(otherIndex)\n\n        if (\n          otherEvent[1].type === types.lineEnding ||\n          otherEvent[1].type === types.lineEndingBlank\n        ) {\n          if (otherEvent[0] === 'enter') {\n            if (lineIndex) {\n              events.get(lineIndex)[1].type = types.lineEndingBlank\n            }\n\n            otherEvent[1].type = types.lineEnding\n            lineIndex = otherIndex\n          }\n        } else if (\n          otherEvent[1].type === types.linePrefix ||\n          otherEvent[1].type === types.listItemIndent\n        ) {\n          // Move past.\n        } else {\n          break\n        }\n      }\n\n      if (lineIndex) {\n        // Fix position.\n        event[1].end = {...events.get(lineIndex)[1].start}\n\n        // Switch container exit w/ line endings.\n        parameters = events.slice(lineIndex, index)\n        parameters.unshift(event)\n        events.splice(lineIndex, index - lineIndex + 1, parameters)\n      }\n    }\n  }\n\n  // The changes to the `events` buffer must be copied back into the eventsArray\n  splice(eventsArray, 0, Number.POSITIVE_INFINITY, events.slice(0))\n  return !more\n}\n\n/**\n * Tokenize embedded tokens.\n *\n * @param {SpliceBuffer<Event>} events\n *   Events.\n * @param {number} eventIndex\n *   Index.\n * @returns {Record<string, number>}\n *   Gaps.\n */\nfunction subcontent(events, eventIndex) {\n  const token = events.get(eventIndex)[1]\n  const context = events.get(eventIndex)[2]\n  let startPosition = eventIndex - 1\n  /** @type {Array<number>} */\n  const startPositions = []\n  assert(token.contentType, 'expected `contentType` on subtokens')\n\n  let tokenizer = token._tokenizer\n\n  if (!tokenizer) {\n    tokenizer = context.parser[token.contentType](token.start)\n\n    if (token._contentTypeTextTrailing) {\n      tokenizer._contentTypeTextTrailing = true\n    }\n  }\n\n  const childEvents = tokenizer.events\n  /** @type {Array<[number, number]>} */\n  const jumps = []\n  /** @type {Record<string, number>} */\n  const gaps = {}\n  /** @type {Array<Chunk>} */\n  let stream\n  /** @type {Token | undefined} */\n  let previous\n  let index = -1\n  /** @type {Token | undefined} */\n  let current = token\n  let adjust = 0\n  let start = 0\n  const breaks = [start]\n\n  // Loop forward through the linked tokens to pass them in order to the\n  // subtokenizer.\n  while (current) {\n    // Find the position of the event for this token.\n    while (events.get(++startPosition)[1] !== current) {\n      // Empty.\n    }\n\n    assert(\n      !previous || current.previous === previous,\n      'expected previous to match'\n    )\n    assert(!previous || previous.next === current, 'expected next to match')\n\n    startPositions.push(startPosition)\n\n    if (!current._tokenizer) {\n      stream = context.sliceStream(current)\n\n      if (!current.next) {\n        stream.push(codes.eof)\n      }\n\n      if (previous) {\n        tokenizer.defineSkip(current.start)\n      }\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = true\n      }\n\n      tokenizer.write(stream)\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = undefined\n      }\n    }\n\n    // Unravel the next token.\n    previous = current\n    current = current.next\n  }\n\n  // Now, loop back through all events (and linked tokens), to figure out which\n  // parts belong where.\n  current = token\n\n  while (++index < childEvents.length) {\n    if (\n      // Find a void token that includes a break.\n      childEvents[index][0] === 'exit' &&\n      childEvents[index - 1][0] === 'enter' &&\n      childEvents[index][1].type === childEvents[index - 1][1].type &&\n      childEvents[index][1].start.line !== childEvents[index][1].end.line\n    ) {\n      assert(current, 'expected a current token')\n      start = index + 1\n      breaks.push(start)\n      // Help GC.\n      current._tokenizer = undefined\n      current.previous = undefined\n      current = current.next\n    }\n  }\n\n  // Help GC.\n  tokenizer.events = []\n\n  // If there’s one more token (which is the cases for lines that end in an\n  // EOF), that’s perfect: the last point we found starts it.\n  // If there isn’t then make sure any remaining content is added to it.\n  if (current) {\n    // Help GC.\n    current._tokenizer = undefined\n    current.previous = undefined\n    assert(!current.next, 'expected no next token')\n  } else {\n    breaks.pop()\n  }\n\n  // Now splice the events from the subtokenizer into the current events,\n  // moving back to front so that splice indices aren’t affected.\n  index = breaks.length\n\n  while (index--) {\n    const slice = childEvents.slice(breaks[index], breaks[index + 1])\n    const start = startPositions.pop()\n    assert(start !== undefined, 'expected a start position when splicing')\n    jumps.push([start, start + slice.length - 1])\n    events.splice(start, 2, slice)\n  }\n\n  jumps.reverse()\n  index = -1\n\n  while (++index < jumps.length) {\n    gaps[adjust + jumps[index][0]] = adjust + jumps[index][1]\n    adjust += jumps[index][1] - jumps[index][0] - 1\n  }\n\n  return gaps\n}\n", "/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {subtokenize} from 'micromark-util-subtokenize'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * No name because it must not be turned off.\n * @type {Construct}\n */\nexport const content = {resolve: resolveContent, tokenize: tokenizeContent}\n\n/** @type {Construct} */\nconst continuationConstruct = {partial: true, tokenize: tokenizeContinuation}\n\n/**\n * Content is transparent: it’s parsed right now. That way, definitions are also\n * parsed right now: before text in paragraphs (specifically, media) are parsed.\n *\n * @type {Resolver}\n */\nfunction resolveContent(events) {\n  subtokenize(events)\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContent(effects, ok) {\n  /** @type {Token | undefined} */\n  let previous\n\n  return chunkStart\n\n  /**\n   * Before a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkStart(code) {\n    assert(\n      code !== codes.eof && !markdownLineEnding(code),\n      'expected no eof or eol'\n    )\n\n    effects.enter(types.content)\n    previous = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent\n    })\n    return chunkInside(code)\n  }\n\n  /**\n   * In a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkInside(code) {\n    if (code === codes.eof) {\n      return contentEnd(code)\n    }\n\n    // To do: in `markdown-rs`, each line is parsed on its own, and everything\n    // is stitched together resolving.\n    if (markdownLineEnding(code)) {\n      return effects.check(\n        continuationConstruct,\n        contentContinue,\n        contentEnd\n      )(code)\n    }\n\n    // Data.\n    effects.consume(code)\n    return chunkInside\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentEnd(code) {\n    effects.exit(types.chunkContent)\n    effects.exit(types.content)\n    return ok(code)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentContinue(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.consume(code)\n    effects.exit(types.chunkContent)\n    assert(previous, 'expected previous token')\n    previous.next = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent,\n      previous\n    })\n    previous = previous.next\n    return chunkInside\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContinuation(effects, ok, nok) {\n  const self = this\n\n  return startLookahead\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function startLookahead(code) {\n    assert(markdownLineEnding(code), 'expected a line ending')\n    effects.exit(types.chunkContent)\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, prefixed, types.linePrefix)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function prefixed(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n\n    const tail = self.events[self.events.length - 1]\n\n    if (\n      !self.parser.constructs.disable.null.includes('codeIndented') &&\n      tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n    ) {\n      return ok(code)\n    }\n\n    return effects.interrupt(self.parser.constructs.flow, nok, ok)(code)\n  }\n}\n", "/**\n * @import {Effects, State, TokenType} from 'micromark-util-types'\n */\n\nimport {\n  asciiControl,\n  markdownLineEndingOrSpace,\n  markdownLineEnding\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * Parse destinations.\n *\n * ###### Examples\n *\n * ```markdown\n * <a>\n * <a\\>b>\n * <a b>\n * <a)>\n * a\n * a\\)b\n * a(b)c\n * a(b)\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type for whole (`<a>` or `b`).\n * @param {TokenType} literalType\n *   Type when enclosed (`<a>`).\n * @param {TokenType} literalMarkerType\n *   Type for enclosing (`<` and `>`).\n * @param {TokenType} rawType\n *   Type when not enclosed (`b`).\n * @param {TokenType} stringType\n *   Type for the value (`a` or `b`).\n * @param {number | undefined} [max=Infinity]\n *   Depth of nested parens (inclusive).\n * @returns {State}\n *   Start state.\n */\nexport function factoryDestination(\n  effects,\n  ok,\n  nok,\n  type,\n  literalType,\n  literalMarkerType,\n  rawType,\n  stringType,\n  max\n) {\n  const limit = max || Number.POSITIVE_INFINITY\n  let balance = 0\n\n  return start\n\n  /**\n   * Start of destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *     ^\n   * > | aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.lessThan) {\n      effects.enter(type)\n      effects.enter(literalType)\n      effects.enter(literalMarkerType)\n      effects.consume(code)\n      effects.exit(literalMarkerType)\n      return enclosedBefore\n    }\n\n    // ASCII control, space, closing paren.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.rightParenthesis ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter(type)\n    effects.enter(rawType)\n    effects.enter(stringType)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return raw(code)\n  }\n\n  /**\n   * After `<`, at an enclosed destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosedBefore(code) {\n    if (code === codes.greaterThan) {\n      effects.enter(literalMarkerType)\n      effects.consume(code)\n      effects.exit(literalMarkerType)\n      effects.exit(literalType)\n      effects.exit(type)\n      return ok\n    }\n\n    effects.enter(stringType)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return enclosed(code)\n  }\n\n  /**\n   * In enclosed destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosed(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.chunkString)\n      effects.exit(stringType)\n      return enclosedBefore(code)\n    }\n\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      markdownLineEnding(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? enclosedEscape : enclosed\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | <a\\*a>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosedEscape(code) {\n    if (\n      code === codes.lessThan ||\n      code === codes.greaterThan ||\n      code === codes.backslash\n    ) {\n      effects.consume(code)\n      return enclosed\n    }\n\n    return enclosed(code)\n  }\n\n  /**\n   * In raw destination.\n   *\n   * ```markdown\n   * > | aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function raw(code) {\n    if (\n      !balance &&\n      (code === codes.eof ||\n        code === codes.rightParenthesis ||\n        markdownLineEndingOrSpace(code))\n    ) {\n      effects.exit(types.chunkString)\n      effects.exit(stringType)\n      effects.exit(rawType)\n      effects.exit(type)\n      return ok(code)\n    }\n\n    if (balance < limit && code === codes.leftParenthesis) {\n      effects.consume(code)\n      balance++\n      return raw\n    }\n\n    if (code === codes.rightParenthesis) {\n      effects.consume(code)\n      balance--\n      return raw\n    }\n\n    // ASCII control (but *not* `\\0`) and space and `(`.\n    // Note: in `markdown-rs`, `\\0` exists in codes, in `micromark-js` it\n    // doesn’t.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.leftParenthesis ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? rawEscape : raw\n  }\n\n  /**\n   * After `\\`, at special character.\n   *\n   * ```markdown\n   * > | a\\*a\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function rawEscape(code) {\n    if (\n      code === codes.leftParenthesis ||\n      code === codes.rightParenthesis ||\n      code === codes.backslash\n    ) {\n      effects.consume(code)\n      return raw\n    }\n\n    return raw(code)\n  }\n}\n", "/**\n * @import {\n *   Effects,\n *   State,\n *   TokenizeContext,\n *   TokenType\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * Parse labels.\n *\n * > 👉 **Note**: labels in markdown are capped at 999 characters in the string.\n *\n * ###### Examples\n *\n * ```markdown\n * [a]\n * [a\n * b]\n * [a\\]b]\n * ```\n *\n * @this {TokenizeContext}\n *   Tokenize context.\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type of the whole label (`[a]`).\n * @param {TokenType} markerType\n *   Type for the markers (`[` and `]`).\n * @param {TokenType} stringType\n *   Type for the identifier (`a`).\n * @returns {State}\n *   Start state.\n */\nexport function factoryLabel(effects, ok, nok, type, markerType, stringType) {\n  const self = this\n  let size = 0\n  /** @type {boolean} */\n  let seen\n\n  return start\n\n  /**\n   * Start of label.\n   *\n   * ```markdown\n   * > | [a]\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter(type)\n    effects.enter(markerType)\n    effects.consume(code)\n    effects.exit(markerType)\n    effects.enter(stringType)\n    return atBreak\n  }\n\n  /**\n   * In label, at something, before something else.\n   *\n   * ```markdown\n   * > | [a]\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (\n      size > constants.linkReferenceSizeMax ||\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      (code === codes.rightSquareBracket && !seen) ||\n      // To do: remove in the future once we’ve switched from\n      // `micromark-extension-footnote` to `micromark-extension-gfm-footnote`,\n      // which doesn’t need this.\n      // Hidden footnotes hook.\n      /* c8 ignore next 3 */\n      (code === codes.caret &&\n        !size &&\n        '_hiddenFootnoteSupport' in self.parser.constructs)\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.exit(stringType)\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      effects.exit(type)\n      return ok\n    }\n\n    // To do: indent? Link chunks and EOLs together?\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return atBreak\n    }\n\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return labelInside(code)\n  }\n\n  /**\n   * In label, in text.\n   *\n   * ```markdown\n   * > | [a]\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelInside(code) {\n    if (\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      code === codes.rightSquareBracket ||\n      markdownLineEnding(code) ||\n      size++ > constants.linkReferenceSizeMax\n    ) {\n      effects.exit(types.chunkString)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    if (!seen) seen = !markdownSpace(code)\n    return code === codes.backslash ? labelEscape : labelInside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | [a\\*a]\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEscape(code) {\n    if (\n      code === codes.leftSquareBracket ||\n      code === codes.backslash ||\n      code === codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return labelInside\n    }\n\n    return labelInside(code)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Effects,\n *   State,\n *   TokenType\n * } from 'micromark-util-types'\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * Parse titles.\n *\n * ###### Examples\n *\n * ```markdown\n * \"a\"\n * 'b'\n * (c)\n * \"a\n * b\"\n * 'a\n *     b'\n * (a\\)b)\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type of the whole title (`\"a\"`, `'b'`, `(c)`).\n * @param {TokenType} markerType\n *   Type for the markers (`\"`, `'`, `(`, and `)`).\n * @param {TokenType} stringType\n *   Type for the value (`a`).\n * @returns {State}\n *   Start state.\n */\nexport function factoryTitle(effects, ok, nok, type, markerType, stringType) {\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of title.\n   *\n   * ```markdown\n   * > | \"a\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.leftParenthesis\n    ) {\n      effects.enter(type)\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      marker = code === codes.leftParenthesis ? codes.rightParenthesis : code\n      return begin\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After opening marker.\n   *\n   * This is also used at the closing marker.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function begin(code) {\n    if (code === marker) {\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      effects.exit(type)\n      return ok\n    }\n\n    effects.enter(stringType)\n    return atBreak(code)\n  }\n\n  /**\n   * At something, before something else.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.exit(stringType)\n      return begin(marker)\n    }\n\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    // Note: blank lines can’t exist in content.\n    if (markdownLineEnding(code)) {\n      // To do: use `space_or_tab_eol_with_options`, connect.\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return factorySpace(effects, atBreak, types.linePrefix)\n    }\n\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return inside(code)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker || code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? escape : inside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | \"a\\*b\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function escape(code) {\n    if (code === marker || code === codes.backslash) {\n      effects.consume(code)\n      return inside\n    }\n\n    return inside(code)\n  }\n}\n", "/**\n * @import {Effects, State} from 'micromark-util-types'\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {types} from 'micromark-util-symbol'\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   line endings or spaces in markdown are often optional, in which case this\n *     factory can be used and `ok` will be switched to whether spaces were found\n *     or not\n * *   one line ending or space can be detected with\n *     `markdownLineEndingOrSpace(code)` right before using `factoryWhitespace`\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @returns {State}\n *   Start state.\n */\nexport function factoryWhitespace(effects, ok) {\n  /** @type {boolean} */\n  let seen\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      seen = true\n      return start\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(\n        effects,\n        start,\n        seen ? types.linePrefix : types.lineSuffix\n      )(code)\n    }\n\n    return ok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factoryDestination} from 'micromark-factory-destination'\nimport {factoryLabel} from 'micromark-factory-label'\nimport {factorySpace} from 'micromark-factory-space'\nimport {factoryTitle} from 'micromark-factory-title'\nimport {factoryWhitespace} from 'micromark-factory-whitespace'\nimport {\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const definition = {name: 'definition', tokenize: tokenizeDefinition}\n\n/** @type {Construct} */\nconst titleBefore = {partial: true, tokenize: tokenizeTitleBefore}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeDefinition(effects, ok, nok) {\n  const self = this\n  /** @type {string} */\n  let identifier\n\n  return start\n\n  /**\n   * At start of a definition.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Do not interrupt paragraphs (but do follow definitions).\n    // To do: do `interrupt` the way `markdown-rs` does.\n    // To do: parse whitespace the way `markdown-rs` does.\n    effects.enter(types.definition)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `[`.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    // To do: parse whitespace the way `markdown-rs` does.\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    return factoryLabel.call(\n      self,\n      effects,\n      labelAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      types.definitionLabel,\n      types.definitionLabelMarker,\n      types.definitionLabelString\n    )(code)\n  }\n\n  /**\n   * After label.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    identifier = normalizeIdentifier(\n      self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n    )\n\n    if (code === codes.colon) {\n      effects.enter(types.definitionMarker)\n      effects.consume(code)\n      effects.exit(types.definitionMarker)\n      return markerAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After marker.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function markerAfter(code) {\n    // Note: whitespace is optional.\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, destinationBefore)(code)\n      : destinationBefore(code)\n  }\n\n  /**\n   * Before destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationBefore(code) {\n    return factoryDestination(\n      effects,\n      destinationAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      types.definitionDestination,\n      types.definitionDestinationLiteral,\n      types.definitionDestinationLiteralMarker,\n      types.definitionDestinationRaw,\n      types.definitionDestinationString\n    )(code)\n  }\n\n  /**\n   * After destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationAfter(code) {\n    return effects.attempt(titleBefore, after, after)(code)\n  }\n\n  /**\n   * After definition.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return markdownSpace(code)\n      ? factorySpace(effects, afterWhitespace, types.whitespace)(code)\n      : afterWhitespace(code)\n  }\n\n  /**\n   * After definition, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterWhitespace(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.definition)\n\n      // Note: we don’t care about uniqueness.\n      // It’s likely that that doesn’t happen very frequently.\n      // It is more likely that it wastes precious time.\n      self.parser.defined.push(identifier)\n\n      // To do: `markdown-rs` interrupt.\n      // // You’d be interrupting.\n      // tokenizer.interrupt = true\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeTitleBefore(effects, ok, nok) {\n  return titleBefore\n\n  /**\n   * After destination, at whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleBefore(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, beforeMarker)(code)\n      : nok(code)\n  }\n\n  /**\n   * At title.\n   *\n   * ```markdown\n   *   | [a]: b\n   * > | \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeMarker(code) {\n    return factoryTitle(\n      effects,\n      titleAfter,\n      nok,\n      types.definitionTitle,\n      types.definitionTitleMarker,\n      types.definitionTitleString\n    )(code)\n  }\n\n  /**\n   * After title.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfter(code) {\n    return markdownSpace(code)\n      ? factorySpace(\n          effects,\n          titleAfterOptionalWhitespace,\n          types.whitespace\n        )(code)\n      : titleAfterOptionalWhitespace(code)\n  }\n\n  /**\n   * After title, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfterOptionalWhitespace(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const hardBreakEscape = {\n  name: 'hardBreakEscape',\n  tokenize: tokenizeHardBreakEscape\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHardBreakEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of a hard break (escape).\n   *\n   * ```markdown\n   * > | a\\\n   *      ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`')\n    effects.enter(types.hardBreakEscape)\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After `\\`, at eol.\n   *\n   * ```markdown\n   * > | a\\\n   *       ^\n   *   | b\n   * ```\n   *\n   *  @type {State}\n   */\n  function after(code) {\n    if (markdownLineEnding(code)) {\n      effects.exit(types.hardBreakEscape)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {splice} from 'micromark-util-chunked'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const headingAtx = {\n  name: 'headingAtx',\n  resolve: resolveHeadingAtx,\n  tokenize: tokenizeHeadingAtx\n}\n\n/** @type {Resolver} */\nfunction resolveHeadingAtx(events, context) {\n  let contentEnd = events.length - 2\n  let contentStart = 3\n  /** @type {Token} */\n  let content\n  /** @type {Token} */\n  let text\n\n  // Prefix whitespace, part of the opening.\n  if (events[contentStart][1].type === types.whitespace) {\n    contentStart += 2\n  }\n\n  // Suffix whitespace, part of the closing.\n  if (\n    contentEnd - 2 > contentStart &&\n    events[contentEnd][1].type === types.whitespace\n  ) {\n    contentEnd -= 2\n  }\n\n  if (\n    events[contentEnd][1].type === types.atxHeadingSequence &&\n    (contentStart === contentEnd - 1 ||\n      (contentEnd - 4 > contentStart &&\n        events[contentEnd - 2][1].type === types.whitespace))\n  ) {\n    contentEnd -= contentStart + 1 === contentEnd ? 2 : 4\n  }\n\n  if (contentEnd > contentStart) {\n    content = {\n      type: types.atxHeadingText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end\n    }\n    text = {\n      type: types.chunkText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end,\n      contentType: constants.contentTypeText\n    }\n\n    splice(events, contentStart, contentEnd - contentStart + 1, [\n      ['enter', content, context],\n      ['enter', text, context],\n      ['exit', text, context],\n      ['exit', content, context]\n    ])\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHeadingAtx(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of a heading (atx).\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    effects.enter(types.atxHeading)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `#`.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.numberSign, 'expected `#`')\n    effects.enter(types.atxHeadingSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (\n      code === codes.numberSign &&\n      size++ < constants.atxHeadingOpeningFenceSizeMax\n    ) {\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    // Always at least one `#`.\n    if (code === codes.eof || markdownLineEndingOrSpace(code)) {\n      effects.exit(types.atxHeadingSequence)\n      return atBreak(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ## aa\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.atxHeadingSequence)\n      return sequenceFurther(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.atxHeading)\n      // To do: interrupt like `markdown-rs`.\n      // // Feel free to interrupt.\n      // tokenizer.interrupt = false\n      return ok(code)\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(effects, atBreak, types.whitespace)(code)\n    }\n\n    // To do: generate `data` tokens, add the `text` token later.\n    // Needs edit map, see: `markdown.rs`.\n    effects.enter(types.atxHeadingText)\n    return data(code)\n  }\n\n  /**\n   * In further sequence (after whitespace).\n   *\n   * Could be normal “visible” hashes in the heading or a final sequence.\n   *\n   * ```markdown\n   * > | ## aa ##\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceFurther(code) {\n    if (code === codes.numberSign) {\n      effects.consume(code)\n      return sequenceFurther\n    }\n\n    effects.exit(types.atxHeadingSequence)\n    return atBreak(code)\n  }\n\n  /**\n   * In text.\n   *\n   * ```markdown\n   * > | ## aa\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === codes.eof ||\n      code === codes.numberSign ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      effects.exit(types.atxHeadingText)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n}\n", "/**\n * List of lowercase HTML “block” tag names.\n *\n * The list, when parsing HTML (flow), results in more relaxed rules (condition\n * 6).\n * Because they are known blocks, the HTML-like syntax doesn’t have to be\n * strictly parsed.\n * For tag names not in this list, a more strict algorithm (condition 7) is used\n * to detect whether the HTML-like syntax is seen as HTML (flow) or not.\n *\n * This is copied from:\n * <https://spec.commonmark.org/0.30/#html-blocks>.\n *\n * > 👉 **Note**: `search` was added in `CommonMark@0.31`.\n */\nexport const htmlBlockNames = [\n  'address',\n  'article',\n  'aside',\n  'base',\n  'basefont',\n  'blockquote',\n  'body',\n  'caption',\n  'center',\n  'col',\n  'colgroup',\n  'dd',\n  'details',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'frame',\n  'frameset',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hr',\n  'html',\n  'iframe',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'menu',\n  'menuitem',\n  'nav',\n  'noframes',\n  'ol',\n  'optgroup',\n  'option',\n  'p',\n  'param',\n  'search',\n  'section',\n  'summary',\n  'table',\n  'tbody',\n  'td',\n  'tfoot',\n  'th',\n  'thead',\n  'title',\n  'tr',\n  'track',\n  'ul'\n]\n\n/**\n * List of lowercase HTML “raw” tag names.\n *\n * The list, when parsing HTML (flow), results in HTML that can include lines\n * without exiting, until a closing tag also in this list is found (condition\n * 1).\n *\n * This module is copied from:\n * <https://spec.commonmark.org/0.30/#html-blocks>.\n *\n * > 👉 **Note**: `textarea` was added in `CommonMark@0.30`.\n */\nexport const htmlRawNames = ['pre', 'script', 'style', 'textarea']\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {htmlBlockNames, htmlRawNames} from 'micromark-util-html-tag-name'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {blankLine} from './blank-line.js'\n\n/** @type {Construct} */\nexport const htmlFlow = {\n  concrete: true,\n  name: 'htmlFlow',\n  resolveTo: resolveToHtmlFlow,\n  tokenize: tokenizeHtmlFlow\n}\n\n/** @type {Construct} */\nconst blankLineBefore = {partial: true, tokenize: tokenizeBlankLineBefore}\nconst nonLazyContinuationStart = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuationStart\n}\n\n/** @type {Resolver} */\nfunction resolveToHtmlFlow(events) {\n  let index = events.length\n\n  while (index--) {\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === types.htmlFlow\n    ) {\n      break\n    }\n  }\n\n  if (index > 1 && events[index - 2][1].type === types.linePrefix) {\n    // Add the prefix start to the HTML token.\n    events[index][1].start = events[index - 2][1].start\n    // Add the prefix start to the HTML line token.\n    events[index + 1][1].start = events[index - 2][1].start\n    // Remove the line prefix.\n    events.splice(index - 2, 2)\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlFlow(effects, ok, nok) {\n  const self = this\n  /** @type {number} */\n  let marker\n  /** @type {boolean} */\n  let closingTag\n  /** @type {string} */\n  let buffer\n  /** @type {number} */\n  let index\n  /** @type {Code} */\n  let markerB\n\n  return start\n\n  /**\n   * Start of HTML (flow).\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * At `<`, after optional whitespace.\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.htmlFlow)\n    effects.enter(types.htmlFlowData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | <x />\n   *      ^\n   * > | <!doctype>\n   *      ^\n   * > | <!--xxx-->\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === codes.slash) {\n      effects.consume(code)\n      closingTag = true\n      return tagCloseStart\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      marker = constants.htmlInstruction\n      // To do:\n      // tokenizer.concrete = true\n      // To do: use `markdown-rs` style interrupt.\n      // While we’re in an instruction instead of a declaration, we’re on a `?`\n      // right now, so we do need to search for `>`, similar to declarations.\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *       ^\n   * > | <!--xxx-->\n   *       ^\n   * > | <![CDATA[>&<]]>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      marker = constants.htmlComment\n      return commentOpenInside\n    }\n\n    if (code === codes.leftSquareBracket) {\n      effects.consume(code)\n      marker = constants.htmlCdata\n      index = 0\n      return cdataOpenInside\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      marker = constants.htmlDeclaration\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!-`, inside a comment, at another `-`.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<![`, inside CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *        ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n\n      if (index === value.length) {\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok : continuation\n      }\n\n      return cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | </x>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    if (asciiAlpha(code)) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In tag name.\n   *\n   * ```markdown\n   * > | <ab>\n   *      ^^\n   * > | </ab>\n   *       ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagName(code) {\n    if (\n      code === codes.eof ||\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      const slash = code === codes.slash\n      const name = buffer.toLowerCase()\n\n      if (!slash && !closingTag && htmlRawNames.includes(name)) {\n        marker = constants.htmlRaw\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      if (htmlBlockNames.includes(buffer.toLowerCase())) {\n        marker = constants.htmlBasic\n\n        if (slash) {\n          effects.consume(code)\n          return basicSelfClosing\n        }\n\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      marker = constants.htmlComplete\n      // Do not support complete HTML when interrupting.\n      return self.interrupt && !self.parser.lazy[self.now().line]\n        ? nok(code)\n        : closingTag\n          ? completeClosingTagAfter(code)\n          : completeAttributeNameBefore(code)\n    }\n\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      buffer += String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a basic tag name.\n   *\n   * ```markdown\n   * > | <div/>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function basicSelfClosing(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuation\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a complete tag name.\n   *\n   * ```markdown\n   * > | <x/>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeClosingTagAfter(code) {\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeClosingTagAfter\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * At an attribute name.\n   *\n   * At first, this state is used after a complete tag name, after whitespace,\n   * where it expects optional attributes or the end of the tag.\n   * It is also reused after attributes, when expecting more optional\n   * attributes.\n   *\n   * ```markdown\n   * > | <a />\n   *        ^\n   * > | <a :b>\n   *        ^\n   * > | <a _b>\n   *        ^\n   * > | <a b>\n   *        ^\n   * > | <a >\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameBefore(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      return completeEnd\n    }\n\n    // ASCII alphanumerical and `:` and `_`.\n    if (code === codes.colon || code === codes.underscore || asciiAlpha(code)) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeNameBefore\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | <a :b>\n   *         ^\n   * > | <a _b>\n   *         ^\n   * > | <a b>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeName(code) {\n    // ASCII alphanumerical and `-`, `.`, `:`, and `_`.\n    if (\n      code === codes.dash ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.underscore ||\n      asciiAlphanumeric(code)\n    ) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    return completeAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, at an optional initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b>\n   *         ^\n   * > | <a b=c>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameAfter(code) {\n    if (code === codes.equalsTo) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeNameAfter\n    }\n\n    return completeAttributeNameBefore(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * > | <a b=\"c\">\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueBefore(code) {\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.quotationMark || code === codes.apostrophe) {\n      effects.consume(code)\n      markerB = code\n      return completeAttributeValueQuoted\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    return completeAttributeValueUnquoted(code)\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *           ^\n   * > | <a b='c'>\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuoted(code) {\n    if (code === markerB) {\n      effects.consume(code)\n      markerB = null\n      return completeAttributeValueQuotedAfter\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueUnquoted(code) {\n    if (\n      code === codes.eof ||\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.slash ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return completeAttributeNameAfter(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the\n   * end of the tag.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuotedAfter(code) {\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownSpace(code)\n    ) {\n      return completeAttributeNameBefore(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a complete tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeEnd(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>` in a complete tag.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAfter(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return continuation(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In continuation of any HTML kind.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuation(code) {\n    if (code === codes.dash && marker === constants.htmlComment) {\n      effects.consume(code)\n      return continuationCommentInside\n    }\n\n    if (code === codes.lessThan && marker === constants.htmlRaw) {\n      effects.consume(code)\n      return continuationRawTagOpen\n    }\n\n    if (code === codes.greaterThan && marker === constants.htmlDeclaration) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    if (code === codes.questionMark && marker === constants.htmlInstruction) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    if (code === codes.rightSquareBracket && marker === constants.htmlCdata) {\n      effects.consume(code)\n      return continuationCdataInside\n    }\n\n    if (\n      markdownLineEnding(code) &&\n      (marker === constants.htmlBasic || marker === constants.htmlComplete)\n    ) {\n      effects.exit(types.htmlFlowData)\n      return effects.check(\n        blankLineBefore,\n        continuationAfter,\n        continuationStart\n      )(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.htmlFlowData)\n      return continuationStart(code)\n    }\n\n    effects.consume(code)\n    return continuation\n  }\n\n  /**\n   * In continuation, at eol.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStart(code) {\n    return effects.check(\n      nonLazyContinuationStart,\n      continuationStartNonLazy,\n      continuationAfter\n    )(code)\n  }\n\n  /**\n   * In continuation, at eol, before non-lazy content.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStartNonLazy(code) {\n    assert(markdownLineEnding(code))\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return continuationBefore\n  }\n\n  /**\n   * In continuation, before non-lazy content.\n   *\n   * ```markdown\n   *   | <x>\n   * > | asd\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return continuationStart(code)\n    }\n\n    effects.enter(types.htmlFlowData)\n    return continuation(code)\n  }\n\n  /**\n   * In comment continuation, after one `-`, expecting another.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCommentInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `<`, at `/`.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawTagOpen(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      buffer = ''\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `</`, in a raw tag name.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                             ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawEndTag(code) {\n    if (code === codes.greaterThan) {\n      const name = buffer.toLowerCase()\n\n      if (htmlRawNames.includes(name)) {\n        effects.consume(code)\n        return continuationClose\n      }\n\n      return continuation(code)\n    }\n\n    if (asciiAlpha(code) && buffer.length < constants.htmlRawSizeMax) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer += String.fromCharCode(code)\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In cdata continuation, after `]`, expecting `]>`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *                  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCdataInside(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In declaration or instruction continuation, at `>`.\n   *\n   * ```markdown\n   * > | <!-->\n   *         ^\n   * > | <?>\n   *       ^\n   * > | <!q>\n   *        ^\n   * > | <!--ab-->\n   *             ^\n   * > | <![CDATA[>&<]]>\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationDeclarationInside(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    // More dashes.\n    if (code === codes.dash && marker === constants.htmlComment) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In closed continuation: everything we get until the eol/eof is part of it.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationClose(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.htmlFlowData)\n      return continuationAfter(code)\n    }\n\n    effects.consume(code)\n    return continuationClose\n  }\n\n  /**\n   * Done.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationAfter(code) {\n    effects.exit(types.htmlFlow)\n    // // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    // // No longer concrete.\n    // tokenizer.concrete = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuationStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * At eol, before continuation.\n   *\n   * ```markdown\n   * > | * ```js\n   *            ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * A continuation.\n   *\n   * ```markdown\n   *   | * ```js\n   * > | b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLineBefore(effects, ok, nok) {\n  return start\n\n  /**\n   * Before eol, expecting blank line.\n   *\n   * ```markdown\n   * > | <div>\n   *          ^\n   *   |\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected a line ending')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return effects.attempt(blankLine, ok, nok)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const htmlText = {name: 'htmlText', tokenize: tokenizeHtmlText}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlText(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code> | undefined} */\n  let marker\n  /** @type {number} */\n  let index\n  /** @type {State} */\n  let returnState\n\n  return start\n\n  /**\n   * Start of HTML (text).\n   *\n   * ```markdown\n   * > | a <b> c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.htmlText)\n    effects.enter(types.htmlTextData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | a <b> c\n   *        ^\n   * > | a <!doctype> c\n   *        ^\n   * > | a <!--b--> c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === codes.slash) {\n      effects.consume(code)\n      return tagCloseStart\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      return instruction\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | a <!doctype> c\n   *         ^\n   * > | a <!--b--> c\n   *         ^\n   * > | a <![CDATA[>&<]]> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentOpenInside\n    }\n\n    if (code === codes.leftSquareBracket) {\n      effects.consume(code)\n      index = 0\n      return cdataOpenInside\n    }\n\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return declaration\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In a comment, after `<!-`, at another `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In comment.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function comment(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = comment\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return comment\n  }\n\n  /**\n   * In comment, after `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentClose(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return comment(code)\n  }\n\n  /**\n   * In comment, after `--`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentEnd(code) {\n    return code === codes.greaterThan\n      ? end(code)\n      : code === codes.dash\n        ? commentClose(code)\n        : comment(code)\n  }\n\n  /**\n   * After `<![`, in CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *          ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n      return index === value.length ? cdata : cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In CDATA.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdata(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = cdata\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return cdata\n  }\n\n  /**\n   * In CDATA, after `]`, at another `]`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataClose(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In CDATA, after `]]`, at `>`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataEnd(code) {\n    if (code === codes.greaterThan) {\n      return end(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In declaration.\n   *\n   * ```markdown\n   * > | a <!b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declaration(code) {\n    if (code === codes.eof || code === codes.greaterThan) {\n      return end(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = declaration\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return declaration\n  }\n\n  /**\n   * In instruction.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instruction(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      return instructionClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = instruction\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return instruction\n  }\n\n  /**\n   * In instruction, after `?`, at `>`.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instructionClose(code) {\n    return code === codes.greaterThan ? end(code) : instruction(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</x`, in a tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagClose(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return tagCloseBetween(code)\n  }\n\n  /**\n   * In closing tag, after tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseBetween(code) {\n    if (markdownLineEnding(code)) {\n      returnState = tagCloseBetween\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagCloseBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * After `<x`, in opening tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpen(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In opening tag, after tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenBetween(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      return end\n    }\n\n    // ASCII alphabetical and `:` and `_`.\n    if (code === codes.colon || code === codes.underscore || asciiAlpha(code)) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenBetween\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeName(code) {\n    // ASCII alphabetical and `-`, `.`, `:`, and `_`.\n    if (\n      code === codes.dash ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.underscore ||\n      asciiAlphanumeric(code)\n    ) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    return tagOpenAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, before initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeNameAfter(code) {\n    if (code === codes.equalsTo) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeNameAfter\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenAttributeNameAfter\n    }\n\n    return tagOpenBetween(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueBefore(code) {\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.quotationMark || code === codes.apostrophe) {\n      effects.consume(code)\n      marker = code\n      return tagOpenAttributeValueQuoted\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueBefore\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuoted(code) {\n    if (code === marker) {\n      effects.consume(code)\n      marker = undefined\n      return tagOpenAttributeValueQuotedAfter\n    }\n\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueQuoted\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueUnquoted(code) {\n    if (\n      code === codes.eof ||\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the end\n   * of the tag.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuotedAfter(code) {\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function end(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      effects.exit(types.htmlTextData)\n      effects.exit(types.htmlText)\n      return ok\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * At eol.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   * > | a <!--a\n   *            ^\n   *   | b-->\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingBefore(code) {\n    assert(returnState, 'expected return state')\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.exit(types.htmlTextData)\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return lineEndingAfter\n  }\n\n  /**\n   * After eol, at optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfter(code) {\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return markdownSpace(code)\n      ? factorySpace(\n          effects,\n          lineEndingAfterPrefix,\n          types.linePrefix,\n          self.parser.constructs.disable.null.includes('codeIndented')\n            ? undefined\n            : constants.tabSize\n        )(code)\n      : lineEndingAfterPrefix(code)\n  }\n\n  /**\n   * After eol, after optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfterPrefix(code) {\n    effects.enter(types.htmlTextData)\n    return returnState(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   Event,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factoryDestination} from 'micromark-factory-destination'\nimport {factoryLabel} from 'micromark-factory-label'\nimport {factoryTitle} from 'micromark-factory-title'\nimport {factoryWhitespace} from 'micromark-factory-whitespace'\nimport {markdownLineEndingOrSpace} from 'micromark-util-character'\nimport {push, splice} from 'micromark-util-chunked'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const labelEnd = {\n  name: 'labelEnd',\n  resolveAll: resolveAllLabelEnd,\n  resolveTo: resolveToLabelEnd,\n  tokenize: tokenizeLabelEnd\n}\n\n/** @type {Construct} */\nconst resourceConstruct = {tokenize: tokenizeResource}\n/** @type {Construct} */\nconst referenceFullConstruct = {tokenize: tokenizeReferenceFull}\n/** @type {Construct} */\nconst referenceCollapsedConstruct = {tokenize: tokenizeReferenceCollapsed}\n\n/** @type {Resolver} */\nfunction resolveAllLabelEnd(events) {\n  let index = -1\n  /** @type {Array<Event>} */\n  const newEvents = []\n  while (++index < events.length) {\n    const token = events[index][1]\n    newEvents.push(events[index])\n\n    if (\n      token.type === types.labelImage ||\n      token.type === types.labelLink ||\n      token.type === types.labelEnd\n    ) {\n      // Remove the marker.\n      const offset = token.type === types.labelImage ? 4 : 2\n      token.type = types.data\n      index += offset\n    }\n  }\n\n  // If the events are equal, we don't have to copy newEvents to events\n  if (events.length !== newEvents.length) {\n    splice(events, 0, events.length, newEvents)\n  }\n\n  return events\n}\n\n/** @type {Resolver} */\nfunction resolveToLabelEnd(events, context) {\n  let index = events.length\n  let offset = 0\n  /** @type {Token} */\n  let token\n  /** @type {number | undefined} */\n  let open\n  /** @type {number | undefined} */\n  let close\n  /** @type {Array<Event>} */\n  let media\n\n  // Find an opening.\n  while (index--) {\n    token = events[index][1]\n\n    if (open) {\n      // If we see another link, or inactive link label, we’ve been here before.\n      if (\n        token.type === types.link ||\n        (token.type === types.labelLink && token._inactive)\n      ) {\n        break\n      }\n\n      // Mark other link openings as inactive, as we can’t have links in\n      // links.\n      if (events[index][0] === 'enter' && token.type === types.labelLink) {\n        token._inactive = true\n      }\n    } else if (close) {\n      if (\n        events[index][0] === 'enter' &&\n        (token.type === types.labelImage || token.type === types.labelLink) &&\n        !token._balanced\n      ) {\n        open = index\n\n        if (token.type !== types.labelLink) {\n          offset = 2\n          break\n        }\n      }\n    } else if (token.type === types.labelEnd) {\n      close = index\n    }\n  }\n\n  assert(open !== undefined, '`open` is supposed to be found')\n  assert(close !== undefined, '`close` is supposed to be found')\n\n  const group = {\n    type: events[open][1].type === types.labelLink ? types.link : types.image,\n    start: {...events[open][1].start},\n    end: {...events[events.length - 1][1].end}\n  }\n\n  const label = {\n    type: types.label,\n    start: {...events[open][1].start},\n    end: {...events[close][1].end}\n  }\n\n  const text = {\n    type: types.labelText,\n    start: {...events[open + offset + 2][1].end},\n    end: {...events[close - 2][1].start}\n  }\n\n  media = [\n    ['enter', group, context],\n    ['enter', label, context]\n  ]\n\n  // Opening marker.\n  media = push(media, events.slice(open + 1, open + offset + 3))\n\n  // Text open.\n  media = push(media, [['enter', text, context]])\n\n  // Always populated by defaults.\n  assert(\n    context.parser.constructs.insideSpan.null,\n    'expected `insideSpan.null` to be populated'\n  )\n  // Between.\n  media = push(\n    media,\n    resolveAll(\n      context.parser.constructs.insideSpan.null,\n      events.slice(open + offset + 4, close - 3),\n      context\n    )\n  )\n\n  // Text close, marker close, label close.\n  media = push(media, [\n    ['exit', text, context],\n    events[close - 2],\n    events[close - 1],\n    ['exit', label, context]\n  ])\n\n  // Reference, resource, or so.\n  media = push(media, events.slice(close + 1))\n\n  // Media close.\n  media = push(media, [['exit', group, context]])\n\n  splice(events, open, events.length, media)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelEnd(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  /** @type {Token} */\n  let labelStart\n  /** @type {boolean} */\n  let defined\n\n  // Find an opening.\n  while (index--) {\n    if (\n      (self.events[index][1].type === types.labelImage ||\n        self.events[index][1].type === types.labelLink) &&\n      !self.events[index][1]._balanced\n    ) {\n      labelStart = self.events[index][1]\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * Start of label end.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.rightSquareBracket, 'expected `]`')\n\n    // If there is not an okay opening.\n    if (!labelStart) {\n      return nok(code)\n    }\n\n    // If the corresponding label (link) start is marked as inactive,\n    // it means we’d be wrapping a link, like this:\n    //\n    // ```markdown\n    // > | a [b [c](d) e](f) g.\n    //                  ^\n    // ```\n    //\n    // We can’t have that, so it’s just balanced brackets.\n    if (labelStart._inactive) {\n      return labelEndNok(code)\n    }\n\n    defined = self.parser.defined.includes(\n      normalizeIdentifier(\n        self.sliceSerialize({start: labelStart.end, end: self.now()})\n      )\n    )\n    effects.enter(types.labelEnd)\n    effects.enter(types.labelMarker)\n    effects.consume(code)\n    effects.exit(types.labelMarker)\n    effects.exit(types.labelEnd)\n    return after\n  }\n\n  /**\n   * After `]`.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Note: `markdown-rs` also parses GFM footnotes here, which for us is in\n    // an extension.\n\n    // Resource (`[asd](fgh)`)?\n    if (code === codes.leftParenthesis) {\n      return effects.attempt(\n        resourceConstruct,\n        labelEndOk,\n        defined ? labelEndOk : labelEndNok\n      )(code)\n    }\n\n    // Full (`[asd][fgh]`) or collapsed (`[asd][]`) reference?\n    if (code === codes.leftSquareBracket) {\n      return effects.attempt(\n        referenceFullConstruct,\n        labelEndOk,\n        defined ? referenceNotFull : labelEndNok\n      )(code)\n    }\n\n    // Shortcut (`[asd]`) reference?\n    return defined ? labelEndOk(code) : labelEndNok(code)\n  }\n\n  /**\n   * After `]`, at `[`, but not at a full reference.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] b\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceNotFull(code) {\n    return effects.attempt(\n      referenceCollapsedConstruct,\n      labelEndOk,\n      labelEndNok\n    )(code)\n  }\n\n  /**\n   * Done, we found something.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *           ^\n   * > | [a][b] c\n   *           ^\n   * > | [a][] b\n   *          ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndOk(code) {\n    // Note: `markdown-rs` does a bunch of stuff here.\n    return ok(code)\n  }\n\n  /**\n   * Done, it’s nothing.\n   *\n   * There was an okay opening, but we didn’t match anything.\n   *\n   * ```markdown\n   * > | [a](b c\n   *        ^\n   * > | [a][b c\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndNok(code) {\n    labelStart._balanced = true\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeResource(effects, ok, nok) {\n  return resourceStart\n\n  /**\n   * At a resource.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceStart(code) {\n    assert(code === codes.leftParenthesis, 'expected left paren')\n    effects.enter(types.resource)\n    effects.enter(types.resourceMarker)\n    effects.consume(code)\n    effects.exit(types.resourceMarker)\n    return resourceBefore\n  }\n\n  /**\n   * In resource, after `(`, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBefore(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceOpen)(code)\n      : resourceOpen(code)\n  }\n\n  /**\n   * In resource, after optional whitespace, at `)` or a destination.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceOpen(code) {\n    if (code === codes.rightParenthesis) {\n      return resourceEnd(code)\n    }\n\n    return factoryDestination(\n      effects,\n      resourceDestinationAfter,\n      resourceDestinationMissing,\n      types.resourceDestination,\n      types.resourceDestinationLiteral,\n      types.resourceDestinationLiteralMarker,\n      types.resourceDestinationRaw,\n      types.resourceDestinationString,\n      constants.linkResourceDestinationBalanceMax\n    )(code)\n  }\n\n  /**\n   * In resource, after destination, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationAfter(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceBetween)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * At invalid destination.\n   *\n   * ```markdown\n   * > | [a](<<) b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationMissing(code) {\n    return nok(code)\n  }\n\n  /**\n   * In resource, after destination and whitespace, at `(` or title.\n   *\n   * ```markdown\n   * > | [a](b ) c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBetween(code) {\n    if (\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.leftParenthesis\n    ) {\n      return factoryTitle(\n        effects,\n        resourceTitleAfter,\n        nok,\n        types.resourceTitle,\n        types.resourceTitleMarker,\n        types.resourceTitleString\n      )(code)\n    }\n\n    return resourceEnd(code)\n  }\n\n  /**\n   * In resource, after title, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b \"c\") d\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceTitleAfter(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceEnd)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * In resource, at `)`.\n   *\n   * ```markdown\n   * > | [a](b) d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceEnd(code) {\n    if (code === codes.rightParenthesis) {\n      effects.enter(types.resourceMarker)\n      effects.consume(code)\n      effects.exit(types.resourceMarker)\n      effects.exit(types.resource)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceFull(effects, ok, nok) {\n  const self = this\n\n  return referenceFull\n\n  /**\n   * In a reference (full), at the `[`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFull(code) {\n    assert(code === codes.leftSquareBracket, 'expected left bracket')\n    return factoryLabel.call(\n      self,\n      effects,\n      referenceFullAfter,\n      referenceFullMissing,\n      types.reference,\n      types.referenceMarker,\n      types.referenceString\n    )(code)\n  }\n\n  /**\n   * In a reference (full), after `]`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullAfter(code) {\n    return self.parser.defined.includes(\n      normalizeIdentifier(\n        self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n      )\n    )\n      ? ok(code)\n      : nok(code)\n  }\n\n  /**\n   * In reference (full) that was missing.\n   *\n   * ```markdown\n   * > | [a][b d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullMissing(code) {\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceCollapsed(effects, ok, nok) {\n  return referenceCollapsedStart\n\n  /**\n   * In reference (collapsed), at `[`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceCollapsedStart(code) {\n    // We only attempt a collapsed label if there’s a `[`.\n    assert(code === codes.leftSquareBracket, 'expected left bracket')\n    effects.enter(types.reference)\n    effects.enter(types.referenceMarker)\n    effects.consume(code)\n    effects.exit(types.referenceMarker)\n    return referenceCollapsedOpen\n  }\n\n  /**\n   * In reference (collapsed), at `]`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *         ^\n   * ```\n   *\n   *  @type {State}\n   */\n  function referenceCollapsedOpen(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.enter(types.referenceMarker)\n      effects.consume(code)\n      effects.exit(types.referenceMarker)\n      effects.exit(types.reference)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {codes, types} from 'micromark-util-symbol'\nimport {labelEnd} from './label-end.js'\n\n/** @type {Construct} */\nexport const labelStartImage = {\n  name: 'labelStartImage',\n  resolveAll: labelEnd.resolveAll,\n  tokenize: tokenizeLabelStartImage\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartImage(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (image) start.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.exclamationMark, 'expected `!`')\n    effects.enter(types.labelImage)\n    effects.enter(types.labelImageMarker)\n    effects.consume(code)\n    effects.exit(types.labelImageMarker)\n    return open\n  }\n\n  /**\n   * After `!`, at `[`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.leftSquareBracket) {\n      effects.enter(types.labelMarker)\n      effects.consume(code)\n      effects.exit(types.labelMarker)\n      effects.exit(types.labelImage)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `![`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *         ^\n   * ```\n   *\n   * This is needed in because, when GFM footnotes are enabled, images never\n   * form when started with a `^`.\n   * Instead, links form:\n   *\n   * ```markdown\n   * ![^a](b)\n   *\n   * ![^a][b]\n   *\n   * [b]: c\n   * ```\n   *\n   * ```html\n   * <p>!<a href=\\\"b\\\">^a</a></p>\n   * <p>!<a href=\\\"c\\\">^a</a></p>\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // To do: use a new field to do this, this is still needed for\n    // `micromark-extension-gfm-footnote`, but the `label-start-link`\n    // behavior isn’t.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {codes, types} from 'micromark-util-symbol'\nimport {labelEnd} from './label-end.js'\n\n/** @type {Construct} */\nexport const labelStartLink = {\n  name: 'labelStartLink',\n  resolveAll: labelEnd.resolveAll,\n  tokenize: tokenizeLabelStartLink\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartLink(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (link) start.\n   *\n   * ```markdown\n   * > | a [b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter(types.labelLink)\n    effects.enter(types.labelMarker)\n    effects.consume(code)\n    effects.exit(types.labelMarker)\n    effects.exit(types.labelLink)\n    return after\n  }\n\n  /** @type {State} */\n  function after(code) {\n    // To do: this isn’t needed in `micromark-extension-gfm-footnote`,\n    // remove.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const lineEnding = {name: 'lineEnding', tokenize: tokenizeLineEnding}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLineEnding(effects, ok) {\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, ok, types.linePrefix)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const thematicBreak = {\n  name: 'thematicBreak',\n  tokenize: tokenizeThematicBreak\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeThematicBreak(effects, ok, nok) {\n  let size = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of thematic break.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    effects.enter(types.thematicBreak)\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at marker.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(\n      code === codes.asterisk ||\n        code === codes.dash ||\n        code === codes.underscore,\n      'expected `*`, `-`, or `_`'\n    )\n    marker = code\n    return atBreak(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.enter(types.thematicBreakSequence)\n      return sequence(code)\n    }\n\n    if (\n      size >= constants.thematicBreakMarkerCountMin &&\n      (code === codes.eof || markdownLineEnding(code))\n    ) {\n      effects.exit(types.thematicBreak)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequence(code) {\n    if (code === marker) {\n      effects.consume(code)\n      size++\n      return sequence\n    }\n\n    effects.exit(types.thematicBreakSequence)\n    return markdownSpace(code)\n      ? factorySpace(effects, atBreak, types.whitespace)(code)\n      : atBreak(code)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   Exiter,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {asciiDigit, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {blankLine} from './blank-line.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/** @type {Construct} */\nexport const list = {\n  continuation: {tokenize: tokenizeListContinuation},\n  exit: tokenizeListEnd,\n  name: 'list',\n  tokenize: tokenizeListStart\n}\n\n/** @type {Construct} */\nconst listItemPrefixWhitespaceConstruct = {\n  partial: true,\n  tokenize: tokenizeListItemPrefixWhitespace\n}\n\n/** @type {Construct} */\nconst indentConstruct = {partial: true, tokenize: tokenizeIndent}\n\n// To do: `markdown-rs` parses list items on their own and later stitches them\n// together.\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListStart(effects, ok, nok) {\n  const self = this\n  const tail = self.events[self.events.length - 1]\n  let initialSize =\n    tail && tail[1].type === types.linePrefix\n      ? tail[2].sliceSerialize(tail[1], true).length\n      : 0\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    assert(self.containerState, 'expected state')\n    const kind =\n      self.containerState.type ||\n      (code === codes.asterisk || code === codes.plusSign || code === codes.dash\n        ? types.listUnordered\n        : types.listOrdered)\n\n    if (\n      kind === types.listUnordered\n        ? !self.containerState.marker || code === self.containerState.marker\n        : asciiDigit(code)\n    ) {\n      if (!self.containerState.type) {\n        self.containerState.type = kind\n        effects.enter(kind, {_container: true})\n      }\n\n      if (kind === types.listUnordered) {\n        effects.enter(types.listItemPrefix)\n        return code === codes.asterisk || code === codes.dash\n          ? effects.check(thematicBreak, nok, atMarker)(code)\n          : atMarker(code)\n      }\n\n      if (!self.interrupt || code === codes.digit1) {\n        effects.enter(types.listItemPrefix)\n        effects.enter(types.listItemValue)\n        return inside(code)\n      }\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function inside(code) {\n    assert(self.containerState, 'expected state')\n    if (asciiDigit(code) && ++size < constants.listItemValueSizeMax) {\n      effects.consume(code)\n      return inside\n    }\n\n    if (\n      (!self.interrupt || size < 2) &&\n      (self.containerState.marker\n        ? code === self.containerState.marker\n        : code === codes.rightParenthesis || code === codes.dot)\n    ) {\n      effects.exit(types.listItemValue)\n      return atMarker(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * @type {State}\n   **/\n  function atMarker(code) {\n    assert(self.containerState, 'expected state')\n    assert(code !== codes.eof, 'eof (`null`) is not a marker')\n    effects.enter(types.listItemMarker)\n    effects.consume(code)\n    effects.exit(types.listItemMarker)\n    self.containerState.marker = self.containerState.marker || code\n    return effects.check(\n      blankLine,\n      // Can’t be empty when interrupting.\n      self.interrupt ? nok : onBlank,\n      effects.attempt(\n        listItemPrefixWhitespaceConstruct,\n        endOfPrefix,\n        otherPrefix\n      )\n    )\n  }\n\n  /** @type {State} */\n  function onBlank(code) {\n    assert(self.containerState, 'expected state')\n    self.containerState.initialBlankLine = true\n    initialSize++\n    return endOfPrefix(code)\n  }\n\n  /** @type {State} */\n  function otherPrefix(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.listItemPrefixWhitespace)\n      effects.consume(code)\n      effects.exit(types.listItemPrefixWhitespace)\n      return endOfPrefix\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function endOfPrefix(code) {\n    assert(self.containerState, 'expected state')\n    self.containerState.size =\n      initialSize +\n      self.sliceSerialize(effects.exit(types.listItemPrefix), true).length\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListContinuation(effects, ok, nok) {\n  const self = this\n\n  assert(self.containerState, 'expected state')\n  self.containerState._closeFlow = undefined\n\n  return effects.check(blankLine, onBlank, notBlank)\n\n  /** @type {State} */\n  function onBlank(code) {\n    assert(self.containerState, 'expected state')\n    assert(typeof self.containerState.size === 'number', 'expected size')\n    self.containerState.furtherBlankLines =\n      self.containerState.furtherBlankLines ||\n      self.containerState.initialBlankLine\n\n    // We have a blank line.\n    // Still, try to consume at most the items size.\n    return factorySpace(\n      effects,\n      ok,\n      types.listItemIndent,\n      self.containerState.size + 1\n    )(code)\n  }\n\n  /** @type {State} */\n  function notBlank(code) {\n    assert(self.containerState, 'expected state')\n    if (self.containerState.furtherBlankLines || !markdownSpace(code)) {\n      self.containerState.furtherBlankLines = undefined\n      self.containerState.initialBlankLine = undefined\n      return notInCurrentItem(code)\n    }\n\n    self.containerState.furtherBlankLines = undefined\n    self.containerState.initialBlankLine = undefined\n    return effects.attempt(indentConstruct, ok, notInCurrentItem)(code)\n  }\n\n  /** @type {State} */\n  function notInCurrentItem(code) {\n    assert(self.containerState, 'expected state')\n    // While we do continue, we signal that the flow should be closed.\n    self.containerState._closeFlow = true\n    // As we’re closing flow, we’re no longer interrupting.\n    self.interrupt = undefined\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return factorySpace(\n      effects,\n      effects.attempt(list, ok, nok),\n      types.linePrefix,\n      self.parser.constructs.disable.null.includes('codeIndented')\n        ? undefined\n        : constants.tabSize\n    )(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this\n\n  assert(self.containerState, 'expected state')\n  assert(typeof self.containerState.size === 'number', 'expected size')\n\n  return factorySpace(\n    effects,\n    afterPrefix,\n    types.listItemIndent,\n    self.containerState.size + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    assert(self.containerState, 'expected state')\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.listItemIndent &&\n      tail[2].sliceSerialize(tail[1], true).length === self.containerState.size\n      ? ok(code)\n      : nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Exiter}\n */\nfunction tokenizeListEnd(effects) {\n  assert(this.containerState, 'expected state')\n  assert(typeof this.containerState.type === 'string', 'expected type')\n  effects.exit(this.containerState.type)\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListItemPrefixWhitespace(effects, ok, nok) {\n  const self = this\n\n  // Always populated by defaults.\n  assert(\n    self.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n\n  return factorySpace(\n    effects,\n    afterPrefix,\n    types.listItemPrefixWhitespace,\n    self.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : constants.tabSize + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n\n    return !markdownSpace(code) &&\n      tail &&\n      tail[1].type === types.listItemPrefixWhitespace\n      ? ok(code)\n      : nok(code)\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const setextUnderline = {\n  name: 'setextUnderline',\n  resolveTo: resolveToSetextUnderline,\n  tokenize: tokenizeSetextUnderline\n}\n\n/** @type {Resolver} */\nfunction resolveToSetextUnderline(events, context) {\n  // To do: resolve like `markdown-rs`.\n  let index = events.length\n  /** @type {number | undefined} */\n  let content\n  /** @type {number | undefined} */\n  let text\n  /** @type {number | undefined} */\n  let definition\n\n  // Find the opening of the content.\n  // It’ll always exist: we don’t tokenize if it isn’t there.\n  while (index--) {\n    if (events[index][0] === 'enter') {\n      if (events[index][1].type === types.content) {\n        content = index\n        break\n      }\n\n      if (events[index][1].type === types.paragraph) {\n        text = index\n      }\n    }\n    // Exit\n    else {\n      if (events[index][1].type === types.content) {\n        // Remove the content end (if needed we’ll add it later)\n        events.splice(index, 1)\n      }\n\n      if (!definition && events[index][1].type === types.definition) {\n        definition = index\n      }\n    }\n  }\n\n  assert(text !== undefined, 'expected a `text` index to be found')\n  assert(content !== undefined, 'expected a `text` index to be found')\n  assert(events[content][2] === context, 'enter context should be same')\n  assert(\n    events[events.length - 1][2] === context,\n    'enter context should be same'\n  )\n  const heading = {\n    type: types.setextHeading,\n    start: {...events[content][1].start},\n    end: {...events[events.length - 1][1].end}\n  }\n\n  // Change the paragraph to setext heading text.\n  events[text][1].type = types.setextHeadingText\n\n  // If we have definitions in the content, we’ll keep on having content,\n  // but we need move it.\n  if (definition) {\n    events.splice(text, 0, ['enter', heading, context])\n    events.splice(definition + 1, 0, ['exit', events[content][1], context])\n    events[content][1].end = {...events[definition][1].end}\n  } else {\n    events[content][1] = heading\n  }\n\n  // Add the heading exit at the end.\n  events.push(['exit', heading, context])\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeSetextUnderline(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * At start of heading (setext) underline.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length\n    /** @type {boolean | undefined} */\n    let paragraph\n\n    assert(\n      code === codes.dash || code === codes.equalsTo,\n      'expected `=` or `-`'\n    )\n\n    // Find an opening.\n    while (index--) {\n      // Skip enter/exit of line ending, line prefix, and content.\n      // We can now either have a definition or a paragraph.\n      if (\n        self.events[index][1].type !== types.lineEnding &&\n        self.events[index][1].type !== types.linePrefix &&\n        self.events[index][1].type !== types.content\n      ) {\n        paragraph = self.events[index][1].type === types.paragraph\n        break\n      }\n    }\n\n    // To do: handle lazy/pierce like `markdown-rs`.\n    // To do: parse indent like `markdown-rs`.\n    if (!self.parser.lazy[self.now().line] && (self.interrupt || paragraph)) {\n      effects.enter(types.setextHeadingLine)\n      marker = code\n      return before(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After optional whitespace, at `-` or `=`.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    effects.enter(types.setextHeadingLineSequence)\n    return inside(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    effects.exit(types.setextHeadingLineSequence)\n\n    return markdownSpace(code)\n      ? factorySpace(effects, after, types.lineSuffix)(code)\n      : after(code)\n  }\n\n  /**\n   * After sequence, after optional whitespace.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.setextHeadingLine)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n", "/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {blankLine, content} from 'micromark-core-commonmark'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {InitialConstruct} */\nexport const flow = {tokenize: initializeFlow}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeFlow(effects) {\n  const self = this\n  const initial = effects.attempt(\n    // Try to parse a blank line.\n    blankLine,\n    atBlankEnding,\n    // Try to parse initial flow (essentially, only code).\n    effects.attempt(\n      this.parser.constructs.flowInitial,\n      afterConstruct,\n      factorySpace(\n        effects,\n        effects.attempt(\n          this.parser.constructs.flow,\n          afterConstruct,\n          effects.attempt(content, afterConstruct)\n        ),\n        types.linePrefix\n      )\n    )\n  )\n\n  return initial\n\n  /** @type {State} */\n  function atBlankEnding(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEndingBlank)\n    effects.consume(code)\n    effects.exit(types.lineEndingBlank)\n    self.currentConstruct = undefined\n    return initial\n  }\n\n  /** @type {State} */\n  function afterConstruct(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    self.currentConstruct = undefined\n    return initial\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   InitialConstruct,\n *   Initializer,\n *   Resolver,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\nexport const resolver = {resolveAll: createResolver()}\nexport const string = initializeFactory('string')\nexport const text = initializeFactory('text')\n\n/**\n * @param {'string' | 'text'} field\n *   Field.\n * @returns {InitialConstruct}\n *   Construct.\n */\nfunction initializeFactory(field) {\n  return {\n    resolveAll: createResolver(\n      field === 'text' ? resolveAllLineSuffixes : undefined\n    ),\n    tokenize: initializeText\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Initializer}\n   */\n  function initializeText(effects) {\n    const self = this\n    const constructs = this.parser.constructs[field]\n    const text = effects.attempt(constructs, start, notText)\n\n    return start\n\n    /** @type {State} */\n    function start(code) {\n      return atBreak(code) ? text(code) : notText(code)\n    }\n\n    /** @type {State} */\n    function notText(code) {\n      if (code === codes.eof) {\n        effects.consume(code)\n        return\n      }\n\n      effects.enter(types.data)\n      effects.consume(code)\n      return data\n    }\n\n    /** @type {State} */\n    function data(code) {\n      if (atBreak(code)) {\n        effects.exit(types.data)\n        return text(code)\n      }\n\n      // Data.\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * @param {Code} code\n     *   Code.\n     * @returns {boolean}\n     *   Whether the code is a break.\n     */\n    function atBreak(code) {\n      if (code === codes.eof) {\n        return true\n      }\n\n      const list = constructs[code]\n      let index = -1\n\n      if (list) {\n        // Always populated by defaults.\n        assert(Array.isArray(list), 'expected `disable.null` to be populated')\n\n        while (++index < list.length) {\n          const item = list[index]\n          if (!item.previous || item.previous.call(self, self.previous)) {\n            return true\n          }\n        }\n      }\n\n      return false\n    }\n  }\n}\n\n/**\n * @param {Resolver | undefined} [extraResolver]\n *   Resolver.\n * @returns {Resolver}\n *   Resolver.\n */\nfunction createResolver(extraResolver) {\n  return resolveAllText\n\n  /** @type {Resolver} */\n  function resolveAllText(events, context) {\n    let index = -1\n    /** @type {number | undefined} */\n    let enter\n\n    // A rather boring computation (to merge adjacent `data` events) which\n    // improves mm performance by 29%.\n    while (++index <= events.length) {\n      if (enter === undefined) {\n        if (events[index] && events[index][1].type === types.data) {\n          enter = index\n          index++\n        }\n      } else if (!events[index] || events[index][1].type !== types.data) {\n        // Don’t do anything if there is one data token.\n        if (index !== enter + 2) {\n          events[enter][1].end = events[index - 1][1].end\n          events.splice(enter + 2, index - enter - 2)\n          index = enter + 2\n        }\n\n        enter = undefined\n      }\n    }\n\n    return extraResolver ? extraResolver(events, context) : events\n  }\n}\n\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */\nfunction resolveAllLineSuffixes(events, context) {\n  let eventIndex = 0 // Skip first.\n\n  while (++eventIndex <= events.length) {\n    if (\n      (eventIndex === events.length ||\n        events[eventIndex][1].type === types.lineEnding) &&\n      events[eventIndex - 1][1].type === types.data\n    ) {\n      const data = events[eventIndex - 1][1]\n      const chunks = context.sliceStream(data)\n      let index = chunks.length\n      let bufferIndex = -1\n      let size = 0\n      /** @type {boolean | undefined} */\n      let tabs\n\n      while (index--) {\n        const chunk = chunks[index]\n\n        if (typeof chunk === 'string') {\n          bufferIndex = chunk.length\n\n          while (chunk.charCodeAt(bufferIndex - 1) === codes.space) {\n            size++\n            bufferIndex--\n          }\n\n          if (bufferIndex) break\n          bufferIndex = -1\n        }\n        // Number\n        else if (chunk === codes.horizontalTab) {\n          tabs = true\n          size++\n        } else if (chunk === codes.virtualSpace) {\n          // Empty\n        } else {\n          // Replacement character, exit.\n          index++\n          break\n        }\n      }\n\n      // Allow final trailing whitespace.\n      if (context._contentTypeTextTrailing && eventIndex === events.length) {\n        size = 0\n      }\n\n      if (size) {\n        const token = {\n          type:\n            eventIndex === events.length ||\n            tabs ||\n            size < constants.hardBreakPrefixSizeMin\n              ? types.lineSuffix\n              : types.hardBreakTrailing,\n          start: {\n            _bufferIndex: index\n              ? bufferIndex\n              : data.start._bufferIndex + bufferIndex,\n            _index: data.start._index + index,\n            line: data.end.line,\n            column: data.end.column - size,\n            offset: data.end.offset - size\n          },\n          end: {...data.end}\n        }\n\n        data.end = {...token.start}\n\n        if (data.start.offset === data.end.offset) {\n          Object.assign(data, token)\n        } else {\n          events.splice(\n            eventIndex,\n            0,\n            ['enter', token, context],\n            ['exit', token, context]\n          )\n          eventIndex += 2\n        }\n      }\n\n      eventIndex++\n    }\n  }\n\n  return events\n}\n", "/**\n * @import {Extension} from 'micromark-util-types'\n */\n\nimport {\n  attention,\n  autolink,\n  blockQuote,\n  characterEscape,\n  characterReference,\n  codeFenced,\n  codeIndented,\n  codeText,\n  definition,\n  hardBreakEscape,\n  headingAtx,\n  htmlFlow,\n  htmlText,\n  labelEnd,\n  labelStartImage,\n  labelStartLink,\n  lineEnding,\n  list,\n  setextUnderline,\n  thematicBreak\n} from 'micromark-core-commonmark'\nimport {codes} from 'micromark-util-symbol'\nimport {resolver as resolveText} from './initialize/text.js'\n\n/** @satisfies {Extension['document']} */\nexport const document = {\n  [codes.asterisk]: list,\n  [codes.plusSign]: list,\n  [codes.dash]: list,\n  [codes.digit0]: list,\n  [codes.digit1]: list,\n  [codes.digit2]: list,\n  [codes.digit3]: list,\n  [codes.digit4]: list,\n  [codes.digit5]: list,\n  [codes.digit6]: list,\n  [codes.digit7]: list,\n  [codes.digit8]: list,\n  [codes.digit9]: list,\n  [codes.greaterThan]: blockQuote\n}\n\n/** @satisfies {Extension['contentInitial']} */\nexport const contentInitial = {\n  [codes.leftSquareBracket]: definition\n}\n\n/** @satisfies {Extension['flowInitial']} */\nexport const flowInitial = {\n  [codes.horizontalTab]: codeIndented,\n  [codes.virtualSpace]: codeIndented,\n  [codes.space]: codeIndented\n}\n\n/** @satisfies {Extension['flow']} */\nexport const flow = {\n  [codes.numberSign]: headingAtx,\n  [codes.asterisk]: thematicBreak,\n  [codes.dash]: [setextUnderline, thematicBreak],\n  [codes.lessThan]: htmlFlow,\n  [codes.equalsTo]: setextUnderline,\n  [codes.underscore]: thematicBreak,\n  [codes.graveAccent]: codeFenced,\n  [codes.tilde]: codeFenced\n}\n\n/** @satisfies {Extension['string']} */\nexport const string = {\n  [codes.ampersand]: characterReference,\n  [codes.backslash]: characterEscape\n}\n\n/** @satisfies {Extension['text']} */\nexport const text = {\n  [codes.carriageReturn]: lineEnding,\n  [codes.lineFeed]: lineEnding,\n  [codes.carriageReturnLineFeed]: lineEnding,\n  [codes.exclamationMark]: labelStartImage,\n  [codes.ampersand]: characterReference,\n  [codes.asterisk]: attention,\n  [codes.lessThan]: [autolink, htmlText],\n  [codes.leftSquareBracket]: labelStartLink,\n  [codes.backslash]: [hardBreakEscape, characterEscape],\n  [codes.rightSquareBracket]: labelEnd,\n  [codes.underscore]: attention,\n  [codes.graveAccent]: codeText\n}\n\n/** @satisfies {Extension['insideSpan']} */\nexport const insideSpan = {null: [attention, resolveText]}\n\n/** @satisfies {Extension['attentionMarkers']} */\nexport const attentionMarkers = {null: [codes.asterisk, codes.underscore]}\n\n/** @satisfies {Extension['disable']} */\nexport const disable = {null: []}\n", "/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */\n\nimport createDebug from 'debug'\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {push, splice} from 'micromark-util-chunked'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, values} from 'micromark-util-symbol'\n\nconst debug = createDebug('micromark')\n\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */\nexport function createTokenizer(parser, initialize, from) {\n  /** @type {Point} */\n  let point = {\n    _bufferIndex: -1,\n    _index: 0,\n    line: (from && from.line) || 1,\n    column: (from && from.column) || 1,\n    offset: (from && from.offset) || 0\n  }\n  /** @type {Record<string, number>} */\n  const columnStart = {}\n  /** @type {Array<Construct>} */\n  const resolveAllConstructs = []\n  /** @type {Array<Chunk>} */\n  let chunks = []\n  /** @type {Array<Token>} */\n  let stack = []\n  /** @type {boolean | undefined} */\n  let consumed = true\n\n  /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */\n  const effects = {\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    consume,\n    enter,\n    exit,\n    interrupt: constructFactory(onsuccessfulcheck, {interrupt: true})\n  }\n\n  /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */\n  const context = {\n    code: codes.eof,\n    containerState: {},\n    defineSkip,\n    events: [],\n    now,\n    parser,\n    previous: codes.eof,\n    sliceSerialize,\n    sliceStream,\n    write\n  }\n\n  /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */\n  let state = initialize.tokenize.call(context, effects)\n\n  /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */\n  let expectedCode\n\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize)\n  }\n\n  return context\n\n  /** @type {TokenizeContext['write']} */\n  function write(slice) {\n    chunks = push(chunks, slice)\n\n    main()\n\n    // Exit if we’re not done, resolve might change stuff.\n    if (chunks[chunks.length - 1] !== codes.eof) {\n      return []\n    }\n\n    addResult(initialize, 0)\n\n    // Otherwise, resolve, and exit.\n    context.events = resolveAll(resolveAllConstructs, context.events, context)\n\n    return context.events\n  }\n\n  //\n  // Tools.\n  //\n\n  /** @type {TokenizeContext['sliceSerialize']} */\n  function sliceSerialize(token, expandTabs) {\n    return serializeChunks(sliceStream(token), expandTabs)\n  }\n\n  /** @type {TokenizeContext['sliceStream']} */\n  function sliceStream(token) {\n    return sliceChunks(chunks, token)\n  }\n\n  /** @type {TokenizeContext['now']} */\n  function now() {\n    // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n    const {_bufferIndex, _index, line, column, offset} = point\n    return {_bufferIndex, _index, line, column, offset}\n  }\n\n  /** @type {TokenizeContext['defineSkip']} */\n  function defineSkip(value) {\n    columnStart[value.line] = value.column\n    accountForPotentialSkip()\n    debug('position: define skip: `%j`', point)\n  }\n\n  //\n  // State management.\n  //\n\n  /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function main() {\n    /** @type {number} */\n    let chunkIndex\n\n    while (point._index < chunks.length) {\n      const chunk = chunks[point._index]\n\n      // If we’re in a buffer chunk, loop through it.\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index\n\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0\n        }\n\n        while (\n          point._index === chunkIndex &&\n          point._bufferIndex < chunk.length\n        ) {\n          go(chunk.charCodeAt(point._bufferIndex))\n        }\n      } else {\n        go(chunk)\n      }\n    }\n  }\n\n  /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function go(code) {\n    assert(consumed === true, 'expected character to be consumed')\n    consumed = undefined\n    debug('main: passing `%s` to %s', code, state && state.name)\n    expectedCode = code\n    assert(typeof state === 'function', 'expected state')\n    state = state(code)\n  }\n\n  /** @type {Effects['consume']} */\n  function consume(code) {\n    assert(code === expectedCode, 'expected given code to equal expected code')\n\n    debug('consume: `%s`', code)\n\n    assert(\n      consumed === undefined,\n      'expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used'\n    )\n    assert(\n      code === null\n        ? context.events.length === 0 ||\n            context.events[context.events.length - 1][0] === 'exit'\n        : context.events[context.events.length - 1][0] === 'enter',\n      'expected last token to be open'\n    )\n\n    if (markdownLineEnding(code)) {\n      point.line++\n      point.column = 1\n      point.offset += code === codes.carriageReturnLineFeed ? 2 : 1\n      accountForPotentialSkip()\n      debug('position: after eol: `%j`', point)\n    } else if (code !== codes.virtualSpace) {\n      point.column++\n      point.offset++\n    }\n\n    // Not in a string chunk.\n    if (point._bufferIndex < 0) {\n      point._index++\n    } else {\n      point._bufferIndex++\n\n      // At end of string chunk.\n      if (\n        point._bufferIndex ===\n        // Points w/ non-negative `_bufferIndex` reference\n        // strings.\n        /** @type {string} */ (chunks[point._index]).length\n      ) {\n        point._bufferIndex = -1\n        point._index++\n      }\n    }\n\n    // Expose the previous character.\n    context.previous = code\n\n    // Mark as consumed.\n    consumed = true\n  }\n\n  /** @type {Effects['enter']} */\n  function enter(type, fields) {\n    /** @type {Token} */\n    // @ts-expect-error Patch instead of assign required fields to help GC.\n    const token = fields || {}\n    token.type = type\n    token.start = now()\n\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n    debug('enter: `%s`', type)\n\n    context.events.push(['enter', token, context])\n\n    stack.push(token)\n\n    return token\n  }\n\n  /** @type {Effects['exit']} */\n  function exit(type) {\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n\n    const token = stack.pop()\n    assert(token, 'cannot close w/o open tokens')\n    token.end = now()\n\n    assert(type === token.type, 'expected exit token to match current token')\n\n    assert(\n      !(\n        token.start._index === token.end._index &&\n        token.start._bufferIndex === token.end._bufferIndex\n      ),\n      'expected non-empty token (`' + type + '`)'\n    )\n\n    debug('exit: `%s`', token.type)\n    context.events.push(['exit', token, context])\n\n    return token\n  }\n\n  /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from)\n  }\n\n  /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulcheck(_, info) {\n    info.restore()\n  }\n\n  /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */\n  function constructFactory(onreturn, fields) {\n    return hook\n\n    /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */\n    function hook(constructs, returnState, bogusState) {\n      /** @type {ReadonlyArray<Construct>} */\n      let listOfConstructs\n      /** @type {number} */\n      let constructIndex\n      /** @type {Construct} */\n      let currentConstruct\n      /** @type {Info} */\n      let info\n\n      return Array.isArray(constructs)\n        ? /* c8 ignore next 1 */\n          handleListOfConstructs(constructs)\n        : 'tokenize' in constructs\n          ? // Looks like a construct.\n            handleListOfConstructs([/** @type {Construct} */ (constructs)])\n          : handleMapOfConstructs(constructs)\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleMapOfConstructs(map) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          const left = code !== null && map[code]\n          const all = code !== null && map.null\n          const list = [\n            // To do: add more extension tests.\n            /* c8 ignore next 2 */\n            ...(Array.isArray(left) ? left : left ? [left] : []),\n            ...(Array.isArray(all) ? all : all ? [all] : [])\n          ]\n\n          return handleListOfConstructs(list)(code)\n        }\n      }\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleListOfConstructs(list) {\n        listOfConstructs = list\n        constructIndex = 0\n\n        if (list.length === 0) {\n          assert(bogusState, 'expected `bogusState` to be given')\n          return bogusState\n        }\n\n        return handleConstruct(list[constructIndex])\n      }\n\n      /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */\n      function handleConstruct(construct) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          // To do: not needed to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store()\n          currentConstruct = construct\n\n          if (!construct.partial) {\n            context.currentConstruct = construct\n          }\n\n          // Always populated by defaults.\n          assert(\n            context.parser.constructs.disable.null,\n            'expected `disable.null` to be populated'\n          )\n\n          if (\n            construct.name &&\n            context.parser.constructs.disable.null.includes(construct.name)\n          ) {\n            return nok(code)\n          }\n\n          return construct.tokenize.call(\n            // If we do have fields, create an object w/ `context` as its\n            // prototype.\n            // This allows a “live binding”, which is needed for `interrupt`.\n            fields ? Object.assign(Object.create(context), fields) : context,\n            effects,\n            ok,\n            nok\n          )(code)\n        }\n      }\n\n      /** @type {State} */\n      function ok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        onreturn(currentConstruct, info)\n        return returnState\n      }\n\n      /** @type {State} */\n      function nok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        info.restore()\n\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex])\n        }\n\n        return bogusState\n      }\n    }\n  }\n\n  /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addResult(construct, from) {\n    if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n      resolveAllConstructs.push(construct)\n    }\n\n    if (construct.resolve) {\n      splice(\n        context.events,\n        from,\n        context.events.length - from,\n        construct.resolve(context.events.slice(from), context)\n      )\n    }\n\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context)\n    }\n\n    assert(\n      construct.partial ||\n        context.events.length === 0 ||\n        context.events[context.events.length - 1][0] === 'exit',\n      'expected last token to end'\n    )\n  }\n\n  /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */\n  function store() {\n    const startPoint = now()\n    const startPrevious = context.previous\n    const startCurrentConstruct = context.currentConstruct\n    const startEventsIndex = context.events.length\n    const startStack = Array.from(stack)\n\n    return {from: startEventsIndex, restore}\n\n    /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */\n    function restore() {\n      point = startPoint\n      context.previous = startPrevious\n      context.currentConstruct = startCurrentConstruct\n      context.events.length = startEventsIndex\n      stack = startStack\n      accountForPotentialSkip()\n      debug('position: restore: `%j`', point)\n    }\n  }\n\n  /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line]\n      point.offset += columnStart[point.line] - 1\n    }\n  }\n}\n\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */\nfunction sliceChunks(chunks, token) {\n  const startIndex = token.start._index\n  const startBufferIndex = token.start._bufferIndex\n  const endIndex = token.end._index\n  const endBufferIndex = token.end._bufferIndex\n  /** @type {Array<Chunk>} */\n  let view\n\n  if (startIndex === endIndex) {\n    assert(endBufferIndex > -1, 'expected non-negative end buffer index')\n    assert(startBufferIndex > -1, 'expected non-negative start buffer index')\n    // @ts-expect-error `_bufferIndex` is used on string chunks.\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)]\n  } else {\n    view = chunks.slice(startIndex, endIndex)\n\n    if (startBufferIndex > -1) {\n      const head = view[0]\n      if (typeof head === 'string') {\n        view[0] = head.slice(startBufferIndex)\n        /* c8 ignore next 4 -- used to be used, no longer */\n      } else {\n        assert(startBufferIndex === 0, 'expected `startBufferIndex` to be `0`')\n        view.shift()\n      }\n    }\n\n    if (endBufferIndex > 0) {\n      // @ts-expect-error `_bufferIndex` is used on string chunks.\n      view.push(chunks[endIndex].slice(0, endBufferIndex))\n    }\n  }\n\n  return view\n}\n\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */\nfunction serializeChunks(chunks, expandTabs) {\n  let index = -1\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {boolean | undefined} */\n  let atTab\n\n  while (++index < chunks.length) {\n    const chunk = chunks[index]\n    /** @type {string} */\n    let value\n\n    if (typeof chunk === 'string') {\n      value = chunk\n    } else\n      switch (chunk) {\n        case codes.carriageReturn: {\n          value = values.cr\n\n          break\n        }\n\n        case codes.lineFeed: {\n          value = values.lf\n\n          break\n        }\n\n        case codes.carriageReturnLineFeed: {\n          value = values.cr + values.lf\n\n          break\n        }\n\n        case codes.horizontalTab: {\n          value = expandTabs ? values.space : values.ht\n\n          break\n        }\n\n        case codes.virtualSpace: {\n          if (!expandTabs && atTab) continue\n          value = values.space\n\n          break\n        }\n\n        default: {\n          assert(typeof chunk === 'number', 'expected number')\n          // Currently only replacement character.\n          value = String.fromCharCode(chunk)\n        }\n      }\n\n    atTab = chunk === codes.horizontalTab\n    result.push(value)\n  }\n\n  return result.join('')\n}\n", "/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */\n\nimport {combineExtensions} from 'micromark-util-combine-extensions'\nimport {content} from './initialize/content.js'\nimport {document} from './initialize/document.js'\nimport {flow} from './initialize/flow.js'\nimport {string, text} from './initialize/text.js'\nimport * as defaultConstructs from './constructs.js'\nimport {createTokenizer} from './create-tokenizer.js'\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */\nexport function parse(options) {\n  const settings = options || {}\n  const constructs = /** @type {FullNormalizedExtension} */ (\n    combineExtensions([defaultConstructs, ...(settings.extensions || [])])\n  )\n\n  /** @type {ParseContext} */\n  const parser = {\n    constructs,\n    content: create(content),\n    defined: [],\n    document: create(document),\n    flow: create(flow),\n    lazy: {},\n    string: create(string),\n    text: create(text)\n  }\n\n  return parser\n\n  /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */\n  function create(initial) {\n    return creator\n    /** @type {Create} */\n    function creator(from) {\n      return createTokenizer(parser, initial, from)\n    }\n  }\n}\n", "/**\n * @import {Event} from 'micromark-util-types'\n */\n\nimport {subtokenize} from 'micromark-util-subtokenize'\n\n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */\nexport function postprocess(events) {\n  while (!subtokenize(events)) {\n    // Empty\n  }\n\n  return events\n}\n", "/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */\n\n/**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */\n\nimport {codes, constants} from 'micromark-util-symbol'\n\nconst search = /[\\0\\t\\n\\r]/g\n\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */\nexport function preprocess() {\n  let column = 1\n  let buffer = ''\n  /** @type {boolean | undefined} */\n  let start = true\n  /** @type {boolean | undefined} */\n  let atCarriageReturn\n\n  return preprocessor\n\n  /** @type {Preprocessor} */\n  // eslint-disable-next-line complexity\n  function preprocessor(value, encoding, end) {\n    /** @type {Array<Chunk>} */\n    const chunks = []\n    /** @type {RegExpMatchArray | null} */\n    let match\n    /** @type {number} */\n    let next\n    /** @type {number} */\n    let startPosition\n    /** @type {number} */\n    let endPosition\n    /** @type {Code} */\n    let code\n\n    value =\n      buffer +\n      (typeof value === 'string'\n        ? value.toString()\n        : new TextDecoder(encoding || undefined).decode(value))\n\n    startPosition = 0\n    buffer = ''\n\n    if (start) {\n      // To do: `markdown-rs` actually parses BOMs (byte order mark).\n      if (value.charCodeAt(0) === codes.byteOrderMarker) {\n        startPosition++\n      }\n\n      start = undefined\n    }\n\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition\n      match = search.exec(value)\n      endPosition =\n        match && match.index !== undefined ? match.index : value.length\n      code = value.charCodeAt(endPosition)\n\n      if (!match) {\n        buffer = value.slice(startPosition)\n        break\n      }\n\n      if (\n        code === codes.lf &&\n        startPosition === endPosition &&\n        atCarriageReturn\n      ) {\n        chunks.push(codes.carriageReturnLineFeed)\n        atCarriageReturn = undefined\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(codes.carriageReturn)\n          atCarriageReturn = undefined\n        }\n\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition))\n          column += endPosition - startPosition\n        }\n\n        switch (code) {\n          case codes.nul: {\n            chunks.push(codes.replacementCharacter)\n            column++\n\n            break\n          }\n\n          case codes.ht: {\n            next = Math.ceil(column / constants.tabSize) * constants.tabSize\n            chunks.push(codes.horizontalTab)\n            while (column++ < next) chunks.push(codes.virtualSpace)\n\n            break\n          }\n\n          case codes.lf: {\n            chunks.push(codes.lineFeed)\n            column = 1\n\n            break\n          }\n\n          default: {\n            atCarriageReturn = true\n            column = 1\n          }\n        }\n      }\n\n      startPosition = endPosition + 1\n    }\n\n    if (end) {\n      if (atCarriageReturn) chunks.push(codes.carriageReturn)\n      if (buffer) chunks.push(buffer)\n      chunks.push(codes.eof)\n    }\n\n    return chunks\n  }\n}\n", "import {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {codes, constants} from 'micromark-util-symbol'\n\nconst characterEscapeOrReference =\n  /\\\\([!-/:-@[-`{-~])|&(#(?:\\d{1,7}|x[\\da-f]{1,6})|[\\da-z]{1,31});/gi\n\n/**\n * Decode markdown strings (which occur in places such as fenced code info\n * strings, destinations, labels, and titles).\n *\n * The “string” content type allows character escapes and -references.\n * This decodes those.\n *\n * @param {string} value\n *   Value to decode.\n * @returns {string}\n *   Decoded value.\n */\nexport function decodeString(value) {\n  return value.replace(characterEscapeOrReference, decode)\n}\n\n/**\n * @param {string} $0\n *   Match.\n * @param {string} $1\n *   Character escape.\n * @param {string} $2\n *   Character reference.\n * @returns {string}\n *   Decoded value\n */\nfunction decode($0, $1, $2) {\n  if ($1) {\n    // Escape.\n    return $1\n  }\n\n  // Reference.\n  const head = $2.charCodeAt(0)\n\n  if (head === codes.numberSign) {\n    const head = $2.charCodeAt(1)\n    const hex = head === codes.lowercaseX || head === codes.uppercaseX\n    return decodeNumericCharacterReference(\n      $2.slice(hex ? 2 : 1),\n      hex ? constants.numericBaseHexadecimal : constants.numericBaseDecimal\n    )\n  }\n\n  return decodeNamedCharacterReference($2) || $0\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n */\n\n/**\n * Serialize the positional info of a point, position (start and end points),\n * or node.\n *\n * @param {Node | NodeLike | Point | PointLike | Position | PositionLike | null | undefined} [value]\n *   Node, position, or point.\n * @returns {string}\n *   Pretty printed positional info of a node (`string`).\n *\n *   In the format of a range `ls:cs-le:ce` (when given `node` or `position`)\n *   or a point `l:c` (when given `point`), where `l` stands for line, `c` for\n *   column, `s` for `start`, and `e` for end.\n *   An empty string (`''`) is returned if the given value is neither `node`,\n *   `position`, nor `point`.\n */\nexport function stringifyPosition(value) {\n  // Nothing.\n  if (!value || typeof value !== 'object') {\n    return ''\n  }\n\n  // Node.\n  if ('position' in value || 'type' in value) {\n    return position(value.position)\n  }\n\n  // Position.\n  if ('start' in value || 'end' in value) {\n    return position(value)\n  }\n\n  // Point.\n  if ('line' in value || 'column' in value) {\n    return point(value)\n  }\n\n  // ?\n  return ''\n}\n\n/**\n * @param {Point | PointLike | null | undefined} point\n * @returns {string}\n */\nfunction point(point) {\n  return index(point && point.line) + ':' + index(point && point.column)\n}\n\n/**\n * @param {Position | PositionLike | null | undefined} pos\n * @returns {string}\n */\nfunction position(pos) {\n  return point(pos && pos.start) + '-' + point(pos && pos.end)\n}\n\n/**\n * @param {number | null | undefined} value\n * @returns {number}\n */\nfunction index(value) {\n  return value && typeof value === 'number' ? value : 1\n}\n", "/**\n * @import {\n *   Break,\n *   Blockquote,\n *   Code,\n *   Definition,\n *   Emphasis,\n *   Heading,\n *   Html,\n *   Image,\n *   InlineCode,\n *   Link,\n *   ListItem,\n *   List,\n *   Nodes,\n *   Paragraph,\n *   PhrasingContent,\n *   ReferenceType,\n *   Root,\n *   Strong,\n *   Text,\n *   ThematicBreak\n * } from 'mdast'\n * @import {\n *   Encoding,\n *   Event,\n *   Token,\n *   Value\n * } from 'micromark-util-types'\n * @import {Point} from 'unist'\n * @import {\n *   CompileContext,\n *   CompileData,\n *   Config,\n *   Extension,\n *   Handle,\n *   OnEnterError,\n *   Options\n * } from './types.js'\n */\n\nimport {ok as assert} from 'devlop'\nimport {toString} from 'mdast-util-to-string'\nimport {parse, postprocess, preprocess} from 'micromark'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {decodeString} from 'micromark-util-decode-string'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Turn markdown into a syntax tree.\n *\n * @overload\n * @param {Value} value\n * @param {Encoding | null | undefined} [encoding]\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @overload\n * @param {Value} value\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @param {Value} value\n *   Markdown to parse.\n * @param {Encoding | Options | null | undefined} [encoding]\n *   Character encoding for when `value` is `Buffer`.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {Root}\n *   mdast tree.\n */\nexport function fromMarkdown(value, encoding, options) {\n  if (typeof encoding !== 'string') {\n    options = encoding\n    encoding = undefined\n  }\n\n  return compiler(options)(\n    postprocess(\n      parse(options)\n        .document()\n        .write(preprocess()(value, encoding, true))\n    )\n  )\n}\n\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */\nfunction compiler(options) {\n  /** @type {Config} */\n  const config = {\n    transforms: [],\n    canContainEols: ['emphasis', 'fragment', 'heading', 'paragraph', 'strong'],\n    enter: {\n      autolink: opener(link),\n      autolinkProtocol: onenterdata,\n      autolinkEmail: onenterdata,\n      atxHeading: opener(heading),\n      blockQuote: opener(blockQuote),\n      characterEscape: onenterdata,\n      characterReference: onenterdata,\n      codeFenced: opener(codeFlow),\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: opener(codeFlow, buffer),\n      codeText: opener(codeText, buffer),\n      codeTextData: onenterdata,\n      data: onenterdata,\n      codeFlowValue: onenterdata,\n      definition: opener(definition),\n      definitionDestinationString: buffer,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: opener(emphasis),\n      hardBreakEscape: opener(hardBreak),\n      hardBreakTrailing: opener(hardBreak),\n      htmlFlow: opener(html, buffer),\n      htmlFlowData: onenterdata,\n      htmlText: opener(html, buffer),\n      htmlTextData: onenterdata,\n      image: opener(image),\n      label: buffer,\n      link: opener(link),\n      listItem: opener(listItem),\n      listItemValue: onenterlistitemvalue,\n      listOrdered: opener(list, onenterlistordered),\n      listUnordered: opener(list),\n      paragraph: opener(paragraph),\n      reference: onenterreference,\n      referenceString: buffer,\n      resourceDestinationString: buffer,\n      resourceTitleString: buffer,\n      setextHeading: opener(heading),\n      strong: opener(strong),\n      thematicBreak: opener(thematicBreak)\n    },\n    exit: {\n      atxHeading: closer(),\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolink: closer(),\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: closer(),\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      characterReference: onexitcharacterreference,\n      codeFenced: closer(onexitcodefenced),\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onexitcodefencedfencemeta,\n      codeFlowValue: onexitdata,\n      codeIndented: closer(onexitcodeindented),\n      codeText: closer(onexitcodetext),\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: closer(),\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: closer(),\n      hardBreakEscape: closer(onexithardbreak),\n      hardBreakTrailing: closer(onexithardbreak),\n      htmlFlow: closer(onexithtmlflow),\n      htmlFlowData: onexitdata,\n      htmlText: closer(onexithtmltext),\n      htmlTextData: onexitdata,\n      image: closer(onexitimage),\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: closer(onexitlink),\n      listItem: closer(),\n      listOrdered: closer(),\n      listUnordered: closer(),\n      paragraph: closer(),\n      referenceString: onexitreferencestring,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      resource: onexitresource,\n      setextHeading: closer(onexitsetextheading),\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: closer(),\n      thematicBreak: closer()\n    }\n  }\n\n  configure(config, (options || {}).mdastExtensions || [])\n\n  /** @type {CompileData} */\n  const data = {}\n\n  return compile\n\n  /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */\n  function compile(events) {\n    /** @type {Root} */\n    let tree = {type: 'root', children: []}\n    /** @type {Omit<CompileContext, 'sliceSerialize'>} */\n    const context = {\n      stack: [tree],\n      tokenStack: [],\n      config,\n      enter,\n      exit,\n      buffer,\n      resume,\n      data\n    }\n    /** @type {Array<number>} */\n    const listStack = []\n    let index = -1\n\n    while (++index < events.length) {\n      // We preprocess lists to add `listItem` tokens, and to infer whether\n      // items the list itself are spread out.\n      if (\n        events[index][1].type === types.listOrdered ||\n        events[index][1].type === types.listUnordered\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          const tail = listStack.pop()\n          assert(typeof tail === 'number', 'expected list ot be open')\n          index = prepareList(events, tail, index)\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      const handler = config[events[index][0]]\n\n      if (own.call(handler, events[index][1].type)) {\n        handler[events[index][1].type].call(\n          Object.assign(\n            {sliceSerialize: events[index][2].sliceSerialize},\n            context\n          ),\n          events[index][1]\n        )\n      }\n    }\n\n    // Handle tokens still being open.\n    if (context.tokenStack.length > 0) {\n      const tail = context.tokenStack[context.tokenStack.length - 1]\n      const handler = tail[1] || defaultOnError\n      handler.call(context, undefined, tail[0])\n    }\n\n    // Figure out `root` position.\n    tree.position = {\n      start: point(\n        events.length > 0 ? events[0][1].start : {line: 1, column: 1, offset: 0}\n      ),\n      end: point(\n        events.length > 0\n          ? events[events.length - 2][1].end\n          : {line: 1, column: 1, offset: 0}\n      )\n    }\n\n    // Call transforms.\n    index = -1\n    while (++index < config.transforms.length) {\n      tree = config.transforms[index](tree) || tree\n    }\n\n    return tree\n  }\n\n  /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */\n  function prepareList(events, start, length) {\n    let index = start - 1\n    let containerBalance = -1\n    let listSpread = false\n    /** @type {Token | undefined} */\n    let listItem\n    /** @type {number | undefined} */\n    let lineIndex\n    /** @type {number | undefined} */\n    let firstBlankLineIndex\n    /** @type {boolean | undefined} */\n    let atMarker\n\n    while (++index <= length) {\n      const event = events[index]\n\n      switch (event[1].type) {\n        case types.listUnordered:\n        case types.listOrdered:\n        case types.blockQuote: {\n          if (event[0] === 'enter') {\n            containerBalance++\n          } else {\n            containerBalance--\n          }\n\n          atMarker = undefined\n\n          break\n        }\n\n        case types.lineEndingBlank: {\n          if (event[0] === 'enter') {\n            if (\n              listItem &&\n              !atMarker &&\n              !containerBalance &&\n              !firstBlankLineIndex\n            ) {\n              firstBlankLineIndex = index\n            }\n\n            atMarker = undefined\n          }\n\n          break\n        }\n\n        case types.linePrefix:\n        case types.listItemValue:\n        case types.listItemMarker:\n        case types.listItemPrefix:\n        case types.listItemPrefixWhitespace: {\n          // Empty.\n\n          break\n        }\n\n        default: {\n          atMarker = undefined\n        }\n      }\n\n      if (\n        (!containerBalance &&\n          event[0] === 'enter' &&\n          event[1].type === types.listItemPrefix) ||\n        (containerBalance === -1 &&\n          event[0] === 'exit' &&\n          (event[1].type === types.listUnordered ||\n            event[1].type === types.listOrdered))\n      ) {\n        if (listItem) {\n          let tailIndex = index\n          lineIndex = undefined\n\n          while (tailIndex--) {\n            const tailEvent = events[tailIndex]\n\n            if (\n              tailEvent[1].type === types.lineEnding ||\n              tailEvent[1].type === types.lineEndingBlank\n            ) {\n              if (tailEvent[0] === 'exit') continue\n\n              if (lineIndex) {\n                events[lineIndex][1].type = types.lineEndingBlank\n                listSpread = true\n              }\n\n              tailEvent[1].type = types.lineEnding\n              lineIndex = tailIndex\n            } else if (\n              tailEvent[1].type === types.linePrefix ||\n              tailEvent[1].type === types.blockQuotePrefix ||\n              tailEvent[1].type === types.blockQuotePrefixWhitespace ||\n              tailEvent[1].type === types.blockQuoteMarker ||\n              tailEvent[1].type === types.listItemIndent\n            ) {\n              // Empty\n            } else {\n              break\n            }\n          }\n\n          if (\n            firstBlankLineIndex &&\n            (!lineIndex || firstBlankLineIndex < lineIndex)\n          ) {\n            listItem._spread = true\n          }\n\n          // Fix position.\n          listItem.end = Object.assign(\n            {},\n            lineIndex ? events[lineIndex][1].start : event[1].end\n          )\n\n          events.splice(lineIndex || index, 0, ['exit', listItem, event[2]])\n          index++\n          length++\n        }\n\n        // Create a new list item.\n        if (event[1].type === types.listItemPrefix) {\n          /** @type {Token} */\n          const item = {\n            type: 'listItem',\n            _spread: false,\n            start: Object.assign({}, event[1].start),\n            // @ts-expect-error: we’ll add `end` in a second.\n            end: undefined\n          }\n          listItem = item\n          events.splice(index, 0, ['enter', item, event[2]])\n          index++\n          length++\n          firstBlankLineIndex = undefined\n          atMarker = true\n        }\n      }\n    }\n\n    events[start][1]._spread = listSpread\n    return length\n  }\n\n  /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Nodes} create\n   *   Create a node.\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function opener(create, and) {\n    return open\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function open(token) {\n      enter.call(this, create(token), token)\n      if (and) and.call(this, token)\n    }\n  }\n\n  /**\n   * @type {CompileContext['buffer']}\n   */\n  function buffer() {\n    this.stack.push({type: 'fragment', children: []})\n  }\n\n  /**\n   * @type {CompileContext['enter']}\n   */\n  function enter(node, token, errorHandler) {\n    const parent = this.stack[this.stack.length - 1]\n    assert(parent, 'expected `parent`')\n    assert('children' in parent, 'expected `parent`')\n    /** @type {Array<Nodes>} */\n    const siblings = parent.children\n    siblings.push(node)\n    this.stack.push(node)\n    this.tokenStack.push([token, errorHandler || undefined])\n    node.position = {\n      start: point(token.start),\n      // @ts-expect-error: `end` will be patched later.\n      end: undefined\n    }\n  }\n\n  /**\n   * Create a closer handle.\n   *\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function closer(and) {\n    return close\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function close(token) {\n      if (and) and.call(this, token)\n      exit.call(this, token)\n    }\n  }\n\n  /**\n   * @type {CompileContext['exit']}\n   */\n  function exit(token, onExitError) {\n    const node = this.stack.pop()\n    assert(node, 'expected `node`')\n    const open = this.tokenStack.pop()\n\n    if (!open) {\n      throw new Error(\n        'Cannot close `' +\n          token.type +\n          '` (' +\n          stringifyPosition({start: token.start, end: token.end}) +\n          '): it’s not open'\n      )\n    } else if (open[0].type !== token.type) {\n      if (onExitError) {\n        onExitError.call(this, token, open[0])\n      } else {\n        const handler = open[1] || defaultOnError\n        handler.call(this, token, open[0])\n      }\n    }\n\n    assert(node.type !== 'fragment', 'unexpected fragment `exit`ed')\n    assert(node.position, 'expected `position` to be defined')\n    node.position.end = point(token.end)\n  }\n\n  /**\n   * @type {CompileContext['resume']}\n   */\n  function resume() {\n    return toString(this.stack.pop())\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered() {\n    this.data.expectingFirstListItemValue = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (this.data.expectingFirstListItemValue) {\n      const ancestor = this.stack[this.stack.length - 2]\n      assert(ancestor, 'expected nodes on stack')\n      assert(ancestor.type === 'list', 'expected list on stack')\n      ancestor.start = Number.parseInt(\n        this.sliceSerialize(token),\n        constants.numericBaseDecimal\n      )\n      this.data.expectingFirstListItemValue = undefined\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n    node.lang = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfencemeta() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n    node.meta = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    // Exit if this is the closing fence.\n    if (this.data.flowCodeInside) return\n    this.buffer()\n    this.data.flowCodeInside = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefenced() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '')\n    this.data.flowCodeInside = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeindented() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/(\\r?\\n|\\r)$/g, '')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.label = label\n    node.identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'heading', 'expected heading on stack')\n\n    if (!node.depth) {\n      const depth = this.sliceSerialize(token).length\n\n      assert(\n        depth === 1 ||\n          depth === 2 ||\n          depth === 3 ||\n          depth === 4 ||\n          depth === 5 ||\n          depth === 6,\n        'expected `depth` between `1` and `6`'\n      )\n\n      node.depth = depth\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    this.data.setextHeadingSlurpLineEnding = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'heading', 'expected heading on stack')\n\n    node.depth =\n      this.sliceSerialize(token).codePointAt(0) === codes.equalsTo ? 1 : 2\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    this.data.setextHeadingSlurpLineEnding = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterdata(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert('children' in node, 'expected parent on stack')\n    /** @type {Array<Nodes>} */\n    const siblings = node.children\n\n    let tail = siblings[siblings.length - 1]\n\n    if (!tail || tail.type !== 'text') {\n      // Add a new text node.\n      tail = text()\n      tail.position = {\n        start: point(token.start),\n        // @ts-expect-error: we’ll add `end` later.\n        end: undefined\n      }\n      siblings.push(tail)\n    }\n\n    this.stack.push(tail)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitdata(token) {\n    const tail = this.stack.pop()\n    assert(tail, 'expected a `node` to be on the stack')\n    assert('value' in tail, 'expected a `literal` to be on the stack')\n    assert(tail.position, 'expected `node` to have an open position')\n    tail.value += this.sliceSerialize(token)\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlineending(token) {\n    const context = this.stack[this.stack.length - 1]\n    assert(context, 'expected `node`')\n\n    // If we’re at a hard break, include the line ending in there.\n    if (this.data.atHardBreak) {\n      assert('children' in context, 'expected `parent`')\n      const tail = context.children[context.children.length - 1]\n      assert(tail.position, 'expected tail to have a starting position')\n      tail.position.end = point(token.end)\n      this.data.atHardBreak = undefined\n      return\n    }\n\n    if (\n      !this.data.setextHeadingSlurpLineEnding &&\n      config.canContainEols.includes(context.type)\n    ) {\n      onenterdata.call(this, token)\n      onexitdata.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithardbreak() {\n    this.data.atHardBreak = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmlflow() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmltext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcodetext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'inlineCode', 'expected inline code on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlink() {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    this.data.referenceType = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitimage() {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'image', 'expected image on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    this.data.referenceType = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabeltext(token) {\n    const string = this.sliceSerialize(token)\n    const ancestor = this.stack[this.stack.length - 2]\n    assert(ancestor, 'expected ancestor on stack')\n    assert(\n      ancestor.type === 'image' || ancestor.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    ancestor.label = decodeString(string)\n    // @ts-expect-error: same as above.\n    ancestor.identifier = normalizeIdentifier(string).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabel() {\n    const fragment = this.stack[this.stack.length - 1]\n    assert(fragment, 'expected node on stack')\n    assert(fragment.type === 'fragment', 'expected fragment on stack')\n    const value = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // Assume a reference.\n    this.data.inReference = true\n\n    if (node.type === 'link') {\n      /** @type {Array<PhrasingContent>} */\n      const children = fragment.children\n\n      node.children = children\n    } else {\n      node.alt = value\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcedestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcetitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresource() {\n    this.data.inReference = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterreference() {\n    this.data.referenceType = 'collapsed'\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitreferencestring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image reference or link reference on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    node.label = label\n    // @ts-expect-error: same as above.\n    node.identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n    this.data.referenceType = 'full'\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcharacterreferencemarker(token) {\n    assert(\n      token.type === 'characterReferenceMarkerNumeric' ||\n        token.type === 'characterReferenceMarkerHexadecimal'\n    )\n    this.data.characterReferenceType = token.type\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const data = this.sliceSerialize(token)\n    const type = this.data.characterReferenceType\n    /** @type {string} */\n    let value\n\n    if (type) {\n      value = decodeNumericCharacterReference(\n        data,\n        type === types.characterReferenceMarkerNumeric\n          ? constants.numericBaseDecimal\n          : constants.numericBaseHexadecimal\n      )\n      this.data.characterReferenceType = undefined\n    } else {\n      const result = decodeNamedCharacterReference(data)\n      assert(result !== false, 'expected reference to decode')\n      value = result\n    }\n\n    const tail = this.stack[this.stack.length - 1]\n    assert(tail, 'expected `node`')\n    assert('value' in tail, 'expected `node.value`')\n    tail.value += value\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreference(token) {\n    const tail = this.stack.pop()\n    assert(tail, 'expected `node`')\n    assert(tail.position, 'expected `node.position`')\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    node.url = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    node.url = 'mailto:' + this.sliceSerialize(token)\n  }\n\n  //\n  // Creaters.\n  //\n\n  /** @returns {Blockquote} */\n  function blockQuote() {\n    return {type: 'blockquote', children: []}\n  }\n\n  /** @returns {Code} */\n  function codeFlow() {\n    return {type: 'code', lang: null, meta: null, value: ''}\n  }\n\n  /** @returns {InlineCode} */\n  function codeText() {\n    return {type: 'inlineCode', value: ''}\n  }\n\n  /** @returns {Definition} */\n  function definition() {\n    return {\n      type: 'definition',\n      identifier: '',\n      label: null,\n      title: null,\n      url: ''\n    }\n  }\n\n  /** @returns {Emphasis} */\n  function emphasis() {\n    return {type: 'emphasis', children: []}\n  }\n\n  /** @returns {Heading} */\n  function heading() {\n    return {\n      type: 'heading',\n      // @ts-expect-error `depth` will be set later.\n      depth: 0,\n      children: []\n    }\n  }\n\n  /** @returns {Break} */\n  function hardBreak() {\n    return {type: 'break'}\n  }\n\n  /** @returns {Html} */\n  function html() {\n    return {type: 'html', value: ''}\n  }\n\n  /** @returns {Image} */\n  function image() {\n    return {type: 'image', title: null, url: '', alt: null}\n  }\n\n  /** @returns {Link} */\n  function link() {\n    return {type: 'link', title: null, url: '', children: []}\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {List}\n   */\n  function list(token) {\n    return {\n      type: 'list',\n      ordered: token.type === 'listOrdered',\n      start: null,\n      spread: token._spread,\n      children: []\n    }\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */\n  function listItem(token) {\n    return {\n      type: 'listItem',\n      spread: token._spread,\n      checked: null,\n      children: []\n    }\n  }\n\n  /** @returns {Paragraph} */\n  function paragraph() {\n    return {type: 'paragraph', children: []}\n  }\n\n  /** @returns {Strong} */\n  function strong() {\n    return {type: 'strong', children: []}\n  }\n\n  /** @returns {Text} */\n  function text() {\n    return {type: 'text', value: ''}\n  }\n\n  /** @returns {ThematicBreak} */\n  function thematicBreak() {\n    return {type: 'thematicBreak'}\n  }\n}\n\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */\nfunction point(d) {\n  return {line: d.line, column: d.column, offset: d.offset}\n}\n\n/**\n * @param {Config} combined\n * @param {Array<Array<Extension> | Extension>} extensions\n * @returns {undefined}\n */\nfunction configure(combined, extensions) {\n  let index = -1\n\n  while (++index < extensions.length) {\n    const value = extensions[index]\n\n    if (Array.isArray(value)) {\n      configure(combined, value)\n    } else {\n      extension(combined, value)\n    }\n  }\n}\n\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {undefined}\n */\nfunction extension(combined, extension) {\n  /** @type {keyof Extension} */\n  let key\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      switch (key) {\n        case 'canContainEols': {\n          const right = extension[key]\n          if (right) {\n            combined[key].push(...right)\n          }\n\n          break\n        }\n\n        case 'transforms': {\n          const right = extension[key]\n          if (right) {\n            combined[key].push(...right)\n          }\n\n          break\n        }\n\n        case 'enter':\n        case 'exit': {\n          const right = extension[key]\n          if (right) {\n            Object.assign(combined[key], right)\n          }\n\n          break\n        }\n        // No default\n      }\n    }\n  }\n}\n\n/** @type {OnEnterError} */\nfunction defaultOnError(left, right) {\n  if (left) {\n    throw new Error(\n      'Cannot close `' +\n        left.type +\n        '` (' +\n        stringifyPosition({start: left.start, end: left.end}) +\n        '): a different token (`' +\n        right.type +\n        '`, ' +\n        stringifyPosition({start: right.start, end: right.end}) +\n        ') is open'\n    )\n  } else {\n    throw new Error(\n      'Cannot close document, a token (`' +\n        right.type +\n        '`, ' +\n        stringifyPosition({start: right.start, end: right.end}) +\n        ') is still open'\n    )\n  }\n}\n", "/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-from-markdown').Options} FromMarkdownOptions\n * @typedef {import('unified').Parser<Root>} Parser\n * @typedef {import('unified').Processor<Root>} Processor\n */\n\n/**\n * @typedef {Omit<FromMarkdownOptions, 'extensions' | 'mdastExtensions'>} Options\n */\n\nimport {fromMarkdown} from 'mdast-util-from-markdown'\n\n/**\n * Aadd support for parsing from markdown.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function remarkParse(options) {\n  /** @type {Processor} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this\n\n  self.parser = parser\n\n  /**\n   * @type {Parser}\n   */\n  function parser(doc) {\n    return fromMarkdown(doc, {\n      ...self.data('settings'),\n      ...options,\n      // Note: these options are not in the readme.\n      // The goal is for them to be set by plugins on `data` instead of being\n      // passed by users.\n      extensions: self.data('micromarkExtensions') || [],\n      mdastExtensions: self.data('fromMarkdownExtensions') || []\n    })\n  }\n}\n"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAIA,QAAI,IAAI;AACR,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AAgBZ,WAAO,UAAU,SAAU,KAAK,SAAS;AACvC,gBAAU,WAAW,CAAC;AACtB,UAAI,OAAO,OAAO;AAClB,UAAI,SAAS,YAAY,IAAI,SAAS,GAAG;AACvC,eAAOA,OAAM,GAAG;AAAA,MAClB,WAAW,SAAS,YAAY,SAAS,GAAG,GAAG;AAC7C,eAAO,QAAQ,OAAO,QAAQ,GAAG,IAAI,SAAS,GAAG;AAAA,MACnD;AACA,YAAM,IAAI;AAAA,QACR,0DACE,KAAK,UAAU,GAAG;AAAA,MACtB;AAAA,IACF;AAUA,aAASA,OAAM,KAAK;AAClB,YAAM,OAAO,GAAG;AAChB,UAAI,IAAI,SAAS,KAAK;AACpB;AAAA,MACF;AACA,UAAI,QAAQ,mIAAmI;AAAA,QAC7I;AAAA,MACF;AACA,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,IAAI,WAAW,MAAM,CAAC,CAAC;AAC3B,UAAI,QAAQ,MAAM,CAAC,KAAK,MAAM,YAAY;AAC1C,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAUA,aAAS,SAAS,IAAI;AACpB,UAAI,QAAQ,KAAK,IAAI,EAAE;AACvB,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,aAAO,KAAK;AAAA,IACd;AAUA,aAAS,QAAQ,IAAI;AACnB,UAAI,QAAQ,KAAK,IAAI,EAAE;AACvB,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,KAAK;AAAA,MACnC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,MAAM;AAAA,MACpC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,QAAQ;AAAA,MACtC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,QAAQ;AAAA,MACtC;AACA,aAAO,KAAK;AAAA,IACd;AAMA,aAAS,OAAO,IAAI,OAAO,GAAG,MAAM;AAClC,UAAI,WAAW,SAAS,IAAI;AAC5B,aAAO,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,QAAQ,WAAW,MAAM;AAAA,IAC7D;AAAA;AAAA;;;ACjKA;AAAA;AAMA,aAAS,MAAM,KAAK;AACnB,MAAAC,aAAY,QAAQA;AACpB,MAAAA,aAAY,UAAUA;AACtB,MAAAA,aAAY,SAAS;AACrB,MAAAA,aAAY,UAAUC;AACtB,MAAAD,aAAY,SAAS;AACrB,MAAAA,aAAY,UAAU;AACtB,MAAAA,aAAY,WAAW;AACvB,MAAAA,aAAY,UAAU;AAEtB,aAAO,KAAK,GAAG,EAAE,QAAQ,SAAO;AAC/B,QAAAA,aAAY,GAAG,IAAI,IAAI,GAAG;AAAA,MAC3B,CAAC;AAMD,MAAAA,aAAY,QAAQ,CAAC;AACrB,MAAAA,aAAY,QAAQ,CAAC;AAOrB,MAAAA,aAAY,aAAa,CAAC;AAQ1B,eAAS,YAAY,WAAW;AAC/B,YAAI,OAAO;AAEX,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,kBAAS,QAAQ,KAAK,OAAQ,UAAU,WAAW,CAAC;AACpD,kBAAQ;AAAA,QACT;AAEA,eAAOA,aAAY,OAAO,KAAK,IAAI,IAAI,IAAIA,aAAY,OAAO,MAAM;AAAA,MACrE;AACA,MAAAA,aAAY,cAAc;AAS1B,eAASA,aAAY,WAAW;AAC/B,YAAI;AACJ,YAAI,iBAAiB;AACrB,YAAI;AACJ,YAAI;AAEJ,iBAASE,UAAS,MAAM;AAEvB,cAAI,CAACA,OAAM,SAAS;AACnB;AAAA,UACD;AAEA,gBAAM,OAAOA;AAGb,gBAAM,OAAO,OAAO,oBAAI,KAAK,CAAC;AAC9B,gBAAM,KAAK,QAAQ,YAAY;AAC/B,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,qBAAW;AAEX,eAAK,CAAC,IAAIF,aAAY,OAAO,KAAK,CAAC,CAAC;AAEpC,cAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAEhC,iBAAK,QAAQ,IAAI;AAAA,UAClB;AAGA,cAAIG,SAAQ;AACZ,eAAK,CAAC,IAAI,KAAK,CAAC,EAAE,QAAQ,iBAAiB,CAAC,OAAO,WAAW;AAE7D,gBAAI,UAAU,MAAM;AACnB,qBAAO;AAAA,YACR;AACA,YAAAA;AACA,kBAAM,YAAYH,aAAY,WAAW,MAAM;AAC/C,gBAAI,OAAO,cAAc,YAAY;AACpC,oBAAM,MAAM,KAAKG,MAAK;AACtB,sBAAQ,UAAU,KAAK,MAAM,GAAG;AAGhC,mBAAK,OAAOA,QAAO,CAAC;AACpB,cAAAA;AAAA,YACD;AACA,mBAAO;AAAA,UACR,CAAC;AAGD,UAAAH,aAAY,WAAW,KAAK,MAAM,IAAI;AAEtC,gBAAM,QAAQ,KAAK,OAAOA,aAAY;AACtC,gBAAM,MAAM,MAAM,IAAI;AAAA,QACvB;AAEA,QAAAE,OAAM,YAAY;AAClB,QAAAA,OAAM,YAAYF,aAAY,UAAU;AACxC,QAAAE,OAAM,QAAQF,aAAY,YAAY,SAAS;AAC/C,QAAAE,OAAM,SAAS;AACf,QAAAA,OAAM,UAAUF,aAAY;AAE5B,eAAO,eAAeE,QAAO,WAAW;AAAA,UACvC,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,KAAK,MAAM;AACV,gBAAI,mBAAmB,MAAM;AAC5B,qBAAO;AAAA,YACR;AACA,gBAAI,oBAAoBF,aAAY,YAAY;AAC/C,gCAAkBA,aAAY;AAC9B,6BAAeA,aAAY,QAAQ,SAAS;AAAA,YAC7C;AAEA,mBAAO;AAAA,UACR;AAAA,UACA,KAAK,OAAK;AACT,6BAAiB;AAAA,UAClB;AAAA,QACD,CAAC;AAGD,YAAI,OAAOA,aAAY,SAAS,YAAY;AAC3C,UAAAA,aAAY,KAAKE,MAAK;AAAA,QACvB;AAEA,eAAOA;AAAA,MACR;AAEA,eAAS,OAAO,WAAW,WAAW;AACrC,cAAM,WAAWF,aAAY,KAAK,aAAa,OAAO,cAAc,cAAc,MAAM,aAAa,SAAS;AAC9G,iBAAS,MAAM,KAAK;AACpB,eAAO;AAAA,MACR;AASA,eAAS,OAAO,YAAY;AAC3B,QAAAA,aAAY,KAAK,UAAU;AAC3B,QAAAA,aAAY,aAAa;AAEzB,QAAAA,aAAY,QAAQ,CAAC;AACrB,QAAAA,aAAY,QAAQ,CAAC;AAErB,cAAM,SAAS,OAAO,eAAe,WAAW,aAAa,IAC3D,KAAK,EACL,QAAQ,KAAK,GAAG,EAChB,MAAM,GAAG,EACT,OAAO,OAAO;AAEhB,mBAAW,MAAM,OAAO;AACvB,cAAI,GAAG,CAAC,MAAM,KAAK;AAClB,YAAAA,aAAY,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC;AAAA,UACnC,OAAO;AACN,YAAAA,aAAY,MAAM,KAAK,EAAE;AAAA,UAC1B;AAAA,QACD;AAAA,MACD;AAUA,eAAS,gBAAgBI,SAAQ,UAAU;AAC1C,YAAI,cAAc;AAClB,YAAI,gBAAgB;AACpB,YAAI,YAAY;AAChB,YAAI,aAAa;AAEjB,eAAO,cAAcA,QAAO,QAAQ;AACnC,cAAI,gBAAgB,SAAS,WAAW,SAAS,aAAa,MAAMA,QAAO,WAAW,KAAK,SAAS,aAAa,MAAM,MAAM;AAE5H,gBAAI,SAAS,aAAa,MAAM,KAAK;AACpC,0BAAY;AACZ,2BAAa;AACb;AAAA,YACD,OAAO;AACN;AACA;AAAA,YACD;AAAA,UACD,WAAW,cAAc,IAAI;AAE5B,4BAAgB,YAAY;AAC5B;AACA,0BAAc;AAAA,UACf,OAAO;AACN,mBAAO;AAAA,UACR;AAAA,QACD;AAGA,eAAO,gBAAgB,SAAS,UAAU,SAAS,aAAa,MAAM,KAAK;AAC1E;AAAA,QACD;AAEA,eAAO,kBAAkB,SAAS;AAAA,MACnC;AAQA,eAASH,WAAU;AAClB,cAAM,aAAa;AAAA,UAClB,GAAGD,aAAY;AAAA,UACf,GAAGA,aAAY,MAAM,IAAI,eAAa,MAAM,SAAS;AAAA,QACtD,EAAE,KAAK,GAAG;AACV,QAAAA,aAAY,OAAO,EAAE;AACrB,eAAO;AAAA,MACR;AASA,eAAS,QAAQ,MAAM;AACtB,mBAAW,QAAQA,aAAY,OAAO;AACrC,cAAI,gBAAgB,MAAM,IAAI,GAAG;AAChC,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,mBAAW,MAAMA,aAAY,OAAO;AACnC,cAAI,gBAAgB,MAAM,EAAE,GAAG;AAC9B,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AASA,eAAS,OAAO,KAAK;AACpB,YAAI,eAAe,OAAO;AACzB,iBAAO,IAAI,SAAS,IAAI;AAAA,QACzB;AACA,eAAO;AAAA,MACR;AAMA,eAAS,UAAU;AAClB,gBAAQ,KAAK,uIAAuI;AAAA,MACrJ;AAEA,MAAAA,aAAY,OAAOA,aAAY,KAAK,CAAC;AAErC,aAAOA;AAAA,IACR;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnSjB;AAAA;AAMA,YAAQ,aAAa;AACrB,YAAQ,OAAO;AACf,YAAQ,OAAO;AACf,YAAQ,YAAY;AACpB,YAAQ,UAAU,aAAa;AAC/B,YAAQ,UAAW,uBAAM;AACxB,UAAI,SAAS;AAEb,aAAO,MAAM;AACZ,YAAI,CAAC,QAAQ;AACZ,mBAAS;AACT,kBAAQ,KAAK,uIAAuI;AAAA,QACrJ;AAAA,MACD;AAAA,IACD,GAAG;AAMH,YAAQ,SAAS;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAWA,aAAS,YAAY;AAIpB,UAAI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,SAAS,cAAc,OAAO,QAAQ,SAAS;AACrH,eAAO;AAAA,MACR;AAGA,UAAI,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,uBAAuB,GAAG;AAChI,eAAO;AAAA,MACR;AAEA,UAAI;AAKJ,aAAQ,OAAO,aAAa,eAAe,SAAS,mBAAmB,SAAS,gBAAgB,SAAS,SAAS,gBAAgB,MAAM;AAAA,MAEtI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,WAAY,OAAO,QAAQ,aAAa,OAAO,QAAQ;AAAA;AAAA,MAG1H,OAAO,cAAc,eAAe,UAAU,cAAc,IAAI,UAAU,UAAU,YAAY,EAAE,MAAM,gBAAgB,MAAM,SAAS,EAAE,CAAC,GAAG,EAAE,KAAK;AAAA,MAEpJ,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,oBAAoB;AAAA,IAC1H;AAQA,aAAS,WAAW,MAAM;AACzB,WAAK,CAAC,KAAK,KAAK,YAAY,OAAO,MAClC,KAAK,aACJ,KAAK,YAAY,QAAQ,OAC1B,KAAK,CAAC,KACL,KAAK,YAAY,QAAQ,OAC1B,MAAM,OAAO,QAAQ,SAAS,KAAK,IAAI;AAExC,UAAI,CAAC,KAAK,WAAW;AACpB;AAAA,MACD;AAEA,YAAM,IAAI,YAAY,KAAK;AAC3B,WAAK,OAAO,GAAG,GAAG,GAAG,gBAAgB;AAKrC,UAAIK,SAAQ;AACZ,UAAI,QAAQ;AACZ,WAAK,CAAC,EAAE,QAAQ,eAAe,WAAS;AACvC,YAAI,UAAU,MAAM;AACnB;AAAA,QACD;AACA,QAAAA;AACA,YAAI,UAAU,MAAM;AAGnB,kBAAQA;AAAA,QACT;AAAA,MACD,CAAC;AAED,WAAK,OAAO,OAAO,GAAG,CAAC;AAAA,IACxB;AAUA,YAAQ,MAAM,QAAQ,SAAS,QAAQ,QAAQ,MAAM;AAAA,IAAC;AAQtD,aAAS,KAAK,YAAY;AACzB,UAAI;AACH,YAAI,YAAY;AACf,kBAAQ,QAAQ,QAAQ,SAAS,UAAU;AAAA,QAC5C,OAAO;AACN,kBAAQ,QAAQ,WAAW,OAAO;AAAA,QACnC;AAAA,MACD,SAAS,OAAO;AAAA,MAGhB;AAAA,IACD;AAQA,aAAS,OAAO;AACf,UAAI;AACJ,UAAI;AACH,YAAI,QAAQ,QAAQ,QAAQ,OAAO;AAAA,MACpC,SAAS,OAAO;AAAA,MAGhB;AAGA,UAAI,CAAC,KAAK,OAAO,YAAY,eAAe,SAAS,SAAS;AAC7D,YAAI,QAAQ,IAAI;AAAA,MACjB;AAEA,aAAO;AAAA,IACR;AAaA,aAAS,eAAe;AACvB,UAAI;AAGH,eAAO;AAAA,MACR,SAAS,OAAO;AAAA,MAGhB;AAAA,IACD;AAEA,WAAO,UAAU,iBAAoB,OAAO;AAE5C,QAAM,EAAC,WAAU,IAAI,OAAO;AAM5B,eAAW,IAAI,SAAU,GAAG;AAC3B,UAAI;AACH,eAAO,KAAK,UAAU,CAAC;AAAA,MACxB,SAAS,OAAO;AACf,eAAO,iCAAiC,MAAM;AAAA,MAC/C;AAAA,IACD;AAAA;AAAA;;;ACxQA,IAAM,iBAAN,cAA6B,MAAM;AAAA,EACjC;AAAA;AAAA,IAA6B;AAAA;AAAA,EAC7B;AAAA;AAAA,IAA6B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmB7B,YAAY,SAAS,QAAQ,UAAU,UAAU,WAAW;AAC1D,UAAM,OAAO;AAEb,QAAI,MAAM,mBAAmB;AAC3B,YAAM,kBAAkB,MAAM,KAAK,WAAW;AAAA,IAChD;AAKA,SAAK,SAAS;AAKd,SAAK,WAAW;AAKhB,SAAK,YAAY;AAKjB,SAAK,WAAW;AAAA,EAClB;AACF;AA6HO,SAAS,GAAG,OAAO,SAAS;AACjC;AAAA,IACE,QAAQ,KAAK;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAoCA,SAAS,OAAO,MAAM,QAAQ,UAAU,UAAU,gBAAgB,aAAa;AAC7E,MAAI,CAAC,MAAM;AACT,UAAM,uBAAuB,QACzB,cACA,IAAI;AAAA,MACF,eAAe;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC;AAAA,IACH;AAAA,EACN;AACF;;;ACjOA,IAAM,eAAe,CAAC;AAef,SAAS,SAAS,OAAO,SAAS;AACvC,QAAM,WAAW,WAAW;AAC5B,QAAM,kBACJ,OAAO,SAAS,oBAAoB,YAChC,SAAS,kBACT;AACN,QAAM,cACJ,OAAO,SAAS,gBAAgB,YAAY,SAAS,cAAc;AAErE,SAAO,IAAI,OAAO,iBAAiB,WAAW;AAChD;AAcA,SAAS,IAAI,OAAO,iBAAiB,aAAa;AAChD,MAAI,KAAK,KAAK,GAAG;AACf,QAAI,WAAW,OAAO;AACpB,aAAO,MAAM,SAAS,UAAU,CAAC,cAAc,KAAK,MAAM;AAAA,IAC5D;AAEA,QAAI,mBAAmB,SAAS,SAAS,MAAM,KAAK;AAClD,aAAO,MAAM;AAAA,IACf;AAEA,QAAI,cAAc,OAAO;AACvB,aAAO,IAAI,MAAM,UAAU,iBAAiB,WAAW;AAAA,IACzD;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,IAAI,OAAO,iBAAiB,WAAW;AAAA,EAChD;AAEA,SAAO;AACT;AAcA,SAAS,IAAIC,SAAQ,iBAAiB,aAAa;AAEjD,QAAM,SAAS,CAAC;AAChB,MAAIC,SAAQ;AAEZ,SAAO,EAAEA,SAAQD,QAAO,QAAQ;AAC9B,WAAOC,MAAK,IAAI,IAAID,QAAOC,MAAK,GAAG,iBAAiB,WAAW;AAAA,EACjE;AAEA,SAAO,OAAO,KAAK,EAAE;AACvB;AAUA,SAAS,KAAK,OAAO;AACnB,SAAO,QAAQ,SAAS,OAAO,UAAU,QAAQ;AACnD;;;ACzFO,IAAM;AAAA;AAAA,EAA8B;AAAA,IACzC,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,wBAAwB;AAAA,IACxB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,iBAAiB;AAAA;AAAA,IACjB,eAAe;AAAA;AAAA,IACf,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,WAAW;AAAA;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,iBAAiB;AAAA;AAAA,IACjB,kBAAkB;AAAA;AAAA,IAClB,UAAU;AAAA;AAAA,IACV,UAAU;AAAA;AAAA,IACV,OAAO;AAAA;AAAA,IACP,MAAM;AAAA;AAAA,IACN,KAAK;AAAA;AAAA,IACL,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,OAAO;AAAA;AAAA,IACP,WAAW;AAAA;AAAA,IACX,UAAU;AAAA;AAAA,IACV,UAAU;AAAA;AAAA,IACV,aAAa;AAAA;AAAA,IACb,cAAc;AAAA;AAAA,IACd,QAAQ;AAAA;AAAA,IACR,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,mBAAmB;AAAA;AAAA,IACnB,WAAW;AAAA;AAAA,IACX,oBAAoB;AAAA;AAAA,IACpB,OAAO;AAAA;AAAA,IACP,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,YAAY;AAAA;AAAA,IACZ,gBAAgB;AAAA;AAAA,IAChB,aAAa;AAAA;AAAA,IACb,iBAAiB;AAAA;AAAA,IACjB,OAAO;AAAA;AAAA,IACP,KAAK;AAAA;AAAA,IAEL,iBAAiB;AAAA;AAAA,IAEjB,sBAAsB;AAAA;AAAA,EACxB;AAAA;;;ACrJO,IAAM;AAAA;AAAA,EAAkC;AAAA,IAC7C,oBAAoB;AAAA;AAAA,IACpB,qBAAqB;AAAA;AAAA,IACrB,+BAA+B;AAAA;AAAA,IAC/B,uBAAuB;AAAA;AAAA,IACvB,uBAAuB;AAAA;AAAA,IACvB,oBAAoB;AAAA;AAAA,IACpB,2BAA2B;AAAA;AAAA,IAC3B,0BAA0B;AAAA;AAAA,IAC1B,kCAAkC;AAAA;AAAA,IAClC,sCAAsC;AAAA;AAAA,IACtC,gCAAgC;AAAA;AAAA,IAChC,2BAA2B;AAAA;AAAA,IAC3B,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA;AAAA,IACxB,WAAW;AAAA;AAAA,IACX,WAAW;AAAA;AAAA,IACX,aAAa;AAAA;AAAA,IACb,cAAc;AAAA;AAAA,IACd,iBAAiB;AAAA;AAAA,IACjB,iBAAiB;AAAA;AAAA,IACjB,gBAAgB;AAAA;AAAA,IAChB,SAAS;AAAA;AAAA,IACT,mCAAmC;AAAA;AAAA,IACnC,sBAAsB;AAAA;AAAA,IACtB,sBAAsB;AAAA;AAAA,IACtB,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,SAAS;AAAA;AAAA,IACT,6BAA6B;AAAA;AAAA,IAC7B,oBAAoB;AAAA;AAAA,EACtB;AAAA;;;AC9BO,IAAM;AAAA;AAAA,EAA8B;AAAA;AAAA,IAEzC,MAAM;AAAA;AAAA;AAAA,IAIN,YAAY;AAAA;AAAA;AAAA,IAIZ,YAAY;AAAA;AAAA,IAGZ,iBAAiB;AAAA;AAAA;AAAA,IAIjB,YAAY;AAAA;AAAA;AAAA,IAIZ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWZ,YAAY;AAAA;AAAA,IAGZ,oBAAoB;AAAA;AAAA;AAAA,IAIpB,gBAAgB;AAAA;AAAA;AAAA,IAIhB,UAAU;AAAA;AAAA,IAGV,eAAe;AAAA;AAAA,IAGf,gBAAgB;AAAA;AAAA,IAGhB,kBAAkB;AAAA;AAAA;AAAA,IAIlB,iBAAiB;AAAA;AAAA,IAGjB,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,IAMtB,oBAAoB;AAAA;AAAA,IAGpB,0BAA0B;AAAA;AAAA,IAG1B,iCAAiC;AAAA;AAAA,IAGjC,qCAAqC;AAAA;AAAA,IAGrC,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASzB,YAAY;AAAA;AAAA;AAAA,IAIZ,iBAAiB;AAAA;AAAA,IAGjB,yBAAyB;AAAA;AAAA;AAAA,IAIzB,qBAAqB;AAAA;AAAA;AAAA,IAIrB,qBAAqB;AAAA;AAAA,IAGrB,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASf,cAAc;AAAA;AAAA;AAAA;AAAA,IAKd,UAAU;AAAA,IAEV,cAAc;AAAA;AAAA,IAGd,iBAAiB;AAAA;AAAA,IAGjB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYlB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAST,YAAY;AAAA;AAAA;AAAA;AAAA,IAKZ,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,IAMvB,8BAA8B;AAAA;AAAA,IAG9B,oCAAoC;AAAA;AAAA;AAAA;AAAA,IAKpC,0BAA0B;AAAA;AAAA;AAAA,IAI1B,6BAA6B;AAAA;AAAA;AAAA,IAI7B,iBAAiB;AAAA;AAAA,IAGjB,uBAAuB;AAAA;AAAA;AAAA,IAIvB,uBAAuB;AAAA;AAAA,IAGvB,kBAAkB;AAAA;AAAA;AAAA,IAIlB,iBAAiB;AAAA;AAAA,IAGjB,uBAAuB;AAAA;AAAA;AAAA,IAIvB,uBAAuB;AAAA;AAAA;AAAA,IAIvB,UAAU;AAAA;AAAA,IAGV,kBAAkB;AAAA;AAAA;AAAA,IAIlB,cAAc;AAAA;AAAA,IAGd,cAAc;AAAA;AAAA;AAAA,IAId,iBAAiB;AAAA;AAAA;AAAA,IAIjB,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASnB,UAAU;AAAA,IAEV,cAAc;AAAA;AAAA;AAAA,IAId,UAAU;AAAA,IAEV,cAAc;AAAA;AAAA;AAAA;AAAA,IAKd,OAAO;AAAA;AAAA;AAAA,IAIP,OAAO;AAAA;AAAA;AAAA,IAIP,WAAW;AAAA;AAAA;AAAA,IAIX,WAAW;AAAA;AAAA;AAAA,IAIX,YAAY;AAAA;AAAA,IAGZ,aAAa;AAAA;AAAA,IAGb,kBAAkB;AAAA;AAAA;AAAA,IAIlB,UAAU;AAAA;AAAA;AAAA,IAIV,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUN,WAAW;AAAA;AAAA;AAAA,IAIX,WAAW;AAAA;AAAA,IAGX,iBAAiB;AAAA;AAAA;AAAA,IAIjB,iBAAiB;AAAA;AAAA;AAAA;AAAA,IAKjB,UAAU;AAAA;AAAA;AAAA,IAIV,qBAAqB;AAAA;AAAA;AAAA;AAAA,IAKrB,4BAA4B;AAAA;AAAA,IAG5B,kCAAkC;AAAA;AAAA;AAAA,IAIlC,wBAAwB;AAAA;AAAA;AAAA,IAIxB,2BAA2B;AAAA;AAAA,IAG3B,gBAAgB;AAAA;AAAA;AAAA,IAIhB,eAAe;AAAA;AAAA,IAGf,qBAAqB;AAAA;AAAA;AAAA,IAIrB,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYrB,eAAe;AAAA;AAAA;AAAA,IAIf,mBAAmB;AAAA;AAAA;AAAA,IAInB,mBAAmB;AAAA;AAAA,IAGnB,2BAA2B;AAAA;AAAA;AAAA,IAI3B,QAAQ;AAAA;AAAA,IAGR,gBAAgB;AAAA;AAAA;AAAA,IAIhB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASZ,eAAe;AAAA;AAAA,IAGf,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWvB,YAAY;AAAA;AAAA,IAEZ,kBAAkB;AAAA;AAAA,IAElB,kBAAkB;AAAA;AAAA,IAElB,4BAA4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAW5B,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWb,eAAe;AAAA;AAAA,IAGf,gBAAgB;AAAA;AAAA,IAGhB,gBAAgB;AAAA;AAAA;AAAA;AAAA,IAKhB,gBAAgB;AAAA;AAAA,IAGhB,0BAA0B;AAAA;AAAA,IAG1B,eAAe;AAAA;AAAA,IAGf,eAAe;AAAA,IACf,cAAc;AAAA,IACd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,EACf;AAAA;;;AC5bO,IAAM;AAAA;AAAA,EAA+B;AAAA,IAC1C,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA;;;ACpFO,SAAS,OAAOC,OAAM,OAAO,QAAQ,OAAO;AACjD,QAAM,MAAMA,MAAK;AACjB,MAAI,aAAa;AAEjB,MAAI;AAGJ,MAAI,QAAQ,GAAG;AACb,YAAQ,CAAC,QAAQ,MAAM,IAAI,MAAM;AAAA,EACnC,OAAO;AACL,YAAQ,QAAQ,MAAM,MAAM;AAAA,EAC9B;AAEA,WAAS,SAAS,IAAI,SAAS;AAG/B,MAAI,MAAM,SAAS,UAAU,oBAAoB;AAC/C,iBAAa,MAAM,KAAK,KAAK;AAC7B,eAAW,QAAQ,OAAO,MAAM;AAEhC,IAAAA,MAAK,OAAO,GAAG,UAAU;AAAA,EAC3B,OAAO;AAEL,QAAI,OAAQ,CAAAA,MAAK,OAAO,OAAO,MAAM;AAGrC,WAAO,aAAa,MAAM,QAAQ;AAChC,mBAAa,MAAM;AAAA,QACjB;AAAA,QACA,aAAa,UAAU;AAAA,MACzB;AACA,iBAAW,QAAQ,OAAO,CAAC;AAE3B,MAAAA,MAAK,OAAO,GAAG,UAAU;AAEzB,oBAAc,UAAU;AACxB,eAAS,UAAU;AAAA,IACrB;AAAA,EACF;AACF;AAkBO,SAAS,KAAKA,OAAM,OAAO;AAChC,MAAIA,MAAK,SAAS,GAAG;AACnB,WAAOA,OAAMA,MAAK,QAAQ,GAAG,KAAK;AAClC,WAAOA;AAAA,EACT;AAEA,SAAO;AACT;;;AC7EA,IAAM,iBAAiB,CAAC,EAAE;AAUnB,SAAS,kBAAkB,YAAY;AAE5C,QAAMC,OAAM,CAAC;AACb,MAAIC,SAAQ;AAEZ,SAAO,EAAEA,SAAQ,WAAW,QAAQ;AAClC,oBAAgBD,MAAK,WAAWC,MAAK,CAAC;AAAA,EACxC;AAEA,SAAOD;AACT;AAYA,SAAS,gBAAgBA,MAAKE,YAAW;AAEvC,MAAI;AAEJ,OAAK,QAAQA,YAAW;AACtB,UAAM,QAAQ,eAAe,KAAKF,MAAK,IAAI,IAAIA,KAAI,IAAI,IAAI;AAE3D,UAAM,OAAO,UAAUA,KAAI,IAAI,IAAI,CAAC;AAEpC,UAAM,QAAQE,WAAU,IAAI;AAE5B,QAAI;AAEJ,QAAI,OAAO;AACT,WAAK,QAAQ,OAAO;AAClB,YAAI,CAAC,eAAe,KAAK,MAAM,IAAI,EAAG,MAAK,IAAI,IAAI,CAAC;AACpD,cAAM,QAAQ,MAAM,IAAI;AACxB;AAAA;AAAA,UAEE,KAAK,IAAI;AAAA,UACT,MAAM,QAAQ,KAAK,IAAI,QAAQ,QAAQ,CAAC,KAAK,IAAI,CAAC;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAaA,SAAS,WAAW,UAAUC,OAAM;AAClC,MAAIF,SAAQ;AAEZ,QAAM,SAAS,CAAC;AAEhB,SAAO,EAAEA,SAAQE,MAAK,QAAQ;AAE5B;AAAC,KAACA,MAAKF,MAAK,EAAE,QAAQ,UAAU,WAAW,QAAQ,KAAKE,MAAKF,MAAK,CAAC;AAAA,EACrE;AAEA,SAAO,UAAU,GAAG,GAAG,MAAM;AAC/B;;;AC5EO,SAAS,gCAAgC,OAAO,MAAM;AAC3D,QAAM,OAAO,OAAO,SAAS,OAAO,IAAI;AAExC;AAAA;AAAA,IAEE,OAAO,MAAM,MACb,SAAS,MAAM,MACd,OAAO,MAAM,MAAM,OAAO,MAAM;AAAA,IAEhC,OAAO,MAAM,SAAS,OAAO;AAAA,IAE7B,OAAO,SAAU,OAAO;AAAA,IAExB,OAAO,SAAU,OAAO;AAAA,KAExB,OAAO,WAAY,UACnB,OAAO,WAAY;AAAA;AAAA,IAGpB,OAAO;AAAA,IACP;AACA,WAAO,OAAO;AAAA,EAChB;AAEA,SAAO,OAAO,cAAc,IAAI;AAClC;;;ACpBO,SAAS,oBAAoB,OAAO;AACzC,SACE,MAEG,QAAQ,eAAe,OAAO,KAAK,EAEnC,QAAQ,UAAU,EAAE,EAOpB,YAAY,EACZ,YAAY;AAEnB;;;ACdO,IAAM,aAAa,WAAW,UAAU;AAcxC,IAAM,oBAAoB,WAAW,YAAY;AAuBjD,IAAM,aAAa,WAAW,qBAAqB;AAanD,SAAS,aAAa,MAAM;AACjC;AAAA;AAAA;AAAA,IAGE,SAAS,SAAS,OAAO,MAAM,SAAS,SAAS,MAAM;AAAA;AAE3D;AAaO,IAAM,aAAa,WAAW,IAAI;AAoBlC,IAAM,gBAAgB,WAAW,YAAY;AAe7C,IAAM,mBAAmB,WAAW,gBAAgB;AAiBpD,SAAS,mBAAmB,MAAM;AACvC,SAAO,SAAS,QAAQ,OAAO,MAAM;AACvC;AAWO,SAAS,0BAA0B,MAAM;AAC9C,SAAO,SAAS,SAAS,OAAO,MAAM,OAAO,SAAS,MAAM;AAC9D;AAiBO,SAAS,cAAc,MAAM;AAClC,SACE,SAAS,MAAM,iBACf,SAAS,MAAM,gBACf,SAAS,MAAM;AAEnB;AAuBO,IAAM,qBAAqB,WAAW,cAAc;AAsBpD,IAAM,oBAAoB,WAAW,IAAI;AAUhD,SAAS,WAAW,OAAO;AACzB,SAAO;AAUP,WAAS,MAAM,MAAM;AACnB,WAAO,SAAS,QAAQ,OAAO,MAAM,MAAM,KAAK,OAAO,aAAa,IAAI,CAAC;AAAA,EAC3E;AACF;;;ACzMA,IAAMG,kBAAiB,CAAC,EAAE;;;ACVnB,SAAS,aAAa,SAASC,KAAI,MAAM,KAAK;AACnD,QAAM,QAAQ,MAAM,MAAM,IAAI,OAAO;AACrC,MAAI,OAAO;AAEX,SAAO;AAGP,WAAS,MAAM,MAAM;AACnB,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,MAAM,IAAI;AAClB,aAAO,OAAO,IAAI;AAAA,IACpB;AAEA,WAAOA,IAAG,IAAI;AAAA,EAChB;AAGA,WAAS,OAAO,MAAM;AACpB,QAAI,cAAc,IAAI,KAAK,SAAS,OAAO;AACzC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,IAAI;AACjB,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;;;AClDO,IAAM,UAAU,EAAC,UAAU,kBAAiB;AAQnD,SAAS,kBAAkB,SAAS;AAClC,QAAM,eAAe,QAAQ;AAAA,IAC3B,KAAK,OAAO,WAAW;AAAA,IACvB;AAAA,IACA;AAAA,EACF;AAEA,MAAIC;AAEJ,SAAO;AAGP,WAAS,2BAA2B,MAAM;AACxC;AAAA,MACE,SAAS,MAAM,OAAO,mBAAmB,IAAI;AAAA,MAC7C;AAAA,IACF;AAEA,QAAI,SAAS,MAAM,KAAK;AACtB,cAAQ,QAAQ,IAAI;AACpB;AAAA,IACF;AAEA,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO,aAAa,SAAS,cAAc,MAAM,UAAU;AAAA,EAC7D;AAGA,WAAS,iBAAiB,MAAM;AAC9B;AAAA,MACE,SAAS,MAAM,OAAO,CAAC,mBAAmB,IAAI;AAAA,MAC9C;AAAA,IACF;AACA,YAAQ,MAAM,MAAM,SAAS;AAC7B,WAAO,UAAU,IAAI;AAAA,EACvB;AAGA,WAAS,UAAU,MAAM;AACvB,UAAM,QAAQ,QAAQ,MAAM,MAAM,WAAW;AAAA,MAC3C,aAAa,UAAU;AAAA,MACvB,UAAAA;AAAA,IACF,CAAC;AAED,QAAIA,WAAU;AACZ,MAAAA,UAAS,OAAO;AAAA,IAClB;AAEA,IAAAA,YAAW;AAEX,WAAO,KAAK,IAAI;AAAA,EAClB;AAGA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,KAAK;AACtB,cAAQ,KAAK,MAAM,SAAS;AAC5B,cAAQ,KAAK,MAAM,SAAS;AAC5B,cAAQ,QAAQ,IAAI;AACpB;AAAA,IACF;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,SAAS;AAC5B,aAAO;AAAA,IACT;AAGA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AACF;;;ACxEO,IAAMC,YAAW,EAAC,UAAU,mBAAkB;AAGrD,IAAM,qBAAqB,EAAC,UAAU,kBAAiB;AAQvD,SAAS,mBAAmB,SAAS;AACnC,QAAM,OAAO;AAEb,QAAM,QAAQ,CAAC;AACf,MAAI,YAAY;AAEhB,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,SAAO;AAGP,WAAS,MAAM,MAAM;AAWnB,QAAI,YAAY,MAAM,QAAQ;AAC5B,YAAM,OAAO,MAAM,SAAS;AAC5B,WAAK,iBAAiB,KAAK,CAAC;AAC5B;AAAA,QACE,KAAK,CAAC,EAAE;AAAA,QACR;AAAA,MACF;AACA,aAAO,QAAQ;AAAA,QACb,KAAK,CAAC,EAAE;AAAA,QACR;AAAA,QACA;AAAA,MACF,EAAE,IAAI;AAAA,IACR;AAGA,WAAO,mBAAmB,IAAI;AAAA,EAChC;AAGA,WAAS,iBAAiB,MAAM;AAC9B;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AAEA;AAKA,QAAI,KAAK,eAAe,YAAY;AAClC,WAAK,eAAe,aAAa;AAEjC,UAAI,WAAW;AACb,kBAAU;AAAA,MACZ;AAIA,YAAM,mBAAmB,KAAK,OAAO;AACrC,UAAI,kBAAkB;AAEtB,UAAIC;AAGJ,aAAO,mBAAmB;AACxB,YACE,KAAK,OAAO,eAAe,EAAE,CAAC,MAAM,UACpC,KAAK,OAAO,eAAe,EAAE,CAAC,EAAE,SAAS,MAAM,WAC/C;AACA,UAAAA,SAAQ,KAAK,OAAO,eAAe,EAAE,CAAC,EAAE;AACxC;AAAA,QACF;AAAA,MACF;AAEA,SAAOA,QAAO,oCAAoC;AAElD,qBAAe,SAAS;AAGxB,UAAIC,SAAQ;AAEZ,aAAOA,SAAQ,KAAK,OAAO,QAAQ;AACjC,aAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAM,EAAC,GAAGD,OAAK;AACrC,QAAAC;AAAA,MACF;AAGA;AAAA,QACE,KAAK;AAAA,QACL,kBAAkB;AAAA,QAClB;AAAA,QACA,KAAK,OAAO,MAAM,gBAAgB;AAAA,MACpC;AAGA,WAAK,OAAO,SAASA;AAErB,aAAO,mBAAmB,IAAI;AAAA,IAChC;AAEA,WAAO,MAAM,IAAI;AAAA,EACnB;AAGA,WAAS,mBAAmB,MAAM;AAMhC,QAAI,cAAc,MAAM,QAAQ;AAI9B,UAAI,CAAC,WAAW;AACd,eAAO,kBAAkB,IAAI;AAAA,MAC/B;AAKA,UAAI,UAAU,oBAAoB,UAAU,iBAAiB,UAAU;AACrE,eAAO,UAAU,IAAI;AAAA,MACvB;AAOA,WAAK,YAAY;AAAA,QACf,UAAU,oBAAoB,CAAC,UAAU;AAAA,MAC3C;AAAA,IACF;AAGA,SAAK,iBAAiB,CAAC;AACvB,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,IAAI;AAAA,EACR;AAGA,WAAS,qBAAqB,MAAM;AAClC,QAAI,UAAW,WAAU;AACzB,mBAAe,SAAS;AACxB,WAAO,kBAAkB,IAAI;AAAA,EAC/B;AAGA,WAAS,sBAAsB,MAAM;AACnC,SAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,cAAc,MAAM;AACxD,sBAAkB,KAAK,IAAI,EAAE;AAC7B,WAAO,UAAU,IAAI;AAAA,EACvB;AAGA,WAAS,kBAAkB,MAAM;AAE/B,SAAK,iBAAiB,CAAC;AACvB,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,IAAI;AAAA,EACR;AAGA,WAAS,kBAAkB,MAAM;AAC/B;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AACA;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AACA;AACA,UAAM,KAAK,CAAC,KAAK,kBAAkB,KAAK,cAAc,CAAC;AAEvD,WAAO,kBAAkB,IAAI;AAAA,EAC/B;AAGA,WAAS,UAAU,MAAM;AACvB,QAAI,SAAS,MAAM,KAAK;AACtB,UAAI,UAAW,WAAU;AACzB,qBAAe,CAAC;AAChB,cAAQ,QAAQ,IAAI;AACpB;AAAA,IACF;AAEA,gBAAY,aAAa,KAAK,OAAO,KAAK,KAAK,IAAI,CAAC;AACpD,YAAQ,MAAM,MAAM,WAAW;AAAA,MAC7B,YAAY;AAAA,MACZ,aAAa,UAAU;AAAA,MACvB,UAAU;AAAA,IACZ,CAAC;AAED,WAAO,aAAa,IAAI;AAAA,EAC1B;AAGA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,KAAK;AACtB,mBAAa,QAAQ,KAAK,MAAM,SAAS,GAAG,IAAI;AAChD,qBAAe,CAAC;AAChB,cAAQ,QAAQ,IAAI;AACpB;AAAA,IACF;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,QAAQ,IAAI;AACpB,mBAAa,QAAQ,KAAK,MAAM,SAAS,CAAC;AAE1C,kBAAY;AACZ,WAAK,YAAY;AACjB,aAAO;AAAA,IACT;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAUA,WAAS,aAAa,OAAO,WAAW;AACtC,OAAO,WAAW,oDAAoD;AACtE,UAAM,SAAS,KAAK,YAAY,KAAK;AACrC,QAAI,UAAW,QAAO,KAAK,IAAI;AAC/B,UAAM,WAAW;AACjB,QAAI,WAAY,YAAW,OAAO;AAClC,iBAAa;AACb,cAAU,WAAW,MAAM,KAAK;AAChC,cAAU,MAAM,MAAM;AAmCtB,QAAI,KAAK,OAAO,KAAK,MAAM,MAAM,IAAI,GAAG;AACtC,UAAIA,SAAQ,UAAU,OAAO;AAE7B,aAAOA,UAAS;AACd;AAAA;AAAA,UAEE,UAAU,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAM,SAAS;AAAA,WAEzC,CAAC,UAAU,OAAOA,MAAK,EAAE,CAAC,EAAE;AAAA,UAE3B,UAAU,OAAOA,MAAK,EAAE,CAAC,EAAE,IAAI,SAAS;AAAA,UAC1C;AAGA;AAAA,QACF;AAAA,MACF;AAIA,YAAM,mBAAmB,KAAK,OAAO;AACrC,UAAI,kBAAkB;AAEtB,UAAI;AAEJ,UAAID;AAGJ,aAAO,mBAAmB;AACxB,YACE,KAAK,OAAO,eAAe,EAAE,CAAC,MAAM,UACpC,KAAK,OAAO,eAAe,EAAE,CAAC,EAAE,SAAS,MAAM,WAC/C;AACA,cAAI,MAAM;AACR,YAAAA,SAAQ,KAAK,OAAO,eAAe,EAAE,CAAC,EAAE;AACxC;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,SAAOA,QAAO,oCAAoC;AAElD,qBAAe,SAAS;AAGxB,MAAAC,SAAQ;AAER,aAAOA,SAAQ,KAAK,OAAO,QAAQ;AACjC,aAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAM,EAAC,GAAGD,OAAK;AACrC,QAAAC;AAAA,MACF;AAGA;AAAA,QACE,KAAK;AAAA,QACL,kBAAkB;AAAA,QAClB;AAAA,QACA,KAAK,OAAO,MAAM,gBAAgB;AAAA,MACpC;AAGA,WAAK,OAAO,SAASA;AAAA,IACvB;AAAA,EACF;AAQA,WAAS,eAAe,MAAM;AAC5B,QAAIA,SAAQ,MAAM;AAGlB,WAAOA,WAAU,MAAM;AACrB,YAAM,QAAQ,MAAMA,MAAK;AACzB,WAAK,iBAAiB,MAAM,CAAC;AAC7B;AAAA,QACE,MAAM,CAAC,EAAE;AAAA,QACT;AAAA,MACF;AACA,YAAM,CAAC,EAAE,KAAK,KAAK,MAAM,OAAO;AAAA,IAClC;AAEA,UAAM,SAAS;AAAA,EACjB;AAEA,WAAS,YAAY;AACnB;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AACA,OAAO,WAAW,oDAAoD;AACtE,cAAU,MAAM,CAAC,MAAM,GAAG,CAAC;AAC3B,iBAAa;AACb,gBAAY;AACZ,SAAK,eAAe,aAAa;AAAA,EACnC;AACF;AAQA,SAAS,kBAAkB,SAASC,KAAI,KAAK;AAE3C;AAAA,IACE,KAAK,OAAO,WAAW,QAAQ;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,QAAQ,QAAQ,KAAK,OAAO,WAAW,UAAUA,KAAI,GAAG;AAAA,IACxD,MAAM;AAAA,IACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,EAChB;AACF;;;ACnaO,SAAS,kBAAkB,MAAM;AACtC,MACE,SAAS,MAAM,OACf,0BAA0B,IAAI,KAC9B,kBAAkB,IAAI,GACtB;AACA,WAAO,UAAU;AAAA,EACnB;AAEA,MAAI,mBAAmB,IAAI,GAAG;AAC5B,WAAO,UAAU;AAAA,EACnB;AACF;;;ACrBO,SAAS,WAAWC,aAAY,QAAQ,SAAS;AAEtD,QAAM,SAAS,CAAC;AAChB,MAAIC,SAAQ;AAEZ,SAAO,EAAEA,SAAQD,YAAW,QAAQ;AAClC,UAAM,UAAUA,YAAWC,MAAK,EAAE;AAElC,QAAI,WAAW,CAAC,OAAO,SAAS,OAAO,GAAG;AACxC,eAAS,QAAQ,QAAQ,OAAO;AAChC,aAAO,KAAK,OAAO;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AACT;;;ACVO,IAAM,YAAY;AAAA,EACvB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,UAAU;AACZ;AAQA,SAAS,oBAAoB,QAAQ,SAAS;AAC5C,MAAIC,SAAQ;AAEZ,MAAI;AAEJ,MAAI;AAEJ,MAAIC;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAMJ,SAAO,EAAED,SAAQ,OAAO,QAAQ;AAE9B,QACE,OAAOA,MAAK,EAAE,CAAC,MAAM,WACrB,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,uBAC1B,OAAOA,MAAK,EAAE,CAAC,EAAE,QACjB;AACA,aAAOA;AAGP,aAAO,QAAQ;AAEb,YACE,OAAO,IAAI,EAAE,CAAC,MAAM,UACpB,OAAO,IAAI,EAAE,CAAC,EAAE,SAAS,uBACzB,OAAO,IAAI,EAAE,CAAC,EAAE;AAAA,QAEhB,QAAQ,eAAe,OAAO,IAAI,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,MAClD,QAAQ,eAAe,OAAOA,MAAK,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,GACvD;AAKA,eACG,OAAO,IAAI,EAAE,CAAC,EAAE,UAAU,OAAOA,MAAK,EAAE,CAAC,EAAE,WAC3C,OAAOA,MAAK,EAAE,CAAC,EAAE,IAAI,SAAS,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAM,UAAU,KAChE,GACG,OAAO,IAAI,EAAE,CAAC,EAAE,IAAI,SACnB,OAAO,IAAI,EAAE,CAAC,EAAE,MAAM,SACtB,OAAOA,MAAK,EAAE,CAAC,EAAE,IAAI,SACrB,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAM,UACzB,IAEF;AACA;AAAA,UACF;AAGA,gBACE,OAAO,IAAI,EAAE,CAAC,EAAE,IAAI,SAAS,OAAO,IAAI,EAAE,CAAC,EAAE,MAAM,SAAS,KAC5D,OAAOA,MAAK,EAAE,CAAC,EAAE,IAAI,SAAS,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAM,SAAS,IAC1D,IACA;AAEN,gBAAM,QAAQ,EAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,IAAG;AACrC,gBAAM,MAAM,EAAC,GAAG,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAK;AACtC,oBAAU,OAAO,CAAC,GAAG;AACrB,oBAAU,KAAK,GAAG;AAElB,4BAAkB;AAAA,YAChB,MAAM,MAAM,IAAI,MAAM,iBAAiB,MAAM;AAAA,YAC7C;AAAA,YACA,KAAK,EAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,IAAG;AAAA,UAC9B;AACA,4BAAkB;AAAA,YAChB,MAAM,MAAM,IAAI,MAAM,iBAAiB,MAAM;AAAA,YAC7C,OAAO,EAAC,GAAG,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAK;AAAA,YACjC;AAAA,UACF;AACA,UAAAC,QAAO;AAAA,YACL,MAAM,MAAM,IAAI,MAAM,aAAa,MAAM;AAAA,YACzC,OAAO,EAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,IAAG;AAAA,YAC9B,KAAK,EAAC,GAAG,OAAOD,MAAK,EAAE,CAAC,EAAE,MAAK;AAAA,UACjC;AACA,kBAAQ;AAAA,YACN,MAAM,MAAM,IAAI,MAAM,SAAS,MAAM;AAAA,YACrC,OAAO,EAAC,GAAG,gBAAgB,MAAK;AAAA,YAChC,KAAK,EAAC,GAAG,gBAAgB,IAAG;AAAA,UAC9B;AAEA,iBAAO,IAAI,EAAE,CAAC,EAAE,MAAM,EAAC,GAAG,gBAAgB,MAAK;AAC/C,iBAAOA,MAAK,EAAE,CAAC,EAAE,QAAQ,EAAC,GAAG,gBAAgB,IAAG;AAEhD,uBAAa,CAAC;AAGd,cAAI,OAAO,IAAI,EAAE,CAAC,EAAE,IAAI,SAAS,OAAO,IAAI,EAAE,CAAC,EAAE,MAAM,QAAQ;AAC7D,yBAAa,KAAK,YAAY;AAAA,cAC5B,CAAC,SAAS,OAAO,IAAI,EAAE,CAAC,GAAG,OAAO;AAAA,cAClC,CAAC,QAAQ,OAAO,IAAI,EAAE,CAAC,GAAG,OAAO;AAAA,YACnC,CAAC;AAAA,UACH;AAGA,uBAAa,KAAK,YAAY;AAAA,YAC5B,CAAC,SAAS,OAAO,OAAO;AAAA,YACxB,CAAC,SAAS,iBAAiB,OAAO;AAAA,YAClC,CAAC,QAAQ,iBAAiB,OAAO;AAAA,YACjC,CAAC,SAASC,OAAM,OAAO;AAAA,UACzB,CAAC;AAGD;AAAA,YACE,QAAQ,OAAO,WAAW,WAAW;AAAA,YACrC;AAAA,UACF;AAGA,uBAAa;AAAA,YACX;AAAA,YACA;AAAA,cACE,QAAQ,OAAO,WAAW,WAAW;AAAA,cACrC,OAAO,MAAM,OAAO,GAAGD,MAAK;AAAA,cAC5B;AAAA,YACF;AAAA,UACF;AAGA,uBAAa,KAAK,YAAY;AAAA,YAC5B,CAAC,QAAQC,OAAM,OAAO;AAAA,YACtB,CAAC,SAAS,iBAAiB,OAAO;AAAA,YAClC,CAAC,QAAQ,iBAAiB,OAAO;AAAA,YACjC,CAAC,QAAQ,OAAO,OAAO;AAAA,UACzB,CAAC;AAGD,cAAI,OAAOD,MAAK,EAAE,CAAC,EAAE,IAAI,SAAS,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAM,QAAQ;AAC/D,qBAAS;AACT,yBAAa,KAAK,YAAY;AAAA,cAC5B,CAAC,SAAS,OAAOA,MAAK,EAAE,CAAC,GAAG,OAAO;AAAA,cACnC,CAAC,QAAQ,OAAOA,MAAK,EAAE,CAAC,GAAG,OAAO;AAAA,YACpC,CAAC;AAAA,UACH,OAAO;AACL,qBAAS;AAAA,UACX;AAEA,iBAAO,QAAQ,OAAO,GAAGA,SAAQ,OAAO,GAAG,UAAU;AAErD,UAAAA,SAAQ,OAAO,WAAW,SAAS,SAAS;AAC5C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,EAAAA,SAAQ;AAER,SAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,QAAI,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,qBAAqB;AACjD,aAAOA,MAAK,EAAE,CAAC,EAAE,OAAO;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AACT;AAOA,SAAS,kBAAkB,SAASE,KAAI;AACtC,QAAMC,oBAAmB,KAAK,OAAO,WAAW,iBAAiB;AACjE,QAAMC,YAAW,KAAK;AACtB,QAAM,SAAS,kBAAkBA,SAAQ;AAGzC,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB;AAAA,MACE,SAAS,MAAM,YAAY,SAAS,MAAM;AAAA,MAC1C;AAAA,IACF;AACA,aAAS;AACT,YAAQ,MAAM,mBAAmB;AACjC,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AACpB,QAAI,SAAS,QAAQ;AACnB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,UAAM,QAAQ,QAAQ,KAAK,mBAAmB;AAG9C,UAAM,QAAQ,kBAAkB,IAAI;AAGpC,OAAOD,mBAAkB,6CAA6C;AAEtE,UAAM,OACJ,CAAC,SACA,UAAU,UAAU,6BAA6B,UAClDA,kBAAiB,SAAS,IAAI;AAChC,UAAM,QACJ,CAAC,UACA,WAAW,UAAU,6BAA6B,SACnDA,kBAAiB,SAASC,SAAQ;AAEpC,UAAM,QAAQ;AAAA,MACZ,WAAW,MAAM,WAAW,OAAO,SAAS,UAAU,CAAC;AAAA,IACzD;AACA,UAAM,SAAS;AAAA,MACb,WAAW,MAAM,WAAW,QAAQ,UAAU,SAAS,CAAC;AAAA,IAC1D;AACA,WAAOF,IAAG,IAAI;AAAA,EAChB;AACF;AAeA,SAAS,UAAUG,QAAO,QAAQ;AAChC,EAAAA,OAAM,UAAU;AAChB,EAAAA,OAAM,UAAU;AAChB,EAAAA,OAAM,gBAAgB;AACxB;;;ACxRO,IAAM,WAAW,EAAC,MAAM,YAAY,UAAU,iBAAgB;AAOrE,SAAS,iBAAiB,SAASC,KAAI,KAAK;AAC1C,MAAI,OAAO;AAEX,SAAO;AAcP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,UAAU,cAAc;AAC9C,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,cAAc;AAClC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,cAAc;AACjC,YAAQ,MAAM,MAAM,gBAAgB;AACpC,WAAO;AAAA,EACT;AAcA,WAAS,KAAK,MAAM;AAClB,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,QAAQ;AACzB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,WAAO,WAAW,IAAI;AAAA,EACxB;AAcA,WAAS,mBAAmB,MAAM;AAEhC,QACE,SAAS,MAAM,YACf,SAAS,MAAM,QACf,SAAS,MAAM,OACf,kBAAkB,IAAI,GACtB;AAEA,aAAO;AACP,aAAO,yBAAyB,IAAI;AAAA,IACtC;AAEA,WAAO,WAAW,IAAI;AAAA,EACxB;AAcA,WAAS,yBAAyB,MAAM;AACtC,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AACP,aAAO;AAAA,IACT;AAGA,SACG,SAAS,MAAM,YACd,SAAS,MAAM,QACf,SAAS,MAAM,OACf,kBAAkB,IAAI,MACxB,SAAS,UAAU,uBACnB;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AAYA,WAAS,UAAU,MAAM;AACvB,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,KAAK,MAAM,gBAAgB;AACnC,cAAQ,MAAM,MAAM,cAAc;AAClC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,cAAc;AACjC,cAAQ,KAAK,MAAM,QAAQ;AAC3B,aAAOA;AAAA,IACT;AAGA,QACE,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,YACf,aAAa,IAAI,GACjB;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,WAAW,MAAM;AACxB,QAAI,SAAS,MAAM,QAAQ;AACzB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,iBAAiB,MAAM;AAC9B,WAAO,kBAAkB,IAAI,IAAI,WAAW,IAAI,IAAI,IAAI,IAAI;AAAA,EAC9D;AAYA,WAAS,WAAW,MAAM;AACxB,QAAI,SAAS,MAAM,KAAK;AACtB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AACP,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,aAAa;AAE9B,cAAQ,KAAK,MAAM,gBAAgB,EAAE,OAAO,MAAM;AAClD,cAAQ,MAAM,MAAM,cAAc;AAClC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,cAAc;AACjC,cAAQ,KAAK,MAAM,QAAQ;AAC3B,aAAOA;AAAA,IACT;AAEA,WAAO,WAAW,IAAI;AAAA,EACxB;AAcA,WAAS,WAAW,MAAM;AAExB,SACG,SAAS,MAAM,QAAQ,kBAAkB,IAAI,MAC9C,SAAS,UAAU,uBACnB;AACA,YAAM,OAAO,SAAS,MAAM,OAAO,aAAa;AAChD,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;AC9PO,IAAM,YAAY,EAAC,SAAS,MAAM,UAAU,kBAAiB;AAOpE,SAAS,kBAAkB,SAASC,KAAI,KAAK;AAC3C,SAAO;AAgBP,WAAS,MAAM,MAAM;AACnB,WAAO,cAAc,IAAI,IACrB,aAAa,SAAS,OAAO,MAAM,UAAU,EAAE,IAAI,IACnD,MAAM,IAAI;AAAA,EAChB;AAgBA,WAAS,MAAM,MAAM;AACnB,WAAO,SAAS,MAAM,OAAO,mBAAmB,IAAI,IAAIA,IAAG,IAAI,IAAI,IAAI,IAAI;AAAA,EAC7E;AACF;;;AC7CO,IAAM,aAAa;AAAA,EACxB,cAAc,EAAC,UAAU,+BAA8B;AAAA,EACvD;AAAA,EACA,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,wBAAwB,SAASC,KAAI,KAAK;AACjD,QAAM,OAAO;AAEb,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,aAAa;AAC9B,YAAM,QAAQ,KAAK;AAEnB,SAAO,OAAO,sDAAsD;AAEpE,UAAI,CAAC,MAAM,MAAM;AACf,gBAAQ,MAAM,MAAM,YAAY,EAAC,YAAY,KAAI,CAAC;AAClD,cAAM,OAAO;AAAA,MACf;AAEA,cAAQ,MAAM,MAAM,gBAAgB;AACpC,cAAQ,MAAM,MAAM,gBAAgB;AACpC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,gBAAgB;AACnC,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,MAAM,MAAM;AACnB,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,MAAM,MAAM,0BAA0B;AAC9C,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,0BAA0B;AAC7C,cAAQ,KAAK,MAAM,gBAAgB;AACnC,aAAOA;AAAA,IACT;AAEA,YAAQ,KAAK,MAAM,gBAAgB;AACnC,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;AAeA,SAAS,+BAA+B,SAASA,KAAI,KAAK;AACxD,QAAM,OAAO;AAEb,SAAO;AAeP,WAAS,UAAU,MAAM;AACvB,QAAI,cAAc,IAAI,GAAG;AAEvB;AAAA,QACE,KAAK,OAAO,WAAW,QAAQ;AAAA,QAC/B;AAAA,MACF;AAEA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,MAChB,EAAE,IAAI;AAAA,IACR;AAEA,WAAO,WAAW,IAAI;AAAA,EACxB;AAeA,WAAS,WAAW,MAAM;AACxB,WAAO,QAAQ,QAAQ,YAAYA,KAAI,GAAG,EAAE,IAAI;AAAA,EAClD;AACF;AAGA,SAAS,KAAK,SAAS;AACrB,UAAQ,KAAK,MAAM,UAAU;AAC/B;;;ACnJO,IAAM,kBAAkB;AAAA,EAC7B,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,wBAAwB,SAASC,KAAI,KAAK;AACjD,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,WAAW,eAAe;AAChD,YAAQ,MAAM,MAAM,eAAe;AACnC,YAAQ,MAAM,MAAM,YAAY;AAChC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,YAAY;AAC/B,WAAO;AAAA,EACT;AAYA,WAAS,OAAO,MAAM;AAEpB,QAAI,iBAAiB,IAAI,GAAG;AAC1B,cAAQ,MAAM,MAAM,oBAAoB;AACxC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,oBAAoB;AACvC,cAAQ,KAAK,MAAM,eAAe;AAClC,aAAOA;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;AChDO,IAAM,qBAAqB;AAAA,EAChC,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,2BAA2B,SAASC,KAAI,KAAK;AACpD,QAAM,OAAO;AACb,MAAI,OAAO;AAEX,MAAI;AAEJ,MAAI;AAEJ,SAAO;AAgBP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,WAAW,cAAc;AAC/C,YAAQ,MAAM,MAAM,kBAAkB;AACtC,YAAQ,MAAM,MAAM,wBAAwB;AAC5C,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,wBAAwB;AAC3C,WAAO;AAAA,EACT;AAiBA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,YAAY;AAC7B,cAAQ,MAAM,MAAM,+BAA+B;AACnD,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,+BAA+B;AAClD,aAAO;AAAA,IACT;AAEA,YAAQ,MAAM,MAAM,uBAAuB;AAC3C,UAAM,UAAU;AAChB,WAAO;AACP,WAAO,MAAM,IAAI;AAAA,EACnB;AAcA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,MAAM,cAAc,SAAS,MAAM,YAAY;AAC1D,cAAQ,MAAM,MAAM,mCAAmC;AACvD,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,mCAAmC;AACtD,cAAQ,MAAM,MAAM,uBAAuB;AAC3C,YAAM,UAAU;AAChB,aAAO;AACP,aAAO;AAAA,IACT;AAEA,YAAQ,MAAM,MAAM,uBAAuB;AAC3C,UAAM,UAAU;AAChB,WAAO;AACP,WAAO,MAAM,IAAI;AAAA,EACnB;AAmBA,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,aAAa,MAAM;AACpC,YAAM,QAAQ,QAAQ,KAAK,MAAM,uBAAuB;AAExD,UACE,SAAS,qBACT,CAAC,8BAA8B,KAAK,eAAe,KAAK,CAAC,GACzD;AACA,eAAO,IAAI,IAAI;AAAA,MACjB;AAIA,cAAQ,MAAM,MAAM,wBAAwB;AAC5C,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,wBAAwB;AAC3C,cAAQ,KAAK,MAAM,kBAAkB;AACrC,aAAOA;AAAA,IACT;AAEA,QAAI,KAAK,IAAI,KAAK,SAAS,KAAK;AAC9B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;ACrJA,IAAM,sBAAsB;AAAA,EAC1B,SAAS;AAAA,EACT,UAAU;AACZ;AAGO,IAAM,aAAa;AAAA,EACxB,UAAU;AAAA,EACV,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,mBAAmB,SAASC,KAAI,KAAK;AAC5C,QAAM,OAAO;AAEb,QAAM,aAAa,EAAC,SAAS,MAAM,UAAU,mBAAkB;AAC/D,MAAI,gBAAgB;AACpB,MAAI,WAAW;AAEf,MAAI;AAEJ,SAAO;AAcP,WAAS,MAAM,MAAM;AAEnB,WAAO,mBAAmB,IAAI;AAAA,EAChC;AAcA,WAAS,mBAAmB,MAAM;AAChC;AAAA,MACE,SAAS,MAAM,eAAe,SAAS,MAAM;AAAA,MAC7C;AAAA,IACF;AAEA,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,oBACE,QAAQ,KAAK,CAAC,EAAE,SAAS,MAAM,aAC3B,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,SACtC;AAEN,aAAS;AACT,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,MAAM,MAAM,eAAe;AACnC,YAAQ,MAAM,MAAM,uBAAuB;AAC3C,WAAO,aAAa,IAAI;AAAA,EAC1B;AAcA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,QAAQ;AACnB;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,UAAU,2BAA2B;AAClD,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,KAAK,MAAM,uBAAuB;AAC1C,WAAO,cAAc,IAAI,IACrB,aAAa,SAAS,YAAY,MAAM,UAAU,EAAE,IAAI,IACxD,WAAW,IAAI;AAAA,EACrB;AAcA,WAAS,WAAW,MAAM;AACxB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,eAAe;AAClC,aAAO,KAAK,YACRA,IAAG,IAAI,IACP,QAAQ,MAAM,qBAAqB,gBAAgB,KAAK,EAAE,IAAI;AAAA,IACpE;AAEA,YAAQ,MAAM,MAAM,mBAAmB;AACvC,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,KAAK,IAAI;AAAA,EAClB;AAcA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,MAAM,mBAAmB;AACtC,aAAO,WAAW,IAAI;AAAA,IACxB;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,MAAM,mBAAmB;AACtC,aAAO,aAAa,SAAS,YAAY,MAAM,UAAU,EAAE,IAAI;AAAA,IACjE;AAEA,QAAI,SAAS,MAAM,eAAe,SAAS,QAAQ;AACjD,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAcA,WAAS,WAAW,MAAM;AACxB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,aAAO,WAAW,IAAI;AAAA,IACxB;AAEA,YAAQ,MAAM,MAAM,mBAAmB;AACvC,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,KAAK,IAAI;AAAA,EAClB;AAcA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,MAAM,mBAAmB;AACtC,aAAO,WAAW,IAAI;AAAA,IACxB;AAEA,QAAI,SAAS,MAAM,eAAe,SAAS,QAAQ;AACjD,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAeA,WAAS,eAAe,MAAM;AAC5B,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,WAAO,QAAQ,QAAQ,YAAY,OAAO,aAAa,EAAE,IAAI;AAAA,EAC/D;AAcA,WAAS,cAAc,MAAM;AAC3B,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO;AAAA,EACT;AAcA,WAAS,aAAa,MAAM;AAC1B,WAAO,gBAAgB,KAAK,cAAc,IAAI,IAC1C;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,gBAAgB;AAAA,IAClB,EAAE,IAAI,IACN,mBAAmB,IAAI;AAAA,EAC7B;AAcA,WAAS,mBAAmB,MAAM;AAChC,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,aAAO,QAAQ,MAAM,qBAAqB,gBAAgB,KAAK,EAAE,IAAI;AAAA,IACvE;AAEA,YAAQ,MAAM,MAAM,aAAa;AACjC,WAAO,aAAa,IAAI;AAAA,EAC1B;AAcA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,aAAa;AAChC,aAAO,mBAAmB,IAAI;AAAA,IAChC;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAcA,WAAS,MAAM,MAAM;AACnB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAOA,IAAG,IAAI;AAAA,EAChB;AAOA,WAAS,mBAAmBC,UAASD,KAAIE,MAAK;AAC5C,QAAI,OAAO;AAEX,WAAO;AAOP,aAAS,YAAY,MAAM;AACzB,SAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,MAAAD,SAAQ,MAAM,MAAM,UAAU;AAC9B,MAAAA,SAAQ,QAAQ,IAAI;AACpB,MAAAA,SAAQ,KAAK,MAAM,UAAU;AAC7B,aAAOE;AAAA,IACT;AAcA,aAASA,OAAM,MAAM;AAEnB;AAAA,QACE,KAAK,OAAO,WAAW,QAAQ;AAAA,QAC/B;AAAA,MACF;AAGA,MAAAF,SAAQ,MAAM,MAAM,eAAe;AACnC,aAAO,cAAc,IAAI,IACrB;AAAA,QACEA;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,MAChB,EAAE,IAAI,IACN,oBAAoB,IAAI;AAAA,IAC9B;AAcA,aAAS,oBAAoB,MAAM;AACjC,UAAI,SAAS,QAAQ;AACnB,QAAAA,SAAQ,MAAM,MAAM,uBAAuB;AAC3C,eAAO,cAAc,IAAI;AAAA,MAC3B;AAEA,aAAOC,KAAI,IAAI;AAAA,IACjB;AAcA,aAAS,cAAc,MAAM;AAC3B,UAAI,SAAS,QAAQ;AACnB;AACA,QAAAD,SAAQ,QAAQ,IAAI;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ,UAAU;AACpB,QAAAA,SAAQ,KAAK,MAAM,uBAAuB;AAC1C,eAAO,cAAc,IAAI,IACrB,aAAaA,UAAS,oBAAoB,MAAM,UAAU,EAAE,IAAI,IAChE,mBAAmB,IAAI;AAAA,MAC7B;AAEA,aAAOC,KAAI,IAAI;AAAA,IACjB;AAcA,aAAS,mBAAmB,MAAM;AAChC,UAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,QAAAD,SAAQ,KAAK,MAAM,eAAe;AAClC,eAAOD,IAAG,IAAI;AAAA,MAChB;AAEA,aAAOE,KAAI,IAAI;AAAA,IACjB;AAAA,EACF;AACF;AAOA,SAAS,4BAA4B,SAASF,KAAI,KAAK;AACrD,QAAM,OAAO;AAEb,SAAO;AAOP,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO;AAAA,EACT;AAOA,WAAS,UAAU,MAAM;AACvB,WAAO,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI,IAAIA,IAAG,IAAI;AAAA,EAChE;AACF;;;AClfO,IAAM,eAAe;AAAA,EAC1B,MAAM;AAAA,EACN,UAAU;AACZ;AAGA,IAAM,eAAe,EAAC,SAAS,MAAM,UAAU,qBAAoB;AAOnE,SAAS,qBAAqB,SAASI,KAAI,KAAK;AAC9C,QAAM,OAAO;AACb,SAAO;AAgBP,WAAS,MAAM,MAAM;AAEnB,OAAO,cAAc,IAAI,CAAC;AAC1B,YAAQ,MAAM,MAAM,YAAY;AAGhC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,UAAU,UAAU;AAAA,IACtB,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,YAAY,MAAM;AACzB,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,WAAO,QACL,KAAK,CAAC,EAAE,SAAS,MAAM,cACvB,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,UAAU,UAAU,UACxD,QAAQ,IAAI,IACZ,IAAI,IAAI;AAAA,EACd;AAYA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,MAAM,IAAI;AAAA,IACnB;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,aAAO,QAAQ,QAAQ,cAAc,SAAS,KAAK,EAAE,IAAI;AAAA,IAC3D;AAEA,YAAQ,MAAM,MAAM,aAAa;AACjC,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AACpB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,aAAa;AAChC,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAGA,WAAS,MAAM,MAAM;AACnB,YAAQ,KAAK,MAAM,YAAY;AAI/B,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;AAOA,SAAS,qBAAqB,SAASA,KAAI,KAAK;AAC9C,QAAM,OAAO;AAEb,SAAOC;AAaP,WAASA,cAAa,MAAM;AAG1B,QAAI,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG;AACrC,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAOA;AAAA,IACT;AAQA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,UAAU,UAAU;AAAA,IACtB,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,YAAY,MAAM;AACzB,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,WAAO,QACL,KAAK,CAAC,EAAE,SAAS,MAAM,cACvB,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,UAAU,UAAU,UACxDD,IAAG,IAAI,IACP,mBAAmB,IAAI,IACrBC,cAAa,IAAI,IACjB,IAAI,IAAI;AAAA,EAChB;AACF;;;ACxLO,IAAM,WAAW;AAAA,EACtB,MAAM;AAAA,EACN;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AACZ;AAIA,SAAS,gBAAgB,QAAQ;AAC/B,MAAI,gBAAgB,OAAO,SAAS;AACpC,MAAI,iBAAiB;AAErB,MAAIC;AAEJ,MAAI;AAGJ,OACG,OAAO,cAAc,EAAE,CAAC,EAAE,SAAS,MAAM,cACxC,OAAO,cAAc,EAAE,CAAC,EAAE,SAAS,aACpC,OAAO,aAAa,EAAE,CAAC,EAAE,SAAS,MAAM,cACvC,OAAO,aAAa,EAAE,CAAC,EAAE,SAAS,UACpC;AACA,IAAAA,SAAQ;AAGR,WAAO,EAAEA,SAAQ,eAAe;AAC9B,UAAI,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,cAAc;AAEhD,eAAO,cAAc,EAAE,CAAC,EAAE,OAAO,MAAM;AACvC,eAAO,aAAa,EAAE,CAAC,EAAE,OAAO,MAAM;AACtC,0BAAkB;AAClB,yBAAiB;AACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,EAAAA,SAAQ,iBAAiB;AACzB;AAEA,SAAO,EAAEA,UAAS,eAAe;AAC/B,QAAI,UAAU,QAAW;AACvB,UACEA,WAAU,iBACV,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,YAChC;AACA,gBAAQA;AAAA,MACV;AAAA,IACF,WACEA,WAAU,iBACV,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,YAChC;AACA,aAAO,KAAK,EAAE,CAAC,EAAE,OAAO,MAAM;AAE9B,UAAIA,WAAU,QAAQ,GAAG;AACvB,eAAO,KAAK,EAAE,CAAC,EAAE,MAAM,OAAOA,SAAQ,CAAC,EAAE,CAAC,EAAE;AAC5C,eAAO,OAAO,QAAQ,GAAGA,SAAQ,QAAQ,CAAC;AAC1C,yBAAiBA,SAAQ,QAAQ;AACjC,QAAAA,SAAQ,QAAQ;AAAA,MAClB;AAEA,cAAQ;AAAA,IACV;AAAA,EACF;AAEA,SAAO;AACT;AAOA,SAAS,SAAS,MAAM;AAEtB,SACE,SAAS,MAAM,eACf,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM;AAE1D;AAOA,SAAS,iBAAiB,SAASC,KAAI,KAAK;AAC1C,QAAM,OAAO;AACb,MAAI,WAAW;AAEf,MAAI;AAEJ,MAAI;AAEJ,SAAO;AAcP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,aAAa,kBAAkB;AACrD,OAAO,SAAS,KAAK,MAAM,KAAK,QAAQ,GAAG,2BAA2B;AACtE,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,gBAAgB;AACpC,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,MAAM,gBAAgB;AACnC,WAAO,QAAQ,IAAI;AAAA,EACrB;AAYA,WAAS,QAAQ,MAAM;AAErB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAKA,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,MAAM,OAAO;AACrB,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,OAAO;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,MAAM,MAAM,gBAAgB;AAC5C,aAAO;AACP,aAAO,cAAc,IAAI;AAAA,IAC3B;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO;AAAA,IACT;AAGA,YAAQ,MAAM,MAAM,YAAY;AAChC,WAAO,KAAK,IAAI;AAAA,EAClB;AAYA,WAAS,KAAK,MAAM;AAClB,QACE,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,eACf,mBAAmB,IAAI,GACvB;AACA,cAAQ,KAAK,MAAM,YAAY;AAC/B,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,cAAc,MAAM;AAE3B,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,UAAU;AACrB,cAAQ,KAAK,MAAM,gBAAgB;AACnC,cAAQ,KAAK,MAAM,QAAQ;AAC3B,aAAOA,IAAG,IAAI;AAAA,IAChB;AAGA,UAAM,OAAO,MAAM;AACnB,WAAO,KAAK,IAAI;AAAA,EAClB;AACF;;;ACjOO,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,YAAY,SAAS;AAEnB,SAAK,OAAO,UAAU,CAAC,GAAG,OAAO,IAAI,CAAC;AAEtC,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IAAIC,QAAO;AACT,QAAIA,SAAQ,KAAKA,UAAS,KAAK,KAAK,SAAS,KAAK,MAAM,QAAQ;AAC9D,YAAM,IAAI;AAAA,QACR,0BACEA,SACA,oCACC,KAAK,KAAK,SAAS,KAAK,MAAM,UAC/B;AAAA,MACJ;AAAA,IACF;AAEA,QAAIA,SAAQ,KAAK,KAAK,OAAQ,QAAO,KAAK,KAAKA,MAAK;AACpD,WAAO,KAAK,MAAM,KAAK,MAAM,SAASA,SAAQ,KAAK,KAAK,SAAS,CAAC;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AACX,WAAO,KAAK,KAAK,SAAS,KAAK,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ;AACN,SAAK,UAAU,CAAC;AAChB,WAAO,KAAK,MAAM,IAAI;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,OAAO,KAAK;AAEhB,UAAM,OACJ,QAAQ,QAAQ,QAAQ,SAAY,OAAO,oBAAoB;AAEjE,QAAI,OAAO,KAAK,KAAK,QAAQ;AAC3B,aAAO,KAAK,KAAK,MAAM,OAAO,IAAI;AAAA,IACpC;AAEA,QAAI,QAAQ,KAAK,KAAK,QAAQ;AAC5B,aAAO,KAAK,MACT;AAAA,QACC,KAAK,MAAM,SAAS,OAAO,KAAK,KAAK;AAAA,QACrC,KAAK,MAAM,SAAS,QAAQ,KAAK,KAAK;AAAA,MACxC,EACC,QAAQ;AAAA,IACb;AAEA,WAAO,KAAK,KACT,MAAM,KAAK,EACX;AAAA,MACC,KAAK,MAAM,MAAM,KAAK,MAAM,SAAS,OAAO,KAAK,KAAK,MAAM,EAAE,QAAQ;AAAA,IACxE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,OAAO,OAAO,aAAa,OAAO;AAEhC,UAAM,QAAQ,eAAe;AAE7B,SAAK,UAAU,KAAK,MAAM,KAAK,CAAC;AAChC,UAAM,UAAU,KAAK,MAAM;AAAA,MACzB,KAAK,MAAM,SAAS;AAAA,MACpB,OAAO;AAAA,IACT;AACA,QAAI,MAAO,aAAY,KAAK,MAAM,KAAK;AACvC,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM;AACJ,SAAK,UAAU,OAAO,iBAAiB;AACvC,WAAO,KAAK,KAAK,IAAI;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KAAK,MAAM;AACT,SAAK,UAAU,OAAO,iBAAiB;AACvC,SAAK,KAAK,KAAK,IAAI;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,OAAO;AACd,SAAK,UAAU,OAAO,iBAAiB;AACvC,gBAAY,KAAK,MAAM,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,QAAQ,MAAM;AACZ,SAAK,UAAU,CAAC;AAChB,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAAY,OAAO;AACjB,SAAK,UAAU,CAAC;AAChB,gBAAY,KAAK,OAAO,MAAM,QAAQ,CAAC;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,UAAU,GAAG;AACX,QACE,MAAM,KAAK,KAAK,UACf,IAAI,KAAK,KAAK,UAAU,KAAK,MAAM,WAAW,KAC9C,IAAI,KAAK,KAAK,KAAK,WAAW;AAE/B;AACF,QAAI,IAAI,KAAK,KAAK,QAAQ;AAExB,YAAM,UAAU,KAAK,KAAK,OAAO,GAAG,OAAO,iBAAiB;AAC5D,kBAAY,KAAK,OAAO,QAAQ,QAAQ,CAAC;AAAA,IAC3C,OAAO;AAEL,YAAM,UAAU,KAAK,MAAM;AAAA,QACzB,KAAK,KAAK,SAAS,KAAK,MAAM,SAAS;AAAA,QACvC,OAAO;AAAA,MACT;AACA,kBAAY,KAAK,MAAM,QAAQ,QAAQ,CAAC;AAAA,IAC1C;AAAA,EACF;AACF;AAcA,SAAS,YAAYC,OAAM,OAAO;AAEhC,MAAI,aAAa;AAEjB,MAAI,MAAM,SAAS,UAAU,oBAAoB;AAC/C,IAAAA,MAAK,KAAK,GAAG,KAAK;AAAA,EACpB,OAAO;AACL,WAAO,aAAa,MAAM,QAAQ;AAChC,MAAAA,MAAK;AAAA,QACH,GAAG,MAAM,MAAM,YAAY,aAAa,UAAU,kBAAkB;AAAA,MACtE;AACA,oBAAc,UAAU;AAAA,IAC1B;AAAA,EACF;AACF;;;AC1QO,SAAS,YAAY,aAAa;AAEvC,QAAM,QAAQ,CAAC;AACf,MAAIC,SAAQ;AAEZ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AACJ,QAAM,SAAS,IAAI,aAAa,WAAW;AAE3C,SAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,WAAOA,UAAS,OAAO;AACrB,MAAAA,SAAQ,MAAMA,MAAK;AAAA,IACrB;AAEA,YAAQ,OAAO,IAAIA,MAAK;AAIxB,QACEA,UACA,MAAM,CAAC,EAAE,SAAS,MAAM,aACxB,OAAO,IAAIA,SAAQ,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM,gBACxC;AACA,SAAO,MAAM,CAAC,EAAE,YAAY,oCAAoC;AAChE,kBAAY,MAAM,CAAC,EAAE,WAAW;AAChC,mBAAa;AAEb,UACE,aAAa,UAAU,UACvB,UAAU,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,iBACxC;AACA,sBAAc;AAAA,MAChB;AAEA,UACE,aAAa,UAAU,UACvB,UAAU,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,SACxC;AACA,eAAO,EAAE,aAAa,UAAU,QAAQ;AACtC,cAAI,UAAU,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,SAAS;AACnD;AAAA,UACF;AAEA,cAAI,UAAU,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,WAAW;AACrD,sBAAU,UAAU,EAAE,CAAC,EAAE,8BAA8B;AACvD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,QAAI,MAAM,CAAC,MAAM,SAAS;AACxB,UAAI,MAAM,CAAC,EAAE,aAAa;AACxB,eAAO,OAAO,OAAO,WAAW,QAAQA,MAAK,CAAC;AAC9C,QAAAA,SAAQ,MAAMA,MAAK;AACnB,eAAO;AAAA,MACT;AAAA,IACF,WAES,MAAM,CAAC,EAAE,YAAY;AAC5B,mBAAaA;AACb,kBAAY;AAEZ,aAAO,cAAc;AACnB,qBAAa,OAAO,IAAI,UAAU;AAElC,YACE,WAAW,CAAC,EAAE,SAAS,MAAM,cAC7B,WAAW,CAAC,EAAE,SAAS,MAAM,iBAC7B;AACA,cAAI,WAAW,CAAC,MAAM,SAAS;AAC7B,gBAAI,WAAW;AACb,qBAAO,IAAI,SAAS,EAAE,CAAC,EAAE,OAAO,MAAM;AAAA,YACxC;AAEA,uBAAW,CAAC,EAAE,OAAO,MAAM;AAC3B,wBAAY;AAAA,UACd;AAAA,QACF,WACE,WAAW,CAAC,EAAE,SAAS,MAAM,cAC7B,WAAW,CAAC,EAAE,SAAS,MAAM,gBAC7B;AAAA,QAEF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAEA,UAAI,WAAW;AAEb,cAAM,CAAC,EAAE,MAAM,EAAC,GAAG,OAAO,IAAI,SAAS,EAAE,CAAC,EAAE,MAAK;AAGjD,qBAAa,OAAO,MAAM,WAAWA,MAAK;AAC1C,mBAAW,QAAQ,KAAK;AACxB,eAAO,OAAO,WAAWA,SAAQ,YAAY,GAAG,UAAU;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAGA,SAAO,aAAa,GAAG,OAAO,mBAAmB,OAAO,MAAM,CAAC,CAAC;AAChE,SAAO,CAAC;AACV;AAYA,SAAS,WAAW,QAAQ,YAAY;AACtC,QAAM,QAAQ,OAAO,IAAI,UAAU,EAAE,CAAC;AACtC,QAAM,UAAU,OAAO,IAAI,UAAU,EAAE,CAAC;AACxC,MAAI,gBAAgB,aAAa;AAEjC,QAAM,iBAAiB,CAAC;AACxB,KAAO,MAAM,aAAa,qCAAqC;AAE/D,MAAI,YAAY,MAAM;AAEtB,MAAI,CAAC,WAAW;AACd,gBAAY,QAAQ,OAAO,MAAM,WAAW,EAAE,MAAM,KAAK;AAEzD,QAAI,MAAM,0BAA0B;AAClC,gBAAU,2BAA2B;AAAA,IACvC;AAAA,EACF;AAEA,QAAM,cAAc,UAAU;AAE9B,QAAM,QAAQ,CAAC;AAEf,QAAM,OAAO,CAAC;AAEd,MAAI;AAEJ,MAAIC;AACJ,MAAID,SAAQ;AAEZ,MAAI,UAAU;AACd,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,QAAM,SAAS,CAAC,KAAK;AAIrB,SAAO,SAAS;AAEd,WAAO,OAAO,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,SAAS;AAAA,IAEnD;AAEA;AAAA,MACE,CAACC,aAAY,QAAQ,aAAaA;AAAA,MAClC;AAAA,IACF;AACA,OAAO,CAACA,aAAYA,UAAS,SAAS,SAAS,wBAAwB;AAEvE,mBAAe,KAAK,aAAa;AAEjC,QAAI,CAAC,QAAQ,YAAY;AACvB,eAAS,QAAQ,YAAY,OAAO;AAEpC,UAAI,CAAC,QAAQ,MAAM;AACjB,eAAO,KAAK,MAAM,GAAG;AAAA,MACvB;AAEA,UAAIA,WAAU;AACZ,kBAAU,WAAW,QAAQ,KAAK;AAAA,MACpC;AAEA,UAAI,QAAQ,6BAA6B;AACvC,kBAAU,qCAAqC;AAAA,MACjD;AAEA,gBAAU,MAAM,MAAM;AAEtB,UAAI,QAAQ,6BAA6B;AACvC,kBAAU,qCAAqC;AAAA,MACjD;AAAA,IACF;AAGA,IAAAA,YAAW;AACX,cAAU,QAAQ;AAAA,EACpB;AAIA,YAAU;AAEV,SAAO,EAAED,SAAQ,YAAY,QAAQ;AACnC;AAAA;AAAA,MAEE,YAAYA,MAAK,EAAE,CAAC,MAAM,UAC1B,YAAYA,SAAQ,CAAC,EAAE,CAAC,MAAM,WAC9B,YAAYA,MAAK,EAAE,CAAC,EAAE,SAAS,YAAYA,SAAQ,CAAC,EAAE,CAAC,EAAE,QACzD,YAAYA,MAAK,EAAE,CAAC,EAAE,MAAM,SAAS,YAAYA,MAAK,EAAE,CAAC,EAAE,IAAI;AAAA,MAC/D;AACA,SAAO,SAAS,0BAA0B;AAC1C,cAAQA,SAAQ;AAChB,aAAO,KAAK,KAAK;AAEjB,cAAQ,aAAa;AACrB,cAAQ,WAAW;AACnB,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AAGA,YAAU,SAAS,CAAC;AAKpB,MAAI,SAAS;AAEX,YAAQ,aAAa;AACrB,YAAQ,WAAW;AACnB,OAAO,CAAC,QAAQ,MAAM,wBAAwB;AAAA,EAChD,OAAO;AACL,WAAO,IAAI;AAAA,EACb;AAIA,EAAAA,SAAQ,OAAO;AAEf,SAAOA,UAAS;AACd,UAAM,QAAQ,YAAY,MAAM,OAAOA,MAAK,GAAG,OAAOA,SAAQ,CAAC,CAAC;AAChE,UAAME,SAAQ,eAAe,IAAI;AACjC,OAAOA,WAAU,QAAW,yCAAyC;AACrE,UAAM,KAAK,CAACA,QAAOA,SAAQ,MAAM,SAAS,CAAC,CAAC;AAC5C,WAAO,OAAOA,QAAO,GAAG,KAAK;AAAA,EAC/B;AAEA,QAAM,QAAQ;AACd,EAAAF,SAAQ;AAER,SAAO,EAAEA,SAAQ,MAAM,QAAQ;AAC7B,SAAK,SAAS,MAAMA,MAAK,EAAE,CAAC,CAAC,IAAI,SAAS,MAAMA,MAAK,EAAE,CAAC;AACxD,cAAU,MAAMA,MAAK,EAAE,CAAC,IAAI,MAAMA,MAAK,EAAE,CAAC,IAAI;AAAA,EAChD;AAEA,SAAO;AACT;;;ACtQO,IAAMG,WAAU,EAAC,SAAS,gBAAgB,UAAU,gBAAe;AAG1E,IAAM,wBAAwB,EAAC,SAAS,MAAM,UAAU,qBAAoB;AAQ5E,SAAS,eAAe,QAAQ;AAC9B,cAAY,MAAM;AAClB,SAAO;AACT;AAOA,SAAS,gBAAgB,SAASC,KAAI;AAEpC,MAAIC;AAEJ,SAAO;AAYP,WAAS,WAAW,MAAM;AACxB;AAAA,MACE,SAAS,MAAM,OAAO,CAAC,mBAAmB,IAAI;AAAA,MAC9C;AAAA,IACF;AAEA,YAAQ,MAAM,MAAM,OAAO;AAC3B,IAAAA,YAAW,QAAQ,MAAM,MAAM,cAAc;AAAA,MAC3C,aAAa,UAAU;AAAA,IACzB,CAAC;AACD,WAAO,YAAY,IAAI;AAAA,EACzB;AAYA,WAAS,YAAY,MAAM;AACzB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,WAAW,IAAI;AAAA,IACxB;AAIA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,aAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAE,IAAI;AAAA,IACR;AAGA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAOA,WAAS,WAAW,MAAM;AACxB,YAAQ,KAAK,MAAM,YAAY;AAC/B,YAAQ,KAAK,MAAM,OAAO;AAC1B,WAAOD,IAAG,IAAI;AAAA,EAChB;AAOA,WAAS,gBAAgB,MAAM;AAC7B,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,YAAY;AAC/B,OAAOC,WAAU,yBAAyB;AAC1C,IAAAA,UAAS,OAAO,QAAQ,MAAM,MAAM,cAAc;AAAA,MAChD,aAAa,UAAU;AAAA,MACvB,UAAAA;AAAA,IACF,CAAC;AACD,IAAAA,YAAWA,UAAS;AACpB,WAAO;AAAA,EACT;AACF;AAOA,SAAS,qBAAqB,SAASD,KAAI,KAAK;AAC9C,QAAM,OAAO;AAEb,SAAO;AAOP,WAAS,eAAe,MAAM;AAC5B,OAAO,mBAAmB,IAAI,GAAG,wBAAwB;AACzD,YAAQ,KAAK,MAAM,YAAY;AAC/B,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO,aAAa,SAAS,UAAU,MAAM,UAAU;AAAA,EACzD;AAOA,WAAS,SAAS,MAAM;AACtB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,aAAO,IAAI,IAAI;AAAA,IACjB;AAGA;AAAA,MACE,KAAK,OAAO,WAAW,QAAQ;AAAA,MAC/B;AAAA,IACF;AAEA,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAE/C,QACE,CAAC,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,KAC5D,QACA,KAAK,CAAC,EAAE,SAAS,MAAM,cACvB,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,UAAU,UAAU,SAC1D;AACA,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,WAAO,QAAQ,UAAU,KAAK,OAAO,WAAW,MAAM,KAAKA,GAAE,EAAE,IAAI;AAAA,EACrE;AACF;;;ACxIO,SAAS,mBACd,SACAE,KACA,KACA,MACA,aACA,mBACA,SACA,YACA,KACA;AACA,QAAM,QAAQ,OAAO,OAAO;AAC5B,MAAI,UAAU;AAEd,SAAO;AAcP,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,UAAU;AAC3B,cAAQ,MAAM,IAAI;AAClB,cAAQ,MAAM,WAAW;AACzB,cAAQ,MAAM,iBAAiB;AAC/B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,iBAAiB;AAC9B,aAAO;AAAA,IACT;AAGA,QACE,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,oBACf,aAAa,IAAI,GACjB;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,MAAM,IAAI;AAClB,YAAQ,MAAM,OAAO;AACrB,YAAQ,MAAM,UAAU;AACxB,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,eAAe,MAAM;AAC5B,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,MAAM,iBAAiB;AAC/B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,iBAAiB;AAC9B,cAAQ,KAAK,WAAW;AACxB,cAAQ,KAAK,IAAI;AACjB,aAAOA;AAAA,IACT;AAEA,YAAQ,MAAM,UAAU;AACxB,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,SAAS,IAAI;AAAA,EACtB;AAYA,WAAS,SAAS,MAAM;AACtB,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,UAAU;AACvB,aAAO,eAAe,IAAI;AAAA,IAC5B;AAEA,QACE,SAAS,MAAM,OACf,SAAS,MAAM,YACf,mBAAmB,IAAI,GACvB;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO,SAAS,MAAM,YAAY,iBAAiB;AAAA,EACrD;AAYA,WAAS,eAAe,MAAM;AAC5B,QACE,SAAS,MAAM,YACf,SAAS,MAAM,eACf,SAAS,MAAM,WACf;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,SAAS,IAAI;AAAA,EACtB;AAYA,WAAS,IAAI,MAAM;AACjB,QACE,CAAC,YACA,SAAS,MAAM,OACd,SAAS,MAAM,oBACf,0BAA0B,IAAI,IAChC;AACA,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,UAAU;AACvB,cAAQ,KAAK,OAAO;AACpB,cAAQ,KAAK,IAAI;AACjB,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,QAAI,UAAU,SAAS,SAAS,MAAM,iBAAiB;AACrD,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,kBAAkB;AACnC,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAKA,QACE,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,mBACf,aAAa,IAAI,GACjB;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO,SAAS,MAAM,YAAY,YAAY;AAAA,EAChD;AAYA,WAAS,UAAU,MAAM;AACvB,QACE,SAAS,MAAM,mBACf,SAAS,MAAM,oBACf,SAAS,MAAM,WACf;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;AClNO,SAAS,aAAa,SAASC,KAAI,KAAK,MAAM,YAAY,YAAY;AAC3E,QAAM,OAAO;AACb,MAAI,OAAO;AAEX,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,mBAAmB,cAAc;AACvD,YAAQ,MAAM,IAAI;AAClB,YAAQ,MAAM,UAAU;AACxB,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,UAAU;AACvB,YAAQ,MAAM,UAAU;AACxB,WAAO;AAAA,EACT;AAYA,WAAS,QAAQ,MAAM;AACrB,QACE,OAAO,UAAU,wBACjB,SAAS,MAAM,OACf,SAAS,MAAM,qBACd,SAAS,MAAM,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,IAMtC,SAAS,MAAM,SACd,CAAC,QACD,4BAA4B,KAAK,OAAO,YAC1C;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,KAAK,UAAU;AACvB,cAAQ,MAAM,UAAU;AACxB,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,UAAU;AACvB,cAAQ,KAAK,IAAI;AACjB,aAAOA;AAAA,IACT;AAGA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO;AAAA,IACT;AAEA,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,YAAY,IAAI;AAAA,EACzB;AAYA,WAAS,YAAY,MAAM;AACzB,QACE,SAAS,MAAM,OACf,SAAS,MAAM,qBACf,SAAS,MAAM,sBACf,mBAAmB,IAAI,KACvB,SAAS,UAAU,sBACnB;AACA,cAAQ,KAAK,MAAM,WAAW;AAC9B,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,YAAQ,QAAQ,IAAI;AACpB,QAAI,CAAC,KAAM,QAAO,CAAC,cAAc,IAAI;AACrC,WAAO,SAAS,MAAM,YAAY,cAAc;AAAA,EAClD;AAYA,WAAS,YAAY,MAAM;AACzB,QACE,SAAS,MAAM,qBACf,SAAS,MAAM,aACf,SAAS,MAAM,oBACf;AACA,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,YAAY,IAAI;AAAA,EACzB;AACF;;;AC/HO,SAAS,aAAa,SAASC,KAAI,KAAK,MAAM,YAAY,YAAY;AAE3E,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,QACE,SAAS,MAAM,iBACf,SAAS,MAAM,cACf,SAAS,MAAM,iBACf;AACA,cAAQ,MAAM,IAAI;AAClB,cAAQ,MAAM,UAAU;AACxB,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,UAAU;AACvB,eAAS,SAAS,MAAM,kBAAkB,MAAM,mBAAmB;AACnE,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAcA,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,QAAQ;AACnB,cAAQ,MAAM,UAAU;AACxB,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,UAAU;AACvB,cAAQ,KAAK,IAAI;AACjB,aAAOA;AAAA,IACT;AAEA,YAAQ,MAAM,UAAU;AACxB,WAAO,QAAQ,IAAI;AAAA,EACrB;AAYA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,QAAQ;AACnB,cAAQ,KAAK,UAAU;AACvB,aAAO,MAAM,MAAM;AAAA,IACrB;AAEA,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAGA,QAAI,mBAAmB,IAAI,GAAG;AAE5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO,aAAa,SAAS,SAAS,MAAM,UAAU;AAAA,IACxD;AAEA,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,OAAO,IAAI;AAAA,EACpB;AAOA,WAAS,OAAO,MAAM;AACpB,QAAI,SAAS,UAAU,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AACrE,cAAQ,KAAK,MAAM,WAAW;AAC9B,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO,SAAS,MAAM,YAAY,SAAS;AAAA,EAC7C;AAYA,WAAS,OAAO,MAAM;AACpB,QAAI,SAAS,UAAU,SAAS,MAAM,WAAW;AAC/C,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,OAAO,IAAI;AAAA,EACpB;AACF;;;AC9IO,SAAS,kBAAkB,SAASC,KAAI;AAE7C,MAAI;AAEJ,SAAO;AAGP,WAAS,MAAM,MAAM;AACnB,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO;AACP,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,OAAO,MAAM,aAAa,MAAM;AAAA,MAClC,EAAE,IAAI;AAAA,IACR;AAEA,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;;;AC5BO,IAAM,aAAa,EAAC,MAAM,cAAc,UAAU,mBAAkB;AAG3E,IAAM,cAAc,EAAC,SAAS,MAAM,UAAU,oBAAmB;AAOjE,SAAS,mBAAmB,SAASC,KAAI,KAAK;AAC5C,QAAM,OAAO;AAEb,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AAInB,YAAQ,MAAM,MAAM,UAAU;AAC9B,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AAEpB,OAAO,SAAS,MAAM,mBAAmB,cAAc;AACvD,WAAO,aAAa;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,WAAW,MAAM;AACxB,iBAAa;AAAA,MACX,KAAK,eAAe,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,IACzE;AAEA,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,MAAM,MAAM,gBAAgB;AACpC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,gBAAgB;AACnC,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,YAAY,MAAM;AAEzB,WAAO,0BAA0B,IAAI,IACjC,kBAAkB,SAAS,iBAAiB,EAAE,IAAI,IAClD,kBAAkB,IAAI;AAAA,EAC5B;AAYA,WAAS,kBAAkB,MAAM;AAC/B,WAAO;AAAA,MACL;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,iBAAiB,MAAM;AAC9B,WAAO,QAAQ,QAAQ,aAAa,OAAO,KAAK,EAAE,IAAI;AAAA,EACxD;AAcA,WAAS,MAAM,MAAM;AACnB,WAAO,cAAc,IAAI,IACrB,aAAa,SAAS,iBAAiB,MAAM,UAAU,EAAE,IAAI,IAC7D,gBAAgB,IAAI;AAAA,EAC1B;AAcA,WAAS,gBAAgB,MAAM;AAC7B,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,UAAU;AAK7B,WAAK,OAAO,QAAQ,KAAK,UAAU;AAKnC,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAOA,SAAS,oBAAoB,SAASA,KAAI,KAAK;AAC7C,SAAOC;AAcP,WAASA,aAAY,MAAM;AACzB,WAAO,0BAA0B,IAAI,IACjC,kBAAkB,SAAS,YAAY,EAAE,IAAI,IAC7C,IAAI,IAAI;AAAA,EACd;AAaA,WAAS,aAAa,MAAM;AAC1B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,WAAW,MAAM;AACxB,WAAO,cAAc,IAAI,IACrB;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM;AAAA,IACR,EAAE,IAAI,IACN,6BAA6B,IAAI;AAAA,EACvC;AAYA,WAAS,6BAA6B,MAAM;AAC1C,WAAO,SAAS,MAAM,OAAO,mBAAmB,IAAI,IAAID,IAAG,IAAI,IAAI,IAAI,IAAI;AAAA,EAC7E;AACF;;;ACxRO,IAAM,kBAAkB;AAAA,EAC7B,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,wBAAwB,SAASE,KAAI,KAAK;AACjD,SAAO;AAaP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,WAAW,eAAe;AAChD,YAAQ,MAAM,MAAM,eAAe;AACnC,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAaA,WAAS,MAAM,MAAM;AACnB,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,KAAK,MAAM,eAAe;AAClC,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;AC1CO,IAAM,aAAa;AAAA,EACxB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,UAAU;AACZ;AAGA,SAAS,kBAAkB,QAAQ,SAAS;AAC1C,MAAI,aAAa,OAAO,SAAS;AACjC,MAAI,eAAe;AAEnB,MAAIC;AAEJ,MAAIC;AAGJ,MAAI,OAAO,YAAY,EAAE,CAAC,EAAE,SAAS,MAAM,YAAY;AACrD,oBAAgB;AAAA,EAClB;AAGA,MACE,aAAa,IAAI,gBACjB,OAAO,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,YACrC;AACA,kBAAc;AAAA,EAChB;AAEA,MACE,OAAO,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,uBACpC,iBAAiB,aAAa,KAC5B,aAAa,IAAI,gBAChB,OAAO,aAAa,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM,aAC7C;AACA,kBAAc,eAAe,MAAM,aAAa,IAAI;AAAA,EACtD;AAEA,MAAI,aAAa,cAAc;AAC7B,IAAAD,WAAU;AAAA,MACR,MAAM,MAAM;AAAA,MACZ,OAAO,OAAO,YAAY,EAAE,CAAC,EAAE;AAAA,MAC/B,KAAK,OAAO,UAAU,EAAE,CAAC,EAAE;AAAA,IAC7B;AACA,IAAAC,QAAO;AAAA,MACL,MAAM,MAAM;AAAA,MACZ,OAAO,OAAO,YAAY,EAAE,CAAC,EAAE;AAAA,MAC/B,KAAK,OAAO,UAAU,EAAE,CAAC,EAAE;AAAA,MAC3B,aAAa,UAAU;AAAA,IACzB;AAEA,WAAO,QAAQ,cAAc,aAAa,eAAe,GAAG;AAAA,MAC1D,CAAC,SAASD,UAAS,OAAO;AAAA,MAC1B,CAAC,SAASC,OAAM,OAAO;AAAA,MACvB,CAAC,QAAQA,OAAM,OAAO;AAAA,MACtB,CAAC,QAAQD,UAAS,OAAO;AAAA,IAC3B,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAOA,SAAS,mBAAmB,SAASE,KAAI,KAAK;AAC5C,MAAI,OAAO;AAEX,SAAO;AAYP,WAAS,MAAM,MAAM;AAEnB,YAAQ,MAAM,MAAM,UAAU;AAC9B,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AACpB,OAAO,SAAS,MAAM,YAAY,cAAc;AAChD,YAAQ,MAAM,MAAM,kBAAkB;AACtC,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,aAAa,MAAM;AAC1B,QACE,SAAS,MAAM,cACf,SAAS,UAAU,+BACnB;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,MAAM,OAAO,0BAA0B,IAAI,GAAG;AACzD,cAAQ,KAAK,MAAM,kBAAkB;AACrC,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,MAAM,YAAY;AAC7B,cAAQ,MAAM,MAAM,kBAAkB;AACtC,aAAO,gBAAgB,IAAI;AAAA,IAC7B;AAEA,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,UAAU;AAI7B,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,aAAO,aAAa,SAAS,SAAS,MAAM,UAAU,EAAE,IAAI;AAAA,IAC9D;AAIA,YAAQ,MAAM,MAAM,cAAc;AAClC,WAAO,KAAK,IAAI;AAAA,EAClB;AAcA,WAAS,gBAAgB,MAAM;AAC7B,QAAI,SAAS,MAAM,YAAY;AAC7B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,MAAM,kBAAkB;AACrC,WAAO,QAAQ,IAAI;AAAA,EACrB;AAYA,WAAS,KAAK,MAAM;AAClB,QACE,SAAS,MAAM,OACf,SAAS,MAAM,cACf,0BAA0B,IAAI,GAC9B;AACA,cAAQ,KAAK,MAAM,cAAc;AACjC,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AACF;;;ACzNO,IAAM,iBAAiB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAcO,IAAM,eAAe,CAAC,OAAO,UAAU,SAAS,UAAU;;;ACpE1D,IAAM,WAAW;AAAA,EACtB,UAAU;AAAA,EACV,MAAM;AAAA,EACN,WAAW;AAAA,EACX,UAAU;AACZ;AAGA,IAAM,kBAAkB,EAAC,SAAS,MAAM,UAAU,wBAAuB;AACzE,IAAM,2BAA2B;AAAA,EAC/B,SAAS;AAAA,EACT,UAAU;AACZ;AAGA,SAAS,kBAAkB,QAAQ;AACjC,MAAIC,SAAQ,OAAO;AAEnB,SAAOA,UAAS;AACd,QACE,OAAOA,MAAK,EAAE,CAAC,MAAM,WACrB,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,UAChC;AACA;AAAA,IACF;AAAA,EACF;AAEA,MAAIA,SAAQ,KAAK,OAAOA,SAAQ,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM,YAAY;AAE/D,WAAOA,MAAK,EAAE,CAAC,EAAE,QAAQ,OAAOA,SAAQ,CAAC,EAAE,CAAC,EAAE;AAE9C,WAAOA,SAAQ,CAAC,EAAE,CAAC,EAAE,QAAQ,OAAOA,SAAQ,CAAC,EAAE,CAAC,EAAE;AAElD,WAAO,OAAOA,SAAQ,GAAG,CAAC;AAAA,EAC5B;AAEA,SAAO;AACT;AAOA,SAAS,iBAAiB,SAASC,KAAI,KAAK;AAC1C,QAAM,OAAO;AAEb,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAID;AAEJ,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AAEnB,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AACpB,OAAO,SAAS,MAAM,UAAU,cAAc;AAC9C,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,YAAY;AAChC,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAgBA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,iBAAiB;AAClC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,mBAAa;AACb,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,cAAc;AAC/B,cAAQ,QAAQ,IAAI;AACpB,eAAS,UAAU;AAMnB,aAAO,KAAK,YAAYC,MAAK;AAAA,IAC/B;AAGA,QAAI,WAAW,IAAI,GAAG;AACpB,SAAO,SAAS,IAAI;AACpB,cAAQ,QAAQ,IAAI;AACpB,eAAS,OAAO,aAAa,IAAI;AACjC,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAgBA,WAAS,gBAAgB,MAAM;AAC7B,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,eAAS,UAAU;AACnB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,mBAAmB;AACpC,cAAQ,QAAQ,IAAI;AACpB,eAAS,UAAU;AACnB,MAAAD,SAAQ;AACR,aAAO;AAAA,IACT;AAGA,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,eAAS,UAAU;AAGnB,aAAO,KAAK,YAAYC,MAAK;AAAA,IAC/B;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,kBAAkB,MAAM;AAC/B,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AAGpB,aAAO,KAAK,YAAYA,MAAK;AAAA,IAC/B;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,gBAAgB,MAAM;AAC7B,UAAM,QAAQ,UAAU;AAExB,QAAI,SAAS,MAAM,WAAWD,QAAO,GAAG;AACtC,cAAQ,QAAQ,IAAI;AAEpB,UAAIA,WAAU,MAAM,QAAQ;AAG1B,eAAO,KAAK,YAAYC,MAAK;AAAA,MAC/B;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,cAAc,MAAM;AAC3B,QAAI,WAAW,IAAI,GAAG;AACpB,SAAO,SAAS,IAAI;AACpB,cAAQ,QAAQ,IAAI;AACpB,eAAS,OAAO,aAAa,IAAI;AACjC,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAcA,WAAS,QAAQ,MAAM;AACrB,QACE,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,eACf,0BAA0B,IAAI,GAC9B;AACA,YAAM,QAAQ,SAAS,MAAM;AAC7B,YAAM,OAAO,OAAO,YAAY;AAEhC,UAAI,CAAC,SAAS,CAAC,cAAc,aAAa,SAAS,IAAI,GAAG;AACxD,iBAAS,UAAU;AAGnB,eAAO,KAAK,YAAYA,IAAG,IAAI,IAAI,aAAa,IAAI;AAAA,MACtD;AAEA,UAAI,eAAe,SAAS,OAAO,YAAY,CAAC,GAAG;AACjD,iBAAS,UAAU;AAEnB,YAAI,OAAO;AACT,kBAAQ,QAAQ,IAAI;AACpB,iBAAO;AAAA,QACT;AAIA,eAAO,KAAK,YAAYA,IAAG,IAAI,IAAI,aAAa,IAAI;AAAA,MACtD;AAEA,eAAS,UAAU;AAEnB,aAAO,KAAK,aAAa,CAAC,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,IACtD,IAAI,IAAI,IACR,aACE,wBAAwB,IAAI,IAC5B,4BAA4B,IAAI;AAAA,IACxC;AAGA,QAAI,SAAS,MAAM,QAAQ,kBAAkB,IAAI,GAAG;AAClD,cAAQ,QAAQ,IAAI;AACpB,gBAAU,OAAO,aAAa,IAAI;AAClC,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,iBAAiB,MAAM;AAC9B,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AAGpB,aAAO,KAAK,YAAYA,MAAK;AAAA,IAC/B;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,wBAAwB,MAAM;AACrC,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,YAAY,IAAI;AAAA,EACzB;AAyBA,WAAS,4BAA4B,MAAM;AACzC,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,MAAM,SAAS,SAAS,MAAM,cAAc,WAAW,IAAI,GAAG;AACzE,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,YAAY,IAAI;AAAA,EACzB;AAgBA,WAAS,sBAAsB,MAAM;AAEnC,QACE,SAAS,MAAM,QACf,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,cACf,kBAAkB,IAAI,GACtB;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,2BAA2B,IAAI;AAAA,EACxC;AAeA,WAAS,2BAA2B,MAAM;AACxC,QAAI,SAAS,MAAM,UAAU;AAC3B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,4BAA4B,IAAI;AAAA,EACzC;AAeA,WAAS,6BAA6B,MAAM;AAC1C,QACE,SAAS,MAAM,OACf,SAAS,MAAM,YACf,SAAS,MAAM,YACf,SAAS,MAAM,eACf,SAAS,MAAM,aACf;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,iBAAiB,SAAS,MAAM,YAAY;AAC7D,cAAQ,QAAQ,IAAI;AACpB,gBAAU;AACV,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,+BAA+B,IAAI;AAAA,EAC5C;AAcA,WAAS,6BAA6B,MAAM;AAC1C,QAAI,SAAS,SAAS;AACpB,cAAQ,QAAQ,IAAI;AACpB,gBAAU;AACV,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,+BAA+B,MAAM;AAC5C,QACE,SAAS,MAAM,OACf,SAAS,MAAM,iBACf,SAAS,MAAM,cACf,SAAS,MAAM,SACf,SAAS,MAAM,YACf,SAAS,MAAM,YACf,SAAS,MAAM,eACf,SAAS,MAAM,eACf,0BAA0B,IAAI,GAC9B;AACA,aAAO,2BAA2B,IAAI;AAAA,IACxC;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAaA,WAAS,kCAAkC,MAAM;AAC/C,QACE,SAAS,MAAM,SACf,SAAS,MAAM,eACf,cAAc,IAAI,GAClB;AACA,aAAO,4BAA4B,IAAI;AAAA,IACzC;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,YAAY,MAAM;AACzB,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,cAAc,MAAM;AAC3B,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAGlD,aAAO,aAAa,IAAI;AAAA,IAC1B;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,QAAQ,WAAW,UAAU,aAAa;AAC3D,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,YAAY,WAAW,UAAU,SAAS;AAC3D,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,eAAe,WAAW,UAAU,iBAAiB;AACtE,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,gBAAgB,WAAW,UAAU,iBAAiB;AACvE,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,sBAAsB,WAAW,UAAU,WAAW;AACvE,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QACE,mBAAmB,IAAI,MACtB,WAAW,UAAU,aAAa,WAAW,UAAU,eACxD;AACA,cAAQ,KAAK,MAAM,YAAY;AAC/B,aAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAE,IAAI;AAAA,IACR;AAEA,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,YAAY;AAC/B,aAAO,kBAAkB,IAAI;AAAA,IAC/B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAaA,WAAS,kBAAkB,MAAM;AAC/B,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,IAAI;AAAA,EACR;AAaA,WAAS,yBAAyB,MAAM;AACtC,OAAO,mBAAmB,IAAI,CAAC;AAC/B,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO;AAAA,EACT;AAaA,WAAS,mBAAmB,MAAM;AAChC,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,aAAO,kBAAkB,IAAI;AAAA,IAC/B;AAEA,YAAQ,MAAM,MAAM,YAAY;AAChC,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,0BAA0B,MAAM;AACvC,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,uBAAuB,MAAM;AACpC,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,eAAS;AACT,aAAO;AAAA,IACT;AAEA,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,sBAAsB,MAAM;AACnC,QAAI,SAAS,MAAM,aAAa;AAC9B,YAAM,OAAO,OAAO,YAAY;AAEhC,UAAI,aAAa,SAAS,IAAI,GAAG;AAC/B,gBAAQ,QAAQ,IAAI;AACpB,eAAO;AAAA,MACT;AAEA,aAAO,aAAa,IAAI;AAAA,IAC1B;AAEA,QAAI,WAAW,IAAI,KAAK,OAAO,SAAS,UAAU,gBAAgB;AAChE,SAAO,SAAS,IAAI;AACpB,cAAQ,QAAQ,IAAI;AACpB,gBAAU,OAAO,aAAa,IAAI;AAClC,aAAO;AAAA,IACT;AAEA,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,wBAAwB,MAAM;AACrC,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,aAAa,IAAI;AAAA,EAC1B;AAoBA,WAAS,8BAA8B,MAAM;AAC3C,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,MAAM,QAAQ,WAAW,UAAU,aAAa;AAC3D,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,aAAa,IAAI;AAAA,EAC1B;AAYA,WAAS,kBAAkB,MAAM;AAC/B,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,YAAY;AAC/B,aAAO,kBAAkB,IAAI;AAAA,IAC/B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,kBAAkB,MAAM;AAC/B,YAAQ,KAAK,MAAM,QAAQ;AAK3B,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;AAOA,SAAS,iCAAiC,SAASA,KAAI,KAAK;AAC1D,QAAM,OAAO;AAEb,SAAO;AAaP,WAAS,MAAM,MAAM;AACnB,QAAI,mBAAmB,IAAI,GAAG;AAC5B,cAAQ,MAAM,MAAM,UAAU;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAaA,WAAS,MAAM,MAAM;AACnB,WAAO,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI,IAAIA,IAAG,IAAI;AAAA,EAChE;AACF;AAOA,SAAS,wBAAwB,SAASA,KAAI,KAAK;AACjD,SAAO;AAaP,WAAS,MAAM,MAAM;AACnB,OAAO,mBAAmB,IAAI,GAAG,wBAAwB;AACzD,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO,QAAQ,QAAQ,WAAWA,KAAI,GAAG;AAAA,EAC3C;AACF;;;ACt8BO,IAAM,WAAW,EAAC,MAAM,YAAY,UAAU,iBAAgB;AAOrE,SAAS,iBAAiB,SAASC,KAAI,KAAK;AAC1C,QAAM,OAAO;AAEb,MAAI;AAEJ,MAAIC;AAEJ,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,UAAU,cAAc;AAC9C,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,YAAY;AAChC,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAgBA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,iBAAiB;AAClC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,cAAc;AAC/B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAgBA,WAAS,gBAAgB,MAAM;AAC7B,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,mBAAmB;AACpC,cAAQ,QAAQ,IAAI;AACpB,MAAAA,SAAQ;AACR,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,kBAAkB,MAAM;AAC/B,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,MAAM;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,IAAI;AAAA,EACrB;AAYA,WAAS,WAAW,MAAM;AACxB,WAAO,SAAS,MAAM,cAClB,IAAI,IAAI,IACR,SAAS,MAAM,OACb,aAAa,IAAI,IACjB,QAAQ,IAAI;AAAA,EACpB;AAYA,WAAS,gBAAgB,MAAM;AAC7B,UAAM,QAAQ,UAAU;AAExB,QAAI,SAAS,MAAM,WAAWA,QAAO,GAAG;AACtC,cAAQ,QAAQ,IAAI;AACpB,aAAOA,WAAU,MAAM,SAAS,QAAQ;AAAA,IAC1C;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,WAAW,MAAM;AACxB,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,IAAI;AAAA,EACnB;AAYA,WAAS,SAAS,MAAM;AACtB,QAAI,SAAS,MAAM,aAAa;AAC9B,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,IAAI;AAAA,EACnB;AAYA,WAAS,YAAY,MAAM;AACzB,QAAI,SAAS,MAAM,OAAO,SAAS,MAAM,aAAa;AACpD,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,YAAY,MAAM;AACzB,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,cAAc;AAC/B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,iBAAiB,MAAM;AAC9B,WAAO,SAAS,MAAM,cAAc,IAAI,IAAI,IAAI,YAAY,IAAI;AAAA,EAClE;AAYA,WAAS,cAAc,MAAM;AAE3B,QAAI,WAAW,IAAI,GAAG;AACpB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,SAAS,MAAM;AAEtB,QAAI,SAAS,MAAM,QAAQ,kBAAkB,IAAI,GAAG;AAClD,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,gBAAgB,IAAI;AAAA,EAC7B;AAYA,WAAS,gBAAgB,MAAM;AAC7B,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,QAAQ,MAAM;AAErB,QAAI,SAAS,MAAM,QAAQ,kBAAkB,IAAI,GAAG;AAClD,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QACE,SAAS,MAAM,SACf,SAAS,MAAM,eACf,0BAA0B,IAAI,GAC9B;AACA,aAAO,eAAe,IAAI;AAAA,IAC5B;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,eAAe,MAAM;AAC5B,QAAI,SAAS,MAAM,OAAO;AACxB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAGA,QAAI,SAAS,MAAM,SAAS,SAAS,MAAM,cAAc,WAAW,IAAI,GAAG;AACzE,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,qBAAqB,MAAM;AAElC,QACE,SAAS,MAAM,QACf,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,cACf,kBAAkB,IAAI,GACtB;AACA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,0BAA0B,IAAI;AAAA,EACvC;AAaA,WAAS,0BAA0B,MAAM;AACvC,QAAI,SAAS,MAAM,UAAU;AAC3B,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,eAAe,IAAI;AAAA,EAC5B;AAaA,WAAS,4BAA4B,MAAM;AACzC,QACE,SAAS,MAAM,OACf,SAAS,MAAM,YACf,SAAS,MAAM,YACf,SAAS,MAAM,eACf,SAAS,MAAM,aACf;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,SAAS,MAAM,iBAAiB,SAAS,MAAM,YAAY;AAC7D,cAAQ,QAAQ,IAAI;AACpB,eAAS;AACT,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,4BAA4B,MAAM;AACzC,QAAI,SAAS,QAAQ;AACnB,cAAQ,QAAQ,IAAI;AACpB,eAAS;AACT,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,oBAAc;AACd,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,8BAA8B,MAAM;AAC3C,QACE,SAAS,MAAM,OACf,SAAS,MAAM,iBACf,SAAS,MAAM,cACf,SAAS,MAAM,YACf,SAAS,MAAM,YACf,SAAS,MAAM,aACf;AACA,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,QACE,SAAS,MAAM,SACf,SAAS,MAAM,eACf,0BAA0B,IAAI,GAC9B;AACA,aAAO,eAAe,IAAI;AAAA,IAC5B;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAaA,WAAS,iCAAiC,MAAM;AAC9C,QACE,SAAS,MAAM,SACf,SAAS,MAAM,eACf,0BAA0B,IAAI,GAC9B;AACA,aAAO,eAAe,IAAI;AAAA,IAC5B;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,IAAI,MAAM;AACjB,QAAI,SAAS,MAAM,aAAa;AAC9B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,YAAY;AAC/B,cAAQ,KAAK,MAAM,QAAQ;AAC3B,aAAOD;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAgBA,WAAS,iBAAiB,MAAM;AAC9B,OAAO,aAAa,uBAAuB;AAC3C,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,YAAQ,KAAK,MAAM,YAAY;AAC/B,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO;AAAA,EACT;AAgBA,WAAS,gBAAgB,MAAM;AAE7B;AAAA,MACE,KAAK,OAAO,WAAW,QAAQ;AAAA,MAC/B;AAAA,IACF;AACA,WAAO,cAAc,IAAI,IACrB;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,IAChB,EAAE,IAAI,IACN,sBAAsB,IAAI;AAAA,EAChC;AAgBA,WAAS,sBAAsB,MAAM;AACnC,YAAQ,MAAM,MAAM,YAAY;AAChC,WAAO,YAAY,IAAI;AAAA,EACzB;AACF;;;ACtvBO,IAAM,WAAW;AAAA,EACtB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AACZ;AAGA,IAAM,oBAAoB,EAAC,UAAU,iBAAgB;AAErD,IAAM,yBAAyB,EAAC,UAAU,sBAAqB;AAE/D,IAAM,8BAA8B,EAAC,UAAU,2BAA0B;AAGzE,SAAS,mBAAmB,QAAQ;AAClC,MAAIE,SAAQ;AAEZ,QAAM,YAAY,CAAC;AACnB,SAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,UAAM,QAAQ,OAAOA,MAAK,EAAE,CAAC;AAC7B,cAAU,KAAK,OAAOA,MAAK,CAAC;AAE5B,QACE,MAAM,SAAS,MAAM,cACrB,MAAM,SAAS,MAAM,aACrB,MAAM,SAAS,MAAM,UACrB;AAEA,YAAM,SAAS,MAAM,SAAS,MAAM,aAAa,IAAI;AACrD,YAAM,OAAO,MAAM;AACnB,MAAAA,UAAS;AAAA,IACX;AAAA,EACF;AAGA,MAAI,OAAO,WAAW,UAAU,QAAQ;AACtC,WAAO,QAAQ,GAAG,OAAO,QAAQ,SAAS;AAAA,EAC5C;AAEA,SAAO;AACT;AAGA,SAAS,kBAAkB,QAAQ,SAAS;AAC1C,MAAIA,SAAQ,OAAO;AACnB,MAAI,SAAS;AAEb,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAGJ,SAAOA,UAAS;AACd,YAAQ,OAAOA,MAAK,EAAE,CAAC;AAEvB,QAAI,MAAM;AAER,UACE,MAAM,SAAS,MAAM,QACpB,MAAM,SAAS,MAAM,aAAa,MAAM,WACzC;AACA;AAAA,MACF;AAIA,UAAI,OAAOA,MAAK,EAAE,CAAC,MAAM,WAAW,MAAM,SAAS,MAAM,WAAW;AAClE,cAAM,YAAY;AAAA,MACpB;AAAA,IACF,WAAW,OAAO;AAChB,UACE,OAAOA,MAAK,EAAE,CAAC,MAAM,YACpB,MAAM,SAAS,MAAM,cAAc,MAAM,SAAS,MAAM,cACzD,CAAC,MAAM,WACP;AACA,eAAOA;AAEP,YAAI,MAAM,SAAS,MAAM,WAAW;AAClC,mBAAS;AACT;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,MAAM,SAAS,MAAM,UAAU;AACxC,cAAQA;AAAA,IACV;AAAA,EACF;AAEA,KAAO,SAAS,QAAW,gCAAgC;AAC3D,KAAO,UAAU,QAAW,iCAAiC;AAE7D,QAAM,QAAQ;AAAA,IACZ,MAAM,OAAO,IAAI,EAAE,CAAC,EAAE,SAAS,MAAM,YAAY,MAAM,OAAO,MAAM;AAAA,IACpE,OAAO,EAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,MAAK;AAAA,IAChC,KAAK,EAAC,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,IAAG;AAAA,EAC3C;AAEA,QAAM,QAAQ;AAAA,IACZ,MAAM,MAAM;AAAA,IACZ,OAAO,EAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,MAAK;AAAA,IAChC,KAAK,EAAC,GAAG,OAAO,KAAK,EAAE,CAAC,EAAE,IAAG;AAAA,EAC/B;AAEA,QAAMC,QAAO;AAAA,IACX,MAAM,MAAM;AAAA,IACZ,OAAO,EAAC,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,IAAG;AAAA,IAC3C,KAAK,EAAC,GAAG,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,MAAK;AAAA,EACrC;AAEA,UAAQ;AAAA,IACN,CAAC,SAAS,OAAO,OAAO;AAAA,IACxB,CAAC,SAAS,OAAO,OAAO;AAAA,EAC1B;AAGA,UAAQ,KAAK,OAAO,OAAO,MAAM,OAAO,GAAG,OAAO,SAAS,CAAC,CAAC;AAG7D,UAAQ,KAAK,OAAO,CAAC,CAAC,SAASA,OAAM,OAAO,CAAC,CAAC;AAG9C;AAAA,IACE,QAAQ,OAAO,WAAW,WAAW;AAAA,IACrC;AAAA,EACF;AAEA,UAAQ;AAAA,IACN;AAAA,IACA;AAAA,MACE,QAAQ,OAAO,WAAW,WAAW;AAAA,MACrC,OAAO,MAAM,OAAO,SAAS,GAAG,QAAQ,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAGA,UAAQ,KAAK,OAAO;AAAA,IAClB,CAAC,QAAQA,OAAM,OAAO;AAAA,IACtB,OAAO,QAAQ,CAAC;AAAA,IAChB,OAAO,QAAQ,CAAC;AAAA,IAChB,CAAC,QAAQ,OAAO,OAAO;AAAA,EACzB,CAAC;AAGD,UAAQ,KAAK,OAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAG3C,UAAQ,KAAK,OAAO,CAAC,CAAC,QAAQ,OAAO,OAAO,CAAC,CAAC;AAE9C,SAAO,QAAQ,MAAM,OAAO,QAAQ,KAAK;AAEzC,SAAO;AACT;AAOA,SAAS,iBAAiB,SAASC,KAAI,KAAK;AAC1C,QAAM,OAAO;AACb,MAAIF,SAAQ,KAAK,OAAO;AAExB,MAAI;AAEJ,MAAI;AAGJ,SAAOA,UAAS;AACd,SACG,KAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,cACpC,KAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,cACvC,CAAC,KAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,WACvB;AACA,mBAAa,KAAK,OAAOA,MAAK,EAAE,CAAC;AACjC;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAiBP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,oBAAoB,cAAc;AAGxD,QAAI,CAAC,YAAY;AACf,aAAO,IAAI,IAAI;AAAA,IACjB;AAWA,QAAI,WAAW,WAAW;AACxB,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,cAAU,KAAK,OAAO,QAAQ;AAAA,MAC5B;AAAA,QACE,KAAK,eAAe,EAAC,OAAO,WAAW,KAAK,KAAK,KAAK,IAAI,EAAC,CAAC;AAAA,MAC9D;AAAA,IACF;AACA,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,WAAW;AAC/B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,WAAW;AAC9B,YAAQ,KAAK,MAAM,QAAQ;AAC3B,WAAO;AAAA,EACT;AAkBA,WAAS,MAAM,MAAM;AAKnB,QAAI,SAAS,MAAM,iBAAiB;AAClC,aAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA,UAAU,aAAa;AAAA,MACzB,EAAE,IAAI;AAAA,IACR;AAGA,QAAI,SAAS,MAAM,mBAAmB;AACpC,aAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA,UAAU,mBAAmB;AAAA,MAC/B,EAAE,IAAI;AAAA,IACR;AAGA,WAAO,UAAU,WAAW,IAAI,IAAI,YAAY,IAAI;AAAA,EACtD;AAgBA,WAAS,iBAAiB,MAAM;AAC9B,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,IAAI;AAAA,EACR;AAkBA,WAAS,WAAW,MAAM;AAExB,WAAOE,IAAG,IAAI;AAAA,EAChB;AAkBA,WAAS,YAAY,MAAM;AACzB,eAAW,YAAY;AACvB,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAOA,SAAS,iBAAiB,SAASA,KAAI,KAAK;AAC1C,SAAO;AAYP,WAAS,cAAc,MAAM;AAC3B,OAAO,SAAS,MAAM,iBAAiB,qBAAqB;AAC5D,YAAQ,MAAM,MAAM,QAAQ;AAC5B,YAAQ,MAAM,MAAM,cAAc;AAClC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,cAAc;AACjC,WAAO;AAAA,EACT;AAYA,WAAS,eAAe,MAAM;AAC5B,WAAO,0BAA0B,IAAI,IACjC,kBAAkB,SAAS,YAAY,EAAE,IAAI,IAC7C,aAAa,IAAI;AAAA,EACvB;AAYA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,kBAAkB;AACnC,aAAO,YAAY,IAAI;AAAA,IACzB;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,yBAAyB,MAAM;AACtC,WAAO,0BAA0B,IAAI,IACjC,kBAAkB,SAAS,eAAe,EAAE,IAAI,IAChD,YAAY,IAAI;AAAA,EACtB;AAYA,WAAS,2BAA2B,MAAM;AACxC,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,gBAAgB,MAAM;AAC7B,QACE,SAAS,MAAM,iBACf,SAAS,MAAM,cACf,SAAS,MAAM,iBACf;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACR,EAAE,IAAI;AAAA,IACR;AAEA,WAAO,YAAY,IAAI;AAAA,EACzB;AAYA,WAAS,mBAAmB,MAAM;AAChC,WAAO,0BAA0B,IAAI,IACjC,kBAAkB,SAAS,WAAW,EAAE,IAAI,IAC5C,YAAY,IAAI;AAAA,EACtB;AAYA,WAAS,YAAY,MAAM;AACzB,QAAI,SAAS,MAAM,kBAAkB;AACnC,cAAQ,MAAM,MAAM,cAAc;AAClC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,cAAc;AACjC,cAAQ,KAAK,MAAM,QAAQ;AAC3B,aAAOA;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAOA,SAAS,sBAAsB,SAASA,KAAI,KAAK;AAC/C,QAAM,OAAO;AAEb,SAAO;AAYP,WAAS,cAAc,MAAM;AAC3B,OAAO,SAAS,MAAM,mBAAmB,uBAAuB;AAChE,WAAO,aAAa;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,EAAE,IAAI;AAAA,EACR;AAYA,WAAS,mBAAmB,MAAM;AAChC,WAAO,KAAK,OAAO,QAAQ;AAAA,MACzB;AAAA,QACE,KAAK,eAAe,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,MACzE;AAAA,IACF,IACIA,IAAG,IAAI,IACP,IAAI,IAAI;AAAA,EACd;AAYA,WAAS,qBAAqB,MAAM;AAClC,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAOA,SAAS,2BAA2B,SAASA,KAAI,KAAK;AACpD,SAAO;AAcP,WAAS,wBAAwB,MAAM;AAErC,OAAO,SAAS,MAAM,mBAAmB,uBAAuB;AAChE,YAAQ,MAAM,MAAM,SAAS;AAC7B,YAAQ,MAAM,MAAM,eAAe;AACnC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,eAAe;AAClC,WAAO;AAAA,EACT;AAcA,WAAS,uBAAuB,MAAM;AACpC,QAAI,SAAS,MAAM,oBAAoB;AACrC,cAAQ,MAAM,MAAM,eAAe;AACnC,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,eAAe;AAClC,cAAQ,KAAK,MAAM,SAAS;AAC5B,aAAOA;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;ACjoBO,IAAM,kBAAkB;AAAA,EAC7B,MAAM;AAAA,EACN,YAAY,SAAS;AAAA,EACrB,UAAU;AACZ;AAOA,SAAS,wBAAwB,SAASC,KAAI,KAAK;AACjD,QAAM,OAAO;AAEb,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,iBAAiB,cAAc;AACrD,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,MAAM,MAAM,gBAAgB;AACpC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,gBAAgB;AACnC,WAAO;AAAA,EACT;AAYA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,mBAAmB;AACpC,cAAQ,MAAM,MAAM,WAAW;AAC/B,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,MAAM,UAAU;AAC7B,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AA6BA,WAAS,MAAM,MAAM;AAMnB,WAAO,SAAS,MAAM,SACpB,4BAA4B,KAAK,OAAO,aACtC,IAAI,IAAI,IACRA,IAAG,IAAI;AAAA,EACb;AACF;;;AC/FO,IAAM,iBAAiB;AAAA,EAC5B,MAAM;AAAA,EACN,YAAY,SAAS;AAAA,EACrB,UAAU;AACZ;AAOA,SAAS,uBAAuB,SAASC,KAAI,KAAK;AAChD,QAAM,OAAO;AAEb,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,mBAAmB,cAAc;AACvD,YAAQ,MAAM,MAAM,SAAS;AAC7B,YAAQ,MAAM,MAAM,WAAW;AAC/B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,WAAW;AAC9B,YAAQ,KAAK,MAAM,SAAS;AAC5B,WAAO;AAAA,EACT;AAGA,WAAS,MAAM,MAAM;AAKnB,WAAO,SAAS,MAAM,SACpB,4BAA4B,KAAK,OAAO,aACtC,IAAI,IAAI,IACRA,IAAG,IAAI;AAAA,EACb;AACF;;;AC9CO,IAAM,aAAa,EAAC,MAAM,cAAc,UAAU,mBAAkB;AAO3E,SAAS,mBAAmB,SAASC,KAAI;AACvC,SAAO;AAGP,WAAS,MAAM,MAAM;AACnB,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO,aAAa,SAASA,KAAI,MAAM,UAAU;AAAA,EACnD;AACF;;;ACjBO,IAAM,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,UAAU;AACZ;AAOA,SAAS,sBAAsB,SAASC,KAAI,KAAK;AAC/C,MAAI,OAAO;AAEX,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAM,MAAM;AACnB,YAAQ,MAAM,MAAM,aAAa;AAEjC,WAAO,OAAO,IAAI;AAAA,EACpB;AAYA,WAAS,OAAO,MAAM;AACpB;AAAA,MACE,SAAS,MAAM,YACb,SAAS,MAAM,QACf,SAAS,MAAM;AAAA,MACjB;AAAA,IACF;AACA,aAAS;AACT,WAAO,QAAQ,IAAI;AAAA,EACrB;AAYA,WAAS,QAAQ,MAAM;AACrB,QAAI,SAAS,QAAQ;AACnB,cAAQ,MAAM,MAAM,qBAAqB;AACzC,aAAO,SAAS,IAAI;AAAA,IACtB;AAEA,QACE,QAAQ,UAAU,gCACjB,SAAS,MAAM,OAAO,mBAAmB,IAAI,IAC9C;AACA,cAAQ,KAAK,MAAM,aAAa;AAChC,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAYA,WAAS,SAAS,MAAM;AACtB,QAAI,SAAS,QAAQ;AACnB,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,MAAM,qBAAqB;AACxC,WAAO,cAAc,IAAI,IACrB,aAAa,SAAS,SAAS,MAAM,UAAU,EAAE,IAAI,IACrD,QAAQ,IAAI;AAAA,EAClB;AACF;;;ACpGO,IAAM,OAAO;AAAA,EAClB,cAAc,EAAC,UAAU,yBAAwB;AAAA,EACjD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AACZ;AAGA,IAAM,oCAAoC;AAAA,EACxC,SAAS;AAAA,EACT,UAAU;AACZ;AAGA,IAAM,kBAAkB,EAAC,SAAS,MAAM,UAAU,eAAc;AAUhE,SAAS,kBAAkB,SAASC,KAAI,KAAK;AAC3C,QAAM,OAAO;AACb,QAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,MAAI,cACF,QAAQ,KAAK,CAAC,EAAE,SAAS,MAAM,aAC3B,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,SACtC;AACN,MAAI,OAAO;AAEX,SAAO;AAGP,WAAS,MAAM,MAAM;AACnB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,UAAM,OACJ,KAAK,eAAe,SACnB,SAAS,MAAM,YAAY,SAAS,MAAM,YAAY,SAAS,MAAM,OAClE,MAAM,gBACN,MAAM;AAEZ,QACE,SAAS,MAAM,gBACX,CAAC,KAAK,eAAe,UAAU,SAAS,KAAK,eAAe,SAC5D,WAAW,IAAI,GACnB;AACA,UAAI,CAAC,KAAK,eAAe,MAAM;AAC7B,aAAK,eAAe,OAAO;AAC3B,gBAAQ,MAAM,MAAM,EAAC,YAAY,KAAI,CAAC;AAAA,MACxC;AAEA,UAAI,SAAS,MAAM,eAAe;AAChC,gBAAQ,MAAM,MAAM,cAAc;AAClC,eAAO,SAAS,MAAM,YAAY,SAAS,MAAM,OAC7C,QAAQ,MAAM,eAAe,KAAK,QAAQ,EAAE,IAAI,IAChD,SAAS,IAAI;AAAA,MACnB;AAEA,UAAI,CAAC,KAAK,aAAa,SAAS,MAAM,QAAQ;AAC5C,gBAAQ,MAAM,MAAM,cAAc;AAClC,gBAAQ,MAAM,MAAM,aAAa;AACjC,eAAO,OAAO,IAAI;AAAA,MACpB;AAAA,IACF;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAGA,WAAS,OAAO,MAAM;AACpB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,QAAI,WAAW,IAAI,KAAK,EAAE,OAAO,UAAU,sBAAsB;AAC/D,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,SACG,CAAC,KAAK,aAAa,OAAO,OAC1B,KAAK,eAAe,SACjB,SAAS,KAAK,eAAe,SAC7B,SAAS,MAAM,oBAAoB,SAAS,MAAM,MACtD;AACA,cAAQ,KAAK,MAAM,aAAa;AAChC,aAAO,SAAS,IAAI;AAAA,IACtB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAKA,WAAS,SAAS,MAAM;AACtB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,OAAO,SAAS,MAAM,KAAK,8BAA8B;AACzD,YAAQ,MAAM,MAAM,cAAc;AAClC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,cAAc;AACjC,SAAK,eAAe,SAAS,KAAK,eAAe,UAAU;AAC3D,WAAO,QAAQ;AAAA,MACb;AAAA;AAAA,MAEA,KAAK,YAAY,MAAM;AAAA,MACvB,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,WAAS,QAAQ,MAAM;AACrB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,SAAK,eAAe,mBAAmB;AACvC;AACA,WAAO,YAAY,IAAI;AAAA,EACzB;AAGA,WAAS,YAAY,MAAM;AACzB,QAAI,cAAc,IAAI,GAAG;AACvB,cAAQ,MAAM,MAAM,wBAAwB;AAC5C,cAAQ,QAAQ,IAAI;AACpB,cAAQ,KAAK,MAAM,wBAAwB;AAC3C,aAAO;AAAA,IACT;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAGA,WAAS,YAAY,MAAM;AACzB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,SAAK,eAAe,OAClB,cACA,KAAK,eAAe,QAAQ,KAAK,MAAM,cAAc,GAAG,IAAI,EAAE;AAChE,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;AAOA,SAAS,yBAAyB,SAASA,KAAI,KAAK;AAClD,QAAM,OAAO;AAEb,KAAO,KAAK,gBAAgB,gBAAgB;AAC5C,OAAK,eAAe,aAAa;AAEjC,SAAO,QAAQ,MAAM,WAAW,SAAS,QAAQ;AAGjD,WAAS,QAAQ,MAAM;AACrB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,OAAO,OAAO,KAAK,eAAe,SAAS,UAAU,eAAe;AACpE,SAAK,eAAe,oBAClB,KAAK,eAAe,qBACpB,KAAK,eAAe;AAItB,WAAO;AAAA,MACL;AAAA,MACAA;AAAA,MACA,MAAM;AAAA,MACN,KAAK,eAAe,OAAO;AAAA,IAC7B,EAAE,IAAI;AAAA,EACR;AAGA,WAAS,SAAS,MAAM;AACtB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,QAAI,KAAK,eAAe,qBAAqB,CAAC,cAAc,IAAI,GAAG;AACjE,WAAK,eAAe,oBAAoB;AACxC,WAAK,eAAe,mBAAmB;AACvC,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,SAAK,eAAe,oBAAoB;AACxC,SAAK,eAAe,mBAAmB;AACvC,WAAO,QAAQ,QAAQ,iBAAiBA,KAAI,gBAAgB,EAAE,IAAI;AAAA,EACpE;AAGA,WAAS,iBAAiB,MAAM;AAC9B,OAAO,KAAK,gBAAgB,gBAAgB;AAE5C,SAAK,eAAe,aAAa;AAEjC,SAAK,YAAY;AAEjB;AAAA,MACE,KAAK,OAAO,WAAW,QAAQ;AAAA,MAC/B;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA,QAAQ,QAAQ,MAAMA,KAAI,GAAG;AAAA,MAC7B,MAAM;AAAA,MACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,IAChB,EAAE,IAAI;AAAA,EACR;AACF;AAOA,SAAS,eAAe,SAASA,KAAI,KAAK;AACxC,QAAM,OAAO;AAEb,KAAO,KAAK,gBAAgB,gBAAgB;AAC5C,KAAO,OAAO,KAAK,eAAe,SAAS,UAAU,eAAe;AAEpE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,KAAK,eAAe,OAAO;AAAA,EAC7B;AAGA,WAAS,YAAY,MAAM;AACzB,OAAO,KAAK,gBAAgB,gBAAgB;AAC5C,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,WAAO,QACL,KAAK,CAAC,EAAE,SAAS,MAAM,kBACvB,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,WAAW,KAAK,eAAe,OACnEA,IAAG,IAAI,IACP,IAAI,IAAI;AAAA,EACd;AACF;AAOA,SAAS,gBAAgB,SAAS;AAChC,KAAO,KAAK,gBAAgB,gBAAgB;AAC5C,KAAO,OAAO,KAAK,eAAe,SAAS,UAAU,eAAe;AACpE,UAAQ,KAAK,KAAK,eAAe,IAAI;AACvC;AAOA,SAAS,iCAAiC,SAASA,KAAI,KAAK;AAC1D,QAAM,OAAO;AAGb;AAAA,IACE,KAAK,OAAO,WAAW,QAAQ;AAAA,IAC/B;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU,UAAU;AAAA,EAC1B;AAGA,WAAS,YAAY,MAAM;AACzB,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAE/C,WAAO,CAAC,cAAc,IAAI,KACxB,QACA,KAAK,CAAC,EAAE,SAAS,MAAM,2BACrBA,IAAG,IAAI,IACP,IAAI,IAAI;AAAA,EACd;AACF;;;AChSO,IAAM,kBAAkB;AAAA,EAC7B,MAAM;AAAA,EACN,WAAW;AAAA,EACX,UAAU;AACZ;AAGA,SAAS,yBAAyB,QAAQ,SAAS;AAEjD,MAAIC,SAAQ,OAAO;AAEnB,MAAIC;AAEJ,MAAIC;AAEJ,MAAIC;AAIJ,SAAOH,UAAS;AACd,QAAI,OAAOA,MAAK,EAAE,CAAC,MAAM,SAAS;AAChC,UAAI,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,SAAS;AAC3C,QAAAC,WAAUD;AACV;AAAA,MACF;AAEA,UAAI,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,WAAW;AAC7C,QAAAE,QAAOF;AAAA,MACT;AAAA,IACF,OAEK;AACH,UAAI,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,SAAS;AAE3C,eAAO,OAAOA,QAAO,CAAC;AAAA,MACxB;AAEA,UAAI,CAACG,eAAc,OAAOH,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,YAAY;AAC7D,QAAAG,cAAaH;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAEA,KAAOE,UAAS,QAAW,qCAAqC;AAChE,KAAOD,aAAY,QAAW,qCAAqC;AACnE,KAAO,OAAOA,QAAO,EAAE,CAAC,MAAM,SAAS,8BAA8B;AACrE;AAAA,IACE,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,MAAM;AAAA,IACjC;AAAA,EACF;AACA,QAAM,UAAU;AAAA,IACd,MAAM,MAAM;AAAA,IACZ,OAAO,EAAC,GAAG,OAAOA,QAAO,EAAE,CAAC,EAAE,MAAK;AAAA,IACnC,KAAK,EAAC,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,IAAG;AAAA,EAC3C;AAGA,SAAOC,KAAI,EAAE,CAAC,EAAE,OAAO,MAAM;AAI7B,MAAIC,aAAY;AACd,WAAO,OAAOD,OAAM,GAAG,CAAC,SAAS,SAAS,OAAO,CAAC;AAClD,WAAO,OAAOC,cAAa,GAAG,GAAG,CAAC,QAAQ,OAAOF,QAAO,EAAE,CAAC,GAAG,OAAO,CAAC;AACtE,WAAOA,QAAO,EAAE,CAAC,EAAE,MAAM,EAAC,GAAG,OAAOE,WAAU,EAAE,CAAC,EAAE,IAAG;AAAA,EACxD,OAAO;AACL,WAAOF,QAAO,EAAE,CAAC,IAAI;AAAA,EACvB;AAGA,SAAO,KAAK,CAAC,QAAQ,SAAS,OAAO,CAAC;AAEtC,SAAO;AACT;AAOA,SAAS,wBAAwB,SAASG,KAAI,KAAK;AACjD,QAAM,OAAO;AAEb,MAAI;AAEJ,SAAO;AAaP,WAAS,MAAM,MAAM;AACnB,QAAIJ,SAAQ,KAAK,OAAO;AAExB,QAAI;AAEJ;AAAA,MACE,SAAS,MAAM,QAAQ,SAAS,MAAM;AAAA,MACtC;AAAA,IACF;AAGA,WAAOA,UAAS;AAGd,UACE,KAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,cACrC,KAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,cACrC,KAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,SACrC;AACA,oBAAY,KAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM;AACjD;AAAA,MACF;AAAA,IACF;AAIA,QAAI,CAAC,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,MAAM,KAAK,aAAa,YAAY;AACvE,cAAQ,MAAM,MAAM,iBAAiB;AACrC,eAAS;AACT,aAAO,OAAO,IAAI;AAAA,IACpB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AAaA,WAAS,OAAO,MAAM;AACpB,YAAQ,MAAM,MAAM,yBAAyB;AAC7C,WAAO,OAAO,IAAI;AAAA,EACpB;AAaA,WAAS,OAAO,MAAM;AACpB,QAAI,SAAS,QAAQ;AACnB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,MAAM,yBAAyB;AAE5C,WAAO,cAAc,IAAI,IACrB,aAAa,SAAS,OAAO,MAAM,UAAU,EAAE,IAAI,IACnD,MAAM,IAAI;AAAA,EAChB;AAaA,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,iBAAiB;AACpC,aAAOI,IAAG,IAAI;AAAA,IAChB;AAEA,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;;;ACjMO,IAAM,OAAO,EAAC,UAAU,eAAc;AAQ7C,SAAS,eAAe,SAAS;AAC/B,QAAM,OAAO;AACb,QAAM,UAAU,QAAQ;AAAA;AAAA,IAEtB;AAAA,IACA;AAAA;AAAA,IAEA,QAAQ;AAAA,MACN,KAAK,OAAO,WAAW;AAAA,MACvB;AAAA,MACA;AAAA,QACE;AAAA,QACA,QAAQ;AAAA,UACN,KAAK,OAAO,WAAW;AAAA,UACvB;AAAA,UACA,QAAQ,QAAQC,UAAS,cAAc;AAAA,QACzC;AAAA,QACA,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAGP,WAAS,cAAc,MAAM;AAC3B;AAAA,MACE,SAAS,MAAM,OAAO,mBAAmB,IAAI;AAAA,MAC7C;AAAA,IACF;AAEA,QAAI,SAAS,MAAM,KAAK;AACtB,cAAQ,QAAQ,IAAI;AACpB;AAAA,IACF;AAEA,YAAQ,MAAM,MAAM,eAAe;AACnC,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,eAAe;AAClC,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAGA,WAAS,eAAe,MAAM;AAC5B;AAAA,MACE,SAAS,MAAM,OAAO,mBAAmB,IAAI;AAAA,MAC7C;AAAA,IACF;AAEA,QAAI,SAAS,MAAM,KAAK;AACtB,cAAQ,QAAQ,IAAI;AACpB;AAAA,IACF;AAEA,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AACF;;;ACvEO,IAAM,WAAW,EAAC,YAAY,eAAe,EAAC;AAC9C,IAAM,SAAS,kBAAkB,QAAQ;AACzC,IAAM,OAAO,kBAAkB,MAAM;AAQ5C,SAAS,kBAAkB,OAAO;AAChC,SAAO;AAAA,IACL,YAAY;AAAA,MACV,UAAU,SAAS,yBAAyB;AAAA,IAC9C;AAAA,IACA,UAAU;AAAA,EACZ;AAOA,WAAS,eAAe,SAAS;AAC/B,UAAM,OAAO;AACb,UAAMC,cAAa,KAAK,OAAO,WAAW,KAAK;AAC/C,UAAMC,QAAO,QAAQ,QAAQD,aAAY,OAAO,OAAO;AAEvD,WAAO;AAGP,aAAS,MAAM,MAAM;AACnB,aAAO,QAAQ,IAAI,IAAIC,MAAK,IAAI,IAAI,QAAQ,IAAI;AAAA,IAClD;AAGA,aAAS,QAAQ,MAAM;AACrB,UAAI,SAAS,MAAM,KAAK;AACtB,gBAAQ,QAAQ,IAAI;AACpB;AAAA,MACF;AAEA,cAAQ,MAAM,MAAM,IAAI;AACxB,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAGA,aAAS,KAAK,MAAM;AAClB,UAAI,QAAQ,IAAI,GAAG;AACjB,gBAAQ,KAAK,MAAM,IAAI;AACvB,eAAOA,MAAK,IAAI;AAAA,MAClB;AAGA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAQA,aAAS,QAAQ,MAAM;AACrB,UAAI,SAAS,MAAM,KAAK;AACtB,eAAO;AAAA,MACT;AAEA,YAAMC,QAAOF,YAAW,IAAI;AAC5B,UAAIG,SAAQ;AAEZ,UAAID,OAAM;AAER,WAAO,MAAM,QAAQA,KAAI,GAAG,yCAAyC;AAErE,eAAO,EAAEC,SAAQD,MAAK,QAAQ;AAC5B,gBAAM,OAAOA,MAAKC,MAAK;AACvB,cAAI,CAAC,KAAK,YAAY,KAAK,SAAS,KAAK,MAAM,KAAK,QAAQ,GAAG;AAC7D,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAQA,SAAS,eAAe,eAAe;AACrC,SAAO;AAGP,WAAS,eAAe,QAAQ,SAAS;AACvC,QAAIA,SAAQ;AAEZ,QAAI;AAIJ,WAAO,EAAEA,UAAS,OAAO,QAAQ;AAC/B,UAAI,UAAU,QAAW;AACvB,YAAI,OAAOA,MAAK,KAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,MAAM;AACzD,kBAAQA;AACR,UAAAA;AAAA,QACF;AAAA,MACF,WAAW,CAAC,OAAOA,MAAK,KAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,MAAM;AAEjE,YAAIA,WAAU,QAAQ,GAAG;AACvB,iBAAO,KAAK,EAAE,CAAC,EAAE,MAAM,OAAOA,SAAQ,CAAC,EAAE,CAAC,EAAE;AAC5C,iBAAO,OAAO,QAAQ,GAAGA,SAAQ,QAAQ,CAAC;AAC1C,UAAAA,SAAQ,QAAQ;AAAA,QAClB;AAEA,gBAAQ;AAAA,MACV;AAAA,IACF;AAEA,WAAO,gBAAgB,cAAc,QAAQ,OAAO,IAAI;AAAA,EAC1D;AACF;AAaA,SAAS,uBAAuB,QAAQ,SAAS;AAC/C,MAAI,aAAa;AAEjB,SAAO,EAAE,cAAc,OAAO,QAAQ;AACpC,SACG,eAAe,OAAO,UACrB,OAAO,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,eACvC,OAAO,aAAa,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM,MACzC;AACA,YAAM,OAAO,OAAO,aAAa,CAAC,EAAE,CAAC;AACrC,YAAM,SAAS,QAAQ,YAAY,IAAI;AACvC,UAAIA,SAAQ,OAAO;AACnB,UAAI,cAAc;AAClB,UAAI,OAAO;AAEX,UAAI;AAEJ,aAAOA,UAAS;AACd,cAAM,QAAQ,OAAOA,MAAK;AAE1B,YAAI,OAAO,UAAU,UAAU;AAC7B,wBAAc,MAAM;AAEpB,iBAAO,MAAM,WAAW,cAAc,CAAC,MAAM,MAAM,OAAO;AACxD;AACA;AAAA,UACF;AAEA,cAAI,YAAa;AACjB,wBAAc;AAAA,QAChB,WAES,UAAU,MAAM,eAAe;AACtC,iBAAO;AACP;AAAA,QACF,WAAW,UAAU,MAAM,cAAc;AAAA,QAEzC,OAAO;AAEL,UAAAA;AACA;AAAA,QACF;AAAA,MACF;AAGA,UAAI,QAAQ,4BAA4B,eAAe,OAAO,QAAQ;AACpE,eAAO;AAAA,MACT;AAEA,UAAI,MAAM;AACR,cAAM,QAAQ;AAAA,UACZ,MACE,eAAe,OAAO,UACtB,QACA,OAAO,UAAU,yBACb,MAAM,aACN,MAAM;AAAA,UACZ,OAAO;AAAA,YACL,cAAcA,SACV,cACA,KAAK,MAAM,eAAe;AAAA,YAC9B,QAAQ,KAAK,MAAM,SAASA;AAAA,YAC5B,MAAM,KAAK,IAAI;AAAA,YACf,QAAQ,KAAK,IAAI,SAAS;AAAA,YAC1B,QAAQ,KAAK,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,KAAK,EAAC,GAAG,KAAK,IAAG;AAAA,QACnB;AAEA,aAAK,MAAM,EAAC,GAAG,MAAM,MAAK;AAE1B,YAAI,KAAK,MAAM,WAAW,KAAK,IAAI,QAAQ;AACzC,iBAAO,OAAO,MAAM,KAAK;AAAA,QAC3B,OAAO;AACL,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA,CAAC,SAAS,OAAO,OAAO;AAAA,YACxB,CAAC,QAAQ,OAAO,OAAO;AAAA,UACzB;AACA,wBAAc;AAAA,QAChB;AAAA,MACF;AAEA;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACnPA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA,YAAAC;AAAA;AA8BO,IAAMC,YAAW;AAAA,EACtB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,IAAI,GAAG;AAAA,EACd,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,WAAW,GAAG;AACvB;AAGO,IAAM,iBAAiB;AAAA,EAC5B,CAAC,MAAM,iBAAiB,GAAG;AAC7B;AAGO,IAAM,cAAc;AAAA,EACzB,CAAC,MAAM,aAAa,GAAG;AAAA,EACvB,CAAC,MAAM,YAAY,GAAG;AAAA,EACtB,CAAC,MAAM,KAAK,GAAG;AACjB;AAGO,IAAMC,QAAO;AAAA,EAClB,CAAC,MAAM,UAAU,GAAG;AAAA,EACpB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,IAAI,GAAG,CAAC,iBAAiB,aAAa;AAAA,EAC7C,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,UAAU,GAAG;AAAA,EACpB,CAAC,MAAM,WAAW,GAAG;AAAA,EACrB,CAAC,MAAM,KAAK,GAAG;AACjB;AAGO,IAAMC,UAAS;AAAA,EACpB,CAAC,MAAM,SAAS,GAAG;AAAA,EACnB,CAAC,MAAM,SAAS,GAAG;AACrB;AAGO,IAAMC,QAAO;AAAA,EAClB,CAAC,MAAM,cAAc,GAAG;AAAA,EACxB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,sBAAsB,GAAG;AAAA,EAChC,CAAC,MAAM,eAAe,GAAG;AAAA,EACzB,CAAC,MAAM,SAAS,GAAG;AAAA,EACnB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,QAAQ,GAAG,CAAC,UAAU,QAAQ;AAAA,EACrC,CAAC,MAAM,iBAAiB,GAAG;AAAA,EAC3B,CAAC,MAAM,SAAS,GAAG,CAAC,iBAAiB,eAAe;AAAA,EACpD,CAAC,MAAM,kBAAkB,GAAG;AAAA,EAC5B,CAAC,MAAM,UAAU,GAAG;AAAA,EACpB,CAAC,MAAM,WAAW,GAAG;AACvB;AAGO,IAAM,aAAa,EAAC,MAAM,CAAC,WAAW,QAAW,EAAC;AAGlD,IAAM,mBAAmB,EAAC,MAAM,CAAC,MAAM,UAAU,MAAM,UAAU,EAAC;AAGlE,IAAM,UAAU,EAAC,MAAM,CAAC,EAAC;;;AC7DhC,mBAAwB;AAOxB,IAAM,YAAQ,aAAAC,SAAY,WAAW;AAoB9B,SAAS,gBAAgB,QAAQ,YAAY,MAAM;AAExD,MAAIC,SAAQ;AAAA,IACV,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,MAAO,QAAQ,KAAK,QAAS;AAAA,IAC7B,QAAS,QAAQ,KAAK,UAAW;AAAA,IACjC,QAAS,QAAQ,KAAK,UAAW;AAAA,EACnC;AAEA,QAAM,cAAc,CAAC;AAErB,QAAM,uBAAuB,CAAC;AAE9B,MAAI,SAAS,CAAC;AAEd,MAAI,QAAQ,CAAC;AAEb,MAAI,WAAW;AAOf,QAAM,UAAU;AAAA,IACd,SAAS,iBAAiB,qBAAqB;AAAA,IAC/C,OAAO,iBAAiB,iBAAiB;AAAA,IACzC;AAAA,IACA;AAAA,IACA,MAAAC;AAAA,IACA,WAAW,iBAAiB,mBAAmB,EAAC,WAAW,KAAI,CAAC;AAAA,EAClE;AAOA,QAAM,UAAU;AAAA,IACd,MAAM,MAAM;AAAA,IACZ,gBAAgB,CAAC;AAAA,IACjB;AAAA,IACA,QAAQ,CAAC;AAAA,IACT;AAAA,IACA;AAAA,IACA,UAAU,MAAM;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAOA,MAAI,QAAQ,WAAW,SAAS,KAAK,SAAS,OAAO;AAOrD,MAAI;AAEJ,MAAI,WAAW,YAAY;AACzB,yBAAqB,KAAK,UAAU;AAAA,EACtC;AAEA,SAAO;AAGP,WAAS,MAAM,OAAO;AACpB,aAAS,KAAK,QAAQ,KAAK;AAE3B,SAAK;AAGL,QAAI,OAAO,OAAO,SAAS,CAAC,MAAM,MAAM,KAAK;AAC3C,aAAO,CAAC;AAAA,IACV;AAEA,cAAU,YAAY,CAAC;AAGvB,YAAQ,SAAS,WAAW,sBAAsB,QAAQ,QAAQ,OAAO;AAEzE,WAAO,QAAQ;AAAA,EACjB;AAOA,WAAS,eAAe,OAAO,YAAY;AACzC,WAAO,gBAAgB,YAAY,KAAK,GAAG,UAAU;AAAA,EACvD;AAGA,WAAS,YAAY,OAAO;AAC1B,WAAO,YAAY,QAAQ,KAAK;AAAA,EAClC;AAGA,WAAS,MAAM;AAEb,UAAM,EAAC,cAAc,QAAQ,MAAM,QAAQ,OAAM,IAAID;AACrD,WAAO,EAAC,cAAc,QAAQ,MAAM,QAAQ,OAAM;AAAA,EACpD;AAGA,WAAS,WAAW,OAAO;AACzB,gBAAY,MAAM,IAAI,IAAI,MAAM;AAChC,4BAAwB;AACxB,UAAM,+BAA+BA,MAAK;AAAA,EAC5C;AAiBA,WAAS,OAAO;AAEd,QAAI;AAEJ,WAAOA,OAAM,SAAS,OAAO,QAAQ;AACnC,YAAM,QAAQ,OAAOA,OAAM,MAAM;AAGjC,UAAI,OAAO,UAAU,UAAU;AAC7B,qBAAaA,OAAM;AAEnB,YAAIA,OAAM,eAAe,GAAG;AAC1B,UAAAA,OAAM,eAAe;AAAA,QACvB;AAEA,eACEA,OAAM,WAAW,cACjBA,OAAM,eAAe,MAAM,QAC3B;AACA,aAAG,MAAM,WAAWA,OAAM,YAAY,CAAC;AAAA,QACzC;AAAA,MACF,OAAO;AACL,WAAG,KAAK;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAUA,WAAS,GAAG,MAAM;AAChB,OAAO,aAAa,MAAM,mCAAmC;AAC7D,eAAW;AACX,UAAM,4BAA4B,MAAM,SAAS,MAAM,IAAI;AAC3D,mBAAe;AACf,OAAO,OAAO,UAAU,YAAY,gBAAgB;AACpD,YAAQ,MAAM,IAAI;AAAA,EACpB;AAGA,WAAS,QAAQ,MAAM;AACrB,OAAO,SAAS,cAAc,4CAA4C;AAE1E,UAAM,iBAAiB,IAAI;AAE3B;AAAA,MACE,aAAa;AAAA,MACb;AAAA,IACF;AACA;AAAA,MACE,SAAS,OACL,QAAQ,OAAO,WAAW,KACxB,QAAQ,OAAO,QAAQ,OAAO,SAAS,CAAC,EAAE,CAAC,MAAM,SACnD,QAAQ,OAAO,QAAQ,OAAO,SAAS,CAAC,EAAE,CAAC,MAAM;AAAA,MACrD;AAAA,IACF;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,MAAAA,OAAM;AACN,MAAAA,OAAM,SAAS;AACf,MAAAA,OAAM,UAAU,SAAS,MAAM,yBAAyB,IAAI;AAC5D,8BAAwB;AACxB,YAAM,6BAA6BA,MAAK;AAAA,IAC1C,WAAW,SAAS,MAAM,cAAc;AACtC,MAAAA,OAAM;AACN,MAAAA,OAAM;AAAA,IACR;AAGA,QAAIA,OAAM,eAAe,GAAG;AAC1B,MAAAA,OAAM;AAAA,IACR,OAAO;AACL,MAAAA,OAAM;AAGN,UACEA,OAAM;AAAA;AAAA;AAAA,MAGiB,OAAOA,OAAM,MAAM,EAAG,QAC7C;AACA,QAAAA,OAAM,eAAe;AACrB,QAAAA,OAAM;AAAA,MACR;AAAA,IACF;AAGA,YAAQ,WAAW;AAGnB,eAAW;AAAA,EACb;AAGA,WAAS,MAAM,MAAM,QAAQ;AAG3B,UAAM,QAAQ,UAAU,CAAC;AACzB,UAAM,OAAO;AACb,UAAM,QAAQ,IAAI;AAElB,OAAO,OAAO,SAAS,UAAU,sBAAsB;AACvD,OAAO,KAAK,SAAS,GAAG,2BAA2B;AACnD,UAAM,eAAe,IAAI;AAEzB,YAAQ,OAAO,KAAK,CAAC,SAAS,OAAO,OAAO,CAAC;AAE7C,UAAM,KAAK,KAAK;AAEhB,WAAO;AAAA,EACT;AAGA,WAASC,MAAK,MAAM;AAClB,OAAO,OAAO,SAAS,UAAU,sBAAsB;AACvD,OAAO,KAAK,SAAS,GAAG,2BAA2B;AAEnD,UAAM,QAAQ,MAAM,IAAI;AACxB,OAAO,OAAO,8BAA8B;AAC5C,UAAM,MAAM,IAAI;AAEhB,OAAO,SAAS,MAAM,MAAM,4CAA4C;AAExE;AAAA,MACE,EACE,MAAM,MAAM,WAAW,MAAM,IAAI,UACjC,MAAM,MAAM,iBAAiB,MAAM,IAAI;AAAA,MAEzC,gCAAgC,OAAO;AAAA,IACzC;AAEA,UAAM,cAAc,MAAM,IAAI;AAC9B,YAAQ,OAAO,KAAK,CAAC,QAAQ,OAAO,OAAO,CAAC;AAE5C,WAAO;AAAA,EACT;AAOA,WAAS,sBAAsB,WAAW,MAAM;AAC9C,cAAU,WAAW,KAAK,IAAI;AAAA,EAChC;AAOA,WAAS,kBAAkB,GAAG,MAAM;AAClC,SAAK,QAAQ;AAAA,EACf;AAUA,WAAS,iBAAiB,UAAU,QAAQ;AAC1C,WAAO;AAeP,aAAS,KAAKC,aAAY,aAAa,YAAY;AAEjD,UAAI;AAEJ,UAAI;AAEJ,UAAI;AAEJ,UAAI;AAEJ,aAAO,MAAM,QAAQA,WAAU;AAAA;AAAA,QAE3B,uBAAuBA,WAAU;AAAA,UACjC,cAAcA;AAAA;AAAA,QAEZ,uBAAuB;AAAA;AAAA,UAA2BA;AAAA,QAAW,CAAC;AAAA,UAC9D,sBAAsBA,WAAU;AAUtC,eAAS,sBAAsB,KAAK;AAClC,eAAO;AAGP,iBAAS,MAAM,MAAM;AACnB,gBAAM,OAAO,SAAS,QAAQ,IAAI,IAAI;AACtC,gBAAMC,OAAM,SAAS,QAAQ,IAAI;AACjC,gBAAMC,QAAO;AAAA;AAAA;AAAA,YAGX,GAAI,MAAM,QAAQ,IAAI,IAAI,OAAO,OAAO,CAAC,IAAI,IAAI,CAAC;AAAA,YAClD,GAAI,MAAM,QAAQD,IAAG,IAAIA,OAAMA,OAAM,CAACA,IAAG,IAAI,CAAC;AAAA,UAChD;AAEA,iBAAO,uBAAuBC,KAAI,EAAE,IAAI;AAAA,QAC1C;AAAA,MACF;AAUA,eAAS,uBAAuBA,OAAM;AACpC,2BAAmBA;AACnB,yBAAiB;AAEjB,YAAIA,MAAK,WAAW,GAAG;AACrB,aAAO,YAAY,mCAAmC;AACtD,iBAAO;AAAA,QACT;AAEA,eAAO,gBAAgBA,MAAK,cAAc,CAAC;AAAA,MAC7C;AAUA,eAAS,gBAAgB,WAAW;AAClC,eAAO;AAGP,iBAAS,MAAM,MAAM;AAKnB,iBAAO,MAAM;AACb,6BAAmB;AAEnB,cAAI,CAAC,UAAU,SAAS;AACtB,oBAAQ,mBAAmB;AAAA,UAC7B;AAGA;AAAA,YACE,QAAQ,OAAO,WAAW,QAAQ;AAAA,YAClC;AAAA,UACF;AAEA,cACE,UAAU,QACV,QAAQ,OAAO,WAAW,QAAQ,KAAK,SAAS,UAAU,IAAI,GAC9D;AACA,mBAAO,IAAI,IAAI;AAAA,UACjB;AAEA,iBAAO,UAAU,SAAS;AAAA;AAAA;AAAA;AAAA,YAIxB,SAAS,OAAO,OAAO,OAAO,OAAO,OAAO,GAAG,MAAM,IAAI;AAAA,YACzD;AAAA,YACAC;AAAA,YACA;AAAA,UACF,EAAE,IAAI;AAAA,QACR;AAAA,MACF;AAGA,eAASA,IAAG,MAAM;AAChB,WAAO,SAAS,cAAc,eAAe;AAC7C,mBAAW;AACX,iBAAS,kBAAkB,IAAI;AAC/B,eAAO;AAAA,MACT;AAGA,eAAS,IAAI,MAAM;AACjB,WAAO,SAAS,cAAc,eAAe;AAC7C,mBAAW;AACX,aAAK,QAAQ;AAEb,YAAI,EAAE,iBAAiB,iBAAiB,QAAQ;AAC9C,iBAAO,gBAAgB,iBAAiB,cAAc,CAAC;AAAA,QACzD;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAUA,WAAS,UAAU,WAAWC,OAAM;AAClC,QAAI,UAAU,cAAc,CAAC,qBAAqB,SAAS,SAAS,GAAG;AACrE,2BAAqB,KAAK,SAAS;AAAA,IACrC;AAEA,QAAI,UAAU,SAAS;AACrB;AAAA,QACE,QAAQ;AAAA,QACRA;AAAA,QACA,QAAQ,OAAO,SAASA;AAAA,QACxB,UAAU,QAAQ,QAAQ,OAAO,MAAMA,KAAI,GAAG,OAAO;AAAA,MACvD;AAAA,IACF;AAEA,QAAI,UAAU,WAAW;AACvB,cAAQ,SAAS,UAAU,UAAU,QAAQ,QAAQ,OAAO;AAAA,IAC9D;AAEA;AAAA,MACE,UAAU,WACR,QAAQ,OAAO,WAAW,KAC1B,QAAQ,OAAO,QAAQ,OAAO,SAAS,CAAC,EAAE,CAAC,MAAM;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAQA,WAAS,QAAQ;AACf,UAAM,aAAa,IAAI;AACvB,UAAM,gBAAgB,QAAQ;AAC9B,UAAM,wBAAwB,QAAQ;AACtC,UAAM,mBAAmB,QAAQ,OAAO;AACxC,UAAM,aAAa,MAAM,KAAK,KAAK;AAEnC,WAAO,EAAC,MAAM,kBAAkB,QAAO;AAQvC,aAAS,UAAU;AACjB,MAAAN,SAAQ;AACR,cAAQ,WAAW;AACnB,cAAQ,mBAAmB;AAC3B,cAAQ,OAAO,SAAS;AACxB,cAAQ;AACR,8BAAwB;AACxB,YAAM,2BAA2BA,MAAK;AAAA,IACxC;AAAA,EACF;AASA,WAAS,0BAA0B;AACjC,QAAIA,OAAM,QAAQ,eAAeA,OAAM,SAAS,GAAG;AACjD,MAAAA,OAAM,SAAS,YAAYA,OAAM,IAAI;AACrC,MAAAA,OAAM,UAAU,YAAYA,OAAM,IAAI,IAAI;AAAA,IAC5C;AAAA,EACF;AACF;AAYA,SAAS,YAAY,QAAQ,OAAO;AAClC,QAAM,aAAa,MAAM,MAAM;AAC/B,QAAM,mBAAmB,MAAM,MAAM;AACrC,QAAM,WAAW,MAAM,IAAI;AAC3B,QAAM,iBAAiB,MAAM,IAAI;AAEjC,MAAI;AAEJ,MAAI,eAAe,UAAU;AAC3B,OAAO,iBAAiB,IAAI,wCAAwC;AACpE,OAAO,mBAAmB,IAAI,0CAA0C;AAExE,WAAO,CAAC,OAAO,UAAU,EAAE,MAAM,kBAAkB,cAAc,CAAC;AAAA,EACpE,OAAO;AACL,WAAO,OAAO,MAAM,YAAY,QAAQ;AAExC,QAAI,mBAAmB,IAAI;AACzB,YAAM,OAAO,KAAK,CAAC;AACnB,UAAI,OAAO,SAAS,UAAU;AAC5B,aAAK,CAAC,IAAI,KAAK,MAAM,gBAAgB;AAAA,MAEvC,OAAO;AACL,WAAO,qBAAqB,GAAG,uCAAuC;AACtE,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAEA,QAAI,iBAAiB,GAAG;AAEtB,WAAK,KAAK,OAAO,QAAQ,EAAE,MAAM,GAAG,cAAc,CAAC;AAAA,IACrD;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,gBAAgB,QAAQ,YAAY;AAC3C,MAAIO,SAAQ;AAEZ,QAAM,SAAS,CAAC;AAEhB,MAAI;AAEJ,SAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,UAAM,QAAQ,OAAOA,MAAK;AAE1B,QAAI;AAEJ,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ;AAAA,IACV;AACE,cAAQ,OAAO;AAAA,QACb,KAAK,MAAM,gBAAgB;AACzB,kBAAQ,OAAO;AAEf;AAAA,QACF;AAAA,QAEA,KAAK,MAAM,UAAU;AACnB,kBAAQ,OAAO;AAEf;AAAA,QACF;AAAA,QAEA,KAAK,MAAM,wBAAwB;AACjC,kBAAQ,OAAO,KAAK,OAAO;AAE3B;AAAA,QACF;AAAA,QAEA,KAAK,MAAM,eAAe;AACxB,kBAAQ,aAAa,OAAO,QAAQ,OAAO;AAE3C;AAAA,QACF;AAAA,QAEA,KAAK,MAAM,cAAc;AACvB,cAAI,CAAC,cAAc,MAAO;AAC1B,kBAAQ,OAAO;AAEf;AAAA,QACF;AAAA,QAEA,SAAS;AACP,aAAO,OAAO,UAAU,UAAU,iBAAiB;AAEnD,kBAAQ,OAAO,aAAa,KAAK;AAAA,QACnC;AAAA,MACF;AAEF,YAAQ,UAAU,MAAM;AACxB,WAAO,KAAK,KAAK;AAAA,EACnB;AAEA,SAAO,OAAO,KAAK,EAAE;AACvB;;;ACprBO,SAAS,MAAM,SAAS;AAC7B,QAAM,WAAW,WAAW,CAAC;AAC7B,QAAMC;AAAA;AAAA,IACJ,kBAAkB,CAAC,oBAAmB,GAAI,SAAS,cAAc,CAAC,CAAE,CAAC;AAAA;AAIvE,QAAM,SAAS;AAAA,IACb,YAAAA;AAAA,IACA,SAAS,OAAO,OAAO;AAAA,IACvB,SAAS,CAAC;AAAA,IACV,UAAU,OAAOC,SAAQ;AAAA,IACzB,MAAM,OAAO,IAAI;AAAA,IACjB,MAAM,CAAC;AAAA,IACP,QAAQ,OAAO,MAAM;AAAA,IACrB,MAAM,OAAO,IAAI;AAAA,EACnB;AAEA,SAAO;AAQP,WAAS,OAAO,SAAS;AACvB,WAAO;AAEP,aAAS,QAAQ,MAAM;AACrB,aAAO,gBAAgB,QAAQ,SAAS,IAAI;AAAA,IAC9C;AAAA,EACF;AACF;;;AC7CO,SAAS,YAAY,QAAQ;AAClC,SAAO,CAAC,YAAY,MAAM,GAAG;AAAA,EAE7B;AAEA,SAAO;AACT;;;ACCA,IAAM,SAAS;AAMR,SAAS,aAAa;AAC3B,MAAI,SAAS;AACb,MAAI,SAAS;AAEb,MAAI,QAAQ;AAEZ,MAAI;AAEJ,SAAO;AAIP,WAAS,aAAa,OAAO,UAAU,KAAK;AAE1C,UAAM,SAAS,CAAC;AAEhB,QAAI;AAEJ,QAAI;AAEJ,QAAI;AAEJ,QAAI;AAEJ,QAAI;AAEJ,YACE,UACC,OAAO,UAAU,WACd,MAAM,SAAS,IACf,IAAI,YAAY,YAAY,MAAS,EAAE,OAAO,KAAK;AAEzD,oBAAgB;AAChB,aAAS;AAET,QAAI,OAAO;AAET,UAAI,MAAM,WAAW,CAAC,MAAM,MAAM,iBAAiB;AACjD;AAAA,MACF;AAEA,cAAQ;AAAA,IACV;AAEA,WAAO,gBAAgB,MAAM,QAAQ;AACnC,aAAO,YAAY;AACnB,cAAQ,OAAO,KAAK,KAAK;AACzB,oBACE,SAAS,MAAM,UAAU,SAAY,MAAM,QAAQ,MAAM;AAC3D,aAAO,MAAM,WAAW,WAAW;AAEnC,UAAI,CAAC,OAAO;AACV,iBAAS,MAAM,MAAM,aAAa;AAClC;AAAA,MACF;AAEA,UACE,SAAS,MAAM,MACf,kBAAkB,eAClB,kBACA;AACA,eAAO,KAAK,MAAM,sBAAsB;AACxC,2BAAmB;AAAA,MACrB,OAAO;AACL,YAAI,kBAAkB;AACpB,iBAAO,KAAK,MAAM,cAAc;AAChC,6BAAmB;AAAA,QACrB;AAEA,YAAI,gBAAgB,aAAa;AAC/B,iBAAO,KAAK,MAAM,MAAM,eAAe,WAAW,CAAC;AACnD,oBAAU,cAAc;AAAA,QAC1B;AAEA,gBAAQ,MAAM;AAAA,UACZ,KAAK,MAAM,KAAK;AACd,mBAAO,KAAK,MAAM,oBAAoB;AACtC;AAEA;AAAA,UACF;AAAA,UAEA,KAAK,MAAM,IAAI;AACb,mBAAO,KAAK,KAAK,SAAS,UAAU,OAAO,IAAI,UAAU;AACzD,mBAAO,KAAK,MAAM,aAAa;AAC/B,mBAAO,WAAW,KAAM,QAAO,KAAK,MAAM,YAAY;AAEtD;AAAA,UACF;AAAA,UAEA,KAAK,MAAM,IAAI;AACb,mBAAO,KAAK,MAAM,QAAQ;AAC1B,qBAAS;AAET;AAAA,UACF;AAAA,UAEA,SAAS;AACP,+BAAmB;AACnB,qBAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAEA,sBAAgB,cAAc;AAAA,IAChC;AAEA,QAAI,KAAK;AACP,UAAI,iBAAkB,QAAO,KAAK,MAAM,cAAc;AACtD,UAAI,OAAQ,QAAO,KAAK,MAAM;AAC9B,aAAO,KAAK,MAAM,GAAG;AAAA,IACvB;AAEA,WAAO;AAAA,EACT;AACF;;;ACxIA,IAAM,6BACJ;AAcK,SAAS,aAAa,OAAO;AAClC,SAAO,MAAM,QAAQ,4BAA4B,MAAM;AACzD;AAYA,SAAS,OAAO,IAAI,IAAI,IAAI;AAC1B,MAAI,IAAI;AAEN,WAAO;AAAA,EACT;AAGA,QAAM,OAAO,GAAG,WAAW,CAAC;AAE5B,MAAI,SAAS,MAAM,YAAY;AAC7B,UAAMC,QAAO,GAAG,WAAW,CAAC;AAC5B,UAAM,MAAMA,UAAS,MAAM,cAAcA,UAAS,MAAM;AACxD,WAAO;AAAA,MACL,GAAG,MAAM,MAAM,IAAI,CAAC;AAAA,MACpB,MAAM,UAAU,yBAAyB,UAAU;AAAA,IACrD;AAAA,EACF;AAEA,SAAO,8BAA8B,EAAE,KAAK;AAC9C;;;AChBO,SAAS,kBAAkB,OAAO;AAEvC,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,WAAO;AAAA,EACT;AAGA,MAAI,cAAc,SAAS,UAAU,OAAO;AAC1C,WAAO,SAAS,MAAM,QAAQ;AAAA,EAChC;AAGA,MAAI,WAAW,SAAS,SAAS,OAAO;AACtC,WAAO,SAAS,KAAK;AAAA,EACvB;AAGA,MAAI,UAAU,SAAS,YAAY,OAAO;AACxC,WAAO,MAAM,KAAK;AAAA,EACpB;AAGA,SAAO;AACT;AAMA,SAAS,MAAMC,QAAO;AACpB,SAAO,MAAMA,UAASA,OAAM,IAAI,IAAI,MAAM,MAAMA,UAASA,OAAM,MAAM;AACvE;AAMA,SAAS,SAAS,KAAK;AACrB,SAAO,MAAM,OAAO,IAAI,KAAK,IAAI,MAAM,MAAM,OAAO,IAAI,GAAG;AAC7D;AAMA,SAAS,MAAM,OAAO;AACpB,SAAO,SAAS,OAAO,UAAU,WAAW,QAAQ;AACtD;;;AChCA,IAAM,MAAM,CAAC,EAAE;AAyBR,SAAS,aAAa,OAAO,UAAU,SAAS;AACrD,MAAI,OAAO,aAAa,UAAU;AAChC,cAAU;AACV,eAAW;AAAA,EACb;AAEA,SAAO,SAAS,OAAO;AAAA,IACrB;AAAA,MACE,MAAM,OAAO,EACV,SAAS,EACT,MAAM,WAAW,EAAE,OAAO,UAAU,IAAI,CAAC;AAAA,IAC9C;AAAA,EACF;AACF;AAOA,SAAS,SAAS,SAAS;AAEzB,QAAM,SAAS;AAAA,IACb,YAAY,CAAC;AAAA,IACb,gBAAgB,CAAC,YAAY,YAAY,WAAW,aAAa,QAAQ;AAAA,IACzE,OAAO;AAAA,MACL,UAAU,OAAO,IAAI;AAAA,MACrB,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,YAAY,OAAO,OAAO;AAAA,MAC1B,YAAY,OAAOC,WAAU;AAAA,MAC7B,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,YAAY,OAAO,QAAQ;AAAA,MAC3B,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,cAAc,OAAO,UAAU,MAAM;AAAA,MACrC,UAAU,OAAOC,WAAU,MAAM;AAAA,MACjC,cAAc;AAAA,MACd,MAAM;AAAA,MACN,eAAe;AAAA,MACf,YAAY,OAAOC,WAAU;AAAA,MAC7B,6BAA6B;AAAA,MAC7B,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,UAAU,OAAO,QAAQ;AAAA,MACzB,iBAAiB,OAAO,SAAS;AAAA,MACjC,mBAAmB,OAAO,SAAS;AAAA,MACnC,UAAU,OAAO,MAAM,MAAM;AAAA,MAC7B,cAAc;AAAA,MACd,UAAU,OAAO,MAAM,MAAM;AAAA,MAC7B,cAAc;AAAA,MACd,OAAO,OAAO,KAAK;AAAA,MACnB,OAAO;AAAA,MACP,MAAM,OAAO,IAAI;AAAA,MACjB,UAAU,OAAO,QAAQ;AAAA,MACzB,eAAe;AAAA,MACf,aAAa,OAAOC,OAAM,kBAAkB;AAAA,MAC5C,eAAe,OAAOA,KAAI;AAAA,MAC1B,WAAW,OAAO,SAAS;AAAA,MAC3B,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,2BAA2B;AAAA,MAC3B,qBAAqB;AAAA,MACrB,eAAe,OAAO,OAAO;AAAA,MAC7B,QAAQ,OAAO,MAAM;AAAA,MACrB,eAAe,OAAOC,cAAa;AAAA,IACrC;AAAA,IACA,MAAM;AAAA,MACJ,YAAY,OAAO;AAAA,MACnB,oBAAoB;AAAA,MACpB,UAAU,OAAO;AAAA,MACjB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,YAAY,OAAO;AAAA,MACnB,sBAAsB;AAAA,MACtB,qCAAqC;AAAA,MACrC,iCAAiC;AAAA,MACjC,yBAAyB;AAAA,MACzB,oBAAoB;AAAA,MACpB,YAAY,OAAO,gBAAgB;AAAA,MACnC,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,eAAe;AAAA,MACf,cAAc,OAAO,kBAAkB;AAAA,MACvC,UAAU,OAAO,cAAc;AAAA,MAC/B,cAAc;AAAA,MACd,MAAM;AAAA,MACN,YAAY,OAAO;AAAA,MACnB,6BAA6B;AAAA,MAC7B,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,UAAU,OAAO;AAAA,MACjB,iBAAiB,OAAO,eAAe;AAAA,MACvC,mBAAmB,OAAO,eAAe;AAAA,MACzC,UAAU,OAAO,cAAc;AAAA,MAC/B,cAAc;AAAA,MACd,UAAU,OAAO,cAAc;AAAA,MAC/B,cAAc;AAAA,MACd,OAAO,OAAO,WAAW;AAAA,MACzB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,MAAM,OAAO,UAAU;AAAA,MACvB,UAAU,OAAO;AAAA,MACjB,aAAa,OAAO;AAAA,MACpB,eAAe,OAAO;AAAA,MACtB,WAAW,OAAO;AAAA,MAClB,iBAAiB;AAAA,MACjB,2BAA2B;AAAA,MAC3B,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,eAAe,OAAO,mBAAmB;AAAA,MACzC,2BAA2B;AAAA,MAC3B,mBAAmB;AAAA,MACnB,QAAQ,OAAO;AAAA,MACf,eAAe,OAAO;AAAA,IACxB;AAAA,EACF;AAEA,YAAU,SAAS,WAAW,CAAC,GAAG,mBAAmB,CAAC,CAAC;AAGvD,QAAM,OAAO,CAAC;AAEd,SAAOC;AAUP,WAASA,SAAQ,QAAQ;AAEvB,QAAI,OAAO,EAAC,MAAM,QAAQ,UAAU,CAAC,EAAC;AAEtC,UAAM,UAAU;AAAA,MACd,OAAO,CAAC,IAAI;AAAA,MACZ,YAAY,CAAC;AAAA,MACb;AAAA,MACA;AAAA,MACA,MAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,YAAY,CAAC;AACnB,QAAIC,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,OAAO,QAAQ;AAG9B,UACE,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,eAChC,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,eAChC;AACA,YAAI,OAAOA,MAAK,EAAE,CAAC,MAAM,SAAS;AAChC,oBAAU,KAAKA,MAAK;AAAA,QACtB,OAAO;AACL,gBAAM,OAAO,UAAU,IAAI;AAC3B,aAAO,OAAO,SAAS,UAAU,0BAA0B;AAC3D,UAAAA,SAAQ,YAAY,QAAQ,MAAMA,MAAK;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAEA,IAAAA,SAAQ;AAER,WAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,YAAM,UAAU,OAAO,OAAOA,MAAK,EAAE,CAAC,CAAC;AAEvC,UAAI,IAAI,KAAK,SAAS,OAAOA,MAAK,EAAE,CAAC,EAAE,IAAI,GAAG;AAC5C,gBAAQ,OAAOA,MAAK,EAAE,CAAC,EAAE,IAAI,EAAE;AAAA,UAC7B,OAAO;AAAA,YACL,EAAC,gBAAgB,OAAOA,MAAK,EAAE,CAAC,EAAE,eAAc;AAAA,YAChD;AAAA,UACF;AAAA,UACA,OAAOA,MAAK,EAAE,CAAC;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAGA,QAAI,QAAQ,WAAW,SAAS,GAAG;AACjC,YAAM,OAAO,QAAQ,WAAW,QAAQ,WAAW,SAAS,CAAC;AAC7D,YAAM,UAAU,KAAK,CAAC,KAAK;AAC3B,cAAQ,KAAK,SAAS,QAAW,KAAK,CAAC,CAAC;AAAA,IAC1C;AAGA,SAAK,WAAW;AAAA,MACd,OAAOC;AAAA,QACL,OAAO,SAAS,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAC;AAAA,MACzE;AAAA,MACA,KAAKA;AAAA,QACH,OAAO,SAAS,IACZ,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,MAC7B,EAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAC;AAAA,MACpC;AAAA,IACF;AAGA,IAAAD,SAAQ;AACR,WAAO,EAAEA,SAAQ,OAAO,WAAW,QAAQ;AACzC,aAAO,OAAO,WAAWA,MAAK,EAAE,IAAI,KAAK;AAAA,IAC3C;AAEA,WAAO;AAAA,EACT;AAQA,WAAS,YAAY,QAAQ,OAAO,QAAQ;AAC1C,QAAIA,SAAQ,QAAQ;AACpB,QAAI,mBAAmB;AACvB,QAAI,aAAa;AAEjB,QAAIE;AAEJ,QAAI;AAEJ,QAAI;AAEJ,QAAI;AAEJ,WAAO,EAAEF,UAAS,QAAQ;AACxB,YAAM,QAAQ,OAAOA,MAAK;AAE1B,cAAQ,MAAM,CAAC,EAAE,MAAM;AAAA,QACrB,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX,KAAK,MAAM,YAAY;AACrB,cAAI,MAAM,CAAC,MAAM,SAAS;AACxB;AAAA,UACF,OAAO;AACL;AAAA,UACF;AAEA,qBAAW;AAEX;AAAA,QACF;AAAA,QAEA,KAAK,MAAM,iBAAiB;AAC1B,cAAI,MAAM,CAAC,MAAM,SAAS;AACxB,gBACEE,aACA,CAAC,YACD,CAAC,oBACD,CAAC,qBACD;AACA,oCAAsBF;AAAA,YACxB;AAEA,uBAAW;AAAA,UACb;AAEA;AAAA,QACF;AAAA,QAEA,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX,KAAK,MAAM,0BAA0B;AAGnC;AAAA,QACF;AAAA,QAEA,SAAS;AACP,qBAAW;AAAA,QACb;AAAA,MACF;AAEA,UACG,CAAC,oBACA,MAAM,CAAC,MAAM,WACb,MAAM,CAAC,EAAE,SAAS,MAAM,kBACzB,qBAAqB,MACpB,MAAM,CAAC,MAAM,WACZ,MAAM,CAAC,EAAE,SAAS,MAAM,iBACvB,MAAM,CAAC,EAAE,SAAS,MAAM,cAC5B;AACA,YAAIE,WAAU;AACZ,cAAI,YAAYF;AAChB,sBAAY;AAEZ,iBAAO,aAAa;AAClB,kBAAM,YAAY,OAAO,SAAS;AAElC,gBACE,UAAU,CAAC,EAAE,SAAS,MAAM,cAC5B,UAAU,CAAC,EAAE,SAAS,MAAM,iBAC5B;AACA,kBAAI,UAAU,CAAC,MAAM,OAAQ;AAE7B,kBAAI,WAAW;AACb,uBAAO,SAAS,EAAE,CAAC,EAAE,OAAO,MAAM;AAClC,6BAAa;AAAA,cACf;AAEA,wBAAU,CAAC,EAAE,OAAO,MAAM;AAC1B,0BAAY;AAAA,YACd,WACE,UAAU,CAAC,EAAE,SAAS,MAAM,cAC5B,UAAU,CAAC,EAAE,SAAS,MAAM,oBAC5B,UAAU,CAAC,EAAE,SAAS,MAAM,8BAC5B,UAAU,CAAC,EAAE,SAAS,MAAM,oBAC5B,UAAU,CAAC,EAAE,SAAS,MAAM,gBAC5B;AAAA,YAEF,OAAO;AACL;AAAA,YACF;AAAA,UACF;AAEA,cACE,wBACC,CAAC,aAAa,sBAAsB,YACrC;AACA,YAAAE,UAAS,UAAU;AAAA,UACrB;AAGA,UAAAA,UAAS,MAAM,OAAO;AAAA,YACpB,CAAC;AAAA,YACD,YAAY,OAAO,SAAS,EAAE,CAAC,EAAE,QAAQ,MAAM,CAAC,EAAE;AAAA,UACpD;AAEA,iBAAO,OAAO,aAAaF,QAAO,GAAG,CAAC,QAAQE,WAAU,MAAM,CAAC,CAAC,CAAC;AACjE,UAAAF;AACA;AAAA,QACF;AAGA,YAAI,MAAM,CAAC,EAAE,SAAS,MAAM,gBAAgB;AAE1C,gBAAM,OAAO;AAAA,YACX,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,CAAC,EAAE,KAAK;AAAA;AAAA,YAEvC,KAAK;AAAA,UACP;AACA,UAAAE,YAAW;AACX,iBAAO,OAAOF,QAAO,GAAG,CAAC,SAAS,MAAM,MAAM,CAAC,CAAC,CAAC;AACjD,UAAAA;AACA;AACA,gCAAsB;AACtB,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAEA,WAAO,KAAK,EAAE,CAAC,EAAE,UAAU;AAC3B,WAAO;AAAA,EACT;AAYA,WAAS,OAAO,QAAQ,KAAK;AAC3B,WAAO;AAOP,aAAS,KAAK,OAAO;AACnB,YAAM,KAAK,MAAM,OAAO,KAAK,GAAG,KAAK;AACrC,UAAI,IAAK,KAAI,KAAK,MAAM,KAAK;AAAA,IAC/B;AAAA,EACF;AAKA,WAAS,SAAS;AAChB,SAAK,MAAM,KAAK,EAAC,MAAM,YAAY,UAAU,CAAC,EAAC,CAAC;AAAA,EAClD;AAKA,WAAS,MAAMG,OAAM,OAAO,cAAc;AACxC,UAAM,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC/C,OAAO,QAAQ,mBAAmB;AAClC,OAAO,cAAc,QAAQ,mBAAmB;AAEhD,UAAM,WAAW,OAAO;AACxB,aAAS,KAAKA,KAAI;AAClB,SAAK,MAAM,KAAKA,KAAI;AACpB,SAAK,WAAW,KAAK,CAAC,OAAO,gBAAgB,MAAS,CAAC;AACvD,IAAAA,MAAK,WAAW;AAAA,MACd,OAAOF,OAAM,MAAM,KAAK;AAAA;AAAA,MAExB,KAAK;AAAA,IACP;AAAA,EACF;AAUA,WAAS,OAAO,KAAK;AACnB,WAAO;AAOP,aAAS,MAAM,OAAO;AACpB,UAAI,IAAK,KAAI,KAAK,MAAM,KAAK;AAC7B,MAAAF,MAAK,KAAK,MAAM,KAAK;AAAA,IACvB;AAAA,EACF;AAKA,WAASA,MAAK,OAAO,aAAa;AAChC,UAAMI,QAAO,KAAK,MAAM,IAAI;AAC5B,OAAOA,OAAM,iBAAiB;AAC9B,UAAM,OAAO,KAAK,WAAW,IAAI;AAEjC,QAAI,CAAC,MAAM;AACT,YAAM,IAAI;AAAA,QACR,mBACE,MAAM,OACN,QACA,kBAAkB,EAAC,OAAO,MAAM,OAAO,KAAK,MAAM,IAAG,CAAC,IACtD;AAAA,MACJ;AAAA,IACF,WAAW,KAAK,CAAC,EAAE,SAAS,MAAM,MAAM;AACtC,UAAI,aAAa;AACf,oBAAY,KAAK,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,MACvC,OAAO;AACL,cAAM,UAAU,KAAK,CAAC,KAAK;AAC3B,gBAAQ,KAAK,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,MACnC;AAAA,IACF;AAEA,OAAOA,MAAK,SAAS,YAAY,8BAA8B;AAC/D,OAAOA,MAAK,UAAU,mCAAmC;AACzD,IAAAA,MAAK,SAAS,MAAMF,OAAM,MAAM,GAAG;AAAA,EACrC;AAKA,WAAS,SAAS;AAChB,WAAO,SAAS,KAAK,MAAM,IAAI,CAAC;AAAA,EAClC;AAUA,WAAS,qBAAqB;AAC5B,SAAK,KAAK,8BAA8B;AAAA,EAC1C;AAMA,WAAS,qBAAqB,OAAO;AACnC,QAAI,KAAK,KAAK,6BAA6B;AACzC,YAAM,WAAW,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACjD,SAAO,UAAU,yBAAyB;AAC1C,SAAO,SAAS,SAAS,QAAQ,wBAAwB;AACzD,eAAS,QAAQ,OAAO;AAAA,QACtB,KAAK,eAAe,KAAK;AAAA,QACzB,UAAU;AAAA,MACZ;AACA,WAAK,KAAK,8BAA8B;AAAA,IAC1C;AAAA,EACF;AAMA,WAAS,4BAA4B;AACnC,UAAMG,QAAO,KAAK,OAAO;AACzB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,QAAQ,wBAAwB;AACrD,IAAAA,MAAK,OAAOC;AAAA,EACd;AAMA,WAAS,4BAA4B;AACnC,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,QAAQ,wBAAwB;AACrD,IAAAA,MAAK,OAAOC;AAAA,EACd;AAMA,WAAS,wBAAwB;AAE/B,QAAI,KAAK,KAAK,eAAgB;AAC9B,SAAK,OAAO;AACZ,SAAK,KAAK,iBAAiB;AAAA,EAC7B;AAMA,WAAS,mBAAmB;AAC1B,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,QAAQ,wBAAwB;AAErD,IAAAA,MAAK,QAAQC,MAAK,QAAQ,4BAA4B,EAAE;AACxD,SAAK,KAAK,iBAAiB;AAAA,EAC7B;AAMA,WAAS,qBAAqB;AAC5B,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,QAAQ,wBAAwB;AAErD,IAAAA,MAAK,QAAQC,MAAK,QAAQ,gBAAgB,EAAE;AAAA,EAC9C;AAMA,WAAS,4BAA4B,OAAO;AAC1C,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,cAAc,8BAA8B;AAEjE,IAAAA,MAAK,QAAQ;AACb,IAAAA,MAAK,aAAa;AAAA,MAChB,KAAK,eAAe,KAAK;AAAA,IAC3B,EAAE,YAAY;AAAA,EAChB;AAMA,WAAS,8BAA8B;AACrC,UAAMC,QAAO,KAAK,OAAO;AACzB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,cAAc,8BAA8B;AAEjE,IAAAA,MAAK,QAAQC;AAAA,EACf;AAMA,WAAS,oCAAoC;AAC3C,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,cAAc,8BAA8B;AAEjE,IAAAA,MAAK,MAAMC;AAAA,EACb;AAMA,WAAS,yBAAyB,OAAO;AACvC,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,WAAW,2BAA2B;AAE3D,QAAI,CAACA,MAAK,OAAO;AACf,YAAM,QAAQ,KAAK,eAAe,KAAK,EAAE;AAEzC;AAAA,QACE,UAAU,KACR,UAAU,KACV,UAAU,KACV,UAAU,KACV,UAAU,KACV,UAAU;AAAA,QACZ;AAAA,MACF;AAEA,MAAAA,MAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAMA,WAAS,0BAA0B;AACjC,SAAK,KAAK,+BAA+B;AAAA,EAC3C;AAMA,WAAS,gCAAgC,OAAO;AAC9C,UAAMA,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,WAAW,2BAA2B;AAE3D,IAAAA,MAAK,QACH,KAAK,eAAe,KAAK,EAAE,YAAY,CAAC,MAAM,MAAM,WAAW,IAAI;AAAA,EACvE;AAMA,WAAS,sBAAsB;AAC7B,SAAK,KAAK,+BAA+B;AAAA,EAC3C;AAOA,WAAS,YAAY,OAAO;AAC1B,UAAMA,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAO,cAAcA,OAAM,0BAA0B;AAErD,UAAM,WAAWA,MAAK;AAEtB,QAAI,OAAO,SAAS,SAAS,SAAS,CAAC;AAEvC,QAAI,CAAC,QAAQ,KAAK,SAAS,QAAQ;AAEjC,aAAOE,MAAK;AACZ,WAAK,WAAW;AAAA,QACd,OAAOJ,OAAM,MAAM,KAAK;AAAA;AAAA,QAExB,KAAK;AAAA,MACP;AACA,eAAS,KAAK,IAAI;AAAA,IACpB;AAEA,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AAOA,WAAS,WAAW,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,IAAI;AAC5B,OAAO,MAAM,sCAAsC;AACnD,OAAO,WAAW,MAAM,yCAAyC;AACjE,OAAO,KAAK,UAAU,0CAA0C;AAChE,SAAK,SAAS,KAAK,eAAe,KAAK;AACvC,SAAK,SAAS,MAAMA,OAAM,MAAM,GAAG;AAAA,EACrC;AAOA,WAAS,iBAAiB,OAAO;AAC/B,UAAM,UAAU,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAChD,OAAO,SAAS,iBAAiB;AAGjC,QAAI,KAAK,KAAK,aAAa;AACzB,SAAO,cAAc,SAAS,mBAAmB;AACjD,YAAM,OAAO,QAAQ,SAAS,QAAQ,SAAS,SAAS,CAAC;AACzD,SAAO,KAAK,UAAU,2CAA2C;AACjE,WAAK,SAAS,MAAMA,OAAM,MAAM,GAAG;AACnC,WAAK,KAAK,cAAc;AACxB;AAAA,IACF;AAEA,QACE,CAAC,KAAK,KAAK,gCACX,OAAO,eAAe,SAAS,QAAQ,IAAI,GAC3C;AACA,kBAAY,KAAK,MAAM,KAAK;AAC5B,iBAAW,KAAK,MAAM,KAAK;AAAA,IAC7B;AAAA,EACF;AAOA,WAAS,kBAAkB;AACzB,SAAK,KAAK,cAAc;AAAA,EAC1B;AAOA,WAAS,iBAAiB;AACxB,UAAMG,QAAO,KAAK,OAAO;AACzB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,QAAQ,wBAAwB;AAErD,IAAAA,MAAK,QAAQC;AAAA,EACf;AAOA,WAAS,iBAAiB;AACxB,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,QAAQ,wBAAwB;AAErD,IAAAA,MAAK,QAAQC;AAAA,EACf;AAOA,WAAS,iBAAiB;AACxB,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,cAAc,+BAA+B;AAElE,IAAAA,MAAK,QAAQC;AAAA,EACf;AAOA,WAAS,aAAa;AACpB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,QAAQ,wBAAwB;AAMrD,QAAI,KAAK,KAAK,aAAa;AAEzB,YAAM,gBAAgB,KAAK,KAAK,iBAAiB;AAEjD,MAAAA,MAAK,QAAQ;AAEb,MAAAA,MAAK,gBAAgB;AAErB,aAAOA,MAAK;AACZ,aAAOA,MAAK;AAAA,IACd,OAAO;AAEL,aAAOA,MAAK;AAEZ,aAAOA,MAAK;AAAA,IACd;AAEA,SAAK,KAAK,gBAAgB;AAAA,EAC5B;AAOA,WAAS,cAAc;AACrB,UAAMA,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,SAAS,yBAAyB;AAMvD,QAAI,KAAK,KAAK,aAAa;AAEzB,YAAM,gBAAgB,KAAK,KAAK,iBAAiB;AAEjD,MAAAA,MAAK,QAAQ;AAEb,MAAAA,MAAK,gBAAgB;AAErB,aAAOA,MAAK;AACZ,aAAOA,MAAK;AAAA,IACd,OAAO;AAEL,aAAOA,MAAK;AAEZ,aAAOA,MAAK;AAAA,IACd;AAEA,SAAK,KAAK,gBAAgB;AAAA,EAC5B;AAOA,WAAS,gBAAgB,OAAO;AAC9B,UAAMG,UAAS,KAAK,eAAe,KAAK;AACxC,UAAM,WAAW,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACjD,OAAO,UAAU,4BAA4B;AAC7C;AAAA,MACE,SAAS,SAAS,WAAW,SAAS,SAAS;AAAA,MAC/C;AAAA,IACF;AAIA,aAAS,QAAQ,aAAaA,OAAM;AAEpC,aAAS,aAAa,oBAAoBA,OAAM,EAAE,YAAY;AAAA,EAChE;AAOA,WAAS,cAAc;AACrB,UAAM,WAAW,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACjD,OAAO,UAAU,wBAAwB;AACzC,OAAO,SAAS,SAAS,YAAY,4BAA4B;AACjE,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAMH,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC;AAAA,MACEA,MAAK,SAAS,WAAWA,MAAK,SAAS;AAAA,MACvC;AAAA,IACF;AAGA,SAAK,KAAK,cAAc;AAExB,QAAIA,MAAK,SAAS,QAAQ;AAExB,YAAM,WAAW,SAAS;AAE1B,MAAAA,MAAK,WAAW;AAAA,IAClB,OAAO;AACL,MAAAA,MAAK,MAAM;AAAA,IACb;AAAA,EACF;AAOA,WAAS,kCAAkC;AACzC,UAAMC,QAAO,KAAK,OAAO;AACzB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC;AAAA,MACEA,MAAK,SAAS,WAAWA,MAAK,SAAS;AAAA,MACvC;AAAA,IACF;AACA,IAAAA,MAAK,MAAMC;AAAA,EACb;AAOA,WAAS,4BAA4B;AACnC,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC;AAAA,MACEA,MAAK,SAAS,WAAWA,MAAK,SAAS;AAAA,MACvC;AAAA,IACF;AACA,IAAAA,MAAK,QAAQC;AAAA,EACf;AAOA,WAAS,iBAAiB;AACxB,SAAK,KAAK,cAAc;AAAA,EAC1B;AAOA,WAAS,mBAAmB;AAC1B,SAAK,KAAK,gBAAgB;AAAA,EAC5B;AAOA,WAAS,sBAAsB,OAAO;AACpC,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAMD,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC;AAAA,MACEA,MAAK,SAAS,WAAWA,MAAK,SAAS;AAAA,MACvC;AAAA,IACF;AAIA,IAAAA,MAAK,QAAQ;AAEb,IAAAA,MAAK,aAAa;AAAA,MAChB,KAAK,eAAe,KAAK;AAAA,IAC3B,EAAE,YAAY;AACd,SAAK,KAAK,gBAAgB;AAAA,EAC5B;AAOA,WAAS,+BAA+B,OAAO;AAC7C;AAAA,MACE,MAAM,SAAS,qCACb,MAAM,SAAS;AAAA,IACnB;AACA,SAAK,KAAK,yBAAyB,MAAM;AAAA,EAC3C;AAMA,WAAS,8BAA8B,OAAO;AAC5C,UAAMC,QAAO,KAAK,eAAe,KAAK;AACtC,UAAM,OAAO,KAAK,KAAK;AAEvB,QAAI;AAEJ,QAAI,MAAM;AACR,cAAQ;AAAA,QACNA;AAAA,QACA,SAAS,MAAM,kCACX,UAAU,qBACV,UAAU;AAAA,MAChB;AACA,WAAK,KAAK,yBAAyB;AAAA,IACrC,OAAO;AACL,YAAM,SAAS,8BAA8BA,KAAI;AACjD,SAAO,WAAW,OAAO,8BAA8B;AACvD,cAAQ;AAAA,IACV;AAEA,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,iBAAiB;AAC9B,OAAO,WAAW,MAAM,uBAAuB;AAC/C,SAAK,SAAS;AAAA,EAChB;AAMA,WAAS,yBAAyB,OAAO;AACvC,UAAM,OAAO,KAAK,MAAM,IAAI;AAC5B,OAAO,MAAM,iBAAiB;AAC9B,OAAO,KAAK,UAAU,0BAA0B;AAChD,SAAK,SAAS,MAAMH,OAAM,MAAM,GAAG;AAAA,EACrC;AAMA,WAAS,uBAAuB,OAAO;AACrC,eAAW,KAAK,MAAM,KAAK;AAC3B,UAAME,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,QAAQ,wBAAwB;AAErD,IAAAA,MAAK,MAAM,KAAK,eAAe,KAAK;AAAA,EACtC;AAMA,WAAS,oBAAoB,OAAO;AAClC,eAAW,KAAK,MAAM,KAAK;AAC3B,UAAMA,QAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAOA,OAAM,wBAAwB;AACrC,OAAOA,MAAK,SAAS,QAAQ,wBAAwB;AAErD,IAAAA,MAAK,MAAM,YAAY,KAAK,eAAe,KAAK;AAAA,EAClD;AAOA,WAASV,cAAa;AACpB,WAAO,EAAC,MAAM,cAAc,UAAU,CAAC,EAAC;AAAA,EAC1C;AAGA,WAAS,WAAW;AAClB,WAAO,EAAC,MAAM,QAAQ,MAAM,MAAM,MAAM,MAAM,OAAO,GAAE;AAAA,EACzD;AAGA,WAASC,YAAW;AAClB,WAAO,EAAC,MAAM,cAAc,OAAO,GAAE;AAAA,EACvC;AAGA,WAASC,cAAa;AACpB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,EACF;AAGA,WAAS,WAAW;AAClB,WAAO,EAAC,MAAM,YAAY,UAAU,CAAC,EAAC;AAAA,EACxC;AAGA,WAAS,UAAU;AACjB,WAAO;AAAA,MACL,MAAM;AAAA;AAAA,MAEN,OAAO;AAAA,MACP,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAGA,WAAS,YAAY;AACnB,WAAO,EAAC,MAAM,QAAO;AAAA,EACvB;AAGA,WAAS,OAAO;AACd,WAAO,EAAC,MAAM,QAAQ,OAAO,GAAE;AAAA,EACjC;AAGA,WAAS,QAAQ;AACf,WAAO,EAAC,MAAM,SAAS,OAAO,MAAM,KAAK,IAAI,KAAK,KAAI;AAAA,EACxD;AAGA,WAAS,OAAO;AACd,WAAO,EAAC,MAAM,QAAQ,OAAO,MAAM,KAAK,IAAI,UAAU,CAAC,EAAC;AAAA,EAC1D;AAMA,WAASC,MAAK,OAAO;AACnB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAM,SAAS;AAAA,MACxB,OAAO;AAAA,MACP,QAAQ,MAAM;AAAA,MACd,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAMA,WAAS,SAAS,OAAO;AACvB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,QAAQ,MAAM;AAAA,MACd,SAAS;AAAA,MACT,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAGA,WAAS,YAAY;AACnB,WAAO,EAAC,MAAM,aAAa,UAAU,CAAC,EAAC;AAAA,EACzC;AAGA,WAAS,SAAS;AAChB,WAAO,EAAC,MAAM,UAAU,UAAU,CAAC,EAAC;AAAA,EACtC;AAGA,WAASS,QAAO;AACd,WAAO,EAAC,MAAM,QAAQ,OAAO,GAAE;AAAA,EACjC;AAGA,WAASR,iBAAgB;AACvB,WAAO,EAAC,MAAM,gBAAe;AAAA,EAC/B;AACF;AAUA,SAASI,OAAM,GAAG;AAChB,SAAO,EAAC,MAAM,EAAE,MAAM,QAAQ,EAAE,QAAQ,QAAQ,EAAE,OAAM;AAC1D;AAOA,SAAS,UAAU,UAAU,YAAY;AACvC,MAAID,SAAQ;AAEZ,SAAO,EAAEA,SAAQ,WAAW,QAAQ;AAClC,UAAM,QAAQ,WAAWA,MAAK;AAE9B,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,gBAAU,UAAU,KAAK;AAAA,IAC3B,OAAO;AACL,gBAAU,UAAU,KAAK;AAAA,IAC3B;AAAA,EACF;AACF;AAOA,SAAS,UAAU,UAAUO,YAAW;AAEtC,MAAI;AAEJ,OAAK,OAAOA,YAAW;AACrB,QAAI,IAAI,KAAKA,YAAW,GAAG,GAAG;AAC5B,cAAQ,KAAK;AAAA,QACX,KAAK,kBAAkB;AACrB,gBAAM,QAAQA,WAAU,GAAG;AAC3B,cAAI,OAAO;AACT,qBAAS,GAAG,EAAE,KAAK,GAAG,KAAK;AAAA,UAC7B;AAEA;AAAA,QACF;AAAA,QAEA,KAAK,cAAc;AACjB,gBAAM,QAAQA,WAAU,GAAG;AAC3B,cAAI,OAAO;AACT,qBAAS,GAAG,EAAE,KAAK,GAAG,KAAK;AAAA,UAC7B;AAEA;AAAA,QACF;AAAA,QAEA,KAAK;AAAA,QACL,KAAK,QAAQ;AACX,gBAAM,QAAQA,WAAU,GAAG;AAC3B,cAAI,OAAO;AACT,mBAAO,OAAO,SAAS,GAAG,GAAG,KAAK;AAAA,UACpC;AAEA;AAAA,QACF;AAAA,MAEF;AAAA,IACF;AAAA,EACF;AACF;AAGA,SAAS,eAAe,MAAM,OAAO;AACnC,MAAI,MAAM;AACR,UAAM,IAAI;AAAA,MACR,mBACE,KAAK,OACL,QACA,kBAAkB,EAAC,OAAO,KAAK,OAAO,KAAK,KAAK,IAAG,CAAC,IACpD,4BACA,MAAM,OACN,QACA,kBAAkB,EAAC,OAAO,MAAM,OAAO,KAAK,MAAM,IAAG,CAAC,IACtD;AAAA,IACJ;AAAA,EACF,OAAO;AACL,UAAM,IAAI;AAAA,MACR,sCACE,MAAM,OACN,QACA,kBAAkB,EAAC,OAAO,MAAM,OAAO,KAAK,MAAM,IAAG,CAAC,IACtD;AAAA,IACJ;AAAA,EACF;AACF;;;AC9yCe,SAAR,YAA6B,SAAS;AAG3C,QAAM,OAAO;AAEb,OAAK,SAAS;AAKd,WAAS,OAAO,KAAK;AACnB,WAAO,aAAa,KAAK;AAAA,MACvB,GAAG,KAAK,KAAK,UAAU;AAAA,MACvB,GAAG;AAAA;AAAA;AAAA;AAAA,MAIH,YAAY,KAAK,KAAK,qBAAqB,KAAK,CAAC;AAAA,MACjD,iBAAiB,KAAK,KAAK,wBAAwB,KAAK,CAAC;AAAA,IAC3D,CAAC;AAAA,EACH;AACF;", "names": ["parse", "createDebug", "disable", "debug", "index", "search", "index", "values", "index", "list", "all", "index", "extension", "list", "hasOwnProperty", "ok", "previous", "document", "point", "index", "ok", "constructs", "index", "index", "text", "ok", "attentionMarkers", "previous", "point", "ok", "ok", "ok", "ok", "ok", "ok", "effects", "nok", "start", "ok", "furtherStart", "index", "ok", "index", "list", "index", "previous", "start", "content", "ok", "previous", "ok", "ok", "ok", "ok", "ok", "titleBefore", "ok", "content", "text", "ok", "index", "ok", "ok", "index", "index", "text", "ok", "ok", "ok", "ok", "ok", "ok", "index", "content", "text", "definition", "ok", "content", "constructs", "text", "list", "index", "document", "flow", "string", "text", "document", "flow", "string", "text", "createDebug", "point", "exit", "constructs", "all", "list", "ok", "from", "index", "constructs", "document", "head", "point", "blockQuote", "codeText", "definition", "list", "thematicBreak", "compile", "exit", "index", "point", "listItem", "node", "data", "text", "string", "extension"]}