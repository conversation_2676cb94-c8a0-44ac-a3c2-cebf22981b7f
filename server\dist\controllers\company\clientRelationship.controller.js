"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteClientRelationship = exports.updateClientRelationship = exports.createClientRelationship = exports.getClientRelationships = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Get all client relationships for a company
const getClientRelationships = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        // Get relationships where the company is either the client or the provider
        const clientRelationships = await prisma_js_1.prisma.clientRelationship.findMany({
            where: {
                OR: [
                    { clientCompanyId: Number(companyId) },
                    { providerCompanyId: Number(companyId) },
                ],
            },
            include: {
                clientCompany: true,
                providerCompany: true,
            },
        });
        res.json(clientRelationships);
    }
    catch (error) {
        next(createError(500, "Failed to fetch client relationships"));
    }
};
exports.getClientRelationships = getClientRelationships;
// Create a new client relationship
const createClientRelationship = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        const { clientCompanyId, providerCompanyId, status, contractValue, notes } = req.body;
        // Ensure the company is part of the relationship
        if (companyId !== clientCompanyId && companyId !== providerCompanyId) {
            return next(createError(400, "The company must be part of the relationship"));
        }
        // Check if relationship already exists
        const existingRelationship = await prisma_js_1.prisma.clientRelationship.findFirst({
            where: {
                clientCompanyId: Number(clientCompanyId),
                providerCompanyId: Number(providerCompanyId),
            },
        });
        if (existingRelationship) {
            return next(createError(409, "Relationship already exists"));
        }
        const relationship = await prisma_js_1.prisma.clientRelationship.create({
            data: {
                clientCompanyId: Number(clientCompanyId),
                providerCompanyId: Number(providerCompanyId),
                status,
                contractValue,
                notes,
            },
            include: {
                clientCompany: true,
                providerCompany: true,
            },
        });
        res.status(201).json(relationship);
    }
    catch (error) {
        next(createError(500, "Failed to create client relationship"));
    }
};
exports.createClientRelationship = createClientRelationship;
// Update a client relationship
const updateClientRelationship = async (req, res, next) => {
    try {
        const { relationshipId } = req.params;
        const { status, contractValue, notes, endDate, lifetimeValue, lastTransactionDate, transactionCount, } = req.body;
        const relationship = await prisma_js_1.prisma.clientRelationship.update({
            where: { id: Number(relationshipId) },
            data: {
                status,
                contractValue,
                notes,
                endDate,
                lifetimeValue,
                lastTransactionDate,
                transactionCount,
                updatedAt: new Date(),
            },
            include: {
                clientCompany: true,
                providerCompany: true,
            },
        });
        res.json(relationship);
    }
    catch (error) {
        if (error.code === "P2025") {
            return next(createError(404, "Relationship not found"));
        }
        next(createError(500, "Failed to update client relationship"));
    }
};
exports.updateClientRelationship = updateClientRelationship;
// Delete a client relationship
const deleteClientRelationship = async (req, res, next) => {
    try {
        const { relationshipId } = req.params;
        await prisma_js_1.prisma.clientRelationship.delete({
            where: { id: Number(relationshipId) },
        });
        res.status(204).send();
    }
    catch (error) {
        if (error.code === "P2025") {
            return next(createError(404, "Relationship not found"));
        }
        next(createError(500, "Failed to delete client relationship"));
    }
};
exports.deleteClientRelationship = deleteClientRelationship;
//# sourceMappingURL=clientRelationship.controller.js.map