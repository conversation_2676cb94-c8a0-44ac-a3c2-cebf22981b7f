import {
  visitParents
} from "./chunk-UNNXCBJO.js";

// node_modules/.pnpm/unist-util-position@4.0.4/node_modules/unist-util-position/lib/index.js
var pointStart = point("start");
var pointEnd = point("end");
function position(node) {
  return { start: pointStart(node), end: pointEnd(node) };
}
function point(type) {
  return point2;
  function point2(node) {
    const point3 = node && node.position && node.position[type] || {};
    return {
      // @ts-expect-error: in practice, null is allowed.
      line: point3.line || null,
      // @ts-expect-error: in practice, null is allowed.
      column: point3.column || null,
      // @ts-expect-error: in practice, null is allowed.
      offset: point3.offset > -1 ? point3.offset : null
    };
  }
}

// node_modules/.pnpm/unist-util-visit@4.1.2/node_modules/unist-util-visit/lib/index.js
var visit = (
  /**
   * @type {(
   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &
   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)
   * )}
   */
  /**
   * @param {Node} tree
   * @param {Test} test
   * @param {Visitor} visitor
   * @param {boolean | null | undefined} [reverse]
   * @returns {void}
   */
  function(tree, test, visitor, reverse) {
    if (typeof test === "function" && typeof visitor !== "function") {
      reverse = visitor;
      visitor = test;
      test = null;
    }
    visitParents(tree, test, overload, reverse);
    function overload(node, parents) {
      const parent = parents[parents.length - 1];
      return visitor(
        node,
        parent ? parent.children.indexOf(node) : null,
        parent
      );
    }
  }
);

export {
  pointStart,
  pointEnd,
  position,
  visit
};
//# sourceMappingURL=chunk-FHB3G66O.js.map
