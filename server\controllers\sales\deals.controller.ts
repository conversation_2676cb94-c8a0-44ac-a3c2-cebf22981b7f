import { Request, Response } from "express";
import { prisma } from "../../lib/prisma.js";
import { logDealOperation } from "../../utils/activityLogger.utils.js";
import { AuthenticatedRequest } from "../../types/auth.js";

// Get all deals
export const getAllDeals = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      stage,
      type,
      assignedTo,
      search,
      minValue,
      maxValue,
      sortBy = "createdAt",
      sortOrder = "desc",
      page = 1,
      limit = 10,
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build filter conditions
    const where: any = {};

    if (stage) {
      where.stage = stage;
    }

    if (type) {
      where.type = type;
    }

    if (assignedTo) {
      where.assignedToId = Number(assignedTo);
    }

    if (minValue) {
      where.value = {
        ...where.value,
        gte: Number(minValue),
      };
    }

    if (maxValue) {
      where.value = {
        ...where.value,
        lte: Number(maxValue),
      };
    }

    if (search) {
      where.OR = [
        { name: { contains: search as string, mode: "insensitive" } },
        { description: { contains: search as string, mode: "insensitive" } },
      ];
    }

    // Get total count for pagination
    const totalCount = await prisma.deal.count({ where });

    // Get deals with pagination, sorting and filtering
    const deals = await prisma.deal.findMany({
      where,
      include: {
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
        lead: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            company: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
        _count: {
          select: {
            activities: true,
            products: true,
            notes: true,
          },
        },
      },
      orderBy: {
        [sortBy as string]: sortOrder,
      },
      skip,
      take: Number(limit),
    });

    res.status(200).json({
      deals,
      pagination: {
        total: totalCount,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(totalCount / Number(limit)),
      },
    });
  } catch (error) {
    console.error("Error fetching deals:", error);
    res.status(500).json({ error: "Failed to fetch deals" });
  }
};

// Get deal by ID
export const getDealById = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    const deal = await prisma.deal.findUnique({
      where: { id: Number(id) },
      include: {
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
        lead: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            company: true,
            jobTitle: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
            logo: true,
            email: true,
            phone: true,
            website: true,
          },
        },
        products: {
          include: {
            product: true,
          },
        },
        activities: {
          include: {
            assignedTo: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
          orderBy: {
            startDate: "desc",
          },
        },
        notes: {
          include: {
            createdBy: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        quotations: {
          select: {
            id: true,
            quotationNumber: true,
            title: true,
            status: true,
            issueDate: true,
            expiryDate: true,
            totalAmount: true,
          },
        },
      },
    });

    if (!deal) {
      res.status(404).json({ error: "Deal not found" });
      return;
    }

    res.status(200).json(deal);
  } catch (error) {
    console.error("Error fetching deal:", error);
    res.status(500).json({ error: "Failed to fetch deal" });
  }
};

// Create a new deal
export const createDeal = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      name,
      description,
      value,
      currency,
      stage,
      type,
      probability,
      expectedCloseDate,
      leadId,
      companyId,
      assignedToId,
      products,
    } = req.body;

    // Create deal
    const deal = await prisma.deal.create({
      data: {
        name,
        description,
        value,
        currency: currency || "USD",
        stage,
        type,
        probability,
        expectedCloseDate: expectedCloseDate
          ? new Date(expectedCloseDate)
          : null,
        leadId,
        companyId,
        assignedToId: assignedToId ? Number(assignedToId) : null,
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
        lead: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            company: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
      },
    });

    // Add products if provided
    if (products && Array.isArray(products) && products.length > 0) {
      await Promise.all(
        products.map(async (product: any) => {
          await prisma.dealProduct.create({
            data: {
              dealId: deal.id,
              productId: product.productId,
              quantity: product.quantity,
              unitPrice: product.unitPrice,
              discount: product.discount || 0,
              total:
                product.quantity *
                product.unitPrice *
                (1 - (product.discount || 0) / 100),
            },
          });
        })
      );
    }

    // Get the updated deal with products
    const finalDeal = await prisma.deal.findUnique({
      where: { id: deal.id },
      include: {
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
        lead: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            company: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
        products: {
          include: {
            product: true,
          },
        },
      },
    });

    // Log deal creation activity
    if (req.user?.id) {
      await logDealOperation(
        "CREATE",
        finalDeal.name,
        finalDeal.id,
        req,
        req.user.id
      );
    }

    res.status(201).json(finalDeal);
  } catch (error) {
    console.error("Error creating deal:", error);
    res.status(500).json({ error: "Failed to create deal" });
  }
};

// Update a deal
export const updateDeal = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      value,
      currency,
      stage,
      type,
      probability,
      expectedCloseDate,
      actualCloseDate,
      leadId,
      companyId,
      assignedToId,
    } = req.body;

    // Check if deal exists
    const existingDeal = await prisma.deal.findUnique({
      where: { id: Number(id) },
    });

    if (!existingDeal) {
      res.status(404).json({ error: "Deal not found" });
      return;
    }

    // Update deal
    const updatedDeal = await prisma.deal.update({
      where: { id: Number(id) },
      data: {
        name,
        description,
        value,
        currency,
        stage,
        type,
        probability,
        expectedCloseDate: expectedCloseDate
          ? new Date(expectedCloseDate)
          : null,
        actualCloseDate: actualCloseDate ? new Date(actualCloseDate) : null,
        leadId,
        companyId,
        assignedToId: assignedToId ? Number(assignedToId) : null,
        updatedAt: new Date(),
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
          },
        },
        lead: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            company: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
        products: {
          include: {
            product: true,
          },
        },
      },
    });

    // Log deal update activity
    if (req.user?.id) {
      const changes = {};
      const updateFields = [
        "name",
        "description",
        "value",
        "currency",
        "stage",
        "type",
        "probability",
        "expectedCloseDate",
        "actualCloseDate",
        "leadId",
        "companyId",
        "assignedToId",
      ];

      updateFields.forEach((field) => {
        if (
          req.body[field] !== undefined &&
          existingDeal[field] !== req.body[field]
        ) {
          changes[field] = {
            old: existingDeal[field],
            new: req.body[field],
          };
        }
      });

      if (Object.keys(changes).length > 0) {
        await logDealOperation(
          "UPDATE",
          updatedDeal.name,
          updatedDeal.id,
          req,
          req.user.id,
          changes
        );
      }
    }

    res.status(200).json(updatedDeal);
  } catch (error) {
    console.error("Error updating deal:", error);
    res.status(500).json({ error: "Failed to update deal" });
  }
};

// Delete a deal
export const deleteDeal = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    // Check if deal exists
    const deal = await prisma.deal.findUnique({
      where: { id: Number(id) },
    });

    if (!deal) {
      res.status(404).json({ error: "Deal not found" });
      return;
    }

    // Log deal deletion activity before deleting
    if (req.user?.id) {
      await logDealOperation("DELETE", deal.name, deal.id, req, req.user.id);
    }

    // Delete deal (cascade will handle related records)
    await prisma.deal.delete({
      where: { id: Number(id) },
    });

    res.status(200).json({ message: "Deal deleted successfully" });
  } catch (error) {
    console.error("Error deleting deal:", error);
    res.status(500).json({ error: "Failed to delete deal" });
  }
};

// Get deals for pipeline view
export const getPipelineDeals = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    // Get all pipeline stages
    const stages = await prisma.salesPipelineStage.findMany({
      where: { isActive: true },
      orderBy: { order: "asc" },
    });

    // If no custom stages are defined, use default DealStage enum values
    const pipelineStages =
      stages.length > 0
        ? stages
        : [
            { id: "PROSPECTING", name: "Prospecting", order: 1 },
            { id: "QUALIFICATION", name: "Qualification", order: 2 },
            { id: "NEEDS_ANALYSIS", name: "Needs Analysis", order: 3 },
            { id: "VALUE_PROPOSITION", name: "Value Proposition", order: 4 },
            { id: "PROPOSAL", name: "Proposal", order: 5 },
            { id: "NEGOTIATION", name: "Negotiation", order: 6 },
            { id: "CLOSED_WON", name: "Closed Won", order: 7 },
            { id: "CLOSED_LOST", name: "Closed Lost", order: 8 },
          ];

    // Get filter parameters
    const { assignedTo, timeframe } = req.query;

    // Build filter conditions
    const where: any = {};

    if (assignedTo) {
      where.assignedToId = Number(assignedTo);
    }

    if (timeframe) {
      const now = new Date();
      let startDate = new Date();

      switch (timeframe) {
        case "this_week":
          startDate.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
          break;
        case "this_month":
          startDate = new Date(now.getFullYear(), now.getMonth(), 1); // Start of month
          break;
        case "this_quarter":
          const quarter = Math.floor(now.getMonth() / 3);
          startDate = new Date(now.getFullYear(), quarter * 3, 1); // Start of quarter
          break;
        case "this_year":
          startDate = new Date(now.getFullYear(), 0, 1); // Start of year
          break;
        default:
          // No timeframe filter
          break;
      }

      if (timeframe !== "all") {
        where.createdAt = {
          gte: startDate,
        };
      }
    }

    // Get all deals
    const deals = await prisma.deal.findMany({
      where,
      include: {
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
            logo: true,
          },
        },
        lead: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        _count: {
          select: {
            activities: true,
            products: true,
          },
        },
      },
      orderBy: [{ probability: "desc" }, { value: "desc" }],
    });

    // Group deals by stage
    const pipeline = pipelineStages.map((stage) => {
      const stageDeals = deals.filter((deal) => {
        // Match either by custom stage ID or by enum value
        return stages.length > 0
          ? deal.stage === stage.id
          : deal.stage === stage.id;
      });

      const totalValue = stageDeals.reduce((sum, deal) => sum + deal.value, 0);

      return {
        id: stage.id,
        name: stage.name,
        order: stage.order,
        deals: stageDeals,
        count: stageDeals.length,
        totalValue,
      };
    });

    // Calculate pipeline metrics
    const totalDeals = deals.length;
    const totalValue = deals.reduce((sum, deal) => sum + deal.value, 0);
    const weightedValue = deals.reduce(
      (sum, deal) => sum + (deal.value * deal.probability) / 100,
      0
    );

    res.status(200).json({
      pipeline,
      metrics: {
        totalDeals,
        totalValue,
        weightedValue,
        averageDealSize: totalDeals > 0 ? totalValue / totalDeals : 0,
      },
    });
  } catch (error) {
    console.error("Error fetching pipeline deals:", error);
    res.status(500).json({ error: "Failed to fetch pipeline deals" });
  }
};

// Add product to deal
export const addProductToDeal = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { productId, quantity, unitPrice, discount = 0 } = req.body;

    // Check if deal exists
    const deal = await prisma.deal.findUnique({
      where: { id: Number(id) },
    });

    if (!deal) {
      res.status(404).json({ error: "Deal not found" });
      return;
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: Number(productId) },
    });

    if (!product) {
      res.status(404).json({ error: "Product not found" });
      return;
    }

    // Check if product is already added to the deal
    const existingProduct = await prisma.dealProduct.findUnique({
      where: {
        dealId_productId: {
          dealId: Number(id),
          productId: Number(productId),
        },
      },
    });

    if (existingProduct) {
      res.status(400).json({ error: "Product already added to this deal" });
      return;
    }

    // Calculate total
    const total = quantity * unitPrice * (1 - discount / 100);

    // Add product to deal
    const dealProduct = await prisma.dealProduct.create({
      data: {
        dealId: Number(id),
        productId: Number(productId),
        quantity,
        unitPrice,
        discount,
        total,
      },
      include: {
        product: true,
      },
    });

    // Update deal value
    const dealProducts = await prisma.dealProduct.findMany({
      where: { dealId: Number(id) },
    });

    const newDealValue = dealProducts.reduce(
      (sum, product) => sum + product.total,
      0
    );

    await prisma.deal.update({
      where: { id: Number(id) },
      data: {
        value: newDealValue,
        updatedAt: new Date(),
      },
    });

    res.status(201).json(dealProduct);
  } catch (error) {
    console.error("Error adding product to deal:", error);
    res.status(500).json({ error: "Failed to add product to deal" });
  }
};

// Update deal product
export const updateDealProduct = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id, productId } = req.params;
    const { quantity, unitPrice, discount = 0 } = req.body;

    // Check if deal product exists
    const dealProduct = await prisma.dealProduct.findUnique({
      where: {
        dealId_productId: {
          dealId: Number(id),
          productId: Number(productId),
        },
      },
    });

    if (!dealProduct) {
      res.status(404).json({ error: "Product not found in this deal" });
      return;
    }

    // Calculate total
    const total = quantity * unitPrice * (1 - discount / 100);

    // Update deal product
    const updatedDealProduct = await prisma.dealProduct.update({
      where: {
        dealId_productId: {
          dealId: Number(id),
          productId: Number(productId),
        },
      },
      data: {
        quantity,
        unitPrice,
        discount,
        total,
      },
      include: {
        product: true,
      },
    });

    // Update deal value
    const dealProducts = await prisma.dealProduct.findMany({
      where: { dealId: Number(id) },
    });

    const newDealValue = dealProducts.reduce(
      (sum, product) => sum + product.total,
      0
    );

    await prisma.deal.update({
      where: { id: Number(id) },
      data: {
        value: newDealValue,
        updatedAt: new Date(),
      },
    });

    res.status(200).json(updatedDealProduct);
  } catch (error) {
    console.error("Error updating deal product:", error);
    res.status(500).json({ error: "Failed to update deal product" });
  }
};

// Remove product from deal
export const removeDealProduct = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id, productId } = req.params;

    // Check if deal product exists
    const dealProduct = await prisma.dealProduct.findUnique({
      where: {
        dealId_productId: {
          dealId: Number(id),
          productId: Number(productId),
        },
      },
    });

    if (!dealProduct) {
      res.status(404).json({ error: "Product not found in this deal" });
      return;
    }

    // Remove product from deal
    await prisma.dealProduct.delete({
      where: {
        dealId_productId: {
          dealId: Number(id),
          productId: Number(productId),
        },
      },
    });

    // Update deal value
    const dealProducts = await prisma.dealProduct.findMany({
      where: { dealId: Number(id) },
    });

    const newDealValue = dealProducts.reduce(
      (sum, product) => sum + product.total,
      0
    );

    await prisma.deal.update({
      where: { id: Number(id) },
      data: {
        value: newDealValue,
        updatedAt: new Date(),
      },
    });

    res.status(200).json({ message: "Product removed from deal successfully" });
  } catch (error) {
    console.error("Error removing product from deal:", error);
    res.status(500).json({ error: "Failed to remove product from deal" });
  }
};

// Add note to deal
export const addDealNote = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    // Check if deal exists
    const deal = await prisma.deal.findUnique({
      where: { id: Number(id) },
    });

    if (!deal) {
      res.status(404).json({ error: "Deal not found" });
      return;
    }

    // Add note to deal
    const note = await prisma.dealNote.create({
      data: {
        dealId: Number(id),
        content,
        createdById: userId,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
      },
    });

    res.status(201).json(note);
  } catch (error) {
    console.error("Error adding note to deal:", error);
    res.status(500).json({ error: "Failed to add note to deal" });
  }
};
