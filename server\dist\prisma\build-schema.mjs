import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
const __dirname = path.dirname(fileURLToPath(import.meta.url));
// Base schema content
const baseSchema = `// This file is auto-generated. Do not edit directly.
// Learn more: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
`;
// Read and combine schema files
const schemaDir = path.join(__dirname, "schema");
const outputFile = path.join(__dirname, "schema.prisma");
// Define core models that should be processed first
const coreModelFiles = [
    "00-base.prisma",
    "01-user.prisma",
    "02-company.prisma",
    "05-product.prisma",
];
// Function to extract model definitions from a file
function extractModelDefinitions(content) {
    const models = {};
    const lines = content.split("\n");
    let currentModel = null;
    let modelContent = [];
    let insideModel = false;
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        // Check if this is a model definition line
        const modelMatch = line.match(/^model\s+(\w+)\s+\{/);
        if (modelMatch) {
            currentModel = modelMatch[1];
            insideModel = true;
            modelContent = [line];
        }
        else if (insideModel) {
            modelContent.push(line);
            if (line.trim() === "}") {
                insideModel = false;
                models[currentModel] = modelContent.join("\n");
                currentModel = null;
                modelContent = [];
            }
        }
    }
    return models;
}
// Function to remove model definitions from content
function removeModelDefinitions(content, modelNames) {
    const lines = content.split("\n");
    const processedLines = [];
    let skipLines = false;
    let currentModel = null;
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        // Check if this is a model definition line
        const modelMatch = line.match(/^model\s+(\w+)\s+\{/);
        if (modelMatch) {
            currentModel = modelMatch[1];
            if (modelNames.includes(currentModel)) {
                skipLines = true;
                continue;
            }
        }
        if (skipLines && line.trim() === "}") {
            skipLines = false;
            currentModel = null;
            continue;
        }
        if (!skipLines) {
            processedLines.push(line);
        }
    }
    return processedLines.join("\n");
}
try {
    // Ensure the schema directory exists
    if (!fs.existsSync(schemaDir)) {
        console.error(`Schema directory not found: ${schemaDir}`);
        process.exit(1);
    }
    // Read all .prisma files from the schema directory
    const allFiles = fs
        .readdirSync(schemaDir)
        .filter((file) => file.endsWith(".prisma"))
        .sort((a, b) => {
        // Ensure base schema is processed first
        if (a.startsWith("00-"))
            return -1;
        if (b.startsWith("00-"))
            return 1;
        return a.localeCompare(b);
    });
    if (allFiles.length === 0) {
        console.error("No .prisma schema files found in the schema directory");
        process.exit(1);
    }
    // First, process core model files
    let combinedSchema = baseSchema;
    const coreModels = {};
    // Extract core models
    for (const file of coreModelFiles) {
        if (allFiles.includes(file)) {
            const filePath = path.join(schemaDir, file);
            const fileContent = fs.readFileSync(filePath, "utf-8");
            if (fileContent.trim()) {
                const models = extractModelDefinitions(fileContent);
                Object.assign(coreModels, models);
                combinedSchema += `\n// Content from ${file}\n${fileContent}\n`;
            }
        }
    }
    // Get the list of core model names
    const coreModelNames = Object.keys(coreModels);
    // Process remaining files, removing duplicate model definitions
    const remainingFiles = allFiles.filter((file) => !coreModelFiles.includes(file));
    for (const file of remainingFiles) {
        const filePath = path.join(schemaDir, file);
        const fileContent = fs.readFileSync(filePath, "utf-8");
        if (fileContent.trim()) {
            const processedContent = removeModelDefinitions(fileContent, coreModelNames);
            if (processedContent.trim()) {
                combinedSchema += `\n// Content from ${file}\n${processedContent}\n`;
            }
        }
        else {
            console.warn(`Warning: Empty schema file: ${file}`);
        }
    }
    // Write the combined schema
    fs.writeFileSync(outputFile, combinedSchema);
    console.log("Schema files combined successfully:");
    console.log("Files processed:", allFiles.join(", "));
    console.log("Output:", outputFile);
}
catch (error) {
    console.error("Error combining schema files:", error);
    process.exit(1);
}
//# sourceMappingURL=build-schema.mjs.map