"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.activityLoggerMiddleware = void 0;
const activityLogger_js_1 = require("../services/activityLogger.js");
// Middleware to automatically log activities based on route patterns
const activityLoggerMiddleware = (options) => {
    return async (req, res, next) => {
        // Store original res.json to intercept response
        const originalJson = res.json;
        res.json = function (body) {
            // Log activity after successful response
            if (res.statusCode >= 200 && res.statusCode < 300) {
                const logData = {
                    type: options.type || getTypeFromMethod(req.method),
                    action: options.action || req.method.toLowerCase(),
                    module: options.module,
                    entity: options.entity,
                    entityId: req.params.id || body?.id?.toString(),
                    title: options.getTitle ? options.getTitle(req) : getDefaultTitle(req, options.module),
                    description: options.getDescription ? options.getDescription(req) : getDefaultDescription(req, options.module),
                    userId: req.user?.id,
                    status: "SUCCESS",
                    severity: options.severity || "INFO",
                    changes: req.method === "PUT" || req.method === "PATCH" ? req.body : undefined,
                };
                // Log activity asynchronously without blocking response
                activityLogger_js_1.ActivityLogger.logFromRequest(req, logData).catch(error => {
                    console.error("Failed to log activity:", error);
                });
            }
            return originalJson.call(this, body);
        };
        next();
    };
};
exports.activityLoggerMiddleware = activityLoggerMiddleware;
// Helper function to determine activity type from HTTP method
function getTypeFromMethod(method) {
    switch (method.toUpperCase()) {
        case "POST":
            return "CREATE";
        case "PUT":
        case "PATCH":
            return "UPDATE";
        case "DELETE":
            return "DELETE";
        case "GET":
            return "OTHER"; // Usually we don't log GET requests
        default:
            return "OTHER";
    }
}
// Helper function to generate default title
function getDefaultTitle(req, module) {
    const method = req.method.toUpperCase();
    const entity = req.route?.path?.split('/')[1] || 'item';
    switch (method) {
        case "POST":
            return `${entity.charAt(0).toUpperCase() + entity.slice(1)} Created`;
        case "PUT":
        case "PATCH":
            return `${entity.charAt(0).toUpperCase() + entity.slice(1)} Updated`;
        case "DELETE":
            return `${entity.charAt(0).toUpperCase() + entity.slice(1)} Deleted`;
        default:
            return `${module} Activity`;
    }
}
// Helper function to generate default description
function getDefaultDescription(req, module) {
    const method = req.method.toUpperCase();
    const entity = req.route?.path?.split('/')[1] || 'item';
    switch (method) {
        case "POST":
            return `A new ${entity} was created in the ${module} module`;
        case "PUT":
        case "PATCH":
            return `An existing ${entity} was updated in the ${module} module`;
        case "DELETE":
            return `An ${entity} was deleted from the ${module} module`;
        default:
            return `Activity performed in the ${module} module`;
    }
}
//# sourceMappingURL=activityLogger.middleware.js.map