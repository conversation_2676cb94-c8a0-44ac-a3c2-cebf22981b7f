"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const stripe_1 = __importDefault(require("stripe"));
exports.default = async (req, res) => {
    try {
        const { amount, currency = "eur" } = req.body;
        if (!amount) {
            return res.status(400).json({ error: "Amount is required" });
        }
        const stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY, {
            apiVersion: "2025-04-30.basil", // Use the latest API version
        });
        // Create a PaymentIntent
        const paymentIntent = await stripe.paymentIntents.create({
            amount: Math.round(amount * 100), // Convert to cents
            currency,
            automatic_payment_methods: {
                enabled: true,
            },
        });
        console.log("Created payment intent with ID:", paymentIntent.id);
        console.log("Client secret:", paymentIntent.client_secret);
        return res.json({
            clientSecret: paymentIntent.client_secret,
        });
    }
    catch (error) {
        console.error("Error creating payment intent:", error);
        return res.status(500).json({
            error: "Failed to create payment intent",
            details: error.message,
        });
    }
};
//# sourceMappingURL=create-payment-intent.js.map