{"version": 3, "file": "activityLogger.middleware.js", "sourceRoot": "", "sources": ["../../middleware/activityLogger.middleware.ts"], "names": [], "mappings": ";;;AACA,qEAA+D;AAE/D,qEAAqE;AAC9D,MAAM,wBAAwB,GAAG,CAAC,OAQxC,EAAE,EAAE;IACH,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC/D,gDAAgD;QAChD,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;QAE9B,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;YAC3B,yCAAyC;YACzC,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBAClD,MAAM,OAAO,GAAG;oBACd,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC;oBACnD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE;oBAClD,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;oBAC/C,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC;oBACtF,WAAW,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC;oBAC9G,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;oBACpB,MAAM,EAAE,SAAkB;oBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,MAAe;oBAC7C,OAAO,EAAE,GAAG,CAAC,MAAM,KAAK,KAAK,IAAI,GAAG,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;iBAC/E,CAAC;gBAEF,wDAAwD;gBACxD,kCAAc,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBACxD,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC;YACL,CAAC;YAED,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAzCW,QAAA,wBAAwB,4BAyCnC;AAEF,8DAA8D;AAC9D,SAAS,iBAAiB,CAAC,MAAc;IACvC,QAAQ,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QAC7B,KAAK,MAAM;YACT,OAAO,QAAQ,CAAC;QAClB,KAAK,KAAK,CAAC;QACX,KAAK,OAAO;YACV,OAAO,QAAQ,CAAC;QAClB,KAAK,QAAQ;YACX,OAAO,QAAQ,CAAC;QAClB,KAAK,KAAK;YACR,OAAO,OAAO,CAAC,CAAC,oCAAoC;QACtD;YACE,OAAO,OAAO,CAAC;IACnB,CAAC;AACH,CAAC;AAED,4CAA4C;AAC5C,SAAS,eAAe,CAAC,GAAY,EAAE,MAAc;IACnD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;IACxC,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC;IAExD,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,MAAM;YACT,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC;QACvE,KAAK,KAAK,CAAC;QACX,KAAK,OAAO;YACV,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC;QACvE,KAAK,QAAQ;YACX,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC;QACvE;YACE,OAAO,GAAG,MAAM,WAAW,CAAC;IAChC,CAAC;AACH,CAAC;AAED,kDAAkD;AAClD,SAAS,qBAAqB,CAAC,GAAY,EAAE,MAAc;IACzD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;IACxC,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC;IAExD,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,MAAM;YACT,OAAO,SAAS,MAAM,uBAAuB,MAAM,SAAS,CAAC;QAC/D,KAAK,KAAK,CAAC;QACX,KAAK,OAAO;YACV,OAAO,eAAe,MAAM,uBAAuB,MAAM,SAAS,CAAC;QACrE,KAAK,QAAQ;YACX,OAAO,MAAM,MAAM,yBAAyB,MAAM,SAAS,CAAC;QAC9D;YACE,OAAO,6BAA6B,MAAM,SAAS,CAAC;IACxD,CAAC;AACH,CAAC"}