"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSystemAlerts = exports.getPerformanceMetrics = exports.getResourceUsage = exports.getServiceStatus = exports.getSystemHealthOverview = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const os = __importStar(require("os"));
const util_1 = require("util");
const child_process_1 = require("child_process");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
// Get system health overview
const getSystemHealthOverview = async (req, res) => {
    try {
        // Get current system metrics
        const cpuUsage = await getCpuUsage();
        const memoryUsage = getMemoryUsage();
        const diskUsage = await getDiskUsage();
        const networkStats = await getNetworkStats();
        // Get active users count (users with sessions in last 24 hours)
        const activeUsers = await prisma_js_1.prisma.sessionLog.count({
            where: {
                loginAt: {
                    gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
                },
                logoutAt: null,
            },
        });
        // Get API request metrics (approximate from recent activity)
        const apiRequests = await getApiRequestCount();
        // Get system alerts count
        const systemAlerts = await prisma_js_1.prisma.systemAlert.count({
            where: {
                status: "ACTIVE",
            },
        });
        // Calculate trends (simplified - compare with previous hour)
        const previousHour = new Date(Date.now() - 60 * 60 * 1000);
        const previousActiveUsers = await prisma_js_1.prisma.sessionLog.count({
            where: {
                loginAt: {
                    gte: new Date(previousHour.getTime() - 24 * 60 * 60 * 1000),
                    lt: previousHour,
                },
                logoutAt: null,
            },
        });
        const activeUsersTrend = activeUsers - previousActiveUsers;
        res.json({
            systemHealth: Math.round(100 - (cpuUsage + memoryUsage.percentage) / 2),
            systemHealthTrend: 2, // Simplified trend
            activeUsers,
            activeUsersTrend,
            apiRequests,
            apiRequestsTrend: 15, // Simplified trend
            systemAlerts,
            systemAlertsTrend: -2, // Simplified trend
        });
    }
    catch (error) {
        console.error("Error fetching system health overview:", error);
        res.status(500).json({ error: "Failed to fetch system health overview" });
    }
};
exports.getSystemHealthOverview = getSystemHealthOverview;
// Get service status
const getServiceStatus = async (req, res) => {
    try {
        let services = await prisma_js_1.prisma.serviceStatus.findMany({
            orderBy: { serviceName: "asc" },
        });
        // If no services exist, create default ones
        if (services.length === 0) {
            const defaultServices = [
                {
                    serviceName: "database",
                    displayName: "Database",
                    description: "Primary database server",
                    status: "OPERATIONAL",
                },
                {
                    serviceName: "api_server",
                    displayName: "API Server",
                    description: "REST API endpoints",
                    status: "OPERATIONAL",
                },
                {
                    serviceName: "file_storage",
                    displayName: "File Storage",
                    description: "Document storage service",
                    status: "OPERATIONAL",
                },
                {
                    serviceName: "authentication",
                    displayName: "Authentication",
                    description: "User authentication service",
                    status: "OPERATIONAL",
                },
                {
                    serviceName: "email_service",
                    displayName: "Email Service",
                    description: "Notification emails",
                    status: "OPERATIONAL",
                },
            ];
            // Create default services
            for (const service of defaultServices) {
                await prisma_js_1.prisma.serviceStatus.create({ data: service });
            }
            services = await prisma_js_1.prisma.serviceStatus.findMany({
                orderBy: { serviceName: "asc" },
            });
        }
        // Update service health checks
        await updateServiceHealthChecks();
        // Refresh services after health checks
        services = await prisma_js_1.prisma.serviceStatus.findMany({
            orderBy: { serviceName: "asc" },
        });
        res.json(services);
    }
    catch (error) {
        console.error("Error fetching service status:", error);
        res.status(500).json({ error: "Failed to fetch service status" });
    }
};
exports.getServiceStatus = getServiceStatus;
// Get resource usage
const getResourceUsage = async (req, res) => {
    try {
        const cpuUsage = await getCpuUsage();
        const memoryUsage = getMemoryUsage();
        const diskUsage = await getDiskUsage();
        const networkStats = await getNetworkStats();
        const resources = {
            cpu: {
                percentage: Math.round(cpuUsage),
                cores: os.cpus().length,
                load: os.loadavg()[0],
            },
            memory: {
                percentage: Math.round(memoryUsage.percentage),
                used: memoryUsage.used,
                total: memoryUsage.total,
            },
            disk: {
                percentage: Math.round(diskUsage.percentage),
                used: diskUsage.used,
                total: diskUsage.total,
            },
            network: {
                download: networkStats.download,
                upload: networkStats.upload,
                downloadPercentage: 50, // Simplified
                uploadPercentage: 40, // Simplified
            },
        };
        res.json(resources);
    }
    catch (error) {
        console.error("Error fetching resource usage:", error);
        res.status(500).json({ error: "Failed to fetch resource usage" });
    }
};
exports.getResourceUsage = getResourceUsage;
// Get performance metrics
const getPerformanceMetrics = async (req, res) => {
    try {
        // Get recent performance metrics
        const recentMetrics = await prisma_js_1.prisma.performanceMetric.findMany({
            where: {
                timestamp: {
                    gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
                },
            },
            orderBy: { timestamp: "desc" },
            take: 100,
        });
        // Group by metric name and calculate averages
        const metricGroups = recentMetrics.reduce((acc, metric) => {
            if (!acc[metric.metricName]) {
                acc[metric.metricName] = [];
            }
            acc[metric.metricName].push(metric);
            return acc;
        }, {});
        const performanceMetrics = {
            apiResponseTime: calculateAverage(metricGroups.api_response_time || []),
            apiResponseTimeTrend: -5,
            dbQueryTime: calculateAverage(metricGroups.db_query_time || []),
            dbQueryTimeTrend: -8,
            errorRate: calculateAverage(metricGroups.error_rate || []),
            errorRateTrend: -10,
            pageLoadTime: calculateAverage(metricGroups.page_load_time || []),
            pageLoadTimeTrend: -3,
            timeLabels: ["1h ago", "50m ago", "40m ago", "30m ago", "20m ago", "10m ago", "Now"],
            apiResponseTimeHistory: [145, 140, 135, 130, 125, 122, 120],
            dbQueryTimeHistory: [60, 55, 52, 50, 48, 46, 45],
            errorRateHistory: [0.8, 0.7, 0.7, 0.6, 0.6, 0.5, 0.5],
            pageLoadTimeHistory: [920, 900, 890, 880, 870, 860, 850],
        };
        res.json(performanceMetrics);
    }
    catch (error) {
        console.error("Error fetching performance metrics:", error);
        res.status(500).json({ error: "Failed to fetch performance metrics" });
    }
};
exports.getPerformanceMetrics = getPerformanceMetrics;
// Get system alerts
const getSystemAlerts = async (req, res) => {
    try {
        const alerts = await prisma_js_1.prisma.systemAlert.findMany({
            where: {
                status: {
                    in: ["ACTIVE", "ACKNOWLEDGED"],
                },
            },
            include: {
                acknowledgedByUser: {
                    select: { firstName: true, lastName: true },
                },
                resolvedByUser: {
                    select: { firstName: true, lastName: true },
                },
            },
            orderBy: [
                { severity: "desc" },
                { createdAt: "desc" },
            ],
            take: 10,
        });
        res.json(alerts);
    }
    catch (error) {
        console.error("Error fetching system alerts:", error);
        res.status(500).json({ error: "Failed to fetch system alerts" });
    }
};
exports.getSystemAlerts = getSystemAlerts;
// Helper functions
async function getCpuUsage() {
    try {
        const startUsage = process.cpuUsage();
        await new Promise(resolve => setTimeout(resolve, 100));
        const endUsage = process.cpuUsage(startUsage);
        const totalUsage = endUsage.user + endUsage.system;
        const totalTime = 100000; // 100ms in microseconds
        return Math.min((totalUsage / totalTime) * 100, 100);
    }
    catch {
        return 0;
    }
}
function getMemoryUsage() {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    return {
        used: usedMemory,
        total: totalMemory,
        percentage: (usedMemory / totalMemory) * 100,
    };
}
async function getDiskUsage() {
    try {
        if (process.platform === "win32") {
            // Windows
            const { stdout } = await execAsync('wmic logicaldisk get size,freespace,caption');
            // Parse Windows output (simplified)
            return { used: 720 * 1024 * 1024 * 1024, total: 1000 * 1024 * 1024 * 1024, percentage: 72 };
        }
        else {
            // Unix-like systems
            const { stdout } = await execAsync("df -h / | tail -1");
            const parts = stdout.trim().split(/\s+/);
            const usedPercent = parseInt(parts[4].replace('%', ''));
            return { used: 0, total: 0, percentage: usedPercent };
        }
    }
    catch {
        return { used: 720 * 1024 * 1024 * 1024, total: 1000 * 1024 * 1024 * 1024, percentage: 72 };
    }
}
async function getNetworkStats() {
    // Simplified network stats
    return {
        download: 5 * 1024 * 1024, // 5 MB/s
        upload: 2 * 1024 * 1024, // 2 MB/s
    };
}
async function getApiRequestCount() {
    // Simplified API request count based on recent user activity
    const recentActivity = await prisma_js_1.prisma.sessionLog.count({
        where: {
            loginAt: {
                gte: new Date(Date.now() - 60 * 60 * 1000), // Last hour
            },
        },
    });
    return recentActivity * 50; // Approximate requests per active session
}
async function updateServiceHealthChecks() {
    // Update database service
    try {
        await prisma_js_1.prisma.$queryRaw `SELECT 1`;
        await prisma_js_1.prisma.serviceStatus.updateMany({
            where: { serviceName: "database" },
            data: {
                status: "OPERATIONAL",
                responseTime: 45,
                uptime: 99.9,
                lastCheck: new Date(),
            },
        });
    }
    catch {
        await prisma_js_1.prisma.serviceStatus.updateMany({
            where: { serviceName: "database" },
            data: {
                status: "DOWN",
                lastCheck: new Date(),
            },
        });
    }
    // Update other services (simplified)
    const services = ["api_server", "file_storage", "authentication", "email_service"];
    for (const serviceName of services) {
        await prisma_js_1.prisma.serviceStatus.updateMany({
            where: { serviceName },
            data: {
                status: "OPERATIONAL",
                responseTime: Math.random() * 200 + 50,
                uptime: 99 + Math.random(),
                lastCheck: new Date(),
            },
        });
    }
}
function calculateAverage(metrics) {
    if (metrics.length === 0)
        return 0;
    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
    return Math.round(sum / metrics.length);
}
//# sourceMappingURL=system-health.controller.js.map