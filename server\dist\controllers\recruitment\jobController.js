"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.closeJob = exports.publishJob = exports.deleteJob = exports.updateJob = exports.createJob = exports.getJob = exports.getJobs = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// Get all job postings for a company
const getJobs = async (req, res) => {
    try {
        const { companyId } = req.params;
        const { status, page = 1, limit = 10 } = req.query;
        const where = { companyId: parseInt(companyId) };
        if (status) {
            where.status = status;
        }
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const [jobs, total] = await Promise.all([
            prisma.jobPosting.findMany({
                where,
                include: {
                    creator: {
                        select: { id: true, firstName: true, lastName: true, email: true }
                    },
                    applications: {
                        select: { id: true, status: true }
                    },
                    _count: {
                        select: { applications: true }
                    }
                },
                orderBy: { createdAt: 'desc' },
                skip,
                take: parseInt(limit)
            }),
            prisma.jobPosting.count({ where })
        ]);
        res.json({
            data: jobs,
            total,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(total / parseInt(limit))
        });
    }
    catch (error) {
        console.error('Error fetching jobs:', error);
        res.status(500).json({ error: 'Failed to fetch jobs' });
    }
};
exports.getJobs = getJobs;
// Get single job posting
const getJob = async (req, res) => {
    try {
        const { id } = req.params;
        const job = await prisma.jobPosting.findUnique({
            where: { id: parseInt(id) },
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true }
                },
                applications: {
                    include: {
                        applicant: {
                            select: { id: true, firstName: true, lastName: true, email: true }
                        }
                    }
                },
                company: {
                    select: { id: true, name: true, logo: true }
                }
            }
        });
        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }
        res.json(job);
    }
    catch (error) {
        console.error('Error fetching job:', error);
        res.status(500).json({ error: 'Failed to fetch job' });
    }
};
exports.getJob = getJob;
// Create new job posting
const createJob = async (req, res) => {
    try {
        const { companyId } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }
        const jobData = {
            ...req.body,
            companyId: parseInt(companyId),
            createdBy: userId
        };
        const job = await prisma.jobPosting.create({
            data: jobData,
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true }
                },
                company: {
                    select: { id: true, name: true, logo: true }
                }
            }
        });
        res.status(201).json(job);
    }
    catch (error) {
        console.error('Error creating job:', error);
        res.status(500).json({ error: 'Failed to create job' });
    }
};
exports.createJob = createJob;
// Update job posting
const updateJob = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }
        // Check if job exists and user has permission
        const existingJob = await prisma.jobPosting.findUnique({
            where: { id: parseInt(id) }
        });
        if (!existingJob) {
            return res.status(404).json({ error: 'Job not found' });
        }
        const job = await prisma.jobPosting.update({
            where: { id: parseInt(id) },
            data: req.body,
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true }
                },
                company: {
                    select: { id: true, name: true, logo: true }
                }
            }
        });
        res.json(job);
    }
    catch (error) {
        console.error('Error updating job:', error);
        res.status(500).json({ error: 'Failed to update job' });
    }
};
exports.updateJob = updateJob;
// Delete job posting
const deleteJob = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }
        // Check if job exists and user has permission
        const existingJob = await prisma.jobPosting.findUnique({
            where: { id: parseInt(id) }
        });
        if (!existingJob) {
            return res.status(404).json({ error: 'Job not found' });
        }
        await prisma.jobPosting.delete({
            where: { id: parseInt(id) }
        });
        res.json({ message: 'Job deleted successfully' });
    }
    catch (error) {
        console.error('Error deleting job:', error);
        res.status(500).json({ error: 'Failed to delete job' });
    }
};
exports.deleteJob = deleteJob;
// Publish job posting
const publishJob = async (req, res) => {
    try {
        const { id } = req.params;
        const job = await prisma.jobPosting.update({
            where: { id: parseInt(id) },
            data: {
                status: 'ACTIVE',
                publishedAt: new Date()
            }
        });
        res.json(job);
    }
    catch (error) {
        console.error('Error publishing job:', error);
        res.status(500).json({ error: 'Failed to publish job' });
    }
};
exports.publishJob = publishJob;
// Close job posting
const closeJob = async (req, res) => {
    try {
        const { id } = req.params;
        const job = await prisma.jobPosting.update({
            where: { id: parseInt(id) },
            data: { status: 'CLOSED' }
        });
        res.json(job);
    }
    catch (error) {
        console.error('Error closing job:', error);
        res.status(500).json({ error: 'Failed to close job' });
    }
};
exports.closeJob = closeJob;
//# sourceMappingURL=jobController.js.map