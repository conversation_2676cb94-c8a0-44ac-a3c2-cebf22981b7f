"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const authorizeRoles_js_1 = require("../middleware/authorizeRoles.js");
// Import controllers
const system_health_controller_js_1 = require("../controllers/core/system-health.controller.js");
const router = express_1.default.Router();
// All routes require authentication and admin/superadmin role
router.use(route_helpers_js_1.auth);
router.use((0, authorizeRoles_js_1.authorizeRoles)("ADMIN", "SUPERADMIN"));
// System health overview
router.get("/overview", (0, route_helpers_js_1.wrapController)(system_health_controller_js_1.getSystemHealthOverview));
// Service status
router.get("/services", (0, route_helpers_js_1.wrapController)(system_health_controller_js_1.getServiceStatus));
// Resource usage
router.get("/resources", (0, route_helpers_js_1.wrapController)(system_health_controller_js_1.getResourceUsage));
// Performance metrics
router.get("/performance", (0, route_helpers_js_1.wrapController)(system_health_controller_js_1.getPerformanceMetrics));
// System alerts
router.get("/alerts", (0, route_helpers_js_1.wrapController)(system_health_controller_js_1.getSystemAlerts));
exports.default = router;
//# sourceMappingURL=system-health.js.map