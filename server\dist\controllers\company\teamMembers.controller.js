"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCompanyInvitations = exports.cancelTeamInvitation = exports.resendTeamInvitation = exports.createTeamInvitation = exports.removeTeamMember = exports.updateTeamMemberRole = exports.getCompanyTeamMembers = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Get all team members for a company
const getCompanyTeamMembers = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        // Validate required fields
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return next(createHttpError(404, "Company not found"));
        }
        // Get all team members for the company
        const teamMembers = await prisma_js_1.prisma.userCompany.findMany({
            where: {
                companyId: Number(companyId),
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(teamMembers);
    }
    catch (error) {
        console.error("Error getting company team members:", error);
        next(createHttpError(500, "Failed to fetch team members"));
    }
};
exports.getCompanyTeamMembers = getCompanyTeamMembers;
// Update team member role
const updateTeamMemberRole = async (req, res, next) => {
    try {
        const { companyId, memberId } = req.params;
        const { role } = req.body;
        // Validate required fields
        if (!companyId || !memberId) {
            return next(createHttpError(400, "Company ID and Member ID are required"));
        }
        if (!role) {
            return next(createHttpError(400, "Role is required"));
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return next(createHttpError(404, "Company not found"));
        }
        // Check if the user making the request is an admin or owner of the company
        const requesterRole = await prisma_js_1.prisma.userCompany.findFirst({
            where: {
                userId: req.user?.id,
                companyId: Number(companyId),
                role: { in: ["ADMIN", "OWNER"] },
            },
        });
        if (!requesterRole && !req.user?.roles.includes("SUPERADMIN")) {
            return next(createHttpError(403, "You don't have permission to update team member roles"));
        }
        // Update the team member's role
        const updatedMember = await prisma_js_1.prisma.userCompany.update({
            where: {
                id: Number(memberId),
            },
            data: {
                role,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        avatar: true,
                    },
                },
            },
        });
        res.json(updatedMember);
    }
    catch (error) {
        console.error("Error updating team member role:", error);
        next(createHttpError(500, "Failed to update team member role"));
    }
};
exports.updateTeamMemberRole = updateTeamMemberRole;
// Remove team member from company
const removeTeamMember = async (req, res, next) => {
    try {
        const { companyId, memberId } = req.params;
        // Validate required fields
        if (!companyId || !memberId) {
            return next(createHttpError(400, "Company ID and Member ID are required"));
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return next(createHttpError(404, "Company not found"));
        }
        // Check if the user making the request is an admin or owner of the company
        const requesterRole = await prisma_js_1.prisma.userCompany.findFirst({
            where: {
                userId: req.user?.id,
                companyId: Number(companyId),
                role: { in: ["ADMIN", "OWNER"] },
            },
        });
        if (!requesterRole && !req.user?.roles.includes("SUPERADMIN")) {
            return next(createHttpError(403, "You don't have permission to remove team members"));
        }
        // Delete the team member
        await prisma_js_1.prisma.userCompany.delete({
            where: {
                id: Number(memberId),
            },
        });
        res.status(204).send();
    }
    catch (error) {
        console.error("Error removing team member:", error);
        next(createHttpError(500, "Failed to remove team member"));
    }
};
exports.removeTeamMember = removeTeamMember;
// Create invitation for a new team member
const createTeamInvitation = async (req, res, next) => {
    try {
        console.log("createTeamInvitation: Request received", {
            params: req.params,
            body: req.body,
            user: req.user,
        });
        const { companyId } = req.params;
        let { email, role, message } = req.body;
        // Handle role if it's an object (from frontend)
        if (typeof role === "object" && role !== null && role.value) {
            console.log("createTeamInvitation: Role is an object, extracting value", role);
            role = role.value;
        }
        console.log("createTeamInvitation: Extracted data", {
            companyId,
            email,
            role,
            message,
        });
        // Validate required fields
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        if (!email || !role) {
            return next(createHttpError(400, "Email and role are required"));
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return next(createHttpError(404, "Company not found"));
        }
        // Check if the user making the request is an admin or owner of the company
        const requesterRole = await prisma_js_1.prisma.userCompany.findFirst({
            where: {
                userId: req.user?.id,
                companyId: Number(companyId),
                role: { in: ["ADMIN", "OWNER"] },
            },
        });
        if (!requesterRole && !req.user?.roles.includes("SUPERADMIN")) {
            return next(createHttpError(403, "You don't have permission to invite team members"));
        }
        // Check if the email is already invited
        const existingInvitation = await prisma_js_1.prisma.companyInvitation.findFirst({
            where: {
                companyId: Number(companyId),
                email,
                status: "PENDING",
            },
        });
        if (existingInvitation) {
            return next(createHttpError(400, "An invitation has already been sent to this email"));
        }
        // Check if the user is already a member of the company
        const existingUser = await prisma_js_1.prisma.user.findUnique({
            where: { email },
        });
        if (existingUser) {
            const existingMember = await prisma_js_1.prisma.userCompany.findFirst({
                where: {
                    userId: existingUser.id,
                    companyId: Number(companyId),
                },
            });
            if (existingMember) {
                return next(createHttpError(400, "This user is already a member of the company"));
            }
        }
        // Generate a unique invitation token
        const invitationToken = Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15);
        // Set expiration date (30 days from now)
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 30);
        // Create the invitation
        const invitation = await prisma_js_1.prisma.companyInvitation.create({
            data: {
                companyId: Number(companyId),
                email,
                role,
                message,
                invitationToken,
                status: "PENDING",
                expiresAt,
            },
        });
        // Send invitation email to the user
        try {
            console.log("createTeamInvitation: Preparing to send email to", email);
            // Import the sendEmail function
            const { sendEmail } = await import("../../utils/sendEmail.js");
            const CLIENT_URL = process.env.CLIENT_URL || "http://localhost:3000";
            // Create the invitation link
            const invitationLink = `${CLIENT_URL}/auth/signup?token=${invitationToken}&email=${email}&role=${role}&company=${company.name}`;
            const emailSubject = `Invitation to join ${company.name}`;
            const emailBody = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Invitation to join ${company.name}</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              line-height: 1.6;
              color: #333;
              background-color: #f9f9f9;
              margin: 0;
              padding: 0;
            }
            .container {
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              background-color: #1e293b; /* Dark background */
              color: #e2e8f0; /* Light text */
              border-radius: 8px;
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
            .header {
              text-align: center;
              padding: 20px 0;
              border-bottom: 1px solid #475569;
            }
            .content {
              padding: 30px 20px;
            }
            .footer {
              text-align: center;
              padding: 20px 0;
              font-size: 12px;
              color: #94a3b8;
              border-top: 1px solid #475569;
            }
            h1 {
              color: #8b5cf6; /* Purple accent */
              margin-top: 0;
            }
            .button {
              display: inline-block;
              background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
              color: white;
              text-decoration: none;
              padding: 12px 24px;
              border-radius: 4px;
              margin: 20px 0;
              font-weight: 600;
              text-align: center;
            }
            .message-box {
              background-color: #2d3748;
              border-left: 4px solid #8b5cf6;
              padding: 15px;
              margin: 20px 0;
              border-radius: 4px;
            }
            .expiry {
              font-size: 14px;
              color: #94a3b8;
              margin-top: 30px;
              text-align: center;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <img src="https://devskills.ee/img/logo/logo-light.svg" alt="${company.name} Logo" height="40">
            </div>
            <div class="content">
              <h1>You've been invited to join ${company.name}</h1>
              <p>Hello,</p>
              <p>You have been invited to join <strong>${company.name}</strong> as a <strong>${role}</strong>.</p>
              ${message
                ? `<div class="message-box"><p><strong>Message:</strong> ${message}</p></div>`
                : ""}
              <p>Click the button below to accept the invitation and create your account:</p>
              <a href="${invitationLink}" class="button">Accept Invitation</a>
              <p class="expiry">This invitation will expire on ${expiresAt.toLocaleDateString()}.</p>
            </div>
            <div class="footer">
              <p>© ${new Date().getFullYear()} DevSkills Ultimation Studio. All rights reserved.</p>
              <p>If you didn't expect this invitation, you can safely ignore this email.</p>
            </div>
          </div>
        </body>
        </html>
      `;
            // Send the HTML email (passing null as text and the HTML content as the html parameter)
            await sendEmail(email, emailSubject, null, emailBody);
            console.log("createTeamInvitation: Email sent successfully");
        }
        catch (emailError) {
            console.error("Error sending invitation email:", emailError);
            // Don't fail the request if email sending fails
        }
        console.log("createTeamInvitation: Invitation created successfully", invitation);
        res.status(201).json(invitation);
    }
    catch (error) {
        console.error("Error creating team invitation:", error);
        next(createHttpError(500, "Failed to create team invitation"));
    }
};
exports.createTeamInvitation = createTeamInvitation;
// Resend invitation
const resendTeamInvitation = async (req, res, next) => {
    try {
        const { companyId, invitationId } = req.params;
        // Validate required fields
        if (!companyId || !invitationId) {
            return next(createHttpError(400, "Company ID and Invitation ID are required"));
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return next(createHttpError(404, "Company not found"));
        }
        // Check if the invitation exists
        const invitation = await prisma_js_1.prisma.companyInvitation.findUnique({
            where: { id: Number(invitationId) },
        });
        if (!invitation || invitation.companyId !== Number(companyId)) {
            return next(createHttpError(404, "Invitation not found"));
        }
        // Check if the invitation is still pending
        if (invitation.status !== "PENDING") {
            return next(createHttpError(400, "Invitation is no longer pending"));
        }
        // Update the invitation with a new expiration date
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 30);
        const updatedInvitation = await prisma_js_1.prisma.companyInvitation.update({
            where: { id: Number(invitationId) },
            data: {
                expiresAt,
            },
        });
        // TODO: Resend invitation email to the user
        res.json(updatedInvitation);
    }
    catch (error) {
        console.error("Error resending team invitation:", error);
        next(createHttpError(500, "Failed to resend team invitation"));
    }
};
exports.resendTeamInvitation = resendTeamInvitation;
// Cancel invitation
const cancelTeamInvitation = async (req, res, next) => {
    try {
        const { companyId, invitationId } = req.params;
        // Validate required fields
        if (!companyId || !invitationId) {
            return next(createHttpError(400, "Company ID and Invitation ID are required"));
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return next(createHttpError(404, "Company not found"));
        }
        // Check if the invitation exists
        const invitation = await prisma_js_1.prisma.companyInvitation.findUnique({
            where: { id: Number(invitationId) },
        });
        if (!invitation || invitation.companyId !== Number(companyId)) {
            return next(createHttpError(404, "Invitation not found"));
        }
        // Delete the invitation
        await prisma_js_1.prisma.companyInvitation.delete({
            where: { id: Number(invitationId) },
        });
        res.status(204).send();
    }
    catch (error) {
        console.error("Error cancelling team invitation:", error);
        next(createHttpError(500, "Failed to cancel team invitation"));
    }
};
exports.cancelTeamInvitation = cancelTeamInvitation;
// Get all pending invitations for a company
const getCompanyInvitations = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        // Validate required fields
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        // Check if company exists
        const company = await prisma_js_1.prisma.company.findUnique({
            where: { id: Number(companyId) },
        });
        if (!company) {
            return next(createHttpError(404, "Company not found"));
        }
        // Get all pending invitations for the company
        const invitations = await prisma_js_1.prisma.companyInvitation.findMany({
            where: {
                companyId: Number(companyId),
                status: "PENDING",
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        res.json(invitations);
    }
    catch (error) {
        console.error("Error getting company invitations:", error);
        next(createHttpError(500, "Failed to fetch company invitations"));
    }
};
exports.getCompanyInvitations = getCompanyInvitations;
//# sourceMappingURL=teamMembers.controller.js.map