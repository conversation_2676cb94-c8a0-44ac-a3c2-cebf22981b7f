"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendChatMessage = exports.createChatConversation = exports.getChatMessages = exports.getChatConversations = exports.getCompanyMembers = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
// Get company members for chat (users from the same company)
const getCompanyMembers = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Get the user's company through UserCompany relationship
        const userCompany = await prisma_js_1.prisma.userCompany.findFirst({
            where: {
                userId: userId,
                status: "ACTIVE",
            },
            select: { companyId: true },
        });
        if (!userCompany?.companyId) {
            res.status(200).json([]); // Return empty array if user has no company
            return;
        }
        // Get all users from the same company (excluding the current user)
        const companyMembers = await prisma_js_1.prisma.user.findMany({
            where: {
                companies: {
                    some: {
                        companyId: userCompany.companyId,
                        status: "ACTIVE",
                    },
                },
                id: {
                    not: userId,
                },
            },
            select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                avatar: true,
            },
            orderBy: [{ firstName: "asc" }, { lastName: "asc" }],
        });
        res.status(200).json(companyMembers);
    }
    catch (error) {
        console.error("Error fetching company members:", error);
        res.status(500).json({ error: "Failed to fetch company members" });
    }
};
exports.getCompanyMembers = getCompanyMembers;
// Get all chat conversations for the authenticated user
const getChatConversations = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Get all conversations where the user is a participant
        const conversations = await prisma_js_1.prisma.chatConversation.findMany({
            where: {
                participants: {
                    some: {
                        userId,
                        leftAt: null, // Only include conversations the user hasn't left
                    },
                },
            },
            include: {
                participants: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                email: true,
                            },
                        },
                    },
                },
                messages: {
                    orderBy: {
                        createdAt: "desc",
                    },
                    take: 1, // Get only the latest message
                },
            },
            orderBy: {
                updatedAt: "desc",
            },
        });
        // Transform the data to include last message preview and unread count
        const transformedConversations = await Promise.all(conversations.map(async (conversation) => {
            // Get unread count
            const unreadCount = await prisma_js_1.prisma.chatMessage.count({
                where: {
                    conversationId: conversation.id,
                    senderId: {
                        not: userId,
                    },
                    status: {
                        in: ["SENT", "DELIVERED"],
                    },
                },
            });
            // Get participant IDs
            const participantIds = conversation.participants.map((participant) => participant.userId);
            // Get last message preview
            const lastMessage = conversation.messages[0];
            const lastMessagePreview = lastMessage ? lastMessage.content : null;
            const lastMessageTime = lastMessage ? lastMessage.createdAt : null;
            return {
                id: conversation.id,
                name: conversation.name,
                type: conversation.type,
                participants: participantIds,
                lastMessageId: lastMessage ? lastMessage.id : null,
                lastMessagePreview,
                lastMessageTime,
                unreadCount,
                createdAt: conversation.createdAt,
                updatedAt: conversation.updatedAt,
            };
        }));
        res.status(200).json(transformedConversations);
    }
    catch (error) {
        console.error("Error fetching chat conversations:", error);
        res.status(500).json({ error: "Failed to fetch chat conversations" });
    }
};
exports.getChatConversations = getChatConversations;
// Get messages for a specific conversation
const getChatMessages = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { conversationId } = req.params;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Verify the user is a participant in the conversation
        const participant = await prisma_js_1.prisma.chatParticipant.findFirst({
            where: {
                conversationId: Number(conversationId),
                userId,
                leftAt: null,
            },
        });
        if (!participant) {
            res
                .status(403)
                .json({ error: "You are not a participant in this conversation" });
            return;
        }
        // Get messages
        const messages = await prisma_js_1.prisma.chatMessage.findMany({
            where: {
                conversationId: Number(conversationId),
            },
            orderBy: {
                createdAt: "asc",
            },
        });
        // Mark messages as read
        await prisma_js_1.prisma.chatMessage.updateMany({
            where: {
                conversationId: Number(conversationId),
                senderId: {
                    not: userId,
                },
                status: {
                    in: ["SENT", "DELIVERED"],
                },
            },
            data: {
                status: "READ",
            },
        });
        res.status(200).json(messages);
    }
    catch (error) {
        console.error("Error fetching chat messages:", error);
        res.status(500).json({ error: "Failed to fetch chat messages" });
    }
};
exports.getChatMessages = getChatMessages;
// Create a new conversation
const createChatConversation = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { name, type, participants } = req.body;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Validate required fields
        if (!type || !participants || !Array.isArray(participants)) {
            res.status(400).json({ error: "Missing required fields" });
            return;
        }
        // For direct messages, ensure there are exactly 2 participants
        if (type === "DIRECT" && participants.length !== 1) {
            res.status(400).json({
                error: "Direct messages must have exactly one other participant",
            });
            return;
        }
        // For group chats, ensure there is a name and at least 2 other participants
        if (type === "GROUP") {
            if (!name) {
                res.status(400).json({ error: "Group chats must have a name" });
                return;
            }
            if (participants.length < 2) {
                res.status(400).json({
                    error: "Group chats must have at least 2 other participants",
                });
                return;
            }
        }
        // Create the conversation
        const conversation = await prisma_js_1.prisma.chatConversation.create({
            data: {
                name: type === "GROUP" ? name : null,
                type,
                participants: {
                    create: [
                        // Add the current user
                        {
                            userId,
                        },
                        // Add the other participants
                        ...participants.map((participantId) => ({
                            userId: participantId,
                        })),
                    ],
                },
            },
            include: {
                participants: true,
            },
        });
        res.status(201).json(conversation);
    }
    catch (error) {
        console.error("Error creating chat conversation:", error);
        res.status(500).json({ error: "Failed to create chat conversation" });
    }
};
exports.createChatConversation = createChatConversation;
// Send a message in a conversation
const sendChatMessage = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { conversationId } = req.params;
        const { content } = req.body;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Validate required fields
        if (!content) {
            res.status(400).json({ error: "Message content is required" });
            return;
        }
        // Verify the user is a participant in the conversation
        const participant = await prisma_js_1.prisma.chatParticipant.findFirst({
            where: {
                conversationId: Number(conversationId),
                userId,
                leftAt: null,
            },
        });
        if (!participant) {
            res
                .status(403)
                .json({ error: "You are not a participant in this conversation" });
            return;
        }
        // Create the message
        const message = await prisma_js_1.prisma.chatMessage.create({
            data: {
                conversationId: Number(conversationId),
                senderId: userId,
                content,
                status: "SENT",
            },
        });
        // Update the conversation's updatedAt timestamp
        await prisma_js_1.prisma.chatConversation.update({
            where: {
                id: Number(conversationId),
            },
            data: {
                updatedAt: new Date(),
            },
        });
        // In a real implementation, we would notify other participants
        // For now, we'll just mark the message as delivered for demonstration
        await prisma_js_1.prisma.chatMessage.update({
            where: {
                id: message.id,
            },
            data: {
                status: "DELIVERED",
            },
        });
        res.status(201).json(message);
    }
    catch (error) {
        console.error("Error sending chat message:", error);
        res.status(500).json({ error: "Failed to send chat message" });
    }
};
exports.sendChatMessage = sendChatMessage;
//# sourceMappingURL=chat.controller.js.map