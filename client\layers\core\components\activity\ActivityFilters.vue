<!-- client/layers/core/components/activity/ActivityFilters.vue -->

<template>
  <BaseCard class="p-4">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Search -->
      <BaseInput
        v-model="filters.search"
        placeholder="Search activities..."
        icon="solar:magnifer-line-duotone"
        class="w-full"
        @update:model-value="updateFilters"
      />

      <!-- Type Filter -->
      <BaseSelect
        v-model="filters.type"
        placeholder="Filter by Type"
        class="w-full"
        @update:model-value="updateFilters"
      >
        <option value="">All Types</option>
        <option
          v-for="type in activityTypes"
          :key="type.value"
          :value="type.value"
        >
          {{ type.label }}
        </option>
      </BaseSelect>

      <!-- User Filter -->
      <BaseSelect
        v-model="filters.user"
        placeholder="Filter by User"
        class="w-full"
        @update:model-value="updateFilters"
      >
        <option value="">All Users</option>
        <option v-for="user in users" :key="user.id" :value="user.id">
          {{ user.name }}
        </option>
      </BaseSelect>

      <!-- Date Filter -->
      <BaseSelect
        v-model="filters.date"
        placeholder="Filter by Date"
        class="w-full"
        @update:model-value="updateFilters"
      >
        <option value="">All Dates</option>
        <option value="today">Today</option>
        <option value="yesterday">Yesterday</option>
        <option value="last7days">Last 7 Days</option>
        <option value="last30days">Last 30 Days</option>
        <option value="thisMonth">This Month</option>
        <option value="lastMonth">Last Month</option>
      </BaseSelect>
    </div>

    <!-- Applied Filters -->
    <div v-if="hasActiveFilters" class="flex flex-wrap gap-2 mt-3">
      <BaseTag
        v-if="filters.search"
        color="info"
        flavor="pastel"
        size="sm"
        removable
        @remove="removeFilter('search')"
      >
        Search: {{ filters.search }}
      </BaseTag>

      <BaseTag
        v-if="filters.type"
        color="primary"
        flavor="pastel"
        size="sm"
        removable
        @remove="removeFilter('type')"
      >
        Type: {{ getTypeLabel(filters.type) }}
      </BaseTag>

      <BaseTag
        v-if="filters.user"
        color="success"
        flavor="pastel"
        size="sm"
        removable
        @remove="removeFilter('user')"
      >
        User: {{ getUserName(filters.user) }}
      </BaseTag>

      <BaseTag
        v-if="filters.date"
        color="warning"
        flavor="pastel"
        size="sm"
        removable
        @remove="removeFilter('date')"
      >
        Date: {{ getDateLabel(filters.date) }}
      </BaseTag>

      <BaseButton
        v-if="hasActiveFilters"
        color="muted"
        flavor="link"
        size="sm"
        @click="clearAllFilters"
      >
        Clear All
      </BaseButton>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";

const props = defineProps({
  initialFilters: {
    type: Object,
    default: () => ({
      search: "",
      type: "",
      user: "",
      date: "",
    }),
  },
  activityTypes: {
    type: Array,
    default: () => [],
  },
  users: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["update:filters"]);

const filters = ref({
  search: props.initialFilters.search || "",
  type: props.initialFilters.type || "",
  user: props.initialFilters.user || "",
  date: props.initialFilters.date || "",
});

const hasActiveFilters = computed(() => {
  return (
    filters.value.search ||
    filters.value.type ||
    filters.value.user ||
    filters.value.date
  );
});

const getTypeLabel = (typeValue) => {
  const type = props.activityTypes.find((t) => t.value === typeValue);
  return type ? type.label : typeValue;
};

const getUserName = (userId) => {
  const user = props.users.find((u) => u.id === userId);
  return user ? user.name : userId;
};

const getDateLabel = (dateValue) => {
  const dateMap = {
    today: "Today",
    yesterday: "Yesterday",
    last7days: "Last 7 Days",
    last30days: "Last 30 Days",
    thisMonth: "This Month",
    lastMonth: "Last Month",
  };

  return dateMap[dateValue] || dateValue;
};

const updateFilters = () => {
  emit("update:filters", { ...filters.value });
};

const removeFilter = (filterName) => {
  filters.value[filterName] = "";
  updateFilters();
};

const clearAllFilters = () => {
  filters.value = {
    search: "",
    type: "",
    user: "",
    date: "",
  };
  updateFilters();
};

// Watch for changes in initialFilters
watch(
  () => props.initialFilters,
  (newFilters) => {
    filters.value = { ...newFilters };
  },
  { deep: true }
);
</script>
