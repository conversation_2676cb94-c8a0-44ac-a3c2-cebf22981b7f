"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const company_controller_js_1 = require("../controllers/company/company.controller.js");
const clientRelationship_controller_js_1 = require("../controllers/company/clientRelationship.controller.js");
const supplierRelationship_controller_js_1 = require("../controllers/company/supplierRelationship.controller.js");
const companyTransition_controller_js_1 = require("../controllers/company/companyTransition.controller.js");
const workforce_controller_js_1 = require("../controllers/company/workforce.controller.js");
const contact_controller_js_1 = require("../controllers/company/contact.controller.js");
const teamMembers_controller_js_1 = require("../controllers/company/teamMembers.controller.js");
const documents_controller_js_1 = require("../controllers/company/documents.controller.js");
const router = express_1.default.Router();
// Main company routes
router.get("/", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(company_controller_js_1.getAllCompanies));
router.post("/", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(company_controller_js_1.createCompany));
router.get("/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(company_controller_js_1.getCompanyById));
router.put("/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(company_controller_js_1.updateCompany));
router.put("/:id/address", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(company_controller_js_1.updateCompany));
router.delete("/:id", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(company_controller_js_1.deleteCompany));
// Workforce needs routes
router.get("/:id/workforce-needs", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workforce_controller_js_1.getCompanyWorkforceNeeds));
router.post("/:id/workforce-needs", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workforce_controller_js_1.createWorkforceNeed));
router.put("/workforce-needs/:needId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workforce_controller_js_1.updateWorkforceNeed));
router.delete("/workforce-needs/:needId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(workforce_controller_js_1.deleteWorkforceNeed));
// Contacts routes
router.get("/:id/contacts", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(contact_controller_js_1.getCompanyContacts));
router.post("/:id/contacts", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(contact_controller_js_1.createContact));
router.put("/contacts/:contactId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(contact_controller_js_1.updateContact));
router.delete("/contacts/:contactId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(contact_controller_js_1.deleteContact));
// Team Members routes
router.get("/:companyId/members", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(teamMembers_controller_js_1.getCompanyTeamMembers));
router.put("/:companyId/members/:memberId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(teamMembers_controller_js_1.updateTeamMemberRole));
router.delete("/:companyId/members/:memberId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(teamMembers_controller_js_1.removeTeamMember));
router.post("/:companyId/invitations", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(teamMembers_controller_js_1.createTeamInvitation));
router.post("/:companyId/invitations/:invitationId/resend", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(teamMembers_controller_js_1.resendTeamInvitation));
router.delete("/:companyId/invitations/:invitationId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(teamMembers_controller_js_1.cancelTeamInvitation));
router.get("/:companyId/invitations", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(teamMembers_controller_js_1.getCompanyInvitations));
// Documents routes
router.get("/:companyId/documents", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.getCompanyDocuments));
router.post("/:companyId/documents", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.uploadCompanyDocument));
router.get("/:companyId/documents/:documentId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.getDocumentById));
router.delete("/:companyId/documents/:documentId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(documents_controller_js_1.deleteDocument));
// Client relationship routes
router.get("/:companyId/client-relationships", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(clientRelationship_controller_js_1.getClientRelationships));
router.post("/:companyId/client-relationships", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(clientRelationship_controller_js_1.createClientRelationship));
router.put("/client-relationships/:relationshipId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(clientRelationship_controller_js_1.updateClientRelationship));
router.delete("/client-relationships/:relationshipId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(clientRelationship_controller_js_1.deleteClientRelationship));
// Supplier relationship routes
router.get("/:companyId/supplier-relationships", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(supplierRelationship_controller_js_1.getSupplierRelationships));
router.post("/:companyId/supplier-relationships", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(supplierRelationship_controller_js_1.createSupplierRelationship));
router.put("/supplier-relationships/:relationshipId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(supplierRelationship_controller_js_1.updateSupplierRelationship));
router.delete("/supplier-relationships/:relationshipId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(supplierRelationship_controller_js_1.deleteSupplierRelationship));
// Company transition routes
router.get("/:companyId/transition", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(companyTransition_controller_js_1.getCompanyTransition));
router.post("/:companyId/transition", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(companyTransition_controller_js_1.createCompanyTransition));
router.put("/transition/:transitionId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(companyTransition_controller_js_1.updateCompanyTransition));
exports.default = router;
//# sourceMappingURL=companies.js.map