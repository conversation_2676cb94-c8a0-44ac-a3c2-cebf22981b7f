"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../../utils/route-helpers.js");
const interviewController_js_1 = require("../../controllers/recruitment/interviewController.js");
const router = express_1.default.Router();
// All routes require authentication
router.use(route_helpers_js_1.auth);
// Interview routes
router.get("/company/:companyId", (0, route_helpers_js_1.wrapController)(interviewController_js_1.getInterviews));
router.get("/company/:companyId/upcoming", (0, route_helpers_js_1.wrapController)(interview<PERSON>ontroller_js_1.getUpcomingInterviews));
router.get("/company/:companyId/status/:status", (0, route_helpers_js_1.wrapController)(interviewController_js_1.getInterviewsByStatus));
router.get("/:id", (0, route_helpers_js_1.wrapController)(interviewController_js_1.getInterview));
router.post("/application/:applicationId", (0, route_helpers_js_1.wrapController)(interviewController_js_1.scheduleInterview));
router.put("/:id", (0, route_helpers_js_1.wrapController)(interviewController_js_1.updateInterview));
router.patch("/:id/cancel", (0, route_helpers_js_1.wrapController)(interviewController_js_1.cancelInterview));
router.patch("/:id/complete", (0, route_helpers_js_1.wrapController)(interviewController_js_1.completeInterview));
router.patch("/:id/reschedule", (0, route_helpers_js_1.wrapController)(interviewController_js_1.rescheduleInterview));
exports.default = router;
//# sourceMappingURL=interviews.js.map