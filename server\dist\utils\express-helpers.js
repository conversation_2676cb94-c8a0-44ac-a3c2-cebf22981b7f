"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.wrapResponseHandler = exports.wrapMiddleware = exports.asHandler = exports.asyncHandler = void 0;
/**
 * Wraps an async request handler to catch errors and pass them to the next middleware
 */
const asyncHandler = (fn) => {
    return async (req, res, next) => {
        try {
            await fn(req, res, next);
        }
        catch (error) {
            next(error);
        }
    };
};
exports.asyncHandler = asyncHandler;
/**
 * Converts any function to a valid Express request handler
 * This helps with TypeScript compatibility issues
 */
const asHandler = (fn) => {
    return fn;
};
exports.asHandler = asHandler;
/**
 * Wraps a middleware to ensure it's compatible with Express types
 * This is particularly useful for the authenticateToken middleware
 */
const wrapMiddleware = (middleware) => {
    return middleware;
};
exports.wrapMiddleware = wrapMiddleware;
/**
 * Wraps a response handler to ensure consistent API responses
 */
const wrapResponseHandler = (fn) => {
    return async (req, res, next) => {
        try {
            const result = await fn(req, res, next);
            // If the function already sent a response, don't do anything
            if (res.headersSent) {
                return;
            }
            // If the function returned a value, send it as a JSON response
            if (result !== undefined) {
                return res.json({
                    success: true,
                    data: result,
                });
            }
        }
        catch (error) {
            // If an error occurred, pass it to the next middleware
            next(error);
        }
    };
};
exports.wrapResponseHandler = wrapResponseHandler;
//# sourceMappingURL=express-helpers.js.map