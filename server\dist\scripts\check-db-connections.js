"use strict";
/**
 * <PERSON>ript to check database connections
 *
 * Usage:
 * node scripts/check-db-connections.js
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
// Create a new Prisma client
const prisma = new client_1.PrismaClient();
async function checkConnections() {
    try {
        console.log('Checking database connections...');
        // Query to get current connection count
        const result = await prisma.$queryRaw `SELECT 
      count(*) as connection_count,
      count(CASE WHEN state = 'active' THEN 1 END) as active_connections,
      count(CASE WHEN state = 'idle' THEN 1 END) as idle_connections
    FROM pg_stat_activity 
    WHERE datname = current_database()`;
        // Get max connections from PostgreSQL config
        const maxConnectionsResult = await prisma.$queryRaw `SHOW max_connections`;
        // Format results
        const connectionCount = parseInt(result[0].connection_count, 10);
        const activeConnections = parseInt(result[0].active_connections, 10);
        const idleConnections = parseInt(result[0].idle_connections, 10);
        const maxConnections = parseInt(maxConnectionsResult[0].max_connections, 10);
        const connectionPercentage = (connectionCount / maxConnections) * 100;
        console.log('\n=== DATABASE CONNECTION REPORT ===');
        console.log(`Total connections: ${connectionCount}/${maxConnections} (${connectionPercentage.toFixed(2)}%)`);
        console.log(`Active connections: ${activeConnections}`);
        console.log(`Idle connections: ${idleConnections}`);
        // Get detailed connection information
        const detailedConnections = await prisma.$queryRaw `
      SELECT 
        pid,
        usename as username,
        application_name,
        client_addr as client_address,
        state,
        query,
        backend_type,
        EXTRACT(EPOCH FROM (now() - state_change)) as idle_time_seconds
      FROM pg_stat_activity
      WHERE datname = current_database()
      ORDER BY state_change DESC
    `;
        console.log('\n=== ACTIVE QUERIES ===');
        detailedConnections
            .filter(conn => conn.state === 'active' && conn.query !== null && conn.query !== '<insufficient privilege>')
            .forEach((conn, i) => {
            console.log(`\n--- Connection ${i + 1} ---`);
            console.log(`PID: ${conn.pid}`);
            console.log(`User: ${conn.username}`);
            console.log(`Application: ${conn.application_name}`);
            console.log(`Client: ${conn.client_address}`);
            console.log(`Query: ${conn.query.substring(0, 100)}${conn.query.length > 100 ? '...' : ''}`);
        });
        console.log('\n=== IDLE CONNECTIONS ===');
        console.log(`Total idle connections: ${idleConnections}`);
        const longIdleConnections = detailedConnections
            .filter(conn => conn.state === 'idle' && conn.idle_time_seconds > 60);
        console.log(`Long idle connections (>60s): ${longIdleConnections.length}`);
        if (longIdleConnections.length > 0) {
            console.log('\nTop 5 longest idle connections:');
            longIdleConnections
                .sort((a, b) => b.idle_time_seconds - a.idle_time_seconds)
                .slice(0, 5)
                .forEach((conn, i) => {
                const idleMinutes = Math.floor(conn.idle_time_seconds / 60);
                console.log(`${i + 1}. PID ${conn.pid} - ${conn.application_name || 'Unknown'} - Idle for ${idleMinutes} minutes`);
            });
        }
        console.log('\n=== RECOMMENDATIONS ===');
        if (connectionPercentage > 80) {
            console.log('⚠️ WARNING: Connection usage is very high (>80%)!');
            console.log('Consider:');
            console.log('1. Implementing connection pooling');
            console.log('2. Checking for connection leaks');
            console.log('3. Increasing max_connections in PostgreSQL config');
        }
        else if (connectionPercentage > 50) {
            console.log('⚠️ Connection usage is moderate (>50%).');
            console.log('Consider monitoring for potential issues.');
        }
        else {
            console.log('✅ Connection usage is at a healthy level.');
        }
        if (longIdleConnections.length > 10) {
            console.log('\n⚠️ High number of idle connections detected!');
            console.log('Consider:');
            console.log('1. Implementing proper connection cleanup');
            console.log('2. Setting shorter connection timeouts');
            console.log('3. Using a connection pool with proper idle timeout settings');
        }
    }
    catch (error) {
        console.error('Error checking database connections:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
// Run the check
checkConnections();
//# sourceMappingURL=check-db-connections.js.map