{"version": 3, "file": "documents.controller.js", "sourceRoot": "", "sources": ["../../../controllers/company/documents.controller.ts"], "names": [], "mappings": ";;;AACA,mDAA6C;AAE7C,2DAA2D;AAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAC7B,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAEzB,8DAA8D;AAC9D,SAAS,eAAe,CAAC,UAAkB,EAAE,OAAe;IAC1D,MAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IACtC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;IAC9B,OAAO,KAAK,CAAC;AACf,CAAC;AAyBD,oCAAoC;AACpC,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC;IACjC,WAAW,EAAE,CAAC,GAAQ,EAAE,IAAS,EAAE,EAAO,EAAE,EAAE;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,mBAAmB,CAAC,CAAC;QAEhE,uCAAuC;QACvC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACtB,CAAC;IACD,QAAQ,EAAE,CAAC,IAAS,EAAE,IAAS,EAAE,EAAO,EAAE,EAAE;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QACxE,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5C,EAAE,CAAC,IAAI,EAAE,YAAY,GAAG,GAAG,CAAC,CAAC;IAC/B,CAAC;CACF,CAAC,CAAC;AAEH,MAAM,MAAM,GAAG,MAAM,CAAC;IACpB,OAAO;IACP,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,aAAa;KAC1C;IACD,UAAU,EAAE,CAAC,IAAS,EAAE,KAAU,EAAE,EAAO,EAAE,EAAE;QAC7C,gCAAgC;QAChC,oDAAoD;QACpD,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACjB,CAAC;CACF,CAAC,CAAC;AAEH,kCAAkC;AAC3B,MAAM,mBAAmB,GAAG,KAAK,EACtC,GAAyB,EACzB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAEjC,2BAA2B;QAC3B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,0BAA0B;QAC1B,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,oCAAoC;QACpC,MAAM,SAAS,GAAG,MAAM,kBAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C,KAAK,EAAE;gBACL,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;aAC7B;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACtB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mCAAmC,CAAC,CAAC,CAAC;IAClE,CAAC;AACH,CAAC,CAAC;AArCW,QAAA,mBAAmB,uBAqC9B;AAEF,kCAAkC;AAC3B,MAAM,qBAAqB,GAAG,KAAK,EACxC,GAAyB,EACzB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAEjC,2BAA2B;QAC3B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,0BAA0B;QAC1B,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,uCAAuC;QACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAQ,EAAE,EAAE;YACjD,IAAI,GAAG,EAAE,CAAC;gBACR,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;gBAChD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YACtD,CAAC;YAED,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAEjD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC,CAAC;YACtE,CAAC;YAED,yCAAyC;YACzC,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,uBAAuB,CAAC;YAClE,MAAM,WAAW,GAAG,GAAG,OAAO,sBAAsB,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAExE,2CAA2C;YAC3C,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC5C,IAAI,EAAE;oBACJ,IAAI;oBACJ,GAAG,EAAE,WAAW;oBAChB,IAAI,EAAE,OAAO;oBACb,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;oBAC5B,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;oBACnB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;oBAC3B,UAAU,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC;oBAC7B,WAAW;oBACX,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,SAAS;oBACpC,QAAQ,EAAE,QAAQ,IAAI,SAAS;iBAChC;aACF,CAAC,CAAC;YAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mCAAmC,CAAC,CAAC,CAAC;IAClE,CAAC;AACH,CAAC,CAAC;AAjEW,QAAA,qBAAqB,yBAiEhC;AAEF,0BAA0B;AACnB,MAAM,eAAe,GAAG,KAAK,EAClC,GAAyB,EACzB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAE7C,2BAA2B;QAC3B,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9B,OAAO,IAAI,CACT,eAAe,CAAC,GAAG,EAAE,yCAAyC,CAAC,CAChE,CAAC;QACJ,CAAC;QAED,0BAA0B;QAC1B,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,mBAAmB;QACnB,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,UAAU,CAAC;aACvB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1D,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC,CAAC;IACzD,CAAC;AACH,CAAC,CAAC;AAxCW,QAAA,eAAe,mBAwC1B;AAEF,oBAAoB;AACb,MAAM,cAAc,GAAG,KAAK,EACjC,GAAyB,EACzB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAE7C,2BAA2B;QAC3B,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9B,OAAO,IAAI,CACT,eAAe,CAAC,GAAG,EAAE,yCAAyC,CAAC,CAChE,CAAC;QACJ,CAAC;QAED,0BAA0B;QAC1B,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,mBAAmB;QACnB,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,UAAU,CAAC;aACvB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1D,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,kCAAkC;QAClC,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC;YACjB,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,uBAAuB,CAAC;YAClE,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;iBAC1B,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;iBACpB,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAElC,IAAI,CAAC;gBACH,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC5B,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAC1B,CAAC;YACH,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC;gBAC3C,oDAAoD;YACtD,CAAC;QACH,CAAC;QAED,wCAAwC;QACxC,MAAM,kBAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,UAAU,CAAC;aACvB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,2BAA2B,CAAC,CAAC,CAAC;IAC1D,CAAC;AACH,CAAC,CAAC;AAhEW,QAAA,cAAc,kBAgEzB"}