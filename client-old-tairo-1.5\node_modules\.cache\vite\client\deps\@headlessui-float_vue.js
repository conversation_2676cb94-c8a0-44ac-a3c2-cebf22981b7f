import "./chunk-IKZWERSR.js";

// node_modules/.pnpm/@headlessui-float+vue@0.15.0_@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2___vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui-float/vue/dist/headlessui-float.mjs
import { ref as h5, Fragment as ye3, unref as we, watch as P5, onMounted as U6, onBeforeUnmount as be3, computed as O8, watchEffect as te5, mergeProps as N12, cloneVNode as k8, h as b4, toRef as J6, shallowRef as Fe2, nextTick as Ce, provide as $7, defineComponent as R10, inject as W6, createCommentVNode as M4, Transition as Te3 } from "vue";

// node_modules/.pnpm/@tanstack+virtual-core@3.13.4/node_modules/@tanstack/virtual-core/dist/esm/utils.js
function memo(getDeps, fn, opts) {
  let deps = opts.initialDeps ?? [];
  let result;
  function memoizedFunction() {
    var _a, _b, _c, _d;
    let depTime;
    if (opts.key && ((_a = opts.debug) == null ? void 0 : _a.call(opts))) depTime = Date.now();
    const newDeps = getDeps();
    const depsChanged = newDeps.length !== deps.length || newDeps.some((dep, index) => deps[index] !== dep);
    if (!depsChanged) {
      return result;
    }
    deps = newDeps;
    let resultTime;
    if (opts.key && ((_b = opts.debug) == null ? void 0 : _b.call(opts))) resultTime = Date.now();
    result = fn(...newDeps);
    if (opts.key && ((_c = opts.debug) == null ? void 0 : _c.call(opts))) {
      const depEndTime = Math.round((Date.now() - depTime) * 100) / 100;
      const resultEndTime = Math.round((Date.now() - resultTime) * 100) / 100;
      const resultFpsPercentage = resultEndTime / 16;
      const pad = (str, num) => {
        str = String(str);
        while (str.length < num) {
          str = " " + str;
        }
        return str;
      };
      console.info(
        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,
        `
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(
          0,
          Math.min(120 - 120 * resultFpsPercentage, 120)
        )}deg 100% 31%);`,
        opts == null ? void 0 : opts.key
      );
    }
    (_d = opts == null ? void 0 : opts.onChange) == null ? void 0 : _d.call(opts, result);
    return result;
  }
  memoizedFunction.updateDeps = (newDeps) => {
    deps = newDeps;
  };
  return memoizedFunction;
}
function notUndefined(value, msg) {
  if (value === void 0) {
    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : ""}`);
  } else {
    return value;
  }
}
var approxEqual = (a8, b5) => Math.abs(a8 - b5) < 1;
var debounce = (targetWindow, fn, ms) => {
  let timeoutId;
  return function(...args) {
    targetWindow.clearTimeout(timeoutId);
    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms);
  };
};

// node_modules/.pnpm/@tanstack+virtual-core@3.13.4/node_modules/@tanstack/virtual-core/dist/esm/index.js
var defaultKeyExtractor = (index) => index;
var defaultRangeExtractor = (range) => {
  const start = Math.max(range.startIndex - range.overscan, 0);
  const end = Math.min(range.endIndex + range.overscan, range.count - 1);
  const arr = [];
  for (let i13 = start; i13 <= end; i13++) {
    arr.push(i13);
  }
  return arr;
};
var observeElementRect = (instance, cb) => {
  const element = instance.scrollElement;
  if (!element) {
    return;
  }
  const targetWindow = instance.targetWindow;
  if (!targetWindow) {
    return;
  }
  const handler = (rect) => {
    const { width, height } = rect;
    cb({ width: Math.round(width), height: Math.round(height) });
  };
  handler(element.getBoundingClientRect());
  if (!targetWindow.ResizeObserver) {
    return () => {
    };
  }
  const observer = new targetWindow.ResizeObserver((entries) => {
    const run = () => {
      const entry = entries[0];
      if (entry == null ? void 0 : entry.borderBoxSize) {
        const box = entry.borderBoxSize[0];
        if (box) {
          handler({ width: box.inlineSize, height: box.blockSize });
          return;
        }
      }
      handler(element.getBoundingClientRect());
    };
    instance.options.useAnimationFrameWithResizeObserver ? requestAnimationFrame(run) : run();
  });
  observer.observe(element, { box: "border-box" });
  return () => {
    observer.unobserve(element);
  };
};
var addEventListenerOptions = {
  passive: true
};
var supportsScrollend = typeof window == "undefined" ? true : "onscrollend" in window;
var observeElementOffset = (instance, cb) => {
  const element = instance.scrollElement;
  if (!element) {
    return;
  }
  const targetWindow = instance.targetWindow;
  if (!targetWindow) {
    return;
  }
  let offset3 = 0;
  const fallback = instance.options.useScrollendEvent && supportsScrollend ? () => void 0 : debounce(
    targetWindow,
    () => {
      cb(offset3, false);
    },
    instance.options.isScrollingResetDelay
  );
  const createHandler = (isScrolling) => () => {
    const { horizontal, isRtl } = instance.options;
    offset3 = horizontal ? element["scrollLeft"] * (isRtl && -1 || 1) : element["scrollTop"];
    fallback();
    cb(offset3, isScrolling);
  };
  const handler = createHandler(true);
  const endHandler = createHandler(false);
  endHandler();
  element.addEventListener("scroll", handler, addEventListenerOptions);
  const registerScrollendEvent = instance.options.useScrollendEvent && supportsScrollend;
  if (registerScrollendEvent) {
    element.addEventListener("scrollend", endHandler, addEventListenerOptions);
  }
  return () => {
    element.removeEventListener("scroll", handler);
    if (registerScrollendEvent) {
      element.removeEventListener("scrollend", endHandler);
    }
  };
};
var measureElement = (element, entry, instance) => {
  if (entry == null ? void 0 : entry.borderBoxSize) {
    const box = entry.borderBoxSize[0];
    if (box) {
      const size3 = Math.round(
        box[instance.options.horizontal ? "inlineSize" : "blockSize"]
      );
      return size3;
    }
  }
  return Math.round(
    element.getBoundingClientRect()[instance.options.horizontal ? "width" : "height"]
  );
};
var elementScroll = (offset3, {
  adjustments = 0,
  behavior
}, instance) => {
  var _a, _b;
  const toOffset = offset3 + adjustments;
  (_b = (_a = instance.scrollElement) == null ? void 0 : _a.scrollTo) == null ? void 0 : _b.call(_a, {
    [instance.options.horizontal ? "left" : "top"]: toOffset,
    behavior
  });
};
var Virtualizer = class {
  constructor(opts) {
    this.unsubs = [];
    this.scrollElement = null;
    this.targetWindow = null;
    this.isScrolling = false;
    this.scrollToIndexTimeoutId = null;
    this.measurementsCache = [];
    this.itemSizeCache = /* @__PURE__ */ new Map();
    this.pendingMeasuredCacheIndexes = [];
    this.scrollRect = null;
    this.scrollOffset = null;
    this.scrollDirection = null;
    this.scrollAdjustments = 0;
    this.elementsCache = /* @__PURE__ */ new Map();
    this.observer = /* @__PURE__ */ (() => {
      let _ro = null;
      const get = () => {
        if (_ro) {
          return _ro;
        }
        if (!this.targetWindow || !this.targetWindow.ResizeObserver) {
          return null;
        }
        return _ro = new this.targetWindow.ResizeObserver((entries) => {
          entries.forEach((entry) => {
            const run = () => {
              this._measureElement(entry.target, entry);
            };
            this.options.useAnimationFrameWithResizeObserver ? requestAnimationFrame(run) : run();
          });
        });
      };
      return {
        disconnect: () => {
          var _a;
          (_a = get()) == null ? void 0 : _a.disconnect();
          _ro = null;
        },
        observe: (target) => {
          var _a;
          return (_a = get()) == null ? void 0 : _a.observe(target, { box: "border-box" });
        },
        unobserve: (target) => {
          var _a;
          return (_a = get()) == null ? void 0 : _a.unobserve(target);
        }
      };
    })();
    this.range = null;
    this.setOptions = (opts2) => {
      Object.entries(opts2).forEach(([key, value]) => {
        if (typeof value === "undefined") delete opts2[key];
      });
      this.options = {
        debug: false,
        initialOffset: 0,
        overscan: 1,
        paddingStart: 0,
        paddingEnd: 0,
        scrollPaddingStart: 0,
        scrollPaddingEnd: 0,
        horizontal: false,
        getItemKey: defaultKeyExtractor,
        rangeExtractor: defaultRangeExtractor,
        onChange: () => {
        },
        measureElement,
        initialRect: { width: 0, height: 0 },
        scrollMargin: 0,
        gap: 0,
        indexAttribute: "data-index",
        initialMeasurementsCache: [],
        lanes: 1,
        isScrollingResetDelay: 150,
        enabled: true,
        isRtl: false,
        useScrollendEvent: false,
        useAnimationFrameWithResizeObserver: false,
        ...opts2
      };
    };
    this.notify = (sync) => {
      var _a, _b;
      (_b = (_a = this.options).onChange) == null ? void 0 : _b.call(_a, this, sync);
    };
    this.maybeNotify = memo(
      () => {
        this.calculateRange();
        return [
          this.isScrolling,
          this.range ? this.range.startIndex : null,
          this.range ? this.range.endIndex : null
        ];
      },
      (isScrolling) => {
        this.notify(isScrolling);
      },
      {
        key: "maybeNotify",
        debug: () => this.options.debug,
        initialDeps: [
          this.isScrolling,
          this.range ? this.range.startIndex : null,
          this.range ? this.range.endIndex : null
        ]
      }
    );
    this.cleanup = () => {
      this.unsubs.filter(Boolean).forEach((d10) => d10());
      this.unsubs = [];
      this.observer.disconnect();
      this.scrollElement = null;
      this.targetWindow = null;
    };
    this._didMount = () => {
      return () => {
        this.cleanup();
      };
    };
    this._willUpdate = () => {
      var _a;
      const scrollElement = this.options.enabled ? this.options.getScrollElement() : null;
      if (this.scrollElement !== scrollElement) {
        this.cleanup();
        if (!scrollElement) {
          this.maybeNotify();
          return;
        }
        this.scrollElement = scrollElement;
        if (this.scrollElement && "ownerDocument" in this.scrollElement) {
          this.targetWindow = this.scrollElement.ownerDocument.defaultView;
        } else {
          this.targetWindow = ((_a = this.scrollElement) == null ? void 0 : _a.window) ?? null;
        }
        this.elementsCache.forEach((cached) => {
          this.observer.observe(cached);
        });
        this._scrollToOffset(this.getScrollOffset(), {
          adjustments: void 0,
          behavior: void 0
        });
        this.unsubs.push(
          this.options.observeElementRect(this, (rect) => {
            this.scrollRect = rect;
            this.maybeNotify();
          })
        );
        this.unsubs.push(
          this.options.observeElementOffset(this, (offset3, isScrolling) => {
            this.scrollAdjustments = 0;
            this.scrollDirection = isScrolling ? this.getScrollOffset() < offset3 ? "forward" : "backward" : null;
            this.scrollOffset = offset3;
            this.isScrolling = isScrolling;
            this.maybeNotify();
          })
        );
      }
    };
    this.getSize = () => {
      if (!this.options.enabled) {
        this.scrollRect = null;
        return 0;
      }
      this.scrollRect = this.scrollRect ?? this.options.initialRect;
      return this.scrollRect[this.options.horizontal ? "width" : "height"];
    };
    this.getScrollOffset = () => {
      if (!this.options.enabled) {
        this.scrollOffset = null;
        return 0;
      }
      this.scrollOffset = this.scrollOffset ?? (typeof this.options.initialOffset === "function" ? this.options.initialOffset() : this.options.initialOffset);
      return this.scrollOffset;
    };
    this.getFurthestMeasurement = (measurements, index) => {
      const furthestMeasurementsFound = /* @__PURE__ */ new Map();
      const furthestMeasurements = /* @__PURE__ */ new Map();
      for (let m12 = index - 1; m12 >= 0; m12--) {
        const measurement = measurements[m12];
        if (furthestMeasurementsFound.has(measurement.lane)) {
          continue;
        }
        const previousFurthestMeasurement = furthestMeasurements.get(
          measurement.lane
        );
        if (previousFurthestMeasurement == null || measurement.end > previousFurthestMeasurement.end) {
          furthestMeasurements.set(measurement.lane, measurement);
        } else if (measurement.end < previousFurthestMeasurement.end) {
          furthestMeasurementsFound.set(measurement.lane, true);
        }
        if (furthestMeasurementsFound.size === this.options.lanes) {
          break;
        }
      }
      return furthestMeasurements.size === this.options.lanes ? Array.from(furthestMeasurements.values()).sort((a8, b5) => {
        if (a8.end === b5.end) {
          return a8.index - b5.index;
        }
        return a8.end - b5.end;
      })[0] : void 0;
    };
    this.getMeasurementOptions = memo(
      () => [
        this.options.count,
        this.options.paddingStart,
        this.options.scrollMargin,
        this.options.getItemKey,
        this.options.enabled
      ],
      (count, paddingStart, scrollMargin, getItemKey, enabled) => {
        this.pendingMeasuredCacheIndexes = [];
        return {
          count,
          paddingStart,
          scrollMargin,
          getItemKey,
          enabled
        };
      },
      {
        key: false
      }
    );
    this.getMeasurements = memo(
      () => [this.getMeasurementOptions(), this.itemSizeCache],
      ({ count, paddingStart, scrollMargin, getItemKey, enabled }, itemSizeCache) => {
        if (!enabled) {
          this.measurementsCache = [];
          this.itemSizeCache.clear();
          return [];
        }
        if (this.measurementsCache.length === 0) {
          this.measurementsCache = this.options.initialMeasurementsCache;
          this.measurementsCache.forEach((item) => {
            this.itemSizeCache.set(item.key, item.size);
          });
        }
        const min2 = this.pendingMeasuredCacheIndexes.length > 0 ? Math.min(...this.pendingMeasuredCacheIndexes) : 0;
        this.pendingMeasuredCacheIndexes = [];
        const measurements = this.measurementsCache.slice(0, min2);
        for (let i13 = min2; i13 < count; i13++) {
          const key = getItemKey(i13);
          const furthestMeasurement = this.options.lanes === 1 ? measurements[i13 - 1] : this.getFurthestMeasurement(measurements, i13);
          const start = furthestMeasurement ? furthestMeasurement.end + this.options.gap : paddingStart + scrollMargin;
          const measuredSize = itemSizeCache.get(key);
          const size3 = typeof measuredSize === "number" ? measuredSize : this.options.estimateSize(i13);
          const end = start + size3;
          const lane = furthestMeasurement ? furthestMeasurement.lane : i13 % this.options.lanes;
          measurements[i13] = {
            index: i13,
            start,
            size: size3,
            end,
            key,
            lane
          };
        }
        this.measurementsCache = measurements;
        return measurements;
      },
      {
        key: "getMeasurements",
        debug: () => this.options.debug
      }
    );
    this.calculateRange = memo(
      () => [
        this.getMeasurements(),
        this.getSize(),
        this.getScrollOffset(),
        this.options.lanes
      ],
      (measurements, outerSize, scrollOffset, lanes) => {
        return this.range = measurements.length > 0 && outerSize > 0 ? calculateRange({
          measurements,
          outerSize,
          scrollOffset,
          lanes
        }) : null;
      },
      {
        key: "calculateRange",
        debug: () => this.options.debug
      }
    );
    this.getVirtualIndexes = memo(
      () => {
        let startIndex = null;
        let endIndex = null;
        const range = this.calculateRange();
        if (range) {
          startIndex = range.startIndex;
          endIndex = range.endIndex;
        }
        this.maybeNotify.updateDeps([this.isScrolling, startIndex, endIndex]);
        return [
          this.options.rangeExtractor,
          this.options.overscan,
          this.options.count,
          startIndex,
          endIndex
        ];
      },
      (rangeExtractor, overscan, count, startIndex, endIndex) => {
        return startIndex === null || endIndex === null ? [] : rangeExtractor({
          startIndex,
          endIndex,
          overscan,
          count
        });
      },
      {
        key: "getVirtualIndexes",
        debug: () => this.options.debug
      }
    );
    this.indexFromElement = (node) => {
      const attributeName = this.options.indexAttribute;
      const indexStr = node.getAttribute(attributeName);
      if (!indexStr) {
        console.warn(
          `Missing attribute name '${attributeName}={index}' on measured element.`
        );
        return -1;
      }
      return parseInt(indexStr, 10);
    };
    this._measureElement = (node, entry) => {
      const index = this.indexFromElement(node);
      const item = this.measurementsCache[index];
      if (!item) {
        return;
      }
      const key = item.key;
      const prevNode = this.elementsCache.get(key);
      if (prevNode !== node) {
        if (prevNode) {
          this.observer.unobserve(prevNode);
        }
        this.observer.observe(node);
        this.elementsCache.set(key, node);
      }
      if (node.isConnected) {
        this.resizeItem(index, this.options.measureElement(node, entry, this));
      }
    };
    this.resizeItem = (index, size3) => {
      const item = this.measurementsCache[index];
      if (!item) {
        return;
      }
      const itemSize = this.itemSizeCache.get(item.key) ?? item.size;
      const delta = size3 - itemSize;
      if (delta !== 0) {
        if (this.shouldAdjustScrollPositionOnItemSizeChange !== void 0 ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this) : item.start < this.getScrollOffset() + this.scrollAdjustments) {
          if (this.options.debug) {
            console.info("correction", delta);
          }
          this._scrollToOffset(this.getScrollOffset(), {
            adjustments: this.scrollAdjustments += delta,
            behavior: void 0
          });
        }
        this.pendingMeasuredCacheIndexes.push(item.index);
        this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size3));
        this.notify(false);
      }
    };
    this.measureElement = (node) => {
      if (!node) {
        this.elementsCache.forEach((cached, key) => {
          if (!cached.isConnected) {
            this.observer.unobserve(cached);
            this.elementsCache.delete(key);
          }
        });
        return;
      }
      this._measureElement(node, void 0);
    };
    this.getVirtualItems = memo(
      () => [this.getVirtualIndexes(), this.getMeasurements()],
      (indexes, measurements) => {
        const virtualItems = [];
        for (let k9 = 0, len = indexes.length; k9 < len; k9++) {
          const i13 = indexes[k9];
          const measurement = measurements[i13];
          virtualItems.push(measurement);
        }
        return virtualItems;
      },
      {
        key: "getVirtualItems",
        debug: () => this.options.debug
      }
    );
    this.getVirtualItemForOffset = (offset3) => {
      const measurements = this.getMeasurements();
      if (measurements.length === 0) {
        return void 0;
      }
      return notUndefined(
        measurements[findNearestBinarySearch(
          0,
          measurements.length - 1,
          (index) => notUndefined(measurements[index]).start,
          offset3
        )]
      );
    };
    this.getOffsetForAlignment = (toOffset, align, itemSize = 0) => {
      const size3 = this.getSize();
      const scrollOffset = this.getScrollOffset();
      if (align === "auto") {
        align = toOffset >= scrollOffset + size3 ? "end" : "start";
      }
      if (align === "center") {
        toOffset += (itemSize - size3) / 2;
      } else if (align === "end") {
        toOffset -= size3;
      }
      const scrollSizeProp = this.options.horizontal ? "scrollWidth" : "scrollHeight";
      const scrollSize = this.scrollElement ? "document" in this.scrollElement ? this.scrollElement.document.documentElement[scrollSizeProp] : this.scrollElement[scrollSizeProp] : 0;
      const maxOffset = scrollSize - size3;
      return Math.max(Math.min(maxOffset, toOffset), 0);
    };
    this.getOffsetForIndex = (index, align = "auto") => {
      index = Math.max(0, Math.min(index, this.options.count - 1));
      const item = this.measurementsCache[index];
      if (!item) {
        return void 0;
      }
      const size3 = this.getSize();
      const scrollOffset = this.getScrollOffset();
      if (align === "auto") {
        if (item.end >= scrollOffset + size3 - this.options.scrollPaddingEnd) {
          align = "end";
        } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {
          align = "start";
        } else {
          return [scrollOffset, align];
        }
      }
      const toOffset = align === "end" ? item.end + this.options.scrollPaddingEnd : item.start - this.options.scrollPaddingStart;
      return [
        this.getOffsetForAlignment(toOffset, align, item.size),
        align
      ];
    };
    this.isDynamicMode = () => this.elementsCache.size > 0;
    this.cancelScrollToIndex = () => {
      if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {
        this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId);
        this.scrollToIndexTimeoutId = null;
      }
    };
    this.scrollToOffset = (toOffset, { align = "start", behavior } = {}) => {
      this.cancelScrollToIndex();
      if (behavior === "smooth" && this.isDynamicMode()) {
        console.warn(
          "The `smooth` scroll behavior is not fully supported with dynamic size."
        );
      }
      this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {
        adjustments: void 0,
        behavior
      });
    };
    this.scrollToIndex = (index, { align: initialAlign = "auto", behavior } = {}) => {
      index = Math.max(0, Math.min(index, this.options.count - 1));
      this.cancelScrollToIndex();
      if (behavior === "smooth" && this.isDynamicMode()) {
        console.warn(
          "The `smooth` scroll behavior is not fully supported with dynamic size."
        );
      }
      const offsetAndAlign = this.getOffsetForIndex(index, initialAlign);
      if (!offsetAndAlign) return;
      const [offset3, align] = offsetAndAlign;
      this._scrollToOffset(offset3, { adjustments: void 0, behavior });
      if (behavior !== "smooth" && this.isDynamicMode() && this.targetWindow) {
        this.scrollToIndexTimeoutId = this.targetWindow.setTimeout(() => {
          this.scrollToIndexTimeoutId = null;
          const elementInDOM = this.elementsCache.has(
            this.options.getItemKey(index)
          );
          if (elementInDOM) {
            const [latestOffset] = notUndefined(
              this.getOffsetForIndex(index, align)
            );
            if (!approxEqual(latestOffset, this.getScrollOffset())) {
              this.scrollToIndex(index, { align, behavior });
            }
          } else {
            this.scrollToIndex(index, { align, behavior });
          }
        });
      }
    };
    this.scrollBy = (delta, { behavior } = {}) => {
      this.cancelScrollToIndex();
      if (behavior === "smooth" && this.isDynamicMode()) {
        console.warn(
          "The `smooth` scroll behavior is not fully supported with dynamic size."
        );
      }
      this._scrollToOffset(this.getScrollOffset() + delta, {
        adjustments: void 0,
        behavior
      });
    };
    this.getTotalSize = () => {
      var _a;
      const measurements = this.getMeasurements();
      let end;
      if (measurements.length === 0) {
        end = this.options.paddingStart;
      } else if (this.options.lanes === 1) {
        end = ((_a = measurements[measurements.length - 1]) == null ? void 0 : _a.end) ?? 0;
      } else {
        const endByLane = Array(this.options.lanes).fill(null);
        let endIndex = measurements.length - 1;
        while (endIndex > 0 && endByLane.some((val) => val === null)) {
          const item = measurements[endIndex];
          if (endByLane[item.lane] === null) {
            endByLane[item.lane] = item.end;
          }
          endIndex--;
        }
        end = Math.max(...endByLane.filter((val) => val !== null));
      }
      return Math.max(
        end - this.options.scrollMargin + this.options.paddingEnd,
        0
      );
    };
    this._scrollToOffset = (offset3, {
      adjustments,
      behavior
    }) => {
      this.options.scrollToFn(offset3, { behavior, adjustments }, this);
    };
    this.measure = () => {
      this.itemSizeCache = /* @__PURE__ */ new Map();
      this.notify(false);
    };
    this.setOptions(opts);
  }
};
var findNearestBinarySearch = (low, high, getCurrentValue, value) => {
  while (low <= high) {
    const middle = (low + high) / 2 | 0;
    const currentValue = getCurrentValue(middle);
    if (currentValue < value) {
      low = middle + 1;
    } else if (currentValue > value) {
      high = middle - 1;
    } else {
      return middle;
    }
  }
  if (low > 0) {
    return low - 1;
  } else {
    return 0;
  }
};
function calculateRange({
  measurements,
  outerSize,
  scrollOffset,
  lanes
}) {
  const lastIndex = measurements.length - 1;
  const getOffset = (index) => measurements[index].start;
  let startIndex = findNearestBinarySearch(
    0,
    lastIndex,
    getOffset,
    scrollOffset
  );
  let endIndex = startIndex;
  if (lanes === 1) {
    while (endIndex < lastIndex && measurements[endIndex].end < scrollOffset + outerSize) {
      endIndex++;
    }
  } else if (lanes > 1) {
    const endPerLane = Array(lanes).fill(0);
    while (endIndex < lastIndex && endPerLane.some((pos) => pos < scrollOffset + outerSize)) {
      const item = measurements[endIndex];
      endPerLane[item.lane] = item.end;
      endIndex++;
    }
    const startPerLane = Array(lanes).fill(scrollOffset + outerSize);
    while (startIndex > 0 && startPerLane.some((pos) => pos >= scrollOffset)) {
      const item = measurements[startIndex];
      startPerLane[item.lane] = item.start;
      startIndex--;
    }
    startIndex = Math.max(0, startIndex - startIndex % lanes);
    endIndex = Math.min(lastIndex, endIndex + (lanes - 1 - endIndex % lanes));
  }
  return { startIndex, endIndex };
}

// node_modules/.pnpm/@tanstack+vue-virtual@3.13.4_vue@3.5.13_typescript@5.8.2_/node_modules/@tanstack/vue-virtual/dist/esm/index.js
import { computed, unref, shallowRef, watch, triggerRef, onScopeDispose } from "vue";
function useVirtualizerBase(options) {
  const virtualizer = new Virtualizer(unref(options));
  const state = shallowRef(virtualizer);
  const cleanup = virtualizer._didMount();
  watch(
    () => unref(options).getScrollElement(),
    (el) => {
      if (el) {
        virtualizer._willUpdate();
      }
    },
    {
      immediate: true
    }
  );
  watch(
    () => unref(options),
    (options2) => {
      virtualizer.setOptions({
        ...options2,
        onChange: (instance, sync) => {
          var _a;
          triggerRef(state);
          (_a = options2.onChange) == null ? void 0 : _a.call(options2, instance, sync);
        }
      });
      virtualizer._willUpdate();
      triggerRef(state);
    },
    {
      immediate: true
    }
  );
  onScopeDispose(cleanup);
  return state;
}
function useVirtualizer(options) {
  return useVirtualizerBase(
    computed(() => ({
      observeElementRect,
      observeElementOffset,
      scrollToFn: elementScroll,
      ...unref(options)
    }))
  );
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/combobox/combobox.js
import { cloneVNode as de, computed as m, defineComponent as H2, Fragment as se, h as z, inject as ee, nextTick as N3, onMounted as X, onUnmounted as fe, provide as te, reactive as ve, ref as k2, toRaw as L, watch as J, watchEffect as Y } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-controllable.js
import { computed as p, ref as s } from "vue";
function d(u10, e4, r6) {
  let i13 = s(r6 == null ? void 0 : r6.value), f8 = p(() => u10.value !== void 0);
  return [p(() => f8.value ? u10.value : i13.value), function(t8) {
    return f8.value || (i13.value = t8), e4 == null ? void 0 : e4(t8);
  }];
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-disposables.js
import { onUnmounted as s2 } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/micro-task.js
function t(e4) {
  typeof queueMicrotask == "function" ? queueMicrotask(e4) : Promise.resolve().then(e4).catch((o11) => setTimeout(() => {
    throw o11;
  }));
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/disposables.js
function o() {
  let a8 = [], s14 = { addEventListener(e4, t8, r6, i13) {
    return e4.addEventListener(t8, r6, i13), s14.add(() => e4.removeEventListener(t8, r6, i13));
  }, requestAnimationFrame(...e4) {
    let t8 = requestAnimationFrame(...e4);
    s14.add(() => cancelAnimationFrame(t8));
  }, nextFrame(...e4) {
    s14.requestAnimationFrame(() => {
      s14.requestAnimationFrame(...e4);
    });
  }, setTimeout(...e4) {
    let t8 = setTimeout(...e4);
    s14.add(() => clearTimeout(t8));
  }, microTask(...e4) {
    let t8 = { current: true };
    return t(() => {
      t8.current && e4[0]();
    }), s14.add(() => {
      t8.current = false;
    });
  }, style(e4, t8, r6) {
    let i13 = e4.style.getPropertyValue(t8);
    return Object.assign(e4.style, { [t8]: r6 }), this.add(() => {
      Object.assign(e4.style, { [t8]: i13 });
    });
  }, group(e4) {
    let t8 = o();
    return e4(t8), this.add(() => t8.dispose());
  }, add(e4) {
    return a8.push(e4), () => {
      let t8 = a8.indexOf(e4);
      if (t8 >= 0) for (let r6 of a8.splice(t8, 1)) r6();
    };
  }, dispose() {
    for (let e4 of a8.splice(0)) e4();
  } };
  return s14;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-disposables.js
function i() {
  let o11 = o();
  return s2(() => o11.dispose()), o11;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-frame-debounce.js
function t2() {
  let e4 = i();
  return (o11) => {
    e4.dispose(), e4.nextFrame(o11);
  };
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-id.js
import * as e from "vue";
var r;
var n = Symbol("headlessui.useid");
var o2 = 0;
var i2 = (r = e.useId) != null ? r : function() {
  return e.inject(n, () => `${++o2}`)();
};

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-outside-click.js
import { computed as s4, ref as E2 } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/dom.js
function o3(e4) {
  var l7;
  if (e4 == null || e4.value == null) return null;
  let n9 = (l7 = e4.value.$el) != null ? l7 : e4.value;
  return n9 instanceof Node ? n9 : null;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/focus-management.js
import { nextTick as b } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/match.js
function u(r6, n9, ...a8) {
  if (r6 in n9) {
    let e4 = n9[r6];
    return typeof e4 == "function" ? e4(...a8) : e4;
  }
  let t8 = new Error(`Tried to handle "${r6}" but there is no handler defined. Only defined handlers are: ${Object.keys(n9).map((e4) => `"${e4}"`).join(", ")}.`);
  throw Error.captureStackTrace && Error.captureStackTrace(t8, u), t8;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/env.js
var i3 = Object.defineProperty;
var d2 = (t8, e4, r6) => e4 in t8 ? i3(t8, e4, { enumerable: true, configurable: true, writable: true, value: r6 }) : t8[e4] = r6;
var n2 = (t8, e4, r6) => (d2(t8, typeof e4 != "symbol" ? e4 + "" : e4, r6), r6);
var s3 = class {
  constructor() {
    n2(this, "current", this.detect());
    n2(this, "currentId", 0);
  }
  set(e4) {
    this.current !== e4 && (this.currentId = 0, this.current = e4);
  }
  reset() {
    this.set(this.detect());
  }
  nextId() {
    return ++this.currentId;
  }
  get isServer() {
    return this.current === "server";
  }
  get isClient() {
    return this.current === "client";
  }
  detect() {
    return typeof window == "undefined" || typeof document == "undefined" ? "server" : "client";
  }
};
var c = new s3();

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/owner.js
function i4(r6) {
  if (c.isServer) return null;
  if (r6 instanceof Node) return r6.ownerDocument;
  if (r6 != null && r6.hasOwnProperty("value")) {
    let n9 = o3(r6);
    if (n9) return n9.ownerDocument;
  }
  return document;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/focus-management.js
var c2 = ["[contentEditable=true]", "[tabindex]", "a[href]", "area[href]", "button:not([disabled])", "iframe", "input:not([disabled])", "select:not([disabled])", "textarea:not([disabled])"].map((e4) => `${e4}:not([tabindex='-1'])`).join(",");
var N = ((n9) => (n9[n9.First = 1] = "First", n9[n9.Previous = 2] = "Previous", n9[n9.Next = 4] = "Next", n9[n9.Last = 8] = "Last", n9[n9.WrapAround = 16] = "WrapAround", n9[n9.NoScroll = 32] = "NoScroll", n9))(N || {});
var T = ((o11) => (o11[o11.Error = 0] = "Error", o11[o11.Overflow = 1] = "Overflow", o11[o11.Success = 2] = "Success", o11[o11.Underflow = 3] = "Underflow", o11))(T || {});
var F = ((t8) => (t8[t8.Previous = -1] = "Previous", t8[t8.Next = 1] = "Next", t8))(F || {});
function E(e4 = document.body) {
  return e4 == null ? [] : Array.from(e4.querySelectorAll(c2)).sort((r6, t8) => Math.sign((r6.tabIndex || Number.MAX_SAFE_INTEGER) - (t8.tabIndex || Number.MAX_SAFE_INTEGER)));
}
var h = ((t8) => (t8[t8.Strict = 0] = "Strict", t8[t8.Loose = 1] = "Loose", t8))(h || {});
function w(e4, r6 = 0) {
  var t8;
  return e4 === ((t8 = i4(e4)) == null ? void 0 : t8.body) ? false : u(r6, { [0]() {
    return e4.matches(c2);
  }, [1]() {
    let l7 = e4;
    for (; l7 !== null; ) {
      if (l7.matches(c2)) return true;
      l7 = l7.parentElement;
    }
    return false;
  } });
}
function _(e4) {
  let r6 = i4(e4);
  b(() => {
    r6 && !w(r6.activeElement, 0) && S(e4);
  });
}
var y = ((t8) => (t8[t8.Keyboard = 0] = "Keyboard", t8[t8.Mouse = 1] = "Mouse", t8))(y || {});
typeof window != "undefined" && typeof document != "undefined" && (document.addEventListener("keydown", (e4) => {
  e4.metaKey || e4.altKey || e4.ctrlKey || (document.documentElement.dataset.headlessuiFocusVisible = "");
}, true), document.addEventListener("click", (e4) => {
  e4.detail === 1 ? delete document.documentElement.dataset.headlessuiFocusVisible : e4.detail === 0 && (document.documentElement.dataset.headlessuiFocusVisible = "");
}, true));
function S(e4) {
  e4 == null || e4.focus({ preventScroll: true });
}
var H = ["textarea", "input"].join(",");
function I(e4) {
  var r6, t8;
  return (t8 = (r6 = e4 == null ? void 0 : e4.matches) == null ? void 0 : r6.call(e4, H)) != null ? t8 : false;
}
function O(e4, r6 = (t8) => t8) {
  return e4.slice().sort((t8, l7) => {
    let o11 = r6(t8), i13 = r6(l7);
    if (o11 === null || i13 === null) return 0;
    let n9 = o11.compareDocumentPosition(i13);
    return n9 & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : n9 & Node.DOCUMENT_POSITION_PRECEDING ? 1 : 0;
  });
}
function v(e4, r6) {
  return P(E(), r6, { relativeTo: e4 });
}
function P(e4, r6, { sorted: t8 = true, relativeTo: l7 = null, skipElements: o11 = [] } = {}) {
  var m12;
  let i13 = (m12 = Array.isArray(e4) ? e4.length > 0 ? e4[0].ownerDocument : document : e4 == null ? void 0 : e4.ownerDocument) != null ? m12 : document, n9 = Array.isArray(e4) ? t8 ? O(e4) : e4 : E(e4);
  o11.length > 0 && n9.length > 1 && (n9 = n9.filter((s14) => !o11.includes(s14))), l7 = l7 != null ? l7 : i13.activeElement;
  let x8 = (() => {
    if (r6 & 5) return 1;
    if (r6 & 10) return -1;
    throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last");
  })(), p10 = (() => {
    if (r6 & 1) return 0;
    if (r6 & 2) return Math.max(0, n9.indexOf(l7)) - 1;
    if (r6 & 4) return Math.max(0, n9.indexOf(l7)) + 1;
    if (r6 & 8) return n9.length - 1;
    throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last");
  })(), L9 = r6 & 32 ? { preventScroll: true } : {}, a8 = 0, d10 = n9.length, u10;
  do {
    if (a8 >= d10 || a8 + d10 <= 0) return 0;
    let s14 = p10 + a8;
    if (r6 & 16) s14 = (s14 + d10) % d10;
    else {
      if (s14 < 0) return 3;
      if (s14 >= d10) return 1;
    }
    u10 = n9[s14], u10 == null || u10.focus(L9), a8 += x8;
  } while (u10 !== i13.activeElement);
  return r6 & 6 && I(u10) && u10.select(), 2;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/platform.js
function t3() {
  return /iPhone/gi.test(window.navigator.platform) || /Mac/gi.test(window.navigator.platform) && window.navigator.maxTouchPoints > 0;
}
function i5() {
  return /Android/gi.test(window.navigator.userAgent);
}
function n3() {
  return t3() || i5();
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-document-event.js
import { watchEffect as r2 } from "vue";
function u2(e4, t8, n9) {
  c.isServer || r2((o11) => {
    document.addEventListener(e4, t8, n9), o11(() => document.removeEventListener(e4, t8, n9));
  });
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-window-event.js
import { watchEffect as i6 } from "vue";
function w2(e4, n9, t8) {
  c.isServer || i6((o11) => {
    window.addEventListener(e4, n9, t8), o11(() => window.removeEventListener(e4, n9, t8));
  });
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-outside-click.js
function w3(f8, m12, l7 = s4(() => true)) {
  function a8(e4, r6) {
    if (!l7.value || e4.defaultPrevented) return;
    let t8 = r6(e4);
    if (t8 === null || !t8.getRootNode().contains(t8)) return;
    let c7 = function o11(n9) {
      return typeof n9 == "function" ? o11(n9()) : Array.isArray(n9) || n9 instanceof Set ? n9 : [n9];
    }(f8);
    for (let o11 of c7) {
      if (o11 === null) continue;
      let n9 = o11 instanceof HTMLElement ? o11 : o3(o11);
      if (n9 != null && n9.contains(t8) || e4.composed && e4.composedPath().includes(n9)) return;
    }
    return !w(t8, h.Loose) && t8.tabIndex !== -1 && e4.preventDefault(), m12(e4, t8);
  }
  let u10 = E2(null);
  u2("pointerdown", (e4) => {
    var r6, t8;
    l7.value && (u10.value = ((t8 = (r6 = e4.composedPath) == null ? void 0 : r6.call(e4)) == null ? void 0 : t8[0]) || e4.target);
  }, true), u2("mousedown", (e4) => {
    var r6, t8;
    l7.value && (u10.value = ((t8 = (r6 = e4.composedPath) == null ? void 0 : r6.call(e4)) == null ? void 0 : t8[0]) || e4.target);
  }, true), u2("click", (e4) => {
    n3() || u10.value && (a8(e4, () => u10.value), u10.value = null);
  }, true), u2("touchend", (e4) => a8(e4, () => e4.target instanceof HTMLElement ? e4.target : null), true), w2("blur", (e4) => a8(e4, () => window.document.activeElement instanceof HTMLIFrameElement ? window.document.activeElement : null), true);
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-resolve-button-type.js
import { onMounted as i7, ref as f, watchEffect as l } from "vue";
function r3(t8, e4) {
  if (t8) return t8;
  let n9 = e4 != null ? e4 : "button";
  if (typeof n9 == "string" && n9.toLowerCase() === "button") return "button";
}
function s5(t8, e4) {
  let n9 = f(r3(t8.value.type, t8.value.as));
  return i7(() => {
    n9.value = r3(t8.value.type, t8.value.as);
  }), l(() => {
    var u10;
    n9.value || o3(e4) && o3(e4) instanceof HTMLButtonElement && !((u10 = o3(e4)) != null && u10.hasAttribute("type")) && (n9.value = "button");
  }), n9;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-tracked-pointer.js
import { ref as o4 } from "vue";
function r4(e4) {
  return [e4.screenX, e4.screenY];
}
function u3() {
  let e4 = o4([-1, -1]);
  return { wasMoved(n9) {
    let t8 = r4(n9);
    return e4.value[0] === t8[0] && e4.value[1] === t8[1] ? false : (e4.value = t8, true);
  }, update(n9) {
    e4.value = r4(n9);
  } };
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-tree-walker.js
import { watchEffect as p2 } from "vue";
function i8({ container: e4, accept: t8, walk: d10, enabled: o11 }) {
  p2(() => {
    let r6 = e4.value;
    if (!r6 || o11 !== void 0 && !o11.value) return;
    let l7 = i4(e4);
    if (!l7) return;
    let c7 = Object.assign((f8) => t8(f8), { acceptNode: t8 }), n9 = l7.createTreeWalker(r6, NodeFilter.SHOW_ELEMENT, c7, false);
    for (; n9.nextNode(); ) d10(n9.currentNode);
  });
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/internal/hidden.js
import { defineComponent as a } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/render.js
import { cloneVNode as O2, Fragment as x, h as k } from "vue";
var N2 = ((o11) => (o11[o11.None = 0] = "None", o11[o11.RenderStrategy = 1] = "RenderStrategy", o11[o11.Static = 2] = "Static", o11))(N2 || {});
var S2 = ((e4) => (e4[e4.Unmount = 0] = "Unmount", e4[e4.Hidden = 1] = "Hidden", e4))(S2 || {});
function A({ visible: r6 = true, features: t8 = 0, ourProps: e4, theirProps: o11, ...i13 }) {
  var a8;
  let n9 = j(o11, e4), l7 = Object.assign(i13, { props: n9 });
  if (r6 || t8 & 2 && n9.static) return y2(l7);
  if (t8 & 1) {
    let d10 = (a8 = n9.unmount) == null || a8 ? 0 : 1;
    return u(d10, { [0]() {
      return null;
    }, [1]() {
      return y2({ ...i13, props: { ...n9, hidden: true, style: { display: "none" } } });
    } });
  }
  return y2(l7);
}
function y2({ props: r6, attrs: t8, slots: e4, slot: o11, name: i13 }) {
  var m12, h6;
  let { as: n9, ...l7 } = T2(r6, ["unmount", "static"]), a8 = (m12 = e4.default) == null ? void 0 : m12.call(e4, o11), d10 = {};
  if (o11) {
    let u10 = false, c7 = [];
    for (let [p10, f8] of Object.entries(o11)) typeof f8 == "boolean" && (u10 = true), f8 === true && c7.push(p10);
    u10 && (d10["data-headlessui-state"] = c7.join(" "));
  }
  if (n9 === "template") {
    if (a8 = b2(a8 != null ? a8 : []), Object.keys(l7).length > 0 || Object.keys(t8).length > 0) {
      let [u10, ...c7] = a8 != null ? a8 : [];
      if (!v2(u10) || c7.length > 0) throw new Error(['Passing props on "template"!', "", `The current component <${i13} /> is rendering a "template".`, "However we need to passthrough the following props:", Object.keys(l7).concat(Object.keys(t8)).map((s14) => s14.trim()).filter((s14, g6, R11) => R11.indexOf(s14) === g6).sort((s14, g6) => s14.localeCompare(g6)).map((s14) => `  - ${s14}`).join(`
`), "", "You can apply a few solutions:", ['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".', "Render a single element as the child so that we can forward the props onto that element."].map((s14) => `  - ${s14}`).join(`
`)].join(`
`));
      let p10 = j((h6 = u10.props) != null ? h6 : {}, l7, d10), f8 = O2(u10, p10, true);
      for (let s14 in p10) s14.startsWith("on") && (f8.props || (f8.props = {}), f8.props[s14] = p10[s14]);
      return f8;
    }
    return Array.isArray(a8) && a8.length === 1 ? a8[0] : a8;
  }
  return k(n9, Object.assign({}, l7, d10), { default: () => a8 });
}
function b2(r6) {
  return r6.flatMap((t8) => t8.type === x ? b2(t8.children) : [t8]);
}
function j(...r6) {
  var o11;
  if (r6.length === 0) return {};
  if (r6.length === 1) return r6[0];
  let t8 = {}, e4 = {};
  for (let i13 of r6) for (let n9 in i13) n9.startsWith("on") && typeof i13[n9] == "function" ? ((o11 = e4[n9]) != null || (e4[n9] = []), e4[n9].push(i13[n9])) : t8[n9] = i13[n9];
  if (t8.disabled || t8["aria-disabled"]) return Object.assign(t8, Object.fromEntries(Object.keys(e4).map((i13) => [i13, void 0])));
  for (let i13 in e4) Object.assign(t8, { [i13](n9, ...l7) {
    let a8 = e4[i13];
    for (let d10 of a8) {
      if (n9 instanceof Event && n9.defaultPrevented) return;
      d10(n9, ...l7);
    }
  } });
  return t8;
}
function E3(r6) {
  let t8 = Object.assign({}, r6);
  for (let e4 in t8) t8[e4] === void 0 && delete t8[e4];
  return t8;
}
function T2(r6, t8 = []) {
  let e4 = Object.assign({}, r6);
  for (let o11 of t8) o11 in e4 && delete e4[o11];
  return e4;
}
function v2(r6) {
  return r6 == null ? false : typeof r6.type == "string" || typeof r6.type == "object" || typeof r6.type == "function";
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/internal/hidden.js
var u4 = ((e4) => (e4[e4.None = 1] = "None", e4[e4.Focusable = 2] = "Focusable", e4[e4.Hidden = 4] = "Hidden", e4))(u4 || {});
var f2 = a({ name: "Hidden", props: { as: { type: [Object, String], default: "div" }, features: { type: Number, default: 1 } }, setup(t8, { slots: n9, attrs: i13 }) {
  return () => {
    var r6;
    let { features: e4, ...d10 } = t8, o11 = { "aria-hidden": (e4 & 2) === 2 ? true : (r6 = d10["aria-hidden"]) != null ? r6 : void 0, hidden: (e4 & 4) === 4 ? true : void 0, style: { position: "fixed", top: 1, left: 1, width: 1, height: 0, padding: 0, margin: -1, overflow: "hidden", clip: "rect(0, 0, 0, 0)", whiteSpace: "nowrap", borderWidth: "0", ...(e4 & 4) === 4 && (e4 & 2) !== 2 && { display: "none" } } };
    return A({ ourProps: o11, theirProps: d10, slot: {}, attrs: i13, slots: n9, name: "Hidden" });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/internal/open-closed.js
import { inject as p3, provide as r5 } from "vue";
var n4 = Symbol("Context");
var i9 = ((e4) => (e4[e4.Open = 1] = "Open", e4[e4.Closed = 2] = "Closed", e4[e4.Closing = 4] = "Closing", e4[e4.Opening = 8] = "Opening", e4))(i9 || {});
function s6() {
  return l2() !== null;
}
function l2() {
  return p3(n4, null);
}
function t4(o11) {
  r5(n4, o11);
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/keyboard.js
var o5 = ((r6) => (r6.Space = " ", r6.Enter = "Enter", r6.Escape = "Escape", r6.Backspace = "Backspace", r6.Delete = "Delete", r6.ArrowLeft = "ArrowLeft", r6.ArrowUp = "ArrowUp", r6.ArrowRight = "ArrowRight", r6.ArrowDown = "ArrowDown", r6.Home = "Home", r6.End = "End", r6.PageUp = "PageUp", r6.PageDown = "PageDown", r6.Tab = "Tab", r6))(o5 || {});

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/mouse.js
var g = ((f8) => (f8[f8.Left = 0] = "Left", f8[f8.Right = 2] = "Right", f8))(g || {});

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/document-ready.js
function t5(n9) {
  function e4() {
    document.readyState !== "loading" && (n9(), document.removeEventListener("DOMContentLoaded", e4));
  }
  typeof window != "undefined" && typeof document != "undefined" && (document.addEventListener("DOMContentLoaded", e4), e4());
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/active-element-history.js
var t6 = [];
t5(() => {
  function e4(n9) {
    n9.target instanceof HTMLElement && n9.target !== document.body && t6[0] !== n9.target && (t6.unshift(n9.target), t6 = t6.filter((r6) => r6 != null && r6.isConnected), t6.splice(10));
  }
  window.addEventListener("click", e4, { capture: true }), window.addEventListener("mousedown", e4, { capture: true }), window.addEventListener("focus", e4, { capture: true }), document.body.addEventListener("click", e4, { capture: true }), document.body.addEventListener("mousedown", e4, { capture: true }), document.body.addEventListener("focus", e4, { capture: true });
});

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/calculate-active-index.js
function u5(l7) {
  throw new Error("Unexpected object: " + l7);
}
var c3 = ((i13) => (i13[i13.First = 0] = "First", i13[i13.Previous = 1] = "Previous", i13[i13.Next = 2] = "Next", i13[i13.Last = 3] = "Last", i13[i13.Specific = 4] = "Specific", i13[i13.Nothing = 5] = "Nothing", i13))(c3 || {});
function f3(l7, n9) {
  let t8 = n9.resolveItems();
  if (t8.length <= 0) return null;
  let r6 = n9.resolveActiveIndex(), s14 = r6 != null ? r6 : -1;
  switch (l7.focus) {
    case 0: {
      for (let e4 = 0; e4 < t8.length; ++e4) if (!n9.resolveDisabled(t8[e4], e4, t8)) return e4;
      return r6;
    }
    case 1: {
      s14 === -1 && (s14 = t8.length);
      for (let e4 = s14 - 1; e4 >= 0; --e4) if (!n9.resolveDisabled(t8[e4], e4, t8)) return e4;
      return r6;
    }
    case 2: {
      for (let e4 = s14 + 1; e4 < t8.length; ++e4) if (!n9.resolveDisabled(t8[e4], e4, t8)) return e4;
      return r6;
    }
    case 3: {
      for (let e4 = t8.length - 1; e4 >= 0; --e4) if (!n9.resolveDisabled(t8[e4], e4, t8)) return e4;
      return r6;
    }
    case 4: {
      for (let e4 = 0; e4 < t8.length; ++e4) if (n9.resolveId(t8[e4], e4, t8) === l7.id) return e4;
      return r6;
    }
    case 5:
      return null;
    default:
      u5(l7);
  }
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/form.js
function e2(i13 = {}, s14 = null, t8 = []) {
  for (let [r6, n9] of Object.entries(i13)) o6(t8, f4(s14, r6), n9);
  return t8;
}
function f4(i13, s14) {
  return i13 ? i13 + "[" + s14 + "]" : s14;
}
function o6(i13, s14, t8) {
  if (Array.isArray(t8)) for (let [r6, n9] of t8.entries()) o6(i13, f4(s14, r6.toString()), n9);
  else t8 instanceof Date ? i13.push([s14, t8.toISOString()]) : typeof t8 == "boolean" ? i13.push([s14, t8 ? "1" : "0"]) : typeof t8 == "string" ? i13.push([s14, t8]) : typeof t8 == "number" ? i13.push([s14, `${t8}`]) : t8 == null ? i13.push([s14, ""]) : e2(t8, s14, i13);
}
function p4(i13) {
  var t8, r6;
  let s14 = (t8 = i13 == null ? void 0 : i13.form) != null ? t8 : i13.closest("form");
  if (s14) {
    for (let n9 of s14.elements) if (n9 !== i13 && (n9.tagName === "INPUT" && n9.type === "submit" || n9.tagName === "BUTTON" && n9.type === "submit" || n9.nodeName === "INPUT" && n9.type === "image")) {
      n9.click();
      return;
    }
    (r6 = s14.requestSubmit) == null || r6.call(s14);
  }
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/combobox/combobox.js
function De(a8, h6) {
  return a8 === h6;
}
var Ee = ((r6) => (r6[r6.Open = 0] = "Open", r6[r6.Closed = 1] = "Closed", r6))(Ee || {});
var Ve = ((r6) => (r6[r6.Single = 0] = "Single", r6[r6.Multi = 1] = "Multi", r6))(Ve || {});
var ke = ((y10) => (y10[y10.Pointer = 0] = "Pointer", y10[y10.Focus = 1] = "Focus", y10[y10.Other = 2] = "Other", y10))(ke || {});
var ne = Symbol("ComboboxContext");
function K(a8) {
  let h6 = ee(ne, null);
  if (h6 === null) {
    let r6 = new Error(`<${a8} /> is missing a parent <Combobox /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(r6, K), r6;
  }
  return h6;
}
var ie = Symbol("VirtualContext");
var Ae = H2({ name: "VirtualProvider", setup(a8, { slots: h6 }) {
  let r6 = K("VirtualProvider"), y10 = m(() => {
    let c7 = o3(r6.optionsRef);
    if (!c7) return { start: 0, end: 0 };
    let f8 = window.getComputedStyle(c7);
    return { start: parseFloat(f8.paddingBlockStart || f8.paddingTop), end: parseFloat(f8.paddingBlockEnd || f8.paddingBottom) };
  }), o11 = useVirtualizer(m(() => ({ scrollPaddingStart: y10.value.start, scrollPaddingEnd: y10.value.end, count: r6.virtual.value.options.length, estimateSize() {
    return 40;
  }, getScrollElement() {
    return o3(r6.optionsRef);
  }, overscan: 12 }))), u10 = m(() => {
    var c7;
    return (c7 = r6.virtual.value) == null ? void 0 : c7.options;
  }), e4 = k2(0);
  return J([u10], () => {
    e4.value += 1;
  }), te(ie, r6.virtual.value ? o11 : null), () => [z("div", { style: { position: "relative", width: "100%", height: `${o11.value.getTotalSize()}px` }, ref: (c7) => {
    if (c7) {
      if (typeof process != "undefined" && process.env.JEST_WORKER_ID !== void 0 || r6.activationTrigger.value === 0) return;
      r6.activeOptionIndex.value !== null && r6.virtual.value.options.length > r6.activeOptionIndex.value && o11.value.scrollToIndex(r6.activeOptionIndex.value);
    }
  } }, o11.value.getVirtualItems().map((c7) => de(h6.default({ option: r6.virtual.value.options[c7.index], open: r6.comboboxState.value === 0 })[0], { key: `${e4.value}-${c7.index}`, "data-index": c7.index, "aria-setsize": r6.virtual.value.options.length, "aria-posinset": c7.index + 1, style: { position: "absolute", top: 0, left: 0, transform: `translateY(${c7.start}px)`, overflowAnchor: "none" } })))];
} });
var lt = H2({ name: "Combobox", emits: { "update:modelValue": (a8) => true }, props: { as: { type: [Object, String], default: "template" }, disabled: { type: [Boolean], default: false }, by: { type: [String, Function], nullable: true, default: null }, modelValue: { type: [Object, String, Number, Boolean], default: void 0 }, defaultValue: { type: [Object, String, Number, Boolean], default: void 0 }, form: { type: String, optional: true }, name: { type: String, optional: true }, nullable: { type: Boolean, default: false }, multiple: { type: [Boolean], default: false }, immediate: { type: [Boolean], default: false }, virtual: { type: Object, default: null } }, inheritAttrs: false, setup(a8, { slots: h6, attrs: r6, emit: y10 }) {
  let o11 = k2(1), u10 = k2(null), e4 = k2(null), c7 = k2(null), f8 = k2(null), S4 = k2({ static: false, hold: false }), v9 = k2([]), d10 = k2(null), D4 = k2(2), E10 = k2(false);
  function w10(t8 = (n9) => n9) {
    let n9 = d10.value !== null ? v9.value[d10.value] : null, s14 = t8(v9.value.slice()), b5 = s14.length > 0 && s14[0].dataRef.order.value !== null ? s14.sort((C8, A7) => C8.dataRef.order.value - A7.dataRef.order.value) : O(s14, (C8) => o3(C8.dataRef.domRef)), O9 = n9 ? b5.indexOf(n9) : null;
    return O9 === -1 && (O9 = null), { options: b5, activeOptionIndex: O9 };
  }
  let M5 = m(() => a8.multiple ? 1 : 0), $8 = m(() => a8.nullable), [B5, p10] = d(m(() => a8.modelValue), (t8) => y10("update:modelValue", t8), m(() => a8.defaultValue)), R11 = m(() => B5.value === void 0 ? u(M5.value, { [1]: [], [0]: void 0 }) : B5.value), V3 = null, i13 = null;
  function I7(t8) {
    return u(M5.value, { [0]() {
      return p10 == null ? void 0 : p10(t8);
    }, [1]: () => {
      let n9 = L(l7.value.value).slice(), s14 = L(t8), b5 = n9.findIndex((O9) => l7.compare(s14, L(O9)));
      return b5 === -1 ? n9.push(s14) : n9.splice(b5, 1), p10 == null ? void 0 : p10(n9);
    } });
  }
  let T8 = m(() => {
  });
  J([T8], ([t8], [n9]) => {
    if (l7.virtual.value && t8 && n9 && d10.value !== null) {
      let s14 = t8.indexOf(n9[d10.value]);
      s14 !== -1 ? d10.value = s14 : d10.value = null;
    }
  });
  let l7 = { comboboxState: o11, value: R11, mode: M5, compare(t8, n9) {
    if (typeof a8.by == "string") {
      let s14 = a8.by;
      return (t8 == null ? void 0 : t8[s14]) === (n9 == null ? void 0 : n9[s14]);
    }
    return a8.by === null ? De(t8, n9) : a8.by(t8, n9);
  }, calculateIndex(t8) {
    return l7.virtual.value ? a8.by === null ? l7.virtual.value.options.indexOf(t8) : l7.virtual.value.options.findIndex((n9) => l7.compare(n9, t8)) : v9.value.findIndex((n9) => l7.compare(n9.dataRef.value, t8));
  }, defaultValue: m(() => a8.defaultValue), nullable: $8, immediate: m(() => false), virtual: m(() => null), inputRef: e4, labelRef: u10, buttonRef: c7, optionsRef: f8, disabled: m(() => a8.disabled), options: v9, change(t8) {
    p10(t8);
  }, activeOptionIndex: m(() => {
    if (E10.value && d10.value === null && (l7.virtual.value ? l7.virtual.value.options.length > 0 : v9.value.length > 0)) {
      if (l7.virtual.value) {
        let n9 = l7.virtual.value.options.findIndex((s14) => {
          var b5;
          return !((b5 = l7.virtual.value) != null && b5.disabled(s14));
        });
        if (n9 !== -1) return n9;
      }
      let t8 = v9.value.findIndex((n9) => !n9.dataRef.disabled);
      if (t8 !== -1) return t8;
    }
    return d10.value;
  }), activationTrigger: D4, optionsPropsRef: S4, closeCombobox() {
    E10.value = false, !a8.disabled && o11.value !== 1 && (o11.value = 1, d10.value = null);
  }, openCombobox() {
    if (E10.value = true, !a8.disabled && o11.value !== 0) {
      if (l7.value.value) {
        let t8 = l7.calculateIndex(l7.value.value);
        t8 !== -1 && (d10.value = t8);
      }
      o11.value = 0;
    }
  }, setActivationTrigger(t8) {
    D4.value = t8;
  }, goToOption(t8, n9, s14) {
    E10.value = false, V3 !== null && cancelAnimationFrame(V3), V3 = requestAnimationFrame(() => {
      if (a8.disabled || f8.value && !S4.value.static && o11.value === 1) return;
      if (l7.virtual.value) {
        d10.value = t8 === c3.Specific ? n9 : f3({ focus: t8 }, { resolveItems: () => l7.virtual.value.options, resolveActiveIndex: () => {
          var C8, A7;
          return (A7 = (C8 = l7.activeOptionIndex.value) != null ? C8 : l7.virtual.value.options.findIndex((j10) => {
            var q4;
            return !((q4 = l7.virtual.value) != null && q4.disabled(j10));
          })) != null ? A7 : null;
        }, resolveDisabled: (C8) => l7.virtual.value.disabled(C8), resolveId() {
          throw new Error("Function not implemented.");
        } }), D4.value = s14 != null ? s14 : 2;
        return;
      }
      let b5 = w10();
      if (b5.activeOptionIndex === null) {
        let C8 = b5.options.findIndex((A7) => !A7.dataRef.disabled);
        C8 !== -1 && (b5.activeOptionIndex = C8);
      }
      let O9 = t8 === c3.Specific ? n9 : f3({ focus: t8 }, { resolveItems: () => b5.options, resolveActiveIndex: () => b5.activeOptionIndex, resolveId: (C8) => C8.id, resolveDisabled: (C8) => C8.dataRef.disabled });
      d10.value = O9, D4.value = s14 != null ? s14 : 2, v9.value = b5.options;
    });
  }, selectOption(t8) {
    let n9 = v9.value.find((b5) => b5.id === t8);
    if (!n9) return;
    let { dataRef: s14 } = n9;
    I7(s14.value);
  }, selectActiveOption() {
    if (l7.activeOptionIndex.value !== null) {
      if (l7.virtual.value) I7(l7.virtual.value.options[l7.activeOptionIndex.value]);
      else {
        let { dataRef: t8 } = v9.value[l7.activeOptionIndex.value];
        I7(t8.value);
      }
      l7.goToOption(c3.Specific, l7.activeOptionIndex.value);
    }
  }, registerOption(t8, n9) {
    let s14 = ve({ id: t8, dataRef: n9 });
    if (l7.virtual.value) {
      v9.value.push(s14);
      return;
    }
    i13 && cancelAnimationFrame(i13);
    let b5 = w10((O9) => (O9.push(s14), O9));
    d10.value === null && l7.isSelected(n9.value.value) && (b5.activeOptionIndex = b5.options.indexOf(s14)), v9.value = b5.options, d10.value = b5.activeOptionIndex, D4.value = 2, b5.options.some((O9) => !o3(O9.dataRef.domRef)) && (i13 = requestAnimationFrame(() => {
      let O9 = w10();
      v9.value = O9.options, d10.value = O9.activeOptionIndex;
    }));
  }, unregisterOption(t8, n9) {
    if (V3 !== null && cancelAnimationFrame(V3), n9 && (E10.value = true), l7.virtual.value) {
      v9.value = v9.value.filter((b5) => b5.id !== t8);
      return;
    }
    let s14 = w10((b5) => {
      let O9 = b5.findIndex((C8) => C8.id === t8);
      return O9 !== -1 && b5.splice(O9, 1), b5;
    });
    v9.value = s14.options, d10.value = s14.activeOptionIndex, D4.value = 2;
  }, isSelected(t8) {
    return u(M5.value, { [0]: () => l7.compare(L(l7.value.value), L(t8)), [1]: () => L(l7.value.value).some((n9) => l7.compare(L(n9), L(t8))) });
  }, isActive(t8) {
    return d10.value === l7.calculateIndex(t8);
  } };
  w3([e4, c7, f8], () => l7.closeCombobox(), m(() => o11.value === 0)), te(ne, l7), t4(m(() => u(o11.value, { [0]: i9.Open, [1]: i9.Closed })));
  let g6 = m(() => {
    var t8;
    return (t8 = o3(e4)) == null ? void 0 : t8.closest("form");
  });
  return X(() => {
    J([g6], () => {
      if (!g6.value || a8.defaultValue === void 0) return;
      function t8() {
        l7.change(a8.defaultValue);
      }
      return g6.value.addEventListener("reset", t8), () => {
        var n9;
        (n9 = g6.value) == null || n9.removeEventListener("reset", t8);
      };
    }, { immediate: true });
  }), () => {
    var C8, A7, j10;
    let { name: t8, disabled: n9, form: s14, ...b5 } = a8, O9 = { open: o11.value === 0, disabled: n9, activeIndex: l7.activeOptionIndex.value, activeOption: l7.activeOptionIndex.value === null ? null : l7.virtual.value ? l7.virtual.value.options[(C8 = l7.activeOptionIndex.value) != null ? C8 : 0] : (j10 = (A7 = l7.options.value[l7.activeOptionIndex.value]) == null ? void 0 : A7.dataRef.value) != null ? j10 : null, value: R11.value };
    return z(se, [...t8 != null && R11.value != null ? e2({ [t8]: R11.value }).map(([q4, ue5]) => z(f2, E3({ features: u4.Hidden, key: q4, as: "input", type: "hidden", hidden: true, readOnly: true, form: s14, disabled: n9, name: q4, value: ue5 }))) : [], A({ theirProps: { ...r6, ...T2(b5, ["by", "defaultValue", "immediate", "modelValue", "multiple", "nullable", "onUpdate:modelValue", "virtual"]) }, ourProps: {}, slot: O9, slots: h6, attrs: r6, name: "Combobox" })]);
  };
} });
var at = H2({ name: "ComboboxLabel", props: { as: { type: [Object, String], default: "label" }, id: { type: String, default: null } }, setup(a8, { attrs: h6, slots: r6 }) {
  var e4;
  let y10 = (e4 = a8.id) != null ? e4 : `headlessui-combobox-label-${i2()}`, o11 = K("ComboboxLabel");
  function u10() {
    var c7;
    (c7 = o3(o11.inputRef)) == null || c7.focus({ preventScroll: true });
  }
  return () => {
    let c7 = { open: o11.comboboxState.value === 0, disabled: o11.disabled.value }, { ...f8 } = a8, S4 = { id: y10, ref: o11.labelRef, onClick: u10 };
    return A({ ourProps: S4, theirProps: f8, slot: c7, attrs: h6, slots: r6, name: "ComboboxLabel" });
  };
} });
var nt = H2({ name: "ComboboxButton", props: { as: { type: [Object, String], default: "button" }, id: { type: String, default: null } }, setup(a8, { attrs: h6, slots: r6, expose: y10 }) {
  var S4;
  let o11 = (S4 = a8.id) != null ? S4 : `headlessui-combobox-button-${i2()}`, u10 = K("ComboboxButton");
  y10({ el: u10.buttonRef, $el: u10.buttonRef });
  function e4(v9) {
    u10.disabled.value || (u10.comboboxState.value === 0 ? u10.closeCombobox() : (v9.preventDefault(), u10.openCombobox()), N3(() => {
      var d10;
      return (d10 = o3(u10.inputRef)) == null ? void 0 : d10.focus({ preventScroll: true });
    }));
  }
  function c7(v9) {
    switch (v9.key) {
      case o5.ArrowDown:
        v9.preventDefault(), v9.stopPropagation(), u10.comboboxState.value === 1 && u10.openCombobox(), N3(() => {
          var d10;
          return (d10 = u10.inputRef.value) == null ? void 0 : d10.focus({ preventScroll: true });
        });
        return;
      case o5.ArrowUp:
        v9.preventDefault(), v9.stopPropagation(), u10.comboboxState.value === 1 && (u10.openCombobox(), N3(() => {
          u10.value.value || u10.goToOption(c3.Last);
        })), N3(() => {
          var d10;
          return (d10 = u10.inputRef.value) == null ? void 0 : d10.focus({ preventScroll: true });
        });
        return;
      case o5.Escape:
        if (u10.comboboxState.value !== 0) return;
        v9.preventDefault(), u10.optionsRef.value && !u10.optionsPropsRef.value.static && v9.stopPropagation(), u10.closeCombobox(), N3(() => {
          var d10;
          return (d10 = u10.inputRef.value) == null ? void 0 : d10.focus({ preventScroll: true });
        });
        return;
    }
  }
  let f8 = s5(m(() => ({ as: a8.as, type: h6.type })), u10.buttonRef);
  return () => {
    var E10, w10;
    let v9 = { open: u10.comboboxState.value === 0, disabled: u10.disabled.value, value: u10.value.value }, { ...d10 } = a8, D4 = { ref: u10.buttonRef, id: o11, type: f8.value, tabindex: "-1", "aria-haspopup": "listbox", "aria-controls": (E10 = o3(u10.optionsRef)) == null ? void 0 : E10.id, "aria-expanded": u10.comboboxState.value === 0, "aria-labelledby": u10.labelRef.value ? [(w10 = o3(u10.labelRef)) == null ? void 0 : w10.id, o11].join(" ") : void 0, disabled: u10.disabled.value === true ? true : void 0, onKeydown: c7, onClick: e4 };
    return A({ ourProps: D4, theirProps: d10, slot: v9, attrs: h6, slots: r6, name: "ComboboxButton" });
  };
} });
var it = H2({ name: "ComboboxInput", props: { as: { type: [Object, String], default: "input" }, static: { type: Boolean, default: false }, unmount: { type: Boolean, default: true }, displayValue: { type: Function }, defaultValue: { type: String, default: void 0 }, id: { type: String, default: null } }, emits: { change: (a8) => true }, setup(a8, { emit: h6, attrs: r6, slots: y10, expose: o11 }) {
  var V3;
  let u10 = (V3 = a8.id) != null ? V3 : `headlessui-combobox-input-${i2()}`, e4 = K("ComboboxInput"), c7 = m(() => i4(o3(e4.inputRef))), f8 = { value: false };
  o11({ el: e4.inputRef, $el: e4.inputRef });
  function S4() {
    e4.change(null);
    let i13 = o3(e4.optionsRef);
    i13 && (i13.scrollTop = 0), e4.goToOption(c3.Nothing);
  }
  let v9 = m(() => {
    var I7;
    let i13 = e4.value.value;
    return o3(e4.inputRef) ? typeof a8.displayValue != "undefined" && i13 !== void 0 ? (I7 = a8.displayValue(i13)) != null ? I7 : "" : typeof i13 == "string" ? i13 : "" : "";
  });
  X(() => {
    J([v9, e4.comboboxState, c7], ([i13, I7], [T8, l7]) => {
      if (f8.value) return;
      let g6 = o3(e4.inputRef);
      g6 && ((l7 === 0 && I7 === 1 || i13 !== T8) && (g6.value = i13), requestAnimationFrame(() => {
        var s14;
        if (f8.value || !g6 || ((s14 = c7.value) == null ? void 0 : s14.activeElement) !== g6) return;
        let { selectionStart: t8, selectionEnd: n9 } = g6;
        Math.abs((n9 != null ? n9 : 0) - (t8 != null ? t8 : 0)) === 0 && t8 === 0 && g6.setSelectionRange(g6.value.length, g6.value.length);
      }));
    }, { immediate: true }), J([e4.comboboxState], ([i13], [I7]) => {
      if (i13 === 0 && I7 === 1) {
        if (f8.value) return;
        let T8 = o3(e4.inputRef);
        if (!T8) return;
        let l7 = T8.value, { selectionStart: g6, selectionEnd: t8, selectionDirection: n9 } = T8;
        T8.value = "", T8.value = l7, n9 !== null ? T8.setSelectionRange(g6, t8, n9) : T8.setSelectionRange(g6, t8);
      }
    });
  });
  let d10 = k2(false);
  function D4() {
    d10.value = true;
  }
  function E10() {
    o().nextFrame(() => {
      d10.value = false;
    });
  }
  let w10 = t2();
  function M5(i13) {
    switch (f8.value = true, w10(() => {
      f8.value = false;
    }), i13.key) {
      case o5.Enter:
        if (f8.value = false, e4.comboboxState.value !== 0 || d10.value) return;
        if (i13.preventDefault(), i13.stopPropagation(), e4.activeOptionIndex.value === null) {
          e4.closeCombobox();
          return;
        }
        e4.selectActiveOption(), e4.mode.value === 0 && e4.closeCombobox();
        break;
      case o5.ArrowDown:
        return f8.value = false, i13.preventDefault(), i13.stopPropagation(), u(e4.comboboxState.value, { [0]: () => e4.goToOption(c3.Next), [1]: () => e4.openCombobox() });
      case o5.ArrowUp:
        return f8.value = false, i13.preventDefault(), i13.stopPropagation(), u(e4.comboboxState.value, { [0]: () => e4.goToOption(c3.Previous), [1]: () => {
          e4.openCombobox(), N3(() => {
            e4.value.value || e4.goToOption(c3.Last);
          });
        } });
      case o5.Home:
        if (i13.shiftKey) break;
        return f8.value = false, i13.preventDefault(), i13.stopPropagation(), e4.goToOption(c3.First);
      case o5.PageUp:
        return f8.value = false, i13.preventDefault(), i13.stopPropagation(), e4.goToOption(c3.First);
      case o5.End:
        if (i13.shiftKey) break;
        return f8.value = false, i13.preventDefault(), i13.stopPropagation(), e4.goToOption(c3.Last);
      case o5.PageDown:
        return f8.value = false, i13.preventDefault(), i13.stopPropagation(), e4.goToOption(c3.Last);
      case o5.Escape:
        if (f8.value = false, e4.comboboxState.value !== 0) return;
        i13.preventDefault(), e4.optionsRef.value && !e4.optionsPropsRef.value.static && i13.stopPropagation(), e4.nullable.value && e4.mode.value === 0 && e4.value.value === null && S4(), e4.closeCombobox();
        break;
      case o5.Tab:
        if (f8.value = false, e4.comboboxState.value !== 0) return;
        e4.mode.value === 0 && e4.activationTrigger.value !== 1 && e4.selectActiveOption(), e4.closeCombobox();
        break;
    }
  }
  function $8(i13) {
    h6("change", i13), e4.nullable.value && e4.mode.value === 0 && i13.target.value === "" && S4(), e4.openCombobox();
  }
  function B5(i13) {
    var T8, l7, g6;
    let I7 = (T8 = i13.relatedTarget) != null ? T8 : t6.find((t8) => t8 !== i13.currentTarget);
    if (f8.value = false, !((l7 = o3(e4.optionsRef)) != null && l7.contains(I7)) && !((g6 = o3(e4.buttonRef)) != null && g6.contains(I7)) && e4.comboboxState.value === 0) return i13.preventDefault(), e4.mode.value === 0 && (e4.nullable.value && e4.value.value === null ? S4() : e4.activationTrigger.value !== 1 && e4.selectActiveOption()), e4.closeCombobox();
  }
  function p10(i13) {
    var T8, l7, g6;
    let I7 = (T8 = i13.relatedTarget) != null ? T8 : t6.find((t8) => t8 !== i13.currentTarget);
    (l7 = o3(e4.buttonRef)) != null && l7.contains(I7) || (g6 = o3(e4.optionsRef)) != null && g6.contains(I7) || e4.disabled.value || e4.immediate.value && e4.comboboxState.value !== 0 && (e4.openCombobox(), o().nextFrame(() => {
      e4.setActivationTrigger(1);
    }));
  }
  let R11 = m(() => {
    var i13, I7, T8, l7;
    return (l7 = (T8 = (I7 = a8.defaultValue) != null ? I7 : e4.defaultValue.value !== void 0 ? (i13 = a8.displayValue) == null ? void 0 : i13.call(a8, e4.defaultValue.value) : null) != null ? T8 : e4.defaultValue.value) != null ? l7 : "";
  });
  return () => {
    var t8, n9, s14, b5, O9, C8, A7;
    let i13 = { open: e4.comboboxState.value === 0 }, { displayValue: I7, onChange: T8, ...l7 } = a8, g6 = { "aria-controls": (t8 = e4.optionsRef.value) == null ? void 0 : t8.id, "aria-expanded": e4.comboboxState.value === 0, "aria-activedescendant": e4.activeOptionIndex.value === null ? void 0 : e4.virtual.value ? (n9 = e4.options.value.find((j10) => !e4.virtual.value.disabled(j10.dataRef.value) && e4.compare(j10.dataRef.value, e4.virtual.value.options[e4.activeOptionIndex.value]))) == null ? void 0 : n9.id : (s14 = e4.options.value[e4.activeOptionIndex.value]) == null ? void 0 : s14.id, "aria-labelledby": (C8 = (b5 = o3(e4.labelRef)) == null ? void 0 : b5.id) != null ? C8 : (O9 = o3(e4.buttonRef)) == null ? void 0 : O9.id, "aria-autocomplete": "list", id: u10, onCompositionstart: D4, onCompositionend: E10, onKeydown: M5, onInput: $8, onFocus: p10, onBlur: B5, role: "combobox", type: (A7 = r6.type) != null ? A7 : "text", tabIndex: 0, ref: e4.inputRef, defaultValue: R11.value, disabled: e4.disabled.value === true ? true : void 0 };
    return A({ ourProps: g6, theirProps: l7, slot: i13, attrs: r6, slots: y10, features: N2.RenderStrategy | N2.Static, name: "ComboboxInput" });
  };
} });
var ut = H2({ name: "ComboboxOptions", props: { as: { type: [Object, String], default: "ul" }, static: { type: Boolean, default: false }, unmount: { type: Boolean, default: true }, hold: { type: [Boolean], default: false } }, setup(a8, { attrs: h6, slots: r6, expose: y10 }) {
  let o11 = K("ComboboxOptions"), u10 = `headlessui-combobox-options-${i2()}`;
  y10({ el: o11.optionsRef, $el: o11.optionsRef }), Y(() => {
    o11.optionsPropsRef.value.static = a8.static;
  }), Y(() => {
    o11.optionsPropsRef.value.hold = a8.hold;
  });
  let e4 = l2(), c7 = m(() => e4 !== null ? (e4.value & i9.Open) === i9.Open : o11.comboboxState.value === 0);
  i8({ container: m(() => o3(o11.optionsRef)), enabled: m(() => o11.comboboxState.value === 0), accept(S4) {
    return S4.getAttribute("role") === "option" ? NodeFilter.FILTER_REJECT : S4.hasAttribute("role") ? NodeFilter.FILTER_SKIP : NodeFilter.FILTER_ACCEPT;
  }, walk(S4) {
    S4.setAttribute("role", "none");
  } });
  function f8(S4) {
    S4.preventDefault();
  }
  return () => {
    var D4, E10, w10;
    let S4 = { open: o11.comboboxState.value === 0 }, v9 = { "aria-labelledby": (w10 = (D4 = o3(o11.labelRef)) == null ? void 0 : D4.id) != null ? w10 : (E10 = o3(o11.buttonRef)) == null ? void 0 : E10.id, id: u10, ref: o11.optionsRef, role: "listbox", "aria-multiselectable": o11.mode.value === 1 ? true : void 0, onMousedown: f8 }, d10 = T2(a8, ["hold"]);
    return A({ ourProps: v9, theirProps: d10, slot: S4, attrs: h6, slots: o11.virtual.value && o11.comboboxState.value === 0 ? { ...r6, default: () => [z(Ae, {}, r6.default)] } : r6, features: N2.RenderStrategy | N2.Static, visible: c7.value, name: "ComboboxOptions" });
  };
} });
var rt = H2({ name: "ComboboxOption", props: { as: { type: [Object, String], default: "li" }, value: { type: [Object, String, Number, Boolean] }, disabled: { type: Boolean, default: false }, order: { type: [Number], default: null } }, setup(a8, { slots: h6, attrs: r6, expose: y10 }) {
  let o11 = K("ComboboxOption"), u10 = `headlessui-combobox-option-${i2()}`, e4 = k2(null), c7 = m(() => a8.disabled);
  y10({ el: e4, $el: e4 });
  let f8 = m(() => {
    var p10;
    return o11.virtual.value ? o11.activeOptionIndex.value === o11.calculateIndex(a8.value) : o11.activeOptionIndex.value === null ? false : ((p10 = o11.options.value[o11.activeOptionIndex.value]) == null ? void 0 : p10.id) === u10;
  }), S4 = m(() => o11.isSelected(a8.value)), v9 = ee(ie, null), d10 = m(() => ({ disabled: a8.disabled, value: a8.value, domRef: e4, order: m(() => a8.order) }));
  X(() => o11.registerOption(u10, d10)), fe(() => o11.unregisterOption(u10, f8.value)), Y(() => {
    let p10 = o3(e4);
    p10 && (v9 == null || v9.value.measureElement(p10));
  }), Y(() => {
    o11.comboboxState.value === 0 && f8.value && (o11.virtual.value || o11.activationTrigger.value !== 0 && N3(() => {
      var p10, R11;
      return (R11 = (p10 = o3(e4)) == null ? void 0 : p10.scrollIntoView) == null ? void 0 : R11.call(p10, { block: "nearest" });
    }));
  });
  function D4(p10) {
    p10.preventDefault(), p10.button === g.Left && (c7.value || (o11.selectOption(u10), n3() || requestAnimationFrame(() => {
      var R11;
      return (R11 = o3(o11.inputRef)) == null ? void 0 : R11.focus({ preventScroll: true });
    }), o11.mode.value === 0 && o11.closeCombobox()));
  }
  function E10() {
    var R11;
    if (a8.disabled || (R11 = o11.virtual.value) != null && R11.disabled(a8.value)) return o11.goToOption(c3.Nothing);
    let p10 = o11.calculateIndex(a8.value);
    o11.goToOption(c3.Specific, p10);
  }
  let w10 = u3();
  function M5(p10) {
    w10.update(p10);
  }
  function $8(p10) {
    var V3;
    if (!w10.wasMoved(p10) || a8.disabled || (V3 = o11.virtual.value) != null && V3.disabled(a8.value) || f8.value) return;
    let R11 = o11.calculateIndex(a8.value);
    o11.goToOption(c3.Specific, R11, 0);
  }
  function B5(p10) {
    var R11;
    w10.wasMoved(p10) && (a8.disabled || (R11 = o11.virtual.value) != null && R11.disabled(a8.value) || f8.value && (o11.optionsPropsRef.value.hold || o11.goToOption(c3.Nothing)));
  }
  return () => {
    let { disabled: p10 } = a8, R11 = { active: f8.value, selected: S4.value, disabled: p10 }, V3 = { id: u10, ref: e4, role: "option", tabIndex: p10 === true ? void 0 : -1, "aria-disabled": p10 === true ? true : void 0, "aria-selected": S4.value, disabled: void 0, onMousedown: D4, onFocus: E10, onPointerenter: M5, onMouseenter: M5, onPointermove: $8, onMousemove: $8, onPointerleave: B5, onMouseleave: B5 }, i13 = T2(a8, ["order", "value"]);
    return A({ ourProps: V3, theirProps: i13, slot: R11, attrs: r6, slots: h6, name: "ComboboxOption" });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/dialog/dialog.js
import { computed as o8, defineComponent as O4, h as v5, inject as Y2, nextTick as se2, onMounted as $3, onUnmounted as pe, provide as de2, ref as y6, watchEffect as fe2 } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/focus-trap/focus-trap.js
import { computed as L2, defineComponent as I2, Fragment as j2, h as R, onMounted as M, onUnmounted as h2, ref as E5, watch as g2, watchEffect as K2 } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-event-listener.js
import { watchEffect as i10 } from "vue";
function E4(n9, e4, o11, r6) {
  c.isServer || i10((t8) => {
    n9 = n9 != null ? n9 : window, n9.addEventListener(e4, o11, r6), t8(() => n9.removeEventListener(e4, o11, r6));
  });
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-tab-direction.js
import { ref as a2 } from "vue";
var d3 = ((r6) => (r6[r6.Forwards = 0] = "Forwards", r6[r6.Backwards = 1] = "Backwards", r6))(d3 || {});
function n5() {
  let o11 = a2(0);
  return w2("keydown", (e4) => {
    e4.key === "Tab" && (o11.value = e4.shiftKey ? 1 : 0);
  }), o11;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/focus-trap/focus-trap.js
function B(t8) {
  if (!t8) return /* @__PURE__ */ new Set();
  if (typeof t8 == "function") return new Set(t8());
  let n9 = /* @__PURE__ */ new Set();
  for (let r6 of t8.value) {
    let l7 = o3(r6);
    l7 instanceof HTMLElement && n9.add(l7);
  }
  return n9;
}
var A2 = ((e4) => (e4[e4.None = 1] = "None", e4[e4.InitialFocus = 2] = "InitialFocus", e4[e4.TabLock = 4] = "TabLock", e4[e4.FocusLock = 8] = "FocusLock", e4[e4.RestoreFocus = 16] = "RestoreFocus", e4[e4.All = 30] = "All", e4))(A2 || {});
var ue = Object.assign(I2({ name: "FocusTrap", props: { as: { type: [Object, String], default: "div" }, initialFocus: { type: Object, default: null }, features: { type: Number, default: 30 }, containers: { type: [Object, Function], default: E5(/* @__PURE__ */ new Set()) } }, inheritAttrs: false, setup(t8, { attrs: n9, slots: r6, expose: l7 }) {
  let o11 = E5(null);
  l7({ el: o11, $el: o11 });
  let i13 = L2(() => i4(o11)), e4 = E5(false);
  M(() => e4.value = true), h2(() => e4.value = false), $({ ownerDocument: i13 }, L2(() => e4.value && Boolean(t8.features & 16)));
  let m12 = z2({ ownerDocument: i13, container: o11, initialFocus: L2(() => t8.initialFocus) }, L2(() => e4.value && Boolean(t8.features & 2)));
  J2({ ownerDocument: i13, container: o11, containers: t8.containers, previousActiveElement: m12 }, L2(() => e4.value && Boolean(t8.features & 8)));
  let f8 = n5();
  function a8(u10) {
    let T8 = o3(o11);
    if (!T8) return;
    ((w10) => w10())(() => {
      u(f8.value, { [d3.Forwards]: () => {
        P(T8, N.First, { skipElements: [u10.relatedTarget] });
      }, [d3.Backwards]: () => {
        P(T8, N.Last, { skipElements: [u10.relatedTarget] });
      } });
    });
  }
  let s14 = E5(false);
  function F6(u10) {
    u10.key === "Tab" && (s14.value = true, requestAnimationFrame(() => {
      s14.value = false;
    }));
  }
  function H8(u10) {
    if (!e4.value) return;
    let T8 = B(t8.containers);
    o3(o11) instanceof HTMLElement && T8.add(o3(o11));
    let d10 = u10.relatedTarget;
    d10 instanceof HTMLElement && d10.dataset.headlessuiFocusGuard !== "true" && (N4(T8, d10) || (s14.value ? P(o3(o11), u(f8.value, { [d3.Forwards]: () => N.Next, [d3.Backwards]: () => N.Previous }) | N.WrapAround, { relativeTo: u10.target }) : u10.target instanceof HTMLElement && S(u10.target)));
  }
  return () => {
    let u10 = {}, T8 = { ref: o11, onKeydown: F6, onFocusout: H8 }, { features: d10, initialFocus: w10, containers: Q4, ...O9 } = t8;
    return R(j2, [Boolean(d10 & 4) && R(f2, { as: "button", type: "button", "data-headlessui-focus-guard": true, onFocus: a8, features: u4.Focusable }), A({ ourProps: T8, theirProps: { ...n9, ...O9 }, slot: u10, attrs: n9, slots: r6, name: "FocusTrap" }), Boolean(d10 & 4) && R(f2, { as: "button", type: "button", "data-headlessui-focus-guard": true, onFocus: a8, features: u4.Focusable })]);
  };
} }), { features: A2 });
function W(t8) {
  let n9 = E5(t6.slice());
  return g2([t8], ([r6], [l7]) => {
    l7 === true && r6 === false ? t(() => {
      n9.value.splice(0);
    }) : l7 === false && r6 === true && (n9.value = t6.slice());
  }, { flush: "post" }), () => {
    var r6;
    return (r6 = n9.value.find((l7) => l7 != null && l7.isConnected)) != null ? r6 : null;
  };
}
function $({ ownerDocument: t8 }, n9) {
  let r6 = W(n9);
  M(() => {
    K2(() => {
      var l7, o11;
      n9.value || ((l7 = t8.value) == null ? void 0 : l7.activeElement) === ((o11 = t8.value) == null ? void 0 : o11.body) && S(r6());
    }, { flush: "post" });
  }), h2(() => {
    n9.value && S(r6());
  });
}
function z2({ ownerDocument: t8, container: n9, initialFocus: r6 }, l7) {
  let o11 = E5(null), i13 = E5(false);
  return M(() => i13.value = true), h2(() => i13.value = false), M(() => {
    g2([n9, r6, l7], (e4, m12) => {
      if (e4.every((a8, s14) => (m12 == null ? void 0 : m12[s14]) === a8) || !l7.value) return;
      let f8 = o3(n9);
      f8 && t(() => {
        var F6, H8;
        if (!i13.value) return;
        let a8 = o3(r6), s14 = (F6 = t8.value) == null ? void 0 : F6.activeElement;
        if (a8) {
          if (a8 === s14) {
            o11.value = s14;
            return;
          }
        } else if (f8.contains(s14)) {
          o11.value = s14;
          return;
        }
        a8 ? S(a8) : P(f8, N.First | N.NoScroll) === T.Error && console.warn("There are no focusable elements inside the <FocusTrap />"), o11.value = (H8 = t8.value) == null ? void 0 : H8.activeElement;
      });
    }, { immediate: true, flush: "post" });
  }), o11;
}
function J2({ ownerDocument: t8, container: n9, containers: r6, previousActiveElement: l7 }, o11) {
  var i13;
  E4((i13 = t8.value) == null ? void 0 : i13.defaultView, "focus", (e4) => {
    if (!o11.value) return;
    let m12 = B(r6);
    o3(n9) instanceof HTMLElement && m12.add(o3(n9));
    let f8 = l7.value;
    if (!f8) return;
    let a8 = e4.target;
    a8 && a8 instanceof HTMLElement ? N4(m12, a8) ? (l7.value = a8, S(a8)) : (e4.preventDefault(), e4.stopPropagation(), S(f8)) : S(l7.value);
  }, true);
}
function N4(t8, n9) {
  for (let r6 of t8) if (r6.contains(n9)) return true;
  return false;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/document-overflow/use-document-overflow.js
import { computed as p5, watch as s7 } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-store.js
import { onUnmounted as o7, shallowRef as n6 } from "vue";
function m2(t8) {
  let e4 = n6(t8.getSnapshot());
  return o7(t8.subscribe(() => {
    e4.value = t8.getSnapshot();
  })), e4;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/store.js
function a3(o11, r6) {
  let t8 = o11(), n9 = /* @__PURE__ */ new Set();
  return { getSnapshot() {
    return t8;
  }, subscribe(e4) {
    return n9.add(e4), () => n9.delete(e4);
  }, dispatch(e4, ...s14) {
    let i13 = r6[e4].call(t8, ...s14);
    i13 && (t8 = i13, n9.forEach((c7) => c7()));
  } };
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/document-overflow/adjust-scrollbar-padding.js
function c4() {
  let o11;
  return { before({ doc: e4 }) {
    var l7;
    let n9 = e4.documentElement;
    o11 = ((l7 = e4.defaultView) != null ? l7 : window).innerWidth - n9.clientWidth;
  }, after({ doc: e4, d: n9 }) {
    let t8 = e4.documentElement, l7 = t8.clientWidth - t8.offsetWidth, r6 = o11 - l7;
    n9.style(t8, "paddingRight", `${r6}px`);
  } };
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/document-overflow/handle-ios-locking.js
function w4() {
  return t3() ? { before({ doc: r6, d: n9, meta: c7 }) {
    function a8(o11) {
      return c7.containers.flatMap((l7) => l7()).some((l7) => l7.contains(o11));
    }
    n9.microTask(() => {
      var s14;
      if (window.getComputedStyle(r6.documentElement).scrollBehavior !== "auto") {
        let t8 = o();
        t8.style(r6.documentElement, "scrollBehavior", "auto"), n9.add(() => n9.microTask(() => t8.dispose()));
      }
      let o11 = (s14 = window.scrollY) != null ? s14 : window.pageYOffset, l7 = null;
      n9.addEventListener(r6, "click", (t8) => {
        if (t8.target instanceof HTMLElement) try {
          let e4 = t8.target.closest("a");
          if (!e4) return;
          let { hash: f8 } = new URL(e4.href), i13 = r6.querySelector(f8);
          i13 && !a8(i13) && (l7 = i13);
        } catch {
        }
      }, true), n9.addEventListener(r6, "touchstart", (t8) => {
        if (t8.target instanceof HTMLElement) if (a8(t8.target)) {
          let e4 = t8.target;
          for (; e4.parentElement && a8(e4.parentElement); ) e4 = e4.parentElement;
          n9.style(e4, "overscrollBehavior", "contain");
        } else n9.style(t8.target, "touchAction", "none");
      }), n9.addEventListener(r6, "touchmove", (t8) => {
        if (t8.target instanceof HTMLElement) {
          if (t8.target.tagName === "INPUT") return;
          if (a8(t8.target)) {
            let e4 = t8.target;
            for (; e4.parentElement && e4.dataset.headlessuiPortal !== "" && !(e4.scrollHeight > e4.clientHeight || e4.scrollWidth > e4.clientWidth); ) e4 = e4.parentElement;
            e4.dataset.headlessuiPortal === "" && t8.preventDefault();
          } else t8.preventDefault();
        }
      }, { passive: false }), n9.add(() => {
        var e4;
        let t8 = (e4 = window.scrollY) != null ? e4 : window.pageYOffset;
        o11 !== t8 && window.scrollTo(0, o11), l7 && l7.isConnected && (l7.scrollIntoView({ block: "nearest" }), l7 = null);
      });
    });
  } } : {};
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/document-overflow/prevent-scroll.js
function l3() {
  return { before({ doc: e4, d: o11 }) {
    o11.style(e4.documentElement, "overflow", "hidden");
  } };
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/document-overflow/overflow-store.js
function m3(e4) {
  let n9 = {};
  for (let t8 of e4) Object.assign(n9, t8(n9));
  return n9;
}
var a4 = a3(() => /* @__PURE__ */ new Map(), { PUSH(e4, n9) {
  var o11;
  let t8 = (o11 = this.get(e4)) != null ? o11 : { doc: e4, count: 0, d: o(), meta: /* @__PURE__ */ new Set() };
  return t8.count++, t8.meta.add(n9), this.set(e4, t8), this;
}, POP(e4, n9) {
  let t8 = this.get(e4);
  return t8 && (t8.count--, t8.meta.delete(n9)), this;
}, SCROLL_PREVENT({ doc: e4, d: n9, meta: t8 }) {
  let o11 = { doc: e4, d: n9, meta: m3(t8) }, c7 = [w4(), c4(), l3()];
  c7.forEach(({ before: r6 }) => r6 == null ? void 0 : r6(o11)), c7.forEach(({ after: r6 }) => r6 == null ? void 0 : r6(o11));
}, SCROLL_ALLOW({ d: e4 }) {
  e4.dispose();
}, TEARDOWN({ doc: e4 }) {
  this.delete(e4);
} });
a4.subscribe(() => {
  let e4 = a4.getSnapshot(), n9 = /* @__PURE__ */ new Map();
  for (let [t8] of e4) n9.set(t8, t8.documentElement.style.overflow);
  for (let t8 of e4.values()) {
    let o11 = n9.get(t8.doc) === "hidden", c7 = t8.count !== 0;
    (c7 && !o11 || !c7 && o11) && a4.dispatch(t8.count > 0 ? "SCROLL_PREVENT" : "SCROLL_ALLOW", t8), t8.count === 0 && a4.dispatch("TEARDOWN", t8);
  }
});

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/document-overflow/use-document-overflow.js
function d4(t8, a8, n9) {
  let i13 = m2(a4), l7 = p5(() => {
    let e4 = t8.value ? i13.value.get(t8.value) : void 0;
    return e4 ? e4.count > 0 : false;
  });
  return s7([t8, a8], ([e4, m12], [r6], o11) => {
    if (!e4 || !m12) return;
    a4.dispatch("PUSH", e4, n9);
    let f8 = false;
    o11(() => {
      f8 || (a4.dispatch("POP", r6 != null ? r6 : e4, n9), f8 = true);
    });
  }, { immediate: true }), l7;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-inert.js
import { ref as m4, watchEffect as s8 } from "vue";
var i11 = /* @__PURE__ */ new Map();
var t7 = /* @__PURE__ */ new Map();
function E6(d10, f8 = m4(true)) {
  s8((o11) => {
    var a8;
    if (!f8.value) return;
    let e4 = o3(d10);
    if (!e4) return;
    o11(function() {
      var u10;
      if (!e4) return;
      let r6 = (u10 = t7.get(e4)) != null ? u10 : 1;
      if (r6 === 1 ? t7.delete(e4) : t7.set(e4, r6 - 1), r6 !== 1) return;
      let n9 = i11.get(e4);
      n9 && (n9["aria-hidden"] === null ? e4.removeAttribute("aria-hidden") : e4.setAttribute("aria-hidden", n9["aria-hidden"]), e4.inert = n9.inert, i11.delete(e4));
    });
    let l7 = (a8 = t7.get(e4)) != null ? a8 : 0;
    t7.set(e4, l7 + 1), l7 === 0 && (i11.set(e4, { "aria-hidden": e4.getAttribute("aria-hidden"), inert: e4.inert }), e4.setAttribute("aria-hidden", "true"), e4.inert = true);
  });
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-root-containers.js
import { h as m5, ref as s9 } from "vue";
function N5({ defaultContainers: o11 = [], portals: i13, mainTreeNodeRef: H8 } = {}) {
  let t8 = s9(null), r6 = i4(t8);
  function u10() {
    var l7, f8, a8;
    let n9 = [];
    for (let e4 of o11) e4 !== null && (e4 instanceof HTMLElement ? n9.push(e4) : "value" in e4 && e4.value instanceof HTMLElement && n9.push(e4.value));
    if (i13 != null && i13.value) for (let e4 of i13.value) n9.push(e4);
    for (let e4 of (l7 = r6 == null ? void 0 : r6.querySelectorAll("html > *, body > *")) != null ? l7 : []) e4 !== document.body && e4 !== document.head && e4 instanceof HTMLElement && e4.id !== "headlessui-portal-root" && (e4.contains(o3(t8)) || e4.contains((a8 = (f8 = o3(t8)) == null ? void 0 : f8.getRootNode()) == null ? void 0 : a8.host) || n9.some((M5) => e4.contains(M5)) || n9.push(e4));
    return n9;
  }
  return { resolveContainers: u10, contains(n9) {
    return u10().some((l7) => l7.contains(n9));
  }, mainTreeNodeRef: t8, MainTreeNode() {
    return H8 != null ? null : m5(f2, { features: u4.Hidden, ref: t8 });
  } };
}
function v3() {
  let o11 = s9(null);
  return { mainTreeNodeRef: o11, MainTreeNode() {
    return m5(f2, { features: u4.Hidden, ref: o11 });
  } };
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/internal/portal-force-root.js
import { defineComponent as l4, inject as a5, provide as c5 } from "vue";
var e3 = Symbol("ForcePortalRootContext");
function s10() {
  return a5(e3, false);
}
var u6 = l4({ name: "ForcePortalRoot", props: { as: { type: [Object, String], default: "template" }, force: { type: Boolean, default: false } }, setup(o11, { slots: t8, attrs: r6 }) {
  return c5(e3, o11.force), () => {
    let { force: f8, ...n9 } = o11;
    return A({ theirProps: n9, ourProps: {}, slot: {}, slots: t8, attrs: r6, name: "ForcePortalRoot" });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/internal/stack-context.js
import { inject as f5, onMounted as m6, onUnmounted as l5, provide as c6, watch as p6 } from "vue";
var u7 = Symbol("StackContext");
var s11 = ((e4) => (e4[e4.Add = 0] = "Add", e4[e4.Remove = 1] = "Remove", e4))(s11 || {});
function y3() {
  return f5(u7, () => {
  });
}
function R2({ type: o11, enabled: r6, element: e4, onUpdate: i13 }) {
  let a8 = y3();
  function t8(...n9) {
    i13 == null || i13(...n9), a8(...n9);
  }
  m6(() => {
    p6(r6, (n9, d10) => {
      n9 ? t8(0, o11, e4) : d10 === true && t8(1, o11, e4);
    }, { immediate: true, flush: "sync" });
  }), l5(() => {
    r6.value && t8(1, o11, e4);
  }), c6(u7, t8);
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/description/description.js
import { computed as x2, defineComponent as y4, inject as R3, onMounted as v4, onUnmounted as D, provide as j3, ref as p7, unref as C } from "vue";
var u8 = Symbol("DescriptionContext");
function w5() {
  let t8 = R3(u8, null);
  if (t8 === null) throw new Error("Missing parent");
  return t8;
}
function k3({ slot: t8 = p7({}), name: o11 = "Description", props: s14 = {} } = {}) {
  let e4 = p7([]);
  function r6(n9) {
    return e4.value.push(n9), () => {
      let i13 = e4.value.indexOf(n9);
      i13 !== -1 && e4.value.splice(i13, 1);
    };
  }
  return j3(u8, { register: r6, slot: t8, name: o11, props: s14 }), x2(() => e4.value.length > 0 ? e4.value.join(" ") : void 0);
}
var K3 = y4({ name: "Description", props: { as: { type: [Object, String], default: "p" }, id: { type: String, default: null } }, setup(t8, { attrs: o11, slots: s14 }) {
  var n9;
  let e4 = (n9 = t8.id) != null ? n9 : `headlessui-description-${i2()}`, r6 = w5();
  return v4(() => D(r6.register(e4))), () => {
    let { name: i13 = "Description", slot: l7 = p7({}), props: d10 = {} } = r6, { ...c7 } = t8, f8 = { ...Object.entries(d10).reduce((a8, [g6, m12]) => Object.assign(a8, { [g6]: C(m12) }), {}), id: e4 };
    return A({ ourProps: f8, theirProps: c7, slot: l7.value, attrs: o11, slots: s14, name: i13 });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/portal/portal.js
import { computed as w6, defineComponent as m7, getCurrentInstance as j4, h as I3, inject as s12, onMounted as R4, onUnmounted as y5, provide as E7, reactive as G, ref as p8, Teleport as O3, watch as D2, watchEffect as K4 } from "vue";
function x3(e4) {
  let t8 = i4(e4);
  if (!t8) {
    if (e4 === null) return null;
    throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e4}`);
  }
  let l7 = t8.getElementById("headlessui-portal-root");
  if (l7) return l7;
  let r6 = t8.createElement("div");
  return r6.setAttribute("id", "headlessui-portal-root"), t8.body.appendChild(r6);
}
var f6 = /* @__PURE__ */ new WeakMap();
function U(e4) {
  var t8;
  return (t8 = f6.get(e4)) != null ? t8 : 0;
}
function M2(e4, t8) {
  let l7 = t8(U(e4));
  return l7 <= 0 ? f6.delete(e4) : f6.set(e4, l7), l7;
}
var $2 = m7({ name: "Portal", props: { as: { type: [Object, String], default: "div" } }, setup(e4, { slots: t8, attrs: l7 }) {
  let r6 = p8(null), i13 = w6(() => i4(r6)), o11 = s10(), u10 = s12(H3, null), n9 = p8(o11 === true || u10 == null ? x3(r6.value) : u10.resolveTarget());
  n9.value && M2(n9.value, (a8) => a8 + 1);
  let c7 = p8(false);
  R4(() => {
    c7.value = true;
  }), K4(() => {
    o11 || u10 != null && (n9.value = u10.resolveTarget());
  });
  let v9 = s12(d5, null), g6 = false, b5 = j4();
  return D2(r6, () => {
    if (g6 || !v9) return;
    let a8 = o3(r6);
    a8 && (y5(v9.register(a8), b5), g6 = true);
  }), y5(() => {
    var P6, T8;
    let a8 = (P6 = i13.value) == null ? void 0 : P6.getElementById("headlessui-portal-root");
    !a8 || n9.value !== a8 || M2(n9.value, (L9) => L9 - 1) || n9.value.children.length > 0 || (T8 = n9.value.parentElement) == null || T8.removeChild(n9.value);
  }), () => {
    if (!c7.value || n9.value === null) return null;
    let a8 = { ref: r6, "data-headlessui-portal": "" };
    return I3(O3, { to: n9.value }, A({ ourProps: a8, theirProps: e4, slot: {}, attrs: l7, slots: t8, name: "Portal" }));
  };
} });
var d5 = Symbol("PortalParentContext");
function q() {
  let e4 = s12(d5, null), t8 = p8([]);
  function l7(o11) {
    return t8.value.push(o11), e4 && e4.register(o11), () => r6(o11);
  }
  function r6(o11) {
    let u10 = t8.value.indexOf(o11);
    u10 !== -1 && t8.value.splice(u10, 1), e4 && e4.unregister(o11);
  }
  let i13 = { register: l7, unregister: r6, portals: t8 };
  return [t8, m7({ name: "PortalWrapper", setup(o11, { slots: u10 }) {
    return E7(d5, i13), () => {
      var n9;
      return (n9 = u10.default) == null ? void 0 : n9.call(u10);
    };
  } })];
}
var H3 = Symbol("PortalGroupContext");
var z3 = m7({ name: "PortalGroup", props: { as: { type: [Object, String], default: "template" }, target: { type: Object, default: null } }, setup(e4, { attrs: t8, slots: l7 }) {
  let r6 = G({ resolveTarget() {
    return e4.target;
  } });
  return E7(H3, r6), () => {
    let { target: i13, ...o11 } = e4;
    return A({ theirProps: o11, ourProps: {}, slot: {}, attrs: t8, slots: l7, name: "PortalGroup" });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/dialog/dialog.js
var Te = ((l7) => (l7[l7.Open = 0] = "Open", l7[l7.Closed = 1] = "Closed", l7))(Te || {});
var H4 = Symbol("DialogContext");
function T3(t8) {
  let i13 = Y2(H4, null);
  if (i13 === null) {
    let l7 = new Error(`<${t8} /> is missing a parent <Dialog /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(l7, T3), l7;
  }
  return i13;
}
var A3 = "DC8F892D-2EBD-447C-A4C8-A03058436FF4";
var Ye = O4({ name: "Dialog", inheritAttrs: false, props: { as: { type: [Object, String], default: "div" }, static: { type: Boolean, default: false }, unmount: { type: Boolean, default: true }, open: { type: [Boolean, String], default: A3 }, initialFocus: { type: Object, default: null }, id: { type: String, default: null }, role: { type: String, default: "dialog" } }, emits: { close: (t8) => true }, setup(t8, { emit: i13, attrs: l7, slots: p10, expose: s14 }) {
  var q4, W7;
  let n9 = (q4 = t8.id) != null ? q4 : `headlessui-dialog-${i2()}`, u10 = y6(false);
  $3(() => {
    u10.value = true;
  });
  let r6 = false, g6 = o8(() => t8.role === "dialog" || t8.role === "alertdialog" ? t8.role : (r6 || (r6 = true, console.warn(`Invalid role [${g6}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)), "dialog")), D4 = y6(0), S4 = l2(), R11 = o8(() => t8.open === A3 && S4 !== null ? (S4.value & i9.Open) === i9.Open : t8.open), m12 = y6(null), E10 = o8(() => i4(m12));
  if (s14({ el: m12, $el: m12 }), !(t8.open !== A3 || S4 !== null)) throw new Error("You forgot to provide an `open` prop to the `Dialog`.");
  if (typeof R11.value != "boolean") throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${R11.value === A3 ? void 0 : t8.open}`);
  let c7 = o8(() => u10.value && R11.value ? 0 : 1), k9 = o8(() => c7.value === 0), w10 = o8(() => D4.value > 1), N13 = Y2(H4, null) !== null, [Q4, X3] = q(), { resolveContainers: B5, mainTreeNodeRef: K11, MainTreeNode: Z3 } = N5({ portals: Q4, defaultContainers: [o8(() => {
    var e4;
    return (e4 = h6.panelRef.value) != null ? e4 : m12.value;
  })] }), ee5 = o8(() => w10.value ? "parent" : "leaf"), U7 = o8(() => S4 !== null ? (S4.value & i9.Closing) === i9.Closing : false), te6 = o8(() => N13 || U7.value ? false : k9.value), le6 = o8(() => {
    var e4, a8, d10;
    return (d10 = Array.from((a8 = (e4 = E10.value) == null ? void 0 : e4.querySelectorAll("body > *")) != null ? a8 : []).find((f8) => f8.id === "headlessui-portal-root" ? false : f8.contains(o3(K11)) && f8 instanceof HTMLElement)) != null ? d10 : null;
  });
  E6(le6, te6);
  let ae4 = o8(() => w10.value ? true : k9.value), oe3 = o8(() => {
    var e4, a8, d10;
    return (d10 = Array.from((a8 = (e4 = E10.value) == null ? void 0 : e4.querySelectorAll("[data-headlessui-portal]")) != null ? a8 : []).find((f8) => f8.contains(o3(K11)) && f8 instanceof HTMLElement)) != null ? d10 : null;
  });
  E6(oe3, ae4), R2({ type: "Dialog", enabled: o8(() => c7.value === 0), element: m12, onUpdate: (e4, a8) => {
    if (a8 === "Dialog") return u(e4, { [s11.Add]: () => D4.value += 1, [s11.Remove]: () => D4.value -= 1 });
  } });
  let re3 = k3({ name: "DialogDescription", slot: o8(() => ({ open: R11.value })) }), M5 = y6(null), h6 = { titleId: M5, panelRef: y6(null), dialogState: c7, setTitleId(e4) {
    M5.value !== e4 && (M5.value = e4);
  }, close() {
    i13("close", false);
  } };
  de2(H4, h6);
  let ne3 = o8(() => !(!k9.value || w10.value));
  w3(B5, (e4, a8) => {
    e4.preventDefault(), h6.close(), se2(() => a8 == null ? void 0 : a8.focus());
  }, ne3);
  let ie5 = o8(() => !(w10.value || c7.value !== 0));
  E4((W7 = E10.value) == null ? void 0 : W7.defaultView, "keydown", (e4) => {
    ie5.value && (e4.defaultPrevented || e4.key === o5.Escape && (e4.preventDefault(), e4.stopPropagation(), h6.close()));
  });
  let ue5 = o8(() => !(U7.value || c7.value !== 0 || N13));
  return d4(E10, ue5, (e4) => {
    var a8;
    return { containers: [...(a8 = e4.containers) != null ? a8 : [], B5] };
  }), fe2((e4) => {
    if (c7.value !== 0) return;
    let a8 = o3(m12);
    if (!a8) return;
    let d10 = new ResizeObserver((f8) => {
      for (let L9 of f8) {
        let x8 = L9.target.getBoundingClientRect();
        x8.x === 0 && x8.y === 0 && x8.width === 0 && x8.height === 0 && h6.close();
      }
    });
    d10.observe(a8), e4(() => d10.disconnect());
  }), () => {
    let { open: e4, initialFocus: a8, ...d10 } = t8, f8 = { ...l7, ref: m12, id: n9, role: g6.value, "aria-modal": c7.value === 0 ? true : void 0, "aria-labelledby": M5.value, "aria-describedby": re3.value }, L9 = { open: c7.value === 0 };
    return v5(u6, { force: true }, () => [v5($2, () => v5(z3, { target: m12.value }, () => v5(u6, { force: false }, () => v5(ue, { initialFocus: a8, containers: B5, features: k9.value ? u(ee5.value, { parent: ue.features.RestoreFocus, leaf: ue.features.All & ~ue.features.FocusLock }) : ue.features.None }, () => v5(X3, {}, () => A({ ourProps: f8, theirProps: { ...d10, ...l7 }, slot: L9, attrs: l7, slots: p10, visible: c7.value === 0, features: N2.RenderStrategy | N2.Static, name: "Dialog" })))))), v5(Z3)]);
  };
} });
var _e = O4({ name: "DialogOverlay", props: { as: { type: [Object, String], default: "div" }, id: { type: String, default: null } }, setup(t8, { attrs: i13, slots: l7 }) {
  var u10;
  let p10 = (u10 = t8.id) != null ? u10 : `headlessui-dialog-overlay-${i2()}`, s14 = T3("DialogOverlay");
  function n9(r6) {
    r6.target === r6.currentTarget && (r6.preventDefault(), r6.stopPropagation(), s14.close());
  }
  return () => {
    let { ...r6 } = t8;
    return A({ ourProps: { id: p10, "aria-hidden": true, onClick: n9 }, theirProps: r6, slot: { open: s14.dialogState.value === 0 }, attrs: i13, slots: l7, name: "DialogOverlay" });
  };
} });
var ze = O4({ name: "DialogBackdrop", props: { as: { type: [Object, String], default: "div" }, id: { type: String, default: null } }, inheritAttrs: false, setup(t8, { attrs: i13, slots: l7, expose: p10 }) {
  var r6;
  let s14 = (r6 = t8.id) != null ? r6 : `headlessui-dialog-backdrop-${i2()}`, n9 = T3("DialogBackdrop"), u10 = y6(null);
  return p10({ el: u10, $el: u10 }), $3(() => {
    if (n9.panelRef.value === null) throw new Error("A <DialogBackdrop /> component is being used, but a <DialogPanel /> component is missing.");
  }), () => {
    let { ...g6 } = t8, D4 = { id: s14, ref: u10, "aria-hidden": true };
    return v5(u6, { force: true }, () => v5($2, () => A({ ourProps: D4, theirProps: { ...i13, ...g6 }, slot: { open: n9.dialogState.value === 0 }, attrs: i13, slots: l7, name: "DialogBackdrop" })));
  };
} });
var Ge = O4({ name: "DialogPanel", props: { as: { type: [Object, String], default: "div" }, id: { type: String, default: null } }, setup(t8, { attrs: i13, slots: l7, expose: p10 }) {
  var r6;
  let s14 = (r6 = t8.id) != null ? r6 : `headlessui-dialog-panel-${i2()}`, n9 = T3("DialogPanel");
  p10({ el: n9.panelRef, $el: n9.panelRef });
  function u10(g6) {
    g6.stopPropagation();
  }
  return () => {
    let { ...g6 } = t8, D4 = { id: s14, ref: n9.panelRef, onClick: u10 };
    return A({ ourProps: D4, theirProps: g6, slot: { open: n9.dialogState.value === 0 }, attrs: i13, slots: l7, name: "DialogPanel" });
  };
} });
var Ve2 = O4({ name: "DialogTitle", props: { as: { type: [Object, String], default: "h2" }, id: { type: String, default: null } }, setup(t8, { attrs: i13, slots: l7 }) {
  var n9;
  let p10 = (n9 = t8.id) != null ? n9 : `headlessui-dialog-title-${i2()}`, s14 = T3("DialogTitle");
  return $3(() => {
    s14.setTitleId(p10), pe(() => s14.setTitleId(null));
  }), () => {
    let { ...u10 } = t8;
    return A({ ourProps: { id: p10 }, theirProps: u10, slot: { open: s14.dialogState.value === 0 }, attrs: i13, slots: l7, name: "DialogTitle" });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/disclosure/disclosure.js
import { computed as m8, defineComponent as b3, inject as I4, onMounted as P2, onUnmounted as h3, provide as R5, ref as d6, watchEffect as w7 } from "vue";
var $4 = ((o11) => (o11[o11.Open = 0] = "Open", o11[o11.Closed = 1] = "Closed", o11))($4 || {});
var T4 = Symbol("DisclosureContext");
function O5(t8) {
  let r6 = I4(T4, null);
  if (r6 === null) {
    let o11 = new Error(`<${t8} /> is missing a parent <Disclosure /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(o11, O5), o11;
  }
  return r6;
}
var k4 = Symbol("DisclosurePanelContext");
function U2() {
  return I4(k4, null);
}
var N6 = b3({ name: "Disclosure", props: { as: { type: [Object, String], default: "template" }, defaultOpen: { type: [Boolean], default: false } }, setup(t8, { slots: r6, attrs: o11 }) {
  let s14 = d6(t8.defaultOpen ? 0 : 1), e4 = d6(null), i13 = d6(null), n9 = { buttonId: d6(`headlessui-disclosure-button-${i2()}`), panelId: d6(`headlessui-disclosure-panel-${i2()}`), disclosureState: s14, panel: e4, button: i13, toggleDisclosure() {
    s14.value = u(s14.value, { [0]: 1, [1]: 0 });
  }, closeDisclosure() {
    s14.value !== 1 && (s14.value = 1);
  }, close(l7) {
    n9.closeDisclosure();
    let a8 = (() => l7 ? l7 instanceof HTMLElement ? l7 : l7.value instanceof HTMLElement ? o3(l7) : o3(n9.button) : o3(n9.button))();
    a8 == null || a8.focus();
  } };
  return R5(T4, n9), t4(m8(() => u(s14.value, { [0]: i9.Open, [1]: i9.Closed }))), () => {
    let { defaultOpen: l7, ...a8 } = t8, c7 = { open: s14.value === 0, close: n9.close };
    return A({ theirProps: a8, ourProps: {}, slot: c7, slots: r6, attrs: o11, name: "Disclosure" });
  };
} });
var Q = b3({ name: "DisclosureButton", props: { as: { type: [Object, String], default: "button" }, disabled: { type: [Boolean], default: false }, id: { type: String, default: null } }, setup(t8, { attrs: r6, slots: o11, expose: s14 }) {
  let e4 = O5("DisclosureButton"), i13 = U2(), n9 = m8(() => i13 === null ? false : i13.value === e4.panelId.value);
  P2(() => {
    n9.value || t8.id !== null && (e4.buttonId.value = t8.id);
  }), h3(() => {
    n9.value || (e4.buttonId.value = null);
  });
  let l7 = d6(null);
  s14({ el: l7, $el: l7 }), n9.value || w7(() => {
    e4.button.value = l7.value;
  });
  let a8 = s5(m8(() => ({ as: t8.as, type: r6.type })), l7);
  function c7() {
    var u10;
    t8.disabled || (n9.value ? (e4.toggleDisclosure(), (u10 = o3(e4.button)) == null || u10.focus()) : e4.toggleDisclosure());
  }
  function D4(u10) {
    var S4;
    if (!t8.disabled) if (n9.value) switch (u10.key) {
      case o5.Space:
      case o5.Enter:
        u10.preventDefault(), u10.stopPropagation(), e4.toggleDisclosure(), (S4 = o3(e4.button)) == null || S4.focus();
        break;
    }
    else switch (u10.key) {
      case o5.Space:
      case o5.Enter:
        u10.preventDefault(), u10.stopPropagation(), e4.toggleDisclosure();
        break;
    }
  }
  function v9(u10) {
    switch (u10.key) {
      case o5.Space:
        u10.preventDefault();
        break;
    }
  }
  return () => {
    var C8;
    let u10 = { open: e4.disclosureState.value === 0 }, { id: S4, ...K11 } = t8, M5 = n9.value ? { ref: l7, type: a8.value, onClick: c7, onKeydown: D4 } : { id: (C8 = e4.buttonId.value) != null ? C8 : S4, ref: l7, type: a8.value, "aria-expanded": e4.disclosureState.value === 0, "aria-controls": e4.disclosureState.value === 0 || o3(e4.panel) ? e4.panelId.value : void 0, disabled: t8.disabled ? true : void 0, onClick: c7, onKeydown: D4, onKeyup: v9 };
    return A({ ourProps: M5, theirProps: K11, slot: u10, attrs: r6, slots: o11, name: "DisclosureButton" });
  };
} });
var V = b3({ name: "DisclosurePanel", props: { as: { type: [Object, String], default: "div" }, static: { type: Boolean, default: false }, unmount: { type: Boolean, default: true }, id: { type: String, default: null } }, setup(t8, { attrs: r6, slots: o11, expose: s14 }) {
  let e4 = O5("DisclosurePanel");
  P2(() => {
    t8.id !== null && (e4.panelId.value = t8.id);
  }), h3(() => {
    e4.panelId.value = null;
  }), s14({ el: e4.panel, $el: e4.panel }), R5(k4, e4.panelId);
  let i13 = l2(), n9 = m8(() => i13 !== null ? (i13.value & i9.Open) === i9.Open : e4.disclosureState.value === 0);
  return () => {
    var v9;
    let l7 = { open: e4.disclosureState.value === 0, close: e4.close }, { id: a8, ...c7 } = t8, D4 = { id: (v9 = e4.panelId.value) != null ? v9 : a8, ref: e4.panel };
    return A({ ourProps: D4, theirProps: c7, slot: l7, attrs: r6, slots: o11, features: N2.RenderStrategy | N2.Static, visible: n9.value, name: "DisclosurePanel" });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/listbox/listbox.js
import { computed as x4, defineComponent as E8, Fragment as z4, h as N7, inject as _2, nextTick as V2, onMounted as K5, onUnmounted as q2, provide as W2, ref as T5, toRaw as R6, watch as H5, watchEffect as G2 } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-text-value.js
import { ref as n7 } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/get-text-value.js
var a6 = /([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;
function o9(e4) {
  var r6, i13;
  let n9 = (r6 = e4.innerText) != null ? r6 : "", t8 = e4.cloneNode(true);
  if (!(t8 instanceof HTMLElement)) return n9;
  let u10 = false;
  for (let f8 of t8.querySelectorAll('[hidden],[aria-hidden],[role="img"]')) f8.remove(), u10 = true;
  let l7 = u10 ? (i13 = t8.innerText) != null ? i13 : "" : n9;
  return a6.test(l7) && (l7 = l7.replace(a6, "")), l7;
}
function g3(e4) {
  let n9 = e4.getAttribute("aria-label");
  if (typeof n9 == "string") return n9.trim();
  let t8 = e4.getAttribute("aria-labelledby");
  if (t8) {
    let u10 = t8.split(" ").map((l7) => {
      let r6 = document.getElementById(l7);
      if (r6) {
        let i13 = r6.getAttribute("aria-label");
        return typeof i13 == "string" ? i13.trim() : o9(r6).trim();
      }
      return null;
    }).filter(Boolean);
    if (u10.length > 0) return u10.join(", ");
  }
  return o9(e4).trim();
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/hooks/use-text-value.js
function p9(a8) {
  let t8 = n7(""), r6 = n7("");
  return () => {
    let e4 = o3(a8);
    if (!e4) return "";
    let l7 = e4.innerText;
    if (t8.value === l7) return r6.value;
    let u10 = g3(e4).trim().toLowerCase();
    return t8.value = l7, r6.value = u10, u10;
  };
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/listbox/listbox.js
function pe2(o11, b5) {
  return o11 === b5;
}
var ce = ((r6) => (r6[r6.Open = 0] = "Open", r6[r6.Closed = 1] = "Closed", r6))(ce || {});
var ve2 = ((r6) => (r6[r6.Single = 0] = "Single", r6[r6.Multi = 1] = "Multi", r6))(ve2 || {});
var be = ((r6) => (r6[r6.Pointer = 0] = "Pointer", r6[r6.Other = 1] = "Other", r6))(be || {});
function me(o11) {
  requestAnimationFrame(() => requestAnimationFrame(o11));
}
var $5 = Symbol("ListboxContext");
function A4(o11) {
  let b5 = _2($5, null);
  if (b5 === null) {
    let r6 = new Error(`<${o11} /> is missing a parent <Listbox /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(r6, A4), r6;
  }
  return b5;
}
var Ie = E8({ name: "Listbox", emits: { "update:modelValue": (o11) => true }, props: { as: { type: [Object, String], default: "template" }, disabled: { type: [Boolean], default: false }, by: { type: [String, Function], default: () => pe2 }, horizontal: { type: [Boolean], default: false }, modelValue: { type: [Object, String, Number, Boolean], default: void 0 }, defaultValue: { type: [Object, String, Number, Boolean], default: void 0 }, form: { type: String, optional: true }, name: { type: String, optional: true }, multiple: { type: [Boolean], default: false } }, inheritAttrs: false, setup(o11, { slots: b5, attrs: r6, emit: w10 }) {
  let n9 = T5(1), e4 = T5(null), f8 = T5(null), v9 = T5(null), s14 = T5([]), m12 = T5(""), p10 = T5(null), a8 = T5(1);
  function u10(t8 = (i13) => i13) {
    let i13 = p10.value !== null ? s14.value[p10.value] : null, l7 = O(t8(s14.value.slice()), (O9) => o3(O9.dataRef.domRef)), d10 = i13 ? l7.indexOf(i13) : null;
    return d10 === -1 && (d10 = null), { options: l7, activeOptionIndex: d10 };
  }
  let D4 = x4(() => o11.multiple ? 1 : 0), [y10, L9] = d(x4(() => o11.modelValue), (t8) => w10("update:modelValue", t8), x4(() => o11.defaultValue)), M5 = x4(() => y10.value === void 0 ? u(D4.value, { [1]: [], [0]: void 0 }) : y10.value), k9 = { listboxState: n9, value: M5, mode: D4, compare(t8, i13) {
    if (typeof o11.by == "string") {
      let l7 = o11.by;
      return (t8 == null ? void 0 : t8[l7]) === (i13 == null ? void 0 : i13[l7]);
    }
    return o11.by(t8, i13);
  }, orientation: x4(() => o11.horizontal ? "horizontal" : "vertical"), labelRef: e4, buttonRef: f8, optionsRef: v9, disabled: x4(() => o11.disabled), options: s14, searchQuery: m12, activeOptionIndex: p10, activationTrigger: a8, closeListbox() {
    o11.disabled || n9.value !== 1 && (n9.value = 1, p10.value = null);
  }, openListbox() {
    o11.disabled || n9.value !== 0 && (n9.value = 0);
  }, goToOption(t8, i13, l7) {
    if (o11.disabled || n9.value === 1) return;
    let d10 = u10(), O9 = f3(t8 === c3.Specific ? { focus: c3.Specific, id: i13 } : { focus: t8 }, { resolveItems: () => d10.options, resolveActiveIndex: () => d10.activeOptionIndex, resolveId: (h6) => h6.id, resolveDisabled: (h6) => h6.dataRef.disabled });
    m12.value = "", p10.value = O9, a8.value = l7 != null ? l7 : 1, s14.value = d10.options;
  }, search(t8) {
    if (o11.disabled || n9.value === 1) return;
    let l7 = m12.value !== "" ? 0 : 1;
    m12.value += t8.toLowerCase();
    let O9 = (p10.value !== null ? s14.value.slice(p10.value + l7).concat(s14.value.slice(0, p10.value + l7)) : s14.value).find((I7) => I7.dataRef.textValue.startsWith(m12.value) && !I7.dataRef.disabled), h6 = O9 ? s14.value.indexOf(O9) : -1;
    h6 === -1 || h6 === p10.value || (p10.value = h6, a8.value = 1);
  }, clearSearch() {
    o11.disabled || n9.value !== 1 && m12.value !== "" && (m12.value = "");
  }, registerOption(t8, i13) {
    let l7 = u10((d10) => [...d10, { id: t8, dataRef: i13 }]);
    s14.value = l7.options, p10.value = l7.activeOptionIndex;
  }, unregisterOption(t8) {
    let i13 = u10((l7) => {
      let d10 = l7.findIndex((O9) => O9.id === t8);
      return d10 !== -1 && l7.splice(d10, 1), l7;
    });
    s14.value = i13.options, p10.value = i13.activeOptionIndex, a8.value = 1;
  }, theirOnChange(t8) {
    o11.disabled || L9(t8);
  }, select(t8) {
    o11.disabled || L9(u(D4.value, { [0]: () => t8, [1]: () => {
      let i13 = R6(k9.value.value).slice(), l7 = R6(t8), d10 = i13.findIndex((O9) => k9.compare(l7, R6(O9)));
      return d10 === -1 ? i13.push(l7) : i13.splice(d10, 1), i13;
    } }));
  } };
  w3([f8, v9], (t8, i13) => {
    var l7;
    k9.closeListbox(), w(i13, h.Loose) || (t8.preventDefault(), (l7 = o3(f8)) == null || l7.focus());
  }, x4(() => n9.value === 0)), W2($5, k9), t4(x4(() => u(n9.value, { [0]: i9.Open, [1]: i9.Closed })));
  let C8 = x4(() => {
    var t8;
    return (t8 = o3(f8)) == null ? void 0 : t8.closest("form");
  });
  return K5(() => {
    H5([C8], () => {
      if (!C8.value || o11.defaultValue === void 0) return;
      function t8() {
        k9.theirOnChange(o11.defaultValue);
      }
      return C8.value.addEventListener("reset", t8), () => {
        var i13;
        (i13 = C8.value) == null || i13.removeEventListener("reset", t8);
      };
    }, { immediate: true });
  }), () => {
    let { name: t8, modelValue: i13, disabled: l7, form: d10, ...O9 } = o11, h6 = { open: n9.value === 0, disabled: l7, value: M5.value };
    return N7(z4, [...t8 != null && M5.value != null ? e2({ [t8]: M5.value }).map(([I7, Q4]) => N7(f2, E3({ features: u4.Hidden, key: I7, as: "input", type: "hidden", hidden: true, readOnly: true, form: d10, disabled: l7, name: I7, value: Q4 }))) : [], A({ ourProps: {}, theirProps: { ...r6, ...T2(O9, ["defaultValue", "onUpdate:modelValue", "horizontal", "multiple", "by"]) }, slot: h6, slots: b5, attrs: r6, name: "Listbox" })]);
  };
} });
var Ee2 = E8({ name: "ListboxLabel", props: { as: { type: [Object, String], default: "label" }, id: { type: String, default: null } }, setup(o11, { attrs: b5, slots: r6 }) {
  var f8;
  let w10 = (f8 = o11.id) != null ? f8 : `headlessui-listbox-label-${i2()}`, n9 = A4("ListboxLabel");
  function e4() {
    var v9;
    (v9 = o3(n9.buttonRef)) == null || v9.focus({ preventScroll: true });
  }
  return () => {
    let v9 = { open: n9.listboxState.value === 0, disabled: n9.disabled.value }, { ...s14 } = o11, m12 = { id: w10, ref: n9.labelRef, onClick: e4 };
    return A({ ourProps: m12, theirProps: s14, slot: v9, attrs: b5, slots: r6, name: "ListboxLabel" });
  };
} });
var je = E8({ name: "ListboxButton", props: { as: { type: [Object, String], default: "button" }, id: { type: String, default: null } }, setup(o11, { attrs: b5, slots: r6, expose: w10 }) {
  var p10;
  let n9 = (p10 = o11.id) != null ? p10 : `headlessui-listbox-button-${i2()}`, e4 = A4("ListboxButton");
  w10({ el: e4.buttonRef, $el: e4.buttonRef });
  function f8(a8) {
    switch (a8.key) {
      case o5.Space:
      case o5.Enter:
      case o5.ArrowDown:
        a8.preventDefault(), e4.openListbox(), V2(() => {
          var u10;
          (u10 = o3(e4.optionsRef)) == null || u10.focus({ preventScroll: true }), e4.value.value || e4.goToOption(c3.First);
        });
        break;
      case o5.ArrowUp:
        a8.preventDefault(), e4.openListbox(), V2(() => {
          var u10;
          (u10 = o3(e4.optionsRef)) == null || u10.focus({ preventScroll: true }), e4.value.value || e4.goToOption(c3.Last);
        });
        break;
    }
  }
  function v9(a8) {
    switch (a8.key) {
      case o5.Space:
        a8.preventDefault();
        break;
    }
  }
  function s14(a8) {
    e4.disabled.value || (e4.listboxState.value === 0 ? (e4.closeListbox(), V2(() => {
      var u10;
      return (u10 = o3(e4.buttonRef)) == null ? void 0 : u10.focus({ preventScroll: true });
    })) : (a8.preventDefault(), e4.openListbox(), me(() => {
      var u10;
      return (u10 = o3(e4.optionsRef)) == null ? void 0 : u10.focus({ preventScroll: true });
    })));
  }
  let m12 = s5(x4(() => ({ as: o11.as, type: b5.type })), e4.buttonRef);
  return () => {
    var y10, L9;
    let a8 = { open: e4.listboxState.value === 0, disabled: e4.disabled.value, value: e4.value.value }, { ...u10 } = o11, D4 = { ref: e4.buttonRef, id: n9, type: m12.value, "aria-haspopup": "listbox", "aria-controls": (y10 = o3(e4.optionsRef)) == null ? void 0 : y10.id, "aria-expanded": e4.listboxState.value === 0, "aria-labelledby": e4.labelRef.value ? [(L9 = o3(e4.labelRef)) == null ? void 0 : L9.id, n9].join(" ") : void 0, disabled: e4.disabled.value === true ? true : void 0, onKeydown: f8, onKeyup: v9, onClick: s14 };
    return A({ ourProps: D4, theirProps: u10, slot: a8, attrs: b5, slots: r6, name: "ListboxButton" });
  };
} });
var Ae2 = E8({ name: "ListboxOptions", props: { as: { type: [Object, String], default: "ul" }, static: { type: Boolean, default: false }, unmount: { type: Boolean, default: true }, id: { type: String, default: null } }, setup(o11, { attrs: b5, slots: r6, expose: w10 }) {
  var p10;
  let n9 = (p10 = o11.id) != null ? p10 : `headlessui-listbox-options-${i2()}`, e4 = A4("ListboxOptions"), f8 = T5(null);
  w10({ el: e4.optionsRef, $el: e4.optionsRef });
  function v9(a8) {
    switch (f8.value && clearTimeout(f8.value), a8.key) {
      case o5.Space:
        if (e4.searchQuery.value !== "") return a8.preventDefault(), a8.stopPropagation(), e4.search(a8.key);
      case o5.Enter:
        if (a8.preventDefault(), a8.stopPropagation(), e4.activeOptionIndex.value !== null) {
          let u10 = e4.options.value[e4.activeOptionIndex.value];
          e4.select(u10.dataRef.value);
        }
        e4.mode.value === 0 && (e4.closeListbox(), V2(() => {
          var u10;
          return (u10 = o3(e4.buttonRef)) == null ? void 0 : u10.focus({ preventScroll: true });
        }));
        break;
      case u(e4.orientation.value, { vertical: o5.ArrowDown, horizontal: o5.ArrowRight }):
        return a8.preventDefault(), a8.stopPropagation(), e4.goToOption(c3.Next);
      case u(e4.orientation.value, { vertical: o5.ArrowUp, horizontal: o5.ArrowLeft }):
        return a8.preventDefault(), a8.stopPropagation(), e4.goToOption(c3.Previous);
      case o5.Home:
      case o5.PageUp:
        return a8.preventDefault(), a8.stopPropagation(), e4.goToOption(c3.First);
      case o5.End:
      case o5.PageDown:
        return a8.preventDefault(), a8.stopPropagation(), e4.goToOption(c3.Last);
      case o5.Escape:
        a8.preventDefault(), a8.stopPropagation(), e4.closeListbox(), V2(() => {
          var u10;
          return (u10 = o3(e4.buttonRef)) == null ? void 0 : u10.focus({ preventScroll: true });
        });
        break;
      case o5.Tab:
        a8.preventDefault(), a8.stopPropagation();
        break;
      default:
        a8.key.length === 1 && (e4.search(a8.key), f8.value = setTimeout(() => e4.clearSearch(), 350));
        break;
    }
  }
  let s14 = l2(), m12 = x4(() => s14 !== null ? (s14.value & i9.Open) === i9.Open : e4.listboxState.value === 0);
  return () => {
    var y10, L9;
    let a8 = { open: e4.listboxState.value === 0 }, { ...u10 } = o11, D4 = { "aria-activedescendant": e4.activeOptionIndex.value === null || (y10 = e4.options.value[e4.activeOptionIndex.value]) == null ? void 0 : y10.id, "aria-multiselectable": e4.mode.value === 1 ? true : void 0, "aria-labelledby": (L9 = o3(e4.buttonRef)) == null ? void 0 : L9.id, "aria-orientation": e4.orientation.value, id: n9, onKeydown: v9, role: "listbox", tabIndex: 0, ref: e4.optionsRef };
    return A({ ourProps: D4, theirProps: u10, slot: a8, attrs: b5, slots: r6, features: N2.RenderStrategy | N2.Static, visible: m12.value, name: "ListboxOptions" });
  };
} });
var Fe = E8({ name: "ListboxOption", props: { as: { type: [Object, String], default: "li" }, value: { type: [Object, String, Number, Boolean] }, disabled: { type: Boolean, default: false }, id: { type: String, default: null } }, setup(o11, { slots: b5, attrs: r6, expose: w10 }) {
  var C8;
  let n9 = (C8 = o11.id) != null ? C8 : `headlessui-listbox-option-${i2()}`, e4 = A4("ListboxOption"), f8 = T5(null);
  w10({ el: f8, $el: f8 });
  let v9 = x4(() => e4.activeOptionIndex.value !== null ? e4.options.value[e4.activeOptionIndex.value].id === n9 : false), s14 = x4(() => u(e4.mode.value, { [0]: () => e4.compare(R6(e4.value.value), R6(o11.value)), [1]: () => R6(e4.value.value).some((t8) => e4.compare(R6(t8), R6(o11.value))) })), m12 = x4(() => u(e4.mode.value, { [1]: () => {
    var i13;
    let t8 = R6(e4.value.value);
    return ((i13 = e4.options.value.find((l7) => t8.some((d10) => e4.compare(R6(d10), R6(l7.dataRef.value))))) == null ? void 0 : i13.id) === n9;
  }, [0]: () => s14.value })), p10 = p9(f8), a8 = x4(() => ({ disabled: o11.disabled, value: o11.value, get textValue() {
    return p10();
  }, domRef: f8 }));
  K5(() => e4.registerOption(n9, a8)), q2(() => e4.unregisterOption(n9)), K5(() => {
    H5([e4.listboxState, s14], () => {
      e4.listboxState.value === 0 && s14.value && u(e4.mode.value, { [1]: () => {
        m12.value && e4.goToOption(c3.Specific, n9);
      }, [0]: () => {
        e4.goToOption(c3.Specific, n9);
      } });
    }, { immediate: true });
  }), G2(() => {
    e4.listboxState.value === 0 && v9.value && e4.activationTrigger.value !== 0 && V2(() => {
      var t8, i13;
      return (i13 = (t8 = o3(f8)) == null ? void 0 : t8.scrollIntoView) == null ? void 0 : i13.call(t8, { block: "nearest" });
    });
  });
  function u10(t8) {
    if (o11.disabled) return t8.preventDefault();
    e4.select(o11.value), e4.mode.value === 0 && (e4.closeListbox(), V2(() => {
      var i13;
      return (i13 = o3(e4.buttonRef)) == null ? void 0 : i13.focus({ preventScroll: true });
    }));
  }
  function D4() {
    if (o11.disabled) return e4.goToOption(c3.Nothing);
    e4.goToOption(c3.Specific, n9);
  }
  let y10 = u3();
  function L9(t8) {
    y10.update(t8);
  }
  function M5(t8) {
    y10.wasMoved(t8) && (o11.disabled || v9.value || e4.goToOption(c3.Specific, n9, 0));
  }
  function k9(t8) {
    y10.wasMoved(t8) && (o11.disabled || v9.value && e4.goToOption(c3.Nothing));
  }
  return () => {
    let { disabled: t8 } = o11, i13 = { active: v9.value, selected: s14.value, disabled: t8 }, { value: l7, disabled: d10, ...O9 } = o11, h6 = { id: n9, ref: f8, role: "option", tabIndex: t8 === true ? void 0 : -1, "aria-disabled": t8 === true ? true : void 0, "aria-selected": s14.value, disabled: void 0, onClick: u10, onFocus: D4, onPointerenter: L9, onMouseenter: L9, onPointermove: M5, onMousemove: M5, onPointerleave: k9, onMouseleave: k9 };
    return A({ ourProps: h6, theirProps: O9, slot: i13, attrs: r6, slots: b5, name: "ListboxOption" });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/menu/menu.js
import { computed as y7, defineComponent as T6, inject as K6, nextTick as x5, onMounted as N8, onUnmounted as j5, provide as L3, ref as R7, watchEffect as B2 } from "vue";
var Z = ((i13) => (i13[i13.Open = 0] = "Open", i13[i13.Closed = 1] = "Closed", i13))(Z || {});
var ee2 = ((i13) => (i13[i13.Pointer = 0] = "Pointer", i13[i13.Other = 1] = "Other", i13))(ee2 || {});
function te2(o11) {
  requestAnimationFrame(() => requestAnimationFrame(o11));
}
var A5 = Symbol("MenuContext");
function O6(o11) {
  let M5 = K6(A5, null);
  if (M5 === null) {
    let i13 = new Error(`<${o11} /> is missing a parent <Menu /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(i13, O6), i13;
  }
  return M5;
}
var ge = T6({ name: "Menu", props: { as: { type: [Object, String], default: "template" } }, setup(o11, { slots: M5, attrs: i13 }) {
  let I7 = R7(1), p10 = R7(null), e4 = R7(null), r6 = R7([]), f8 = R7(""), d10 = R7(null), g6 = R7(1);
  function b5(t8 = (a8) => a8) {
    let a8 = d10.value !== null ? r6.value[d10.value] : null, n9 = O(t8(r6.value.slice()), (v9) => o3(v9.dataRef.domRef)), s14 = a8 ? n9.indexOf(a8) : null;
    return s14 === -1 && (s14 = null), { items: n9, activeItemIndex: s14 };
  }
  let l7 = { menuState: I7, buttonRef: p10, itemsRef: e4, items: r6, searchQuery: f8, activeItemIndex: d10, activationTrigger: g6, closeMenu: () => {
    I7.value = 1, d10.value = null;
  }, openMenu: () => I7.value = 0, goToItem(t8, a8, n9) {
    let s14 = b5(), v9 = f3(t8 === c3.Specific ? { focus: c3.Specific, id: a8 } : { focus: t8 }, { resolveItems: () => s14.items, resolveActiveIndex: () => s14.activeItemIndex, resolveId: (u10) => u10.id, resolveDisabled: (u10) => u10.dataRef.disabled });
    f8.value = "", d10.value = v9, g6.value = n9 != null ? n9 : 1, r6.value = s14.items;
  }, search(t8) {
    let n9 = f8.value !== "" ? 0 : 1;
    f8.value += t8.toLowerCase();
    let v9 = (d10.value !== null ? r6.value.slice(d10.value + n9).concat(r6.value.slice(0, d10.value + n9)) : r6.value).find((h6) => h6.dataRef.textValue.startsWith(f8.value) && !h6.dataRef.disabled), u10 = v9 ? r6.value.indexOf(v9) : -1;
    u10 === -1 || u10 === d10.value || (d10.value = u10, g6.value = 1);
  }, clearSearch() {
    f8.value = "";
  }, registerItem(t8, a8) {
    let n9 = b5((s14) => [...s14, { id: t8, dataRef: a8 }]);
    r6.value = n9.items, d10.value = n9.activeItemIndex, g6.value = 1;
  }, unregisterItem(t8) {
    let a8 = b5((n9) => {
      let s14 = n9.findIndex((v9) => v9.id === t8);
      return s14 !== -1 && n9.splice(s14, 1), n9;
    });
    r6.value = a8.items, d10.value = a8.activeItemIndex, g6.value = 1;
  } };
  return w3([p10, e4], (t8, a8) => {
    var n9;
    l7.closeMenu(), w(a8, h.Loose) || (t8.preventDefault(), (n9 = o3(p10)) == null || n9.focus());
  }, y7(() => I7.value === 0)), L3(A5, l7), t4(y7(() => u(I7.value, { [0]: i9.Open, [1]: i9.Closed }))), () => {
    let t8 = { open: I7.value === 0, close: l7.closeMenu };
    return A({ ourProps: {}, theirProps: o11, slot: t8, slots: M5, attrs: i13, name: "Menu" });
  };
} });
var Se = T6({ name: "MenuButton", props: { disabled: { type: Boolean, default: false }, as: { type: [Object, String], default: "button" }, id: { type: String, default: null } }, setup(o11, { attrs: M5, slots: i13, expose: I7 }) {
  var b5;
  let p10 = (b5 = o11.id) != null ? b5 : `headlessui-menu-button-${i2()}`, e4 = O6("MenuButton");
  I7({ el: e4.buttonRef, $el: e4.buttonRef });
  function r6(l7) {
    switch (l7.key) {
      case o5.Space:
      case o5.Enter:
      case o5.ArrowDown:
        l7.preventDefault(), l7.stopPropagation(), e4.openMenu(), x5(() => {
          var t8;
          (t8 = o3(e4.itemsRef)) == null || t8.focus({ preventScroll: true }), e4.goToItem(c3.First);
        });
        break;
      case o5.ArrowUp:
        l7.preventDefault(), l7.stopPropagation(), e4.openMenu(), x5(() => {
          var t8;
          (t8 = o3(e4.itemsRef)) == null || t8.focus({ preventScroll: true }), e4.goToItem(c3.Last);
        });
        break;
    }
  }
  function f8(l7) {
    switch (l7.key) {
      case o5.Space:
        l7.preventDefault();
        break;
    }
  }
  function d10(l7) {
    o11.disabled || (e4.menuState.value === 0 ? (e4.closeMenu(), x5(() => {
      var t8;
      return (t8 = o3(e4.buttonRef)) == null ? void 0 : t8.focus({ preventScroll: true });
    })) : (l7.preventDefault(), e4.openMenu(), te2(() => {
      var t8;
      return (t8 = o3(e4.itemsRef)) == null ? void 0 : t8.focus({ preventScroll: true });
    })));
  }
  let g6 = s5(y7(() => ({ as: o11.as, type: M5.type })), e4.buttonRef);
  return () => {
    var n9;
    let l7 = { open: e4.menuState.value === 0 }, { ...t8 } = o11, a8 = { ref: e4.buttonRef, id: p10, type: g6.value, "aria-haspopup": "menu", "aria-controls": (n9 = o3(e4.itemsRef)) == null ? void 0 : n9.id, "aria-expanded": e4.menuState.value === 0, onKeydown: r6, onKeyup: f8, onClick: d10 };
    return A({ ourProps: a8, theirProps: t8, slot: l7, attrs: M5, slots: i13, name: "MenuButton" });
  };
} });
var Me = T6({ name: "MenuItems", props: { as: { type: [Object, String], default: "div" }, static: { type: Boolean, default: false }, unmount: { type: Boolean, default: true }, id: { type: String, default: null } }, setup(o11, { attrs: M5, slots: i13, expose: I7 }) {
  var l7;
  let p10 = (l7 = o11.id) != null ? l7 : `headlessui-menu-items-${i2()}`, e4 = O6("MenuItems"), r6 = R7(null);
  I7({ el: e4.itemsRef, $el: e4.itemsRef }), i8({ container: y7(() => o3(e4.itemsRef)), enabled: y7(() => e4.menuState.value === 0), accept(t8) {
    return t8.getAttribute("role") === "menuitem" ? NodeFilter.FILTER_REJECT : t8.hasAttribute("role") ? NodeFilter.FILTER_SKIP : NodeFilter.FILTER_ACCEPT;
  }, walk(t8) {
    t8.setAttribute("role", "none");
  } });
  function f8(t8) {
    var a8;
    switch (r6.value && clearTimeout(r6.value), t8.key) {
      case o5.Space:
        if (e4.searchQuery.value !== "") return t8.preventDefault(), t8.stopPropagation(), e4.search(t8.key);
      case o5.Enter:
        if (t8.preventDefault(), t8.stopPropagation(), e4.activeItemIndex.value !== null) {
          let s14 = e4.items.value[e4.activeItemIndex.value];
          (a8 = o3(s14.dataRef.domRef)) == null || a8.click();
        }
        e4.closeMenu(), _(o3(e4.buttonRef));
        break;
      case o5.ArrowDown:
        return t8.preventDefault(), t8.stopPropagation(), e4.goToItem(c3.Next);
      case o5.ArrowUp:
        return t8.preventDefault(), t8.stopPropagation(), e4.goToItem(c3.Previous);
      case o5.Home:
      case o5.PageUp:
        return t8.preventDefault(), t8.stopPropagation(), e4.goToItem(c3.First);
      case o5.End:
      case o5.PageDown:
        return t8.preventDefault(), t8.stopPropagation(), e4.goToItem(c3.Last);
      case o5.Escape:
        t8.preventDefault(), t8.stopPropagation(), e4.closeMenu(), x5(() => {
          var n9;
          return (n9 = o3(e4.buttonRef)) == null ? void 0 : n9.focus({ preventScroll: true });
        });
        break;
      case o5.Tab:
        t8.preventDefault(), t8.stopPropagation(), e4.closeMenu(), x5(() => v(o3(e4.buttonRef), t8.shiftKey ? N.Previous : N.Next));
        break;
      default:
        t8.key.length === 1 && (e4.search(t8.key), r6.value = setTimeout(() => e4.clearSearch(), 350));
        break;
    }
  }
  function d10(t8) {
    switch (t8.key) {
      case o5.Space:
        t8.preventDefault();
        break;
    }
  }
  let g6 = l2(), b5 = y7(() => g6 !== null ? (g6.value & i9.Open) === i9.Open : e4.menuState.value === 0);
  return () => {
    var s14, v9;
    let t8 = { open: e4.menuState.value === 0 }, { ...a8 } = o11, n9 = { "aria-activedescendant": e4.activeItemIndex.value === null || (s14 = e4.items.value[e4.activeItemIndex.value]) == null ? void 0 : s14.id, "aria-labelledby": (v9 = o3(e4.buttonRef)) == null ? void 0 : v9.id, id: p10, onKeydown: f8, onKeyup: d10, role: "menu", tabIndex: 0, ref: e4.itemsRef };
    return A({ ourProps: n9, theirProps: a8, slot: t8, attrs: M5, slots: i13, features: N2.RenderStrategy | N2.Static, visible: b5.value, name: "MenuItems" });
  };
} });
var be2 = T6({ name: "MenuItem", inheritAttrs: false, props: { as: { type: [Object, String], default: "template" }, disabled: { type: Boolean, default: false }, id: { type: String, default: null } }, setup(o11, { slots: M5, attrs: i13, expose: I7 }) {
  var v9;
  let p10 = (v9 = o11.id) != null ? v9 : `headlessui-menu-item-${i2()}`, e4 = O6("MenuItem"), r6 = R7(null);
  I7({ el: r6, $el: r6 });
  let f8 = y7(() => e4.activeItemIndex.value !== null ? e4.items.value[e4.activeItemIndex.value].id === p10 : false), d10 = p9(r6), g6 = y7(() => ({ disabled: o11.disabled, get textValue() {
    return d10();
  }, domRef: r6 }));
  N8(() => e4.registerItem(p10, g6)), j5(() => e4.unregisterItem(p10)), B2(() => {
    e4.menuState.value === 0 && f8.value && e4.activationTrigger.value !== 0 && x5(() => {
      var u10, h6;
      return (h6 = (u10 = o3(r6)) == null ? void 0 : u10.scrollIntoView) == null ? void 0 : h6.call(u10, { block: "nearest" });
    });
  });
  function b5(u10) {
    if (o11.disabled) return u10.preventDefault();
    e4.closeMenu(), _(o3(e4.buttonRef));
  }
  function l7() {
    if (o11.disabled) return e4.goToItem(c3.Nothing);
    e4.goToItem(c3.Specific, p10);
  }
  let t8 = u3();
  function a8(u10) {
    t8.update(u10);
  }
  function n9(u10) {
    t8.wasMoved(u10) && (o11.disabled || f8.value || e4.goToItem(c3.Specific, p10, 0));
  }
  function s14(u10) {
    t8.wasMoved(u10) && (o11.disabled || f8.value && e4.goToItem(c3.Nothing));
  }
  return () => {
    let { disabled: u10, ...h6 } = o11, C8 = { active: f8.value, disabled: u10, close: e4.closeMenu };
    return A({ ourProps: { id: p10, ref: r6, role: "menuitem", tabIndex: u10 === true ? void 0 : -1, "aria-disabled": u10 === true ? true : void 0, onClick: b5, onFocus: l7, onPointerenter: a8, onMouseenter: a8, onPointermove: n9, onMousemove: n9, onPointerleave: s14, onMouseleave: s14 }, theirProps: { ...i13, ...h6 }, slot: C8, attrs: i13, slots: M5, name: "MenuItem" });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/popover/popover.js
import { computed as O7, defineComponent as j6, Fragment as W3, h as T7, inject as q3, onMounted as ee3, onUnmounted as te3, provide as z5, ref as R8, shallowRef as ie2, watchEffect as J3 } from "vue";
var Se2 = ((s14) => (s14[s14.Open = 0] = "Open", s14[s14.Closed = 1] = "Closed", s14))(Se2 || {});
var re = Symbol("PopoverContext");
function U3(d10) {
  let P6 = q3(re, null);
  if (P6 === null) {
    let s14 = new Error(`<${d10} /> is missing a parent <${ye.name} /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(s14, U3), s14;
  }
  return P6;
}
var le = Symbol("PopoverGroupContext");
function ae() {
  return q3(le, null);
}
var ue2 = Symbol("PopoverPanelContext");
function ge2() {
  return q3(ue2, null);
}
var ye = j6({ name: "Popover", inheritAttrs: false, props: { as: { type: [Object, String], default: "div" } }, setup(d10, { slots: P6, attrs: s14, expose: h6 }) {
  var u10;
  let f8 = R8(null);
  h6({ el: f8, $el: f8 });
  let t8 = R8(1), o11 = R8(null), y10 = R8(null), v9 = R8(null), m12 = R8(null), b5 = O7(() => i4(f8)), E10 = O7(() => {
    var L9, $8;
    if (!o3(o11) || !o3(m12)) return false;
    for (let x8 of document.querySelectorAll("body > *")) if (Number(x8 == null ? void 0 : x8.contains(o3(o11))) ^ Number(x8 == null ? void 0 : x8.contains(o3(m12)))) return true;
    let e4 = E(), r6 = e4.indexOf(o3(o11)), l7 = (r6 + e4.length - 1) % e4.length, g6 = (r6 + 1) % e4.length, G5 = e4[l7], C8 = e4[g6];
    return !((L9 = o3(m12)) != null && L9.contains(G5)) && !(($8 = o3(m12)) != null && $8.contains(C8));
  }), a8 = { popoverState: t8, buttonId: R8(null), panelId: R8(null), panel: m12, button: o11, isPortalled: E10, beforePanelSentinel: y10, afterPanelSentinel: v9, togglePopover() {
    t8.value = u(t8.value, { [0]: 1, [1]: 0 });
  }, closePopover() {
    t8.value !== 1 && (t8.value = 1);
  }, close(e4) {
    a8.closePopover();
    let r6 = (() => e4 ? e4 instanceof HTMLElement ? e4 : e4.value instanceof HTMLElement ? o3(e4) : o3(a8.button) : o3(a8.button))();
    r6 == null || r6.focus();
  } };
  z5(re, a8), t4(O7(() => u(t8.value, { [0]: i9.Open, [1]: i9.Closed })));
  let S4 = { buttonId: a8.buttonId, panelId: a8.panelId, close() {
    a8.closePopover();
  } }, c7 = ae(), I7 = c7 == null ? void 0 : c7.registerPopover, [F6, w10] = q(), i13 = N5({ mainTreeNodeRef: c7 == null ? void 0 : c7.mainTreeNodeRef, portals: F6, defaultContainers: [o11, m12] });
  function p10() {
    var e4, r6, l7, g6;
    return (g6 = c7 == null ? void 0 : c7.isFocusWithinPopoverGroup()) != null ? g6 : ((e4 = b5.value) == null ? void 0 : e4.activeElement) && (((r6 = o3(o11)) == null ? void 0 : r6.contains(b5.value.activeElement)) || ((l7 = o3(m12)) == null ? void 0 : l7.contains(b5.value.activeElement)));
  }
  return J3(() => I7 == null ? void 0 : I7(S4)), E4((u10 = b5.value) == null ? void 0 : u10.defaultView, "focus", (e4) => {
    var r6, l7;
    e4.target !== window && e4.target instanceof HTMLElement && t8.value === 0 && (p10() || o11 && m12 && (i13.contains(e4.target) || (r6 = o3(a8.beforePanelSentinel)) != null && r6.contains(e4.target) || (l7 = o3(a8.afterPanelSentinel)) != null && l7.contains(e4.target) || a8.closePopover()));
  }, true), w3(i13.resolveContainers, (e4, r6) => {
    var l7;
    a8.closePopover(), w(r6, h.Loose) || (e4.preventDefault(), (l7 = o3(o11)) == null || l7.focus());
  }, O7(() => t8.value === 0)), () => {
    let e4 = { open: t8.value === 0, close: a8.close };
    return T7(W3, [T7(w10, {}, () => A({ theirProps: { ...d10, ...s14 }, ourProps: { ref: f8 }, slot: e4, slots: P6, attrs: s14, name: "Popover" })), T7(i13.MainTreeNode)]);
  };
} });
var Ge2 = j6({ name: "PopoverButton", props: { as: { type: [Object, String], default: "button" }, disabled: { type: [Boolean], default: false }, id: { type: String, default: null } }, inheritAttrs: false, setup(d10, { attrs: P6, slots: s14, expose: h6 }) {
  var u10;
  let f8 = (u10 = d10.id) != null ? u10 : `headlessui-popover-button-${i2()}`, t8 = U3("PopoverButton"), o11 = O7(() => i4(t8.button));
  h6({ el: t8.button, $el: t8.button }), ee3(() => {
    t8.buttonId.value = f8;
  }), te3(() => {
    t8.buttonId.value = null;
  });
  let y10 = ae(), v9 = y10 == null ? void 0 : y10.closeOthers, m12 = ge2(), b5 = O7(() => m12 === null ? false : m12.value === t8.panelId.value), E10 = R8(null), a8 = `headlessui-focus-sentinel-${i2()}`;
  b5.value || J3(() => {
    t8.button.value = o3(E10);
  });
  let S4 = s5(O7(() => ({ as: d10.as, type: P6.type })), E10);
  function c7(e4) {
    var r6, l7, g6, G5, C8;
    if (b5.value) {
      if (t8.popoverState.value === 1) return;
      switch (e4.key) {
        case o5.Space:
        case o5.Enter:
          e4.preventDefault(), (l7 = (r6 = e4.target).click) == null || l7.call(r6), t8.closePopover(), (g6 = o3(t8.button)) == null || g6.focus();
          break;
      }
    } else switch (e4.key) {
      case o5.Space:
      case o5.Enter:
        e4.preventDefault(), e4.stopPropagation(), t8.popoverState.value === 1 && (v9 == null || v9(t8.buttonId.value)), t8.togglePopover();
        break;
      case o5.Escape:
        if (t8.popoverState.value !== 0) return v9 == null ? void 0 : v9(t8.buttonId.value);
        if (!o3(t8.button) || (G5 = o11.value) != null && G5.activeElement && !((C8 = o3(t8.button)) != null && C8.contains(o11.value.activeElement))) return;
        e4.preventDefault(), e4.stopPropagation(), t8.closePopover();
        break;
    }
  }
  function I7(e4) {
    b5.value || e4.key === o5.Space && e4.preventDefault();
  }
  function F6(e4) {
    var r6, l7;
    d10.disabled || (b5.value ? (t8.closePopover(), (r6 = o3(t8.button)) == null || r6.focus()) : (e4.preventDefault(), e4.stopPropagation(), t8.popoverState.value === 1 && (v9 == null || v9(t8.buttonId.value)), t8.togglePopover(), (l7 = o3(t8.button)) == null || l7.focus()));
  }
  function w10(e4) {
    e4.preventDefault(), e4.stopPropagation();
  }
  let i13 = n5();
  function p10() {
    let e4 = o3(t8.panel);
    if (!e4) return;
    function r6() {
      u(i13.value, { [d3.Forwards]: () => P(e4, N.First), [d3.Backwards]: () => P(e4, N.Last) }) === T.Error && P(E().filter((g6) => g6.dataset.headlessuiFocusGuard !== "true"), u(i13.value, { [d3.Forwards]: N.Next, [d3.Backwards]: N.Previous }), { relativeTo: o3(t8.button) });
    }
    r6();
  }
  return () => {
    let e4 = t8.popoverState.value === 0, r6 = { open: e4 }, { ...l7 } = d10, g6 = b5.value ? { ref: E10, type: S4.value, onKeydown: c7, onClick: F6 } : { ref: E10, id: f8, type: S4.value, "aria-expanded": t8.popoverState.value === 0, "aria-controls": o3(t8.panel) ? t8.panelId.value : void 0, disabled: d10.disabled ? true : void 0, onKeydown: c7, onKeyup: I7, onClick: F6, onMousedown: w10 };
    return T7(W3, [A({ ourProps: g6, theirProps: { ...P6, ...l7 }, slot: r6, attrs: P6, slots: s14, name: "PopoverButton" }), e4 && !b5.value && t8.isPortalled.value && T7(f2, { id: a8, features: u4.Focusable, "data-headlessui-focus-guard": true, as: "button", type: "button", onFocus: p10 })]);
  };
} });
var $e = j6({ name: "PopoverOverlay", props: { as: { type: [Object, String], default: "div" }, static: { type: Boolean, default: false }, unmount: { type: Boolean, default: true } }, setup(d10, { attrs: P6, slots: s14 }) {
  let h6 = U3("PopoverOverlay"), f8 = `headlessui-popover-overlay-${i2()}`, t8 = l2(), o11 = O7(() => t8 !== null ? (t8.value & i9.Open) === i9.Open : h6.popoverState.value === 0);
  function y10() {
    h6.closePopover();
  }
  return () => {
    let v9 = { open: h6.popoverState.value === 0 };
    return A({ ourProps: { id: f8, "aria-hidden": true, onClick: y10 }, theirProps: d10, slot: v9, attrs: P6, slots: s14, features: N2.RenderStrategy | N2.Static, visible: o11.value, name: "PopoverOverlay" });
  };
} });
var je2 = j6({ name: "PopoverPanel", props: { as: { type: [Object, String], default: "div" }, static: { type: Boolean, default: false }, unmount: { type: Boolean, default: true }, focus: { type: Boolean, default: false }, id: { type: String, default: null } }, inheritAttrs: false, setup(d10, { attrs: P6, slots: s14, expose: h6 }) {
  var w10;
  let f8 = (w10 = d10.id) != null ? w10 : `headlessui-popover-panel-${i2()}`, { focus: t8 } = d10, o11 = U3("PopoverPanel"), y10 = O7(() => i4(o11.panel)), v9 = `headlessui-focus-sentinel-before-${i2()}`, m12 = `headlessui-focus-sentinel-after-${i2()}`;
  h6({ el: o11.panel, $el: o11.panel }), ee3(() => {
    o11.panelId.value = f8;
  }), te3(() => {
    o11.panelId.value = null;
  }), z5(ue2, o11.panelId), J3(() => {
    var p10, u10;
    if (!t8 || o11.popoverState.value !== 0 || !o11.panel) return;
    let i13 = (p10 = y10.value) == null ? void 0 : p10.activeElement;
    (u10 = o3(o11.panel)) != null && u10.contains(i13) || P(o3(o11.panel), N.First);
  });
  let b5 = l2(), E10 = O7(() => b5 !== null ? (b5.value & i9.Open) === i9.Open : o11.popoverState.value === 0);
  function a8(i13) {
    var p10, u10;
    switch (i13.key) {
      case o5.Escape:
        if (o11.popoverState.value !== 0 || !o3(o11.panel) || y10.value && !((p10 = o3(o11.panel)) != null && p10.contains(y10.value.activeElement))) return;
        i13.preventDefault(), i13.stopPropagation(), o11.closePopover(), (u10 = o3(o11.button)) == null || u10.focus();
        break;
    }
  }
  function S4(i13) {
    var u10, e4, r6, l7, g6;
    let p10 = i13.relatedTarget;
    p10 && o3(o11.panel) && ((u10 = o3(o11.panel)) != null && u10.contains(p10) || (o11.closePopover(), ((r6 = (e4 = o3(o11.beforePanelSentinel)) == null ? void 0 : e4.contains) != null && r6.call(e4, p10) || (g6 = (l7 = o3(o11.afterPanelSentinel)) == null ? void 0 : l7.contains) != null && g6.call(l7, p10)) && p10.focus({ preventScroll: true })));
  }
  let c7 = n5();
  function I7() {
    let i13 = o3(o11.panel);
    if (!i13) return;
    function p10() {
      u(c7.value, { [d3.Forwards]: () => {
        var e4;
        P(i13, N.First) === T.Error && ((e4 = o3(o11.afterPanelSentinel)) == null || e4.focus());
      }, [d3.Backwards]: () => {
        var u10;
        (u10 = o3(o11.button)) == null || u10.focus({ preventScroll: true });
      } });
    }
    p10();
  }
  function F6() {
    let i13 = o3(o11.panel);
    if (!i13) return;
    function p10() {
      u(c7.value, { [d3.Forwards]: () => {
        let u10 = o3(o11.button), e4 = o3(o11.panel);
        if (!u10) return;
        let r6 = E(), l7 = r6.indexOf(u10), g6 = r6.slice(0, l7 + 1), C8 = [...r6.slice(l7 + 1), ...g6];
        for (let L9 of C8.slice()) if (L9.dataset.headlessuiFocusGuard === "true" || e4 != null && e4.contains(L9)) {
          let $8 = C8.indexOf(L9);
          $8 !== -1 && C8.splice($8, 1);
        }
        P(C8, N.First, { sorted: false });
      }, [d3.Backwards]: () => {
        var e4;
        P(i13, N.Previous) === T.Error && ((e4 = o3(o11.button)) == null || e4.focus());
      } });
    }
    p10();
  }
  return () => {
    let i13 = { open: o11.popoverState.value === 0, close: o11.close }, { focus: p10, ...u10 } = d10, e4 = { ref: o11.panel, id: f8, onKeydown: a8, onFocusout: t8 && o11.popoverState.value === 0 ? S4 : void 0, tabIndex: -1 };
    return A({ ourProps: e4, theirProps: { ...P6, ...u10 }, attrs: P6, slot: i13, slots: { ...s14, default: (...r6) => {
      var l7;
      return [T7(W3, [E10.value && o11.isPortalled.value && T7(f2, { id: v9, ref: o11.beforePanelSentinel, features: u4.Focusable, "data-headlessui-focus-guard": true, as: "button", type: "button", onFocus: I7 }), (l7 = s14.default) == null ? void 0 : l7.call(s14, ...r6), E10.value && o11.isPortalled.value && T7(f2, { id: m12, ref: o11.afterPanelSentinel, features: u4.Focusable, "data-headlessui-focus-guard": true, as: "button", type: "button", onFocus: F6 })])];
    } }, features: N2.RenderStrategy | N2.Static, visible: E10.value, name: "PopoverPanel" });
  };
} });
var Ae3 = j6({ name: "PopoverGroup", inheritAttrs: false, props: { as: { type: [Object, String], default: "div" } }, setup(d10, { attrs: P6, slots: s14, expose: h6 }) {
  let f8 = R8(null), t8 = ie2([]), o11 = O7(() => i4(f8)), y10 = v3();
  h6({ el: f8, $el: f8 });
  function v9(a8) {
    let S4 = t8.value.indexOf(a8);
    S4 !== -1 && t8.value.splice(S4, 1);
  }
  function m12(a8) {
    return t8.value.push(a8), () => {
      v9(a8);
    };
  }
  function b5() {
    var c7;
    let a8 = o11.value;
    if (!a8) return false;
    let S4 = a8.activeElement;
    return (c7 = o3(f8)) != null && c7.contains(S4) ? true : t8.value.some((I7) => {
      var F6, w10;
      return ((F6 = a8.getElementById(I7.buttonId.value)) == null ? void 0 : F6.contains(S4)) || ((w10 = a8.getElementById(I7.panelId.value)) == null ? void 0 : w10.contains(S4));
    });
  }
  function E10(a8) {
    for (let S4 of t8.value) S4.buttonId.value !== a8 && S4.close();
  }
  return z5(le, { registerPopover: m12, unregisterPopover: v9, isFocusWithinPopoverGroup: b5, closeOthers: E10, mainTreeNodeRef: y10.mainTreeNodeRef }), () => T7(W3, [A({ ourProps: { ref: f8 }, theirProps: { ...d10, ...P6 }, slot: {}, attrs: P6, slots: s14, name: "PopoverGroup" }), T7(y10.MainTreeNode)]);
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/radio-group/radio-group.js
import { computed as o10, defineComponent as F2, Fragment as _3, h as C3, inject as $6, onMounted as D3, onUnmounted as U4, provide as W4, ref as k6, toRaw as y9, watch as J4 } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/label/label.js
import { computed as v6, defineComponent as x6, inject as L4, onMounted as k5, onUnmounted as C2, provide as j7, ref as y8, unref as h4 } from "vue";
var a7 = Symbol("LabelContext");
function d7() {
  let t8 = L4(a7, null);
  if (t8 === null) {
    let n9 = new Error("You used a <Label /> component, but it is not inside a parent.");
    throw Error.captureStackTrace && Error.captureStackTrace(n9, d7), n9;
  }
  return t8;
}
function E9({ slot: t8 = {}, name: n9 = "Label", props: i13 = {} } = {}) {
  let e4 = y8([]);
  function o11(r6) {
    return e4.value.push(r6), () => {
      let l7 = e4.value.indexOf(r6);
      l7 !== -1 && e4.value.splice(l7, 1);
    };
  }
  return j7(a7, { register: o11, slot: t8, name: n9, props: i13 }), v6(() => e4.value.length > 0 ? e4.value.join(" ") : void 0);
}
var K7 = x6({ name: "Label", props: { as: { type: [Object, String], default: "label" }, passive: { type: [Boolean], default: false }, id: { type: String, default: null } }, setup(t8, { slots: n9, attrs: i13 }) {
  var r6;
  let e4 = (r6 = t8.id) != null ? r6 : `headlessui-label-${i2()}`, o11 = d7();
  return k5(() => C2(o11.register(e4))), () => {
    let { name: l7 = "Label", slot: p10 = {}, props: c7 = {} } = o11, { passive: f8, ...s14 } = t8, u10 = { ...Object.entries(c7).reduce((b5, [g6, m12]) => Object.assign(b5, { [g6]: h4(m12) }), {}), id: e4 };
    return f8 && (delete u10.onClick, delete u10.htmlFor, delete s14.onClick), A({ ourProps: u10, theirProps: s14, slot: p10, attrs: i13, slots: n9, name: l7 });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/radio-group/radio-group.js
function le2(t8, m12) {
  return t8 === m12;
}
var H6 = Symbol("RadioGroupContext");
function N9(t8) {
  let m12 = $6(H6, null);
  if (m12 === null) {
    let u10 = new Error(`<${t8} /> is missing a parent <RadioGroup /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(u10, N9), u10;
  }
  return m12;
}
var he = F2({ name: "RadioGroup", emits: { "update:modelValue": (t8) => true }, props: { as: { type: [Object, String], default: "div" }, disabled: { type: [Boolean], default: false }, by: { type: [String, Function], default: () => le2 }, modelValue: { type: [Object, String, Number, Boolean], default: void 0 }, defaultValue: { type: [Object, String, Number, Boolean], default: void 0 }, form: { type: String, optional: true }, name: { type: String, optional: true }, id: { type: String, default: null } }, inheritAttrs: false, setup(t8, { emit: m12, attrs: u10, slots: S4, expose: g6 }) {
  var O9;
  let d10 = (O9 = t8.id) != null ? O9 : `headlessui-radiogroup-${i2()}`, p10 = k6(null), l7 = k6([]), R11 = E9({ name: "RadioGroupLabel" }), T8 = k3({ name: "RadioGroupDescription" });
  g6({ el: p10, $el: p10 });
  let [f8, G5] = d(o10(() => t8.modelValue), (e4) => m12("update:modelValue", e4), o10(() => t8.defaultValue)), s14 = { options: l7, value: f8, disabled: o10(() => t8.disabled), firstOption: o10(() => l7.value.find((e4) => !e4.propsRef.disabled)), containsCheckedOption: o10(() => l7.value.some((e4) => s14.compare(y9(e4.propsRef.value), y9(t8.modelValue)))), compare(e4, a8) {
    if (typeof t8.by == "string") {
      let n9 = t8.by;
      return (e4 == null ? void 0 : e4[n9]) === (a8 == null ? void 0 : a8[n9]);
    }
    return t8.by(e4, a8);
  }, change(e4) {
    var n9;
    if (t8.disabled || s14.compare(y9(f8.value), y9(e4))) return false;
    let a8 = (n9 = l7.value.find((i13) => s14.compare(y9(i13.propsRef.value), y9(e4)))) == null ? void 0 : n9.propsRef;
    return a8 != null && a8.disabled ? false : (G5(e4), true);
  }, registerOption(e4) {
    l7.value.push(e4), l7.value = O(l7.value, (a8) => a8.element);
  }, unregisterOption(e4) {
    let a8 = l7.value.findIndex((n9) => n9.id === e4);
    a8 !== -1 && l7.value.splice(a8, 1);
  } };
  W4(H6, s14), i8({ container: o10(() => o3(p10)), accept(e4) {
    return e4.getAttribute("role") === "radio" ? NodeFilter.FILTER_REJECT : e4.hasAttribute("role") ? NodeFilter.FILTER_SKIP : NodeFilter.FILTER_ACCEPT;
  }, walk(e4) {
    e4.setAttribute("role", "none");
  } });
  function v9(e4) {
    if (!p10.value || !p10.value.contains(e4.target)) return;
    let a8 = l7.value.filter((n9) => n9.propsRef.disabled === false).map((n9) => n9.element);
    switch (e4.key) {
      case o5.Enter:
        p4(e4.currentTarget);
        break;
      case o5.ArrowLeft:
      case o5.ArrowUp:
        if (e4.preventDefault(), e4.stopPropagation(), P(a8, N.Previous | N.WrapAround) === T.Success) {
          let i13 = l7.value.find((r6) => {
            var c7;
            return r6.element === ((c7 = i4(p10)) == null ? void 0 : c7.activeElement);
          });
          i13 && s14.change(i13.propsRef.value);
        }
        break;
      case o5.ArrowRight:
      case o5.ArrowDown:
        if (e4.preventDefault(), e4.stopPropagation(), P(a8, N.Next | N.WrapAround) === T.Success) {
          let i13 = l7.value.find((r6) => {
            var c7;
            return r6.element === ((c7 = i4(r6.element)) == null ? void 0 : c7.activeElement);
          });
          i13 && s14.change(i13.propsRef.value);
        }
        break;
      case o5.Space:
        {
          e4.preventDefault(), e4.stopPropagation();
          let n9 = l7.value.find((i13) => {
            var r6;
            return i13.element === ((r6 = i4(i13.element)) == null ? void 0 : r6.activeElement);
          });
          n9 && s14.change(n9.propsRef.value);
        }
        break;
    }
  }
  let b5 = o10(() => {
    var e4;
    return (e4 = o3(p10)) == null ? void 0 : e4.closest("form");
  });
  return D3(() => {
    J4([b5], () => {
      if (!b5.value || t8.defaultValue === void 0) return;
      function e4() {
        s14.change(t8.defaultValue);
      }
      return b5.value.addEventListener("reset", e4), () => {
        var a8;
        (a8 = b5.value) == null || a8.removeEventListener("reset", e4);
      };
    }, { immediate: true });
  }), () => {
    let { disabled: e4, name: a8, form: n9, ...i13 } = t8, r6 = { ref: p10, id: d10, role: "radiogroup", "aria-labelledby": R11.value, "aria-describedby": T8.value, onKeydown: v9 };
    return C3(_3, [...a8 != null && f8.value != null ? e2({ [a8]: f8.value }).map(([c7, L9]) => C3(f2, E3({ features: u4.Hidden, key: c7, as: "input", type: "hidden", hidden: true, readOnly: true, form: n9, disabled: e4, name: c7, value: L9 }))) : [], A({ ourProps: r6, theirProps: { ...u10, ...T2(i13, ["modelValue", "defaultValue", "by"]) }, slot: {}, attrs: u10, slots: S4, name: "RadioGroup" })]);
  };
} });
var ie3 = ((u10) => (u10[u10.Empty = 1] = "Empty", u10[u10.Active = 2] = "Active", u10))(ie3 || {});
var Oe = F2({ name: "RadioGroupOption", props: { as: { type: [Object, String], default: "div" }, value: { type: [Object, String, Number, Boolean] }, disabled: { type: Boolean, default: false }, id: { type: String, default: null } }, setup(t8, { attrs: m12, slots: u10, expose: S4 }) {
  var i13;
  let g6 = (i13 = t8.id) != null ? i13 : `headlessui-radiogroup-option-${i2()}`, d10 = N9("RadioGroupOption"), p10 = E9({ name: "RadioGroupLabel" }), l7 = k3({ name: "RadioGroupDescription" }), R11 = k6(null), T8 = o10(() => ({ value: t8.value, disabled: t8.disabled })), f8 = k6(1);
  S4({ el: R11, $el: R11 });
  let G5 = o10(() => o3(R11));
  D3(() => d10.registerOption({ id: g6, element: G5, propsRef: T8 })), U4(() => d10.unregisterOption(g6));
  let s14 = o10(() => {
    var r6;
    return ((r6 = d10.firstOption.value) == null ? void 0 : r6.id) === g6;
  }), v9 = o10(() => d10.disabled.value || t8.disabled), b5 = o10(() => d10.compare(y9(d10.value.value), y9(t8.value))), O9 = o10(() => v9.value ? -1 : b5.value || !d10.containsCheckedOption.value && s14.value ? 0 : -1);
  function e4() {
    var r6;
    d10.change(t8.value) && (f8.value |= 2, (r6 = o3(R11)) == null || r6.focus());
  }
  function a8() {
    f8.value |= 2;
  }
  function n9() {
    f8.value &= -3;
  }
  return () => {
    let { value: r6, disabled: c7, ...L9 } = t8, K11 = { checked: b5.value, disabled: v9.value, active: Boolean(f8.value & 2) }, M5 = { id: g6, ref: R11, role: "radio", "aria-checked": b5.value ? "true" : "false", "aria-labelledby": p10.value, "aria-describedby": l7.value, "aria-disabled": v9.value ? true : void 0, tabIndex: O9.value, onClick: v9.value ? void 0 : e4, onFocus: v9.value ? void 0 : a8, onBlur: v9.value ? void 0 : n9 };
    return A({ ourProps: M5, theirProps: L9, slot: K11, attrs: m12, slots: u10, name: "RadioGroupOption" });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/switch/switch.js
import { computed as u9, defineComponent as v7, Fragment as H7, h as S3, inject as M3, onMounted as I5, provide as P3, ref as w8, watch as j8 } from "vue";
var C4 = Symbol("GroupContext");
var oe = v7({ name: "SwitchGroup", props: { as: { type: [Object, String], default: "template" } }, setup(l7, { slots: c7, attrs: i13 }) {
  let r6 = w8(null), f8 = E9({ name: "SwitchLabel", props: { htmlFor: u9(() => {
    var t8;
    return (t8 = r6.value) == null ? void 0 : t8.id;
  }), onClick(t8) {
    r6.value && (t8.currentTarget.tagName === "LABEL" && t8.preventDefault(), r6.value.click(), r6.value.focus({ preventScroll: true }));
  } } }), p10 = k3({ name: "SwitchDescription" });
  return P3(C4, { switchRef: r6, labelledby: f8, describedby: p10 }), () => A({ theirProps: l7, ourProps: {}, slot: {}, slots: c7, attrs: i13, name: "SwitchGroup" });
} });
var ue3 = v7({ name: "Switch", emits: { "update:modelValue": (l7) => true }, props: { as: { type: [Object, String], default: "button" }, modelValue: { type: Boolean, default: void 0 }, defaultChecked: { type: Boolean, optional: true }, form: { type: String, optional: true }, name: { type: String, optional: true }, value: { type: String, optional: true }, id: { type: String, default: null }, disabled: { type: Boolean, default: false }, tabIndex: { type: Number, default: 0 } }, inheritAttrs: false, setup(l7, { emit: c7, attrs: i13, slots: r6, expose: f8 }) {
  var h6;
  let p10 = (h6 = l7.id) != null ? h6 : `headlessui-switch-${i2()}`, n9 = M3(C4, null), [t8, s14] = d(u9(() => l7.modelValue), (e4) => c7("update:modelValue", e4), u9(() => l7.defaultChecked));
  function m12() {
    s14(!t8.value);
  }
  let E10 = w8(null), o11 = n9 === null ? E10 : n9.switchRef, L9 = s5(u9(() => ({ as: l7.as, type: i13.type })), o11);
  f8({ el: o11, $el: o11 });
  function D4(e4) {
    e4.preventDefault(), m12();
  }
  function R11(e4) {
    e4.key === o5.Space ? (e4.preventDefault(), m12()) : e4.key === o5.Enter && p4(e4.currentTarget);
  }
  function x8(e4) {
    e4.preventDefault();
  }
  let d10 = u9(() => {
    var e4, a8;
    return (a8 = (e4 = o3(o11)) == null ? void 0 : e4.closest) == null ? void 0 : a8.call(e4, "form");
  });
  return I5(() => {
    j8([d10], () => {
      if (!d10.value || l7.defaultChecked === void 0) return;
      function e4() {
        s14(l7.defaultChecked);
      }
      return d10.value.addEventListener("reset", e4), () => {
        var a8;
        (a8 = d10.value) == null || a8.removeEventListener("reset", e4);
      };
    }, { immediate: true });
  }), () => {
    let { name: e4, value: a8, form: K11, tabIndex: y10, ...b5 } = l7, T8 = { checked: t8.value }, B5 = { id: p10, ref: o11, role: "switch", type: L9.value, tabIndex: y10 === -1 ? 0 : y10, "aria-checked": t8.value, "aria-labelledby": n9 == null ? void 0 : n9.labelledby.value, "aria-describedby": n9 == null ? void 0 : n9.describedby.value, onClick: D4, onKeyup: R11, onKeypress: x8 };
    return S3(H7, [e4 != null && t8.value != null ? S3(f2, E3({ features: u4.Hidden, as: "input", type: "checkbox", hidden: true, readOnly: true, checked: t8.value, form: K11, disabled: b5.disabled, name: e4, value: a8 })) : null, A({ ourProps: B5, theirProps: { ...i13, ...T2(b5, ["modelValue", "defaultChecked"]) }, slot: T8, attrs: i13, slots: r6, name: "Switch" })]);
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/tabs/tabs.js
import { computed as v8, defineComponent as L5, Fragment as z6, h as A6, inject as j9, onMounted as F3, onUnmounted as K8, provide as N10, ref as P4, watch as _4, watchEffect as J5 } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/internal/focus-sentinel.js
import { defineComponent as i12, h as m9, ref as f7 } from "vue";
var d8 = i12({ props: { onFocus: { type: Function, required: true } }, setup(t8) {
  let n9 = f7(true);
  return () => n9.value ? m9(f2, { as: "button", type: "button", features: u4.Focusable, onFocus(o11) {
    o11.preventDefault();
    let e4, a8 = 50;
    function r6() {
      var u10;
      if (a8-- <= 0) {
        e4 && cancelAnimationFrame(e4);
        return;
      }
      if ((u10 = t8.onFocus) != null && u10.call(t8)) {
        n9.value = false, cancelAnimationFrame(e4);
        return;
      }
      e4 = requestAnimationFrame(r6);
    }
    e4 = requestAnimationFrame(r6);
  } }) : null;
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/tabs/tabs.js
var te4 = ((s14) => (s14[s14.Forwards = 0] = "Forwards", s14[s14.Backwards = 1] = "Backwards", s14))(te4 || {});
var le3 = ((d10) => (d10[d10.Less = -1] = "Less", d10[d10.Equal = 0] = "Equal", d10[d10.Greater = 1] = "Greater", d10))(le3 || {});
var U5 = Symbol("TabsContext");
function C5(a8) {
  let b5 = j9(U5, null);
  if (b5 === null) {
    let s14 = new Error(`<${a8} /> is missing a parent <TabGroup /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(s14, C5), s14;
  }
  return b5;
}
var G3 = Symbol("TabsSSRContext");
var me2 = L5({ name: "TabGroup", emits: { change: (a8) => true }, props: { as: { type: [Object, String], default: "template" }, selectedIndex: { type: [Number], default: null }, defaultIndex: { type: [Number], default: 0 }, vertical: { type: [Boolean], default: false }, manual: { type: [Boolean], default: false } }, inheritAttrs: false, setup(a8, { slots: b5, attrs: s14, emit: d10 }) {
  var E10;
  let i13 = P4((E10 = a8.selectedIndex) != null ? E10 : a8.defaultIndex), l7 = P4([]), r6 = P4([]), p10 = v8(() => a8.selectedIndex !== null), R11 = v8(() => p10.value ? a8.selectedIndex : i13.value);
  function y10(t8) {
    var c7;
    let n9 = O(u10.tabs.value, o3), o11 = O(u10.panels.value, o3), e4 = n9.filter((I7) => {
      var m12;
      return !((m12 = o3(I7)) != null && m12.hasAttribute("disabled"));
    });
    if (t8 < 0 || t8 > n9.length - 1) {
      let I7 = u(i13.value === null ? 0 : Math.sign(t8 - i13.value), { [-1]: () => 1, [0]: () => u(Math.sign(t8), { [-1]: () => 0, [0]: () => 0, [1]: () => 1 }), [1]: () => 0 }), m12 = u(I7, { [0]: () => n9.indexOf(e4[0]), [1]: () => n9.indexOf(e4[e4.length - 1]) });
      m12 !== -1 && (i13.value = m12), u10.tabs.value = n9, u10.panels.value = o11;
    } else {
      let I7 = n9.slice(0, t8), h6 = [...n9.slice(t8), ...I7].find((W7) => e4.includes(W7));
      if (!h6) return;
      let O9 = (c7 = n9.indexOf(h6)) != null ? c7 : u10.selectedIndex.value;
      O9 === -1 && (O9 = u10.selectedIndex.value), i13.value = O9, u10.tabs.value = n9, u10.panels.value = o11;
    }
  }
  let u10 = { selectedIndex: v8(() => {
    var t8, n9;
    return (n9 = (t8 = i13.value) != null ? t8 : a8.defaultIndex) != null ? n9 : null;
  }), orientation: v8(() => a8.vertical ? "vertical" : "horizontal"), activation: v8(() => a8.manual ? "manual" : "auto"), tabs: l7, panels: r6, setSelectedIndex(t8) {
    R11.value !== t8 && d10("change", t8), p10.value || y10(t8);
  }, registerTab(t8) {
    var o11;
    if (l7.value.includes(t8)) return;
    let n9 = l7.value[i13.value];
    if (l7.value.push(t8), l7.value = O(l7.value, o3), !p10.value) {
      let e4 = (o11 = l7.value.indexOf(n9)) != null ? o11 : i13.value;
      e4 !== -1 && (i13.value = e4);
    }
  }, unregisterTab(t8) {
    let n9 = l7.value.indexOf(t8);
    n9 !== -1 && l7.value.splice(n9, 1);
  }, registerPanel(t8) {
    r6.value.includes(t8) || (r6.value.push(t8), r6.value = O(r6.value, o3));
  }, unregisterPanel(t8) {
    let n9 = r6.value.indexOf(t8);
    n9 !== -1 && r6.value.splice(n9, 1);
  } };
  N10(U5, u10);
  let T8 = P4({ tabs: [], panels: [] }), x8 = P4(false);
  F3(() => {
    x8.value = true;
  }), N10(G3, v8(() => x8.value ? null : T8.value));
  let w10 = v8(() => a8.selectedIndex);
  return F3(() => {
    _4([w10], () => {
      var t8;
      return y10((t8 = a8.selectedIndex) != null ? t8 : a8.defaultIndex);
    }, { immediate: true });
  }), J5(() => {
    if (!p10.value || R11.value == null || u10.tabs.value.length <= 0) return;
    let t8 = O(u10.tabs.value, o3);
    t8.some((o11, e4) => o3(u10.tabs.value[e4]) !== o3(o11)) && u10.setSelectedIndex(t8.findIndex((o11) => o3(o11) === o3(u10.tabs.value[R11.value])));
  }), () => {
    let t8 = { selectedIndex: i13.value };
    return A6(z6, [l7.value.length <= 0 && A6(d8, { onFocus: () => {
      for (let n9 of l7.value) {
        let o11 = o3(n9);
        if ((o11 == null ? void 0 : o11.tabIndex) === 0) return o11.focus(), true;
      }
      return false;
    } }), A({ theirProps: { ...s14, ...T2(a8, ["selectedIndex", "defaultIndex", "manual", "vertical", "onChange"]) }, ourProps: {}, slot: t8, slots: b5, attrs: s14, name: "TabGroup" })]);
  };
} });
var pe3 = L5({ name: "TabList", props: { as: { type: [Object, String], default: "div" } }, setup(a8, { attrs: b5, slots: s14 }) {
  let d10 = C5("TabList");
  return () => {
    let i13 = { selectedIndex: d10.selectedIndex.value }, l7 = { role: "tablist", "aria-orientation": d10.orientation.value };
    return A({ ourProps: l7, theirProps: a8, slot: i13, attrs: b5, slots: s14, name: "TabList" });
  };
} });
var xe = L5({ name: "Tab", props: { as: { type: [Object, String], default: "button" }, disabled: { type: [Boolean], default: false }, id: { type: String, default: null } }, setup(a8, { attrs: b5, slots: s14, expose: d10 }) {
  var o11;
  let i13 = (o11 = a8.id) != null ? o11 : `headlessui-tabs-tab-${i2()}`, l7 = C5("Tab"), r6 = P4(null);
  d10({ el: r6, $el: r6 }), F3(() => l7.registerTab(r6)), K8(() => l7.unregisterTab(r6));
  let p10 = j9(G3), R11 = v8(() => {
    if (p10.value) {
      let e4 = p10.value.tabs.indexOf(i13);
      return e4 === -1 ? p10.value.tabs.push(i13) - 1 : e4;
    }
    return -1;
  }), y10 = v8(() => {
    let e4 = l7.tabs.value.indexOf(r6);
    return e4 === -1 ? R11.value : e4;
  }), u10 = v8(() => y10.value === l7.selectedIndex.value);
  function T8(e4) {
    var I7;
    let c7 = e4();
    if (c7 === T.Success && l7.activation.value === "auto") {
      let m12 = (I7 = i4(r6)) == null ? void 0 : I7.activeElement, h6 = l7.tabs.value.findIndex((O9) => o3(O9) === m12);
      h6 !== -1 && l7.setSelectedIndex(h6);
    }
    return c7;
  }
  function x8(e4) {
    let c7 = l7.tabs.value.map((m12) => o3(m12)).filter(Boolean);
    if (e4.key === o5.Space || e4.key === o5.Enter) {
      e4.preventDefault(), e4.stopPropagation(), l7.setSelectedIndex(y10.value);
      return;
    }
    switch (e4.key) {
      case o5.Home:
      case o5.PageUp:
        return e4.preventDefault(), e4.stopPropagation(), T8(() => P(c7, N.First));
      case o5.End:
      case o5.PageDown:
        return e4.preventDefault(), e4.stopPropagation(), T8(() => P(c7, N.Last));
    }
    if (T8(() => u(l7.orientation.value, { vertical() {
      return e4.key === o5.ArrowUp ? P(c7, N.Previous | N.WrapAround) : e4.key === o5.ArrowDown ? P(c7, N.Next | N.WrapAround) : T.Error;
    }, horizontal() {
      return e4.key === o5.ArrowLeft ? P(c7, N.Previous | N.WrapAround) : e4.key === o5.ArrowRight ? P(c7, N.Next | N.WrapAround) : T.Error;
    } })) === T.Success) return e4.preventDefault();
  }
  let w10 = P4(false);
  function E10() {
    var e4;
    w10.value || (w10.value = true, !a8.disabled && ((e4 = o3(r6)) == null || e4.focus({ preventScroll: true }), l7.setSelectedIndex(y10.value), t(() => {
      w10.value = false;
    })));
  }
  function t8(e4) {
    e4.preventDefault();
  }
  let n9 = s5(v8(() => ({ as: a8.as, type: b5.type })), r6);
  return () => {
    var m12, h6;
    let e4 = { selected: u10.value, disabled: (m12 = a8.disabled) != null ? m12 : false }, { ...c7 } = a8, I7 = { ref: r6, onKeydown: x8, onMousedown: t8, onClick: E10, id: i13, role: "tab", type: n9.value, "aria-controls": (h6 = o3(l7.panels.value[y10.value])) == null ? void 0 : h6.id, "aria-selected": u10.value, tabIndex: u10.value ? 0 : -1, disabled: a8.disabled ? true : void 0 };
    return A({ ourProps: I7, theirProps: c7, slot: e4, attrs: b5, slots: s14, name: "Tab" });
  };
} });
var Ie2 = L5({ name: "TabPanels", props: { as: { type: [Object, String], default: "div" } }, setup(a8, { slots: b5, attrs: s14 }) {
  let d10 = C5("TabPanels");
  return () => {
    let i13 = { selectedIndex: d10.selectedIndex.value };
    return A({ theirProps: a8, ourProps: {}, slot: i13, attrs: s14, slots: b5, name: "TabPanels" });
  };
} });
var ye2 = L5({ name: "TabPanel", props: { as: { type: [Object, String], default: "div" }, static: { type: Boolean, default: false }, unmount: { type: Boolean, default: true }, id: { type: String, default: null }, tabIndex: { type: Number, default: 0 } }, setup(a8, { attrs: b5, slots: s14, expose: d10 }) {
  var T8;
  let i13 = (T8 = a8.id) != null ? T8 : `headlessui-tabs-panel-${i2()}`, l7 = C5("TabPanel"), r6 = P4(null);
  d10({ el: r6, $el: r6 }), F3(() => l7.registerPanel(r6)), K8(() => l7.unregisterPanel(r6));
  let p10 = j9(G3), R11 = v8(() => {
    if (p10.value) {
      let x8 = p10.value.panels.indexOf(i13);
      return x8 === -1 ? p10.value.panels.push(i13) - 1 : x8;
    }
    return -1;
  }), y10 = v8(() => {
    let x8 = l7.panels.value.indexOf(r6);
    return x8 === -1 ? R11.value : x8;
  }), u10 = v8(() => y10.value === l7.selectedIndex.value);
  return () => {
    var n9;
    let x8 = { selected: u10.value }, { tabIndex: w10, ...E10 } = a8, t8 = { ref: r6, id: i13, role: "tabpanel", "aria-labelledby": (n9 = o3(l7.tabs.value[y10.value])) == null ? void 0 : n9.id, tabIndex: u10.value ? w10 : -1 };
    return !u10.value && a8.unmount && !a8.static ? A6(f2, { as: "span", "aria-hidden": true, ...t8 }) : A({ ourProps: t8, theirProps: E10, slot: x8, attrs: b5, slots: s14, features: N2.Static | N2.RenderStrategy, visible: u10.value, name: "TabPanel" });
  };
} });

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/transitions/transition.js
import { computed as w9, defineComponent as K9, h as k7, inject as F5, normalizeClass as ae2, onMounted as C6, onUnmounted as z7, provide as B3, ref as m11, watch as le4, watchEffect as x7 } from "vue";

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/utils/once.js
function l6(r6) {
  let e4 = { called: false };
  return (...t8) => {
    if (!e4.called) return e4.called = true, r6(...t8);
  };
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/transitions/utils/transition.js
function m10(e4, ...t8) {
  e4 && t8.length > 0 && e4.classList.add(...t8);
}
function d9(e4, ...t8) {
  e4 && t8.length > 0 && e4.classList.remove(...t8);
}
var g4 = ((i13) => (i13.Finished = "finished", i13.Cancelled = "cancelled", i13))(g4 || {});
function F4(e4, t8) {
  let i13 = o();
  if (!e4) return i13.dispose;
  let { transitionDuration: n9, transitionDelay: a8 } = getComputedStyle(e4), [l7, s14] = [n9, a8].map((o11) => {
    let [u10 = 0] = o11.split(",").filter(Boolean).map((r6) => r6.includes("ms") ? parseFloat(r6) : parseFloat(r6) * 1e3).sort((r6, c7) => c7 - r6);
    return u10;
  });
  return l7 !== 0 ? i13.setTimeout(() => t8("finished"), l7 + s14) : t8("finished"), i13.add(() => t8("cancelled")), i13.dispose;
}
function L6(e4, t8, i13, n9, a8, l7) {
  let s14 = o(), o11 = l7 !== void 0 ? l6(l7) : () => {
  };
  return d9(e4, ...a8), m10(e4, ...t8, ...i13), s14.nextFrame(() => {
    d9(e4, ...i13), m10(e4, ...n9), s14.add(F4(e4, (u10) => (d9(e4, ...n9, ...t8), m10(e4, ...a8), o11(u10))));
  }), s14.add(() => d9(e4, ...t8, ...i13, ...n9, ...a8)), s14.add(() => o11("cancelled")), s14.dispose;
}

// node_modules/.pnpm/@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui/vue/dist/components/transitions/transition.js
function g5(e4 = "") {
  return e4.split(/\s+/).filter((t8) => t8.length > 1);
}
var R9 = Symbol("TransitionContext");
var pe4 = ((a8) => (a8.Visible = "visible", a8.Hidden = "hidden", a8))(pe4 || {});
function me3() {
  return F5(R9, null) !== null;
}
function Te2() {
  let e4 = F5(R9, null);
  if (e4 === null) throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");
  return e4;
}
function ge3() {
  let e4 = F5(N11, null);
  if (e4 === null) throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");
  return e4;
}
var N11 = Symbol("NestingContext");
function L7(e4) {
  return "children" in e4 ? L7(e4.children) : e4.value.filter(({ state: t8 }) => t8 === "visible").length > 0;
}
function Q2(e4) {
  let t8 = m11([]), a8 = m11(false);
  C6(() => a8.value = true), z7(() => a8.value = false);
  function s14(n9, r6 = S2.Hidden) {
    let l7 = t8.value.findIndex(({ id: f8 }) => f8 === n9);
    l7 !== -1 && (u(r6, { [S2.Unmount]() {
      t8.value.splice(l7, 1);
    }, [S2.Hidden]() {
      t8.value[l7].state = "hidden";
    } }), !L7(t8) && a8.value && (e4 == null || e4()));
  }
  function h6(n9) {
    let r6 = t8.value.find(({ id: l7 }) => l7 === n9);
    return r6 ? r6.state !== "visible" && (r6.state = "visible") : t8.value.push({ id: n9, state: "visible" }), () => s14(n9, S2.Unmount);
  }
  return { children: t8, register: h6, unregister: s14 };
}
var W5 = N2.RenderStrategy;
var he2 = K9({ props: { as: { type: [Object, String], default: "div" }, show: { type: [Boolean], default: null }, unmount: { type: [Boolean], default: true }, appear: { type: [Boolean], default: false }, enter: { type: [String], default: "" }, enterFrom: { type: [String], default: "" }, enterTo: { type: [String], default: "" }, entered: { type: [String], default: "" }, leave: { type: [String], default: "" }, leaveFrom: { type: [String], default: "" }, leaveTo: { type: [String], default: "" } }, emits: { beforeEnter: () => true, afterEnter: () => true, beforeLeave: () => true, afterLeave: () => true }, setup(e4, { emit: t8, attrs: a8, slots: s14, expose: h6 }) {
  let n9 = m11(0);
  function r6() {
    n9.value |= i9.Opening, t8("beforeEnter");
  }
  function l7() {
    n9.value &= ~i9.Opening, t8("afterEnter");
  }
  function f8() {
    n9.value |= i9.Closing, t8("beforeLeave");
  }
  function S4() {
    n9.value &= ~i9.Closing, t8("afterLeave");
  }
  if (!me3() && s6()) return () => k7(Se3, { ...e4, onBeforeEnter: r6, onAfterEnter: l7, onBeforeLeave: f8, onAfterLeave: S4 }, s14);
  let d10 = m11(null), y10 = w9(() => e4.unmount ? S2.Unmount : S2.Hidden);
  h6({ el: d10, $el: d10 });
  let { show: v9, appear: A7 } = Te2(), { register: D4, unregister: H8 } = ge3(), i13 = m11(v9.value ? "visible" : "hidden"), I7 = { value: true }, c7 = i2(), b5 = { value: false }, P6 = Q2(() => {
    !b5.value && i13.value !== "hidden" && (i13.value = "hidden", H8(c7), S4());
  });
  C6(() => {
    let o11 = D4(c7);
    z7(o11);
  }), x7(() => {
    if (y10.value === S2.Hidden && c7) {
      if (v9.value && i13.value !== "visible") {
        i13.value = "visible";
        return;
      }
      u(i13.value, { ["hidden"]: () => H8(c7), ["visible"]: () => D4(c7) });
    }
  });
  let j10 = g5(e4.enter), M5 = g5(e4.enterFrom), X3 = g5(e4.enterTo), _6 = g5(e4.entered), Y4 = g5(e4.leave), Z3 = g5(e4.leaveFrom), ee5 = g5(e4.leaveTo);
  C6(() => {
    x7(() => {
      if (i13.value === "visible") {
        let o11 = o3(d10);
        if (o11 instanceof Comment && o11.data === "") throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?");
      }
    });
  });
  function te6(o11) {
    let E10 = I7.value && !A7.value, p10 = o3(d10);
    !p10 || !(p10 instanceof HTMLElement) || E10 || (b5.value = true, v9.value && r6(), v9.value || f8(), o11(v9.value ? L6(p10, j10, M5, X3, _6, (V3) => {
      b5.value = false, V3 === g4.Finished && l7();
    }) : L6(p10, Y4, Z3, ee5, _6, (V3) => {
      b5.value = false, V3 === g4.Finished && (L7(P6) || (i13.value = "hidden", H8(c7), S4()));
    })));
  }
  return C6(() => {
    le4([v9], (o11, E10, p10) => {
      te6(p10), I7.value = false;
    }, { immediate: true });
  }), B3(N11, P6), t4(w9(() => u(i13.value, { ["visible"]: i9.Open, ["hidden"]: i9.Closed }) | n9.value)), () => {
    let { appear: o11, show: E10, enter: p10, enterFrom: V3, enterTo: Ce2, entered: ye4, leave: be4, leaveFrom: Ee3, leaveTo: Ve3, ...U7 } = e4, ne3 = { ref: d10 }, re3 = { ...U7, ...A7.value && v9.value && c.isServer ? { class: ae2([a8.class, U7.class, ...j10, ...M5]) } : {} };
    return A({ theirProps: re3, ourProps: ne3, slot: {}, slots: s14, attrs: a8, features: W5, visible: i13.value === "visible", name: "TransitionChild" });
  };
} });
var ce2 = he2;
var Se3 = K9({ inheritAttrs: false, props: { as: { type: [Object, String], default: "div" }, show: { type: [Boolean], default: null }, unmount: { type: [Boolean], default: true }, appear: { type: [Boolean], default: false }, enter: { type: [String], default: "" }, enterFrom: { type: [String], default: "" }, enterTo: { type: [String], default: "" }, entered: { type: [String], default: "" }, leave: { type: [String], default: "" }, leaveFrom: { type: [String], default: "" }, leaveTo: { type: [String], default: "" } }, emits: { beforeEnter: () => true, afterEnter: () => true, beforeLeave: () => true, afterLeave: () => true }, setup(e4, { emit: t8, attrs: a8, slots: s14 }) {
  let h6 = l2(), n9 = w9(() => e4.show === null && h6 !== null ? (h6.value & i9.Open) === i9.Open : e4.show);
  x7(() => {
    if (![true, false].includes(n9.value)) throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.');
  });
  let r6 = m11(n9.value ? "visible" : "hidden"), l7 = Q2(() => {
    r6.value = "hidden";
  }), f8 = m11(true), S4 = { show: n9, appear: w9(() => e4.appear || !f8.value) };
  return C6(() => {
    x7(() => {
      f8.value = false, n9.value ? r6.value = "visible" : L7(l7) || (r6.value = "hidden");
    });
  }), B3(N11, l7), B3(R9, S4), () => {
    let d10 = T2(e4, ["show", "appear", "unmount", "onBeforeEnter", "onBeforeLeave", "onAfterEnter", "onAfterLeave"]), y10 = { unmount: e4.unmount };
    return A({ ourProps: { ...y10, as: "template" }, theirProps: {}, slot: {}, slots: { ...s14, default: () => [k7(ce2, { onBeforeEnter: () => t8("beforeEnter"), onAfterEnter: () => t8("afterEnter"), onBeforeLeave: () => t8("beforeLeave"), onAfterLeave: () => t8("afterLeave"), ...a8, ...y10, ...d10 }, s14.default)] }, attrs: {}, features: W5, visible: r6.value === "visible", name: "Transition" });
  };
} });

// node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs
var sides = ["top", "right", "bottom", "left"];
var alignments = ["start", "end"];
var placements = sides.reduce((acc, side) => acc.concat(side, side + "-" + alignments[0], side + "-" + alignments[1]), []);
var min = Math.min;
var max = Math.max;
var round = Math.round;
var floor = Math.floor;
var createCoords = (v9) => ({
  x: v9,
  y: v9
});
var oppositeSideMap = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
};
var oppositeAlignmentMap = {
  start: "end",
  end: "start"
};
function clamp(start, value, end) {
  return max(start, min(value, end));
}
function evaluate(value, param) {
  return typeof value === "function" ? value(param) : value;
}
function getSide(placement) {
  return placement.split("-")[0];
}
function getAlignment(placement) {
  return placement.split("-")[1];
}
function getOppositeAxis(axis) {
  return axis === "x" ? "y" : "x";
}
function getAxisLength(axis) {
  return axis === "y" ? "height" : "width";
}
function getSideAxis(placement) {
  return ["top", "bottom"].includes(getSide(placement)) ? "y" : "x";
}
function getAlignmentAxis(placement) {
  return getOppositeAxis(getSideAxis(placement));
}
function getAlignmentSides(placement, rects, rtl) {
  if (rtl === void 0) {
    rtl = false;
  }
  const alignment = getAlignment(placement);
  const alignmentAxis = getAlignmentAxis(placement);
  const length = getAxisLength(alignmentAxis);
  let mainAlignmentSide = alignmentAxis === "x" ? alignment === (rtl ? "end" : "start") ? "right" : "left" : alignment === "start" ? "bottom" : "top";
  if (rects.reference[length] > rects.floating[length]) {
    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);
  }
  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];
}
function getExpandedPlacements(placement) {
  const oppositePlacement = getOppositePlacement(placement);
  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];
}
function getOppositeAlignmentPlacement(placement) {
  return placement.replace(/start|end/g, (alignment) => oppositeAlignmentMap[alignment]);
}
function getSideList(side, isStart, rtl) {
  const lr = ["left", "right"];
  const rl = ["right", "left"];
  const tb = ["top", "bottom"];
  const bt = ["bottom", "top"];
  switch (side) {
    case "top":
    case "bottom":
      if (rtl) return isStart ? rl : lr;
      return isStart ? lr : rl;
    case "left":
    case "right":
      return isStart ? tb : bt;
    default:
      return [];
  }
}
function getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {
  const alignment = getAlignment(placement);
  let list = getSideList(getSide(placement), direction === "start", rtl);
  if (alignment) {
    list = list.map((side) => side + "-" + alignment);
    if (flipAlignment) {
      list = list.concat(list.map(getOppositeAlignmentPlacement));
    }
  }
  return list;
}
function getOppositePlacement(placement) {
  return placement.replace(/left|right|bottom|top/g, (side) => oppositeSideMap[side]);
}
function expandPaddingObject(padding) {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    ...padding
  };
}
function getPaddingObject(padding) {
  return typeof padding !== "number" ? expandPaddingObject(padding) : {
    top: padding,
    right: padding,
    bottom: padding,
    left: padding
  };
}
function rectToClientRect(rect) {
  const {
    x: x8,
    y: y10,
    width,
    height
  } = rect;
  return {
    width,
    height,
    top: y10,
    left: x8,
    right: x8 + width,
    bottom: y10 + height,
    x: x8,
    y: y10
  };
}

// node_modules/.pnpm/@floating-ui+core@1.6.9/node_modules/@floating-ui/core/dist/floating-ui.core.mjs
function computeCoordsFromPlacement(_ref, placement, rtl) {
  let {
    reference,
    floating
  } = _ref;
  const sideAxis = getSideAxis(placement);
  const alignmentAxis = getAlignmentAxis(placement);
  const alignLength = getAxisLength(alignmentAxis);
  const side = getSide(placement);
  const isVertical = sideAxis === "y";
  const commonX = reference.x + reference.width / 2 - floating.width / 2;
  const commonY = reference.y + reference.height / 2 - floating.height / 2;
  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;
  let coords;
  switch (side) {
    case "top":
      coords = {
        x: commonX,
        y: reference.y - floating.height
      };
      break;
    case "bottom":
      coords = {
        x: commonX,
        y: reference.y + reference.height
      };
      break;
    case "right":
      coords = {
        x: reference.x + reference.width,
        y: commonY
      };
      break;
    case "left":
      coords = {
        x: reference.x - floating.width,
        y: commonY
      };
      break;
    default:
      coords = {
        x: reference.x,
        y: reference.y
      };
  }
  switch (getAlignment(placement)) {
    case "start":
      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);
      break;
    case "end":
      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);
      break;
  }
  return coords;
}
var computePosition = async (reference, floating, config) => {
  const {
    placement = "bottom",
    strategy = "absolute",
    middleware = [],
    platform: platform2
  } = config;
  const validMiddleware = middleware.filter(Boolean);
  const rtl = await (platform2.isRTL == null ? void 0 : platform2.isRTL(floating));
  let rects = await platform2.getElementRects({
    reference,
    floating,
    strategy
  });
  let {
    x: x8,
    y: y10
  } = computeCoordsFromPlacement(rects, placement, rtl);
  let statefulPlacement = placement;
  let middlewareData = {};
  let resetCount = 0;
  for (let i13 = 0; i13 < validMiddleware.length; i13++) {
    const {
      name,
      fn
    } = validMiddleware[i13];
    const {
      x: nextX,
      y: nextY,
      data,
      reset
    } = await fn({
      x: x8,
      y: y10,
      initialPlacement: placement,
      placement: statefulPlacement,
      strategy,
      middlewareData,
      rects,
      platform: platform2,
      elements: {
        reference,
        floating
      }
    });
    x8 = nextX != null ? nextX : x8;
    y10 = nextY != null ? nextY : y10;
    middlewareData = {
      ...middlewareData,
      [name]: {
        ...middlewareData[name],
        ...data
      }
    };
    if (reset && resetCount <= 50) {
      resetCount++;
      if (typeof reset === "object") {
        if (reset.placement) {
          statefulPlacement = reset.placement;
        }
        if (reset.rects) {
          rects = reset.rects === true ? await platform2.getElementRects({
            reference,
            floating,
            strategy
          }) : reset.rects;
        }
        ({
          x: x8,
          y: y10
        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));
      }
      i13 = -1;
    }
  }
  return {
    x: x8,
    y: y10,
    placement: statefulPlacement,
    strategy,
    middlewareData
  };
};
async function detectOverflow(state, options) {
  var _await$platform$isEle;
  if (options === void 0) {
    options = {};
  }
  const {
    x: x8,
    y: y10,
    platform: platform2,
    rects,
    elements,
    strategy
  } = state;
  const {
    boundary = "clippingAncestors",
    rootBoundary = "viewport",
    elementContext = "floating",
    altBoundary = false,
    padding = 0
  } = evaluate(options, state);
  const paddingObject = getPaddingObject(padding);
  const altContext = elementContext === "floating" ? "reference" : "floating";
  const element = elements[altBoundary ? altContext : elementContext];
  const clippingClientRect = rectToClientRect(await platform2.getClippingRect({
    element: ((_await$platform$isEle = await (platform2.isElement == null ? void 0 : platform2.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || await (platform2.getDocumentElement == null ? void 0 : platform2.getDocumentElement(elements.floating)),
    boundary,
    rootBoundary,
    strategy
  }));
  const rect = elementContext === "floating" ? {
    x: x8,
    y: y10,
    width: rects.floating.width,
    height: rects.floating.height
  } : rects.reference;
  const offsetParent = await (platform2.getOffsetParent == null ? void 0 : platform2.getOffsetParent(elements.floating));
  const offsetScale = await (platform2.isElement == null ? void 0 : platform2.isElement(offsetParent)) ? await (platform2.getScale == null ? void 0 : platform2.getScale(offsetParent)) || {
    x: 1,
    y: 1
  } : {
    x: 1,
    y: 1
  };
  const elementClientRect = rectToClientRect(platform2.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform2.convertOffsetParentRelativeRectToViewportRelativeRect({
    elements,
    rect,
    offsetParent,
    strategy
  }) : rect);
  return {
    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,
    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,
    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,
    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x
  };
}
var arrow = (options) => ({
  name: "arrow",
  options,
  async fn(state) {
    const {
      x: x8,
      y: y10,
      placement,
      rects,
      platform: platform2,
      elements,
      middlewareData
    } = state;
    const {
      element,
      padding = 0
    } = evaluate(options, state) || {};
    if (element == null) {
      return {};
    }
    const paddingObject = getPaddingObject(padding);
    const coords = {
      x: x8,
      y: y10
    };
    const axis = getAlignmentAxis(placement);
    const length = getAxisLength(axis);
    const arrowDimensions = await platform2.getDimensions(element);
    const isYAxis = axis === "y";
    const minProp = isYAxis ? "top" : "left";
    const maxProp = isYAxis ? "bottom" : "right";
    const clientProp = isYAxis ? "clientHeight" : "clientWidth";
    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];
    const startDiff = coords[axis] - rects.reference[axis];
    const arrowOffsetParent = await (platform2.getOffsetParent == null ? void 0 : platform2.getOffsetParent(element));
    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;
    if (!clientSize || !await (platform2.isElement == null ? void 0 : platform2.isElement(arrowOffsetParent))) {
      clientSize = elements.floating[clientProp] || rects.floating[length];
    }
    const centerToReference = endDiff / 2 - startDiff / 2;
    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;
    const minPadding = min(paddingObject[minProp], largestPossiblePadding);
    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);
    const min$1 = minPadding;
    const max2 = clientSize - arrowDimensions[length] - maxPadding;
    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;
    const offset3 = clamp(min$1, center, max2);
    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset3 && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;
    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max2 : 0;
    return {
      [axis]: coords[axis] + alignmentOffset,
      data: {
        [axis]: offset3,
        centerOffset: center - offset3 - alignmentOffset,
        ...shouldAddOffset && {
          alignmentOffset
        }
      },
      reset: shouldAddOffset
    };
  }
});
function getPlacementList(alignment, autoAlignment, allowedPlacements) {
  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter((placement) => getAlignment(placement) === alignment), ...allowedPlacements.filter((placement) => getAlignment(placement) !== alignment)] : allowedPlacements.filter((placement) => getSide(placement) === placement);
  return allowedPlacementsSortedByAlignment.filter((placement) => {
    if (alignment) {
      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);
    }
    return true;
  });
}
var autoPlacement = function(options) {
  if (options === void 0) {
    options = {};
  }
  return {
    name: "autoPlacement",
    options,
    async fn(state) {
      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;
      const {
        rects,
        middlewareData,
        placement,
        platform: platform2,
        elements
      } = state;
      const {
        crossAxis = false,
        alignment,
        allowedPlacements = placements,
        autoAlignment = true,
        ...detectOverflowOptions
      } = evaluate(options, state);
      const placements$1 = alignment !== void 0 || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;
      const overflow = await detectOverflow(state, detectOverflowOptions);
      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;
      const currentPlacement = placements$1[currentIndex];
      if (currentPlacement == null) {
        return {};
      }
      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform2.isRTL == null ? void 0 : platform2.isRTL(elements.floating)));
      if (placement !== currentPlacement) {
        return {
          reset: {
            placement: placements$1[0]
          }
        };
      }
      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];
      const allOverflows = [...((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || [], {
        placement: currentPlacement,
        overflows: currentOverflows
      }];
      const nextPlacement = placements$1[currentIndex + 1];
      if (nextPlacement) {
        return {
          data: {
            index: currentIndex + 1,
            overflows: allOverflows
          },
          reset: {
            placement: nextPlacement
          }
        };
      }
      const placementsSortedByMostSpace = allOverflows.map((d10) => {
        const alignment2 = getAlignment(d10.placement);
        return [d10.placement, alignment2 && crossAxis ? (
          // Check along the mainAxis and main crossAxis side.
          d10.overflows.slice(0, 2).reduce((acc, v9) => acc + v9, 0)
        ) : (
          // Check only the mainAxis.
          d10.overflows[0]
        ), d10.overflows];
      }).sort((a8, b5) => a8[1] - b5[1]);
      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter((d10) => d10[2].slice(
        0,
        // Aligned placements should not check their opposite crossAxis
        // side.
        getAlignment(d10[0]) ? 2 : 3
      ).every((v9) => v9 <= 0));
      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];
      if (resetPlacement !== placement) {
        return {
          data: {
            index: currentIndex + 1,
            overflows: allOverflows
          },
          reset: {
            placement: resetPlacement
          }
        };
      }
      return {};
    }
  };
};
var flip = function(options) {
  if (options === void 0) {
    options = {};
  }
  return {
    name: "flip",
    options,
    async fn(state) {
      var _middlewareData$arrow, _middlewareData$flip;
      const {
        placement,
        middlewareData,
        rects,
        initialPlacement,
        platform: platform2,
        elements
      } = state;
      const {
        mainAxis: checkMainAxis = true,
        crossAxis: checkCrossAxis = true,
        fallbackPlacements: specifiedFallbackPlacements,
        fallbackStrategy = "bestFit",
        fallbackAxisSideDirection = "none",
        flipAlignment = true,
        ...detectOverflowOptions
      } = evaluate(options, state);
      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {
        return {};
      }
      const side = getSide(placement);
      const initialSideAxis = getSideAxis(initialPlacement);
      const isBasePlacement = getSide(initialPlacement) === initialPlacement;
      const rtl = await (platform2.isRTL == null ? void 0 : platform2.isRTL(elements.floating));
      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));
      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== "none";
      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {
        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));
      }
      const placements2 = [initialPlacement, ...fallbackPlacements];
      const overflow = await detectOverflow(state, detectOverflowOptions);
      const overflows = [];
      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];
      if (checkMainAxis) {
        overflows.push(overflow[side]);
      }
      if (checkCrossAxis) {
        const sides2 = getAlignmentSides(placement, rects, rtl);
        overflows.push(overflow[sides2[0]], overflow[sides2[1]]);
      }
      overflowsData = [...overflowsData, {
        placement,
        overflows
      }];
      if (!overflows.every((side2) => side2 <= 0)) {
        var _middlewareData$flip2, _overflowsData$filter;
        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;
        const nextPlacement = placements2[nextIndex];
        if (nextPlacement) {
          return {
            data: {
              index: nextIndex,
              overflows: overflowsData
            },
            reset: {
              placement: nextPlacement
            }
          };
        }
        let resetPlacement = (_overflowsData$filter = overflowsData.filter((d10) => d10.overflows[0] <= 0).sort((a8, b5) => a8.overflows[1] - b5.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;
        if (!resetPlacement) {
          switch (fallbackStrategy) {
            case "bestFit": {
              var _overflowsData$filter2;
              const placement2 = (_overflowsData$filter2 = overflowsData.filter((d10) => {
                if (hasFallbackAxisSideDirection) {
                  const currentSideAxis = getSideAxis(d10.placement);
                  return currentSideAxis === initialSideAxis || // Create a bias to the `y` side axis due to horizontal
                  // reading directions favoring greater width.
                  currentSideAxis === "y";
                }
                return true;
              }).map((d10) => [d10.placement, d10.overflows.filter((overflow2) => overflow2 > 0).reduce((acc, overflow2) => acc + overflow2, 0)]).sort((a8, b5) => a8[1] - b5[1])[0]) == null ? void 0 : _overflowsData$filter2[0];
              if (placement2) {
                resetPlacement = placement2;
              }
              break;
            }
            case "initialPlacement":
              resetPlacement = initialPlacement;
              break;
          }
        }
        if (placement !== resetPlacement) {
          return {
            reset: {
              placement: resetPlacement
            }
          };
        }
      }
      return {};
    }
  };
};
function getSideOffsets(overflow, rect) {
  return {
    top: overflow.top - rect.height,
    right: overflow.right - rect.width,
    bottom: overflow.bottom - rect.height,
    left: overflow.left - rect.width
  };
}
function isAnySideFullyClipped(overflow) {
  return sides.some((side) => overflow[side] >= 0);
}
var hide = function(options) {
  if (options === void 0) {
    options = {};
  }
  return {
    name: "hide",
    options,
    async fn(state) {
      const {
        rects
      } = state;
      const {
        strategy = "referenceHidden",
        ...detectOverflowOptions
      } = evaluate(options, state);
      switch (strategy) {
        case "referenceHidden": {
          const overflow = await detectOverflow(state, {
            ...detectOverflowOptions,
            elementContext: "reference"
          });
          const offsets = getSideOffsets(overflow, rects.reference);
          return {
            data: {
              referenceHiddenOffsets: offsets,
              referenceHidden: isAnySideFullyClipped(offsets)
            }
          };
        }
        case "escaped": {
          const overflow = await detectOverflow(state, {
            ...detectOverflowOptions,
            altBoundary: true
          });
          const offsets = getSideOffsets(overflow, rects.floating);
          return {
            data: {
              escapedOffsets: offsets,
              escaped: isAnySideFullyClipped(offsets)
            }
          };
        }
        default: {
          return {};
        }
      }
    }
  };
};
async function convertValueToCoords(state, options) {
  const {
    placement,
    platform: platform2,
    elements
  } = state;
  const rtl = await (platform2.isRTL == null ? void 0 : platform2.isRTL(elements.floating));
  const side = getSide(placement);
  const alignment = getAlignment(placement);
  const isVertical = getSideAxis(placement) === "y";
  const mainAxisMulti = ["left", "top"].includes(side) ? -1 : 1;
  const crossAxisMulti = rtl && isVertical ? -1 : 1;
  const rawValue = evaluate(options, state);
  let {
    mainAxis,
    crossAxis,
    alignmentAxis
  } = typeof rawValue === "number" ? {
    mainAxis: rawValue,
    crossAxis: 0,
    alignmentAxis: null
  } : {
    mainAxis: rawValue.mainAxis || 0,
    crossAxis: rawValue.crossAxis || 0,
    alignmentAxis: rawValue.alignmentAxis
  };
  if (alignment && typeof alignmentAxis === "number") {
    crossAxis = alignment === "end" ? alignmentAxis * -1 : alignmentAxis;
  }
  return isVertical ? {
    x: crossAxis * crossAxisMulti,
    y: mainAxis * mainAxisMulti
  } : {
    x: mainAxis * mainAxisMulti,
    y: crossAxis * crossAxisMulti
  };
}
var offset = function(options) {
  if (options === void 0) {
    options = 0;
  }
  return {
    name: "offset",
    options,
    async fn(state) {
      var _middlewareData$offse, _middlewareData$arrow;
      const {
        x: x8,
        y: y10,
        placement,
        middlewareData
      } = state;
      const diffCoords = await convertValueToCoords(state, options);
      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {
        return {};
      }
      return {
        x: x8 + diffCoords.x,
        y: y10 + diffCoords.y,
        data: {
          ...diffCoords,
          placement
        }
      };
    }
  };
};
var shift = function(options) {
  if (options === void 0) {
    options = {};
  }
  return {
    name: "shift",
    options,
    async fn(state) {
      const {
        x: x8,
        y: y10,
        placement
      } = state;
      const {
        mainAxis: checkMainAxis = true,
        crossAxis: checkCrossAxis = false,
        limiter = {
          fn: (_ref) => {
            let {
              x: x9,
              y: y11
            } = _ref;
            return {
              x: x9,
              y: y11
            };
          }
        },
        ...detectOverflowOptions
      } = evaluate(options, state);
      const coords = {
        x: x8,
        y: y10
      };
      const overflow = await detectOverflow(state, detectOverflowOptions);
      const crossAxis = getSideAxis(getSide(placement));
      const mainAxis = getOppositeAxis(crossAxis);
      let mainAxisCoord = coords[mainAxis];
      let crossAxisCoord = coords[crossAxis];
      if (checkMainAxis) {
        const minSide = mainAxis === "y" ? "top" : "left";
        const maxSide = mainAxis === "y" ? "bottom" : "right";
        const min2 = mainAxisCoord + overflow[minSide];
        const max2 = mainAxisCoord - overflow[maxSide];
        mainAxisCoord = clamp(min2, mainAxisCoord, max2);
      }
      if (checkCrossAxis) {
        const minSide = crossAxis === "y" ? "top" : "left";
        const maxSide = crossAxis === "y" ? "bottom" : "right";
        const min2 = crossAxisCoord + overflow[minSide];
        const max2 = crossAxisCoord - overflow[maxSide];
        crossAxisCoord = clamp(min2, crossAxisCoord, max2);
      }
      const limitedCoords = limiter.fn({
        ...state,
        [mainAxis]: mainAxisCoord,
        [crossAxis]: crossAxisCoord
      });
      return {
        ...limitedCoords,
        data: {
          x: limitedCoords.x - x8,
          y: limitedCoords.y - y10,
          enabled: {
            [mainAxis]: checkMainAxis,
            [crossAxis]: checkCrossAxis
          }
        }
      };
    }
  };
};

// node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs
function hasWindow() {
  return typeof window !== "undefined";
}
function getNodeName(node) {
  if (isNode(node)) {
    return (node.nodeName || "").toLowerCase();
  }
  return "#document";
}
function getWindow(node) {
  var _node$ownerDocument;
  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;
}
function getDocumentElement(node) {
  var _ref;
  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;
}
function isNode(value) {
  if (!hasWindow()) {
    return false;
  }
  return value instanceof Node || value instanceof getWindow(value).Node;
}
function isElement(value) {
  if (!hasWindow()) {
    return false;
  }
  return value instanceof Element || value instanceof getWindow(value).Element;
}
function isHTMLElement(value) {
  if (!hasWindow()) {
    return false;
  }
  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;
}
function isShadowRoot(value) {
  if (!hasWindow() || typeof ShadowRoot === "undefined") {
    return false;
  }
  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;
}
function isOverflowElement(element) {
  const {
    overflow,
    overflowX,
    overflowY,
    display
  } = getComputedStyle2(element);
  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !["inline", "contents"].includes(display);
}
function isTableElement(element) {
  return ["table", "td", "th"].includes(getNodeName(element));
}
function isTopLayer(element) {
  return [":popover-open", ":modal"].some((selector) => {
    try {
      return element.matches(selector);
    } catch (e4) {
      return false;
    }
  });
}
function isContainingBlock(elementOrCss) {
  const webkit = isWebKit();
  const css = isElement(elementOrCss) ? getComputedStyle2(elementOrCss) : elementOrCss;
  return ["transform", "translate", "scale", "rotate", "perspective"].some((value) => css[value] ? css[value] !== "none" : false) || (css.containerType ? css.containerType !== "normal" : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== "none" : false) || !webkit && (css.filter ? css.filter !== "none" : false) || ["transform", "translate", "scale", "rotate", "perspective", "filter"].some((value) => (css.willChange || "").includes(value)) || ["paint", "layout", "strict", "content"].some((value) => (css.contain || "").includes(value));
}
function getContainingBlock(element) {
  let currentNode = getParentNode(element);
  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {
    if (isContainingBlock(currentNode)) {
      return currentNode;
    } else if (isTopLayer(currentNode)) {
      return null;
    }
    currentNode = getParentNode(currentNode);
  }
  return null;
}
function isWebKit() {
  if (typeof CSS === "undefined" || !CSS.supports) return false;
  return CSS.supports("-webkit-backdrop-filter", "none");
}
function isLastTraversableNode(node) {
  return ["html", "body", "#document"].includes(getNodeName(node));
}
function getComputedStyle2(element) {
  return getWindow(element).getComputedStyle(element);
}
function getNodeScroll(element) {
  if (isElement(element)) {
    return {
      scrollLeft: element.scrollLeft,
      scrollTop: element.scrollTop
    };
  }
  return {
    scrollLeft: element.scrollX,
    scrollTop: element.scrollY
  };
}
function getParentNode(node) {
  if (getNodeName(node) === "html") {
    return node;
  }
  const result = (
    // Step into the shadow DOM of the parent of a slotted node.
    node.assignedSlot || // DOM Element detected.
    node.parentNode || // ShadowRoot detected.
    isShadowRoot(node) && node.host || // Fallback.
    getDocumentElement(node)
  );
  return isShadowRoot(result) ? result.host : result;
}
function getNearestOverflowAncestor(node) {
  const parentNode = getParentNode(node);
  if (isLastTraversableNode(parentNode)) {
    return node.ownerDocument ? node.ownerDocument.body : node.body;
  }
  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {
    return parentNode;
  }
  return getNearestOverflowAncestor(parentNode);
}
function getOverflowAncestors(node, list, traverseIframes) {
  var _node$ownerDocument2;
  if (list === void 0) {
    list = [];
  }
  if (traverseIframes === void 0) {
    traverseIframes = true;
  }
  const scrollableAncestor = getNearestOverflowAncestor(node);
  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);
  const win = getWindow(scrollableAncestor);
  if (isBody) {
    const frameElement = getFrameElement(win);
    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);
  }
  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));
}
function getFrameElement(win) {
  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;
}

// node_modules/.pnpm/@floating-ui+dom@1.6.13/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs
function getCssDimensions(element) {
  const css = getComputedStyle2(element);
  let width = parseFloat(css.width) || 0;
  let height = parseFloat(css.height) || 0;
  const hasOffset = isHTMLElement(element);
  const offsetWidth = hasOffset ? element.offsetWidth : width;
  const offsetHeight = hasOffset ? element.offsetHeight : height;
  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;
  if (shouldFallback) {
    width = offsetWidth;
    height = offsetHeight;
  }
  return {
    width,
    height,
    $: shouldFallback
  };
}
function unwrapElement(element) {
  return !isElement(element) ? element.contextElement : element;
}
function getScale(element) {
  const domElement = unwrapElement(element);
  if (!isHTMLElement(domElement)) {
    return createCoords(1);
  }
  const rect = domElement.getBoundingClientRect();
  const {
    width,
    height,
    $: $8
  } = getCssDimensions(domElement);
  let x8 = ($8 ? round(rect.width) : rect.width) / width;
  let y10 = ($8 ? round(rect.height) : rect.height) / height;
  if (!x8 || !Number.isFinite(x8)) {
    x8 = 1;
  }
  if (!y10 || !Number.isFinite(y10)) {
    y10 = 1;
  }
  return {
    x: x8,
    y: y10
  };
}
var noOffsets = createCoords(0);
function getVisualOffsets(element) {
  const win = getWindow(element);
  if (!isWebKit() || !win.visualViewport) {
    return noOffsets;
  }
  return {
    x: win.visualViewport.offsetLeft,
    y: win.visualViewport.offsetTop
  };
}
function shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {
  if (isFixed === void 0) {
    isFixed = false;
  }
  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {
    return false;
  }
  return isFixed;
}
function getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {
  if (includeScale === void 0) {
    includeScale = false;
  }
  if (isFixedStrategy === void 0) {
    isFixedStrategy = false;
  }
  const clientRect = element.getBoundingClientRect();
  const domElement = unwrapElement(element);
  let scale = createCoords(1);
  if (includeScale) {
    if (offsetParent) {
      if (isElement(offsetParent)) {
        scale = getScale(offsetParent);
      }
    } else {
      scale = getScale(element);
    }
  }
  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);
  let x8 = (clientRect.left + visualOffsets.x) / scale.x;
  let y10 = (clientRect.top + visualOffsets.y) / scale.y;
  let width = clientRect.width / scale.x;
  let height = clientRect.height / scale.y;
  if (domElement) {
    const win = getWindow(domElement);
    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;
    let currentWin = win;
    let currentIFrame = getFrameElement(currentWin);
    while (currentIFrame && offsetParent && offsetWin !== currentWin) {
      const iframeScale = getScale(currentIFrame);
      const iframeRect = currentIFrame.getBoundingClientRect();
      const css = getComputedStyle2(currentIFrame);
      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;
      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;
      x8 *= iframeScale.x;
      y10 *= iframeScale.y;
      width *= iframeScale.x;
      height *= iframeScale.y;
      x8 += left;
      y10 += top;
      currentWin = getWindow(currentIFrame);
      currentIFrame = getFrameElement(currentWin);
    }
  }
  return rectToClientRect({
    width,
    height,
    x: x8,
    y: y10
  });
}
function getWindowScrollBarX(element, rect) {
  const leftScroll = getNodeScroll(element).scrollLeft;
  if (!rect) {
    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;
  }
  return rect.left + leftScroll;
}
function getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {
  if (ignoreScrollbarX === void 0) {
    ignoreScrollbarX = false;
  }
  const htmlRect = documentElement.getBoundingClientRect();
  const x8 = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 : (
    // RTL <body> scrollbar.
    getWindowScrollBarX(documentElement, htmlRect)
  ));
  const y10 = htmlRect.top + scroll.scrollTop;
  return {
    x: x8,
    y: y10
  };
}
function convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {
  let {
    elements,
    rect,
    offsetParent,
    strategy
  } = _ref;
  const isFixed = strategy === "fixed";
  const documentElement = getDocumentElement(offsetParent);
  const topLayer = elements ? isTopLayer(elements.floating) : false;
  if (offsetParent === documentElement || topLayer && isFixed) {
    return rect;
  }
  let scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  let scale = createCoords(1);
  const offsets = createCoords(0);
  const isOffsetParentAnElement = isHTMLElement(offsetParent);
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || isOverflowElement(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isHTMLElement(offsetParent)) {
      const offsetRect = getBoundingClientRect(offsetParent);
      scale = getScale(offsetParent);
      offsets.x = offsetRect.x + offsetParent.clientLeft;
      offsets.y = offsetRect.y + offsetParent.clientTop;
    }
  }
  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);
  return {
    width: rect.width * scale.x,
    height: rect.height * scale.y,
    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,
    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y
  };
}
function getClientRects(element) {
  return Array.from(element.getClientRects());
}
function getDocumentRect(element) {
  const html = getDocumentElement(element);
  const scroll = getNodeScroll(element);
  const body = element.ownerDocument.body;
  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);
  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);
  let x8 = -scroll.scrollLeft + getWindowScrollBarX(element);
  const y10 = -scroll.scrollTop;
  if (getComputedStyle2(body).direction === "rtl") {
    x8 += max(html.clientWidth, body.clientWidth) - width;
  }
  return {
    width,
    height,
    x: x8,
    y: y10
  };
}
function getViewportRect(element, strategy) {
  const win = getWindow(element);
  const html = getDocumentElement(element);
  const visualViewport = win.visualViewport;
  let width = html.clientWidth;
  let height = html.clientHeight;
  let x8 = 0;
  let y10 = 0;
  if (visualViewport) {
    width = visualViewport.width;
    height = visualViewport.height;
    const visualViewportBased = isWebKit();
    if (!visualViewportBased || visualViewportBased && strategy === "fixed") {
      x8 = visualViewport.offsetLeft;
      y10 = visualViewport.offsetTop;
    }
  }
  return {
    width,
    height,
    x: x8,
    y: y10
  };
}
function getInnerBoundingClientRect(element, strategy) {
  const clientRect = getBoundingClientRect(element, true, strategy === "fixed");
  const top = clientRect.top + element.clientTop;
  const left = clientRect.left + element.clientLeft;
  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);
  const width = element.clientWidth * scale.x;
  const height = element.clientHeight * scale.y;
  const x8 = left * scale.x;
  const y10 = top * scale.y;
  return {
    width,
    height,
    x: x8,
    y: y10
  };
}
function getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {
  let rect;
  if (clippingAncestor === "viewport") {
    rect = getViewportRect(element, strategy);
  } else if (clippingAncestor === "document") {
    rect = getDocumentRect(getDocumentElement(element));
  } else if (isElement(clippingAncestor)) {
    rect = getInnerBoundingClientRect(clippingAncestor, strategy);
  } else {
    const visualOffsets = getVisualOffsets(element);
    rect = {
      x: clippingAncestor.x - visualOffsets.x,
      y: clippingAncestor.y - visualOffsets.y,
      width: clippingAncestor.width,
      height: clippingAncestor.height
    };
  }
  return rectToClientRect(rect);
}
function hasFixedPositionAncestor(element, stopNode) {
  const parentNode = getParentNode(element);
  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {
    return false;
  }
  return getComputedStyle2(parentNode).position === "fixed" || hasFixedPositionAncestor(parentNode, stopNode);
}
function getClippingElementAncestors(element, cache) {
  const cachedResult = cache.get(element);
  if (cachedResult) {
    return cachedResult;
  }
  let result = getOverflowAncestors(element, [], false).filter((el) => isElement(el) && getNodeName(el) !== "body");
  let currentContainingBlockComputedStyle = null;
  const elementIsFixed = getComputedStyle2(element).position === "fixed";
  let currentNode = elementIsFixed ? getParentNode(element) : element;
  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {
    const computedStyle = getComputedStyle2(currentNode);
    const currentNodeIsContaining = isContainingBlock(currentNode);
    if (!currentNodeIsContaining && computedStyle.position === "fixed") {
      currentContainingBlockComputedStyle = null;
    }
    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === "static" && !!currentContainingBlockComputedStyle && ["absolute", "fixed"].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);
    if (shouldDropCurrentNode) {
      result = result.filter((ancestor) => ancestor !== currentNode);
    } else {
      currentContainingBlockComputedStyle = computedStyle;
    }
    currentNode = getParentNode(currentNode);
  }
  cache.set(element, result);
  return result;
}
function getClippingRect(_ref) {
  let {
    element,
    boundary,
    rootBoundary,
    strategy
  } = _ref;
  const elementClippingAncestors = boundary === "clippingAncestors" ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);
  const clippingAncestors = [...elementClippingAncestors, rootBoundary];
  const firstClippingAncestor = clippingAncestors[0];
  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {
    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);
    accRect.top = max(rect.top, accRect.top);
    accRect.right = min(rect.right, accRect.right);
    accRect.bottom = min(rect.bottom, accRect.bottom);
    accRect.left = max(rect.left, accRect.left);
    return accRect;
  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));
  return {
    width: clippingRect.right - clippingRect.left,
    height: clippingRect.bottom - clippingRect.top,
    x: clippingRect.left,
    y: clippingRect.top
  };
}
function getDimensions(element) {
  const {
    width,
    height
  } = getCssDimensions(element);
  return {
    width,
    height
  };
}
function getRectRelativeToOffsetParent(element, offsetParent, strategy) {
  const isOffsetParentAnElement = isHTMLElement(offsetParent);
  const documentElement = getDocumentElement(offsetParent);
  const isFixed = strategy === "fixed";
  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);
  let scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  const offsets = createCoords(0);
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || isOverflowElement(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isOffsetParentAnElement) {
      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);
      offsets.x = offsetRect.x + offsetParent.clientLeft;
      offsets.y = offsetRect.y + offsetParent.clientTop;
    } else if (documentElement) {
      offsets.x = getWindowScrollBarX(documentElement);
    }
  }
  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);
  const x8 = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;
  const y10 = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;
  return {
    x: x8,
    y: y10,
    width: rect.width,
    height: rect.height
  };
}
function isStaticPositioned(element) {
  return getComputedStyle2(element).position === "static";
}
function getTrueOffsetParent(element, polyfill) {
  if (!isHTMLElement(element) || getComputedStyle2(element).position === "fixed") {
    return null;
  }
  if (polyfill) {
    return polyfill(element);
  }
  let rawOffsetParent = element.offsetParent;
  if (getDocumentElement(element) === rawOffsetParent) {
    rawOffsetParent = rawOffsetParent.ownerDocument.body;
  }
  return rawOffsetParent;
}
function getOffsetParent(element, polyfill) {
  const win = getWindow(element);
  if (isTopLayer(element)) {
    return win;
  }
  if (!isHTMLElement(element)) {
    let svgOffsetParent = getParentNode(element);
    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {
      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {
        return svgOffsetParent;
      }
      svgOffsetParent = getParentNode(svgOffsetParent);
    }
    return win;
  }
  let offsetParent = getTrueOffsetParent(element, polyfill);
  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {
    offsetParent = getTrueOffsetParent(offsetParent, polyfill);
  }
  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {
    return win;
  }
  return offsetParent || getContainingBlock(element) || win;
}
var getElementRects = async function(data) {
  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;
  const getDimensionsFn = this.getDimensions;
  const floatingDimensions = await getDimensionsFn(data.floating);
  return {
    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),
    floating: {
      x: 0,
      y: 0,
      width: floatingDimensions.width,
      height: floatingDimensions.height
    }
  };
};
function isRTL(element) {
  return getComputedStyle2(element).direction === "rtl";
}
var platform = {
  convertOffsetParentRelativeRectToViewportRelativeRect,
  getDocumentElement,
  getClippingRect,
  getOffsetParent,
  getElementRects,
  getClientRects,
  getDimensions,
  getScale,
  isElement,
  isRTL
};
function rectsAreEqual(a8, b5) {
  return a8.x === b5.x && a8.y === b5.y && a8.width === b5.width && a8.height === b5.height;
}
function observeMove(element, onMove) {
  let io = null;
  let timeoutId;
  const root = getDocumentElement(element);
  function cleanup() {
    var _io;
    clearTimeout(timeoutId);
    (_io = io) == null || _io.disconnect();
    io = null;
  }
  function refresh(skip, threshold) {
    if (skip === void 0) {
      skip = false;
    }
    if (threshold === void 0) {
      threshold = 1;
    }
    cleanup();
    const elementRectForRootMargin = element.getBoundingClientRect();
    const {
      left,
      top,
      width,
      height
    } = elementRectForRootMargin;
    if (!skip) {
      onMove();
    }
    if (!width || !height) {
      return;
    }
    const insetTop = floor(top);
    const insetRight = floor(root.clientWidth - (left + width));
    const insetBottom = floor(root.clientHeight - (top + height));
    const insetLeft = floor(left);
    const rootMargin = -insetTop + "px " + -insetRight + "px " + -insetBottom + "px " + -insetLeft + "px";
    const options = {
      rootMargin,
      threshold: max(0, min(1, threshold)) || 1
    };
    let isFirstUpdate = true;
    function handleObserve(entries) {
      const ratio = entries[0].intersectionRatio;
      if (ratio !== threshold) {
        if (!isFirstUpdate) {
          return refresh();
        }
        if (!ratio) {
          timeoutId = setTimeout(() => {
            refresh(false, 1e-7);
          }, 1e3);
        } else {
          refresh(false, ratio);
        }
      }
      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {
        refresh();
      }
      isFirstUpdate = false;
    }
    try {
      io = new IntersectionObserver(handleObserve, {
        ...options,
        // Handle <iframe>s
        root: root.ownerDocument
      });
    } catch (e4) {
      io = new IntersectionObserver(handleObserve, options);
    }
    io.observe(element);
  }
  refresh(true);
  return cleanup;
}
function autoUpdate(reference, floating, update, options) {
  if (options === void 0) {
    options = {};
  }
  const {
    ancestorScroll = true,
    ancestorResize = true,
    elementResize = typeof ResizeObserver === "function",
    layoutShift = typeof IntersectionObserver === "function",
    animationFrame = false
  } = options;
  const referenceEl = unwrapElement(reference);
  const ancestors = ancestorScroll || ancestorResize ? [...referenceEl ? getOverflowAncestors(referenceEl) : [], ...getOverflowAncestors(floating)] : [];
  ancestors.forEach((ancestor) => {
    ancestorScroll && ancestor.addEventListener("scroll", update, {
      passive: true
    });
    ancestorResize && ancestor.addEventListener("resize", update);
  });
  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;
  let reobserveFrame = -1;
  let resizeObserver = null;
  if (elementResize) {
    resizeObserver = new ResizeObserver((_ref) => {
      let [firstEntry] = _ref;
      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {
        resizeObserver.unobserve(floating);
        cancelAnimationFrame(reobserveFrame);
        reobserveFrame = requestAnimationFrame(() => {
          var _resizeObserver;
          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);
        });
      }
      update();
    });
    if (referenceEl && !animationFrame) {
      resizeObserver.observe(referenceEl);
    }
    resizeObserver.observe(floating);
  }
  let frameId;
  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;
  if (animationFrame) {
    frameLoop();
  }
  function frameLoop() {
    const nextRefRect = getBoundingClientRect(reference);
    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {
      update();
    }
    prevRefRect = nextRefRect;
    frameId = requestAnimationFrame(frameLoop);
  }
  update();
  return () => {
    var _resizeObserver2;
    ancestors.forEach((ancestor) => {
      ancestorScroll && ancestor.removeEventListener("scroll", update);
      ancestorResize && ancestor.removeEventListener("resize", update);
    });
    cleanupIo == null || cleanupIo();
    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();
    resizeObserver = null;
    if (animationFrame) {
      cancelAnimationFrame(frameId);
    }
  };
}
var offset2 = offset;
var autoPlacement2 = autoPlacement;
var shift2 = shift;
var flip2 = flip;
var hide2 = hide;
var arrow2 = arrow;
var computePosition2 = (reference, floating, options) => {
  const cache = /* @__PURE__ */ new Map();
  const mergedOptions = {
    platform,
    ...options
  };
  const platformWithCache = {
    ...mergedOptions.platform,
    _c: cache
  };
  return computePosition(reference, floating, {
    ...mergedOptions,
    platform: platformWithCache
  });
};

// node_modules/.pnpm/@floating-ui+vue@1.1.6_vue@3.5.13_typescript@5.8.2_/node_modules/@floating-ui/vue/dist/floating-ui.vue.mjs
import { unref as unref2, computed as computed2, ref, shallowRef as shallowRef2, watch as watch2, getCurrentScope, onScopeDispose as onScopeDispose2, shallowReadonly } from "vue-demi";
function isComponentPublicInstance(target) {
  return target != null && typeof target === "object" && "$el" in target;
}
function unwrapElement2(target) {
  if (isComponentPublicInstance(target)) {
    const element = target.$el;
    return isNode(element) && getNodeName(element) === "#comment" ? null : element;
  }
  return target;
}
function toValue(source) {
  return typeof source === "function" ? source() : unref2(source);
}
function arrow3(options) {
  return {
    name: "arrow",
    options,
    fn(args) {
      const element = unwrapElement2(toValue(options.element));
      if (element == null) {
        return {};
      }
      return arrow2({
        element,
        padding: options.padding
      }).fn(args);
    }
  };
}
function getDPR(element) {
  if (typeof window === "undefined") {
    return 1;
  }
  const win = element.ownerDocument.defaultView || window;
  return win.devicePixelRatio || 1;
}
function roundByDPR(element, value) {
  const dpr = getDPR(element);
  return Math.round(value * dpr) / dpr;
}
function useFloating(reference, floating, options) {
  if (options === void 0) {
    options = {};
  }
  const whileElementsMountedOption = options.whileElementsMounted;
  const openOption = computed2(() => {
    var _toValue;
    return (_toValue = toValue(options.open)) != null ? _toValue : true;
  });
  const middlewareOption = computed2(() => toValue(options.middleware));
  const placementOption = computed2(() => {
    var _toValue2;
    return (_toValue2 = toValue(options.placement)) != null ? _toValue2 : "bottom";
  });
  const strategyOption = computed2(() => {
    var _toValue3;
    return (_toValue3 = toValue(options.strategy)) != null ? _toValue3 : "absolute";
  });
  const transformOption = computed2(() => {
    var _toValue4;
    return (_toValue4 = toValue(options.transform)) != null ? _toValue4 : true;
  });
  const referenceElement = computed2(() => unwrapElement2(reference.value));
  const floatingElement = computed2(() => unwrapElement2(floating.value));
  const x8 = ref(0);
  const y10 = ref(0);
  const strategy = ref(strategyOption.value);
  const placement = ref(placementOption.value);
  const middlewareData = shallowRef2({});
  const isPositioned = ref(false);
  const floatingStyles = computed2(() => {
    const initialStyles = {
      position: strategy.value,
      left: "0",
      top: "0"
    };
    if (!floatingElement.value) {
      return initialStyles;
    }
    const xVal = roundByDPR(floatingElement.value, x8.value);
    const yVal = roundByDPR(floatingElement.value, y10.value);
    if (transformOption.value) {
      return {
        ...initialStyles,
        transform: "translate(" + xVal + "px, " + yVal + "px)",
        ...getDPR(floatingElement.value) >= 1.5 && {
          willChange: "transform"
        }
      };
    }
    return {
      position: strategy.value,
      left: xVal + "px",
      top: yVal + "px"
    };
  });
  let whileElementsMountedCleanup;
  function update() {
    if (referenceElement.value == null || floatingElement.value == null) {
      return;
    }
    const open = openOption.value;
    computePosition2(referenceElement.value, floatingElement.value, {
      middleware: middlewareOption.value,
      placement: placementOption.value,
      strategy: strategyOption.value
    }).then((position) => {
      x8.value = position.x;
      y10.value = position.y;
      strategy.value = position.strategy;
      placement.value = position.placement;
      middlewareData.value = position.middlewareData;
      isPositioned.value = open !== false;
    });
  }
  function cleanup() {
    if (typeof whileElementsMountedCleanup === "function") {
      whileElementsMountedCleanup();
      whileElementsMountedCleanup = void 0;
    }
  }
  function attach() {
    cleanup();
    if (whileElementsMountedOption === void 0) {
      update();
      return;
    }
    if (referenceElement.value != null && floatingElement.value != null) {
      whileElementsMountedCleanup = whileElementsMountedOption(referenceElement.value, floatingElement.value, update);
      return;
    }
  }
  function reset() {
    if (!openOption.value) {
      isPositioned.value = false;
    }
  }
  watch2([middlewareOption, placementOption, strategyOption, openOption], update, {
    flush: "sync"
  });
  watch2([referenceElement, floatingElement], attach, {
    flush: "sync"
  });
  watch2(openOption, reset, {
    flush: "sync"
  });
  if (getCurrentScope()) {
    onScopeDispose2(cleanup);
  }
  return {
    x: shallowReadonly(x8),
    y: shallowReadonly(y10),
    strategy: shallowReadonly(strategy),
    placement: shallowReadonly(placement),
    middlewareData: shallowReadonly(middlewareData),
    isPositioned: shallowReadonly(isPositioned),
    floatingStyles,
    update
  };
}

// node_modules/.pnpm/@headlessui-float+vue@0.15.0_@headlessui+vue@1.7.23_vue@3.5.13_typescript@5.8.2___vue@3.5.13_typescript@5.8.2_/node_modules/@headlessui-float/vue/dist/headlessui-float.mjs
var he3 = Object.defineProperty;
var pe5 = (e4, r6, o11) => r6 in e4 ? he3(e4, r6, { enumerable: true, configurable: true, writable: true, value: o11 }) : e4[r6] = o11;
var G4 = (e4, r6, o11) => pe5(e4, typeof r6 != "symbol" ? r6 + "" : r6, o11);
function B4(e4) {
  return e4 == null || e4.value == null ? null : e4.value instanceof Node ? e4.value : "$el" in e4.value && e4.value.$el ? B4(h5(e4.value.$el)) : "getBoundingClientRect" in e4.value ? e4.value : null;
}
function He(e4) {
  return typeof window > "u" ? 1 : (e4.ownerDocument.defaultView || window).devicePixelRatio || 1;
}
function K10(e4, r6) {
  const o11 = He(e4);
  return Math.round(r6 * o11) / o11;
}
function L8(e4) {
  return e4.reduce((r6, o11) => o11.type === ye3 ? r6.concat(L8(o11.children)) : r6.concat(o11), []);
}
function I6(e4) {
  return e4 == null ? false : typeof e4.type == "string" || typeof e4.type == "object" || typeof e4.type == "function";
}
function Q3(e4) {
  return e4 = we(e4), e4 && (e4 == null ? void 0 : e4.nodeType) !== Node.COMMENT_NODE;
}
var Ie3 = class {
  constructor() {
    G4(this, "current", this.detect());
  }
  set(r6) {
    this.current !== r6 && (this.current = r6);
  }
  reset() {
    this.set(this.detect());
  }
  get isServer() {
    return this.current === "server";
  }
  get isClient() {
    return this.current === "client";
  }
  detect() {
    return typeof window > "u" || typeof document > "u" ? "server" : "client";
  }
};
var X2 = new Ie3();
function ne2(e4) {
  if (X2.isServer) return null;
  if (e4 instanceof Node) return e4.ownerDocument;
  if (e4 && Object.prototype.hasOwnProperty.call(e4, "value")) {
    const r6 = B4(e4);
    if (r6) return r6.ownerDocument;
  }
  return document;
}
function Y3(e4, r6) {
  !r6.vueTransition && (r6.transitionName || r6.transitionType) && console.warn(`[headlessui-float]: <${e4} /> pass "transition-name" or "transition-type" prop, must be set "vue-transition" prop.`);
}
function Ue(e4, r6, o11, a8, t8) {
  P5([
    () => t8.offset,
    () => t8.flip,
    () => t8.shift,
    () => t8.autoPlacement,
    () => t8.arrow,
    () => t8.hide,
    () => t8.middleware
  ], () => {
    const i13 = [];
    (typeof t8.offset == "number" || typeof t8.offset == "object" || typeof t8.offset == "function") && i13.push(offset2(t8.offset)), (t8.flip === true || typeof t8.flip == "number" || typeof t8.flip == "object") && i13.push(flip2({
      padding: typeof t8.flip == "number" ? t8.flip : void 0,
      ...typeof t8.flip == "object" ? t8.flip : {}
    })), (t8.shift === true || typeof t8.shift == "number" || typeof t8.shift == "object") && i13.push(shift2({
      padding: typeof t8.shift == "number" ? t8.shift : void 0,
      ...typeof t8.shift == "object" ? t8.shift : {}
    })), (t8.autoPlacement === true || typeof t8.autoPlacement == "object") && i13.push(autoPlacement2(
      typeof t8.autoPlacement == "object" ? t8.autoPlacement : void 0
    )), i13.push(...typeof t8.middleware == "function" ? t8.middleware({
      referenceEl: r6,
      floatingEl: o11
    }) : t8.middleware || []), (t8.arrow === true || typeof t8.arrow == "number") && i13.push(arrow3({
      element: a8,
      padding: t8.arrow === true ? 0 : t8.arrow
    })), (t8.hide === true || typeof t8.hide == "object" || Array.isArray(t8.hide)) && (Array.isArray(t8.hide) ? t8.hide : [t8.hide]).forEach((u10) => {
      i13.push(hide2(
        typeof u10 == "object" ? u10 : void 0
      ));
    }), e4.value = i13;
  }, { immediate: true });
}
function ze2(e4, r6, o11) {
  let a8 = () => {
  };
  U6(() => {
    if (e4 && X2.isClient && typeof ResizeObserver < "u" && r6.value && r6.value instanceof Element) {
      const t8 = new ResizeObserver(([i13]) => {
        o11.value = i13.borderBoxSize.reduce((u10, { inlineSize: s14 }) => u10 + s14, 0);
      });
      t8.observe(r6.value), a8 = () => {
        t8.disconnect(), o11.value = null;
      };
    }
  }), be3(() => {
    a8();
  });
}
var st = [
  "origin-bottom",
  "origin-top",
  "origin-right",
  "origin-left",
  "origin-bottom-left",
  "origin-bottom-right",
  "origin-top-left",
  "origin-top-right"
];
var De2 = (e4) => {
  switch (e4) {
    case "top":
      return "origin-bottom";
    case "bottom":
      return "origin-top";
    case "left":
      return "origin-right";
    case "right":
      return "origin-left";
    case "top-start":
    case "right-end":
      return "origin-bottom-left";
    case "top-end":
    case "left-end":
      return "origin-bottom-right";
    case "right-start":
    case "bottom-start":
      return "origin-top-left";
    case "left-start":
    case "bottom-end":
      return "origin-top-right";
    default:
      return "origin-center";
  }
};
var ut2 = [
  "origin-bottom",
  "origin-top",
  "ltr:origin-right rtl:origin-left",
  "ltr:origin-left rtl:origin-right",
  "ltr:origin-bottom-left rtl:origin-bottom-right",
  "ltr:origin-bottom-right rtl:origin-bottom-left",
  "ltr:origin-top-left rtl:origin-top-right",
  "ltr:origin-top-right rtl:origin-top-left"
];
var ft = (e4) => {
  switch (e4) {
    case "top":
      return "origin-bottom";
    case "bottom":
      return "origin-top";
    case "left":
      return "ltr:origin-right rtl:origin-left";
    case "right":
      return "ltr:origin-left rtl:origin-right";
    case "top-start":
    case "right-end":
      return "ltr:origin-bottom-left rtl:origin-bottom-right";
    case "top-end":
    case "left-end":
      return "ltr:origin-bottom-right rtl:origin-bottom-left";
    case "right-start":
    case "bottom-start":
      return "ltr:origin-top-left rtl:origin-top-right";
    case "left-start":
    case "bottom-end":
      return "ltr:origin-top-right rtl:origin-top-left";
    default:
      return "origin-center";
  }
};
function Me2(e4, r6) {
  const o11 = O8(() => {
    if (typeof e4.originClass == "function")
      return e4.originClass(r6.value);
    if (typeof e4.originClass == "string")
      return e4.originClass;
    if (e4.tailwindcssOriginClass)
      return De2(r6.value);
  }), a8 = O8(
    () => e4.enter || o11.value ? `${e4.enter || ""} ${o11.value || ""}` : void 0
  ), t8 = O8(
    () => e4.leave || o11.value ? `${e4.leave || ""} ${o11.value || ""}` : void 0
  );
  return { originClassRef: o11, enterActiveClassRef: a8, leaveActiveClassRef: t8 };
}
function re2(e4, r6, ...o11) {
  if (e4 in r6) {
    const t8 = r6[e4];
    return typeof t8 == "function" ? t8(...o11) : t8;
  }
  const a8 = new Error(
    `Tried to handle "${e4}" but there is no handler defined. Only defined handlers are: ${Object.keys(
      r6
    ).map((t8) => `"${t8}"`).join(", ")}.`
  );
  throw Error.captureStackTrace && Error.captureStackTrace(a8, re2), a8;
}
var Z2 = [
  "[contentEditable=true]",
  "[tabindex]",
  "a[href]",
  "area[href]",
  "button:not([disabled])",
  "iframe",
  "input:not([disabled])",
  "select:not([disabled])",
  "textarea:not([disabled])"
].map(
  // TODO: Remove this once JSDOM fixes the issue where an element that is
  // "hidden" can be the document.activeElement, because this is not possible
  // in real browsers.
  false ? (e4) => `${e4}:not([tabindex='-1']):not([style*='display: none'])` : (e4) => `${e4}:not([tabindex='-1'])`
).join(",");
var oe2 = ((e4) => (e4[e4.Strict = 0] = "Strict", e4[e4.Loose = 1] = "Loose", e4))(oe2 || {});
function $e2(e4, r6 = 0) {
  var o11;
  return e4 === ((o11 = ne2(e4)) == null ? void 0 : o11.body) ? false : re2(r6, {
    0() {
      return e4.matches(Z2);
    },
    1() {
      let a8 = e4;
      for (; a8 !== null; ) {
        if (a8.matches(Z2)) return true;
        a8 = a8.parentElement;
      }
      return false;
    }
  });
}
function C7(e4, r6, o11) {
  X2.isServer || te5((a8) => {
    document.addEventListener(e4, r6, o11), a8(() => document.removeEventListener(e4, r6, o11));
  });
}
function ke2(e4, r6, o11 = O8(() => true)) {
  function a8(i13, u10) {
    if (!o11.value || i13.defaultPrevented) return;
    const s14 = u10(i13);
    if (s14 === null || !s14.getRootNode().contains(s14)) return;
    const d10 = function f8(c7) {
      return typeof c7 == "function" ? f8(c7()) : Array.isArray(c7) || c7 instanceof Set ? c7 : [c7];
    }(e4);
    for (const f8 of d10) {
      if (f8 === null) continue;
      const c7 = f8 instanceof HTMLElement ? f8 : B4(f8);
      if (c7 != null && c7.contains(s14) || i13.composed && i13.composedPath().includes(c7))
        return;
    }
    return (
      // This check alllows us to know whether or not we clicked on a "focusable" element like a
      // button or an input. This is a backwards compatibility check so that you can open a <Menu
      // /> and click on another <Menu /> which should close Menu A and open Menu B. We might
      // revisit that so that you will require 2 clicks instead.
      !$e2(s14, oe2.Loose) && // This could be improved, but the `Combobox.Button` adds tabIndex={-1} to make it
      // unfocusable via the keyboard so that tabbing to the next item from the input doesn't
      // first go to the button.
      s14.tabIndex !== -1 && i13.preventDefault(), r6(i13, s14)
    );
  }
  const t8 = h5(null);
  C7("mousedown", (i13) => {
    var u10, s14;
    o11.value && (t8.value = ((s14 = (u10 = i13.composedPath) == null ? void 0 : u10.call(i13)) == null ? void 0 : s14[0]) || i13.target);
  }, true), C7(
    "click",
    (i13) => {
      t8.value && (a8(i13, () => t8.value), t8.value = null);
    },
    // We will use the `capture` phase so that layers in between with `event.stopPropagation()`
    // don't "cancel" this outside click check. E.g.: A `Menu` inside a `DialogPanel` if the `Menu`
    // is open, and you click outside of it in the `DialogPanel` the `Menu` should close. However,
    // the `DialogPanel` has a `onClick(e) { e.stopPropagation() }` which would cancel this.
    true
  ), C7("blur", (i13) => a8(
    i13,
    () => window.document.activeElement instanceof HTMLIFrameElement ? window.document.activeElement : null
  ), true);
}
var ae3 = Symbol("ReferenceContext");
var ie4 = Symbol("FloatingContext");
var le5 = Symbol("ArrowContext");
function se3(e4) {
  const r6 = W6(ae3, null);
  if (r6 === null) {
    const o11 = new Error(`<${e4} /> must be in the <Float /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(o11, se3), o11;
  }
  return r6;
}
function ue4(e4) {
  const r6 = W6(ie4, null);
  if (r6 === null) {
    const o11 = new Error(`<${e4} /> must be in the <Float /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(o11, ue4), o11;
  }
  return r6;
}
function fe3(e4) {
  const r6 = W6(le5, null);
  if (r6 === null) {
    const o11 = new Error(`<${e4} /> must be in the <Float /> component.`);
    throw Error.captureStackTrace && Error.captureStackTrace(o11, fe3), o11;
  }
  return r6;
}
var n8 = {
  as: {
    type: [String, Function],
    default: "template"
  },
  floatingAs: {
    type: [String, Function],
    default: "div"
  },
  show: {
    type: Boolean,
    default: null
  },
  placement: {
    type: String,
    default: "bottom-start"
  },
  strategy: {
    type: String,
    default: "absolute"
  },
  offset: [Number, Function, Object],
  shift: {
    type: [Boolean, Number, Object],
    default: false
  },
  flip: {
    type: [Boolean, Number, Object],
    default: false
  },
  arrow: {
    type: [Boolean, Number],
    default: false
  },
  autoPlacement: {
    type: [Boolean, Object],
    default: false
  },
  hide: {
    type: [Boolean, Object, Array],
    default: false
  },
  referenceHiddenClass: String,
  escapedClass: String,
  autoUpdate: {
    type: [Boolean, Object],
    default: true
  },
  zIndex: {
    type: [Number, String],
    default: 9999
  },
  vueTransition: {
    type: Boolean,
    default: false
  },
  transitionName: String,
  transitionType: String,
  enter: String,
  enterFrom: String,
  enterTo: String,
  leave: String,
  leaveFrom: String,
  leaveTo: String,
  originClass: [String, Function],
  tailwindcssOriginClass: {
    type: Boolean,
    default: false
  },
  portal: {
    type: Boolean,
    default: false
  },
  transform: {
    type: Boolean,
    default: false
  },
  adaptiveWidth: {
    type: [Boolean, Object],
    default: false
  },
  composable: {
    type: Boolean,
    default: false
  },
  dialog: {
    type: Boolean,
    default: false
  },
  middleware: {
    type: [Array, Function],
    default: () => []
  }
};
function ce3(e4, r6, o11, a8) {
  const { referenceRef: t8 } = a8, i13 = r6, u10 = N12(o11, {
    ref: t8
  }), s14 = k8(
    e4,
    i13.as === "template" ? u10 : {}
  );
  return i13.as === "template" ? s14 : typeof i13.as == "string" ? b4(i13.as, u10, [s14]) : b4(i13.as, u10, () => [s14]);
}
function _5(e4, r6, o11, a8) {
  const { floatingRef: t8, props: i13, mounted: u10, show: s14, referenceHidden: d10, escaped: f8, placement: c7, floatingStyles: p10, referenceElWidth: y10, updateFloating: T8 } = a8, l7 = N12(
    { ...i13, as: i13.floatingAs },
    r6
  ), { enterActiveClassRef: g6, leaveActiveClassRef: m12 } = Me2(l7, c7), F6 = {
    show: u10.value ? l7.show : false,
    enter: g6.value,
    enterFrom: l7.enterFrom,
    enterTo: l7.enterTo,
    leave: m12.value,
    leaveFrom: l7.leaveFrom,
    leaveTo: l7.leaveTo,
    onBeforeEnter() {
      s14.value = true;
    },
    onAfterLeave() {
      s14.value = false;
    }
  }, x8 = {
    name: l7.transitionName,
    type: l7.transitionType,
    appear: true,
    ...l7.transitionName ? {} : {
      enterActiveClass: g6.value,
      enterFromClass: l7.enterFrom,
      enterToClass: l7.enterTo,
      leaveActiveClass: m12.value,
      leaveFromClass: l7.leaveFrom,
      leaveToClass: l7.leaveTo
    },
    onBeforeEnter() {
      s14.value = true;
    },
    onAfterLeave() {
      s14.value = false;
    }
  }, E10 = {
    class: [
      d10.value ? l7.referenceHiddenClass : void 0,
      f8.value ? l7.escapedClass : void 0
    ].filter((v9) => !!v9).join(" "),
    style: {
      ...p10.value,
      zIndex: l7.zIndex
    }
  };
  if (l7.adaptiveWidth && typeof y10.value == "number") {
    const v9 = {
      attribute: "width",
      ...typeof l7.adaptiveWidth == "object" ? l7.adaptiveWidth : {}
    };
    E10.style[v9.attribute] = `${y10.value}px`;
  }
  function z8(v9) {
    return l7.portal ? u10.value ? b4($2, () => v9) : M4() : v9;
  }
  function D4(v9) {
    const A7 = N12(
      E10,
      o11,
      l7.dialog ? {} : { ref: t8 }
    );
    return l7.as === "template" ? v9 : typeof l7.as == "string" ? b4(l7.as, A7, v9) : b4(l7.as, A7, () => v9);
  }
  function V3() {
    function v9() {
      var H8;
      const A7 = l7.as === "template" ? N12(
        E10,
        o11,
        l7.dialog ? {} : { ref: t8 }
      ) : null, j10 = k8(e4, A7);
      return ((H8 = e4.props) == null ? void 0 : H8.unmount) === false ? (T8(), j10) : l7.vueTransition && l7.show === false ? M4() : j10;
    }
    return u10.value ? l7.vueTransition ? b4(Te3, {
      ...l7.dialog ? { ref: t8 } : {},
      ...x8
    }, v9) : b4(l7.transitionChild ? he2 : Se3, {
      key: `placement-${c7.value}`,
      ...l7.dialog ? { ref: t8 } : {},
      as: "template",
      ...F6
    }, v9) : M4();
  }
  return z8(
    D4(
      V3()
    )
  );
}
function de3(e4, r6, o11, a8, t8) {
  const i13 = h5(false), u10 = J6(a8, "placement"), s14 = J6(a8, "strategy"), d10 = Fe2({}), f8 = h5(void 0), c7 = h5(void 0), p10 = h5(null), y10 = h5(void 0), T8 = h5(void 0), l7 = O8(() => B4(r6)), g6 = O8(() => B4(o11)), m12 = O8(
    () => Q3(l7) && Q3(g6)
  ), { placement: F6, middlewareData: x8, isPositioned: E10, floatingStyles: z8, update: D4 } = useFloating(l7, g6, {
    placement: u10,
    strategy: s14,
    middleware: d10,
    // If enable dialog mode, then set `transform` to false.
    transform: a8.dialog ? false : a8.transform,
    // Fix transition not smooth bug when dialog mode enabled.
    whileElementsMounted: a8.dialog ? () => () => {
    } : void 0
  }), V3 = h5(null);
  U6(() => {
    i13.value = true;
  }), P5(e4, (w10, S4) => {
    w10 && !S4 ? t8("show") : !w10 && S4 && t8("hide");
  }, { immediate: true });
  function v9() {
    m12.value && (D4(), t8("update"));
  }
  P5([u10, s14, d10], v9, { flush: "sync" }), Ue(
    d10,
    l7,
    g6,
    p10,
    a8
  ), P5([x8, () => a8.hide, E10], () => {
    var w10, S4;
    (a8.hide === true || typeof a8.hide == "object" || Array.isArray(a8.hide)) && (f8.value = ((w10 = x8.value.hide) == null ? void 0 : w10.referenceHidden) || !E10.value, c7.value = (S4 = x8.value.hide) == null ? void 0 : S4.escaped);
  }), P5(x8, () => {
    const w10 = x8.value.arrow;
    y10.value = w10 == null ? void 0 : w10.x, T8.value = w10 == null ? void 0 : w10.y;
  }), ze2(!!a8.adaptiveWidth, l7, V3), P5([e4, m12], async (w10, S4, ve3) => {
    if (await Ce(), e4.value && m12.value && a8.autoUpdate) {
      const ge4 = autoUpdate(
        l7.value,
        g6.value,
        v9,
        typeof a8.autoUpdate == "object" ? a8.autoUpdate : void 0
      );
      ve3(ge4);
    }
  }, { flush: "post", immediate: true });
  const A7 = h5(true);
  P5(l7, () => {
    !(l7.value instanceof Element) && m12.value && A7.value && (A7.value = false, window.requestAnimationFrame(() => {
      A7.value = true, v9();
    }));
  }, { flush: "sync" });
  const j10 = {
    referenceRef: r6,
    placement: F6
  }, H8 = {
    floatingRef: o11,
    props: a8,
    mounted: i13,
    show: e4,
    referenceHidden: f8,
    escaped: c7,
    placement: F6,
    floatingStyles: z8,
    referenceElWidth: V3,
    updateFloating: v9
  }, q4 = {
    ref: p10,
    placement: F6,
    x: y10,
    y: T8
  };
  return $7(le5, q4), { referenceApi: j10, floatingApi: H8, arrowApi: q4, placement: F6, referenceEl: l7, floatingEl: g6, middlewareData: x8, update: v9 };
}
var We = R10({
  name: "Float",
  inheritAttrs: false,
  props: n8,
  emits: ["show", "hide", "update"],
  setup(e4, { emit: r6, slots: o11, attrs: a8 }) {
    Y3("Float", e4);
    const t8 = h5(e4.show ?? false), i13 = h5(null), u10 = h5(null), {
      referenceApi: s14,
      floatingApi: d10,
      placement: f8
    } = de3(t8, i13, u10, e4, r6);
    function c7(y10) {
      return e4.as === "template" ? y10 : typeof e4.as == "string" ? b4(e4.as, a8, y10) : b4(e4.as, a8, () => y10);
    }
    const p10 = {
      placement: f8.value
    };
    return e4.composable || e4.dialog ? ($7(ae3, s14), $7(ie4, d10), () => {
      if (o11.default)
        return c7(o11.default(p10));
    }) : () => {
      if (!o11.default) return;
      const [y10, T8] = L8(o11.default(p10)).filter(I6);
      if (!I6(y10))
        return;
      const l7 = ce3(
        y10,
        { as: "template" },
        {},
        s14
      ), g6 = _5(
        T8,
        { as: e4.floatingAs },
        {},
        d10
      );
      return c7([
        l7,
        g6
      ]);
    };
  }
});
var Le = We;
var Xe = {
  as: n8.as
};
var Ye2 = R10({
  name: "FloatReference",
  inheritAttrs: false,
  props: Xe,
  setup(e4, { slots: r6, attrs: o11 }) {
    const a8 = se3("FloatReference"), { placement: t8 } = a8;
    return () => {
      if (!r6.default) return;
      const i13 = {
        placement: t8.value
      };
      return ce3(
        r6.default(i13)[0],
        e4,
        o11,
        a8
      );
    };
  }
});
var ct = Ye2;
var ee4 = {
  as: n8.floatingAs,
  vueTransition: n8.vueTransition,
  transitionName: n8.transitionName,
  transitionType: n8.transitionType,
  enter: n8.enter,
  enterFrom: n8.enterFrom,
  enterTo: n8.enterTo,
  leave: n8.leave,
  leaveFrom: n8.leaveFrom,
  leaveTo: n8.leaveTo,
  originClass: n8.originClass,
  tailwindcssOriginClass: n8.tailwindcssOriginClass,
  transitionChild: {
    type: Boolean,
    default: false
  }
};
var _e2 = R10({
  name: "FloatContent",
  inheritAttrs: false,
  props: ee4,
  setup(e4, { slots: r6, attrs: o11 }) {
    Y3("FloatContent", e4);
    const a8 = ue4("FloatContent"), { placement: t8 } = a8;
    return () => {
      if (!r6.default) return;
      const i13 = {
        placement: t8.value
      }, u10 = Object.entries(e4).reduce((s14, [d10, f8]) => {
        const c7 = ee4;
        return (typeof c7[d10] == "object" && f8 === c7[d10].default || f8 === void 0) && delete s14[d10], s14;
      }, { ...e4 });
      return _5(
        r6.default(i13)[0],
        u10,
        o11,
        a8
      );
    };
  }
});
var dt = _e2;
var qe = {
  as: {
    ...n8.as,
    default: "div"
  },
  offset: {
    type: Number,
    default: 4
  }
};
var Ge3 = R10({
  name: "FloatArrow",
  props: qe,
  setup(e4, { slots: r6, attrs: o11 }) {
    const { ref: a8, placement: t8, x: i13, y: u10 } = fe3("FloatArrow");
    return () => {
      var f8;
      const s14 = {
        top: "bottom",
        right: "left",
        bottom: "top",
        left: "right"
      }[t8.value.split("-")[0]], d10 = {
        left: a8.value && typeof i13.value == "number" ? `${K10(a8.value, i13.value)}px` : void 0,
        top: a8.value && typeof u10.value == "number" ? `${K10(a8.value, u10.value)}px` : void 0,
        right: void 0,
        bottom: void 0,
        [s14]: `${e4.offset * -1}px`
      };
      if (e4.as === "template") {
        const c7 = {
          placement: t8.value
        }, p10 = (f8 = r6.default) == null ? void 0 : f8.call(r6, c7)[0];
        return !p10 || !I6(p10) ? void 0 : k8(p10, { ref: a8, style: d10 });
      }
      return b4(e4.as, N12(o11, { ref: a8, style: d10 }));
    };
  }
});
var mt = Ge3;
var Je = {
  as: n8.as,
  show: n8.show,
  placement: n8.placement,
  strategy: n8.strategy,
  offset: n8.offset,
  shift: n8.shift,
  flip: n8.flip,
  arrow: n8.arrow,
  autoPlacement: n8.autoPlacement,
  autoUpdate: n8.autoUpdate,
  zIndex: n8.zIndex,
  vueTransition: n8.vueTransition,
  transitionName: n8.transitionName,
  transitionType: n8.transitionType,
  enter: n8.enter,
  enterFrom: n8.enterFrom,
  enterTo: n8.enterTo,
  leave: n8.leave,
  leaveFrom: n8.leaveFrom,
  leaveTo: n8.leaveTo,
  originClass: n8.originClass,
  tailwindcssOriginClass: n8.tailwindcssOriginClass,
  portal: n8.portal,
  transform: n8.transform,
  middleware: n8.middleware
};
var Ke = R10({
  name: "FloatVirtual",
  inheritAttrs: false,
  props: Je,
  emits: ["initial", "show", "hide", "update"],
  setup(e4, { emit: r6, slots: o11, attrs: a8 }) {
    Y3("FloatVirtual", e4);
    const t8 = h5(e4.show ?? false), i13 = h5({
      getBoundingClientRect() {
        return {
          x: 0,
          y: 0,
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          width: 0,
          height: 0
        };
      }
    }), u10 = h5(null), {
      floatingApi: s14,
      placement: d10
    } = de3(t8, i13, u10, e4, r6);
    P5(() => e4.show, () => {
      t8.value = e4.show ?? false;
    });
    function f8() {
      t8.value = false;
    }
    return r6("initial", {
      show: t8,
      placement: d10,
      reference: i13,
      floating: u10
    }), () => {
      if (!o11.default) return;
      const c7 = {
        placement: d10.value,
        close: f8
      }, [p10] = L8(o11.default(c7)).filter(I6);
      return _5(
        p10,
        {
          as: e4.as,
          show: t8.value
        },
        a8,
        s14
      );
    };
  }
});
var me4 = Ke;
var Qe = {
  as: n8.as,
  placement: n8.placement,
  strategy: n8.strategy,
  offset: n8.offset,
  shift: n8.shift,
  flip: {
    ...n8.flip,
    default: true
  },
  arrow: n8.arrow,
  autoPlacement: n8.autoPlacement,
  autoUpdate: n8.autoUpdate,
  zIndex: n8.zIndex,
  vueTransition: n8.vueTransition,
  transitionName: n8.transitionName,
  transitionType: n8.transitionType,
  enter: n8.enter,
  enterFrom: n8.enterFrom,
  enterTo: n8.enterTo,
  leave: n8.leave,
  leaveFrom: n8.leaveFrom,
  leaveTo: n8.leaveTo,
  originClass: n8.originClass,
  tailwindcssOriginClass: n8.tailwindcssOriginClass,
  transform: n8.transform,
  middleware: n8.middleware
};
var Ze = R10({
  name: "FloatContextMenu",
  inheritAttrs: false,
  props: Qe,
  emits: ["show", "hide", "update"],
  setup(e4, { emit: r6, slots: o11, attrs: a8 }) {
    const t8 = h5(false);
    function i13({ show: u10, reference: s14, floating: d10 }) {
      C7("contextmenu", (f8) => {
        f8.preventDefault(), s14.value = {
          getBoundingClientRect() {
            return {
              width: 0,
              height: 0,
              x: f8.clientX,
              y: f8.clientY,
              top: f8.clientY,
              left: f8.clientX,
              right: f8.clientX,
              bottom: f8.clientY
            };
          }
        }, u10.value = true;
      }), ke2(d10, () => {
        u10.value = false;
      }, O8(() => u10.value));
    }
    return U6(() => {
      t8.value = true;
    }), () => {
      if (o11.default && t8.value)
        return b4(me4, {
          ...e4,
          ...a8,
          portal: true,
          onInitial: i13,
          onShow: () => r6("show"),
          onHide: () => r6("hide"),
          onUpdate: () => r6("update")
        }, o11.default);
    };
  }
});
var vt = Ze;
var et = {
  as: n8.as,
  placement: n8.placement,
  strategy: n8.strategy,
  offset: n8.offset,
  shift: n8.shift,
  flip: n8.flip,
  arrow: n8.arrow,
  autoPlacement: n8.autoPlacement,
  autoUpdate: n8.autoUpdate,
  zIndex: n8.zIndex,
  vueTransition: n8.vueTransition,
  transitionName: n8.transitionName,
  transitionType: n8.transitionType,
  enter: n8.enter,
  enterFrom: n8.enterFrom,
  enterTo: n8.enterTo,
  leave: n8.leave,
  leaveFrom: n8.leaveFrom,
  leaveTo: n8.leaveTo,
  originClass: n8.originClass,
  tailwindcssOriginClass: n8.tailwindcssOriginClass,
  transform: n8.transform,
  middleware: n8.middleware,
  globalHideCursor: {
    type: Boolean,
    default: true
  }
};
var tt = R10({
  name: "FloatCursor",
  inheritAttrs: false,
  props: et,
  emits: ["show", "hide", "update"],
  setup({ globalHideCursor: e4, ...r6 }, { emit: o11, slots: a8, attrs: t8 }) {
    const i13 = h5(false);
    function u10({ show: s14, reference: d10, floating: f8 }) {
      function c7() {
        s14.value = true;
      }
      function p10() {
        s14.value = false;
      }
      function y10(m12) {
        d10.value = {
          getBoundingClientRect() {
            return {
              width: 0,
              height: 0,
              x: m12.clientX,
              y: m12.clientY,
              top: m12.clientY,
              left: m12.clientX,
              right: m12.clientX,
              bottom: m12.clientY
            };
          }
        };
      }
      function T8(m12) {
        c7(), y10(m12);
      }
      function l7(m12) {
        c7(), y10(m12.touches[0]);
      }
      const g6 = ne2(f8);
      g6 && (te5((m12) => {
        if (e4 && !g6.getElementById("headlesui-float-cursor-style")) {
          const F6 = g6.createElement("style");
          (g6.head || g6.getElementsByTagName("head")[0]).appendChild(F6), F6.id = "headlesui-float-cursor-style", F6.appendChild(g6.createTextNode([
            "*, *::before, *::after {",
            "  cursor: none !important;",
            "}",
            ".headlesui-float-cursor-root {",
            "  pointer-events: none !important;",
            "}"
          ].join(`
`))), m12(() => {
            var E10;
            return (E10 = g6.getElementById("headlesui-float-cursor-style")) == null ? void 0 : E10.remove();
          });
        }
      }, { flush: "post" }), "ontouchstart" in window || navigator.maxTouchPoints > 0 ? (C7("touchstart", l7), C7("touchend", p10), C7("touchmove", l7)) : (C7("mouseenter", T8), C7("mouseleave", p10), C7("mousemove", T8)));
    }
    return U6(() => {
      i13.value = true;
    }), () => {
      if (a8.default && i13.value)
        return b4(me4, {
          ...r6,
          ...t8,
          portal: true,
          class: "headlesui-float-cursor-root",
          onInitial: u10,
          onShow: () => o11("show"),
          onHide: () => o11("hide"),
          onUpdate: () => o11("update")
        }, a8.default);
    };
  }
});
var gt = tt;
function ht(e4) {
  return R10({
    name: "HighOrderFloat",
    setup(o11, { slots: a8 }) {
      return () => b4(Le, N12(
        e4,
        o11
      ), a8);
    }
  });
}
var nt2 = [
  "Float",
  "FloatArrow",
  "FloatContent",
  "FloatReference"
];
function pt(e4 = {}) {
  const { prefix: r6 = "" } = e4;
  return {
    type: "component",
    resolve: (o11) => {
      if (o11.startsWith(r6)) {
        const a8 = o11.substring(r6.length);
        if (nt2.includes(a8))
          return {
            name: a8,
            from: "@headlessui-float/vue"
          };
      }
    }
  };
}
export {
  Le as Float,
  mt as FloatArrow,
  qe as FloatArrowPropsValidators,
  dt as FloatContent,
  ee4 as FloatContentPropsValidators,
  vt as FloatContextMenu,
  Qe as FloatContextMenuPropsValidators,
  gt as FloatCursor,
  et as FloatCursorPropsValidators,
  n8 as FloatPropsValidators,
  ct as FloatReference,
  Xe as FloatReferencePropsValidators,
  me4 as FloatVirtual,
  Je as FloatVirtualPropsValidators,
  pt as HeadlessUiFloatResolver,
  ht as createHighOrderFloatComponent,
  _5 as renderFloatingElement,
  ce3 as renderReferenceElement,
  De2 as tailwindcssOriginClassResolver,
  st as tailwindcssOriginSafelist,
  ft as tailwindcssRtlOriginClassResolver,
  ut2 as tailwindcssRtlOriginSafelist,
  de3 as useFloat,
  ke2 as useOutsideClick
};
//# sourceMappingURL=@headlessui-float_vue.js.map
