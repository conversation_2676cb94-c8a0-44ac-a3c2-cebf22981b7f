"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getApplicationsByStatus = exports.rejectApplication = exports.shortlistApplication = exports.scoreApplication = exports.updateApplicationStatus = exports.createApplication = exports.getApplication = exports.getApplications = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// Get all applications for a company
const getApplications = async (req, res) => {
    try {
        const { companyId } = req.params;
        const { status, stage, jobId, page = 1, limit = 10 } = req.query;
        const where = {
            job: { companyId: parseInt(companyId) }
        };
        if (status)
            where.status = status;
        if (stage)
            where.stage = stage;
        if (jobId)
            where.jobId = parseInt(jobId);
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const [applications, total] = await Promise.all([
            prisma.jobApplication.findMany({
                where,
                include: {
                    job: {
                        select: { id: true, title: true, department: true }
                    },
                    applicant: {
                        select: { id: true, firstName: true, lastName: true, email: true, avatar: true }
                    },
                    reviewer: {
                        select: { id: true, firstName: true, lastName: true }
                    },
                    interviews: {
                        select: { id: true, scheduledAt: true, status: true, type: true }
                    }
                },
                orderBy: { createdAt: 'desc' },
                skip,
                take: parseInt(limit)
            }),
            prisma.jobApplication.count({ where })
        ]);
        res.json({
            data: applications,
            total,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(total / parseInt(limit))
        });
    }
    catch (error) {
        console.error('Error fetching applications:', error);
        res.status(500).json({ error: 'Failed to fetch applications' });
    }
};
exports.getApplications = getApplications;
// Get single application
const getApplication = async (req, res) => {
    try {
        const { id } = req.params;
        const application = await prisma.jobApplication.findUnique({
            where: { id: parseInt(id) },
            include: {
                job: {
                    include: {
                        company: {
                            select: { id: true, name: true, logo: true }
                        }
                    }
                },
                applicant: {
                    select: { id: true, firstName: true, lastName: true, email: true, phone: true, avatar: true }
                },
                reviewer: {
                    select: { id: true, firstName: true, lastName: true }
                },
                interviews: {
                    include: {
                        interviewer: {
                            select: { id: true, firstName: true, lastName: true }
                        }
                    }
                }
            }
        });
        if (!application) {
            return res.status(404).json({ error: 'Application not found' });
        }
        res.json(application);
    }
    catch (error) {
        console.error('Error fetching application:', error);
        res.status(500).json({ error: 'Failed to fetch application' });
    }
};
exports.getApplication = getApplication;
// Create new application
const createApplication = async (req, res) => {
    try {
        const applicationData = req.body;
        const userId = req.user?.id;
        const application = await prisma.jobApplication.create({
            data: {
                ...applicationData,
                applicantId: userId || null
            },
            include: {
                job: {
                    select: { id: true, title: true, company: { select: { name: true } } }
                },
                applicant: {
                    select: { id: true, firstName: true, lastName: true, email: true }
                }
            }
        });
        res.status(201).json(application);
    }
    catch (error) {
        console.error('Error creating application:', error);
        res.status(500).json({ error: 'Failed to create application' });
    }
};
exports.createApplication = createApplication;
// Update application status
const updateApplicationStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const { status, stage, notes } = req.body;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }
        const application = await prisma.jobApplication.update({
            where: { id: parseInt(id) },
            data: {
                status,
                stage,
                notes,
                reviewedBy: userId,
                reviewedAt: new Date()
            },
            include: {
                job: {
                    select: { id: true, title: true }
                },
                applicant: {
                    select: { id: true, firstName: true, lastName: true, email: true }
                },
                reviewer: {
                    select: { id: true, firstName: true, lastName: true }
                }
            }
        });
        res.json(application);
    }
    catch (error) {
        console.error('Error updating application:', error);
        res.status(500).json({ error: 'Failed to update application' });
    }
};
exports.updateApplicationStatus = updateApplicationStatus;
// Score application
const scoreApplication = async (req, res) => {
    try {
        const { id } = req.params;
        const { score } = req.body;
        if (score < 0 || score > 10) {
            return res.status(400).json({ error: 'Score must be between 0 and 10' });
        }
        const application = await prisma.jobApplication.update({
            where: { id: parseInt(id) },
            data: { score }
        });
        res.json(application);
    }
    catch (error) {
        console.error('Error scoring application:', error);
        res.status(500).json({ error: 'Failed to score application' });
    }
};
exports.scoreApplication = scoreApplication;
// Shortlist application
const shortlistApplication = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }
        const application = await prisma.jobApplication.update({
            where: { id: parseInt(id) },
            data: {
                status: 'SHORTLISTED',
                stage: 'SCREENING',
                reviewedBy: userId,
                reviewedAt: new Date()
            }
        });
        res.json(application);
    }
    catch (error) {
        console.error('Error shortlisting application:', error);
        res.status(500).json({ error: 'Failed to shortlist application' });
    }
};
exports.shortlistApplication = shortlistApplication;
// Reject application
const rejectApplication = async (req, res) => {
    try {
        const { id } = req.params;
        const { notes } = req.body;
        const userId = req.user?.id;
        if (!userId) {
            return res.status(401).json({ error: 'User not authenticated' });
        }
        const application = await prisma.jobApplication.update({
            where: { id: parseInt(id) },
            data: {
                status: 'REJECTED',
                stage: 'REJECTED',
                notes,
                reviewedBy: userId,
                reviewedAt: new Date()
            }
        });
        res.json(application);
    }
    catch (error) {
        console.error('Error rejecting application:', error);
        res.status(500).json({ error: 'Failed to reject application' });
    }
};
exports.rejectApplication = rejectApplication;
// Get applications by status
const getApplicationsByStatus = async (req, res) => {
    try {
        const { companyId, status } = req.params;
        const { page = 1, limit = 10 } = req.query;
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const [applications, total] = await Promise.all([
            prisma.jobApplication.findMany({
                where: {
                    job: { companyId: parseInt(companyId) },
                    status: status.toUpperCase()
                },
                include: {
                    job: {
                        select: { id: true, title: true, department: true }
                    },
                    applicant: {
                        select: { id: true, firstName: true, lastName: true, email: true, avatar: true }
                    }
                },
                orderBy: { createdAt: 'desc' },
                skip,
                take: parseInt(limit)
            }),
            prisma.jobApplication.count({
                where: {
                    job: { companyId: parseInt(companyId) },
                    status: status.toUpperCase()
                }
            })
        ]);
        res.json({
            data: applications,
            total,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(total / parseInt(limit))
        });
    }
    catch (error) {
        console.error('Error fetching applications by status:', error);
        res.status(500).json({ error: 'Failed to fetch applications' });
    }
};
exports.getApplicationsByStatus = getApplicationsByStatus;
//# sourceMappingURL=applicationController.js.map