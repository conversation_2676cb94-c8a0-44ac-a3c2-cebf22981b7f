"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const route_helpers_js_1 = require("../../utils/route-helpers.js");
const company_requests_controller_js_1 = require("../../controllers/recruitment/company-requests.controller.js");
const router = (0, express_1.Router)();
// Get all company join requests for a specific company
router.get("/company/:companyId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(company_requests_controller_js_1.getCompanyRequests));
// Get all company join requests for the current user's company
router.get("/company", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(company_requests_controller_js_1.getCurrentUserCompanyRequests));
// Update a company join request (mark as read, approve, reject)
router.patch("/:requestId", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(company_requests_controller_js_1.updateCompanyRequest));
exports.default = router;
//# sourceMappingURL=company-requests.js.map