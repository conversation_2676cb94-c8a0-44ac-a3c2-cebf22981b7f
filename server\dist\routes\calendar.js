"use strict";
// server/routes/calendar.ts
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const calendar_controller_js_1 = require("../controllers/calendar/calendar.controller.js");
const router = express_1.default.Router();
// All calendar routes require authentication
router.use(route_helpers_js_1.auth);
// Calendar event routes
router.get("/events", (0, route_helpers_js_1.wrapController)(calendar_controller_js_1.getCalendarEvents));
router.post("/events", (0, route_helpers_js_1.wrapController)(calendar_controller_js_1.createCalendarEvent));
router.put("/events/:id", (0, route_helpers_js_1.wrapController)(calendar_controller_js_1.updateCalendarEvent));
router.delete("/events/:id", (0, route_helpers_js_1.wrapController)(calendar_controller_js_1.deleteCalendarEvent));
// Get available event categories based on user role
router.get("/categories", (0, route_helpers_js_1.wrapController)(calendar_controller_js_1.getEventCategories));
exports.default = router;
//# sourceMappingURL=calendar.js.map