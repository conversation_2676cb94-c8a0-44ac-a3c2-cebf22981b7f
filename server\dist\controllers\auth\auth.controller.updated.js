"use strict";
// This is a modified version of the auth.controller.ts file
// with added company owner validation
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.register = void 0;
const client_1 = require("@prisma/client");
const bcrypt = __importStar(require("bcrypt"));
const crypto = __importStar(require("crypto"));
const jwt = __importStar(require("jsonwebtoken"));
// Mock generateCodes function since the actual implementation is not available
function generateCodes() {
    return {
        barcode: crypto.randomBytes(16).toString("hex"),
        qrCode: crypto.randomBytes(16).toString("hex"),
    };
}
// Constants
const SALT_ROUNDS = 10;
const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";
// Prisma client
const prisma = new client_1.PrismaClient();
// Update the register function in auth.controller.ts with this code:
const register = async (req, res) => {
    const { firstName, lastName, mobile, email, companyName, password, role } = req.body;
    try {
        console.log("Registering user with email:", email);
        // Validate role
        const validRoles = ["CLIENT", "PROJECTLEADER", "SALESMAN", "WORKER"];
        const userRole = role && validRoles.includes(role) ? role : "CLIENT";
        console.log(`Registering user with role: ${userRole}`);
        const existingUser = await prisma.user.findUnique({ where: { email } });
        if (existingUser) {
            return res.status(400).json({ message: "Email already in use" });
        }
        // Handle company relationship based on role
        if (userRole === "CLIENT" && companyName) {
            // For CLIENT role, check if company exists and has an owner
            const company = await prisma.company.findFirst({
                where: { name: companyName },
            });
            if (company) {
                // Check if company already has an owner
                const existingOwner = await prisma.userCompany.findFirst({
                    where: {
                        companyId: company.id,
                        role: "OWNER",
                    },
                });
                if (existingOwner) {
                    return res.status(400).json({
                        message: "This company already has an owner. Please use a different company name.",
                    });
                }
            }
        }
        const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);
        const { barcode: userBarcode, qrCode: userQrCode } = await generateCodes();
        const emailConfirmationToken = crypto.randomBytes(32).toString("hex");
        // Create user with the specified role
        const user = await prisma.user.create({
            data: {
                firstName,
                lastName,
                email,
                password: hashedPassword,
                phone: mobile,
                userRoles: {
                    create: [{ role: userRole }], // Cast to Role enum
                },
                barcode: userBarcode,
                qrCode: userQrCode,
                emailConfirmed: false,
                emailConfirmationToken,
                phoneConfirmed: false,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
        });
        console.log("New user created with ID:", user.id);
        // Handle company relationship based on role
        if (userRole === "CLIENT" && companyName) {
            // For CLIENT role, create a new company if it doesn't exist
            let company = await prisma.company.findFirst({
                where: { name: companyName },
            });
            if (!company) {
                const { barcode: companyBarcode, qrCode: companyQrCode } = await generateCodes();
                company = await prisma.company.create({
                    data: {
                        name: companyName,
                        type: "CLIENT", // Adding required CompanyType
                        status: "ACTIVE", // Adding required CompanyStatus
                        barcode: companyBarcode,
                        qrCode: companyQrCode,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                    },
                });
                console.log("New company created with ID:", company.id);
            }
            // Connect user to company as OWNER
            await prisma.user.update({
                where: {
                    id: user.id,
                },
                data: {
                    companies: {
                        create: {
                            company: {
                                connect: {
                                    id: company.id,
                                },
                            },
                            role: "OWNER",
                            status: "ACTIVE",
                        },
                    },
                },
            });
        }
        // Rest of the function remains the same...
        // (Email sending, token generation, etc.)
        // Fix the roles mapping by getting the roles from the created user
        const userWithRoles = await prisma.user.findUnique({
            where: { id: user.id },
            include: { userRoles: true },
        });
        const tokenPayload = {
            id: user.id.toString(),
            roles: userWithRoles?.userRoles.map((role) => role.role) || [userRole],
            email: user.email,
        };
        const accessToken = jwt.sign(tokenPayload, JWT_SECRET, {
            expiresIn: "1d",
        });
        res.status(201).json({
            message: "User registered successfully. You are now automatically logged in.",
            accessToken,
            userId: user.id,
            role: userRole,
        });
    }
    catch (error) {
        console.error("Error during registration:", error);
        res.status(500).json({ message: "Server error", error });
    }
};
exports.register = register;
//# sourceMappingURL=auth.controller.updated.js.map