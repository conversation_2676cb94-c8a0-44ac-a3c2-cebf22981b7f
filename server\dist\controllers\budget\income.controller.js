"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.markIncomeUnpaid = exports.markIncomePaid = exports.deleteIncome = exports.updateIncome = exports.getIncomes = exports.createIncome = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const createIncome = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const payload = req.body;
        const amount = parseFloat(String(payload.amount)) || 0;
        if (payload.interval === "MULTIPLE" &&
            Array.isArray(payload.multipleIntervals) &&
            payload.multipleIntervals.length > 0) {
            const dataArray = payload.multipleIntervals.map((entry) => ({
                userId: userId,
                name: payload.name,
                description: payload.description || "",
                amount,
                paymentMethod: payload.paymentMethod,
                source: payload.source,
                interval: "MULTIPLE",
                startDate: new Date(entry.startDate),
                endDate: new Date(entry.endDate),
            }));
            const result = await prisma_js_1.prisma.income.createMany({ data: dataArray });
            return res.status(201).json({
                success: true,
                count: result.count,
                message: `${result.count} new incomes created (multiple).`,
            });
        }
        if (payload.interval === "RECURRING") {
            const baseDate = payload.startDate
                ? new Date(payload.startDate)
                : new Date();
            const dataArray = Array.from({ length: 12 }, (_, i) => {
                const monthlyDate = new Date(baseDate);
                monthlyDate.setMonth(baseDate.getMonth() + i);
                return {
                    userId: userId,
                    name: payload.name,
                    description: payload.description || "",
                    amount,
                    paymentMethod: payload.paymentMethod,
                    source: payload.source,
                    interval: "RECURRING",
                    startDate: monthlyDate,
                    endDate: monthlyDate,
                };
            });
            const result = await prisma_js_1.prisma.income.createMany({ data: dataArray });
            return res.status(201).json({
                success: true,
                count: result.count,
                message: `${result.count} new incomes created (recurring).`,
            });
        }
        const singleStart = payload.startDate
            ? new Date(payload.startDate)
            : new Date();
        const singleEnd = payload.endDate ? new Date(payload.endDate) : singleStart;
        const newIncome = await prisma_js_1.prisma.income.create({
            data: {
                userId: userId,
                name: payload.name,
                description: payload.description || "",
                amount,
                paymentMethod: payload.paymentMethod,
                source: payload.source,
                interval: payload.interval,
                startDate: singleStart,
                endDate: singleEnd,
            },
        });
        return res.status(201).json(newIncome);
    }
    catch (error) {
        next(error);
    }
};
exports.createIncome = createIncome;
const getIncomes = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const incomes = await prisma_js_1.prisma.income.findMany({
            where: { userId },
            orderBy: { createdAt: "desc" },
        });
        res.json(incomes);
    }
    catch (error) {
        next(error);
    }
};
exports.getIncomes = getIncomes;
const updateIncome = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const incomeId = Number(req.params.id);
        const payload = req.body;
        const existing = await prisma_js_1.prisma.income.findUnique({
            where: { id: incomeId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Income not found or not yours" });
        }
        const updated = await prisma_js_1.prisma.income.update({
            where: { id: incomeId },
            data: {
                name: payload.name,
                description: payload.description,
                amount: payload.amount ? parseFloat(String(payload.amount)) : undefined,
                paymentMethod: payload.paymentMethod,
                source: payload.source,
            },
        });
        res.json(updated);
    }
    catch (error) {
        next(error);
    }
};
exports.updateIncome = updateIncome;
const deleteIncome = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const incomeId = Number(req.params.id);
        const existing = await prisma_js_1.prisma.income.findUnique({
            where: { id: incomeId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Income not found or not yours" });
        }
        await prisma_js_1.prisma.income.delete({
            where: { id: incomeId },
        });
        res.json({ success: true });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteIncome = deleteIncome;
const markIncomePaid = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const incomeId = Number(req.params.id);
        const existing = await prisma_js_1.prisma.income.findUnique({
            where: { id: incomeId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Income not found or not yours" });
        }
        const updated = await prisma_js_1.prisma.income.update({
            where: { id: incomeId },
            data: { paid: true },
        });
        res.json({ success: true, data: updated });
    }
    catch (error) {
        next(error);
    }
};
exports.markIncomePaid = markIncomePaid;
const markIncomeUnpaid = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const incomeId = Number(req.params.id);
        const existing = await prisma_js_1.prisma.income.findUnique({
            where: { id: incomeId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Income not found or not yours" });
        }
        const updated = await prisma_js_1.prisma.income.update({
            where: { id: incomeId },
            data: { paid: false },
        });
        res.json({ success: true, data: updated });
    }
    catch (error) {
        next(error);
    }
};
exports.markIncomeUnpaid = markIncomeUnpaid;
//# sourceMappingURL=income.controller.js.map