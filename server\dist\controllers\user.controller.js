"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentUser = exports.updateUserRoles = exports.deleteUser = exports.createUser = exports.getUserDetailsById = exports.getAllUsers = void 0;
const client_1 = require("@prisma/client");
const bcrypt_1 = __importDefault(require("bcrypt"));
const http_errors_1 = __importDefault(require("http-errors"));
const codeGenerator_js_1 = require("../services/codeGenerator.js");
const prisma = new client_1.PrismaClient();
const SALT_ROUNDS = 10;
const JWT_SECRET = process.env.JWT_SECRET || "your_jwt_secret";
/**
 * Get all users with additional details.
 */
const getAllUsers = async (_req, res, next) => {
    try {
        const users = await prisma.user.findMany({
            include: {
                sessionLogs: true,
                legacySubscriptions: {
                    include: {
                        package: true,
                    },
                },
            },
        });
        // Calculate additional data
        const usersWithDetails = users.map((user) => {
            const sessionCount = user.sessionLogs.length;
            const subscription = user.legacySubscriptions.find((sub) => sub.isActive === true);
            const subscriptionStatus = subscription ? "Active" : "Inactive";
            const subscriptionValidUntil = subscription?.endDate
                ? new Date(subscription.endDate).toLocaleDateString()
                : "N/A";
            return {
                ...user,
                sessionCount,
                subscriptionStatus,
                subscriptionValidUntil,
            };
        });
        res.json(usersWithDetails);
    }
    catch (error) {
        console.error("Error fetching users:", error);
        next((0, http_errors_1.default)(500, "Error fetching users"));
    }
};
exports.getAllUsers = getAllUsers;
/**
 * Get user details by user ID.
 */
const getUserDetailsById = async (req, res, next) => {
    const { userId } = req.params;
    try {
        const user = await prisma.user.findUnique({
            where: { id: parseInt(userId, 10) },
            include: {
                sessionLogs: true,
                legacySubscriptions: {
                    include: {
                        package: true,
                    },
                },
            },
        });
        if (!user) {
            next((0, http_errors_1.default)(404, "User not found"));
            return;
        }
        const sessionCount = user.sessionLogs.length;
        const subscription = user.legacySubscriptions.find((sub) => sub.isActive === true);
        const subscriptionStatus = subscription ? "Active" : "Inactive";
        const subscriptionValidUntil = subscription?.endDate
            ? new Date(subscription.endDate).toLocaleDateString()
            : "N/A";
        res.json({
            ...user,
            sessionCount,
            subscriptionStatus,
            subscriptionValidUntil,
        });
    }
    catch (error) {
        console.error("Error fetching user:", error);
        next((0, http_errors_1.default)(500, "Error fetching user"));
    }
};
exports.getUserDetailsById = getUserDetailsById;
const createUser = async (req, res, next) => {
    const { firstName, lastName, email, password, birthdate, phone, roles, address, address2, street, city, postalCode, state, country, notes, } = req.body;
    try {
        // Check if user already exists
        const existingUser = await prisma.user.findUnique({ where: { email } });
        if (existingUser) {
            next((0, http_errors_1.default)(400, "Email already in use"));
            return;
        }
        // Hash password
        const hashedPassword = await bcrypt_1.default.hash(password, SALT_ROUNDS);
        // Generate unique barcode and QR code
        const { barcode, qrCode } = await (0, codeGenerator_js_1.generateCodes)();
        // Create user
        const user = await prisma.user.create({
            data: {
                firstName,
                lastName,
                email,
                password: hashedPassword,
                birthdate: birthdate ? new Date(birthdate) : null,
                phone,
                address,
                address2,
                street,
                city,
                postalCode,
                state,
                country,
                notes,
                barcode,
                qrCode,
            },
        });
        // Create user roles separately
        if (roles && roles.length > 0) {
            for (const role of roles) {
                await prisma.userRole.create({
                    data: {
                        userId: user.id,
                        role: role, // Cast to any as a workaround
                    },
                });
            }
        }
        else {
            // Add default CLIENT role
            await prisma.userRole.create({
                data: {
                    userId: user.id,
                    role: "CLIENT", // Cast to any as a workaround
                },
            });
        }
        res.status(201).json({ message: "User created successfully", user });
    }
    catch (error) {
        if (error instanceof client_1.Prisma.PrismaClientKnownRequestError &&
            error.code === "P2002" &&
            (Array.isArray(error.meta?.target)
                ? error.meta.target.includes("barcode")
                : error.meta?.target === "barcode")) {
            console.warn("Barcode collision detected, retrying...");
            // Implement retry logic if necessary
        }
        console.error("Error creating user:", error);
        next((0, http_errors_1.default)(500, "Internal Server Error"));
    }
};
exports.createUser = createUser;
/**
 * Delete a user by user ID.
 */
const deleteUser = async (req, res, next) => {
    const { userId } = req.params;
    try {
        await prisma.user.delete({
            where: { id: parseInt(userId, 10) },
        });
        res.status(204).end();
    }
    catch (error) {
        console.error("Error deleting user:", error);
        next((0, http_errors_1.default)(500, "Error deleting user"));
    }
};
exports.deleteUser = deleteUser;
/**
 * Update a user's roles.
 */
const updateUserRoles = async (req, res, next) => {
    const { userId, newRoles } = req.body;
    try {
        // First, delete all existing roles for the user
        await prisma.userRole.deleteMany({
            where: { userId },
        });
        // Then create new roles
        for (const role of newRoles) {
            await prisma.userRole.create({
                data: {
                    userId,
                    role: role, // Cast to any as a workaround
                },
            });
        }
        // Get the updated user with roles
        const updatedUser = await prisma.user.findUnique({
            where: { id: userId },
            include: { userRoles: true },
        });
        res.json(updatedUser);
    }
    catch (error) {
        console.error("Error updating roles:", error);
        next((0, http_errors_1.default)(500, "Error updating roles"));
    }
};
exports.updateUserRoles = updateUserRoles;
const getCurrentUser = async (req, res, next) => {
    try {
        if (!req.user?.id) {
            next((0, http_errors_1.default)(400, "User ID is required"));
            return;
        }
        const userId = req.user.id;
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: { userRoles: true },
        });
        if (!user) {
            next((0, http_errors_1.default)(404, "User not found"));
            return;
        }
        res.json(user);
    }
    catch (error) {
        next(error);
    }
};
exports.getCurrentUser = getCurrentUser;
//# sourceMappingURL=user.controller.js.map