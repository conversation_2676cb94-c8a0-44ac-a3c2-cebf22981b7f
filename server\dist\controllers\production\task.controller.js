"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeTaskAssignment = exports.assignTask = exports.getTaskAssignments = exports.getTaskCalendar = exports.deleteTask = exports.updateTaskStatus = exports.updateTask = exports.createTask = exports.getTaskById = exports.getAllTasks = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const client_1 = require("@prisma/client");
const codeGenerator_js_1 = require("../../services/codeGenerator.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Get all tasks
const getAllTasks = async (req, res, next) => {
    try {
        const { projectId, status } = req.query;
        const whereClause = {
            project: {
                companyId: req.user?.companyId,
            },
        };
        if (projectId) {
            whereClause.projectId = projectId;
        }
        if (status) {
            whereClause.status = status;
        }
        const tasks = await prisma_js_1.prisma.task.findMany({
            where: whereClause,
            include: {
                project: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                assignments: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                avatar: true,
                            },
                        },
                    },
                },
            },
            orderBy: {
                id: "desc",
            },
        });
        res.json(tasks);
    }
    catch (error) {
        console.error("Error fetching tasks:", error);
        next(error);
    }
};
exports.getAllTasks = getAllTasks;
// Get task by ID
const getTaskById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const task = await prisma_js_1.prisma.task.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                project: true,
                assignments: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                avatar: true,
                            },
                        },
                    },
                },
                dependsOn: {
                    include: {
                        parentTask: true,
                    },
                },
                dependentTasks: {
                    include: {
                        dependentTask: true,
                    },
                },
            },
        });
        if (!task) {
            return next(createHttpError(404, "Task not found"));
        }
        res.json(task);
    }
    catch (error) {
        console.error("Error fetching task:", error);
        next(error);
    }
};
exports.getTaskById = getTaskById;
// Create task
const createTask = async (req, res, next) => {
    try {
        const { projectId, name, description, status, startDate, endDate, priority, assignees, dependencies, } = req.body;
        // Generate unique barcode and QR code
        const { barcode, qrCode } = await (0, codeGenerator_js_1.generateCodes)();
        // Create task
        const task = await prisma_js_1.prisma.task.create({
            data: {
                projectId: Number(projectId),
                companyId: req.user?.companyId || 0,
                name,
                description,
                status: status ? status : client_1.TaskStatus.TODO,
                startDate: startDate ? new Date(startDate) : undefined,
                endDate: endDate ? new Date(endDate) : undefined,
                priority: priority ? priority : client_1.Priority.MEDIUM,
                barcode,
                qrCode,
            },
        });
        // Create task assignments if provided
        if (assignees && assignees.length > 0) {
            const assignmentPromises = assignees.map((assignee) => prisma_js_1.prisma.taskAssignment.create({
                data: {
                    taskId: task.id,
                    userId: assignee.userId,
                    role: assignee.role || "ASSIGNEE",
                },
            }));
            await Promise.all(assignmentPromises);
        }
        // Create task dependencies if provided
        if (dependencies && dependencies.length > 0) {
            const dependencyPromises = dependencies.map((dependency) => prisma_js_1.prisma.taskDependency.create({
                data: {
                    parentTaskId: dependency.parentTaskId,
                    dependentTaskId: task.id,
                    type: dependency.type || "FINISH_TO_START",
                },
            }));
            await Promise.all(dependencyPromises);
        }
        // Return created task with assignments
        const createdTask = await prisma_js_1.prisma.task.findUnique({
            where: {
                id: task.id,
            },
            include: {
                assignments: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                avatar: true,
                            },
                        },
                    },
                },
                dependsOn: true,
            },
        });
        res.status(201).json(createdTask);
    }
    catch (error) {
        console.error("Error creating task:", error);
        next(error);
    }
};
exports.createTask = createTask;
// Update task
const updateTask = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { name, description, status, startDate, endDate, priority } = req.body;
        const task = await prisma_js_1.prisma.task.update({
            where: {
                id: Number(id),
            },
            data: {
                name: name !== undefined ? name : undefined,
                description: description !== undefined ? description : undefined,
                status: status ? status : undefined,
                startDate: startDate ? new Date(startDate) : undefined,
                endDate: endDate ? new Date(endDate) : undefined,
                priority: priority ? priority : undefined,
            },
        });
        res.json(task);
    }
    catch (error) {
        console.error("Error updating task:", error);
        next(error);
    }
};
exports.updateTask = updateTask;
// Update task status
const updateTaskStatus = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { status } = req.body;
        const task = await prisma_js_1.prisma.task.update({
            where: {
                id: Number(id),
            },
            data: {
                status: status ? status : undefined,
                ...(status === client_1.TaskStatus.COMPLETED ? { endDate: new Date() } : {}),
            },
        });
        res.json(task);
    }
    catch (error) {
        console.error("Error updating task status:", error);
        next(error);
    }
};
exports.updateTaskStatus = updateTaskStatus;
// Delete task
const deleteTask = async (req, res, next) => {
    try {
        const { id } = req.params;
        await prisma_js_1.prisma.task.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Task deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting task:", error);
        next(error);
    }
};
exports.deleteTask = deleteTask;
// Get task calendar
const getTaskCalendar = async (req, res, next) => {
    try {
        const { startDate, endDate, projectId } = req.query;
        const whereClause = {
            project: {
                companyId: req.user?.companyId,
            },
        };
        if (projectId) {
            whereClause.projectId = projectId;
        }
        if (startDate && endDate) {
            whereClause.OR = [
                {
                    startDate: {
                        gte: new Date(startDate),
                        lte: new Date(endDate),
                    },
                },
                {
                    endDate: {
                        gte: new Date(startDate),
                        lte: new Date(endDate),
                    },
                },
                {
                    dueDate: {
                        gte: new Date(startDate),
                        lte: new Date(endDate),
                    },
                },
            ];
        }
        const tasks = await prisma_js_1.prisma.task.findMany({
            where: whereClause,
            include: {
                project: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                assignments: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                avatar: true,
                            },
                        },
                    },
                },
            },
        });
        // Format tasks for calendar view
        const calendarEvents = tasks.map((task) => ({
            id: task.id,
            title: task.name,
            start: task.startDate,
            end: task.endDate,
            allDay: !task.startDate || !task.endDate,
            status: task.status,
            priority: task.priority,
            project: task.project,
            assignees: task.assignments.map((assignment) => assignment.user),
        }));
        res.json(calendarEvents);
    }
    catch (error) {
        console.error("Error fetching task calendar:", error);
        next(error);
    }
};
exports.getTaskCalendar = getTaskCalendar;
// Get task assignments
const getTaskAssignments = async (req, res, next) => {
    try {
        const { userId } = req.query;
        const whereClause = {
            task: {
                project: {
                    companyId: req.user?.companyId,
                },
            },
        };
        if (userId) {
            whereClause.userId = userId;
        }
        const assignments = await prisma_js_1.prisma.taskAssignment.findMany({
            where: whereClause,
            include: {
                task: {
                    include: {
                        project: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
            },
            orderBy: {
                assignedAt: "desc",
            },
        });
        res.json(assignments);
    }
    catch (error) {
        console.error("Error fetching task assignments:", error);
        next(error);
    }
};
exports.getTaskAssignments = getTaskAssignments;
// Assign task to user
const assignTask = async (req, res, next) => {
    try {
        const { taskId } = req.params;
        const { userId, role } = req.body;
        // Check if assignment already exists
        const existingAssignment = await prisma_js_1.prisma.taskAssignment.findFirst({
            where: {
                taskId: Number(taskId),
                userId: Number(userId),
            },
        });
        if (existingAssignment) {
            return next(createHttpError(400, "User is already assigned to this task"));
        }
        const assignment = await prisma_js_1.prisma.taskAssignment.create({
            data: {
                taskId: Number(taskId),
                userId: Number(userId),
                role: role || "ASSIGNEE",
            },
            include: {
                task: true,
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
            },
        });
        res.status(201).json(assignment);
    }
    catch (error) {
        console.error("Error assigning task:", error);
        next(error);
    }
};
exports.assignTask = assignTask;
// Remove task assignment
const removeTaskAssignment = async (req, res, next) => {
    try {
        const { assignmentId } = req.params;
        await prisma_js_1.prisma.taskAssignment.delete({
            where: {
                id: Number(assignmentId),
            },
        });
        res.json({ message: "Task assignment removed successfully" });
    }
    catch (error) {
        console.error("Error removing task assignment:", error);
        next(error);
    }
};
exports.removeTaskAssignment = removeTaskAssignment;
//# sourceMappingURL=task.controller.js.map