"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEmployeeTrainings = exports.updateEmployeeTraining = exports.enrollEmployeeInTraining = exports.deleteTraining = exports.updateTraining = exports.createTraining = exports.getTrainingById = exports.getAllTrainings = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Using centralized prisma instance from lib/prisma.js
// Get all training programs
const getAllTrainings = async (req, res, next) => {
    try {
        const { companyId } = req.query;
        if (!companyId) {
            return next(createHttpError(400, "Company ID is required"));
        }
        const trainings = await prisma_js_1.prisma.training.findMany({
            where: {
                companyId: Number(companyId),
            },
            orderBy: {
                startDate: "desc",
            },
        });
        res.json(trainings);
    }
    catch (error) {
        console.error("Error fetching training programs:", error);
        next(error);
    }
};
exports.getAllTrainings = getAllTrainings;
// Get a specific training program by ID
const getTrainingById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const training = await prisma_js_1.prisma.training.findUnique({
            where: {
                id: Number(id),
            },
            include: {
                employeeTrainings: {
                    include: {
                        employee: {
                            include: {
                                user: true,
                            },
                        },
                    },
                },
            },
        });
        if (!training) {
            return next(createHttpError(404, "Training program not found"));
        }
        res.json(training);
    }
    catch (error) {
        console.error("Error fetching training program:", error);
        next(error);
    }
};
exports.getTrainingById = getTrainingById;
// Create a new training program
const createTraining = async (req, res, next) => {
    try {
        const { title, description, type, category, startDate, endDate, location, instructorId, maxParticipants, cost, status, companyId, } = req.body;
        if (!title || !startDate || !companyId) {
            return next(createHttpError(400, "Title, start date, and company ID are required"));
        }
        const training = await prisma_js_1.prisma.training.create({
            data: {
                title,
                description,
                type: type, // Cast to TrainingType enum
                startDate: new Date(startDate),
                endDate: endDate ? new Date(endDate) : null,
                location,
                // instructorId field doesn't exist in the schema
                duration: maxParticipants ? parseInt(maxParticipants.toString()) : null,
                cost: cost ? parseFloat(cost.toString()) : null,
                status: status || "SCHEDULED", // Cast to TrainingStatus enum
                companyId: Number(companyId),
            },
        });
        res.status(201).json(training);
    }
    catch (error) {
        console.error("Error creating training program:", error);
        next(error);
    }
};
exports.createTraining = createTraining;
// Update a training program
const updateTraining = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { title, description, type, category, startDate, endDate, location, instructorId, maxParticipants, cost, status, } = req.body;
        const training = await prisma_js_1.prisma.training.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!training) {
            return next(createHttpError(404, "Training program not found"));
        }
        const updatedTraining = await prisma_js_1.prisma.training.update({
            where: {
                id: Number(id),
            },
            data: {
                title: title !== undefined ? title : undefined,
                description: description !== undefined ? description : undefined,
                type: type !== undefined ? type : undefined,
                startDate: startDate ? new Date(startDate) : undefined,
                endDate: endDate ? new Date(endDate) : undefined,
                location: location !== undefined ? location : undefined,
                // instructorId field doesn't exist in the schema
                duration: maxParticipants !== undefined
                    ? parseInt(maxParticipants.toString())
                    : undefined,
                cost: cost !== undefined ? parseFloat(cost.toString()) : undefined,
                status: status !== undefined ? status : undefined,
            },
        });
        res.json(updatedTraining);
    }
    catch (error) {
        console.error("Error updating training program:", error);
        next(error);
    }
};
exports.updateTraining = updateTraining;
// Delete a training program
const deleteTraining = async (req, res, next) => {
    try {
        const { id } = req.params;
        const training = await prisma_js_1.prisma.training.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!training) {
            return next(createHttpError(404, "Training program not found"));
        }
        // Check if there are any employee trainings associated with this program
        const employeeTrainings = await prisma_js_1.prisma.employeeTraining.findMany({
            where: {
                trainingId: Number(id),
            },
        });
        if (employeeTrainings.length > 0) {
            // Delete all employee trainings associated with this program
            await prisma_js_1.prisma.employeeTraining.deleteMany({
                where: {
                    trainingId: Number(id),
                },
            });
        }
        // Delete the training program
        await prisma_js_1.prisma.training.delete({
            where: {
                id: Number(id),
            },
        });
        res.json({ message: "Training program deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting training program:", error);
        next(error);
    }
};
exports.deleteTraining = deleteTraining;
// Enroll an employee in a training program
const enrollEmployeeInTraining = async (req, res, next) => {
    try {
        const { employeeId, trainingProgramId, status, notes } = req.body;
        if (!employeeId || !trainingProgramId) {
            return next(createHttpError(400, "Employee ID and training program ID are required"));
        }
        // Check if employee exists
        const employee = await prisma_js_1.prisma.employee.findUnique({
            where: {
                id: Number(employeeId),
            },
        });
        if (!employee) {
            return next(createHttpError(404, "Employee not found"));
        }
        // Check if training program exists
        const trainingProgram = await prisma_js_1.prisma.training.findUnique({
            where: {
                id: Number(trainingProgramId),
            },
            include: {
                employeeTrainings: true,
            },
        });
        if (!trainingProgram) {
            return next(createHttpError(404, "Training program not found"));
        }
        // Check if enrollment already exists
        const existingEnrollment = await prisma_js_1.prisma.employeeTraining.findFirst({
            where: {
                employeeId: Number(employeeId),
                trainingId: Number(trainingProgramId),
            },
        });
        if (existingEnrollment) {
            return next(createHttpError(409, "Employee is already enrolled in this training program"));
        }
        // Check if the training program has reached its maximum number of participants
        // The Training model has a duration field that we can use as a limit for participants
        if (trainingProgram.duration &&
            trainingProgram.employeeTrainings.length >= trainingProgram.duration) {
            return next(createHttpError(400, "Training program has reached its maximum number of participants"));
        }
        const employeeTraining = await prisma_js_1.prisma.employeeTraining.create({
            data: {
                employeeId: Number(employeeId),
                trainingId: Number(trainingProgramId),
                enrollmentDate: new Date(),
                // The schema doesn't have status or notes fields
            },
        });
        res.status(201).json(employeeTraining);
    }
    catch (error) {
        console.error("Error enrolling employee in training program:", error);
        next(error);
    }
};
exports.enrollEmployeeInTraining = enrollEmployeeInTraining;
// Update an employee's training enrollment
const updateEmployeeTraining = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { status, completionDate, score, feedback, notes } = req.body;
        const employeeTraining = await prisma_js_1.prisma.employeeTraining.findUnique({
            where: {
                id: Number(id),
            },
        });
        if (!employeeTraining) {
            return next(createHttpError(404, "Employee training enrollment not found"));
        }
        const updatedEmployeeTraining = await prisma_js_1.prisma.employeeTraining.update({
            where: {
                id: Number(id),
            },
            data: {
                // The schema only has employeeId, trainingId, and enrollmentDate fields
                // We can only update the enrollmentDate
                enrollmentDate: completionDate ? new Date(completionDate) : undefined,
            },
        });
        res.json(updatedEmployeeTraining);
    }
    catch (error) {
        console.error("Error updating employee training enrollment:", error);
        next(error);
    }
};
exports.updateEmployeeTraining = updateEmployeeTraining;
// Get all training programs for an employee
const getEmployeeTrainings = async (req, res, next) => {
    try {
        const { employeeId } = req.params;
        const employeeTrainings = await prisma_js_1.prisma.employeeTraining.findMany({
            where: {
                employeeId: Number(employeeId),
            },
            include: {
                training: true,
                employee: {
                    include: {
                        user: true,
                    },
                },
            },
            orderBy: {
                enrollmentDate: "desc",
            },
        });
        res.json(employeeTrainings);
    }
    catch (error) {
        console.error("Error fetching employee trainings:", error);
        next(error);
    }
};
exports.getEmployeeTrainings = getEmployeeTrainings;
//# sourceMappingURL=training.controller.js.map