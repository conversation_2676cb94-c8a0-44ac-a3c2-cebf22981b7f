<!-- client-old-tairo-1.5/layers/core/components/activity/ActivityLogDetails.vue -->

<template>
  <TairoModal :open="isOpen" size="lg" @close="$emit('close')">
    <template #header>
      <div class="flex w-full items-center justify-between p-4 md:p-6">
        <div class="flex items-center">
          <BaseIconBox
            size="md"
            :class="getActivityIconClass(activity?.type)"
            rounded="lg"
            color="none"
            class="mr-3"
          >
            <Icon :name="getActivityIcon(activity?.type)" class="h-5 w-5" />
          </BaseIconBox>
          <BaseHeading as="h3" size="md" weight="medium" class="text-muted-900 dark:text-white">
            {{ activity?.title || 'Activity Details' }}
          </BaseHeading>
        </div>
        <button
          class="flex h-8 w-8 items-center justify-center rounded-full text-muted-400 hover:bg-muted-100 hover:text-muted-600 dark:hover:bg-muted-700 dark:hover:text-muted-200"
          @click="$emit('close')"
        >
          <Icon name="lucide:x" class="h-4 w-4" />
        </button>
      </div>
    </template>

    <div class="p-4 md:p-6">
      <div v-if="loading" class="flex justify-center py-8">
        <BaseButtonIcon shape="rounded" color="primary" loading />
      </div>

      <div v-else-if="!activity" class="text-center py-8">
        <Icon name="ph:warning-duotone" class="h-12 w-12 text-muted-400 mx-auto mb-3" />
        <BaseText class="text-muted-500 dark:text-muted-400">
          Activity not found or failed to load
        </BaseText>
      </div>

      <div v-else>
        <!-- Activity Content -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Main Content -->
          <div class="md:col-span-2">
            <div class="mb-6">
              <BaseHeading
                as="h4"
                size="sm"
                weight="medium"
                class="text-muted-800 dark:text-white mb-2"
              >
                Description
              </BaseHeading>
              <BaseParagraph class="text-muted-500 dark:text-muted-400">
                {{ activity.description }}
              </BaseParagraph>
            </div>

            <!-- Changes -->
            <div v-if="activity.changes && activity.changes.length > 0" class="mb-6">
              <BaseHeading
                as="h4"
                size="sm"
                weight="medium"
                class="text-muted-800 dark:text-white mb-2"
              >
                Changes
              </BaseHeading>
              <BaseCard class="p-4">
                <div class="space-y-3">
                  <div
                    v-for="(change, index) in activity.changes"
                    :key="index"
                    class="grid grid-cols-3 gap-4"
                  >
                    <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
                      {{ change.field }}
                    </BaseText>
                    <div class="col-span-2 flex items-center">
                      <div class="flex-1">
                        <BaseText size="sm" class="text-danger-500 line-through mr-2">
                          {{ change.oldValue || '(empty)' }}
                        </BaseText>
                      </div>
                      <Icon name="ph:arrow-right-duotone" class="h-4 w-4 text-muted-400 mx-2" />
                      <div class="flex-1">
                        <BaseText size="sm" class="text-success-500">
                          {{ change.newValue || '(empty)' }}
                        </BaseText>
                      </div>
                    </div>
                  </div>
                </div>
              </BaseCard>
            </div>

            <!-- Related Data -->
            <div v-if="activity.relatedData" class="mb-6">
              <BaseHeading
                as="h4"
                size="sm"
                weight="medium"
                class="text-muted-800 dark:text-white mb-2"
              >
                Related Data
              </BaseHeading>
              <BaseCard class="p-4">
                <pre class="text-xs text-muted-500 dark:text-muted-400 overflow-auto max-h-60">{{
                  formatJson(activity.relatedData)
                }}</pre>
              </BaseCard>
            </div>

            <!-- Tags -->
            <div v-if="activity.tags && activity.tags.length > 0" class="mb-6">
              <BaseHeading
                as="h4"
                size="sm"
                weight="medium"
                class="text-muted-800 dark:text-white mb-2"
              >
                Tags
              </BaseHeading>
              <div class="flex flex-wrap gap-2">
                <BaseTag v-for="tag in activity.tags" :key="tag" color="info" flavor="pastel">
                  {{ tag }}
                </BaseTag>
              </div>
            </div>
          </div>

          <!-- Sidebar -->
          <div>
            <BaseCard class="p-4 mb-4">
              <BaseHeading
                as="h4"
                size="sm"
                weight="medium"
                class="text-muted-800 dark:text-white mb-4"
              >
                Activity Information
              </BaseHeading>

              <div class="space-y-3">
                <div class="flex justify-between">
                  <BaseText size="sm" class="text-muted-400">Type</BaseText>
                  <BaseTag :color="getActivityTypeColor(activity.type)" flavor="pastel" size="sm">
                    {{ formatActivityType(activity.type) }}
                  </BaseTag>
                </div>
                <div class="flex justify-between">
                  <BaseText size="sm" class="text-muted-400">Timestamp</BaseText>
                  <BaseText size="sm" class="text-muted-800 dark:text-muted-100">
                    {{ formatDate(activity.timestamp) }}
                  </BaseText>
                </div>
                <div class="flex justify-between">
                  <BaseText size="sm" class="text-muted-400">Module</BaseText>
                  <BaseText size="sm" class="text-muted-800 dark:text-muted-100">
                    {{ activity.module || 'N/A' }}
                  </BaseText>
                </div>
                <div class="flex justify-between">
                  <BaseText size="sm" class="text-muted-400">IP Address</BaseText>
                  <BaseText size="sm" class="text-muted-800 dark:text-muted-100">
                    {{ activity.ipAddress || 'N/A' }}
                  </BaseText>
                </div>
                <div class="flex justify-between">
                  <BaseText size="sm" class="text-muted-400">Browser</BaseText>
                  <BaseText size="sm" class="text-muted-800 dark:text-muted-100">
                    {{ activity.browser || 'N/A' }}
                  </BaseText>
                </div>
                <div class="flex justify-between">
                  <BaseText size="sm" class="text-muted-400">OS</BaseText>
                  <BaseText size="sm" class="text-muted-800 dark:text-muted-100">
                    {{ activity.os || 'N/A' }}
                  </BaseText>
                </div>
              </div>
            </BaseCard>

            <!-- User Information -->
            <BaseCard v-if="activity.user" class="p-4">
              <BaseHeading
                as="h4"
                size="sm"
                weight="medium"
                class="text-muted-800 dark:text-white mb-4"
              >
                User Information
              </BaseHeading>

              <div class="flex items-center mb-4">
                <BaseAvatar
                  :src="activity.user.avatar"
                  :alt="getUserName(activity.user)"
                  size="lg"
                  class="mr-3"
                />
                <div>
                  <BaseText size="sm" weight="medium" class="text-muted-800 dark:text-muted-100">
                    {{ getUserName(activity.user) }}
                  </BaseText>
                  <BaseText size="xs" class="text-muted-400">
                    {{ activity.user.role || 'N/A' }}
                  </BaseText>
                </div>
              </div>

              <BaseButton color="primary" class="w-full" @click="viewUserProfile">
                <Icon name="ph:user-duotone" class="h-4 w-4 mr-1" />
                View User Profile
              </BaseButton>
            </BaseCard>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex w-full items-center justify-between p-4 md:px-6">
        <BaseButton color="muted" @click="$emit('close')"> Close </BaseButton>

        <div class="flex items-center gap-2">
          <BaseButton
            v-if="activity && activity.type === 'error'"
            color="danger"
            @click="$emit('resolve', activity)"
          >
            <Icon name="ph:check-duotone" class="h-4 w-4 mr-1" />
            Mark as Resolved
          </BaseButton>

          <BaseButton color="primary" @click="exportActivityLog">
            <Icon name="ph:download-duotone" class="h-4 w-4 mr-1" />
            Export
          </BaseButton>
        </div>
      </div>
    </template>
  </TairoModal>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { format } from 'date-fns'
import { useToaster } from '../../../../composables/toaster'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  activityId: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['close', 'resolve'])

const toaster = useToaster()
const loading = ref(false)
const activity = ref(null)

// Methods
const formatDate = (timestamp: Date | string) => {
  if (!timestamp) return 'N/A'

  try {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp
    return format(date, 'MMM d, yyyy HH:mm:ss')
  } catch (error) {
    return 'N/A'
  }
}

const getUserName = (user) => {
  if (!user) return 'Unknown User'

  return `${user.firstName} ${user.lastName}`.trim() || user.username || 'Unknown User'
}

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    login: 'ph:sign-in-duotone',
    logout: 'ph:sign-out-duotone',
    create: 'ph:plus-circle-duotone',
    update: 'ph:pencil-duotone',
    delete: 'ph:trash-duotone',
    share: 'ph:share-network-duotone',
    comment: 'ph:chat-text-duotone',
    upload: 'ph:upload-duotone',
    download: 'ph:download-duotone',
    approve: 'ph:check-circle-duotone',
    reject: 'ph:x-circle-duotone',
    assign: 'ph:user-plus-duotone',
    complete: 'ph:check-square-duotone',
    payment: 'ph:currency-dollar-duotone',
    error: 'ph:warning-duotone',
    security: 'ph:shield-duotone',
    settings: 'ph:gear-duotone',
    notification: 'ph:bell-duotone',
  }

  return iconMap[type] || 'ph:activity-duotone'
}

const getActivityIconClass = (type: string) => {
  const classMap: Record<string, string> = {
    login: 'bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400',
    logout: 'bg-muted-100 text-muted-500 dark:bg-muted-700 dark:text-muted-400',
    create: 'bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400',
    update: 'bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400',
    delete: 'bg-danger-100 text-danger-500 dark:bg-danger-500/20 dark:text-danger-400',
    share: 'bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400',
    comment: 'bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400',
    upload: 'bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400',
    download: 'bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400',
    approve: 'bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400',
    reject: 'bg-danger-100 text-danger-500 dark:bg-danger-500/20 dark:text-danger-400',
    assign: 'bg-warning-100 text-warning-500 dark:bg-warning-500/20 dark:text-warning-400',
    complete: 'bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400',
    payment: 'bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400',
    error: 'bg-danger-100 text-danger-500 dark:bg-danger-500/20 dark:text-danger-400',
    security: 'bg-warning-100 text-warning-500 dark:bg-warning-500/20 dark:text-warning-400',
    settings: 'bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400',
    notification: 'bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400',
  }

  return classMap[type] || 'bg-muted-100 text-muted-500 dark:bg-muted-700 dark:text-muted-400'
}

const getActivityTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    login: 'primary',
    logout: 'muted',
    create: 'success',
    update: 'info',
    delete: 'danger',
    share: 'primary',
    comment: 'info',
    upload: 'success',
    download: 'info',
    approve: 'success',
    reject: 'danger',
    assign: 'warning',
    complete: 'success',
    payment: 'success',
    error: 'danger',
    security: 'warning',
    settings: 'info',
    notification: 'primary',
  }

  return colorMap[type] || 'muted'
}

const formatActivityType = (type: string) => {
  if (!type) return 'Unknown'

  // Convert camelCase or snake_case to Title Case
  return type
    .replace(/([A-Z])/g, ' $1') // Insert a space before all uppercase letters
    .replace(/_/g, ' ') // Replace underscores with spaces
    .replace(/^\w/, (c) => c.toUpperCase()) // Capitalize the first letter
}

const formatJson = (data: any) => {
  if (!data) return ''

  try {
    return JSON.stringify(data, null, 2)
  } catch (error) {
    return String(data)
  }
}

const viewUserProfile = () => {
  if (!activity.value || !activity.value.user) return

  // In a real implementation, this would navigate to the user profile
  // For now, we'll just show a toast message
  toaster.info(`Viewing profile for ${getUserName(activity.value.user)}`)
}

const exportActivityLog = () => {
  if (!activity.value) return

  // In a real implementation, this would export the activity log
  // For now, we'll just show a toast message
  toaster.success('Activity log exported')
}

// Fetch activity data when activityId changes
const fetchActivity = async () => {
  if (!props.activityId) return

  loading.value = true

  try {
    // Simulate API call
    setTimeout(() => {
      // Mock data for a single activity
      activity.value = {
        id: props.activityId,
        title: 'User Profile Updated',
        description:
          'User profile information was updated including contact details and preferences.',
        type: 'update',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        module: 'User Management',
        ipAddress: '*************',
        browser: 'Chrome 98.0.4758.102',
        os: 'Windows 10',
        user: {
          id: 'user_1',
          firstName: 'John',
          lastName: 'Doe',
          avatar: '/img/avatars/cute-cosmonout.svg',
          role: 'Admin',
        },
        tags: ['User', 'Profile', 'Update'],
        changes: [
          {
            field: 'Email',
            oldValue: '<EMAIL>',
            newValue: '<EMAIL>',
          },
          {
            field: 'Phone Number',
            oldValue: '+****************',
            newValue: '+****************',
          },
          {
            field: 'Notification Preferences',
            oldValue: 'Email, SMS',
            newValue: 'Email',
          },
        ],
        relatedData: {
          userId: 'user_1',
          sessionId: 'sess_12345',
          requestId: 'req_67890',
          changes: {
            email: {
              old: '<EMAIL>',
              new: '<EMAIL>',
            },
            phoneNumber: {
              old: '+****************',
              new: '+****************',
            },
            notificationPreferences: {
              old: ['email', 'sms'],
              new: ['email'],
            },
          },
        },
      }

      loading.value = false
    }, 1000)
  } catch (error) {
    console.error('Error fetching activity:', error)
    activity.value = null
    loading.value = false
  }
}

watch(
  () => props.activityId,
  () => {
    if (props.isOpen && props.activityId) {
      fetchActivity()
    }
  }
)

watch(
  () => props.isOpen,
  (isOpen) => {
    if (isOpen && props.activityId) {
      fetchActivity()
    }
  }
)

onMounted(() => {
  if (props.isOpen && props.activityId) {
    fetchActivity()
  }
})
</script>
