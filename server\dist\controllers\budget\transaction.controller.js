"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteTransaction = exports.updateTransaction = exports.getTransactionById = exports.getTransactions = exports.createTransaction = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const createTransaction = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const payload = req.body;
        // Validate required fields
        if (!payload.description ||
            !payload.amount ||
            !payload.category ||
            !payload.date) {
            return res.status(400).json({
                error: "Missing required fields: description, amount, category, and date are required",
            });
        }
        // Create transaction
        const newTransaction = await prisma_js_1.prisma.transaction.create({
            data: {
                userId: userId, // Assert userId is not undefined
                type: payload.type,
                description: payload.description,
                amount: payload.amount,
                category: payload.category,
                date: new Date(payload.date),
                status: payload.status || "COMPLETED",
                notes: payload.notes,
                companyId: payload.companyId,
            },
        });
        return res.status(201).json(newTransaction);
    }
    catch (error) {
        next(error);
    }
};
exports.createTransaction = createTransaction;
const getTransactions = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const type = req.query.type;
        const category = req.query.category;
        const status = req.query.status;
        const companyId = req.query.companyId
            ? parseInt(req.query.companyId)
            : undefined;
        const startDate = req.query.startDate
            ? new Date(req.query.startDate)
            : undefined;
        const endDate = req.query.endDate
            ? new Date(req.query.endDate)
            : undefined;
        // Build filter
        const filter = { userId };
        if (type) {
            filter.type = type;
        }
        if (category) {
            filter.category = category;
        }
        if (status) {
            filter.status = status;
        }
        if (companyId) {
            filter.companyId = companyId;
        }
        if (startDate || endDate) {
            filter.date = {};
            if (startDate) {
                filter.date.gte = startDate;
            }
            if (endDate) {
                filter.date.lte = endDate;
            }
        }
        const transactions = await prisma_js_1.prisma.transaction.findMany({
            where: filter,
            orderBy: { date: "desc" },
        });
        res.json(transactions);
    }
    catch (error) {
        next(error);
    }
};
exports.getTransactions = getTransactions;
const getTransactionById = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const transactionId = parseInt(req.params.id);
        const transaction = await prisma_js_1.prisma.transaction.findUnique({
            where: { id: transactionId },
        });
        if (!transaction || transaction.userId !== userId) {
            return res
                .status(404)
                .json({ error: "Transaction not found or not yours" });
        }
        res.json(transaction);
    }
    catch (error) {
        next(error);
    }
};
exports.getTransactionById = getTransactionById;
const updateTransaction = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const transactionId = parseInt(req.params.id);
        const payload = req.body;
        // Check if transaction exists and belongs to user
        const existing = await prisma_js_1.prisma.transaction.findUnique({
            where: { id: transactionId },
        });
        if (!existing || existing.userId !== userId) {
            return res
                .status(404)
                .json({ error: "Transaction not found or not yours" });
        }
        // Update transaction
        const updated = await prisma_js_1.prisma.transaction.update({
            where: { id: transactionId },
            data: {
                type: payload.type,
                description: payload.description,
                amount: payload.amount,
                category: payload.category,
                date: payload.date ? new Date(payload.date) : undefined,
                status: payload.status,
                notes: payload.notes,
                companyId: payload.companyId,
            },
        });
        res.json(updated);
    }
    catch (error) {
        next(error);
    }
};
exports.updateTransaction = updateTransaction;
const deleteTransaction = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const transactionId = parseInt(req.params.id);
        // Check if transaction exists and belongs to user
        const existing = await prisma_js_1.prisma.transaction.findUnique({
            where: { id: transactionId },
        });
        if (!existing || existing.userId !== userId) {
            return res
                .status(404)
                .json({ error: "Transaction not found or not yours" });
        }
        // Delete transaction
        await prisma_js_1.prisma.transaction.delete({
            where: { id: transactionId },
        });
        res.json({ success: true });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteTransaction = deleteTransaction;
//# sourceMappingURL=transaction.controller.js.map