{"version": 3, "file": "email-data.js", "sourceRoot": "", "sources": ["../../../prisma/seed/email-data.ts"], "names": [], "mappings": ";;AAIA,sCAwLC;AA5LD,2CAA8C;AAE9C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAE3B,KAAK,UAAU,aAAa;IACjC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAExC,IAAI,CAAC;QACH,iCAAiC;QACjC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QAC3C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAE9D,+BAA+B;QAC/B,IAAI,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;YACrD,KAAK,EAAE;gBACL,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,IAAI,CAAC,KAAK;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC9C,IAAI,EAAE;oBACJ,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,iBAAiB;oBACpC,QAAQ,EAAE,QAAQ;oBAClB,SAAS,EAAE,IAAI;iBAChB;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,6BAA6B,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;QAE/D,uBAAuB;QACvB,MAAM,OAAO,GAAG;YACd,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;YAChC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;YAC9B,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE;YACxC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;YAC9B,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;SACnC,CAAC;QAEF,MAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;gBACnD,KAAK,EAAE;oBACL,SAAS,EAAE,YAAY,CAAC,EAAE;oBAC1B,IAAI,EAAE,MAAM,CAAC,IAAI;iBAClB;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;oBAC5C,IAAI,EAAE;wBACJ,SAAS,EAAE,YAAY,CAAC,EAAE;wBAC1B,IAAI,EAAE,MAAM,CAAC,IAAI;wBACjB,IAAI,EAAE,MAAM,CAAC,IAAI;qBAClB;iBACF,CAAC,CAAC;YACL,CAAC;YAED,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,sBAAsB,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;QACxD,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAE,CAAC;QACpE,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAE,CAAC;QAClE,MAAM,eAAe,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAE,CAAC;QAC5E,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAE,CAAC;QAElE,oBAAoB;QACpB,MAAM,YAAY,GAAG;YACnB,eAAe;YACf;gBACE,QAAQ,EAAE,WAAW,CAAC,EAAE;gBACxB,IAAI,EAAE,iCAAiC;gBACvC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE,6bAA6b;gBACnc,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,cAAc;aACtE;YACD;gBACE,QAAQ,EAAE,WAAW,CAAC,EAAE;gBACxB,IAAI,EAAE,oCAAoC;gBAC1C,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO,EAAE,mCAAmC;gBAC5C,IAAI,EAAE,kSAAkS;gBACxS,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,YAAY;aACrE;YACD;gBACE,QAAQ,EAAE,WAAW,CAAC,EAAE;gBACxB,IAAI,EAAE,sCAAsC;gBAC5C,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE,6TAA6T;gBACnU,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,cAAc;aACtE;YACD,cAAc;YACd;gBACE,QAAQ,EAAE,UAAU,CAAC,EAAE;gBACvB,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,EAAE,EAAE,CAAC,sBAAsB,CAAC;gBAC5B,OAAO,EAAE,mCAAmC;gBAC5C,IAAI,EAAE,+LAA+L;gBACrM,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,aAAa;aAC1E;YACD;gBACE,QAAQ,EAAE,UAAU,CAAC,EAAE;gBACvB,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,EAAE,EAAE,CAAC,kBAAkB,CAAC;gBACxB,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE,kNAAkN;gBACxN,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,aAAa;aAC1E;YACD,mBAAmB;YACnB;gBACE,QAAQ,EAAE,eAAe,CAAC,EAAE;gBAC5B,IAAI,EAAE,6BAA6B;gBACnC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO,EAAE,kCAAkC;gBAC3C,IAAI,EAAE,2UAA2U;gBACjV,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,iBAAiB;aACrE;YACD;gBACE,QAAQ,EAAE,eAAe,CAAC,EAAE;gBAC5B,IAAI,EAAE,gCAAgC;gBACtC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE,kXAAkX;gBACxX,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,aAAa;aAC1E;YACD,cAAc;YACd;gBACE,QAAQ,EAAE,UAAU,CAAC,EAAE;gBACvB,IAAI,EAAE,6BAA6B;gBACnC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE,uJAAuJ;gBAC7J,MAAM,EAAE,SAAS;gBACjB,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,cAAc;aAC5E;YACD;gBACE,QAAQ,EAAE,UAAU,CAAC,EAAE;gBACvB,IAAI,EAAE,mBAAmB;gBACzB,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO,EAAE,6BAA6B;gBACtC,IAAI,EAAE,+HAA+H;gBACrI,MAAM,EAAE,SAAS;gBACjB,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,cAAc;aAC5E;SACF,CAAC;QAEF,wBAAwB;QACxB,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;YACjC,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC/B,IAAI,EAAE;oBACJ,SAAS,EAAE,YAAY,CAAC,EAAE;oBAC1B,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,EAAE,EAAE,KAAK,CAAC,EAAE;oBACZ,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,MAAM,EAAE,KAAK,CAAC,MAAa;oBAC3B,UAAU,EAAE,KAAK,CAAC,UAAU;iBAC7B;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,cAAc,YAAY,CAAC,MAAM,gBAAgB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;IAC9D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,yBAAyB;AACzB,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,aAAa,EAAE;SACZ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;QACX,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,OAAO,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC,CAAC,CAAC;AACP,CAAC"}