"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.emailConfirmationStatus = void 0;
const prisma_js_1 = require("../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
const emailConfirmationStatus = async (req, res) => {
    const { userId } = req.query;
    if (!userId) {
        res.status(400).send("User ID is required");
        return;
    }
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
    res.flushHeaders();
    const intervalId = setInterval(async () => {
        try {
            const user = await prisma_js_1.prisma.user.findUnique({
                where: { id: parseInt(userId, 10) },
            });
            if (user && user.emailConfirmed) {
                res.write(`data: ${JSON.stringify({ emailConfirmed: true })}\n\n`);
                clearInterval(intervalId);
                res.end();
            }
        }
        catch (error) {
            console.error("Error checking email confirmation:", error);
            res.write(`data: ${JSON.stringify({ error: "Error checking confirmation" })}\n\n`);
            clearInterval(intervalId);
            res.end();
        }
    }, 1000);
    req.on("close", () => {
        clearInterval(intervalId);
        res.end();
    });
};
exports.emailConfirmationStatus = emailConfirmationStatus;
//# sourceMappingURL=sseController.js.map