"use strict";
// middleware/authorizeRoles.js
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorizeRoles = void 0;
const http_errors_1 = __importDefault(require("http-errors"));
const authorizeRoles = (...allowedRoles) => {
    console.log("authorizeRoles middleware called with allowed roles:", allowedRoles);
    return (req, res, next) => {
        console.log("req.user:", req.user);
        const userRoles = req.user?.roles || [];
        console.log("User roles:", userRoles);
        // Always allow SUPERADMIN access regardless of specified roles
        if (userRoles.includes("SUPERADMIN")) {
            console.log("User is SUPERADMIN, allowing access");
            return next();
        }
        const hasRole = userRoles.some((role) => allowedRoles.includes(role));
        console.log("User has required role?", hasRole);
        if (!hasRole) {
            console.log("Access denied: user does not have required role");
            return next((0, http_errors_1.default)(403, "Forbidden: You do not have access to this resource"));
        }
        next();
    };
};
exports.authorizeRoles = authorizeRoles;
//# sourceMappingURL=authorizeRoles.js.map