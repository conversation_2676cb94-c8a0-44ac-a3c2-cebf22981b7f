"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteBudget = exports.updateBudget = exports.getBudgetById = exports.getBudgets = exports.createBudget = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const createBudget = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const payload = req.body;
        // Validate required fields
        if (!payload.name || !payload.startDate || !payload.endDate) {
            return res.status(400).json({
                error: "Missing required fields: name, startDate, and endDate are required",
            });
        }
        // Validate date range
        const startDate = new Date(payload.startDate);
        const endDate = new Date(payload.endDate);
        if (endDate < startDate) {
            return res.status(400).json({
                error: "End date must be after start date",
            });
        }
        // Create budget
        const newBudget = await prisma_js_1.prisma.budget.create({
            data: {
                userId: userId,
                name: payload.name,
                description: payload.description || "",
                startDate,
                endDate,
                type: payload.type,
                companyId: payload.type === "COMPANY" ? payload.companyId : null,
                isActive: true,
            },
        });
        return res.status(201).json(newBudget);
    }
    catch (error) {
        next(error);
    }
};
exports.createBudget = createBudget;
const getBudgets = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const type = req.query.type;
        const companyId = req.query.companyId
            ? parseInt(req.query.companyId)
            : undefined;
        const isActive = req.query.isActive === "true";
        // Build filter
        const filter = { userId };
        if (type) {
            filter.type = type;
        }
        if (companyId) {
            filter.companyId = companyId;
        }
        if (req.query.isActive !== undefined) {
            filter.isActive = isActive;
        }
        const budgets = await prisma_js_1.prisma.budget.findMany({
            where: filter,
            orderBy: { startDate: "desc" },
            include: {
                _count: {
                    select: {
                        incomes: true,
                        expenses: true,
                    },
                },
            },
        });
        res.json(budgets);
    }
    catch (error) {
        next(error);
    }
};
exports.getBudgets = getBudgets;
const getBudgetById = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const budgetId = parseInt(req.params.id);
        const budget = await prisma_js_1.prisma.budget.findUnique({
            where: { id: budgetId },
            include: {
                incomes: true,
                expenses: true,
            },
        });
        if (!budget || budget.userId !== userId) {
            return res.status(404).json({ error: "Budget not found or not yours" });
        }
        res.json(budget);
    }
    catch (error) {
        next(error);
    }
};
exports.getBudgetById = getBudgetById;
const updateBudget = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const budgetId = parseInt(req.params.id);
        const payload = req.body;
        // Check if budget exists and belongs to user
        const existing = await prisma_js_1.prisma.budget.findUnique({
            where: { id: budgetId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Budget not found or not yours" });
        }
        // Validate date range if both dates are provided
        if (payload.startDate && payload.endDate) {
            const startDate = new Date(payload.startDate);
            const endDate = new Date(payload.endDate);
            if (endDate < startDate) {
                return res.status(400).json({
                    error: "End date must be after start date",
                });
            }
        }
        // Update budget
        const updated = await prisma_js_1.prisma.budget.update({
            where: { id: budgetId },
            data: {
                name: payload.name,
                description: payload.description,
                startDate: payload.startDate ? new Date(payload.startDate) : undefined,
                endDate: payload.endDate ? new Date(payload.endDate) : undefined,
                type: payload.type,
                companyId: payload.type === "COMPANY" ? payload.companyId : null,
            },
        });
        res.json(updated);
    }
    catch (error) {
        next(error);
    }
};
exports.updateBudget = updateBudget;
const deleteBudget = async (req, res, next) => {
    try {
        const userId = req.user?.id;
        const budgetId = parseInt(req.params.id);
        // Check if budget exists and belongs to user
        const existing = await prisma_js_1.prisma.budget.findUnique({
            where: { id: budgetId },
        });
        if (!existing || existing.userId !== userId) {
            return res.status(404).json({ error: "Budget not found or not yours" });
        }
        // Delete budget
        await prisma_js_1.prisma.budget.delete({
            where: { id: budgetId },
        });
        res.json({ success: true });
    }
    catch (error) {
        next(error);
    }
};
exports.deleteBudget = deleteBudget;
//# sourceMappingURL=budget.controller.js.map