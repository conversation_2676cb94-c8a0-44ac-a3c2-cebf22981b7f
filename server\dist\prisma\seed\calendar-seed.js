"use strict";
// server/prisma/seed/calendar-seed.ts
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const calendar_data_js_1 = require("./calendar-data.js");
const prisma = new client_1.PrismaClient();
async function main() {
    console.log("🌱 Starting calendar seed...");
    try {
        await (0, calendar_data_js_1.seedCalendarData)();
        console.log("✅ Calendar seed completed successfully!");
    }
    catch (error) {
        console.error("❌ Calendar seed failed:", error);
        process.exit(1);
    }
    finally {
        await prisma.$disconnect();
    }
}
main()
    .catch((e) => {
    console.error(e);
    process.exit(1);
});
//# sourceMappingURL=calendar-seed.js.map