{"version": 3, "file": "benefits.controller.js", "sourceRoot": "", "sources": ["../../../controllers/hr/benefits.controller.ts"], "names": [], "mappings": ";;;AACA,mDAA6C;AAE7C,8DAA8D;AAC9D,SAAS,eAAe,CAAC,UAAkB,EAAE,OAAe;IAC1D,MAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IACtC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;IAC9B,OAAO,KAAK,CAAC;AACf,CAAC;AAcD,uDAAuD;AAEvD,mBAAmB;AACZ,MAAM,cAAc,GAAG,KAAK,EACjC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;QAEhC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,KAAK,EAAE;gBACL,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;aAC7B;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC,CAAC;IACzD,CAAC;AACH,CAAC,CAAC;AA1BW,QAAA,cAAc,kBA0BzB;AAEF,+BAA+B;AACxB,MAAM,cAAc,GAAG,KAAK,EACjC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAE1B,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,yBAAyB,CAAC,CAAC,CAAC;IACxD,CAAC;AACH,CAAC,CAAC;AAvBW,QAAA,cAAc,kBAuBzB;AAEF,uBAAuB;AAChB,MAAM,aAAa,GAAG,KAAK,EAChC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EACJ,IAAI,EACJ,WAAW,EACX,IAAI,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,oBAAoB,EACpB,SAAS,EACT,OAAO,EACP,MAAM,EACN,SAAS,GACV,GAAG,GAAG,CAAC,IAAI,CAAC;QAEb,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjC,OAAO,IAAI,CACT,eAAe,CAAC,GAAG,EAAE,yCAAyC,CAAC,CAChE,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,IAAI,EAAE;gBACJ,IAAI;gBACJ,WAAW;gBACX,IAAI;gBACJ,QAAQ;gBACR,YAAY;gBACZ,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI;gBAChD,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI;gBAC7C,oBAAoB,EAAE,oBAAoB;oBACxC,CAAC,CAAC,UAAU,CAAC,oBAAoB,CAAC;oBAClC,CAAC,CAAC,IAAI;gBACR,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;gBACvD,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;gBAChD,MAAM,EAAE,MAAM,IAAI,QAAQ;gBAC1B,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;aAC7B;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC,CAAC;IACzD,CAAC;AACH,CAAC,CAAC;AAnDW,QAAA,aAAa,iBAmDxB;AAEF,mBAAmB;AACZ,MAAM,aAAa,GAAG,KAAK,EAChC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,EACJ,IAAI,EACJ,WAAW,EACX,IAAI,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,oBAAoB,EACpB,SAAS,EACT,OAAO,EACP,MAAM,GACP,GAAG,GAAG,CAAC,IAAI,CAAC;QAEb,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACjD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;gBAC3C,WAAW,EAAE,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;gBAChE,IAAI,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;gBAC3C,QAAQ,EAAE,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;gBACvD,YAAY,EAAE,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;gBACnE,QAAQ,EAAE,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS;gBACnE,OAAO,EAAE,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;gBAChE,oBAAoB,EAClB,oBAAoB,KAAK,SAAS;oBAChC,CAAC,CAAC,UAAU,CAAC,oBAAoB,CAAC;oBAClC,CAAC,CAAC,SAAS;gBACf,SAAS,EAAE,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS;gBACpE,OAAO,EAAE,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC9D,MAAM,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;aAClD;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC,CAAC;IACzD,CAAC;AACH,CAAC,CAAC;AA1DW,QAAA,aAAa,iBA0DxB;AAEF,mBAAmB;AACZ,MAAM,aAAa,GAAG,KAAK,EAChC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAE1B,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,wEAAwE;QACxE,MAAM,gBAAgB,GAAG,MAAM,kBAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;YAC7D,KAAK,EAAE;gBACL,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC;aACtB;SACF,CAAC,CAAC;QAEH,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,4DAA4D;YAC5D,MAAM,kBAAM,CAAC,eAAe,CAAC,UAAU,CAAC;gBACtC,KAAK,EAAE;oBACL,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC;iBACtB;aACF,CAAC,CAAC;QACL,CAAC;QAED,qBAAqB;QACrB,MAAM,kBAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1B,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC,CAAC;IACxD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC,CAAC;IACzD,CAAC;AACH,CAAC,CAAC;AA9CW,QAAA,aAAa,iBA8CxB;AAEF,kCAAkC;AAC3B,MAAM,uBAAuB,GAAG,KAAK,EAC1C,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EACJ,UAAU,EACV,SAAS,EACT,aAAa,EACb,cAAc,EACd,oBAAoB,EACpB,KAAK,EACL,MAAM,GACP,GAAG,GAAG,CAAC,IAAI,CAAC;QAEb,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9B,OAAO,IAAI,CACT,eAAe,CAAC,GAAG,EAAE,yCAAyC,CAAC,CAChE,CAAC;QACJ,CAAC;QAED,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,MAAM,kBAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,UAAU,CAAC;aACvB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,0BAA0B;QAC1B,MAAM,OAAO,GAAG,MAAM,kBAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC;aACtB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,qCAAqC;QACrC,MAAM,kBAAkB,GAAG,MAAM,kBAAM,CAAC,eAAe,CAAC,SAAS,CAAC;YAChE,KAAK,EAAE;gBACL,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;gBAC9B,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;aAC7B;SACF,CAAC,CAAC;QAEH,IAAI,kBAAkB,EAAE,CAAC;YACvB,OAAO,IAAI,CACT,eAAe,CAAC,GAAG,EAAE,8CAA8C,CAAC,CACrE,CAAC;QACJ,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,kBAAM,CAAC,eAAe,CAAC,MAAM,CAAC;YAC1D,IAAI,EAAE;gBACJ,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;gBAC9B,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;gBAC5B,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;gBACnE,cAAc,EAAE,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI;gBAClE,oBAAoB,EAAE,oBAAoB;oBACxC,CAAC,CAAC,UAAU,CAAC,oBAAoB,CAAC;oBAClC,CAAC,CAAC,IAAI;gBACR,KAAK;gBACL,MAAM,EAAE,MAAM,IAAI,QAAQ;aAC3B;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACxC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC7D,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,sCAAsC,CAAC,CAAC,CAAC;IACrE,CAAC;AACH,CAAC,CAAC;AA7EW,QAAA,uBAAuB,2BA6ElC;AAEF,mCAAmC;AAC5B,MAAM,mBAAmB,GAAG,KAAK,EACtC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAElC,MAAM,gBAAgB,GAAG,MAAM,kBAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;YAC7D,KAAK,EAAE;gBACL,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;aAC/B;YACD,OAAO,EAAE;gBACP,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,IAAI,EAAE,IAAI;qBACX;iBACF;aACF;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC7B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mCAAmC,CAAC,CAAC,CAAC;IAClE,CAAC;AACH,CAAC,CAAC;AA9BW,QAAA,mBAAmB,uBA8B9B;AAEF,6BAA6B;AACtB,MAAM,qBAAqB,GAAG,KAAK,EACxC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,EACJ,aAAa,EACb,cAAc,EACd,oBAAoB,EACpB,KAAK,EACL,MAAM,GACP,GAAG,GAAG,CAAC,IAAI,CAAC;QAEb,MAAM,eAAe,GAAG,MAAM,kBAAM,CAAC,eAAe,CAAC,UAAU,CAAC;YAC9D,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,4BAA4B,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,sBAAsB,GAAG,MAAM,kBAAM,CAAC,eAAe,CAAC,MAAM,CAAC;YACjE,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;YACD,IAAI,EAAE;gBACJ,aAAa,EACX,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS;gBACnE,cAAc,EACZ,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS;gBACvE,oBAAoB,EAClB,oBAAoB,KAAK,SAAS;oBAChC,CAAC,CAAC,UAAU,CAAC,oBAAoB,CAAC;oBAClC,CAAC,CAAC,SAAS;gBACf,KAAK,EAAE,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;gBAC9C,MAAM,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;aAClD;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACnC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mCAAmC,CAAC,CAAC,CAAC;IAClE,CAAC;AACH,CAAC,CAAC;AAhDW,QAAA,qBAAqB,yBAgDhC;AAEF,oCAAoC;AAC7B,MAAM,qBAAqB,GAAG,KAAK,EACxC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAE1B,MAAM,eAAe,GAAG,MAAM,kBAAM,CAAC,eAAe,CAAC,UAAU,CAAC;YAC9D,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,4BAA4B,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,kBAAM,CAAC,eAAe,CAAC,MAAM,CAAC;YAClC,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;aACf;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,4CAA4C,EAAE,CAAC,CAAC;IACtE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC9D,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,mCAAmC,CAAC,CAAC,CAAC;IAClE,CAAC;AACH,CAAC,CAAC;AA7BW,QAAA,qBAAqB,yBA6BhC"}