"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTransactionStats = exports.updateTransactionStatus = exports.createTransaction = exports.getTransaction = exports.getUserTransactions = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Get all payment transactions for the authenticated user
const getUserTransactions = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { limit = 10, offset = 0, type } = req.query;
        if (!userId) {
            res.status(401).json({
                success: false,
                error: "Unauthorized"
            });
            return;
        }
        const whereClause = {
            userId: Number(userId),
        };
        // Filter by transaction type if provided
        if (type && typeof type === 'string') {
            whereClause.type = type;
        }
        const transactions = await prisma_js_1.prisma.paymentTransaction.findMany({
            where: whereClause,
            include: {
                paymentCard: {
                    select: {
                        id: true,
                        last4: true,
                        brand: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
            take: Number(limit),
            skip: Number(offset),
        });
        // Get total count for pagination
        const totalCount = await prisma_js_1.prisma.paymentTransaction.count({
            where: whereClause,
        });
        res.status(200).json({
            transactions,
            pagination: {
                total: totalCount,
                limit: Number(limit),
                offset: Number(offset),
                hasMore: Number(offset) + Number(limit) < totalCount,
            },
        });
    }
    catch (error) {
        console.error("Error fetching user transactions:", error);
        res.status(500).json({
            success: false,
            error: "Failed to fetch payment transactions"
        });
    }
};
exports.getUserTransactions = getUserTransactions;
// Get a specific payment transaction
const getTransaction = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { transactionId } = req.params;
        if (!userId) {
            res.status(401).json({
                success: false,
                error: "Unauthorized"
            });
            return;
        }
        const transaction = await prisma_js_1.prisma.paymentTransaction.findFirst({
            where: {
                id: transactionId,
                userId: Number(userId),
            },
            include: {
                paymentCard: {
                    select: {
                        id: true,
                        last4: true,
                        brand: true,
                        cardholderName: true,
                    },
                },
            },
        });
        if (!transaction) {
            res.status(404).json({
                success: false,
                error: "Payment transaction not found"
            });
            return;
        }
        res.status(200).json(transaction);
    }
    catch (error) {
        console.error("Error fetching payment transaction:", error);
        res.status(500).json({
            success: false,
            error: "Failed to fetch payment transaction"
        });
    }
};
exports.getTransaction = getTransaction;
// Create a new payment transaction (typically called by payment processing)
const createTransaction = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { paymentCardId, stripePaymentIntentId, description, amount, currency = 'USD', status, type = 'subscription', subscriptionId } = req.body;
        if (!userId) {
            res.status(401).json({
                success: false,
                error: "Unauthorized"
            });
            return;
        }
        // Validate required fields
        if (!description || !amount || !status) {
            res.status(400).json({
                success: false,
                error: "Missing required transaction information"
            });
            return;
        }
        // Verify payment card belongs to user if provided
        if (paymentCardId) {
            const card = await prisma_js_1.prisma.paymentCard.findFirst({
                where: {
                    id: paymentCardId,
                    userId: Number(userId),
                    isActive: true,
                },
            });
            if (!card) {
                res.status(400).json({
                    success: false,
                    error: "Invalid payment card"
                });
                return;
            }
        }
        // Create the transaction
        const transaction = await prisma_js_1.prisma.paymentTransaction.create({
            data: {
                userId: Number(userId),
                paymentCardId,
                stripePaymentIntentId,
                description,
                amount: Number(amount),
                currency,
                status: status.toLowerCase(),
                type,
                subscriptionId,
            },
            include: {
                paymentCard: {
                    select: {
                        id: true,
                        last4: true,
                        brand: true,
                        cardholderName: true,
                    },
                },
            },
        });
        res.status(201).json({
            success: true,
            message: "Payment transaction created successfully",
            data: transaction,
        });
    }
    catch (error) {
        console.error("Error creating payment transaction:", error);
        res.status(500).json({
            success: false,
            error: "Failed to create payment transaction"
        });
    }
};
exports.createTransaction = createTransaction;
// Update a payment transaction status
const updateTransactionStatus = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { transactionId } = req.params;
        const { status } = req.body;
        if (!userId) {
            res.status(401).json({
                success: false,
                error: "Unauthorized"
            });
            return;
        }
        if (!status) {
            res.status(400).json({
                success: false,
                error: "Status is required"
            });
            return;
        }
        // Verify the transaction belongs to the user
        const transaction = await prisma_js_1.prisma.paymentTransaction.findFirst({
            where: {
                id: transactionId,
                userId: Number(userId),
            },
        });
        if (!transaction) {
            res.status(404).json({
                success: false,
                error: "Payment transaction not found"
            });
            return;
        }
        // Update the transaction status
        const updatedTransaction = await prisma_js_1.prisma.paymentTransaction.update({
            where: {
                id: transactionId,
            },
            data: {
                status: status.toLowerCase(),
                updatedAt: new Date(),
            },
            include: {
                paymentCard: {
                    select: {
                        id: true,
                        last4: true,
                        brand: true,
                        cardholderName: true,
                    },
                },
            },
        });
        res.status(200).json({
            success: true,
            message: "Transaction status updated successfully",
            data: updatedTransaction,
        });
    }
    catch (error) {
        console.error("Error updating transaction status:", error);
        res.status(500).json({
            success: false,
            error: "Failed to update transaction status"
        });
    }
};
exports.updateTransactionStatus = updateTransactionStatus;
// Get transaction statistics for the user
const getTransactionStats = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({
                success: false,
                error: "Unauthorized"
            });
            return;
        }
        // Get transaction statistics
        const stats = await prisma_js_1.prisma.paymentTransaction.groupBy({
            by: ['status'],
            where: {
                userId: Number(userId),
            },
            _count: {
                id: true,
            },
            _sum: {
                amount: true,
            },
        });
        // Get monthly spending for the last 6 months
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
        const monthlySpending = await prisma_js_1.prisma.paymentTransaction.findMany({
            where: {
                userId: Number(userId),
                status: 'complete',
                createdAt: {
                    gte: sixMonthsAgo,
                },
            },
            select: {
                amount: true,
                createdAt: true,
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
        res.status(200).json({
            statusStats: stats,
            monthlySpending,
            totalTransactions: stats.reduce((sum, stat) => sum + stat._count.id, 0),
            totalAmount: stats.reduce((sum, stat) => sum + (stat._sum.amount || 0), 0),
        });
    }
    catch (error) {
        console.error("Error fetching transaction stats:", error);
        res.status(500).json({
            success: false,
            error: "Failed to fetch transaction statistics"
        });
    }
};
exports.getTransactionStats = getTransactionStats;
//# sourceMappingURL=transactions.controller.js.map