"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.createHandler = exports.UserRole = void 0;
// Type for user roles
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "ADMIN";
    UserRole["USER"] = "USER";
    UserRole["MANAGER"] = "MANAGER";
    UserRole["EMPLOYEE"] = "EMPLOYEE";
    UserRole["CLIENT"] = "CLIENT";
    UserRole["SUPPLIER"] = "SUPPLIER";
})(UserRole || (exports.UserRole = UserRole = {}));
// Helper function to create a typed request handler
const createHandler = (handler) => {
    return handler;
};
exports.createHandler = createHandler;
// Helper function to wrap async handlers with error catching
const asyncHandler = (fn) => {
    return async (req, res, next) => {
        try {
            await fn(req, res, next);
        }
        catch (error) {
            next(error);
        }
    };
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=index.js.map