{"version": 3, "sources": ["../../../../.pnpm/is-https@4.0.0/node_modules/is-https/dist/index.mjs"], "sourcesContent": ["function isHTTPS(req, trustProxy = true) {\n  const _xForwardedProto = trustProxy && req.headers ? req.headers[\"x-forwarded-proto\"] : void 0;\n  const protoCheck = typeof _xForwardedProto === \"string\" ? _xForwardedProto.includes(\"https\") : void 0;\n  if (protoCheck) {\n    return true;\n  }\n  const _encrypted = req.connection ? req.connection.encrypted : void 0;\n  const encryptedCheck = _encrypted !== void 0 ? _encrypted === true : void 0;\n  if (encryptedCheck) {\n    return true;\n  }\n  if (protoCheck === void 0 && encryptedCheck === void 0) {\n    return void 0;\n  }\n  return false;\n}\n\nexport default isHTTPS;\n"], "mappings": ";;;AAAA,SAAS,QAAQ,KAAK,aAAa,MAAM;AACvC,QAAM,mBAAmB,cAAc,IAAI,UAAU,IAAI,QAAQ,mBAAmB,IAAI;AACxF,QAAM,aAAa,OAAO,qBAAqB,WAAW,iBAAiB,SAAS,OAAO,IAAI;AAC/F,MAAI,YAAY;AACd,WAAO;AAAA,EACT;AACA,QAAM,aAAa,IAAI,aAAa,IAAI,WAAW,YAAY;AAC/D,QAAM,iBAAiB,eAAe,SAAS,eAAe,OAAO;AACrE,MAAI,gBAAgB;AAClB,WAAO;AAAA,EACT;AACA,MAAI,eAAe,UAAU,mBAAmB,QAAQ;AACtD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAO,eAAQ;", "names": []}