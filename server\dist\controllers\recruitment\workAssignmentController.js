"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addCompanyReview = exports.addWorkerReview = exports.approveTimeEntry = exports.addTimeEntry = exports.updateTrialPeriodStatus = exports.updateWorkAssignmentStatus = exports.getWorkAssignmentsForWorker = exports.getWorkAssignmentsForCompany = exports.getWorkAssignmentById = exports.getAllWorkAssignments = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
const client_1 = require("@prisma/client");
// Using centralized prisma instance from lib/prisma.js
// Get all work assignments
const getAllWorkAssignments = async (req, res) => {
    try {
        const assignments = await prisma_js_1.prisma.workAssignment.findMany({
            include: {
                user: true,
                company: true,
                project: true,
                task: true,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json(assignments);
    }
    catch (error) {
        console.error("Error fetching work assignments:", error);
        return res.status(500).json({ error: "Failed to fetch work assignments" });
    }
};
exports.getAllWorkAssignments = getAllWorkAssignments;
// Get work assignment by ID
const getWorkAssignmentById = async (req, res) => {
    const { id } = req.params;
    try {
        const assignment = await prisma_js_1.prisma.workAssignment.findUnique({
            where: { id: Number(id) },
            include: {
                user: true,
                company: true,
                project: true,
                task: true,
            },
        });
        if (!assignment) {
            return res.status(404).json({ error: "Work assignment not found" });
        }
        return res.status(200).json(assignment);
    }
    catch (error) {
        console.error("Error fetching work assignment:", error);
        return res.status(500).json({ error: "Failed to fetch work assignment" });
    }
};
exports.getWorkAssignmentById = getWorkAssignmentById;
// Get work assignments for a company
const getWorkAssignmentsForCompany = async (req, res) => {
    const { companyId } = req.params;
    try {
        const assignments = await prisma_js_1.prisma.workAssignment.findMany({
            where: {
                companyId: Number(companyId),
            },
            include: {
                user: true,
                project: true,
                task: true,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json(assignments);
    }
    catch (error) {
        console.error("Error fetching work assignments for company:", error);
        return res
            .status(500)
            .json({ error: "Failed to fetch work assignments for company" });
    }
};
exports.getWorkAssignmentsForCompany = getWorkAssignmentsForCompany;
// Get work assignments for a worker
const getWorkAssignmentsForWorker = async (req, res) => {
    const { userId } = req.params;
    try {
        const assignments = await prisma_js_1.prisma.workAssignment.findMany({
            where: {
                userId: Number(userId),
            },
            include: {
                company: true,
                project: true,
                task: true,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json(assignments);
    }
    catch (error) {
        console.error("Error fetching work assignments for worker:", error);
        return res
            .status(500)
            .json({ error: "Failed to fetch work assignments for worker" });
    }
};
exports.getWorkAssignmentsForWorker = getWorkAssignmentsForWorker;
// Update work assignment status
const updateWorkAssignmentStatus = async (req, res) => {
    const { id } = req.params;
    const { status, terminationReason } = req.body;
    try {
        // Validate the status is a valid AssignmentStatus enum value
        if (!Object.values(client_1.AssignmentStatus).includes(status)) {
            return res.status(400).json({
                error: "Invalid status value",
                validValues: Object.values(client_1.AssignmentStatus),
            });
        }
        const assignment = await prisma_js_1.prisma.workAssignment.update({
            where: { id: Number(id) },
            data: {
                status: status,
                notes: status === client_1.AssignmentStatus.CANCELLED && terminationReason
                    ? `Termination reason: ${terminationReason}`
                    : undefined,
            },
        });
        // Note: We've removed the jobRequest update logic since the schema has changed
        // and WorkAssignment no longer has a direct relation to JobRequest
        return res.status(200).json(assignment);
    }
    catch (error) {
        console.error("Error updating work assignment status:", error);
        return res
            .status(500)
            .json({ error: "Failed to update work assignment status" });
    }
};
exports.updateWorkAssignmentStatus = updateWorkAssignmentStatus;
// Update trial period status
const updateTrialPeriodStatus = async (req, res) => {
    const { id } = req.params;
    const { trialPeriod, trialFeedback } = req.body;
    try {
        // Since WorkAssignment doesn't have trialPeriod and trialFeedback fields in the schema,
        // we'll store this information in the notes field
        const assignment = await prisma_js_1.prisma.workAssignment.update({
            where: { id: Number(id) },
            data: {
                notes: `Trial Period: ${trialPeriod ? "Passed" : "Failed"}, Feedback: ${trialFeedback || "None"}`,
            },
        });
        return res.status(200).json(assignment);
    }
    catch (error) {
        console.error("Error updating trial period status:", error);
        return res
            .status(500)
            .json({ error: "Failed to update trial period status" });
    }
};
exports.updateTrialPeriodStatus = updateTrialPeriodStatus;
// Add time entry
const addTimeEntry = async (req, res) => {
    const { id } = req.params;
    const { date, hoursWorked, description } = req.body;
    try {
        // Check if work assignment exists
        const assignment = await prisma_js_1.prisma.workAssignment.findUnique({
            where: { id: Number(id) },
        });
        if (!assignment) {
            return res.status(404).json({ error: "Work assignment not found" });
        }
        // Calculate end time based on hours worked
        const startTime = new Date(date);
        const endTime = new Date(startTime.getTime() + hoursWorked * 60 * 60 * 1000);
        // Create time entry
        const timeEntry = await prisma_js_1.prisma.timeEntry.create({
            data: {
                userId: assignment.userId,
                companyId: assignment.companyId,
                projectId: assignment.projectId,
                taskId: assignment.taskId,
                startTime,
                endTime,
                duration: Math.round(hoursWorked * 60), // Convert hours to minutes
                description,
                status: client_1.TimeEntryStatus.ACTIVE,
            },
        });
        return res.status(201).json(timeEntry);
    }
    catch (error) {
        console.error("Error adding time entry:", error);
        return res.status(500).json({ error: "Failed to add time entry" });
    }
};
exports.addTimeEntry = addTimeEntry;
// Approve time entry
const approveTimeEntry = async (req, res) => {
    const { id } = req.params;
    const { approvedBy } = req.body;
    try {
        const timeEntry = await prisma_js_1.prisma.timeEntry.update({
            where: { id: Number(id) },
            data: {
                status: client_1.TimeEntryStatus.COMPLETED,
                approved: true,
                approvedAt: new Date(),
                // Store approvedBy in description since there's no direct field for it
                description: `Approved by ${approvedBy}. ${(await prisma_js_1.prisma.timeEntry.findUnique({ where: { id: Number(id) } }))?.description || ""}`.trim(),
            },
        });
        return res.status(200).json(timeEntry);
    }
    catch (error) {
        console.error("Error approving time entry:", error);
        return res.status(500).json({ error: "Failed to approve time entry" });
    }
};
exports.approveTimeEntry = approveTimeEntry;
// Add worker review
const addWorkerReview = async (req, res) => {
    const { id } = req.params;
    const { overallRating, qualityRating, reliabilityRating, punctualityRating, comment, reviewedBy, isPublic, } = req.body;
    try {
        // Check if work assignment exists
        const assignment = await prisma_js_1.prisma.workAssignment.findUnique({
            where: { id: Number(id) },
        });
        if (!assignment) {
            return res.status(404).json({ error: "Work assignment not found" });
        }
        // Since WorkAssignment is no longer directly related to WorkerReview in the schema,
        // we need to create an IntermediationAssignment first or find an existing one
        // Check if there's a WorkerProfile for this user
        const workerProfile = await prisma_js_1.prisma.workerProfile.findFirst({
            where: { workerId: assignment.userId },
        });
        if (!workerProfile) {
            return res.status(404).json({
                error: "Worker profile not found for this assignment",
                details: "A worker profile is required to create a review",
            });
        }
        // Create a mock IntermediationAssignment to link the review to
        const intermediationAssignment = await prisma_js_1.prisma.intermediationAssignment.create({
            data: {
                workerProfileId: workerProfile.id,
                // We need to link to a JobRequest, but we don't have one directly from WorkAssignment
                // For now, we'll create a placeholder JobRequest
                jobRequestId: await getOrCreateJobRequest(assignment),
                startDate: assignment.startDate,
                endDate: assignment.endDate || new Date(),
                rate: 0, // Placeholder
                rateType: "hourly", // Default value
                status: client_1.WorkerAssignmentStatus.COMPLETED,
            },
        });
        // Create worker review
        const review = await prisma_js_1.prisma.workerReview.create({
            data: {
                workerProfileId: workerProfile.id,
                assignmentId: intermediationAssignment.id,
                overallRating,
                qualityRating,
                reliabilityRating,
                punctualityRating,
                comment,
                reviewedBy,
                isPublic: isPublic !== undefined ? isPublic : true,
            },
        });
        // Update worker profile ratings
        const workerReviews = await prisma_js_1.prisma.workerReview.findMany({
            where: {
                workerProfileId: workerProfile.id,
            },
        });
        if (workerReviews.length > 0) {
            const avgOverallRating = workerReviews.reduce((sum, review) => sum + review.overallRating, 0) /
                workerReviews.length;
            const avgQualityRating = workerReviews.reduce((sum, review) => sum + review.qualityRating, 0) /
                workerReviews.length;
            const avgReliabilityRating = workerReviews.reduce((sum, review) => sum + review.reliabilityRating, 0) / workerReviews.length;
            const avgPunctualityRating = workerReviews.reduce((sum, review) => sum + review.punctualityRating, 0) / workerReviews.length;
            await prisma_js_1.prisma.workerProfile.update({
                where: { id: workerProfile.id },
                data: {
                    overallRating: avgOverallRating,
                    qualityScore: avgQualityRating,
                    reliabilityScore: avgReliabilityRating,
                    punctualityScore: avgPunctualityRating,
                },
            });
        }
        return res.status(201).json(review);
    }
    catch (error) {
        console.error("Error adding worker review:", error);
        return res.status(500).json({ error: "Failed to add worker review" });
    }
};
exports.addWorkerReview = addWorkerReview;
// Helper function to get or create a JobRequest for an assignment
async function getOrCreateJobRequest(assignment) {
    // Try to find an existing JobRequest for this company
    const existingRequest = await prisma_js_1.prisma.jobRequest.findFirst({
        where: {
            companyId: assignment.companyId,
        },
        orderBy: { createdAt: "desc" },
    });
    if (existingRequest) {
        return existingRequest.id;
    }
    // Create a new JobRequest if none exists
    // Use either the input approach or the connect approach, not both
    const newRequest = await prisma_js_1.prisma.jobRequest.create({
        data: {
            // Use the connect approach for company
            company: {
                connect: { id: assignment.companyId },
            },
            status: "completed",
            startDate: assignment.startDate,
            endDate: assignment.endDate || new Date(),
            location: "Unknown",
            title: `Auto-generated for assignment ${assignment.id}`,
            description: `This job request was automatically created to support a worker review for assignment ${assignment.id}`,
            // Add required fields based on the schema
            specialization: "general",
            experienceLevel: "intermediate",
        },
    });
    return newRequest.id;
}
// Add company review
const addCompanyReview = async (req, res) => {
    const { id } = req.params;
    const { overallRating, workEnvironmentRating, managementRating, paymentPunctualityRating, comment, reviewedBy, isPublic, } = req.body;
    try {
        // Check if work assignment exists
        const assignment = await prisma_js_1.prisma.workAssignment.findUnique({
            where: { id: Number(id) },
        });
        if (!assignment) {
            return res.status(404).json({ error: "Work assignment not found" });
        }
        // Since WorkAssignment is no longer directly related to CompanyReview in the schema,
        // we need to create an IntermediationAssignment first or find an existing one
        // Check if there's a WorkerProfile for this user
        const workerProfile = await prisma_js_1.prisma.workerProfile.findFirst({
            where: { workerId: assignment.userId },
        });
        if (!workerProfile) {
            return res.status(404).json({
                error: "Worker profile not found for this assignment",
                details: "A worker profile is required to create a review",
            });
        }
        // Create a mock IntermediationAssignment to link the review to
        const intermediationAssignment = await prisma_js_1.prisma.intermediationAssignment.create({
            data: {
                workerProfileId: workerProfile.id,
                // We need to link to a JobRequest, but we don't have one directly from WorkAssignment
                // For now, we'll create a placeholder JobRequest
                jobRequestId: await getOrCreateJobRequest(assignment),
                startDate: assignment.startDate,
                endDate: assignment.endDate || new Date(),
                rate: 0, // Placeholder
                rateType: "hourly", // Default value
                status: client_1.WorkerAssignmentStatus.COMPLETED,
            },
        });
        // Create company review
        const review = await prisma_js_1.prisma.companyReview.create({
            data: {
                companyId: assignment.companyId,
                assignmentId: intermediationAssignment.id,
                overallRating,
                workEnvironmentRating,
                managementRating,
                paymentPunctualityRating,
                comment,
                reviewedBy,
                isPublic: isPublic !== undefined ? isPublic : true,
            },
        });
        return res.status(201).json(review);
    }
    catch (error) {
        console.error("Error adding company review:", error);
        return res.status(500).json({ error: "Failed to add company review" });
    }
};
exports.addCompanyReview = addCompanyReview;
//# sourceMappingURL=workAssignmentController.js.map