"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const aiController_js_1 = require("../controllers/ai/aiController.js");
const router = express_1.default.Router();
// Command Processing routes
router.post("/process-command", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(aiController_js_1.processCommand));
// Speech Synthesis routes
router.post("/text-to-speech", route_helpers_js_1.auth, (0, route_helpers_js_1.wrapController)(aiController_js_1.textToSpeech));
exports.default = router;
//# sourceMappingURL=ai.js.map