<!--  client/layers/core/components/activity/ActivityLogItem.vue -->

<template>
  <div
    class="activity-log-item p-4 border-b border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800/50"
    :class="{ 'cursor-pointer': clickable }"
    @click="handleClick"
  >
    <div class="flex items-start gap-3">
      <!-- Activity Icon or User Avatar -->
      <BaseAvatar
        v-if="showAvatar && activity.user"
        :src="activity.user.avatar"
        :alt="getUserName(activity.user)"
        size="sm"
        class="flex-shrink-0"
      />

      <BaseIconBox
        v-else
        size="sm"
        :class="getActivityIconClass(activity.type)"
        rounded="full"
        color="none"
        class="flex-shrink-0"
      >
        <Icon :name="getActivityIcon(activity.type)" class="h-4 w-4" />
      </BaseIconBox>

      <!-- Activity Content -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center justify-between">
          <BaseText
            size="sm"
            weight="medium"
            class="text-muted-800 dark:text-muted-100"
          >
            {{ activity.title }}
          </BaseText>
          <BaseText size="xs" class="text-muted-400 whitespace-nowrap ml-2">
            {{ formatTime(activity.timestamp) }}
          </BaseText>
        </div>

        <BaseText size="xs" class="text-muted-500 dark:text-muted-400 mt-1">
          {{ activity.description }}
        </BaseText>

        <!-- User Info -->
        <div v-if="activity.user && !showAvatar" class="flex items-center mt-2">
          <BaseText size="xs" class="text-muted-400 mr-1">By</BaseText>
          <BaseText
            size="xs"
            weight="medium"
            class="text-muted-700 dark:text-muted-200"
          >
            {{ getUserName(activity.user) }}
          </BaseText>
        </div>

        <!-- Tags -->
        <div
          v-if="activity.tags && activity.tags.length > 0"
          class="flex flex-wrap gap-1 mt-2"
        >
          <BaseTag
            v-for="tag in activity.tags"
            :key="tag"
            color="info"
            flavor="pastel"
            size="xs"
          >
            {{ tag }}
          </BaseTag>
        </div>

        <!-- Module Info -->
        <div v-if="activity.module" class="mt-2">
          <BaseText size="xs" class="text-muted-400">
            Module:
            <span class="text-muted-700 dark:text-muted-200">{{
              activity.module
            }}</span>
          </BaseText>
        </div>

        <!-- IP Address -->
        <div v-if="activity.ipAddress" class="mt-1">
          <BaseText size="xs" class="text-muted-400">
            IP:
            <span class="text-muted-700 dark:text-muted-200">{{
              activity.ipAddress
            }}</span>
          </BaseText>
        </div>

        <!-- Details Button -->
        <div v-if="showDetailsButton" class="mt-2">
          <BaseButton
            color="primary"
            flavor="link"
            size="sm"
            @click.stop="$emit('view-details', activity)"
          >
            View Details
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { formatDistanceToNow } from "date-fns";

const props = defineProps({
  activity: {
    type: Object,
    required: true,
  },
  showAvatar: {
    type: Boolean,
    default: false,
  },
  showDetailsButton: {
    type: Boolean,
    default: false,
  },
  clickable: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["click", "view-details"]);

const handleClick = () => {
  if (props.clickable) {
    emit("click", props.activity);
  }
};

const formatTime = (timestamp: string | Date) => {
  if (!timestamp) return "";

  try {
    const date =
      typeof timestamp === "string" ? new Date(timestamp) : timestamp;
    return formatDistanceToNow(date, { addSuffix: true });
  } catch (error) {
    return "";
  }
};

const getUserName = (user) => {
  if (!user) return "Unknown User";

  return (
    `${user.firstName} ${user.lastName}`.trim() ||
    user.username ||
    "Unknown User"
  );
};

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    login: "ph:sign-in-duotone",
    logout: "ph:sign-out-duotone",
    create: "ph:plus-circle-duotone",
    update: "ph:pencil-duotone",
    delete: "ph:trash-duotone",
    share: "ph:share-network-duotone",
    comment: "ph:chat-text-duotone",
    upload: "ph:upload-duotone",
    download: "ph:download-duotone",
    approve: "ph:check-circle-duotone",
    reject: "ph:x-circle-duotone",
    assign: "ph:user-plus-duotone",
    complete: "ph:check-square-duotone",
    payment: "ph:currency-dollar-duotone",
    error: "ph:warning-duotone",
    security: "ph:shield-duotone",
    settings: "ph:gear-duotone",
    notification: "ph:bell-duotone",
  };

  return iconMap[type] || "ph:activity-duotone";
};

const getActivityIconClass = (type: string) => {
  const classMap: Record<string, string> = {
    login:
      "bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400",
    logout: "bg-muted-100 text-muted-500 dark:bg-muted-700 dark:text-muted-400",
    create:
      "bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400",
    update: "bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400",
    delete:
      "bg-danger-100 text-danger-500 dark:bg-danger-500/20 dark:text-danger-400",
    share:
      "bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400",
    comment: "bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400",
    upload:
      "bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400",
    download:
      "bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400",
    approve:
      "bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400",
    reject:
      "bg-danger-100 text-danger-500 dark:bg-danger-500/20 dark:text-danger-400",
    assign:
      "bg-warning-100 text-warning-500 dark:bg-warning-500/20 dark:text-warning-400",
    complete:
      "bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400",
    payment:
      "bg-success-100 text-success-500 dark:bg-success-500/20 dark:text-success-400",
    error:
      "bg-danger-100 text-danger-500 dark:bg-danger-500/20 dark:text-danger-400",
    security:
      "bg-warning-100 text-warning-500 dark:bg-warning-500/20 dark:text-warning-400",
    settings:
      "bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400",
    notification:
      "bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400",
  };

  return (
    classMap[type] ||
    "bg-muted-100 text-muted-500 dark:bg-muted-700 dark:text-muted-400"
  );
};
</script>
