"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const route_helpers_js_1 = require("../utils/route-helpers.js");
const auth_controller_js_1 = require("../controllers/auth/auth.controller.js");
const email_verification_controller_js_1 = require("../controllers/auth/email-verification.controller.js");
const router = express_1.default.Router();
// Authentication routes
router.post("/login", (0, route_helpers_js_1.wrapController)(auth_controller_js_1.login));
router.post("/register", (0, route_helpers_js_1.wrapController)(auth_controller_js_1.register));
router.post("/refresh-token", (0, route_helpers_js_1.wrapController)(auth_controller_js_1.refreshToken));
// Email verification status check
router.get("/verify-status/:userId", (0, route_helpers_js_1.wrapController)(email_verification_controller_js_1.checkVerificationStatus));
// Resend verification email
router.post("/resend-verification", (0, route_helpers_js_1.wrapController)(email_verification_controller_js_1.resendVerificationEmail));
exports.default = router;
//# sourceMappingURL=auth.js.map