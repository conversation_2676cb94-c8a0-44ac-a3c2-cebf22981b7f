"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteWorkforceNeed = exports.updateWorkforceNeed = exports.createWorkforceNeed = exports.getCompanyWorkforceNeeds = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Create a simple error function instead of using http-errors
function createHttpError(statusCode, message) {
    const error = new Error(message);
    error.statusCode = statusCode;
    return error;
}
// Helper function to map status values to RequestStatus enum
function mapStatus(status) {
    switch (status) {
        case "OPEN":
            return "PENDING";
        case "FILLED":
            return "APPROVED";
        case "CANCELLED":
            return "CANCELLED";
        default:
            return "PENDING";
    }
}
const getCompanyWorkforceNeeds = async (req, res, next) => {
    try {
        const { id } = req.params;
        const workforceNeeds = await prisma_js_1.prisma.workforceNeed.findMany({
            where: { companyId: Number(id) },
        });
        res.json(workforceNeeds);
    }
    catch (error) {
        next(createHttpError(500, "Failed to fetch workforce needs"));
    }
};
exports.getCompanyWorkforceNeeds = getCompanyWorkforceNeeds;
const createWorkforceNeed = async (req, res, next) => {
    try {
        const { id } = req.params;
        const payload = req.body;
        // Map the payload to match the Prisma schema
        const workforceNeed = await prisma_js_1.prisma.workforceNeed.create({
            data: {
                companyId: Number(id),
                skillsNeeded: payload.skillsNeeded,
                quantity: payload.quantity,
                duration: payload.duration,
                budget: payload.budget,
                priority: payload.priority,
                status: mapStatus(payload.status),
                aiMatches: payload.aiMatches,
            },
        });
        res.status(201).json(workforceNeed);
    }
    catch (error) {
        next(createHttpError(500, "Failed to create workforce need"));
    }
};
exports.createWorkforceNeed = createWorkforceNeed;
const updateWorkforceNeed = async (req, res, next) => {
    try {
        const { needId } = req.params;
        const payload = req.body;
        // Map the payload to match the Prisma schema
        const updateData = {};
        if (payload.skillsNeeded)
            updateData.skillsNeeded = payload.skillsNeeded;
        if (payload.quantity)
            updateData.quantity = payload.quantity;
        if (payload.duration)
            updateData.duration = payload.duration;
        if (payload.budget)
            updateData.budget = payload.budget;
        if (payload.priority)
            updateData.priority = payload.priority;
        if (payload.status)
            updateData.status = mapStatus(payload.status);
        if (payload.aiMatches)
            updateData.aiMatches = payload.aiMatches;
        const workforceNeed = await prisma_js_1.prisma.workforceNeed.update({
            where: { id: Number(needId) },
            data: updateData,
        });
        res.json(workforceNeed);
    }
    catch (error) {
        if (error.code === "P2025") {
            return next(createHttpError(404, "Workforce need not found"));
        }
        next(createHttpError(500, "Failed to update workforce need"));
    }
};
exports.updateWorkforceNeed = updateWorkforceNeed;
const deleteWorkforceNeed = async (req, res, next) => {
    try {
        const { needId } = req.params;
        await prisma_js_1.prisma.workforceNeed.delete({
            where: { id: Number(needId) },
        });
        res.status(204).send();
    }
    catch (error) {
        if (error.code === "P2025") {
            return next(createHttpError(404, "Workforce need not found"));
        }
        next(createHttpError(500, "Failed to delete workforce need"));
    }
};
exports.deleteWorkforceNeed = deleteWorkforceNeed;
//# sourceMappingURL=workforce.controller.js.map