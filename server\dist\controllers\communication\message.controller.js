"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteContactFormMessage = exports.markContactFormMessageAsRead = exports.getContactFormMessages = void 0;
const prisma_js_1 = require("../../lib/prisma.js");
// Using centralized prisma instance from lib/prisma.js
// Get all contact form messages
const getContactFormMessages = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Get all endpoint requests that came from contact form endpoints
        const messages = await prisma_js_1.prisma.endpointRequest.findMany({
            where: {
                endpoint: {
                    userId: Number(userId),
                },
            },
            orderBy: {
                createdAt: "desc",
            },
            include: {
                endpoint: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        });
        // Transform the data to match the ContactFormMessage interface
        const formattedMessages = messages.map((message) => {
            const requestData = message.requestData;
            return {
                id: message.id,
                endpointId: message.endpointId,
                endpointName: message.endpoint?.name,
                name: requestData.name || "Unknown",
                email: requestData.email || "<EMAIL>",
                message: requestData.message || "",
                isRead: message.isRead || false,
                createdAt: message.createdAt,
            };
        });
        res.status(200).json(formattedMessages);
    }
    catch (error) {
        console.error("Error getting contact form messages:", error);
        res.status(500).json({ error: "Failed to get contact form messages" });
    }
};
exports.getContactFormMessages = getContactFormMessages;
// Mark a contact form message as read
const markContactFormMessageAsRead = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { messageId } = req.params;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Check if the message exists and belongs to the user
        const message = await prisma_js_1.prisma.endpointRequest.findFirst({
            where: {
                id: Number(messageId),
                endpoint: {
                    userId: Number(userId),
                },
            },
        });
        if (!message) {
            res.status(404).json({ error: "Message not found" });
            return;
        }
        // Update the message
        const updatedMessage = await prisma_js_1.prisma.endpointRequest.update({
            where: {
                id: Number(messageId),
            },
            data: {
                isRead: true,
            },
            include: {
                endpoint: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        });
        // Transform the data to match the ContactFormMessage interface
        const requestData = updatedMessage.requestData;
        const formattedMessage = {
            id: updatedMessage.id,
            endpointId: updatedMessage.endpointId,
            endpointName: updatedMessage.endpoint
                ? updatedMessage.endpoint.name
                : "Unknown",
            name: requestData.name || "Unknown",
            email: requestData.email || "<EMAIL>",
            message: requestData.message || "",
            isRead: updatedMessage.isRead || false,
            createdAt: updatedMessage.createdAt,
        };
        res.status(200).json(formattedMessage);
    }
    catch (error) {
        console.error("Error marking contact form message as read:", error);
        res.status(500).json({ error: "Failed to mark message as read" });
    }
};
exports.markContactFormMessageAsRead = markContactFormMessageAsRead;
// Delete a contact form message
const deleteContactFormMessage = async (req, res) => {
    try {
        const userId = req.user?.id;
        const { messageId } = req.params;
        if (!userId) {
            res.status(401).json({ error: "Unauthorized" });
            return;
        }
        // Check if the message exists and belongs to the user
        const message = await prisma_js_1.prisma.endpointRequest.findFirst({
            where: {
                id: Number(messageId),
                endpoint: {
                    userId: Number(userId),
                },
            },
        });
        if (!message) {
            res.status(404).json({ error: "Message not found" });
            return;
        }
        // Delete the message
        await prisma_js_1.prisma.endpointRequest.delete({
            where: {
                id: Number(messageId),
            },
        });
        res.status(200).json({ success: true });
    }
    catch (error) {
        console.error("Error deleting contact form message:", error);
        res.status(500).json({ error: "Failed to delete message" });
    }
};
exports.deleteContactFormMessage = deleteContactFormMessage;
//# sourceMappingURL=message.controller.js.map